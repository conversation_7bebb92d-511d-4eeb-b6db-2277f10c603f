{"ast": null, "code": "import * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nfunction useHelper(object3D, helperConstructor, ...args) {\n  const helper = React.useRef();\n  const scene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    let currentHelper = undefined;\n    if (object3D && object3D != null && object3D.current && helperConstructor) {\n      helper.current = currentHelper = new helperConstructor(object3D.current, ...args);\n    }\n    if (currentHelper) {\n      scene.add(currentHelper);\n      return () => {\n        helper.current = undefined;\n        scene.remove(currentHelper);\n        currentHelper.dispose == null ? void 0 : currentHelper.dispose();\n      };\n    }\n  }, [scene, helperConstructor, object3D, ...args]);\n  useFrame(() => {\n    var _helper$current;\n    return void ((_helper$current = helper.current) == null ? void 0 : _helper$current.update == null ? void 0 : _helper$current.update());\n  });\n  return helper;\n}\nexport { useHelper };", "map": {"version": 3, "names": ["React", "useThree", "useFrame", "useHelper", "object3D", "helperConstructor", "args", "helper", "useRef", "scene", "state", "useLayoutEffect", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "current", "add", "remove", "dispose", "_helper$current", "update"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useHelper.js"], "sourcesContent": ["import * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\n\nfunction useHelper(object3D, helperConstructor, ...args) {\n  const helper = React.useRef();\n  const scene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    let currentHelper = undefined;\n\n    if (object3D && object3D != null && object3D.current && helperConstructor) {\n      helper.current = currentHelper = new helperConstructor(object3D.current, ...args);\n    }\n\n    if (currentHelper) {\n      scene.add(currentHelper);\n      return () => {\n        helper.current = undefined;\n        scene.remove(currentHelper);\n        currentHelper.dispose == null ? void 0 : currentHelper.dispose();\n      };\n    }\n  }, [scene, helperConstructor, object3D, ...args]);\n  useFrame(() => {\n    var _helper$current;\n\n    return void ((_helper$current = helper.current) == null ? void 0 : _helper$current.update == null ? void 0 : _helper$current.update());\n  });\n  return helper;\n}\n\nexport { useHelper };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAEvD,SAASC,SAASA,CAACC,QAAQ,EAAEC,iBAAiB,EAAE,GAAGC,IAAI,EAAE;EACvD,MAAMC,MAAM,GAAGP,KAAK,CAACQ,MAAM,CAAC,CAAC;EAC7B,MAAMC,KAAK,GAAGR,QAAQ,CAACS,KAAK,IAAIA,KAAK,CAACD,KAAK,CAAC;EAC5CT,KAAK,CAACW,eAAe,CAAC,MAAM;IAC1B,IAAIC,aAAa,GAAGC,SAAS;IAE7B,IAAIT,QAAQ,IAAIA,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACU,OAAO,IAAIT,iBAAiB,EAAE;MACzEE,MAAM,CAACO,OAAO,GAAGF,aAAa,GAAG,IAAIP,iBAAiB,CAACD,QAAQ,CAACU,OAAO,EAAE,GAAGR,IAAI,CAAC;IACnF;IAEA,IAAIM,aAAa,EAAE;MACjBH,KAAK,CAACM,GAAG,CAACH,aAAa,CAAC;MACxB,OAAO,MAAM;QACXL,MAAM,CAACO,OAAO,GAAGD,SAAS;QAC1BJ,KAAK,CAACO,MAAM,CAACJ,aAAa,CAAC;QAC3BA,aAAa,CAACK,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGL,aAAa,CAACK,OAAO,CAAC,CAAC;MAClE,CAAC;IACH;EACF,CAAC,EAAE,CAACR,KAAK,EAAEJ,iBAAiB,EAAED,QAAQ,EAAE,GAAGE,IAAI,CAAC,CAAC;EACjDJ,QAAQ,CAAC,MAAM;IACb,IAAIgB,eAAe;IAEnB,OAAO,MAAM,CAACA,eAAe,GAAGX,MAAM,CAACO,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,eAAe,CAACC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGD,eAAe,CAACC,MAAM,CAAC,CAAC,CAAC;EACxI,CAAC,CAAC;EACF,OAAOZ,MAAM;AACf;AAEA,SAASJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}