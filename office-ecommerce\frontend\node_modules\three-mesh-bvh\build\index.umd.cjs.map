{"version": 3, "file": "index.umd.cjs", "sources": ["../src/core/Constants.js", "../src/core/MeshBVHNode.js", "../src/utils/ArrayBoxUtilities.js", "../src/core/buildFunctions.js", "../src/math/SeparatingAxisBounds.js", "../src/math/MathUtilities.js", "../src/math/ExtendedTriangle.js", "../src/math/OrientedBox.js", "../src/utils/ThreeRayIntersectUtilities.js", "../src/utils/GeometryRayIntersectUtilities.js", "../src/utils/TriangleUtilities.js", "../src/utils/PrimitivePool.js", "../src/core/nodeBufferFunctions.js", "../src/core/castFunctions.js", "../src/core/MeshBVH.js", "../src/objects/MeshBVHVisualizer.js", "../src/debug/Debug.js", "../src/utils/ExtensionUtilities.js", "../src/gpu/VertexAttributeTexture.js", "../src/gpu/MeshBVHUniformStruct.js", "../src/gpu/shaderFunctions.js", "../src/utils/StaticGeometryGenerator.js"], "sourcesContent": ["// Split strategy constants\nexport const CENTER = 0;\nexport const AVERAGE = 1;\nexport const SAH = 2;\n\n// Traversal constants\nexport const NOT_INTERSECTED = 0;\nexport const INTERSECTED = 1;\nexport const CONTAINED = 2;\n\n// SAH cost constants\n// TODO: hone these costs more. The relative difference between them should be the\n// difference in measured time to perform a triangle intersection vs traversing\n// bounds.\nexport const TRIANGLE_INTERSECT_COST = 1.25;\nexport const TRAVERSAL_COST = 1;\n\n\n// Build constants\nexport const BYTES_PER_NODE = 6 * 4 + 4 + 4;\nexport const IS_LEAFNODE_FLAG = 0xFFFF;\n\n// EPSILON for computing floating point error during build\n// https://en.wikipedia.org/wiki/Machine_epsilon#Values_for_standard_hardware_floating_point_arithmetics\nexport const FLOAT32_EPSILON = Math.pow( 2, - 24 );\n\n", "export class MeshBVHNode {\n\n\tconstructor() {\n\n\t\t// internal nodes have boundingData, left, right, and splitAxis\n\t\t// leaf nodes have offset and count (referring to primitives in the mesh geometry)\n\n\t}\n\n}\n", "export function arrayToBox( nodeIndex32, array, target ) {\n\n\ttarget.min.x = array[ nodeIndex32 ];\n\ttarget.min.y = array[ nodeIndex32 + 1 ];\n\ttarget.min.z = array[ nodeIndex32 + 2 ];\n\n\ttarget.max.x = array[ nodeIndex32 + 3 ];\n\ttarget.max.y = array[ nodeIndex32 + 4 ];\n\ttarget.max.z = array[ nodeIndex32 + 5 ];\n\n\treturn target;\n\n}\n\nexport function getLongestEdgeIndex( bounds ) {\n\n\tlet splitDimIdx = - 1;\n\tlet splitDist = - Infinity;\n\n\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\tconst dist = bounds[ i + 3 ] - bounds[ i ];\n\t\tif ( dist > splitDist ) {\n\n\t\t\tsplitDist = dist;\n\t\t\tsplitDimIdx = i;\n\n\t\t}\n\n\t}\n\n\treturn splitDimIdx;\n\n}\n\n// copies bounds a into bounds b\nexport function copyBounds( source, target ) {\n\n\ttarget.set( source );\n\n}\n\n// sets bounds target to the union of bounds a and b\nexport function unionBounds( a, b, target ) {\n\n\tlet aVal, bVal;\n\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\tconst d3 = d + 3;\n\n\t\t// set the minimum values\n\t\taVal = a[ d ];\n\t\tbVal = b[ d ];\n\t\ttarget[ d ] = aVal < bVal ? aVal : bVal;\n\n\t\t// set the max values\n\t\taVal = a[ d3 ];\n\t\tbVal = b[ d3 ];\n\t\ttarget[ d3 ] = aVal > bVal ? aVal : bVal;\n\n\t}\n\n}\n\n// expands the given bounds by the provided triangle bounds\nexport function expandByTriangleBounds( startIndex, triangleBounds, bounds ) {\n\n\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\tconst tCenter = triangleBounds[ startIndex + 2 * d ];\n\t\tconst tHalf = triangleBounds[ startIndex + 2 * d + 1 ];\n\n\t\tconst tMin = tCenter - tHalf;\n\t\tconst tMax = tCenter + tHalf;\n\n\t\tif ( tMin < bounds[ d ] ) {\n\n\t\t\tbounds[ d ] = tMin;\n\n\t\t}\n\n\t\tif ( tMax > bounds[ d + 3 ] ) {\n\n\t\t\tbounds[ d + 3 ] = tMax;\n\n\t\t}\n\n\t}\n\n}\n\n// compute bounds surface area\nexport function computeSurfaceArea( bounds ) {\n\n\tconst d0 = bounds[ 3 ] - bounds[ 0 ];\n\tconst d1 = bounds[ 4 ] - bounds[ 1 ];\n\tconst d2 = bounds[ 5 ] - bounds[ 2 ];\n\n\treturn 2 * ( d0 * d1 + d1 * d2 + d2 * d0 );\n\n}\n", "import { BufferAttribute } from 'three';\nimport { MeshBVHNode } from './MeshBVHNode.js';\nimport { getLongestEdgeIndex, computeSurfaceArea, copyBounds, unionBounds, expandByTriangleBounds } from '../utils/ArrayBoxUtilities.js';\nimport {\n\tCENTER, AVERAGE, SAH, TRIANGLE_INTERSECT_COST, TRAVERSAL_COST,\n\tBYTES_PER_NODE, FLOAT32_EPSILON, IS_LEAFNODE_FLAG,\n} from './Constants.js';\n\nfunction ensureIndex( geo, options ) {\n\n\tif ( ! geo.index ) {\n\n\t\tconst vertexCount = geo.attributes.position.count;\n\t\tconst BufferConstructor = options.useSharedArrayBuffer ? SharedArrayBuffer : ArrayBuffer;\n\t\tlet index;\n\t\tif ( vertexCount > 65535 ) {\n\n\t\t\tindex = new Uint32Array( new BufferConstructor( 4 * vertexCount ) );\n\n\t\t} else {\n\n\t\t\tindex = new Uint16Array( new BufferConstructor( 2 * vertexCount ) );\n\n\t\t}\n\n\t\tgeo.setIndex( new BufferAttribute( index, 1 ) );\n\n\t\tfor ( let i = 0; i < vertexCount; i ++ ) {\n\n\t\t\tindex[ i ] = i;\n\n\t\t}\n\n\t}\n\n}\n\n// Computes the set of { offset, count } ranges which need independent BVH roots. Each\n// region in the geometry index that belongs to a different set of material groups requires\n// a separate BVH root, so that triangles indices belonging to one group never get swapped\n// with triangle indices belongs to another group. For example, if the groups were like this:\n//\n// [-------------------------------------------------------------]\n// |__________________|\n//   g0 = [0, 20]  |______________________||_____________________|\n//                      g1 = [16, 40]           g2 = [41, 60]\n//\n// we would need four BVH roots: [0, 15], [16, 20], [21, 40], [41, 60].\nfunction getRootIndexRanges( geo ) {\n\n\tif ( ! geo.groups || ! geo.groups.length ) {\n\n\t\treturn [ { offset: 0, count: geo.index.count / 3 } ];\n\n\t}\n\n\tconst ranges = [];\n\tconst rangeBoundaries = new Set();\n\tfor ( const group of geo.groups ) {\n\n\t\trangeBoundaries.add( group.start );\n\t\trangeBoundaries.add( group.start + group.count );\n\n\t}\n\n\t// note that if you don't pass in a comparator, it sorts them lexicographically as strings :-(\n\tconst sortedBoundaries = Array.from( rangeBoundaries.values() ).sort( ( a, b ) => a - b );\n\tfor ( let i = 0; i < sortedBoundaries.length - 1; i ++ ) {\n\n\t\tconst start = sortedBoundaries[ i ], end = sortedBoundaries[ i + 1 ];\n\t\tranges.push( { offset: ( start / 3 ), count: ( end - start ) / 3 } );\n\n\t}\n\n\treturn ranges;\n\n}\n\n// computes the union of the bounds of all of the given triangles and puts the resulting box in target. If\n// centroidTarget is provided then a bounding box is computed for the centroids of the triangles, as well.\n// These are computed together to avoid redundant accesses to bounds array.\nfunction getBounds( triangleBounds, offset, count, target, centroidTarget = null ) {\n\n\tlet minx = Infinity;\n\tlet miny = Infinity;\n\tlet minz = Infinity;\n\tlet maxx = - Infinity;\n\tlet maxy = - Infinity;\n\tlet maxz = - Infinity;\n\n\tlet cminx = Infinity;\n\tlet cminy = Infinity;\n\tlet cminz = Infinity;\n\tlet cmaxx = - Infinity;\n\tlet cmaxy = - Infinity;\n\tlet cmaxz = - Infinity;\n\n\tconst includeCentroid = centroidTarget !== null;\n\tfor ( let i = offset * 6, end = ( offset + count ) * 6; i < end; i += 6 ) {\n\n\t\tconst cx = triangleBounds[ i + 0 ];\n\t\tconst hx = triangleBounds[ i + 1 ];\n\t\tconst lx = cx - hx;\n\t\tconst rx = cx + hx;\n\t\tif ( lx < minx ) minx = lx;\n\t\tif ( rx > maxx ) maxx = rx;\n\t\tif ( includeCentroid && cx < cminx ) cminx = cx;\n\t\tif ( includeCentroid && cx > cmaxx ) cmaxx = cx;\n\n\t\tconst cy = triangleBounds[ i + 2 ];\n\t\tconst hy = triangleBounds[ i + 3 ];\n\t\tconst ly = cy - hy;\n\t\tconst ry = cy + hy;\n\t\tif ( ly < miny ) miny = ly;\n\t\tif ( ry > maxy ) maxy = ry;\n\t\tif ( includeCentroid && cy < cminy ) cminy = cy;\n\t\tif ( includeCentroid && cy > cmaxy ) cmaxy = cy;\n\n\t\tconst cz = triangleBounds[ i + 4 ];\n\t\tconst hz = triangleBounds[ i + 5 ];\n\t\tconst lz = cz - hz;\n\t\tconst rz = cz + hz;\n\t\tif ( lz < minz ) minz = lz;\n\t\tif ( rz > maxz ) maxz = rz;\n\t\tif ( includeCentroid && cz < cminz ) cminz = cz;\n\t\tif ( includeCentroid && cz > cmaxz ) cmaxz = cz;\n\n\t}\n\n\ttarget[ 0 ] = minx;\n\ttarget[ 1 ] = miny;\n\ttarget[ 2 ] = minz;\n\n\ttarget[ 3 ] = maxx;\n\ttarget[ 4 ] = maxy;\n\ttarget[ 5 ] = maxz;\n\n\tif ( includeCentroid ) {\n\n\t\tcentroidTarget[ 0 ] = cminx;\n\t\tcentroidTarget[ 1 ] = cminy;\n\t\tcentroidTarget[ 2 ] = cminz;\n\n\t\tcentroidTarget[ 3 ] = cmaxx;\n\t\tcentroidTarget[ 4 ] = cmaxy;\n\t\tcentroidTarget[ 5 ] = cmaxz;\n\n\t}\n\n}\n\n// A stand alone function for retrieving the centroid bounds.\nfunction getCentroidBounds( triangleBounds, offset, count, centroidTarget ) {\n\n\tlet cminx = Infinity;\n\tlet cminy = Infinity;\n\tlet cminz = Infinity;\n\tlet cmaxx = - Infinity;\n\tlet cmaxy = - Infinity;\n\tlet cmaxz = - Infinity;\n\n\tfor ( let i = offset * 6, end = ( offset + count ) * 6; i < end; i += 6 ) {\n\n\t\tconst cx = triangleBounds[ i + 0 ];\n\t\tif ( cx < cminx ) cminx = cx;\n\t\tif ( cx > cmaxx ) cmaxx = cx;\n\n\t\tconst cy = triangleBounds[ i + 2 ];\n\t\tif ( cy < cminy ) cminy = cy;\n\t\tif ( cy > cmaxy ) cmaxy = cy;\n\n\t\tconst cz = triangleBounds[ i + 4 ];\n\t\tif ( cz < cminz ) cminz = cz;\n\t\tif ( cz > cmaxz ) cmaxz = cz;\n\n\t}\n\n\tcentroidTarget[ 0 ] = cminx;\n\tcentroidTarget[ 1 ] = cminy;\n\tcentroidTarget[ 2 ] = cminz;\n\n\tcentroidTarget[ 3 ] = cmaxx;\n\tcentroidTarget[ 4 ] = cmaxy;\n\tcentroidTarget[ 5 ] = cmaxz;\n\n}\n\n\n// reorders `tris` such that for `count` elements after `offset`, elements on the left side of the split\n// will be on the left and elements on the right side of the split will be on the right. returns the index\n// of the first element on the right side, or offset + count if there are no elements on the right side.\nfunction partition( index, triangleBounds, offset, count, split ) {\n\n\tlet left = offset;\n\tlet right = offset + count - 1;\n\tconst pos = split.pos;\n\tconst axisOffset = split.axis * 2;\n\n\t// hoare partitioning, see e.g. https://en.wikipedia.org/wiki/Quicksort#Hoare_partition_scheme\n\twhile ( true ) {\n\n\t\twhile ( left <= right && triangleBounds[ left * 6 + axisOffset ] < pos ) {\n\n\t\t\tleft ++;\n\n\t\t}\n\n\n\t\t// if a triangle center lies on the partition plane it is considered to be on the right side\n\t\twhile ( left <= right && triangleBounds[ right * 6 + axisOffset ] >= pos ) {\n\n\t\t\tright --;\n\n\t\t}\n\n\t\tif ( left < right ) {\n\n\t\t\t// we need to swap all of the information associated with the triangles at index\n\t\t\t// left and right; that's the verts in the geometry index, the bounds,\n\t\t\t// and perhaps the SAH planes\n\n\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\tlet t0 = index[ left * 3 + i ];\n\t\t\t\tindex[ left * 3 + i ] = index[ right * 3 + i ];\n\t\t\t\tindex[ right * 3 + i ] = t0;\n\n\t\t\t\tlet t1 = triangleBounds[ left * 6 + i * 2 + 0 ];\n\t\t\t\ttriangleBounds[ left * 6 + i * 2 + 0 ] = triangleBounds[ right * 6 + i * 2 + 0 ];\n\t\t\t\ttriangleBounds[ right * 6 + i * 2 + 0 ] = t1;\n\n\t\t\t\tlet t2 = triangleBounds[ left * 6 + i * 2 + 1 ];\n\t\t\t\ttriangleBounds[ left * 6 + i * 2 + 1 ] = triangleBounds[ right * 6 + i * 2 + 1 ];\n\t\t\t\ttriangleBounds[ right * 6 + i * 2 + 1 ] = t2;\n\n\t\t\t}\n\n\t\t\tleft ++;\n\t\t\tright --;\n\n\t\t} else {\n\n\t\t\treturn left;\n\n\t\t}\n\n\t}\n\n}\n\nconst BIN_COUNT = 32;\nconst binsSort = ( a, b ) => a.candidate - b.candidate;\nconst sahBins = new Array( BIN_COUNT ).fill().map( () => {\n\n\treturn {\n\n\t\tcount: 0,\n\t\tbounds: new Float32Array( 6 ),\n\t\trightCacheBounds: new Float32Array( 6 ),\n\t\tleftCacheBounds: new Float32Array( 6 ),\n\t\tcandidate: 0,\n\n\t};\n\n} );\nconst leftBounds = new Float32Array( 6 );\n\nfunction getOptimalSplit( nodeBoundingData, centroidBoundingData, triangleBounds, offset, count, strategy ) {\n\n\tlet axis = - 1;\n\tlet pos = 0;\n\n\t// Center\n\tif ( strategy === CENTER ) {\n\n\t\taxis = getLongestEdgeIndex( centroidBoundingData );\n\t\tif ( axis !== - 1 ) {\n\n\t\t\tpos = ( centroidBoundingData[ axis ] + centroidBoundingData[ axis + 3 ] ) / 2;\n\n\t\t}\n\n\t} else if ( strategy === AVERAGE ) {\n\n\t\taxis = getLongestEdgeIndex( nodeBoundingData );\n\t\tif ( axis !== - 1 ) {\n\n\t\t\tpos = getAverage( triangleBounds, offset, count, axis );\n\n\t\t}\n\n\t} else if ( strategy === SAH ) {\n\n\t\tconst rootSurfaceArea = computeSurfaceArea( nodeBoundingData );\n\t\tlet bestCost = TRIANGLE_INTERSECT_COST * count;\n\n\t\t// iterate over all axes\n\t\tconst cStart = offset * 6;\n\t\tconst cEnd = ( offset + count ) * 6;\n\t\tfor ( let a = 0; a < 3; a ++ ) {\n\n\t\t\tconst axisLeft = centroidBoundingData[ a ];\n\t\t\tconst axisRight = centroidBoundingData[ a + 3 ];\n\t\t\tconst axisLength = axisRight - axisLeft;\n\t\t\tconst binWidth = axisLength / BIN_COUNT;\n\n\t\t\t// If we have fewer triangles than we're planning to split then just check all\n\t\t\t// the triangle positions because it will be faster.\n\t\t\tif ( count < BIN_COUNT / 4 ) {\n\n\t\t\t\t// initialize the bin candidates\n\t\t\t\tconst truncatedBins = [ ...sahBins ];\n\t\t\t\ttruncatedBins.length = count;\n\n\t\t\t\t// set the candidates\n\t\t\t\tlet b = 0;\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6, b ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ b ];\n\t\t\t\t\tbin.candidate = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tbin.count = 0;\n\n\t\t\t\t\tconst {\n\t\t\t\t\t\tbounds,\n\t\t\t\t\t\tleftCacheBounds,\n\t\t\t\t\t\trightCacheBounds,\n\t\t\t\t\t} = bin;\n\t\t\t\t\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\t\t\t\t\trightCacheBounds[ d ] = Infinity;\n\t\t\t\t\t\trightCacheBounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t\tleftCacheBounds[ d ] = Infinity;\n\t\t\t\t\t\tleftCacheBounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t\tbounds[ d ] = Infinity;\n\t\t\t\t\t\tbounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t}\n\n\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bounds );\n\n\t\t\t\t}\n\n\t\t\t\ttruncatedBins.sort( binsSort );\n\n\t\t\t\t// remove redundant splits\n\t\t\t\tlet splitCount = count;\n\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\twhile ( bi + 1 < splitCount && truncatedBins[ bi + 1 ].candidate === bin.candidate ) {\n\n\t\t\t\t\t\ttruncatedBins.splice( bi + 1, 1 );\n\t\t\t\t\t\tsplitCount --;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// find the appropriate bin for each triangle and expand the bounds.\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6 ) {\n\n\t\t\t\t\tconst center = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\t\tif ( center >= bin.candidate ) {\n\n\t\t\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.rightCacheBounds );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.leftCacheBounds );\n\t\t\t\t\t\t\tbin.count ++;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// expand all the bounds\n\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\tconst leftCount = bin.count;\n\t\t\t\t\tconst rightCount = count - bin.count;\n\n\t\t\t\t\t// check the cost of this split\n\t\t\t\t\tconst leftBounds = bin.leftCacheBounds;\n\t\t\t\t\tconst rightBounds = bin.rightCacheBounds;\n\n\t\t\t\t\tlet leftProb = 0;\n\t\t\t\t\tif ( leftCount !== 0 ) {\n\n\t\t\t\t\t\tleftProb = computeSurfaceArea( leftBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tlet rightProb = 0;\n\t\t\t\t\tif ( rightCount !== 0 ) {\n\n\t\t\t\t\t\trightProb = computeSurfaceArea( rightBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst cost = TRAVERSAL_COST + TRIANGLE_INTERSECT_COST * (\n\t\t\t\t\t\tleftProb * leftCount + rightProb * rightCount\n\t\t\t\t\t);\n\n\t\t\t\t\tif ( cost < bestCost ) {\n\n\t\t\t\t\t\taxis = a;\n\t\t\t\t\t\tbestCost = cost;\n\t\t\t\t\t\tpos = bin.candidate;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\t// reset the bins\n\t\t\t\tfor ( let i = 0; i < BIN_COUNT; i ++ ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tbin.count = 0;\n\t\t\t\t\tbin.candidate = axisLeft + binWidth + i * binWidth;\n\n\t\t\t\t\tconst bounds = bin.bounds;\n\t\t\t\t\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\t\t\t\t\tbounds[ d ] = Infinity;\n\t\t\t\t\t\tbounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// iterate over all center positions\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6 ) {\n\n\t\t\t\t\tconst triCenter = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tconst relativeCenter = triCenter - axisLeft;\n\n\t\t\t\t\t// in the partition function if the centroid lies on the split plane then it is\n\t\t\t\t\t// considered to be on the right side of the split\n\t\t\t\t\tlet binIndex = ~ ~ ( relativeCenter / binWidth );\n\t\t\t\t\tif ( binIndex >= BIN_COUNT ) binIndex = BIN_COUNT - 1;\n\n\t\t\t\t\tconst bin = sahBins[ binIndex ];\n\t\t\t\t\tbin.count ++;\n\n\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.bounds );\n\n\t\t\t\t}\n\n\t\t\t\t// cache the unioned bounds from right to left so we don't have to regenerate them each time\n\t\t\t\tconst lastBin = sahBins[ BIN_COUNT - 1 ];\n\t\t\t\tcopyBounds( lastBin.bounds, lastBin.rightCacheBounds );\n\t\t\t\tfor ( let i = BIN_COUNT - 2; i >= 0; i -- ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tconst nextBin = sahBins[ i + 1 ];\n\t\t\t\t\tunionBounds( bin.bounds, nextBin.rightCacheBounds, bin.rightCacheBounds );\n\n\t\t\t\t}\n\n\t\t\t\tlet leftCount = 0;\n\t\t\t\tfor ( let i = 0; i < BIN_COUNT - 1; i ++ ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tconst binCount = bin.count;\n\t\t\t\t\tconst bounds = bin.bounds;\n\n\t\t\t\t\tconst nextBin = sahBins[ i + 1 ];\n\t\t\t\t\tconst rightBounds = nextBin.rightCacheBounds;\n\n\t\t\t\t\t// don't do anything with the bounds if the new bounds have no triangles\n\t\t\t\t\tif ( binCount !== 0 ) {\n\n\t\t\t\t\t\tif ( leftCount === 0 ) {\n\n\t\t\t\t\t\t\tcopyBounds( bounds, leftBounds );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\tunionBounds( bounds, leftBounds, leftBounds );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\tleftCount += binCount;\n\n\t\t\t\t\t// check the cost of this split\n\t\t\t\t\tlet leftProb = 0;\n\t\t\t\t\tlet rightProb = 0;\n\n\t\t\t\t\tif ( leftCount !== 0 ) {\n\n\t\t\t\t\t\tleftProb = computeSurfaceArea( leftBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst rightCount = count - leftCount;\n\t\t\t\t\tif ( rightCount !== 0 ) {\n\n\t\t\t\t\t\trightProb = computeSurfaceArea( rightBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst cost = TRAVERSAL_COST + TRIANGLE_INTERSECT_COST * (\n\t\t\t\t\t\tleftProb * leftCount + rightProb * rightCount\n\t\t\t\t\t);\n\n\t\t\t\t\tif ( cost < bestCost ) {\n\n\t\t\t\t\t\taxis = a;\n\t\t\t\t\t\tbestCost = cost;\n\t\t\t\t\t\tpos = bin.candidate;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t} else {\n\n\t\tconsole.warn( `MeshBVH: Invalid build strategy value ${ strategy } used.` );\n\n\t}\n\n\treturn { axis, pos };\n\n}\n\n// returns the average coordinate on the specified axis of the all the provided triangles\nfunction getAverage( triangleBounds, offset, count, axis ) {\n\n\tlet avg = 0;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tavg += triangleBounds[ i * 6 + axis * 2 ];\n\n\t}\n\n\treturn avg / count;\n\n}\n\n// precomputes the bounding box for each triangle; required for quickly calculating tree splits.\n// result is an array of size tris.length * 6 where triangle i maps to a\n// [x_center, x_delta, y_center, y_delta, z_center, z_delta] tuple starting at index i * 6,\n// representing the center and half-extent in each dimension of triangle i\nfunction computeTriangleBounds( geo, fullBounds ) {\n\n\tconst posAttr = geo.attributes.position;\n\tconst index = geo.index.array;\n\tconst triCount = index.length / 3;\n\tconst triangleBounds = new Float32Array( triCount * 6 );\n\tconst normalized = posAttr.normalized;\n\n\t// used for non-normalized positions\n\tconst posArr = posAttr.array;\n\n\t// support for an interleaved position buffer\n\tconst bufferOffset = posAttr.offset || 0;\n\tlet stride = 3;\n\tif ( posAttr.isInterleavedBufferAttribute ) {\n\n\t\tstride = posAttr.data.stride;\n\n\t}\n\n\t// used for normalized positions\n\tconst getters = [ 'getX', 'getY', 'getZ' ];\n\n\tfor ( let tri = 0; tri < triCount; tri ++ ) {\n\n\t\tconst tri3 = tri * 3;\n\t\tconst tri6 = tri * 6;\n\n\t\tlet ai, bi, ci;\n\n\t\tif ( normalized ) {\n\n\t\t\tai = index[ tri3 + 0 ];\n\t\t\tbi = index[ tri3 + 1 ];\n\t\t\tci = index[ tri3 + 2 ];\n\n\t\t} else {\n\n\t\t\tai = index[ tri3 + 0 ] * stride + bufferOffset;\n\t\t\tbi = index[ tri3 + 1 ] * stride + bufferOffset;\n\t\t\tci = index[ tri3 + 2 ] * stride + bufferOffset;\n\n\t\t}\n\n\t\tfor ( let el = 0; el < 3; el ++ ) {\n\n\t\t\tlet a, b, c;\n\n\t\t\tif ( normalized ) {\n\n\t\t\t\ta = posAttr[ getters[ el ] ]( ai );\n\t\t\t\tb = posAttr[ getters[ el ] ]( bi );\n\t\t\t\tc = posAttr[ getters[ el ] ]( ci );\n\n\t\t\t} else {\n\n\t\t\t\ta = posArr[ ai + el ];\n\t\t\t\tb = posArr[ bi + el ];\n\t\t\t\tc = posArr[ ci + el ];\n\n\t\t\t}\n\n\t\t\tlet min = a;\n\t\t\tif ( b < min ) min = b;\n\t\t\tif ( c < min ) min = c;\n\n\t\t\tlet max = a;\n\t\t\tif ( b > max ) max = b;\n\t\t\tif ( c > max ) max = c;\n\n\t\t\t// Increase the bounds size by float32 epsilon to avoid precision errors when\n\t\t\t// converting to 32 bit float. Scale the epsilon by the size of the numbers being\n\t\t\t// worked with.\n\t\t\tconst halfExtents = ( max - min ) / 2;\n\t\t\tconst el2 = el * 2;\n\t\t\ttriangleBounds[ tri6 + el2 + 0 ] = min + halfExtents;\n\t\t\ttriangleBounds[ tri6 + el2 + 1 ] = halfExtents + ( Math.abs( min ) + halfExtents ) * FLOAT32_EPSILON;\n\n\t\t\tif ( min < fullBounds[ el ] ) fullBounds[ el ] = min;\n\t\t\tif ( max > fullBounds[ el + 3 ] ) fullBounds[ el + 3 ] = max;\n\n\t\t}\n\n\t}\n\n\treturn triangleBounds;\n\n}\n\nexport function buildTree( geo, options ) {\n\n\tfunction triggerProgress( trianglesProcessed ) {\n\n\t\tif ( onProgress ) {\n\n\t\t\tonProgress( trianglesProcessed / totalTriangles );\n\n\t\t}\n\n\t}\n\n\t// either recursively splits the given node, creating left and right subtrees for it, or makes it a leaf node,\n\t// recording the offset and count of its triangles and writing them into the reordered geometry index.\n\tfunction splitNode( node, offset, count, centroidBoundingData = null, depth = 0 ) {\n\n\t\tif ( ! reachedMaxDepth && depth >= maxDepth ) {\n\n\t\t\treachedMaxDepth = true;\n\t\t\tif ( verbose ) {\n\n\t\t\t\tconsole.warn( `MeshBVH: Max depth of ${ maxDepth } reached when generating BVH. Consider increasing maxDepth.` );\n\t\t\t\tconsole.warn( geo );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// early out if we've met our capacity\n\t\tif ( count <= maxLeafTris || depth >= maxDepth ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\t\t\treturn node;\n\n\t\t}\n\n\t\t// Find where to split the volume\n\t\tconst split = getOptimalSplit( node.boundingData, centroidBoundingData, triangleBounds, offset, count, strategy );\n\t\tif ( split.axis === - 1 ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\t\t\treturn node;\n\n\t\t}\n\n\t\tconst splitOffset = partition( indexArray, triangleBounds, offset, count, split );\n\n\t\t// create the two new child nodes\n\t\tif ( splitOffset === offset || splitOffset === offset + count ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\n\t\t} else {\n\n\t\t\tnode.splitAxis = split.axis;\n\n\t\t\t// create the left child and compute its bounding box\n\t\t\tconst left = new MeshBVHNode();\n\t\t\tconst lstart = offset;\n\t\t\tconst lcount = splitOffset - offset;\n\t\t\tnode.left = left;\n\t\t\tleft.boundingData = new Float32Array( 6 );\n\n\t\t\tgetBounds( triangleBounds, lstart, lcount, left.boundingData, cacheCentroidBoundingData );\n\t\t\tsplitNode( left, lstart, lcount, cacheCentroidBoundingData, depth + 1 );\n\n\t\t\t// repeat for right\n\t\t\tconst right = new MeshBVHNode();\n\t\t\tconst rstart = splitOffset;\n\t\t\tconst rcount = count - lcount;\n\t\t\tnode.right = right;\n\t\t\tright.boundingData = new Float32Array( 6 );\n\n\t\t\tgetBounds( triangleBounds, rstart, rcount, right.boundingData, cacheCentroidBoundingData );\n\t\t\tsplitNode( right, rstart, rcount, cacheCentroidBoundingData, depth + 1 );\n\n\t\t}\n\n\t\treturn node;\n\n\t}\n\n\tensureIndex( geo, options );\n\n\t// Compute the full bounds of the geometry at the same time as triangle bounds because\n\t// we'll need it for the root bounds in the case with no groups and it should be fast here.\n\t// We can't use the geometrying bounding box if it's available because it may be out of date.\n\tconst fullBounds = new Float32Array( 6 );\n\tconst cacheCentroidBoundingData = new Float32Array( 6 );\n\tconst triangleBounds = computeTriangleBounds( geo, fullBounds );\n\tconst indexArray = geo.index.array;\n\tconst maxDepth = options.maxDepth;\n\tconst verbose = options.verbose;\n\tconst maxLeafTris = options.maxLeafTris;\n\tconst strategy = options.strategy;\n\tconst onProgress = options.onProgress;\n\tconst totalTriangles = geo.index.count / 3;\n\tlet reachedMaxDepth = false;\n\n\tconst roots = [];\n\tconst ranges = getRootIndexRanges( geo );\n\n\tif ( ranges.length === 1 ) {\n\n\t\tconst range = ranges[ 0 ];\n\t\tconst root = new MeshBVHNode();\n\t\troot.boundingData = fullBounds;\n\t\tgetCentroidBounds( triangleBounds, range.offset, range.count, cacheCentroidBoundingData );\n\n\t\tsplitNode( root, range.offset, range.count, cacheCentroidBoundingData );\n\t\troots.push( root );\n\n\t} else {\n\n\t\tfor ( let range of ranges ) {\n\n\t\t\tconst root = new MeshBVHNode();\n\t\t\troot.boundingData = new Float32Array( 6 );\n\t\t\tgetBounds( triangleBounds, range.offset, range.count, root.boundingData, cacheCentroidBoundingData );\n\n\t\t\tsplitNode( root, range.offset, range.count, cacheCentroidBoundingData );\n\t\t\troots.push( root );\n\n\t\t}\n\n\t}\n\n\treturn roots;\n\n}\n\nexport function buildPackedTree( geo, options ) {\n\n\t// boundingData  \t\t\t\t: 6 float32\n\t// right / offset \t\t\t\t: 1 uint32\n\t// splitAxis / isLeaf + count \t: 1 uint32 / 2 uint16\n\tconst roots = buildTree( geo, options );\n\n\tlet float32Array;\n\tlet uint32Array;\n\tlet uint16Array;\n\tconst packedRoots = [];\n\tconst BufferConstructor = options.useSharedArrayBuffer ? SharedArrayBuffer : ArrayBuffer;\n\tfor ( let i = 0; i < roots.length; i ++ ) {\n\n\t\tconst root = roots[ i ];\n\t\tlet nodeCount = countNodes( root );\n\n\t\tconst buffer = new BufferConstructor( BYTES_PER_NODE * nodeCount );\n\t\tfloat32Array = new Float32Array( buffer );\n\t\tuint32Array = new Uint32Array( buffer );\n\t\tuint16Array = new Uint16Array( buffer );\n\t\tpopulateBuffer( 0, root );\n\t\tpackedRoots.push( buffer );\n\n\t}\n\n\treturn packedRoots;\n\n\tfunction countNodes( node ) {\n\n\t\tif ( node.count ) {\n\n\t\t\treturn 1;\n\n\t\t} else {\n\n\t\t\treturn 1 + countNodes( node.left ) + countNodes( node.right );\n\n\t\t}\n\n\t}\n\n\tfunction populateBuffer( byteOffset, node ) {\n\n\t\tconst stride4Offset = byteOffset / 4;\n\t\tconst stride2Offset = byteOffset / 2;\n\t\tconst isLeaf = ! ! node.count;\n\t\tconst boundingData = node.boundingData;\n\t\tfor ( let i = 0; i < 6; i ++ ) {\n\n\t\t\tfloat32Array[ stride4Offset + i ] = boundingData[ i ];\n\n\t\t}\n\n\t\tif ( isLeaf ) {\n\n\t\t\tconst offset = node.offset;\n\t\t\tconst count = node.count;\n\t\t\tuint32Array[ stride4Offset + 6 ] = offset;\n\t\t\tuint16Array[ stride2Offset + 14 ] = count;\n\t\t\tuint16Array[ stride2Offset + 15 ] = IS_LEAFNODE_FLAG;\n\t\t\treturn byteOffset + BYTES_PER_NODE;\n\n\t\t} else {\n\n\t\t\tconst left = node.left;\n\t\t\tconst right = node.right;\n\t\t\tconst splitAxis = node.splitAxis;\n\n\t\t\tlet nextUnusedPointer;\n\t\t\tnextUnusedPointer = populateBuffer( byteOffset + BYTES_PER_NODE, left );\n\n\t\t\tif ( ( nextUnusedPointer / 4 ) > Math.pow( 2, 32 ) ) {\n\n\t\t\t\tthrow new Error( 'MeshBVH: Cannot store child pointer greater than 32 bits.' );\n\n\t\t\t}\n\n\t\t\tuint32Array[ stride4Offset + 6 ] = nextUnusedPointer / 4;\n\t\t\tnextUnusedPointer = populateBuffer( nextUnusedPointer, right );\n\n\t\t\tuint32Array[ stride4Offset + 7 ] = splitAxis;\n\t\t\treturn nextUnusedPointer;\n\n\t\t}\n\n\t}\n\n}\n", "import { Vector3 } from 'three';\n\nexport class SeparatingAxisBounds {\n\n\tconstructor() {\n\n\t\tthis.min = Infinity;\n\t\tthis.max = - Infinity;\n\n\t}\n\n\tsetFromPointsField( points, field ) {\n\n\t\tlet min = Infinity;\n\t\tlet max = - Infinity;\n\t\tfor ( let i = 0, l = points.length; i < l; i ++ ) {\n\n\t\t\tconst p = points[ i ];\n\t\t\tconst val = p[ field ];\n\t\t\tmin = val < min ? val : min;\n\t\t\tmax = val > max ? val : max;\n\n\t\t}\n\n\t\tthis.min = min;\n\t\tthis.max = max;\n\n\t}\n\n\tsetFromPoints( axis, points ) {\n\n\t\tlet min = Infinity;\n\t\tlet max = - Infinity;\n\t\tfor ( let i = 0, l = points.length; i < l; i ++ ) {\n\n\t\t\tconst p = points[ i ];\n\t\t\tconst val = axis.dot( p );\n\t\t\tmin = val < min ? val : min;\n\t\t\tmax = val > max ? val : max;\n\n\t\t}\n\n\t\tthis.min = min;\n\t\tthis.max = max;\n\n\t}\n\n\tisSeparated( other ) {\n\n\t\treturn this.min > other.max || other.min > this.max;\n\n\t}\n\n}\n\nSeparatingAxisBounds.prototype.setFromBox = ( function () {\n\n\tconst p = new Vector3();\n\treturn function setFromBox( axis, box ) {\n\n\t\tconst boxMin = box.min;\n\t\tconst boxMax = box.max;\n\t\tlet min = Infinity;\n\t\tlet max = - Infinity;\n\t\tfor ( let x = 0; x <= 1; x ++ ) {\n\n\t\t\tfor ( let y = 0; y <= 1; y ++ ) {\n\n\t\t\t\tfor ( let z = 0; z <= 1; z ++ ) {\n\n\t\t\t\t\tp.x = boxMin.x * x + boxMax.x * ( 1 - x );\n\t\t\t\t\tp.y = boxMin.y * y + boxMax.y * ( 1 - y );\n\t\t\t\t\tp.z = boxMin.z * z + boxMax.z * ( 1 - z );\n\n\t\t\t\t\tconst val = axis.dot( p );\n\t\t\t\t\tmin = Math.min( val, min );\n\t\t\t\t\tmax = Math.max( val, max );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tthis.min = min;\n\t\tthis.max = max;\n\n\t};\n\n} )();\n\nexport const areIntersecting = ( function () {\n\n\tconst cacheSatBounds = new SeparatingAxisBounds();\n\treturn function areIntersecting( shape1, shape2 ) {\n\n\t\tconst points1 = shape1.points;\n\t\tconst satAxes1 = shape1.satAxes;\n\t\tconst satBounds1 = shape1.satBounds;\n\n\t\tconst points2 = shape2.points;\n\t\tconst satAxes2 = shape2.satAxes;\n\t\tconst satBounds2 = shape2.satBounds;\n\n\t\t// check axes of the first shape\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = satBounds1[ i ];\n\t\t\tconst sa = satAxes1[ i ];\n\t\t\tcacheSatBounds.setFromPoints( sa, points2 );\n\t\t\tif ( sb.isSeparated( cacheSatBounds ) ) return false;\n\n\t\t}\n\n\t\t// check axes of the second shape\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = satBounds2[ i ];\n\t\t\tconst sa = satAxes2[ i ];\n\t\t\tcacheSatBounds.setFromPoints( sa, points1 );\n\t\t\tif ( sb.isSeparated( cacheSatBounds ) ) return false;\n\n\t\t}\n\n\t};\n\n} )();\n", "import { Vector3, Vector2, Plane, Line3 } from 'three';\n\nexport const closestPointLineToLine = ( function () {\n\n\t// https://github.com/juj/MathGeoLib/blob/master/src/Geometry/Line.cpp#L56\n\tconst dir1 = new Vector3();\n\tconst dir2 = new Vector3();\n\tconst v02 = new Vector3();\n\treturn function closestPointLineToLine( l1, l2, result ) {\n\n\t\tconst v0 = l1.start;\n\t\tconst v10 = dir1;\n\t\tconst v2 = l2.start;\n\t\tconst v32 = dir2;\n\n\t\tv02.subVectors( v0, v2 );\n\t\tdir1.subVectors( l1.end, l1.start );\n\t\tdir2.subVectors( l2.end, l2.start );\n\n\t\t// float d0232 = v02.Dot(v32);\n\t\tconst d0232 = v02.dot( v32 );\n\n\t\t// float d3210 = v32.Dot(v10);\n\t\tconst d3210 = v32.dot( v10 );\n\n\t\t// float d3232 = v32.Dot(v32);\n\t\tconst d3232 = v32.dot( v32 );\n\n\t\t// float d0210 = v02.Dot(v10);\n\t\tconst d0210 = v02.dot( v10 );\n\n\t\t// float d1010 = v10.Dot(v10);\n\t\tconst d1010 = v10.dot( v10 );\n\n\t\t// float denom = d1010*d3232 - d3210*d3210;\n\t\tconst denom = d1010 * d3232 - d3210 * d3210;\n\n\t\tlet d, d2;\n\t\tif ( denom !== 0 ) {\n\n\t\t\td = ( d0232 * d3210 - d0210 * d3232 ) / denom;\n\n\t\t} else {\n\n\t\t\td = 0;\n\n\t\t}\n\n\t\td2 = ( d0232 + d * d3210 ) / d3232;\n\n\t\tresult.x = d;\n\t\tresult.y = d2;\n\n\t};\n\n} )();\n\nexport const closestPointsSegmentToSegment = ( function () {\n\n\t// https://github.com/juj/MathGeoLib/blob/master/src/Geometry/LineSegment.cpp#L187\n\tconst paramResult = new Vector2();\n\tconst temp1 = new Vector3();\n\tconst temp2 = new Vector3();\n\treturn function closestPointsSegmentToSegment( l1, l2, target1, target2 ) {\n\n\t\tclosestPointLineToLine( l1, l2, paramResult );\n\n\t\tlet d = paramResult.x;\n\t\tlet d2 = paramResult.y;\n\t\tif ( d >= 0 && d <= 1 && d2 >= 0 && d2 <= 1 ) {\n\n\t\t\tl1.at( d, target1 );\n\t\t\tl2.at( d2, target2 );\n\n\t\t\treturn;\n\n\t\t} else if ( d >= 0 && d <= 1 ) {\n\n\t\t\t// Only d2 is out of bounds.\n\t\t\tif ( d2 < 0 ) {\n\n\t\t\t\tl2.at( 0, target2 );\n\n\t\t\t} else {\n\n\t\t\t\tl2.at( 1, target2 );\n\n\t\t\t}\n\n\t\t\tl1.closestPointToPoint( target2, true, target1 );\n\t\t\treturn;\n\n\t\t} else if ( d2 >= 0 && d2 <= 1 ) {\n\n\t\t\t// Only d is out of bounds.\n\t\t\tif ( d < 0 ) {\n\n\t\t\t\tl1.at( 0, target1 );\n\n\t\t\t} else {\n\n\t\t\t\tl1.at( 1, target1 );\n\n\t\t\t}\n\n\t\t\tl2.closestPointToPoint( target1, true, target2 );\n\t\t\treturn;\n\n\t\t} else {\n\n\t\t\t// Both u and u2 are out of bounds.\n\t\t\tlet p;\n\t\t\tif ( d < 0 ) {\n\n\t\t\t\tp = l1.start;\n\n\t\t\t} else {\n\n\t\t\t\tp = l1.end;\n\n\t\t\t}\n\n\t\t\tlet p2;\n\t\t\tif ( d2 < 0 ) {\n\n\t\t\t\tp2 = l2.start;\n\n\t\t\t} else {\n\n\t\t\t\tp2 = l2.end;\n\n\t\t\t}\n\n\t\t\tconst closestPoint = temp1;\n\t\t\tconst closestPoint2 = temp2;\n\t\t\tl1.closestPointToPoint( p2, true, temp1 );\n\t\t\tl2.closestPointToPoint( p, true, temp2 );\n\n\t\t\tif ( closestPoint.distanceToSquared( p2 ) <= closestPoint2.distanceToSquared( p ) ) {\n\n\t\t\t\ttarget1.copy( closestPoint );\n\t\t\t\ttarget2.copy( p2 );\n\t\t\t\treturn;\n\n\t\t\t} else {\n\n\t\t\t\ttarget1.copy( p );\n\t\t\t\ttarget2.copy( closestPoint2 );\n\t\t\t\treturn;\n\n\t\t\t}\n\n\t\t}\n\n\t};\n\n} )();\n\n\nexport const sphereIntersectTriangle = ( function () {\n\n\t// https://stackoverflow.com/questions/34043955/detect-collision-between-sphere-and-triangle-in-three-js\n\tconst closestPointTemp = new Vector3();\n\tconst projectedPointTemp = new Vector3();\n\tconst planeTemp = new Plane();\n\tconst lineTemp = new Line3();\n\treturn function sphereIntersectTriangle( sphere, triangle ) {\n\n\t\tconst { radius, center } = sphere;\n\t\tconst { a, b, c } = triangle;\n\n\t\t// phase 1\n\t\tlineTemp.start = a;\n\t\tlineTemp.end = b;\n\t\tconst closestPoint1 = lineTemp.closestPointToPoint( center, true, closestPointTemp );\n\t\tif ( closestPoint1.distanceTo( center ) <= radius ) return true;\n\n\t\tlineTemp.start = a;\n\t\tlineTemp.end = c;\n\t\tconst closestPoint2 = lineTemp.closestPointToPoint( center, true, closestPointTemp );\n\t\tif ( closestPoint2.distanceTo( center ) <= radius ) return true;\n\n\t\tlineTemp.start = b;\n\t\tlineTemp.end = c;\n\t\tconst closestPoint3 = lineTemp.closestPointToPoint( center, true, closestPointTemp );\n\t\tif ( closestPoint3.distanceTo( center ) <= radius ) return true;\n\n\t\t// phase 2\n\t\tconst plane = triangle.getPlane( planeTemp );\n\t\tconst dp = Math.abs( plane.distanceToPoint( center ) );\n\t\tif ( dp <= radius ) {\n\n\t\t\tconst pp = plane.projectPoint( center, projectedPointTemp );\n\t\t\tconst cp = triangle.containsPoint( pp );\n\t\t\tif ( cp ) return true;\n\n\t\t}\n\n\t\treturn false;\n\n\t};\n\n} )();\n", "import { Triangle, Vector3, Line3, Sphere, Plane } from 'three';\nimport { SeparatingAxisBounds } from './SeparatingAxisBounds.js';\nimport { closestPointsSegmentToSegment, sphereIntersectTriangle } from './MathUtilities.js';\n\nconst DIST_EPSILON = 1e-15;\nfunction isNearZero( value ) {\n\n\treturn Math.abs( value ) < DIST_EPSILON;\n\n}\n\nexport class ExtendedTriangle extends Triangle {\n\n\tconstructor( ...args ) {\n\n\t\tsuper( ...args );\n\n\t\tthis.isExtendedTriangle = true;\n\t\tthis.satAxes = new Array( 4 ).fill().map( () => new Vector3() );\n\t\tthis.satBounds = new Array( 4 ).fill().map( () => new SeparatingAxisBounds() );\n\t\tthis.points = [ this.a, this.b, this.c ];\n\t\tthis.sphere = new Sphere();\n\t\tthis.plane = new Plane();\n\t\tthis.needsUpdate = true;\n\n\t}\n\n\tintersectsSphere( sphere ) {\n\n\t\treturn sphereIntersectTriangle( sphere, this );\n\n\t}\n\n\tupdate() {\n\n\t\tconst a = this.a;\n\t\tconst b = this.b;\n\t\tconst c = this.c;\n\t\tconst points = this.points;\n\n\t\tconst satAxes = this.satAxes;\n\t\tconst satBounds = this.satBounds;\n\n\t\tconst axis0 = satAxes[ 0 ];\n\t\tconst sab0 = satBounds[ 0 ];\n\t\tthis.getNormal( axis0 );\n\t\tsab0.setFromPoints( axis0, points );\n\n\t\tconst axis1 = satAxes[ 1 ];\n\t\tconst sab1 = satBounds[ 1 ];\n\t\taxis1.subVectors( a, b );\n\t\tsab1.setFromPoints( axis1, points );\n\n\t\tconst axis2 = satAxes[ 2 ];\n\t\tconst sab2 = satBounds[ 2 ];\n\t\taxis2.subVectors( b, c );\n\t\tsab2.setFromPoints( axis2, points );\n\n\t\tconst axis3 = satAxes[ 3 ];\n\t\tconst sab3 = satBounds[ 3 ];\n\t\taxis3.subVectors( c, a );\n\t\tsab3.setFromPoints( axis3, points );\n\n\t\tthis.sphere.setFromPoints( this.points );\n\t\tthis.plane.setFromNormalAndCoplanarPoint( axis0, a );\n\t\tthis.needsUpdate = false;\n\n\t}\n\n}\n\nExtendedTriangle.prototype.closestPointToSegment = ( function () {\n\n\tconst point1 = new Vector3();\n\tconst point2 = new Vector3();\n\tconst edge = new Line3();\n\n\treturn function distanceToSegment( segment, target1 = null, target2 = null ) {\n\n\t\tconst { start, end } = segment;\n\t\tconst points = this.points;\n\t\tlet distSq;\n\t\tlet closestDistanceSq = Infinity;\n\n\t\t// check the triangle edges\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst nexti = ( i + 1 ) % 3;\n\t\t\tedge.start.copy( points[ i ] );\n\t\t\tedge.end.copy( points[ nexti ] );\n\n\t\t\tclosestPointsSegmentToSegment( edge, segment, point1, point2 );\n\n\t\t\tdistSq = point1.distanceToSquared( point2 );\n\t\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = distSq;\n\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// check end points\n\t\tthis.closestPointToPoint( start, point1 );\n\t\tdistSq = start.distanceToSquared( point1 );\n\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\tclosestDistanceSq = distSq;\n\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\tif ( target2 ) target2.copy( start );\n\n\t\t}\n\n\t\tthis.closestPointToPoint( end, point1 );\n\t\tdistSq = end.distanceToSquared( point1 );\n\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\tclosestDistanceSq = distSq;\n\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\tif ( target2 ) target2.copy( end );\n\n\t\t}\n\n\t\treturn Math.sqrt( closestDistanceSq );\n\n\t};\n\n} )();\n\nExtendedTriangle.prototype.intersectsTriangle = ( function () {\n\n\tconst saTri2 = new ExtendedTriangle();\n\tconst arr1 = new Array( 3 );\n\tconst arr2 = new Array( 3 );\n\tconst cachedSatBounds = new SeparatingAxisBounds();\n\tconst cachedSatBounds2 = new SeparatingAxisBounds();\n\tconst cachedAxis = new Vector3();\n\tconst dir1 = new Vector3();\n\tconst dir2 = new Vector3();\n\tconst tempDir = new Vector3();\n\tconst edge = new Line3();\n\tconst edge1 = new Line3();\n\tconst edge2 = new Line3();\n\n\t// TODO: If the triangles are coplanar and intersecting the target is nonsensical. It should at least\n\t// be a line contained by both triangles if not a different special case somehow represented in the return result.\n\treturn function intersectsTriangle( other, target = null, suppressLog = false ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tif ( ! other.isExtendedTriangle ) {\n\n\t\t\tsaTri2.copy( other );\n\t\t\tsaTri2.update();\n\t\t\tother = saTri2;\n\n\t\t} else if ( other.needsUpdate ) {\n\n\t\t\tother.update();\n\n\t\t}\n\n\t\tconst plane1 = this.plane;\n\t\tconst plane2 = other.plane;\n\n\t\tif ( Math.abs( plane1.normal.dot( plane2.normal ) ) > 1.0 - 1e-10 ) {\n\n\t\t\t// perform separating axis intersection test only for coplanar triangles\n\t\t\tconst satBounds1 = this.satBounds;\n\t\t\tconst satAxes1 = this.satAxes;\n\t\t\tarr2[ 0 ] = other.a;\n\t\t\tarr2[ 1 ] = other.b;\n\t\t\tarr2[ 2 ] = other.c;\n\t\t\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\t\t\tconst sb = satBounds1[ i ];\n\t\t\t\tconst sa = satAxes1[ i ];\n\t\t\t\tcachedSatBounds.setFromPoints( sa, arr2 );\n\t\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t\t}\n\n\t\t\tconst satBounds2 = other.satBounds;\n\t\t\tconst satAxes2 = other.satAxes;\n\t\t\tarr1[ 0 ] = this.a;\n\t\t\tarr1[ 1 ] = this.b;\n\t\t\tarr1[ 2 ] = this.c;\n\t\t\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\t\t\tconst sb = satBounds2[ i ];\n\t\t\t\tconst sa = satAxes2[ i ];\n\t\t\t\tcachedSatBounds.setFromPoints( sa, arr1 );\n\t\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t\t}\n\n\t\t\t// check crossed axes\n\t\t\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\t\t\tconst sa1 = satAxes1[ i ];\n\t\t\t\tfor ( let i2 = 0; i2 < 4; i2 ++ ) {\n\n\t\t\t\t\tconst sa2 = satAxes2[ i2 ];\n\t\t\t\t\tcachedAxis.crossVectors( sa1, sa2 );\n\t\t\t\t\tcachedSatBounds.setFromPoints( cachedAxis, arr1 );\n\t\t\t\t\tcachedSatBounds2.setFromPoints( cachedAxis, arr2 );\n\t\t\t\t\tif ( cachedSatBounds.isSeparated( cachedSatBounds2 ) ) return false;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( target ) {\n\n\t\t\t\t// TODO find two points that intersect on the edges and make that the result\n\t\t\t\tif ( ! suppressLog ) {\n\n\t\t\t\t\tconsole.warn( 'ExtendedTriangle.intersectsTriangle: Triangles are coplanar which does not support an output edge. Setting edge to 0, 0, 0.' );\n\n\t\t\t\t}\n\n\t\t\t\ttarget.start.set( 0, 0, 0 );\n\t\t\t\ttarget.end.set( 0, 0, 0 );\n\n\t\t\t}\n\n\t\t\treturn true;\n\n\t\t} else {\n\n\t\t\t// find the edge that intersects the other triangle plane\n\t\t\tconst points1 = this.points;\n\t\t\tlet found1 = false;\n\t\t\tlet count1 = 0;\n\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\tconst p = points1[ i ];\n\t\t\t\tconst pNext = points1[ ( i + 1 ) % 3 ];\n\n\t\t\t\tedge.start.copy( p );\n\t\t\t\tedge.end.copy( pNext );\n\t\t\t\tedge.delta( dir1 );\n\n\t\t\t\tconst targetPoint = found1 ? edge1.start : edge1.end;\n\t\t\t\tconst startIntersects = isNearZero( plane2.distanceToPoint( p ) );\n\t\t\t\tif ( isNearZero( plane2.normal.dot( dir1 ) ) && startIntersects ) {\n\n\t\t\t\t\t// if the edge lies on the plane then take the line\n\t\t\t\t\tedge1.copy( edge );\n\t\t\t\t\tcount1 = 2;\n\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t\t// check if the start point is near the plane because \"intersectLine\" is not robust to that case\n\t\t\t\tconst doesIntersect = plane2.intersectLine( edge, targetPoint ) || startIntersects;\n\t\t\t\tif ( doesIntersect && ! isNearZero( targetPoint.distanceTo( pNext ) ) ) {\n\n\t\t\t\t\tcount1 ++;\n\t\t\t\t\tif ( found1 ) {\n\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tfound1 = true;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( count1 === 1 && other.containsPoint( edge1.end ) ) {\n\n\t\t\t\tif ( target ) {\n\n\t\t\t\t\ttarget.start.copy( edge1.end );\n\t\t\t\t\ttarget.end.copy( edge1.end );\n\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\n\t\t\t} else if ( count1 !== 2 ) {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\t// find the other triangles edge that intersects this plane\n\t\t\tconst points2 = other.points;\n\t\t\tlet found2 = false;\n\t\t\tlet count2 = 0;\n\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\tconst p = points2[ i ];\n\t\t\t\tconst pNext = points2[ ( i + 1 ) % 3 ];\n\n\t\t\t\tedge.start.copy( p );\n\t\t\t\tedge.end.copy( pNext );\n\t\t\t\tedge.delta( dir2 );\n\n\t\t\t\tconst targetPoint = found2 ? edge2.start : edge2.end;\n\t\t\t\tconst startIntersects = isNearZero( plane1.distanceToPoint( p ) );\n\t\t\t\tif ( isNearZero( plane1.normal.dot( dir2 ) ) && startIntersects ) {\n\n\t\t\t\t\t// if the edge lies on the plane then take the line\n\t\t\t\t\tedge2.copy( edge );\n\t\t\t\t\tcount2 = 2;\n\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t\t// check if the start point is near the plane because \"intersectLine\" is not robust to that case\n\t\t\t\tconst doesIntersect = plane1.intersectLine( edge, targetPoint ) || startIntersects;\n\t\t\t\tif ( doesIntersect && ! isNearZero( targetPoint.distanceTo( pNext ) ) ) {\n\n\t\t\t\t\tcount2 ++;\n\t\t\t\t\tif ( found2 ) {\n\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tfound2 = true;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( count2 === 1 && this.containsPoint( edge2.end ) ) {\n\n\t\t\t\tif ( target ) {\n\n\t\t\t\t\ttarget.start.copy( edge2.end );\n\t\t\t\t\ttarget.end.copy( edge2.end );\n\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\n\t\t\t} else if ( count2 !== 2 ) {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\t// find swap the second edge so both lines are running the same direction\n\t\t\tedge1.delta( dir1 );\n\t\t\tedge2.delta( dir2 );\n\n\t\t\tif ( dir1.dot( dir2 ) < 0 ) {\n\n\t\t\t\tlet tmp = edge2.start;\n\t\t\t\tedge2.start = edge2.end;\n\t\t\t\tedge2.end = tmp;\n\n\t\t\t}\n\n\t\t\t// check if the edges are overlapping\n\t\t\tconst s1 = edge1.start.dot( dir1 );\n\t\t\tconst e1 = edge1.end.dot( dir1 );\n\t\t\tconst s2 = edge2.start.dot( dir1 );\n\t\t\tconst e2 = edge2.end.dot( dir1 );\n\t\t\tconst separated1 = e1 < s2;\n\t\t\tconst separated2 = s1 < e2;\n\n\t\t\tif ( s1 !== e2 && s2 !== e1 && separated1 === separated2 ) {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\t// assign the target output\n\t\t\tif ( target ) {\n\n\t\t\t\ttempDir.subVectors( edge1.start, edge2.start );\n\t\t\t\tif ( tempDir.dot( dir1 ) > 0 ) {\n\n\t\t\t\t\ttarget.start.copy( edge1.start );\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttarget.start.copy( edge2.start );\n\n\t\t\t\t}\n\n\t\t\t\ttempDir.subVectors( edge1.end, edge2.end );\n\t\t\t\tif ( tempDir.dot( dir1 ) < 0 ) {\n\n\t\t\t\t\ttarget.end.copy( edge1.end );\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttarget.end.copy( edge2.end );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn true;\n\n\t\t}\n\n\t};\n\n} )();\n\n\nExtendedTriangle.prototype.distanceToPoint = ( function () {\n\n\tconst target = new Vector3();\n\treturn function distanceToPoint( point ) {\n\n\t\tthis.closestPointToPoint( point, target );\n\t\treturn point.distanceTo( target );\n\n\t};\n\n} )();\n\n\nExtendedTriangle.prototype.distanceToTriangle = ( function () {\n\n\tconst point = new Vector3();\n\tconst point2 = new Vector3();\n\tconst cornerFields = [ 'a', 'b', 'c' ];\n\tconst line1 = new Line3();\n\tconst line2 = new Line3();\n\n\treturn function distanceToTriangle( other, target1 = null, target2 = null ) {\n\n\t\tconst lineTarget = target1 || target2 ? line1 : null;\n\t\tif ( this.intersectsTriangle( other, lineTarget ) ) {\n\n\t\t\tif ( target1 || target2 ) {\n\n\t\t\t\tif ( target1 ) lineTarget.getCenter( target1 );\n\t\t\t\tif ( target2 ) lineTarget.getCenter( target2 );\n\n\t\t\t}\n\n\t\t\treturn 0;\n\n\t\t}\n\n\t\tlet closestDistanceSq = Infinity;\n\n\t\t// check all point distances\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tlet dist;\n\t\t\tconst field = cornerFields[ i ];\n\t\t\tconst otherVec = other[ field ];\n\t\t\tthis.closestPointToPoint( otherVec, point );\n\n\t\t\tdist = otherVec.distanceToSquared( point );\n\n\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\tif ( target1 ) target1.copy( point );\n\t\t\t\tif ( target2 ) target2.copy( otherVec );\n\n\t\t\t}\n\n\n\t\t\tconst thisVec = this[ field ];\n\t\t\tother.closestPointToPoint( thisVec, point );\n\n\t\t\tdist = thisVec.distanceToSquared( point );\n\n\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\tif ( target1 ) target1.copy( thisVec );\n\t\t\t\tif ( target2 ) target2.copy( point );\n\n\t\t\t}\n\n\t\t}\n\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst f11 = cornerFields[ i ];\n\t\t\tconst f12 = cornerFields[ ( i + 1 ) % 3 ];\n\t\t\tline1.set( this[ f11 ], this[ f12 ] );\n\t\t\tfor ( let i2 = 0; i2 < 3; i2 ++ ) {\n\n\t\t\t\tconst f21 = cornerFields[ i2 ];\n\t\t\t\tconst f22 = cornerFields[ ( i2 + 1 ) % 3 ];\n\t\t\t\tline2.set( other[ f21 ], other[ f22 ] );\n\n\t\t\t\tclosestPointsSegmentToSegment( line1, line2, point, point2 );\n\n\t\t\t\tconst dist = point.distanceToSquared( point2 );\n\t\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\t\tif ( target1 ) target1.copy( point );\n\t\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Math.sqrt( closestDistanceSq );\n\n\t};\n\n} )();\n", "import { Vector3, Matrix4, Line3 } from 'three';\nimport { SeparatingAxisBounds } from './SeparatingAxisBounds.js';\nimport { ExtendedTriangle } from './ExtendedTriangle.js';\nimport { closestPointsSegmentToSegment } from './MathUtilities.js';\n\nexport class OrientedBox {\n\n\tconstructor( min, max, matrix ) {\n\n\t\tthis.isOrientedBox = true;\n\t\tthis.min = new Vector3();\n\t\tthis.max = new Vector3();\n\t\tthis.matrix = new Matrix4();\n\t\tthis.invMatrix = new Matrix4();\n\t\tthis.points = new Array( 8 ).fill().map( () => new Vector3() );\n\t\tthis.satAxes = new Array( 3 ).fill().map( () => new Vector3() );\n\t\tthis.satBounds = new Array( 3 ).fill().map( () => new SeparatingAxisBounds() );\n\t\tthis.alignedSatBounds = new Array( 3 ).fill().map( () => new SeparatingAxisBounds() );\n\t\tthis.needsUpdate = false;\n\n\t\tif ( min ) this.min.copy( min );\n\t\tif ( max ) this.max.copy( max );\n\t\tif ( matrix ) this.matrix.copy( matrix );\n\n\t}\n\n\tset( min, max, matrix ) {\n\n\t\tthis.min.copy( min );\n\t\tthis.max.copy( max );\n\t\tthis.matrix.copy( matrix );\n\t\tthis.needsUpdate = true;\n\n\t}\n\n\tcopy( other ) {\n\n\t\tthis.min.copy( other.min );\n\t\tthis.max.copy( other.max );\n\t\tthis.matrix.copy( other.matrix );\n\t\tthis.needsUpdate = true;\n\n\t}\n\n}\n\nOrientedBox.prototype.update = ( function () {\n\n\treturn function update() {\n\n\t\tconst matrix = this.matrix;\n\t\tconst min = this.min;\n\t\tconst max = this.max;\n\n\t\tconst points = this.points;\n\t\tfor ( let x = 0; x <= 1; x ++ ) {\n\n\t\t\tfor ( let y = 0; y <= 1; y ++ ) {\n\n\t\t\t\tfor ( let z = 0; z <= 1; z ++ ) {\n\n\t\t\t\t\tconst i = ( ( 1 << 0 ) * x ) | ( ( 1 << 1 ) * y ) | ( ( 1 << 2 ) * z );\n\t\t\t\t\tconst v = points[ i ];\n\t\t\t\t\tv.x = x ? max.x : min.x;\n\t\t\t\t\tv.y = y ? max.y : min.y;\n\t\t\t\t\tv.z = z ? max.z : min.z;\n\n\t\t\t\t\tv.applyMatrix4( matrix );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tconst satBounds = this.satBounds;\n\t\tconst satAxes = this.satAxes;\n\t\tconst minVec = points[ 0 ];\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst axis = satAxes[ i ];\n\t\t\tconst sb = satBounds[ i ];\n\t\t\tconst index = 1 << i;\n\t\t\tconst pi = points[ index ];\n\n\t\t\taxis.subVectors( minVec, pi );\n\t\t\tsb.setFromPoints( axis, points );\n\n\t\t}\n\n\t\tconst alignedSatBounds = this.alignedSatBounds;\n\t\talignedSatBounds[ 0 ].setFromPointsField( points, 'x' );\n\t\talignedSatBounds[ 1 ].setFromPointsField( points, 'y' );\n\t\talignedSatBounds[ 2 ].setFromPointsField( points, 'z' );\n\n\t\tthis.invMatrix.copy( this.matrix ).invert();\n\t\tthis.needsUpdate = false;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.intersectsBox = ( function () {\n\n\tconst aabbBounds = new SeparatingAxisBounds();\n\treturn function intersectsBox( box ) {\n\n\t\t// TODO: should this be doing SAT against the AABB?\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tconst min = box.min;\n\t\tconst max = box.max;\n\t\tconst satBounds = this.satBounds;\n\t\tconst satAxes = this.satAxes;\n\t\tconst alignedSatBounds = this.alignedSatBounds;\n\n\t\taabbBounds.min = min.x;\n\t\taabbBounds.max = max.x;\n\t\tif ( alignedSatBounds[ 0 ].isSeparated( aabbBounds ) ) return false;\n\n\t\taabbBounds.min = min.y;\n\t\taabbBounds.max = max.y;\n\t\tif ( alignedSatBounds[ 1 ].isSeparated( aabbBounds ) ) return false;\n\n\t\taabbBounds.min = min.z;\n\t\taabbBounds.max = max.z;\n\t\tif ( alignedSatBounds[ 2 ].isSeparated( aabbBounds ) ) return false;\n\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst axis = satAxes[ i ];\n\t\t\tconst sb = satBounds[ i ];\n\t\t\taabbBounds.setFromBox( axis, box );\n\t\t\tif ( sb.isSeparated( aabbBounds ) ) return false;\n\n\t\t}\n\n\t\treturn true;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.intersectsTriangle = ( function () {\n\n\tconst saTri = new ExtendedTriangle();\n\tconst pointsArr = new Array( 3 );\n\tconst cachedSatBounds = new SeparatingAxisBounds();\n\tconst cachedSatBounds2 = new SeparatingAxisBounds();\n\tconst cachedAxis = new Vector3();\n\treturn function intersectsTriangle( triangle ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tif ( ! triangle.isExtendedTriangle ) {\n\n\t\t\tsaTri.copy( triangle );\n\t\t\tsaTri.update();\n\t\t\ttriangle = saTri;\n\n\t\t} else if ( triangle.needsUpdate ) {\n\n\t\t\ttriangle.update();\n\n\t\t}\n\n\t\tconst satBounds = this.satBounds;\n\t\tconst satAxes = this.satAxes;\n\n\t\tpointsArr[ 0 ] = triangle.a;\n\t\tpointsArr[ 1 ] = triangle.b;\n\t\tpointsArr[ 2 ] = triangle.c;\n\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = satBounds[ i ];\n\t\t\tconst sa = satAxes[ i ];\n\t\t\tcachedSatBounds.setFromPoints( sa, pointsArr );\n\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t}\n\n\t\tconst triSatBounds = triangle.satBounds;\n\t\tconst triSatAxes = triangle.satAxes;\n\t\tconst points = this.points;\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = triSatBounds[ i ];\n\t\t\tconst sa = triSatAxes[ i ];\n\t\t\tcachedSatBounds.setFromPoints( sa, points );\n\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t}\n\n\t\t// check crossed axes\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sa1 = satAxes[ i ];\n\t\t\tfor ( let i2 = 0; i2 < 4; i2 ++ ) {\n\n\t\t\t\tconst sa2 = triSatAxes[ i2 ];\n\t\t\t\tcachedAxis.crossVectors( sa1, sa2 );\n\t\t\t\tcachedSatBounds.setFromPoints( cachedAxis, pointsArr );\n\t\t\t\tcachedSatBounds2.setFromPoints( cachedAxis, points );\n\t\t\t\tif ( cachedSatBounds.isSeparated( cachedSatBounds2 ) ) return false;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn true;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.closestPointToPoint = ( function () {\n\n\treturn function closestPointToPoint( point, target1 ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\ttarget1\n\t\t\t.copy( point )\n\t\t\t.applyMatrix4( this.invMatrix )\n\t\t\t.clamp( this.min, this.max )\n\t\t\t.applyMatrix4( this.matrix );\n\n\t\treturn target1;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.distanceToPoint = ( function () {\n\n\tconst target = new Vector3();\n\treturn function distanceToPoint( point ) {\n\n\t\tthis.closestPointToPoint( point, target );\n\t\treturn point.distanceTo( target );\n\n\t};\n\n} )();\n\nOrientedBox.prototype.distanceToBox = ( function () {\n\n\tconst xyzFields = [ 'x', 'y', 'z' ];\n\tconst segments1 = new Array( 12 ).fill().map( () => new Line3() );\n\tconst segments2 = new Array( 12 ).fill().map( () => new Line3() );\n\n\tconst point1 = new Vector3();\n\tconst point2 = new Vector3();\n\n\t// early out if we find a value below threshold\n\treturn function distanceToBox( box, threshold = 0, target1 = null, target2 = null ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tif ( this.intersectsBox( box ) ) {\n\n\t\t\tif ( target1 || target2 ) {\n\n\t\t\t\tbox.getCenter( point2 );\n\t\t\t\tthis.closestPointToPoint( point2, point1 );\n\t\t\t\tbox.closestPointToPoint( point1, point2 );\n\n\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t}\n\n\t\t\treturn 0;\n\n\t\t}\n\n\t\tconst threshold2 = threshold * threshold;\n\t\tconst min = box.min;\n\t\tconst max = box.max;\n\t\tconst points = this.points;\n\n\n\t\t// iterate over every edge and compare distances\n\t\tlet closestDistanceSq = Infinity;\n\n\t\t// check over all these points\n\t\tfor ( let i = 0; i < 8; i ++ ) {\n\n\t\t\tconst p = points[ i ];\n\t\t\tpoint2.copy( p ).clamp( min, max );\n\n\t\t\tconst dist = p.distanceToSquared( point2 );\n\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\tif ( target1 ) target1.copy( p );\n\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\tif ( dist < threshold2 ) return Math.sqrt( dist );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// generate and check all line segment distances\n\t\tlet count = 0;\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tfor ( let i1 = 0; i1 <= 1; i1 ++ ) {\n\n\t\t\t\tfor ( let i2 = 0; i2 <= 1; i2 ++ ) {\n\n\t\t\t\t\tconst nextIndex = ( i + 1 ) % 3;\n\t\t\t\t\tconst nextIndex2 = ( i + 2 ) % 3;\n\n\t\t\t\t\t// get obb line segments\n\t\t\t\t\tconst index = i1 << nextIndex | i2 << nextIndex2;\n\t\t\t\t\tconst index2 = 1 << i | i1 << nextIndex | i2 << nextIndex2;\n\t\t\t\t\tconst p1 = points[ index ];\n\t\t\t\t\tconst p2 = points[ index2 ];\n\t\t\t\t\tconst line1 = segments1[ count ];\n\t\t\t\t\tline1.set( p1, p2 );\n\n\n\t\t\t\t\t// get aabb line segments\n\t\t\t\t\tconst f1 = xyzFields[ i ];\n\t\t\t\t\tconst f2 = xyzFields[ nextIndex ];\n\t\t\t\t\tconst f3 = xyzFields[ nextIndex2 ];\n\t\t\t\t\tconst line2 = segments2[ count ];\n\t\t\t\t\tconst start = line2.start;\n\t\t\t\t\tconst end = line2.end;\n\n\t\t\t\t\tstart[ f1 ] = min[ f1 ];\n\t\t\t\t\tstart[ f2 ] = i1 ? min[ f2 ] : max[ f2 ];\n\t\t\t\t\tstart[ f3 ] = i2 ? min[ f3 ] : max[ f2 ];\n\n\t\t\t\t\tend[ f1 ] = max[ f1 ];\n\t\t\t\t\tend[ f2 ] = i1 ? min[ f2 ] : max[ f2 ];\n\t\t\t\t\tend[ f3 ] = i2 ? min[ f3 ] : max[ f2 ];\n\n\t\t\t\t\tcount ++;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t// check all the other boxes point\n\t\tfor ( let x = 0; x <= 1; x ++ ) {\n\n\t\t\tfor ( let y = 0; y <= 1; y ++ ) {\n\n\t\t\t\tfor ( let z = 0; z <= 1; z ++ ) {\n\n\t\t\t\t\tpoint2.x = x ? max.x : min.x;\n\t\t\t\t\tpoint2.y = y ? max.y : min.y;\n\t\t\t\t\tpoint2.z = z ? max.z : min.z;\n\n\t\t\t\t\tthis.closestPointToPoint( point2, point1 );\n\t\t\t\t\tconst dist = point2.distanceToSquared( point1 );\n\t\t\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\t\t\tif ( dist < threshold2 ) return Math.sqrt( dist );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tfor ( let i = 0; i < 12; i ++ ) {\n\n\t\t\tconst l1 = segments1[ i ];\n\t\t\tfor ( let i2 = 0; i2 < 12; i2 ++ ) {\n\n\t\t\t\tconst l2 = segments2[ i2 ];\n\t\t\t\tclosestPointsSegmentToSegment( l1, l2, point1, point2 );\n\t\t\t\tconst dist = point1.distanceToSquared( point2 );\n\t\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\t\tif ( dist < threshold2 ) return Math.sqrt( dist );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Math.sqrt( closestDistanceSq );\n\n\t};\n\n} )();\n", "import { Vector3, Vector2, Triangle, DoubleSide, BackSide } from 'three';\n\n// Ripped and modified From THREE.js Mesh raycast\n// https://github.com/mrdoob/three.js/blob/0aa87c999fe61e216c1133fba7a95772b503eddf/src/objects/Mesh.js#L115\nconst vA = /* @__PURE__ */ new Vector3();\nconst vB = /* @__PURE__ */ new Vector3();\nconst vC = /* @__PURE__ */ new Vector3();\n\nconst uvA = /* @__PURE__ */ new Vector2();\nconst uvB = /* @__PURE__ */ new Vector2();\nconst uvC = /* @__PURE__ */ new Vector2();\n\nconst intersectionPoint = /* @__PURE__ */ new Vector3();\nfunction checkIntersection( ray, pA, pB, pC, point, side ) {\n\n\tlet intersect;\n\tif ( side === BackSide ) {\n\n\t\tintersect = ray.intersectTriangle( pC, pB, pA, true, point );\n\n\t} else {\n\n\t\tintersect = ray.intersectTriangle( pA, pB, pC, side !== DoubleSide, point );\n\n\t}\n\n\tif ( intersect === null ) return null;\n\n\tconst distance = ray.origin.distanceTo( point );\n\n\treturn {\n\n\t\tdistance: distance,\n\t\tpoint: point.clone(),\n\n\t};\n\n}\n\nfunction checkBufferGeometryIntersection( ray, position, uv, a, b, c, side ) {\n\n\tvA.fromBufferAttribute( position, a );\n\tvB.fromBufferAttribute( position, b );\n\tvC.fromBufferAttribute( position, c );\n\n\tconst intersection = checkIntersection( ray, vA, vB, vC, intersectionPoint, side );\n\n\tif ( intersection ) {\n\n\t\tif ( uv ) {\n\n\t\t\tuvA.fromBufferAttribute( uv, a );\n\t\t\tuvB.fromBufferAttribute( uv, b );\n\t\t\tuvC.fromBufferAttribute( uv, c );\n\n\t\t\tintersection.uv = Triangle.getUV( intersectionPoint, vA, vB, vC, uvA, uvB, uvC, new Vector2( ) );\n\n\t\t}\n\n\t\tconst face = {\n\t\t\ta: a,\n\t\t\tb: b,\n\t\t\tc: c,\n\t\t\tnormal: new Vector3(),\n\t\t\tmaterialIndex: 0\n\t\t};\n\n\t\tTriangle.getNormal( vA, vB, vC, face.normal );\n\n\t\tintersection.face = face;\n\t\tintersection.faceIndex = a;\n\n\t}\n\n\treturn intersection;\n\n}\n\n// https://github.com/mrdoob/three.js/blob/0aa87c999fe61e216c1133fba7a95772b503eddf/src/objects/Mesh.js#L258\nfunction intersectTri( geo, side, ray, tri, intersections ) {\n\n\tconst triOffset = tri * 3;\n\tconst a = geo.index.getX( triOffset );\n\tconst b = geo.index.getX( triOffset + 1 );\n\tconst c = geo.index.getX( triOffset + 2 );\n\n\tconst intersection = checkBufferGeometryIntersection( ray, geo.attributes.position, geo.attributes.uv, a, b, c, side );\n\n\tif ( intersection ) {\n\n\t\tintersection.faceIndex = tri;\n\t\tif ( intersections ) intersections.push( intersection );\n\t\treturn intersection;\n\n\t}\n\n\treturn null;\n\n}\n\nexport { intersectTri };\n", "import { intersectTri } from './ThreeRayIntersectUtilities.js';\n\nexport function intersectTris( geo, side, ray, offset, count, intersections ) {\n\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tintersectTri( geo, side, ray, i, intersections );\n\n\t}\n\n}\n\nexport function intersectClosestTri( geo, side, ray, offset, count ) {\n\n\tlet dist = Infinity;\n\tlet res = null;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tconst intersection = intersectTri( geo, side, ray, i );\n\t\tif ( intersection && intersection.distance < dist ) {\n\n\t\t\tres = intersection;\n\t\t\tdist = intersection.distance;\n\n\t\t}\n\n\t}\n\n\treturn res;\n\n}\n\n// converts the given BVH raycast intersection to align with the three.js raycast\n// structure (include object, world space distance and point).\nexport function convertRaycastIntersect( hit, object, raycaster ) {\n\n\tif ( hit === null ) {\n\n\t\treturn null;\n\n\t}\n\n\thit.point.applyMatrix4( object.matrixWorld );\n\thit.distance = hit.point.distanceTo( raycaster.ray.origin );\n\thit.object = object;\n\n\tif ( hit.distance < raycaster.near || hit.distance > raycaster.far ) {\n\n\t\treturn null;\n\n\t} else {\n\n\t\treturn hit;\n\n\t}\n\n}\n", "\nimport { Vector2, Vector3, Triangle } from 'three';\n\n// sets the vertices of triangle `tri` with the 3 vertices after i\nexport function setTriangle( tri, i, index, pos ) {\n\n\tconst ta = tri.a;\n\tconst tb = tri.b;\n\tconst tc = tri.c;\n\n\tlet i0 = i;\n\tlet i1 = i + 1;\n\tlet i2 = i + 2;\n\tif ( index ) {\n\n\t\ti0 = index.getX( i );\n\t\ti1 = index.getX( i + 1 );\n\t\ti2 = index.getX( i + 2 );\n\n\t}\n\n\tta.x = pos.getX( i0 );\n\tta.y = pos.getY( i0 );\n\tta.z = pos.getZ( i0 );\n\n\ttb.x = pos.getX( i1 );\n\ttb.y = pos.getY( i1 );\n\ttb.z = pos.getZ( i1 );\n\n\ttc.x = pos.getX( i2 );\n\ttc.y = pos.getY( i2 );\n\ttc.z = pos.getZ( i2 );\n\n}\n\nexport function iterateOverTriangles(\n\toffset,\n\tcount,\n\tgeometry,\n\tintersectsTriangleFunc,\n\tcontained,\n\tdepth,\n\ttriangle\n) {\n\n\tconst index = geometry.index;\n\tconst pos = geometry.attributes.position;\n\tfor ( let i = offset, l = count + offset; i < l; i ++ ) {\n\n\t\tsetTriangle( triangle, i * 3, index, pos );\n\t\ttriangle.needsUpdate = true;\n\n\t\tif ( intersectsTriangleFunc( triangle, i, contained, depth ) ) {\n\n\t\t\treturn true;\n\n\t\t}\n\n\t}\n\n\treturn false;\n\n}\n\nconst tempV1 = /* @__PURE__ */ new Vector3();\nconst tempV2 = /* @__PURE__ */ new Vector3();\nconst tempV3 = /* @__PURE__ */ new Vector3();\nconst tempUV1 = /* @__PURE__ */ new Vector2();\nconst tempUV2 = /* @__PURE__ */ new Vector2();\nconst tempUV3 = /* @__PURE__ */ new Vector2();\n\nexport function getTriangleHitPointInfo( point, geometry, triangleIndex, target ) {\n\n\tconst indices = geometry.getIndex().array;\n\tconst positions = geometry.getAttribute( 'position' );\n\tconst uvs = geometry.getAttribute( 'uv' );\n\n\tconst a = indices[ triangleIndex * 3 ];\n\tconst b = indices[ triangleIndex * 3 + 1 ];\n\tconst c = indices[ triangleIndex * 3 + 2 ];\n\n\ttempV1.fromBufferAttribute( positions, a );\n\ttempV2.fromBufferAttribute( positions, b );\n\ttempV3.fromBufferAttribute( positions, c );\n\n\t// find the associated material index\n\tlet materialIndex = 0;\n\tconst groups = geometry.groups;\n\tconst firstVertexIndex = triangleIndex * 3;\n\tfor ( let i = 0, l = groups.length; i < l; i ++ ) {\n\n\t\tconst group = groups[ i ];\n\t\tconst { start, count } = group;\n\t\tif ( firstVertexIndex >= start && firstVertexIndex < start + count ) {\n\n\t\t\tmaterialIndex = group.materialIndex;\n\t\t\tbreak;\n\n\t\t}\n\n\t}\n\n\t// extract uvs\n\tlet uv = null;\n\tif ( uvs ) {\n\n\t\ttempUV1.fromBufferAttribute( uvs, a );\n\t\ttempUV2.fromBufferAttribute( uvs, b );\n\t\ttempUV3.fromBufferAttribute( uvs, c );\n\n\t\tif ( target && target.uv ) uv = target.uv;\n\t\telse uv = new Vector2();\n\n\t\tTriangle.getUV( point, tempV1, tempV2, tempV3, tempUV1, tempUV2, tempUV3, uv );\n\n\t}\n\n\t// adjust the provided target or create a new one\n\tif ( target ) {\n\n\t\tif ( ! target.face ) target.face = { };\n\t\ttarget.face.a = a;\n\t\ttarget.face.b = b;\n\t\ttarget.face.c = c;\n\t\ttarget.face.materialIndex = materialIndex;\n\t\tif ( ! target.face.normal ) target.face.normal = new Vector3();\n\t\tTriangle.getNormal( tempV1, tempV2, tempV3, target.face.normal );\n\n\t\tif ( uv ) target.uv = uv;\n\n\t\treturn target;\n\n\t} else {\n\n\t\treturn {\n\t\t\tface: {\n\t\t\t\ta: a,\n\t\t\t\tb: b,\n\t\t\t\tc: c,\n\t\t\t\tmaterialIndex: materialIndex,\n\t\t\t\tnormal: Triangle.getNormal( tempV1, tempV2, tempV3, new Vector3() )\n\t\t\t},\n\t\t\tuv: uv\n\t\t};\n\n\t}\n\n}\n", "export class PrimitivePool {\n\n\tconstructor( getNewPrimitive ) {\n\n\t\tthis._getNewPrimitive = getNewPrimitive;\n\t\tthis._primitives = [];\n\n\t}\n\n\tgetPrimitive() {\n\n\t\tconst primitives = this._primitives;\n\t\tif ( primitives.length === 0 ) {\n\n\t\t\treturn this._getNewPrimitive();\n\n\t\t} else {\n\n\t\t\treturn primitives.pop();\n\n\t\t}\n\n\t}\n\n\treleasePrimitive( primitive ) {\n\n\t\tthis._primitives.push( primitive );\n\n\t}\n\n}\n", "export function IS_LEAF( n16, uint16Array ) {\n\n\treturn uint16Array[ n16 + 15 ] === 0xFFFF;\n\n}\n\nexport function OFFSET( n32, uint32Array ) {\n\n\treturn uint32Array[ n32 + 6 ];\n\n}\n\nexport function COUNT( n16, uint16Array ) {\n\n\treturn uint16Array[ n16 + 14 ];\n\n}\n\nexport function LEFT_NODE( n32 ) {\n\n\treturn n32 + 8;\n\n}\n\nexport function RIGHT_NODE( n32, uint32Array ) {\n\n\treturn uint32Array[ n32 + 6 ];\n\n}\n\nexport function SPLIT_AXIS( n32, uint32Array ) {\n\n\treturn uint32Array[ n32 + 7 ];\n\n}\n\nexport function BOUNDING_DATA_INDEX( n32 ) {\n\n\treturn n32;\n\n}\n", "import { Box3, Vector3, Matrix4 } from 'three';\nimport { CONTAINED } from './Constants.js';\n\nimport { OrientedBox } from '../math/OrientedBox.js';\nimport { ExtendedTriangle } from '../math/ExtendedTriangle.js';\nimport { intersectTris, intersectClosestTri } from '../utils/GeometryRayIntersectUtilities.js';\nimport { setTriangle } from '../utils/TriangleUtilities.js';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nimport { PrimitivePool } from '../utils/PrimitivePool.js';\nimport { COUNT, OFFSET, LEFT_NODE, RIGHT_NODE, IS_LEAF, BOUNDING_DATA_INDEX, SPLIT_AXIS } from './nodeBufferFunctions.js';\n\nconst boundingBox = new Box3();\nconst boxIntersection = new Vector3();\nconst xyzFields = [ 'x', 'y', 'z' ];\n\nexport function raycast( nodeIndex32, geometry, side, ray, intersects ) {\n\n\tlet nodeIndex16 = nodeIndex32 * 2, float32Array = _float32Array, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\t\tintersectTris( geometry, side, ray, offset, count, intersects );\n\n\t} else {\n\n\t\tconst leftIndex = LEFT_NODE( nodeIndex32 );\n\t\tif ( intersectRay( leftIndex, float32Array, ray, boxIntersection ) ) {\n\n\t\t\traycast( leftIndex, geometry, side, ray, intersects );\n\n\t\t}\n\n\t\tconst rightIndex = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\tif ( intersectRay( rightIndex, float32Array, ray, boxIntersection ) ) {\n\n\t\t\traycast( rightIndex, geometry, side, ray, intersects );\n\n\t\t}\n\n\t}\n\n}\n\nexport function raycastFirst( nodeIndex32, geometry, side, ray ) {\n\n\tlet nodeIndex16 = nodeIndex32 * 2, float32Array = _float32Array, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\t\treturn intersectClosestTri( geometry, side, ray, offset, count );\n\n\t} else {\n\n\t\t// consider the position of the split plane with respect to the oncoming ray; whichever direction\n\t\t// the ray is coming from, look for an intersection among that side of the tree first\n\t\tconst splitAxis = SPLIT_AXIS( nodeIndex32, uint32Array );\n\t\tconst xyzAxis = xyzFields[ splitAxis ];\n\t\tconst rayDir = ray.direction[ xyzAxis ];\n\t\tconst leftToRight = rayDir >= 0;\n\n\t\t// c1 is the child to check first\n\t\tlet c1, c2;\n\t\tif ( leftToRight ) {\n\n\t\t\tc1 = LEFT_NODE( nodeIndex32 );\n\t\t\tc2 = RIGHT_NODE( nodeIndex32, uint32Array );\n\n\t\t} else {\n\n\t\t\tc1 = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\t\tc2 = LEFT_NODE( nodeIndex32 );\n\n\t\t}\n\n\t\tconst c1Intersection = intersectRay( c1, float32Array, ray, boxIntersection );\n\t\tconst c1Result = c1Intersection ? raycastFirst( c1, geometry, side, ray ) : null;\n\n\t\t// if we got an intersection in the first node and it's closer than the second node's bounding\n\t\t// box, we don't need to consider the second node because it couldn't possibly be a better result\n\t\tif ( c1Result ) {\n\n\t\t\t// check if the point is within the second bounds\n\t\t\t// \"point\" is in the local frame of the bvh\n\t\t\tconst point = c1Result.point[ xyzAxis ];\n\t\t\tconst isOutside = leftToRight ?\n\t\t\t\tpoint <= float32Array[ c2 + splitAxis ] : // min bounding data\n\t\t\t\tpoint >= float32Array[ c2 + splitAxis + 3 ]; // max bounding data\n\n\t\t\tif ( isOutside ) {\n\n\t\t\t\treturn c1Result;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// either there was no intersection in the first node, or there could still be a closer\n\t\t// intersection in the second, so check the second node and then take the better of the two\n\t\tconst c2Intersection = intersectRay( c2, float32Array, ray, boxIntersection );\n\t\tconst c2Result = c2Intersection ? raycastFirst( c2, geometry, side, ray ) : null;\n\n\t\tif ( c1Result && c2Result ) {\n\n\t\t\treturn c1Result.distance <= c2Result.distance ? c1Result : c2Result;\n\n\t\t} else {\n\n\t\t\treturn c1Result || c2Result || null;\n\n\t\t}\n\n\t}\n\n}\n\nexport const shapecast = ( function () {\n\n\tlet _box1, _box2;\n\tconst boxStack = [];\n\tconst boxPool = new PrimitivePool( () => new Box3() );\n\n\treturn function shapecast( ...args ) {\n\n\t\t_box1 = boxPool.getPrimitive();\n\t\t_box2 = boxPool.getPrimitive();\n\t\tboxStack.push( _box1, _box2 );\n\n\t\tconst result = shapecastTraverse( ...args );\n\n\t\tboxPool.releasePrimitive( _box1 );\n\t\tboxPool.releasePrimitive( _box2 );\n\t\tboxStack.pop();\n\t\tboxStack.pop();\n\n\t\tconst length = boxStack.length;\n\t\tif ( length > 0 ) {\n\n\t\t\t_box2 = boxStack[ length - 1 ];\n\t\t\t_box1 = boxStack[ length - 2 ];\n\n\t\t}\n\n\t\treturn result;\n\n\t};\n\n\tfunction shapecastTraverse(\n\t\tnodeIndex32,\n\t\tgeometry,\n\t\tintersectsBoundsFunc,\n\t\tintersectsRangeFunc,\n\t\tnodeScoreFunc = null,\n\t\tnodeIndexByteOffset = 0, // offset for unique node identifier\n\t\tdepth = 0\n\t) {\n\n\t\t// Define these inside the function so it has access to the local variables needed\n\t\t// when converting to the buffer equivalents\n\t\tfunction getLeftOffset( nodeIndex32 ) {\n\n\t\t\tlet nodeIndex16 = nodeIndex32 * 2, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\t\t\t// traverse until we find a leaf\n\t\t\twhile ( ! IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\t\tnodeIndex32 = LEFT_NODE( nodeIndex32 );\n\t\t\t\tnodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t}\n\n\t\t\treturn OFFSET( nodeIndex32, uint32Array );\n\n\t\t}\n\n\t\tfunction getRightEndOffset( nodeIndex32 ) {\n\n\t\t\tlet nodeIndex16 = nodeIndex32 * 2, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\t\t\t// traverse until we find a leaf\n\t\t\twhile ( ! IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\t\t// adjust offset to point to the right node\n\t\t\t\tnodeIndex32 = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\t\t\tnodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t}\n\n\t\t\t// return the end offset of the triangle range\n\t\t\treturn OFFSET( nodeIndex32, uint32Array ) + COUNT( nodeIndex16, uint16Array );\n\n\t\t}\n\n\t\tlet nodeIndex16 = nodeIndex32 * 2, float32Array = _float32Array, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\t\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\t\tif ( isLeaf ) {\n\n\t\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( nodeIndex32 ), float32Array, _box1 );\n\t\t\treturn intersectsRangeFunc( offset, count, false, depth, nodeIndexByteOffset + nodeIndex32, _box1 );\n\n\t\t} else {\n\n\t\t\tconst left = LEFT_NODE( nodeIndex32 );\n\t\t\tconst right = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\t\tlet c1 = left;\n\t\t\tlet c2 = right;\n\n\t\t\tlet score1, score2;\n\t\t\tlet box1, box2;\n\t\t\tif ( nodeScoreFunc ) {\n\n\t\t\t\tbox1 = _box1;\n\t\t\t\tbox2 = _box2;\n\n\t\t\t\t// bounding data is not offset\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c1 ), float32Array, box1 );\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c2 ), float32Array, box2 );\n\n\t\t\t\tscore1 = nodeScoreFunc( box1 );\n\t\t\t\tscore2 = nodeScoreFunc( box2 );\n\n\t\t\t\tif ( score2 < score1 ) {\n\n\t\t\t\t\tc1 = right;\n\t\t\t\t\tc2 = left;\n\n\t\t\t\t\tconst temp = score1;\n\t\t\t\t\tscore1 = score2;\n\t\t\t\t\tscore2 = temp;\n\n\t\t\t\t\tbox1 = box2;\n\t\t\t\t\t// box2 is always set before use below\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// Check box 1 intersection\n\t\t\tif ( ! box1 ) {\n\n\t\t\t\tbox1 = _box1;\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c1 ), float32Array, box1 );\n\n\t\t\t}\n\n\t\t\tconst isC1Leaf = IS_LEAF( c1 * 2, uint16Array );\n\t\t\tconst c1Intersection = intersectsBoundsFunc( box1, isC1Leaf, score1, depth + 1, nodeIndexByteOffset + c1 );\n\n\t\t\tlet c1StopTraversal;\n\t\t\tif ( c1Intersection === CONTAINED ) {\n\n\t\t\t\tconst offset = getLeftOffset( c1 );\n\t\t\t\tconst end = getRightEndOffset( c1 );\n\t\t\t\tconst count = end - offset;\n\n\t\t\t\tc1StopTraversal = intersectsRangeFunc( offset, count, true, depth + 1, nodeIndexByteOffset + c1, box1 );\n\n\t\t\t} else {\n\n\t\t\t\tc1StopTraversal =\n\t\t\t\t\tc1Intersection &&\n\t\t\t\t\tshapecastTraverse(\n\t\t\t\t\t\tc1,\n\t\t\t\t\t\tgeometry,\n\t\t\t\t\t\tintersectsBoundsFunc,\n\t\t\t\t\t\tintersectsRangeFunc,\n\t\t\t\t\t\tnodeScoreFunc,\n\t\t\t\t\t\tnodeIndexByteOffset,\n\t\t\t\t\t\tdepth + 1\n\t\t\t\t\t);\n\n\t\t\t}\n\n\t\t\tif ( c1StopTraversal ) return true;\n\n\t\t\t// Check box 2 intersection\n\t\t\t// cached box2 will have been overwritten by previous traversal\n\t\t\tbox2 = _box2;\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c2 ), float32Array, box2 );\n\n\t\t\tconst isC2Leaf = IS_LEAF( c2 * 2, uint16Array );\n\t\t\tconst c2Intersection = intersectsBoundsFunc( box2, isC2Leaf, score2, depth + 1, nodeIndexByteOffset + c2 );\n\n\t\t\tlet c2StopTraversal;\n\t\t\tif ( c2Intersection === CONTAINED ) {\n\n\t\t\t\tconst offset = getLeftOffset( c2 );\n\t\t\t\tconst end = getRightEndOffset( c2 );\n\t\t\t\tconst count = end - offset;\n\n\t\t\t\tc2StopTraversal = intersectsRangeFunc( offset, count, true, depth + 1, nodeIndexByteOffset + c2, box2 );\n\n\t\t\t} else {\n\n\t\t\t\tc2StopTraversal =\n\t\t\t\t\tc2Intersection &&\n\t\t\t\t\tshapecastTraverse(\n\t\t\t\t\t\tc2,\n\t\t\t\t\t\tgeometry,\n\t\t\t\t\t\tintersectsBoundsFunc,\n\t\t\t\t\t\tintersectsRangeFunc,\n\t\t\t\t\t\tnodeScoreFunc,\n\t\t\t\t\t\tnodeIndexByteOffset,\n\t\t\t\t\t\tdepth + 1\n\t\t\t\t\t);\n\n\t\t\t}\n\n\t\t\tif ( c2StopTraversal ) return true;\n\n\t\t\treturn false;\n\n\t\t}\n\n\t}\n\n} )();\n\nexport const intersectsGeometry = ( function () {\n\n\tconst triangle = new ExtendedTriangle();\n\tconst triangle2 = new ExtendedTriangle();\n\tconst invertedMat = new Matrix4();\n\n\tconst obb = new OrientedBox();\n\tconst obb2 = new OrientedBox();\n\n\treturn function intersectsGeometry( nodeIndex32, geometry, otherGeometry, geometryToBvh, cachedObb = null ) {\n\n\t\tlet nodeIndex16 = nodeIndex32 * 2, float32Array = _float32Array, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\t\tif ( cachedObb === null ) {\n\n\t\t\tif ( ! otherGeometry.boundingBox ) {\n\n\t\t\t\totherGeometry.computeBoundingBox();\n\n\t\t\t}\n\n\t\t\tobb.set( otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh );\n\t\t\tcachedObb = obb;\n\n\t\t}\n\n\t\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\t\tif ( isLeaf ) {\n\n\t\t\tconst thisGeometry = geometry;\n\t\t\tconst thisIndex = thisGeometry.index;\n\t\t\tconst thisPos = thisGeometry.attributes.position;\n\n\t\t\tconst index = otherGeometry.index;\n\t\t\tconst pos = otherGeometry.attributes.position;\n\n\t\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\t\t\t// get the inverse of the geometry matrix so we can transform our triangles into the\n\t\t\t// geometry space we're trying to test. We assume there are fewer triangles being checked\n\t\t\t// here.\n\t\t\tinvertedMat.copy( geometryToBvh ).invert();\n\n\t\t\tif ( otherGeometry.boundsTree ) {\n\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( nodeIndex32 ), float32Array, obb2 );\n\t\t\t\tobb2.matrix.copy( invertedMat );\n\t\t\t\tobb2.needsUpdate = true;\n\n\t\t\t\tconst res = otherGeometry.boundsTree.shapecast( {\n\n\t\t\t\t\tintersectsBounds: box => obb2.intersectsBox( box ),\n\n\t\t\t\t\tintersectsTriangle: tri => {\n\n\t\t\t\t\t\ttri.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttri.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttri.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttri.needsUpdate = true;\n\n\t\t\t\t\t\tfor ( let i = offset * 3, l = ( count + offset ) * 3; i < l; i += 3 ) {\n\n\t\t\t\t\t\t\t// this triangle needs to be transformed into the current BVH coordinate frame\n\t\t\t\t\t\t\tsetTriangle( triangle2, i, thisIndex, thisPos );\n\t\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\t\t\t\t\t\t\tif ( tri.intersectsTriangle( triangle2 ) ) {\n\n\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn false;\n\n\t\t\t\t\t}\n\n\t\t\t\t} );\n\n\t\t\t\treturn res;\n\n\t\t\t} else {\n\n\t\t\t\tfor ( let i = offset * 3, l = ( count + offset * 3 ); i < l; i += 3 ) {\n\n\t\t\t\t\t// this triangle needs to be transformed into the current BVH coordinate frame\n\t\t\t\t\tsetTriangle( triangle, i, thisIndex, thisPos );\n\t\t\t\t\ttriangle.a.applyMatrix4( invertedMat );\n\t\t\t\t\ttriangle.b.applyMatrix4( invertedMat );\n\t\t\t\t\ttriangle.c.applyMatrix4( invertedMat );\n\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\tfor ( let i2 = 0, l2 = index.count; i2 < l2; i2 += 3 ) {\n\n\t\t\t\t\t\tsetTriangle( triangle2, i2, index, pos );\n\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\tif ( triangle.intersectsTriangle( triangle2 ) ) {\n\n\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconst left = nodeIndex32 + 8;\n\t\t\tconst right = uint32Array[ nodeIndex32 + 6 ];\n\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( left ), float32Array, boundingBox );\n\t\t\tconst leftIntersection =\n\t\t\t\tcachedObb.intersectsBox( boundingBox ) &&\n\t\t\t\tintersectsGeometry( left, geometry, otherGeometry, geometryToBvh, cachedObb );\n\n\t\t\tif ( leftIntersection ) return true;\n\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( right ), float32Array, boundingBox );\n\t\t\tconst rightIntersection =\n\t\t\t\tcachedObb.intersectsBox( boundingBox ) &&\n\t\t\t\tintersectsGeometry( right, geometry, otherGeometry, geometryToBvh, cachedObb );\n\n\t\t\tif ( rightIntersection ) return true;\n\n\t\t\treturn false;\n\n\t\t}\n\n\t};\n\n} )();\n\nfunction intersectRay( nodeIndex32, array, ray, target ) {\n\n\tarrayToBox( nodeIndex32, array, boundingBox );\n\treturn ray.intersectBox( boundingBox, target );\n\n}\n\nconst bufferStack = [];\nlet _prevBuffer;\nlet _float32Array;\nlet _uint16Array;\nlet _uint32Array;\nexport function setBuffer( buffer ) {\n\n\tif ( _prevBuffer ) {\n\n\t\tbufferStack.push( _prevBuffer );\n\n\t}\n\n\t_prevBuffer = buffer;\n\t_float32Array = new Float32Array( buffer );\n\t_uint16Array = new Uint16Array( buffer );\n\t_uint32Array = new Uint32Array( buffer );\n\n}\n\nexport function clearBuffer() {\n\n\t_prevBuffer = null;\n\t_float32Array = null;\n\t_uint16Array = null;\n\t_uint32Array = null;\n\n\tif ( bufferStack.length ) {\n\n\t\tsetBuffer( bufferStack.pop() );\n\n\t}\n\n}\n", "import { Vector3, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Matrix4 } from 'three';\nimport { CENTER, BYTES_PER_NODE, IS_LEAFNODE_FLAG } from './Constants.js';\nimport { buildPackedTree } from './buildFunctions.js';\nimport {\n\traycast,\n\traycastFirst,\n\tshapecast,\n\tintersectsGeometry,\n\tsetBuffer,\n\tclearBuffer,\n} from './castFunctions.js';\nimport { OrientedBox } from '../math/OrientedBox.js';\nimport { ExtendedTriangle } from '../math/ExtendedTriangle.js';\nimport { PrimitivePool } from '../utils/PrimitivePool.js';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nimport { iterateOverTriangles, setTriangle } from '../utils/TriangleUtilities.js';\n\nconst SKIP_GENERATION = Symbol( 'skip tree generation' );\n\nconst aabb = /* @__PURE__ */ new Box3();\nconst aabb2 = /* @__PURE__ */ new Box3();\nconst tempMatrix = /* @__PURE__ */ new Matrix4();\nconst obb = /* @__PURE__ */ new OrientedBox();\nconst obb2 = /* @__PURE__ */ new OrientedBox();\nconst temp = /* @__PURE__ */ new Vector3();\nconst temp1 = /* @__PURE__ */ new Vector3();\nconst temp2 = /* @__PURE__ */ new Vector3();\nconst temp3 = /* @__PURE__ */ new Vector3();\nconst temp4 = /* @__PURE__ */ new Vector3();\nconst tempBox = /* @__PURE__ */ new Box3();\nconst trianglePool = /* @__PURE__ */ new PrimitivePool( () => new ExtendedTriangle() );\n\nexport class MeshBVH {\n\n\tstatic serialize( bvh, options = {} ) {\n\n\t\tif ( options.isBufferGeometry ) {\n\n\t\t\tconsole.warn( 'MeshBVH.serialize: The arguments for the function have changed. See documentation for new signature.' );\n\n\t\t\treturn MeshBVH.serialize(\n\t\t\t\targuments[ 0 ],\n\t\t\t\t{\n\t\t\t\t\tcloneBuffers: arguments[ 2 ] === undefined ? true : arguments[ 2 ],\n\t\t\t\t}\n\t\t\t);\n\n\t\t}\n\n\t\toptions = {\n\t\t\tcloneBuffers: true,\n\t\t\t...options,\n\t\t};\n\n\t\tconst geometry = bvh.geometry;\n\t\tconst rootData = bvh._roots;\n\t\tconst indexAttribute = geometry.getIndex();\n\t\tlet result;\n\t\tif ( options.cloneBuffers ) {\n\n\t\t\tresult = {\n\t\t\t\troots: rootData.map( root => root.slice() ),\n\t\t\t\tindex: indexAttribute.array.slice(),\n\t\t\t};\n\n\t\t} else {\n\n\t\t\tresult = {\n\t\t\t\troots: rootData,\n\t\t\t\tindex: indexAttribute.array,\n\t\t\t};\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n\tstatic deserialize( data, geometry, options = {} ) {\n\n\t\tif ( typeof options === 'boolean' ) {\n\n\t\t\tconsole.warn( 'MeshBVH.deserialize: The arguments for the function have changed. See documentation for new signature.' );\n\n\t\t\treturn MeshBVH.deserialize(\n\t\t\t\targuments[ 0 ],\n\t\t\t\targuments[ 1 ],\n\t\t\t\t{\n\t\t\t\t\tsetIndex: arguments[ 2 ] === undefined ? true : arguments[ 2 ],\n\t\t\t\t}\n\t\t\t);\n\n\t\t}\n\n\t\toptions = {\n\t\t\tsetIndex: true,\n\t\t\t...options,\n\t\t};\n\n\t\tconst { index, roots } = data;\n\t\tconst bvh = new MeshBVH( geometry, { ...options, [ SKIP_GENERATION ]: true } );\n\t\tbvh._roots = roots;\n\n\t\tif ( options.setIndex ) {\n\n\t\t\tconst indexAttribute = geometry.getIndex();\n\t\t\tif ( indexAttribute === null ) {\n\n\t\t\t\tconst newIndex = new BufferAttribute( data.index, 1, false );\n\t\t\t\tgeometry.setIndex( newIndex );\n\n\t\t\t} else if ( indexAttribute.array !== index ) {\n\n\t\t\t\tindexAttribute.array.set( index );\n\t\t\t\tindexAttribute.needsUpdate = true;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn bvh;\n\n\t}\n\n\tconstructor( geometry, options = {} ) {\n\n\t\tif ( ! geometry.isBufferGeometry ) {\n\n\t\t\tthrow new Error( 'MeshBVH: Only BufferGeometries are supported.' );\n\n\t\t} else if ( geometry.index && geometry.index.isInterleavedBufferAttribute ) {\n\n\t\t\tthrow new Error( 'MeshBVH: InterleavedBufferAttribute is not supported for the index attribute.' );\n\n\t\t}\n\n\t\t// default options\n\t\toptions = Object.assign( {\n\n\t\t\tstrategy: CENTER,\n\t\t\tmaxDepth: 40,\n\t\t\tmaxLeafTris: 10,\n\t\t\tverbose: true,\n\t\t\tuseSharedArrayBuffer: false,\n\t\t\tsetBoundingBox: true,\n\t\t\tonProgress: null,\n\n\t\t\t// undocumented options\n\n\t\t\t// Whether to skip generating the tree. Used for deserialization.\n\t\t\t[ SKIP_GENERATION ]: false,\n\n\t\t}, options );\n\n\t\tif ( options.useSharedArrayBuffer && typeof SharedArrayBuffer === 'undefined' ) {\n\n\t\t\tthrow new Error( 'MeshBVH: SharedArrayBuffer is not available.' );\n\n\t\t}\n\n\t\tthis._roots = null;\n\t\tif ( ! options[ SKIP_GENERATION ] ) {\n\n\t\t\tthis._roots = buildPackedTree( geometry, options );\n\n\t\t\tif ( ! geometry.boundingBox && options.setBoundingBox ) {\n\n\t\t\t\tgeometry.boundingBox = this.getBoundingBox( new Box3() );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// retain references to the geometry so we can use them it without having to\n\t\t// take a geometry reference in every function.\n\t\tthis.geometry = geometry;\n\n\t}\n\n\trefit( nodeIndices = null ) {\n\n\t\tif ( nodeIndices && Array.isArray( nodeIndices ) ) {\n\n\t\t\tnodeIndices = new Set( nodeIndices );\n\n\t\t}\n\n\t\tconst geometry = this.geometry;\n\t\tconst indexArr = geometry.index.array;\n\t\tconst posAttr = geometry.attributes.position;\n\n\t\tlet buffer, uint32Array, uint16Array, float32Array;\n\t\tlet byteOffset = 0;\n\t\tconst roots = this._roots;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tbuffer = roots[ i ];\n\t\t\tuint32Array = new Uint32Array( buffer );\n\t\t\tuint16Array = new Uint16Array( buffer );\n\t\t\tfloat32Array = new Float32Array( buffer );\n\n\t\t\t_traverse( 0, byteOffset );\n\t\t\tbyteOffset += buffer.byteLength;\n\n\t\t}\n\n\t\tfunction _traverse( node32Index, byteOffset, force = false ) {\n\n\t\t\tconst node16Index = node32Index * 2;\n\t\t\tconst isLeaf = uint16Array[ node16Index + 15 ] === IS_LEAFNODE_FLAG;\n\t\t\tif ( isLeaf ) {\n\n\t\t\t\tconst offset = uint32Array[ node32Index + 6 ];\n\t\t\t\tconst count = uint16Array[ node16Index + 14 ];\n\n\t\t\t\tlet minx = Infinity;\n\t\t\t\tlet miny = Infinity;\n\t\t\t\tlet minz = Infinity;\n\t\t\t\tlet maxx = - Infinity;\n\t\t\t\tlet maxy = - Infinity;\n\t\t\t\tlet maxz = - Infinity;\n\n\t\t\t\tfor ( let i = 3 * offset, l = 3 * ( offset + count ); i < l; i ++ ) {\n\n\t\t\t\t\tconst index = indexArr[ i ];\n\t\t\t\t\tconst x = posAttr.getX( index );\n\t\t\t\t\tconst y = posAttr.getY( index );\n\t\t\t\t\tconst z = posAttr.getZ( index );\n\n\t\t\t\t\tif ( x < minx ) minx = x;\n\t\t\t\t\tif ( x > maxx ) maxx = x;\n\n\t\t\t\t\tif ( y < miny ) miny = y;\n\t\t\t\t\tif ( y > maxy ) maxy = y;\n\n\t\t\t\t\tif ( z < minz ) minz = z;\n\t\t\t\t\tif ( z > maxz ) maxz = z;\n\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\tfloat32Array[ node32Index + 0 ] !== minx ||\n\t\t\t\t\tfloat32Array[ node32Index + 1 ] !== miny ||\n\t\t\t\t\tfloat32Array[ node32Index + 2 ] !== minz ||\n\n\t\t\t\t\tfloat32Array[ node32Index + 3 ] !== maxx ||\n\t\t\t\t\tfloat32Array[ node32Index + 4 ] !== maxy ||\n\t\t\t\t\tfloat32Array[ node32Index + 5 ] !== maxz\n\t\t\t\t) {\n\n\t\t\t\t\tfloat32Array[ node32Index + 0 ] = minx;\n\t\t\t\t\tfloat32Array[ node32Index + 1 ] = miny;\n\t\t\t\t\tfloat32Array[ node32Index + 2 ] = minz;\n\n\t\t\t\t\tfloat32Array[ node32Index + 3 ] = maxx;\n\t\t\t\t\tfloat32Array[ node32Index + 4 ] = maxy;\n\t\t\t\t\tfloat32Array[ node32Index + 5 ] = maxz;\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t} else {\n\n\t\t\t\t\treturn false;\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tconst left = node32Index + 8;\n\t\t\t\tconst right = uint32Array[ node32Index + 6 ];\n\n\t\t\t\t// the identifying node indices provided by the shapecast function include offsets of all\n\t\t\t\t// root buffers to guarantee they're unique between roots so offset left and right indices here.\n\t\t\t\tconst offsetLeft = left + byteOffset;\n\t\t\t\tconst offsetRight = right + byteOffset;\n\t\t\t\tlet forceChildren = force;\n\t\t\t\tlet includesLeft = false;\n\t\t\t\tlet includesRight = false;\n\n\t\t\t\tif ( nodeIndices ) {\n\n\t\t\t\t\t// if we see that neither the left or right child are included in the set that need to be updated\n\t\t\t\t\t// then we assume that all children need to be updated.\n\t\t\t\t\tif ( ! forceChildren ) {\n\n\t\t\t\t\t\tincludesLeft = nodeIndices.has( offsetLeft );\n\t\t\t\t\t\tincludesRight = nodeIndices.has( offsetRight );\n\t\t\t\t\t\tforceChildren = ! includesLeft && ! includesRight;\n\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\n\t\t\t\t\tincludesLeft = true;\n\t\t\t\t\tincludesRight = true;\n\n\t\t\t\t}\n\n\t\t\t\tconst traverseLeft = forceChildren || includesLeft;\n\t\t\t\tconst traverseRight = forceChildren || includesRight;\n\n\t\t\t\tlet leftChange = false;\n\t\t\t\tif ( traverseLeft ) {\n\n\t\t\t\t\tleftChange = _traverse( left, byteOffset, forceChildren );\n\n\t\t\t\t}\n\n\t\t\t\tlet rightChange = false;\n\t\t\t\tif ( traverseRight ) {\n\n\t\t\t\t\trightChange = _traverse( right, byteOffset, forceChildren );\n\n\t\t\t\t}\n\n\t\t\t\tconst didChange = leftChange || rightChange;\n\t\t\t\tif ( didChange ) {\n\n\t\t\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\t\t\tconst lefti = left + i;\n\t\t\t\t\t\tconst righti = right + i;\n\t\t\t\t\t\tconst minLeftValue = float32Array[ lefti ];\n\t\t\t\t\t\tconst maxLeftValue = float32Array[ lefti + 3 ];\n\t\t\t\t\t\tconst minRightValue = float32Array[ righti ];\n\t\t\t\t\t\tconst maxRightValue = float32Array[ righti + 3 ];\n\n\t\t\t\t\t\tfloat32Array[ node32Index + i ] = minLeftValue < minRightValue ? minLeftValue : minRightValue;\n\t\t\t\t\t\tfloat32Array[ node32Index + i + 3 ] = maxLeftValue > maxRightValue ? maxLeftValue : maxRightValue;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\treturn didChange;\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\ttraverse( callback, rootIndex = 0 ) {\n\n\t\tconst buffer = this._roots[ rootIndex ];\n\t\tconst uint32Array = new Uint32Array( buffer );\n\t\tconst uint16Array = new Uint16Array( buffer );\n\t\t_traverse( 0 );\n\n\t\tfunction _traverse( node32Index, depth = 0 ) {\n\n\t\t\tconst node16Index = node32Index * 2;\n\t\t\tconst isLeaf = uint16Array[ node16Index + 15 ] === IS_LEAFNODE_FLAG;\n\t\t\tif ( isLeaf ) {\n\n\t\t\t\tconst offset = uint32Array[ node32Index + 6 ];\n\t\t\t\tconst count = uint16Array[ node16Index + 14 ];\n\t\t\t\tcallback( depth, isLeaf, new Float32Array( buffer, node32Index * 4, 6 ), offset, count );\n\n\t\t\t} else {\n\n\t\t\t\t// TODO: use node functions here\n\t\t\t\tconst left = node32Index + BYTES_PER_NODE / 4;\n\t\t\t\tconst right = uint32Array[ node32Index + 6 ];\n\t\t\t\tconst splitAxis = uint32Array[ node32Index + 7 ];\n\t\t\t\tconst stopTraversal = callback( depth, isLeaf, new Float32Array( buffer, node32Index * 4, 6 ), splitAxis );\n\n\t\t\t\tif ( ! stopTraversal ) {\n\n\t\t\t\t\t_traverse( left, depth + 1 );\n\t\t\t\t\t_traverse( right, depth + 1 );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/* Core Cast Functions */\n\traycast( ray, materialOrSide = FrontSide ) {\n\n\t\tconst roots = this._roots;\n\t\tconst geometry = this.geometry;\n\t\tconst intersects = [];\n\t\tconst isMaterial = materialOrSide.isMaterial;\n\t\tconst isArrayMaterial = Array.isArray( materialOrSide );\n\n\t\tconst groups = geometry.groups;\n\t\tconst side = isMaterial ? materialOrSide.side : materialOrSide;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tconst materialSide = isArrayMaterial ? materialOrSide[ groups[ i ].materialIndex ].side : side;\n\t\t\tconst startCount = intersects.length;\n\n\t\t\tsetBuffer( roots[ i ] );\n\t\t\traycast( 0, geometry, materialSide, ray, intersects );\n\t\t\tclearBuffer();\n\n\t\t\tif ( isArrayMaterial ) {\n\n\t\t\t\tconst materialIndex = groups[ i ].materialIndex;\n\t\t\t\tfor ( let j = startCount, jl = intersects.length; j < jl; j ++ ) {\n\n\t\t\t\t\tintersects[ j ].face.materialIndex = materialIndex;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn intersects;\n\n\t}\n\n\traycastFirst( ray, materialOrSide = FrontSide ) {\n\n\t\tconst roots = this._roots;\n\t\tconst geometry = this.geometry;\n\t\tconst isMaterial = materialOrSide.isMaterial;\n\t\tconst isArrayMaterial = Array.isArray( materialOrSide );\n\n\t\tlet closestResult = null;\n\n\t\tconst groups = geometry.groups;\n\t\tconst side = isMaterial ? materialOrSide.side : materialOrSide;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tconst materialSide = isArrayMaterial ? materialOrSide[ groups[ i ].materialIndex ].side : side;\n\n\t\t\tsetBuffer( roots[ i ] );\n\t\t\tconst result = raycastFirst( 0, geometry, materialSide, ray );\n\t\t\tclearBuffer();\n\n\t\t\tif ( result != null && ( closestResult == null || result.distance < closestResult.distance ) ) {\n\n\t\t\t\tclosestResult = result;\n\t\t\t\tif ( isArrayMaterial ) {\n\n\t\t\t\t\tresult.face.materialIndex = groups[ i ].materialIndex;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn closestResult;\n\n\t}\n\n\tintersectsGeometry( otherGeometry, geomToMesh ) {\n\n\t\tconst geometry = this.geometry;\n\t\tlet result = false;\n\t\tfor ( const root of this._roots ) {\n\n\t\t\tsetBuffer( root );\n\t\t\tresult = intersectsGeometry( 0, geometry, otherGeometry, geomToMesh );\n\t\t\tclearBuffer();\n\n\t\t\tif ( result ) {\n\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n\tshapecast( callbacks, _intersectsTriangleFunc, _orderNodesFunc ) {\n\n\t\tconst geometry = this.geometry;\n\t\tif ( callbacks instanceof Function ) {\n\n\t\t\tif ( _intersectsTriangleFunc ) {\n\n\t\t\t\t// Support the previous function signature that provided three sequential index buffer\n\t\t\t\t// indices here.\n\t\t\t\tconst originalTriangleFunc = _intersectsTriangleFunc;\n\t\t\t\t_intersectsTriangleFunc = ( tri, index, contained, depth ) => {\n\n\t\t\t\t\tconst i3 = index * 3;\n\t\t\t\t\treturn originalTriangleFunc( tri, i3, i3 + 1, i3 + 2, contained, depth );\n\n\t\t\t\t};\n\n\n\t\t\t}\n\n\t\t\tcallbacks = {\n\n\t\t\t\tboundsTraverseOrder: _orderNodesFunc,\n\t\t\t\tintersectsBounds: callbacks,\n\t\t\t\tintersectsTriangle: _intersectsTriangleFunc,\n\t\t\t\tintersectsRange: null,\n\n\t\t\t};\n\n\t\t\tconsole.warn( 'MeshBVH: Shapecast function signature has changed and now takes an object of callbacks as a second argument. See docs for new signature.' );\n\n\t\t}\n\n\t\tconst triangle = trianglePool.getPrimitive();\n\t\tlet {\n\t\t\tboundsTraverseOrder,\n\t\t\tintersectsBounds,\n\t\t\tintersectsRange,\n\t\t\tintersectsTriangle,\n\t\t} = callbacks;\n\n\t\tif ( intersectsRange && intersectsTriangle ) {\n\n\t\t\tconst originalIntersectsRange = intersectsRange;\n\t\t\tintersectsRange = ( offset, count, contained, depth, nodeIndex ) => {\n\n\t\t\t\tif ( ! originalIntersectsRange( offset, count, contained, depth, nodeIndex ) ) {\n\n\t\t\t\t\treturn iterateOverTriangles( offset, count, geometry, intersectsTriangle, contained, depth, triangle );\n\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\n\t\t\t};\n\n\t\t} else if ( ! intersectsRange ) {\n\n\t\t\tif ( intersectsTriangle ) {\n\n\t\t\t\tintersectsRange = ( offset, count, contained, depth ) => {\n\n\t\t\t\t\treturn iterateOverTriangles( offset, count, geometry, intersectsTriangle, contained, depth, triangle );\n\n\t\t\t\t};\n\n\t\t\t} else {\n\n\t\t\t\tintersectsRange = ( offset, count, contained ) => {\n\n\t\t\t\t\treturn contained;\n\n\t\t\t\t};\n\n\t\t\t}\n\n\t\t}\n\n\t\tlet result = false;\n\t\tlet byteOffset = 0;\n\t\tfor ( const root of this._roots ) {\n\n\t\t\tsetBuffer( root );\n\t\t\tresult = shapecast( 0, geometry, intersectsBounds, intersectsRange, boundsTraverseOrder, byteOffset );\n\t\t\tclearBuffer();\n\n\t\t\tif ( result ) {\n\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tbyteOffset += root.byteLength;\n\n\t\t}\n\n\t\ttrianglePool.releasePrimitive( triangle );\n\n\t\treturn result;\n\n\t}\n\n\tbvhcast( otherBvh, matrixToLocal, callbacks ) {\n\n\t\t// BVHCast function for intersecting two BVHs against each other. Ultimately just uses two recursive shapecast calls rather\n\t\t// than an approach that walks down the tree (see bvhcast.js file for more info).\n\n\t\tlet {\n\t\t\tintersectsRanges,\n\t\t\tintersectsTriangles,\n\t\t} = callbacks;\n\n\t\tconst indexAttr = this.geometry.index;\n\t\tconst positionAttr = this.geometry.attributes.position;\n\n\t\tconst otherIndexAttr = otherBvh.geometry.index;\n\t\tconst otherPositionAttr = otherBvh.geometry.attributes.position;\n\n\t\ttempMatrix.copy( matrixToLocal ).invert();\n\n\t\tconst triangle = trianglePool.getPrimitive();\n\t\tconst triangle2 = trianglePool.getPrimitive();\n\n\t\tif ( intersectsTriangles ) {\n\n\t\t\tfunction iterateOverDoubleTriangles( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) {\n\n\t\t\t\tfor ( let i2 = offset2, l2 = offset2 + count2; i2 < l2; i2 ++ ) {\n\n\t\t\t\t\tsetTriangle( triangle2, i2 * 3, otherIndexAttr, otherPositionAttr );\n\t\t\t\t\ttriangle2.a.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.b.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.c.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\tfor ( let i1 = offset1, l1 = offset1 + count1; i1 < l1; i1 ++ ) {\n\n\t\t\t\t\t\tsetTriangle( triangle, i1 * 3, indexAttr, positionAttr );\n\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\tif ( intersectsTriangles( triangle, triangle2, i1, i2, depth1, index1, depth2, index2 ) ) {\n\n\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\tif ( intersectsRanges ) {\n\n\t\t\t\tconst originalIntersectsRanges = intersectsRanges;\n\t\t\t\tintersectsRanges = function ( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) {\n\n\t\t\t\t\tif ( ! originalIntersectsRanges( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) ) {\n\n\t\t\t\t\t\treturn iterateOverDoubleTriangles( offset1, count1, offset2, count2, depth1, index1, depth2, index2 );\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t};\n\n\t\t\t} else {\n\n\t\t\t\tintersectsRanges = iterateOverDoubleTriangles;\n\n\t\t\t}\n\n\t\t}\n\n\t\totherBvh.getBoundingBox( aabb2 );\n\t\taabb2.applyMatrix4( matrixToLocal );\n\t\tconst result = this.shapecast( {\n\n\t\t\tintersectsBounds: box => aabb2.intersectsBox( box ),\n\n\t\t\tintersectsRange: ( offset1, count1, contained, depth1, nodeIndex1, box ) => {\n\n\t\t\t\taabb.copy( box );\n\t\t\t\taabb.applyMatrix4( tempMatrix );\n\t\t\t\treturn otherBvh.shapecast( {\n\n\t\t\t\t\tintersectsBounds: box => aabb.intersectsBox( box ),\n\n\t\t\t\t\tintersectsRange: ( offset2, count2, contained, depth2, nodeIndex2 ) => {\n\n\t\t\t\t\t\treturn intersectsRanges( offset1, count1, offset2, count2, depth1, nodeIndex1, depth2, nodeIndex2 );\n\n\t\t\t\t\t},\n\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t} );\n\n\t\ttrianglePool.releasePrimitive( triangle );\n\t\ttrianglePool.releasePrimitive( triangle2 );\n\t\treturn result;\n\n\t}\n\n\t/* Derived Cast Functions */\n\tintersectsBox( box, boxToMesh ) {\n\n\t\tobb.set( box.min, box.max, boxToMesh );\n\t\tobb.needsUpdate = true;\n\n\t\treturn this.shapecast(\n\t\t\t{\n\t\t\t\tintersectsBounds: box => obb.intersectsBox( box ),\n\t\t\t\tintersectsTriangle: tri => obb.intersectsTriangle( tri )\n\t\t\t}\n\t\t);\n\n\t}\n\n\tintersectsSphere( sphere ) {\n\n\t\treturn this.shapecast(\n\t\t\t{\n\t\t\t\tintersectsBounds: box => sphere.intersectsBox( box ),\n\t\t\t\tintersectsTriangle: tri => tri.intersectsSphere( sphere )\n\t\t\t}\n\t\t);\n\n\t}\n\n\tclosestPointToGeometry( otherGeometry, geometryToBvh, target1 = { }, target2 = { }, minThreshold = 0, maxThreshold = Infinity ) {\n\n\t\tif ( ! otherGeometry.boundingBox ) {\n\n\t\t\totherGeometry.computeBoundingBox();\n\n\t\t}\n\n\t\tobb.set( otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh );\n\t\tobb.needsUpdate = true;\n\n\t\tconst geometry = this.geometry;\n\t\tconst pos = geometry.attributes.position;\n\t\tconst index = geometry.index;\n\t\tconst otherPos = otherGeometry.attributes.position;\n\t\tconst otherIndex = otherGeometry.index;\n\t\tconst triangle = trianglePool.getPrimitive();\n\t\tconst triangle2 = trianglePool.getPrimitive();\n\n\t\tlet tempTarget1 = temp1;\n\t\tlet tempTargetDest1 = temp2;\n\t\tlet tempTarget2 = null;\n\t\tlet tempTargetDest2 = null;\n\n\t\tif ( target2 ) {\n\n\t\t\ttempTarget2 = temp3;\n\t\t\ttempTargetDest2 = temp4;\n\n\t\t}\n\n\t\tlet closestDistance = Infinity;\n\t\tlet closestDistanceTriIndex = null;\n\t\tlet closestDistanceOtherTriIndex = null;\n\t\ttempMatrix.copy( geometryToBvh ).invert();\n\t\tobb2.matrix.copy( tempMatrix );\n\t\tthis.shapecast(\n\t\t\t{\n\n\t\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\t\treturn obb.distanceToBox( box );\n\n\t\t\t\t},\n\n\t\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\t\tif ( score < closestDistance && score < maxThreshold ) {\n\n\t\t\t\t\t\t// if we know the triangles of this bounds will be intersected next then\n\t\t\t\t\t\t// save the bounds to use during triangle checks.\n\t\t\t\t\t\tif ( isLeaf ) {\n\n\t\t\t\t\t\t\tobb2.min.copy( box.min );\n\t\t\t\t\t\t\tobb2.max.copy( box.max );\n\t\t\t\t\t\t\tobb2.needsUpdate = true;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn false;\n\n\t\t\t\t},\n\n\t\t\t\tintersectsRange: ( offset, count ) => {\n\n\t\t\t\t\tif ( otherGeometry.boundsTree ) {\n\n\t\t\t\t\t\t// if the other geometry has a bvh then use the accelerated path where we use shapecast to find\n\t\t\t\t\t\t// the closest bounds in the other geometry to check.\n\t\t\t\t\t\treturn otherGeometry.boundsTree.shapecast( {\n\t\t\t\t\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\t\t\t\t\treturn obb2.distanceToBox( box );\n\n\t\t\t\t\t\t\t},\n\n\t\t\t\t\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\t\t\t\t\treturn score < closestDistance && score < maxThreshold;\n\n\t\t\t\t\t\t\t},\n\n\t\t\t\t\t\t\tintersectsRange: ( otherOffset, otherCount ) => {\n\n\t\t\t\t\t\t\t\tfor ( let i2 = otherOffset * 3, l2 = ( otherOffset + otherCount ) * 3; i2 < l2; i2 += 3 ) {\n\n\t\t\t\t\t\t\t\t\tsetTriangle( triangle2, i2, otherIndex, otherPos );\n\t\t\t\t\t\t\t\t\ttriangle2.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\t\ttriangle2.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\t\ttriangle2.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\t\t\t\tfor ( let i = offset * 3, l = ( offset + count ) * 3; i < l; i += 3 ) {\n\n\t\t\t\t\t\t\t\t\t\tsetTriangle( triangle, i, index, pos );\n\t\t\t\t\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\t\t\t\t\tconst dist = triangle.distanceToTriangle( triangle2, tempTarget1, tempTarget2 );\n\t\t\t\t\t\t\t\t\t\tif ( dist < closestDistance ) {\n\n\t\t\t\t\t\t\t\t\t\t\ttempTargetDest1.copy( tempTarget1 );\n\n\t\t\t\t\t\t\t\t\t\t\tif ( tempTargetDest2 ) {\n\n\t\t\t\t\t\t\t\t\t\t\t\ttempTargetDest2.copy( tempTarget2 );\n\n\t\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t\tclosestDistance = dist;\n\t\t\t\t\t\t\t\t\t\t\tclosestDistanceTriIndex = i / 3;\n\t\t\t\t\t\t\t\t\t\t\tclosestDistanceOtherTriIndex = i2 / 3;\n\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t// stop traversal if we find a point that's under the given threshold\n\t\t\t\t\t\t\t\t\t\tif ( dist < minThreshold ) {\n\n\t\t\t\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t} );\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\t// If no bounds tree then we'll just check every triangle.\n\t\t\t\t\t\tconst triCount = otherIndex ? otherIndex.count : otherPos.count;\n\t\t\t\t\t\tfor ( let i2 = 0, l2 = triCount; i2 < l2; i2 += 3 ) {\n\n\t\t\t\t\t\t\tsetTriangle( triangle2, i2, otherIndex, otherPos );\n\t\t\t\t\t\t\ttriangle2.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\ttriangle2.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\ttriangle2.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\t\tfor ( let i = offset * 3, l = ( offset + count ) * 3; i < l; i += 3 ) {\n\n\t\t\t\t\t\t\t\tsetTriangle( triangle, i, index, pos );\n\t\t\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\t\t\tconst dist = triangle.distanceToTriangle( triangle2, tempTarget1, tempTarget2 );\n\t\t\t\t\t\t\t\tif ( dist < closestDistance ) {\n\n\t\t\t\t\t\t\t\t\ttempTargetDest1.copy( tempTarget1 );\n\n\t\t\t\t\t\t\t\t\tif ( tempTargetDest2 ) {\n\n\t\t\t\t\t\t\t\t\t\ttempTargetDest2.copy( tempTarget2 );\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\tclosestDistance = dist;\n\t\t\t\t\t\t\t\t\tclosestDistanceTriIndex = i / 3;\n\t\t\t\t\t\t\t\t\tclosestDistanceOtherTriIndex = i2 / 3;\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t// stop traversal if we find a point that's under the given threshold\n\t\t\t\t\t\t\t\tif ( dist < minThreshold ) {\n\n\t\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t},\n\n\t\t\t}\n\n\t\t);\n\n\t\ttrianglePool.releasePrimitive( triangle );\n\t\ttrianglePool.releasePrimitive( triangle2 );\n\n\t\tif ( closestDistance === Infinity ) return null;\n\n\t\tif ( ! target1.point ) target1.point = tempTargetDest1.clone();\n\t\telse target1.point.copy( tempTargetDest1 );\n\t\ttarget1.distance = closestDistance,\n\t\ttarget1.faceIndex = closestDistanceTriIndex;\n\n\t\tif ( target2 ) {\n\n\t\t\tif ( ! target2.point ) target2.point = tempTargetDest2.clone();\n\t\t\telse target2.point.copy( tempTargetDest2 );\n\t\t\ttarget2.point.applyMatrix4( tempMatrix );\n\t\t\ttempTargetDest1.applyMatrix4( tempMatrix );\n\t\t\ttarget2.distance = tempTargetDest1.sub( target2.point ).length();\n\t\t\ttarget2.faceIndex = closestDistanceOtherTriIndex;\n\n\t\t}\n\n\t\treturn target1;\n\n\t}\n\n\tclosestPointToPoint( point, target = { }, minThreshold = 0, maxThreshold = Infinity ) {\n\n\t\t// early out if under minThreshold\n\t\t// skip checking if over maxThreshold\n\t\t// set minThreshold = maxThreshold to quickly check if a point is within a threshold\n\t\t// returns Infinity if no value found\n\t\tconst minThresholdSq = minThreshold * minThreshold;\n\t\tconst maxThresholdSq = maxThreshold * maxThreshold;\n\t\tlet closestDistanceSq = Infinity;\n\t\tlet closestDistanceTriIndex = null;\n\t\tthis.shapecast(\n\n\t\t\t{\n\n\t\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\t\ttemp.copy( point ).clamp( box.min, box.max );\n\t\t\t\t\treturn temp.distanceToSquared( point );\n\n\t\t\t\t},\n\n\t\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\t\treturn score < closestDistanceSq && score < maxThresholdSq;\n\n\t\t\t\t},\n\n\t\t\t\tintersectsTriangle: ( tri, triIndex ) => {\n\n\t\t\t\t\ttri.closestPointToPoint( point, temp );\n\t\t\t\t\tconst distSq = point.distanceToSquared( temp );\n\t\t\t\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\t\t\t\ttemp1.copy( temp );\n\t\t\t\t\t\tclosestDistanceSq = distSq;\n\t\t\t\t\t\tclosestDistanceTriIndex = triIndex;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( distSq < minThresholdSq ) {\n\n\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\treturn false;\n\n\t\t\t\t\t}\n\n\t\t\t\t},\n\n\t\t\t}\n\n\t\t);\n\n\t\tif ( closestDistanceSq === Infinity ) return null;\n\n\t\tconst closestDistance = Math.sqrt( closestDistanceSq );\n\n\t\tif ( ! target.point ) target.point = temp1.clone();\n\t\telse target.point.copy( temp1 );\n\t\ttarget.distance = closestDistance,\n\t\ttarget.faceIndex = closestDistanceTriIndex;\n\n\t\treturn target;\n\n\t}\n\n\tgetBoundingBox( target ) {\n\n\t\ttarget.makeEmpty();\n\n\t\tconst roots = this._roots;\n\t\troots.forEach( buffer => {\n\n\t\t\tarrayToBox( 0, new Float32Array( buffer ), tempBox );\n\t\t\ttarget.union( tempBox );\n\n\t\t} );\n\n\t\treturn target;\n\n\t}\n\n}\n", "import { LineBasicMaterial, BufferAttribute, Box3, Group, MeshBasicMaterial, Object3D, BufferGeometry } from 'three';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\n\nconst boundingBox = /* @__PURE__ */ new Box3();\nclass MeshBVHRootVisualizer extends Object3D {\n\n\tget isMesh() {\n\n\t\treturn ! this.displayEdges;\n\n\t}\n\n\tget isLineSegments() {\n\n\t\treturn this.displayEdges;\n\n\t}\n\n\tget isLine() {\n\n\t\treturn this.displayEdges;\n\n\t}\n\n\tconstructor( mesh, material, depth = 10, group = 0 ) {\n\n\t\tsuper();\n\n\t\tthis.material = material;\n\t\tthis.geometry = new BufferGeometry();\n\t\tthis.name = 'MeshBVHRootVisualizer';\n\t\tthis.depth = depth;\n\t\tthis.displayParents = false;\n\t\tthis.mesh = mesh;\n\t\tthis.displayEdges = true;\n\t\tthis._group = group;\n\n\t}\n\n\traycast() {}\n\n\tupdate() {\n\n\t\tconst geometry = this.geometry;\n\t\tconst boundsTree = this.mesh.geometry.boundsTree;\n\t\tconst group = this._group;\n\t\tgeometry.dispose();\n\t\tthis.visible = false;\n\t\tif ( boundsTree ) {\n\n\t\t\t// count the number of bounds required\n\t\t\tconst targetDepth = this.depth - 1;\n\t\t\tconst displayParents = this.displayParents;\n\t\t\tlet boundsCount = 0;\n\t\t\tboundsTree.traverse( ( depth, isLeaf ) => {\n\n\t\t\t\tif ( depth === targetDepth || isLeaf ) {\n\n\t\t\t\t\tboundsCount ++;\n\t\t\t\t\treturn true;\n\n\t\t\t\t} else if ( displayParents ) {\n\n\t\t\t\t\tboundsCount ++;\n\n\t\t\t\t}\n\n\t\t\t}, group );\n\n\t\t\t// fill in the position buffer with the bounds corners\n\t\t\tlet posIndex = 0;\n\t\t\tconst positionArray = new Float32Array( 8 * 3 * boundsCount );\n\t\t\tboundsTree.traverse( ( depth, isLeaf, boundingData ) => {\n\n\t\t\t\tconst terminate = depth === targetDepth || isLeaf;\n\t\t\t\tif ( terminate || displayParents ) {\n\n\t\t\t\t\tarrayToBox( 0, boundingData, boundingBox );\n\n\t\t\t\t\tconst { min, max } = boundingBox;\n\t\t\t\t\tfor ( let x = - 1; x <= 1; x += 2 ) {\n\n\t\t\t\t\t\tconst xVal = x < 0 ? min.x : max.x;\n\t\t\t\t\t\tfor ( let y = - 1; y <= 1; y += 2 ) {\n\n\t\t\t\t\t\t\tconst yVal = y < 0 ? min.y : max.y;\n\t\t\t\t\t\t\tfor ( let z = - 1; z <= 1; z += 2 ) {\n\n\t\t\t\t\t\t\t\tconst zVal = z < 0 ? min.z : max.z;\n\t\t\t\t\t\t\t\tpositionArray[ posIndex + 0 ] = xVal;\n\t\t\t\t\t\t\t\tpositionArray[ posIndex + 1 ] = yVal;\n\t\t\t\t\t\t\t\tpositionArray[ posIndex + 2 ] = zVal;\n\n\t\t\t\t\t\t\t\tposIndex += 3;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn terminate;\n\n\t\t\t\t}\n\n\t\t\t}, group );\n\n\t\t\tlet indexArray;\n\t\t\tlet indices;\n\t\t\tif ( this.displayEdges ) {\n\n\t\t\t\t// fill in the index buffer to point to the corner points\n\t\t\t\tindices = new Uint8Array( [\n\t\t\t\t\t// x axis\n\t\t\t\t\t0, 4,\n\t\t\t\t\t1, 5,\n\t\t\t\t\t2, 6,\n\t\t\t\t\t3, 7,\n\n\t\t\t\t\t// y axis\n\t\t\t\t\t0, 2,\n\t\t\t\t\t1, 3,\n\t\t\t\t\t4, 6,\n\t\t\t\t\t5, 7,\n\n\t\t\t\t\t// z axis\n\t\t\t\t\t0, 1,\n\t\t\t\t\t2, 3,\n\t\t\t\t\t4, 5,\n\t\t\t\t\t6, 7,\n\t\t\t\t] );\n\n\t\t\t} else {\n\n\t\t\t\tindices = new Uint8Array( [\n\n\t\t\t\t\t// X-, X+\n\t\t\t\t\t0, 1, 2,\n\t\t\t\t\t2, 1, 3,\n\n\t\t\t\t\t4, 6, 5,\n\t\t\t\t\t6, 7, 5,\n\n\t\t\t\t\t// Y-, Y+\n\t\t\t\t\t1, 4, 5,\n\t\t\t\t\t0, 4, 1,\n\n\t\t\t\t\t2, 3, 6,\n\t\t\t\t\t3, 7, 6,\n\n\t\t\t\t\t// Z-, Z+\n\t\t\t\t\t0, 2, 4,\n\t\t\t\t\t2, 6, 4,\n\n\t\t\t\t\t1, 5, 3,\n\t\t\t\t\t3, 5, 7,\n\n\t\t\t\t] );\n\n\t\t\t}\n\n\t\t\tif ( positionArray.length > 65535 ) {\n\n\t\t\t\tindexArray = new Uint32Array( indices.length * boundsCount );\n\n\t\t\t} else {\n\n\t\t\t\tindexArray = new Uint16Array( indices.length * boundsCount );\n\n\t\t\t}\n\n\t\t\tconst indexLength = indices.length;\n\t\t\tfor ( let i = 0; i < boundsCount; i ++ ) {\n\n\t\t\t\tconst posOffset = i * 8;\n\t\t\t\tconst indexOffset = i * indexLength;\n\t\t\t\tfor ( let j = 0; j < indexLength; j ++ ) {\n\n\t\t\t\t\tindexArray[ indexOffset + j ] = posOffset + indices[ j ];\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// update the geometry\n\t\t\tgeometry.setIndex(\n\t\t\t\tnew BufferAttribute( indexArray, 1, false ),\n\t\t\t);\n\t\t\tgeometry.setAttribute(\n\t\t\t\t'position',\n\t\t\t\tnew BufferAttribute( positionArray, 3, false ),\n\t\t\t);\n\t\t\tthis.visible = true;\n\n\t\t}\n\n\t}\n\n}\n\nclass MeshBVHVisualizer extends Group {\n\n\tget color() {\n\n\t\treturn this.edgeMaterial.color;\n\n\t}\n\n\tget opacity() {\n\n\t\treturn this.edgeMaterial.opacity;\n\n\t}\n\n\tset opacity( v ) {\n\n\t\tthis.edgeMaterial.opacity = v;\n\t\tthis.meshMaterial.opacity = v;\n\n\t}\n\n\tconstructor( mesh, depth = 10 ) {\n\n\t\tsuper();\n\n\t\tthis.name = 'MeshBVHVisualizer';\n\t\tthis.depth = depth;\n\t\tthis.mesh = mesh;\n\t\tthis.displayParents = false;\n\t\tthis.displayEdges = true;\n\t\tthis._roots = [];\n\n\t\tconst edgeMaterial = new LineBasicMaterial( {\n\t\t\tcolor: 0x00FF88,\n\t\t\ttransparent: true,\n\t\t\topacity: 0.3,\n\t\t\tdepthWrite: false,\n\t\t} );\n\n\t\tconst meshMaterial = new MeshBasicMaterial( {\n\t\t\tcolor: 0x00FF88,\n\t\t\ttransparent: true,\n\t\t\topacity: 0.3,\n\t\t\tdepthWrite: false,\n\t\t} );\n\n\t\tmeshMaterial.color = edgeMaterial.color;\n\n\t\tthis.edgeMaterial = edgeMaterial;\n\t\tthis.meshMaterial = meshMaterial;\n\n\t\tthis.update();\n\n\t}\n\n\tupdate() {\n\n\t\tconst bvh = this.mesh.geometry.boundsTree;\n\t\tconst totalRoots = bvh ? bvh._roots.length : 0;\n\t\twhile ( this._roots.length > totalRoots ) {\n\n\t\t\tconst root = this._roots.pop();\n\t\t\troot.geometry.dispose();\n\t\t\tthis.remove( root );\n\n\t\t}\n\n\t\tfor ( let i = 0; i < totalRoots; i ++ ) {\n\n\t\t\tif ( i >= this._roots.length ) {\n\n\t\t\t\tconst root = new MeshBVHRootVisualizer( this.mesh, this.edgeMaterial, this.depth, i );\n\t\t\t\tthis.add( root );\n\t\t\t\tthis._roots.push( root );\n\n\t\t\t}\n\n\t\t\tconst root = this._roots[ i ];\n\t\t\troot.depth = this.depth;\n\t\t\troot.mesh = this.mesh;\n\t\t\troot.displayParents = this.displayParents;\n\t\t\troot.displayEdges = this.displayEdges;\n\t\t\troot.material = this.displayEdges ? this.edgeMaterial : this.meshMaterial;\n\t\t\troot.update();\n\n\t\t}\n\n\t}\n\n\tupdateMatrixWorld( ...args ) {\n\n\t\tthis.position.copy( this.mesh.position );\n\t\tthis.rotation.copy( this.mesh.rotation );\n\t\tthis.scale.copy( this.mesh.scale );\n\n\t\tsuper.updateMatrixWorld( ...args );\n\n\t}\n\n\tcopy( source ) {\n\n\t\tthis.depth = source.depth;\n\t\tthis.mesh = source.mesh;\n\n\t}\n\n\tclone() {\n\n\t\treturn new MeshBVHVisualizer( this.mesh, this.depth );\n\n\t}\n\n\tdispose() {\n\n\t\tthis.edgeMaterial.dispose();\n\t\tthis.meshMaterial.dispose();\n\n\t\tconst children = this.children;\n\t\tfor ( let i = 0, l = children.length; i < l; i ++ ) {\n\n\t\t\tchildren[ i ].geometry.dispose();\n\n\t\t}\n\n\t}\n\n}\n\n\nexport { MeshBVHVisualizer };\n", "import { Box3, Vector3 } from 'three';\nimport { TRAVERSAL_COST, TRIANGLE_INTERSECT_COST } from '../core/Constants.js';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\n\nconst _box1 = /* @__PURE__ */ new Box3();\nconst _box2 = /* @__PURE__ */ new Box3();\nconst _vec = /* @__PURE__ */ new Vector3();\n\n// https://stackoverflow.com/questions/1248302/how-to-get-the-size-of-a-javascript-object\nfunction getPrimitiveSize( el ) {\n\n\tswitch ( typeof el ) {\n\n\t\tcase 'number':\n\t\t\treturn 8;\n\t\tcase 'string':\n\t\t\treturn el.length * 2;\n\t\tcase 'boolean':\n\t\t\treturn 4;\n\t\tdefault:\n\t\t\treturn 0;\n\n\t}\n\n}\n\nfunction isTypedArray( arr ) {\n\n\tconst regex = /(Uint|Int|Float)(8|16|32)Array/;\n\treturn regex.test( arr.constructor.name );\n\n}\n\nfunction getRootExtremes( bvh, group ) {\n\n\tconst result = {\n\t\tnodeCount: 0,\n\t\tleafNodeCount: 0,\n\n\t\tdepth: {\n\t\t\tmin: Infinity, max: - Infinity\n\t\t},\n\t\ttris: {\n\t\t\tmin: Infinity, max: - Infinity\n\t\t},\n\t\tsplits: [ 0, 0, 0 ],\n\t\tsurfaceAreaScore: 0,\n\t};\n\n\tbvh.traverse( ( depth, isLeaf, boundingData, offsetOrSplit, count ) => {\n\n\t\tconst l0 = boundingData[ 0 + 3 ] - boundingData[ 0 ];\n\t\tconst l1 = boundingData[ 1 + 3 ] - boundingData[ 1 ];\n\t\tconst l2 = boundingData[ 2 + 3 ] - boundingData[ 2 ];\n\n\t\tconst surfaceArea = 2 * ( l0 * l1 + l1 * l2 + l2 * l0 );\n\n\t\tresult.nodeCount ++;\n\t\tif ( isLeaf ) {\n\n\t\t\tresult.leafNodeCount ++;\n\n\t\t\tresult.depth.min = Math.min( depth, result.depth.min );\n\t\t\tresult.depth.max = Math.max( depth, result.depth.max );\n\n\t\t\tresult.tris.min = Math.min( count, result.tris.min );\n\t\t\tresult.tris.max = Math.max( count, result.tris.max );\n\n\t\t\tresult.surfaceAreaScore += surfaceArea * TRIANGLE_INTERSECT_COST * count;\n\n\t\t} else {\n\n\t\t\tresult.splits[ offsetOrSplit ] ++;\n\n\t\t\tresult.surfaceAreaScore += surfaceArea * TRAVERSAL_COST;\n\n\t\t}\n\n\t}, group );\n\n\t// If there are no leaf nodes because the tree hasn't finished generating yet.\n\tif ( result.tris.min === Infinity ) {\n\n\t\tresult.tris.min = 0;\n\t\tresult.tris.max = 0;\n\n\t}\n\n\tif ( result.depth.min === Infinity ) {\n\n\t\tresult.depth.min = 0;\n\t\tresult.depth.max = 0;\n\n\t}\n\n\treturn result;\n\n}\n\nfunction getBVHExtremes( bvh ) {\n\n\treturn bvh._roots.map( ( root, i ) => getRootExtremes( bvh, i ) );\n\n}\n\nfunction estimateMemoryInBytes( obj ) {\n\n\tconst traversed = new Set();\n\tconst stack = [ obj ];\n\tlet bytes = 0;\n\n\twhile ( stack.length ) {\n\n\t\tconst curr = stack.pop();\n\t\tif ( traversed.has( curr ) ) {\n\n\t\t\tcontinue;\n\n\t\t}\n\n\t\ttraversed.add( curr );\n\n\t\tfor ( let key in curr ) {\n\n\t\t\tif ( ! curr.hasOwnProperty( key ) ) {\n\n\t\t\t\tcontinue;\n\n\t\t\t}\n\n\t\t\tbytes += getPrimitiveSize( key );\n\n\t\t\tconst value = curr[ key ];\n\t\t\tif ( value && ( typeof value === 'object' || typeof value === 'function' ) ) {\n\n\t\t\t\tif ( isTypedArray( value ) ) {\n\n\t\t\t\t\tbytes += value.byteLength;\n\n\t\t\t\t} else if ( value instanceof ArrayBuffer ) {\n\n\t\t\t\t\tbytes += value.byteLength;\n\n\t\t\t\t} else {\n\n\t\t\t\t\tstack.push( value );\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tbytes += getPrimitiveSize( value );\n\n\t\t\t}\n\n\n\t\t}\n\n\t}\n\n\treturn bytes;\n\n}\n\nfunction validateBounds( bvh ) {\n\n\tconst geometry = bvh.geometry;\n\tconst depthStack = [];\n\tconst index = geometry.index;\n\tconst position = geometry.getAttribute( 'position' );\n\tlet passes = true;\n\n\tbvh.traverse( ( depth, isLeaf, boundingData, offset, count ) => {\n\n\t\tconst info = {\n\t\t\tdepth,\n\t\t\tisLeaf,\n\t\t\tboundingData,\n\t\t\toffset,\n\t\t\tcount,\n\t\t};\n\t\tdepthStack[ depth ] = info;\n\n\t\tarrayToBox( 0, boundingData, _box1 );\n\t\tconst parent = depthStack[ depth - 1 ];\n\n\t\tif ( isLeaf ) {\n\n\t\t\t// check triangles\n\t\t\tfor ( let i = offset * 3, l = ( offset + count ) * 3; i < l; i += 3 ) {\n\n\t\t\t\tconst i0 = index.getX( i );\n\t\t\t\tconst i1 = index.getX( i + 1 );\n\t\t\t\tconst i2 = index.getX( i + 2 );\n\n\t\t\t\tlet isContained;\n\n\t\t\t\t_vec.fromBufferAttribute( position, i0 );\n\t\t\t\tisContained = _box1.containsPoint( _vec );\n\n\t\t\t\t_vec.fromBufferAttribute( position, i1 );\n\t\t\t\tisContained = isContained && _box1.containsPoint( _vec );\n\n\t\t\t\t_vec.fromBufferAttribute( position, i2 );\n\t\t\t\tisContained = isContained && _box1.containsPoint( _vec );\n\n\t\t\t\tconsole.assert( isContained, 'Leaf bounds does not fully contain triangle.' );\n\t\t\t\tpasses = passes && isContained;\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( parent ) {\n\n\t\t\t// check if my bounds fit in my parents\n\t\t\tarrayToBox( 0, boundingData, _box2 );\n\n\t\t\tconst isContained = _box2.containsBox( _box1 );\n\t\t\tconsole.assert( isContained, 'Parent bounds does not fully contain child.' );\n\t\t\tpasses = passes && isContained;\n\n\t\t}\n\n\t} );\n\n\treturn passes;\n\n}\n\n// Returns a simple, human readable object that represents the BVH.\nfunction getJSONStructure( bvh ) {\n\n\tconst depthStack = [];\n\n\tbvh.traverse( ( depth, isLeaf, boundingData, offset, count ) => {\n\n\t\tconst info = {\n\t\t\tbounds: arrayToBox( 0, boundingData, new Box3() ),\n\t\t};\n\n\t\tif ( isLeaf ) {\n\n\t\t\tinfo.count = count;\n\t\t\tinfo.offset = offset;\n\n\t\t} else {\n\n\t\t\tinfo.left = null;\n\t\t\tinfo.right = null;\n\n\t\t}\n\n\t\tdepthStack[ depth ] = info;\n\n\t\t// traversal hits the left then right node\n\t\tconst parent = depthStack[ depth - 1 ];\n\t\tif ( parent ) {\n\n\t\t\tif ( parent.left === null ) {\n\n\t\t\t\tparent.left = info;\n\n\t\t\t} else {\n\n\t\t\t\tparent.right = info;\n\n\t\t\t}\n\n\t\t}\n\n\t} );\n\n\treturn depthStack[ 0 ];\n\n}\n\nexport { estimateMemoryInBytes, getBVHExtremes, validateBounds, getJSONStructure };\n", "import { Ray, Matrix4, <PERSON><PERSON> } from 'three';\nimport { convertRaycastIntersect } from './GeometryRayIntersectUtilities.js';\nimport { MeshBVH } from '../core/MeshBVH.js';\n\nconst ray = /* @__PURE__ */ new Ray();\nconst tmpInverseMatrix = /* @__PURE__ */ new Matrix4();\nconst origMeshRaycastFunc = Mesh.prototype.raycast;\n\nexport function acceleratedRaycast( raycaster, intersects ) {\n\n\tif ( this.geometry.boundsTree ) {\n\n\t\tif ( this.material === undefined ) return;\n\n\t\ttmpInverseMatrix.copy( this.matrixWorld ).invert();\n\t\tray.copy( raycaster.ray ).applyMatrix4( tmpInverseMatrix );\n\n\t\tconst bvh = this.geometry.boundsTree;\n\t\tif ( raycaster.firstHitOnly === true ) {\n\n\t\t\tconst hit = convertRaycastIntersect( bvh.raycastFirst( ray, this.material ), this, raycaster );\n\t\t\tif ( hit ) {\n\n\t\t\t\tintersects.push( hit );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconst hits = bvh.raycast( ray, this.material );\n\t\t\tfor ( let i = 0, l = hits.length; i < l; i ++ ) {\n\n\t\t\t\tconst hit = convertRaycastIntersect( hits[ i ], this, raycaster );\n\t\t\t\tif ( hit ) {\n\n\t\t\t\t\tintersects.push( hit );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t} else {\n\n\t\torigMeshRaycastFunc.call( this, raycaster, intersects );\n\n\t}\n\n}\n\nexport function computeBoundsTree( options ) {\n\n\tthis.boundsTree = new MeshBVH( this, options );\n\treturn this.boundsTree;\n\n}\n\nexport function disposeBoundsTree() {\n\n\tthis.boundsTree = null;\n\n}\n", "import {\n\tDataTexture,\n\tFloatType,\n\tIntType,\n\tUnsignedIntType,\n\tByteType,\n\tUnsignedByteType,\n\tShortType,\n\tUnsignedShortType,\n\n\tRedFormat,\n\tRGFormat,\n\tRGBAFormat,\n\n\tRedIntegerFormat,\n\tRGIntegerFormat,\n\tRGBAIntegerFormat,\n\n\tNearestFilter,\n} from 'three';\n\nfunction countToStringFormat( count ) {\n\n\tswitch ( count ) {\n\n\t\tcase 1: return 'R';\n\t\tcase 2: return 'RG';\n\t\tcase 3: return 'RGBA';\n\t\tcase 4: return 'RGBA';\n\n\t}\n\n\tthrow new Error();\n\n}\n\nfunction countToFormat( count ) {\n\n\tswitch ( count ) {\n\n\t\tcase 1: return RedFormat;\n\t\tcase 2: return RGFormat;\n\t\tcase 3: return RGBAFormat;\n\t\tcase 4: return RGBAFormat;\n\n\t}\n\n}\n\nfunction countToIntFormat( count ) {\n\n\tswitch ( count ) {\n\n\t\tcase 1: return RedIntegerFormat;\n\t\tcase 2: return RGIntegerFormat;\n\t\tcase 3: return RGBAIntegerFormat;\n\t\tcase 4: return RGBAIntegerFormat;\n\n\t}\n\n}\n\nexport class VertexAttributeTexture extends DataTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis.minFilter = NearestFilter;\n\t\tthis.magFilter = NearestFilter;\n\t\tthis.generateMipmaps = false;\n\t\tthis.overrideItemSize = null;\n\t\tthis._forcedType = null;\n\n\t}\n\n\tupdateFrom( attr ) {\n\n\t\tconst overrideItemSize = this.overrideItemSize;\n\t\tconst originalItemSize = attr.itemSize;\n\t\tconst originalCount = attr.count;\n\t\tif ( overrideItemSize !== null ) {\n\n\t\t\tif ( ( originalItemSize * originalCount ) % overrideItemSize !== 0.0 ) {\n\n\t\t\t\tthrow new Error( 'VertexAttributeTexture: overrideItemSize must divide evenly into buffer length.' );\n\n\t\t\t}\n\n\t\t\tattr.itemSize = overrideItemSize;\n\t\t\tattr.count = originalCount * originalItemSize / overrideItemSize;\n\n\t\t}\n\n\t\tconst itemSize = attr.itemSize;\n\t\tconst count = attr.count;\n\t\tconst normalized = attr.normalized;\n\t\tconst originalBufferCons = attr.array.constructor;\n\t\tconst byteCount = originalBufferCons.BYTES_PER_ELEMENT;\n\t\tlet targetType = this._forcedType;\n\t\tlet finalStride = itemSize;\n\n\t\t// derive the type of texture this should be in the shader\n\t\tif ( targetType === null ) {\n\n\t\t\tswitch ( originalBufferCons ) {\n\n\t\t\t\tcase Float32Array:\n\t\t\t\t\ttargetType = FloatType;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase Uint8Array:\n\t\t\t\tcase Uint16Array:\n\t\t\t\tcase Uint32Array:\n\t\t\t\t\ttargetType = UnsignedIntType;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase Int8Array:\n\t\t\t\tcase Int16Array:\n\t\t\t\tcase Int32Array:\n\t\t\t\t\ttargetType = IntType;\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// get the target format to store the texture as\n\t\tlet type, format, normalizeValue, targetBufferCons;\n\t\tlet internalFormat = countToStringFormat( itemSize );\n\t\tswitch ( targetType ) {\n\n\t\t\tcase FloatType:\n\t\t\t\tnormalizeValue = 1.0;\n\t\t\t\tformat = countToFormat( itemSize );\n\n\t\t\t\tif ( normalized && byteCount === 1 ) {\n\n\t\t\t\t\ttargetBufferCons = originalBufferCons;\n\t\t\t\t\tinternalFormat += '8';\n\n\t\t\t\t\tif ( originalBufferCons === Uint8Array ) {\n\n\t\t\t\t\t\ttype = UnsignedByteType;\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\ttype = ByteType;\n\t\t\t\t\t\tinternalFormat += '_SNORM';\n\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttargetBufferCons = Float32Array;\n\t\t\t\t\tinternalFormat += '32F';\n\t\t\t\t\ttype = FloatType;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t\tcase IntType:\n\t\t\t\tinternalFormat += byteCount * 8 + 'I';\n\t\t\t\tnormalizeValue = normalized ? Math.pow( 2, originalBufferCons.BYTES_PER_ELEMENT * 8 - 1 ) : 1.0;\n\t\t\t\tformat = countToIntFormat( itemSize );\n\n\t\t\t\tif ( byteCount === 1 ) {\n\n\t\t\t\t\ttargetBufferCons = Int8Array;\n\t\t\t\t\ttype = ByteType;\n\n\t\t\t\t} else if ( byteCount === 2 ) {\n\n\t\t\t\t\ttargetBufferCons = Int16Array;\n\t\t\t\t\ttype = ShortType;\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttargetBufferCons = Int32Array;\n\t\t\t\t\ttype = IntType;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t\tcase UnsignedIntType:\n\t\t\t\tinternalFormat += byteCount * 8 + 'UI';\n\t\t\t\tnormalizeValue = normalized ? Math.pow( 2, originalBufferCons.BYTES_PER_ELEMENT * 8 - 1 ) : 1.0;\n\t\t\t\tformat = countToIntFormat( itemSize );\n\n\t\t\t\tif ( byteCount === 1 ) {\n\n\t\t\t\t\ttargetBufferCons = Uint8Array;\n\t\t\t\t\ttype = UnsignedByteType;\n\n\t\t\t\t} else if ( byteCount === 2 ) {\n\n\t\t\t\t\ttargetBufferCons = Uint16Array;\n\t\t\t\t\ttype = UnsignedShortType;\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttargetBufferCons = Uint32Array;\n\t\t\t\t\ttype = UnsignedIntType;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\t// there will be a mismatch between format length and final length because\n\t\t// RGBFormat and RGBIntegerFormat was removed\n\t\tif ( finalStride === 3 && ( format === RGBAFormat || format === RGBAIntegerFormat ) ) {\n\n\t\t\tfinalStride = 4;\n\n\t\t}\n\n\t\t// copy the data over to the new texture array\n\t\tconst dimension = Math.ceil( Math.sqrt( count ) );\n\t\tconst length = finalStride * dimension * dimension;\n\t\tconst dataArray = new targetBufferCons( length );\n\n\t\t// temporarily set the normalized state to false since we have custom normalization logic\n\t\tconst originalNormalized = attr.normalized;\n\t\tattr.normalized = false;\n\t\tfor ( let i = 0; i < count; i ++ ) {\n\n\t\t\tconst ii = finalStride * i;\n\t\t\tdataArray[ ii ] = attr.getX( i ) / normalizeValue;\n\n\t\t\tif ( itemSize >= 2 ) {\n\n\t\t\t\tdataArray[ ii + 1 ] = attr.getY( i ) / normalizeValue;\n\n\t\t\t}\n\n\t\t\tif ( itemSize >= 3 ) {\n\n\t\t\t\tdataArray[ ii + 2 ] = attr.getZ( i ) / normalizeValue;\n\n\t\t\t\tif ( finalStride === 4 ) {\n\n\t\t\t\t\tdataArray[ ii + 3 ] = 1.0;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( itemSize >= 4 ) {\n\n\t\t\t\tdataArray[ ii + 3 ] = attr.getW( i ) / normalizeValue;\n\n\t\t\t}\n\n\t\t}\n\n\t\tattr.normalized = originalNormalized;\n\n\t\tthis.internalFormat = internalFormat;\n\t\tthis.format = format;\n\t\tthis.type = type;\n\t\tthis.image.width = dimension;\n\t\tthis.image.height = dimension;\n\t\tthis.image.data = dataArray;\n\t\tthis.needsUpdate = true;\n\t\tthis.dispose();\n\n\t\tattr.itemSize = originalItemSize;\n\t\tattr.count = originalCount;\n\n\t}\n\n}\n\nexport class UIntVertexAttributeTexture extends VertexAttributeTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis._forcedType = UnsignedIntType;\n\n\t}\n\n}\n\nexport class IntVertexAttributeTexture extends VertexAttributeTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis._forcedType = IntType;\n\n\t}\n\n\n}\n\nexport class FloatVertexAttributeTexture extends VertexAttributeTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis._forcedType = FloatType;\n\n\t}\n\n}\n", "import {\n\tDataTexture,\n\tFloatType,\n\tUnsignedIntType,\n\tRGBAFormat,\n\tRGIntegerFormat,\n\tNearestFilter,\n} from 'three';\nimport {\n\tFloatVertexAttributeTexture,\n\tUIntVertexAttributeTexture,\n} from './VertexAttributeTexture.js';\nimport { BYTES_PER_NODE } from '../core/Constants.js';\nimport {\n\tBOUNDING_DATA_INDEX,\n\tCOUNT,\n\tIS_LEAF,\n\tRIGHT_NODE,\n\tOFFSET,\n\tSPLIT_AXIS,\n} from '../core/nodeBufferFunctions.js';\n\nfunction bvhToTextures( bvh, boundsTexture, contentsTexture ) {\n\n\tconst roots = bvh._roots;\n\n\tif ( roots.length !== 1 ) {\n\n\t\tthrow new Error( 'MeshBVHUniformStruct: Multi-root BVHs not supported.' );\n\n\t}\n\n\tconst root = roots[ 0 ];\n\tconst uint16Array = new Uint16Array( root );\n\tconst uint32Array = new Uint32Array( root );\n\tconst float32Array = new Float32Array( root );\n\n\t// Both bounds need two elements per node so compute the height so it's twice as long as\n\t// the width so we can expand the row by two and still have a square texture\n\tconst nodeCount = root.byteLength / BYTES_PER_NODE;\n\tconst boundsDimension = 2 * Math.ceil( Math.sqrt( nodeCount / 2 ) );\n\tconst boundsArray = new Float32Array( 4 * boundsDimension * boundsDimension );\n\n\tconst contentsDimension = Math.ceil( Math.sqrt( nodeCount ) );\n\tconst contentsArray = new Uint32Array( 2 * contentsDimension * contentsDimension );\n\n\tfor ( let i = 0; i < nodeCount; i ++ ) {\n\n\t\tconst nodeIndex32 = i * BYTES_PER_NODE / 4;\n\t\tconst nodeIndex16 = nodeIndex32 * 2;\n\t\tconst boundsIndex = BOUNDING_DATA_INDEX( nodeIndex32 );\n\t\tfor ( let b = 0; b < 3; b ++ ) {\n\n\t\t\tboundsArray[ 8 * i + 0 + b ] = float32Array[ boundsIndex + 0 + b ];\n\t\t\tboundsArray[ 8 * i + 4 + b ] = float32Array[ boundsIndex + 3 + b ];\n\n\t\t}\n\n\t\tif ( IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\t\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\n\t\t\tconst mergedLeafCount = 0xffff0000 | count;\n\t\t\tcontentsArray[ i * 2 + 0 ] = mergedLeafCount;\n\t\t\tcontentsArray[ i * 2 + 1 ] = offset;\n\n\t\t} else {\n\n\t\t\tconst rightIndex = 4 * RIGHT_NODE( nodeIndex32, uint32Array ) / BYTES_PER_NODE;\n\t\t\tconst splitAxis = SPLIT_AXIS( nodeIndex32, uint32Array );\n\n\t\t\tcontentsArray[ i * 2 + 0 ] = splitAxis;\n\t\t\tcontentsArray[ i * 2 + 1 ] = rightIndex;\n\n\t\t}\n\n\t}\n\n\tboundsTexture.image.data = boundsArray;\n\tboundsTexture.image.width = boundsDimension;\n\tboundsTexture.image.height = boundsDimension;\n\tboundsTexture.format = RGBAFormat;\n\tboundsTexture.type = FloatType;\n\tboundsTexture.internalFormat = 'RGBA32F';\n\tboundsTexture.minFilter = NearestFilter;\n\tboundsTexture.magFilter = NearestFilter;\n\tboundsTexture.generateMipmaps = false;\n\tboundsTexture.needsUpdate = true;\n\tboundsTexture.dispose();\n\n\tcontentsTexture.image.data = contentsArray;\n\tcontentsTexture.image.width = contentsDimension;\n\tcontentsTexture.image.height = contentsDimension;\n\tcontentsTexture.format = RGIntegerFormat;\n\tcontentsTexture.type = UnsignedIntType;\n\tcontentsTexture.internalFormat = 'RG32UI';\n\tcontentsTexture.minFilter = NearestFilter;\n\tcontentsTexture.magFilter = NearestFilter;\n\tcontentsTexture.generateMipmaps = false;\n\tcontentsTexture.needsUpdate = true;\n\tcontentsTexture.dispose();\n\n}\n\nexport class MeshBVHUniformStruct {\n\n\tconstructor() {\n\n\t\tthis.autoDispose = true;\n\t\tthis.index = new UIntVertexAttributeTexture();\n\t\tthis.position = new FloatVertexAttributeTexture();\n\t\tthis.bvhBounds = new DataTexture();\n\t\tthis.bvhContents = new DataTexture();\n\n\t\tthis.index.overrideItemSize = 3;\n\n\t}\n\n\tupdateFrom( bvh ) {\n\n\t\tconst { geometry } = bvh;\n\n\t\tbvhToTextures( bvh, this.bvhBounds, this.bvhContents );\n\n\t\tthis.index.updateFrom( geometry.index );\n\t\tthis.position.updateFrom( geometry.attributes.position );\n\n\t}\n\n\tdispose() {\n\n\t\tconst { index, position, bvhBounds, bvhContents } = this;\n\n\t\tif ( index ) index.dispose();\n\t\tif ( position ) position.dispose();\n\t\tif ( bvhBounds ) bvhBounds.dispose();\n\t\tif ( bvhContents ) bvhContents.dispose();\n\n\t}\n\n}\n", "// Note that a struct cannot be used for the hit record including faceIndices, faceNormal, barycoord,\n// side, and dist because on some mobile GPUS (such as Adreno) numbers are afforded less precision specifically\n// when in a struct leading to inaccurate hit results. See KhronosGroup/WebGL#3351 for more details.\nexport const shaderStructs = /* glsl */`\n#ifndef TRI_INTERSECT_EPSILON\n#define TRI_INTERSECT_EPSILON 1e-5\n#endif\n\n#ifndef INFINITY\n#define INFINITY 1e20\n#endif\n\nstruct BVH {\n\n\tusampler2D index;\n\tsampler2D position;\n\n\tsampler2D bvhBounds;\n\tusampler2D bvhContents;\n\n};\n`;\n\nexport const shaderIntersectFunction = /* glsl */`\n\n// Utilities\nuvec4 uTexelFetch1D( usampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nivec4 iTexelFetch1D( isampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nvec4 texelFetch1D( sampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nvec4 textureSampleBarycoord( sampler2D tex, vec3 barycoord, uvec3 faceIndices ) {\n\n\treturn\n\t\tbarycoord.x * texelFetch1D( tex, faceIndices.x ) +\n\t\tbarycoord.y * texelFetch1D( tex, faceIndices.y ) +\n\t\tbarycoord.z * texelFetch1D( tex, faceIndices.z );\n\n}\n\nvoid ndcToCameraRay(\n\tvec2 coord, mat4 cameraWorld, mat4 invProjectionMatrix,\n\tout vec3 rayOrigin, out vec3 rayDirection\n) {\n\n\t// get camera look direction and near plane for camera clipping\n\tvec4 lookDirection = cameraWorld * vec4( 0.0, 0.0, - 1.0, 0.0 );\n\tvec4 nearVector = invProjectionMatrix * vec4( 0.0, 0.0, - 1.0, 1.0 );\n\tfloat near = abs( nearVector.z / nearVector.w );\n\n\t// get the camera direction and position from camera matrices\n\tvec4 origin = cameraWorld * vec4( 0.0, 0.0, 0.0, 1.0 );\n\tvec4 direction = invProjectionMatrix * vec4( coord, 0.5, 1.0 );\n\tdirection /= direction.w;\n\tdirection = cameraWorld * direction - origin;\n\n\t// slide the origin along the ray until it sits at the near clip plane position\n\torigin.xyz += direction.xyz * near / dot( direction, lookDirection );\n\n\trayOrigin = origin.xyz;\n\trayDirection = direction.xyz;\n\n}\n\n// Raycasting\nfloat intersectsBounds( vec3 rayOrigin, vec3 rayDirection, vec3 boundsMin, vec3 boundsMax ) {\n\n\t// https://www.reddit.com/r/opengl/comments/8ntzz5/fast_glsl_ray_box_intersection/\n\t// https://tavianator.com/2011/ray_box.html\n\tvec3 invDir = 1.0 / rayDirection;\n\n\t// find intersection distances for each plane\n\tvec3 tMinPlane = invDir * ( boundsMin - rayOrigin );\n\tvec3 tMaxPlane = invDir * ( boundsMax - rayOrigin );\n\n\t// get the min and max distances from each intersection\n\tvec3 tMinHit = min( tMaxPlane, tMinPlane );\n\tvec3 tMaxHit = max( tMaxPlane, tMinPlane );\n\n\t// get the furthest hit distance\n\tvec2 t = max( tMinHit.xx, tMinHit.yz );\n\tfloat t0 = max( t.x, t.y );\n\n\t// get the minimum hit distance\n\tt = min( tMaxHit.xx, tMaxHit.yz );\n\tfloat t1 = min( t.x, t.y );\n\n\t// set distance to 0.0 if the ray starts inside the box\n\tfloat dist = max( t0, 0.0 );\n\n\treturn t1 >= dist ? dist : INFINITY;\n\n}\n\nbool intersectsTriangle(\n\tvec3 rayOrigin, vec3 rayDirection, vec3 a, vec3 b, vec3 c,\n\tout vec3 barycoord, out vec3 norm, out float dist, out float side\n) {\n\n\t// https://stackoverflow.com/questions/42740765/intersection-between-line-and-triangle-in-3d\n\tvec3 edge1 = b - a;\n\tvec3 edge2 = c - a;\n\tnorm = cross( edge1, edge2 );\n\n\tfloat det = - dot( rayDirection, norm );\n\tfloat invdet = 1.0 / det;\n\n\tvec3 AO = rayOrigin - a;\n\tvec3 DAO = cross( AO, rayDirection );\n\n\tvec4 uvt;\n\tuvt.x = dot( edge2, DAO ) * invdet;\n\tuvt.y = - dot( edge1, DAO ) * invdet;\n\tuvt.z = dot( AO, norm ) * invdet;\n\tuvt.w = 1.0 - uvt.x - uvt.y;\n\n\t// set the hit information\n\tbarycoord = uvt.wxy; // arranged in A, B, C order\n\tdist = uvt.z;\n\tside = sign( det );\n\tnorm = side * normalize( norm );\n\n\t// add an epsilon to avoid misses between triangles\n\tuvt += vec4( TRI_INTERSECT_EPSILON );\n\n\treturn all( greaterThanEqual( uvt, vec4( 0.0 ) ) );\n\n}\n\nbool intersectTriangles(\n\tBVH bvh, vec3 rayOrigin, vec3 rayDirection, uint offset, uint count,\n\tinout float minDistance,\n\n\t// output variables\n\tout uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord,\n\tout float side, out float dist\n) {\n\n\tbool found = false;\n\tvec3 localBarycoord, localNormal;\n\tfloat localDist, localSide;\n\tfor ( uint i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\tuvec3 indices = uTexelFetch1D( bvh.index, i ).xyz;\n\t\tvec3 a = texelFetch1D( bvh.position, indices.x ).rgb;\n\t\tvec3 b = texelFetch1D( bvh.position, indices.y ).rgb;\n\t\tvec3 c = texelFetch1D( bvh.position, indices.z ).rgb;\n\n\t\tif (\n\t\t\tintersectsTriangle( rayOrigin, rayDirection, a, b, c, localBarycoord, localNormal, localDist, localSide )\n\t\t\t&& localDist < minDistance\n\t\t) {\n\n\t\t\tfound = true;\n\t\t\tminDistance = localDist;\n\n\t\t\tfaceIndices = uvec4( indices.xyz, i );\n\t\t\tfaceNormal = localNormal;\n\n\t\t\tside = localSide;\n\t\t\tbarycoord = localBarycoord;\n\t\t\tdist = localDist;\n\n\t\t}\n\n\t}\n\n\treturn found;\n\n}\n\nfloat intersectsBVHNodeBounds( vec3 rayOrigin, vec3 rayDirection, BVH bvh, uint currNodeIndex ) {\n\n\tvec3 boundsMin = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 0u ).xyz;\n\tvec3 boundsMax = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 1u ).xyz;\n\treturn intersectsBounds( rayOrigin, rayDirection, boundsMin, boundsMax );\n\n}\n\nbool bvhIntersectFirstHit(\n\tBVH bvh, vec3 rayOrigin, vec3 rayDirection,\n\n\t// output variables\n\tout uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord,\n\tout float side, out float dist\n) {\n\n\t// stack needs to be twice as long as the deepest tree we expect because\n\t// we push both the left and right child onto the stack every traversal\n\tint ptr = 0;\n\tuint stack[ 60 ];\n\tstack[ 0 ] = 0u;\n\n\tfloat triangleDistance = 1e20;\n\tbool found = false;\n\twhile ( ptr > - 1 && ptr < 60 ) {\n\n\t\tuint currNodeIndex = stack[ ptr ];\n\t\tptr --;\n\n\t\t// check if we intersect the current bounds\n\t\tfloat boundsHitDistance = intersectsBVHNodeBounds( rayOrigin, rayDirection, bvh, currNodeIndex );\n\t\tif ( boundsHitDistance == INFINITY || boundsHitDistance > triangleDistance ) {\n\n\t\t\tcontinue;\n\n\t\t}\n\n\t\tuvec2 boundsInfo = uTexelFetch1D( bvh.bvhContents, currNodeIndex ).xy;\n\t\tbool isLeaf = bool( boundsInfo.x & 0xffff0000u );\n\n\t\tif ( isLeaf ) {\n\n\t\t\tuint count = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint offset = boundsInfo.y;\n\n\t\t\tfound = intersectTriangles(\n\t\t\t\tbvh, rayOrigin, rayDirection, offset, count, triangleDistance,\n\t\t\t\tfaceIndices, faceNormal, barycoord, side, dist\n\t\t\t) || found;\n\n\t\t} else {\n\n\t\t\tuint leftIndex = currNodeIndex + 1u;\n\t\t\tuint splitAxis = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint rightIndex = boundsInfo.y;\n\n\t\t\tbool leftToRight = rayDirection[ splitAxis ] >= 0.0;\n\t\t\tuint c1 = leftToRight ? leftIndex : rightIndex;\n\t\t\tuint c2 = leftToRight ? rightIndex : leftIndex;\n\n\t\t\t// set c2 in the stack so we traverse it later. We need to keep track of a pointer in\n\t\t\t// the stack while we traverse. The second pointer added is the one that will be\n\t\t\t// traversed first\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c2;\n\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c1;\n\n\t\t}\n\n\t}\n\n\treturn found;\n\n}\n`;\n\n// Distance to Point\nexport const shaderDistanceFunction = /* glsl */`\n\nfloat dot2( in vec3 v ) {\n\n\treturn dot( v, v );\n\n}\n\n\n// https://www.shadertoy.com/view/ttfGWl\nvec3 closestPointToTriangle( vec3 p, vec3 v0, vec3 v1, vec3 v2, out vec3 barycoord ) {\n\n    vec3 v10 = v1 - v0;\n    vec3 v21 = v2 - v1;\n    vec3 v02 = v0 - v2;\n\n\tvec3 p0 = p - v0;\n\tvec3 p1 = p - v1;\n\tvec3 p2 = p - v2;\n\n    vec3 nor = cross( v10, v02 );\n\n    // method 2, in barycentric space\n    vec3  q = cross( nor, p0 );\n    float d = 1.0 / dot2( nor );\n    float u = d * dot( q, v02 );\n    float v = d * dot( q, v10 );\n    float w = 1.0 - u - v;\n\n\tif( u < 0.0 ) {\n\n\t\tw = clamp( dot( p2, v02 ) / dot2( v02 ), 0.0, 1.0 );\n\t\tu = 0.0;\n\t\tv = 1.0 - w;\n\n\t} else if( v < 0.0 ) {\n\n\t\tu = clamp( dot( p0, v10 ) / dot2( v10 ), 0.0, 1.0 );\n\t\tv = 0.0;\n\t\tw = 1.0 - u;\n\n\t} else if( w < 0.0 ) {\n\n\t\tv = clamp( dot( p1, v21 ) / dot2( v21 ), 0.0, 1.0 );\n\t\tw = 0.0;\n\t\tu = 1.0-v;\n\n\t}\n\n\tbarycoord = vec3( u, v, w );\n    return u * v1 + v * v2 + w * v0;\n\n}\n\nfloat distanceToTriangles(\n\tBVH bvh, vec3 point, uint offset, uint count, float closestDistanceSquared,\n\n\tout uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord, out float side, out vec3 outPoint\n) {\n\n\tbool found = false;\n\tuvec3 localIndices;\n\tvec3 localBarycoord;\n\tvec3 localNormal;\n\tfor ( uint i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\tuvec3 indices = uTexelFetch1D( bvh.index, i ).xyz;\n\t\tvec3 a = texelFetch1D( bvh.position, indices.x ).rgb;\n\t\tvec3 b = texelFetch1D( bvh.position, indices.y ).rgb;\n\t\tvec3 c = texelFetch1D( bvh.position, indices.z ).rgb;\n\n\t\t// get the closest point and barycoord\n\t\tvec3 closestPoint = closestPointToTriangle( point, a, b, c, localBarycoord );\n\t\tvec3 delta = point - closestPoint;\n\t\tfloat sqDist = dot2( delta );\n\t\tif ( sqDist < closestDistanceSquared ) {\n\n\t\t\t// set the output results\n\t\t\tclosestDistanceSquared = sqDist;\n\t\t\tfaceIndices = uvec4( indices.xyz, i );\n\t\t\tfaceNormal = normalize( cross( a - b, b - c ) );\n\t\t\tbarycoord = localBarycoord;\n\t\t\toutPoint = closestPoint;\n\t\t\tside = sign( dot( faceNormal, delta ) );\n\n\t\t}\n\n\t}\n\n\treturn closestDistanceSquared;\n\n}\n\nfloat distanceSqToBounds( vec3 point, vec3 boundsMin, vec3 boundsMax ) {\n\n\tvec3 clampedPoint = clamp( point, boundsMin, boundsMax );\n\tvec3 delta = point - clampedPoint;\n\treturn dot( delta, delta );\n\n}\n\nfloat distanceSqToBVHNodeBoundsPoint( vec3 point, BVH bvh, uint currNodeIndex ) {\n\n\tvec3 boundsMin = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 0u ).xyz;\n\tvec3 boundsMax = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 1u ).xyz;\n\treturn distanceSqToBounds( point, boundsMin, boundsMax );\n\n}\n\nfloat bvhClosestPointToPoint(\n\tBVH bvh, vec3 point,\n\n\t// output variables\n\tout uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord,\n\tout float side, out vec3 outPoint\n ) {\n\n\t// stack needs to be twice as long as the deepest tree we expect because\n\t// we push both the left and right child onto the stack every traversal\n\tint ptr = 0;\n\tuint stack[ 60 ];\n\tstack[ 0 ] = 0u;\n\tfloat closestDistanceSquared = pow( 100000.0, 2.0 );\n\tbool found = false;\n\twhile ( ptr > - 1 && ptr < 60 ) {\n\n\t\tuint currNodeIndex = stack[ ptr ];\n\t\tptr --;\n\n\t\t// check if we intersect the current bounds\n\t\tfloat boundsHitDistance = distanceSqToBVHNodeBoundsPoint( point, bvh, currNodeIndex );\n\t\tif ( boundsHitDistance > closestDistanceSquared ) {\n\n\t\t\tcontinue;\n\n\t\t}\n\n\t\tuvec2 boundsInfo = uTexelFetch1D( bvh.bvhContents, currNodeIndex ).xy;\n\t\tbool isLeaf = bool( boundsInfo.x & 0xffff0000u );\n\t\tif ( isLeaf ) {\n\n\t\t\tuint count = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint offset = boundsInfo.y;\n\t\t\tclosestDistanceSquared = distanceToTriangles(\n\t\t\t\tbvh, point, offset, count, closestDistanceSquared,\n\n\t\t\t\t// outputs\n\t\t\t\tfaceIndices, faceNormal, barycoord, side, outPoint\n\t\t\t);\n\n\t\t} else {\n\n\t\t\tuint leftIndex = currNodeIndex + 1u;\n\t\t\tuint splitAxis = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint rightIndex = boundsInfo.y;\n\t\t\tbool leftToRight = distanceSqToBVHNodeBoundsPoint( point, bvh, leftIndex ) < distanceSqToBVHNodeBoundsPoint( point, bvh, rightIndex );//rayDirection[ splitAxis ] >= 0.0;\n\t\t\tuint c1 = leftToRight ? leftIndex : rightIndex;\n\t\t\tuint c2 = leftToRight ? rightIndex : leftIndex;\n\n\t\t\t// set c2 in the stack so we traverse it later. We need to keep track of a pointer in\n\t\t\t// the stack while we traverse. The second pointer added is the one that will be\n\t\t\t// traversed first\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c2;\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c1;\n\n\t\t}\n\n\t}\n\n\treturn sqrt( closestDistanceSquared );\n\n}\n`;\n", "import { Buffer<PERSON>ttribute, BufferGeometry, Vector3, Vector4, Matrix4, Matrix3 } from 'three';\n\nconst _positionVector = /*@__PURE__*/ new Vector3();\nconst _normalVector = /*@__PURE__*/ new Vector3();\nconst _tangentVector = /*@__PURE__*/ new Vector3();\nconst _tangentVector4 = /*@__PURE__*/ new Vector4();\n\nconst _morphVector = /*@__PURE__*/ new Vector3();\nconst _temp = /*@__PURE__*/ new Vector3();\n\nconst _skinIndex = /*@__PURE__*/ new Vector4();\nconst _skinWeight = /*@__PURE__*/ new Vector4();\nconst _matrix = /*@__PURE__*/ new Matrix4();\nconst _boneMatrix = /*@__PURE__*/ new Matrix4();\n\n// Confirms that the two provided attributes are compatible\nfunction validateAttributes( attr1, attr2 ) {\n\n\tif ( ! attr1 && ! attr2 ) {\n\n\t\treturn;\n\n\t}\n\n\tconst sameCount = attr1.count === attr2.count;\n\tconst sameNormalized = attr1.normalized === attr2.normalized;\n\tconst sameType = attr1.array.constructor === attr2.array.constructor;\n\tconst sameItemSize = attr1.itemSize === attr2.itemSize;\n\n\tif ( ! sameCount || ! sameNormalized || ! sameType || ! sameItemSize ) {\n\n\t\tthrow new Error();\n\n\t}\n\n}\n\n// Clones the given attribute with a new compatible buffer attribute but no data\nfunction createAttributeClone( attr, countOverride = null ) {\n\n\tconst cons = attr.array.constructor;\n\tconst normalized = attr.normalized;\n\tconst itemSize = attr.itemSize;\n\tconst count = countOverride === null ? attr.count : countOverride;\n\n\treturn new BufferAttribute( new cons( itemSize * count ), itemSize, normalized );\n\n}\n\n// target offset is the number of elements in the target buffer stride to skip before copying the\n// attributes contents in to.\nfunction copyAttributeContents( attr, target, targetOffset = 0 ) {\n\n\tif ( attr.isInterleavedBufferAttribute ) {\n\n\t\tconst itemSize = attr.itemSize;\n\t\tfor ( let i = 0, l = attr.count; i < l; i ++ ) {\n\n\t\t\tconst io = i + targetOffset;\n\t\t\ttarget.setX( io, attr.getX( i ) );\n\t\t\tif ( itemSize >= 2 ) target.setY( io, attr.getY( i ) );\n\t\t\tif ( itemSize >= 3 ) target.setZ( io, attr.getZ( i ) );\n\t\t\tif ( itemSize >= 4 ) target.setW( io, attr.getW( i ) );\n\n\t\t}\n\n\t} else {\n\n\t\tconst array = target.array;\n\t\tconst cons = array.constructor;\n\t\tconst byteOffset = array.BYTES_PER_ELEMENT * attr.itemSize * targetOffset;\n\t\tconst temp = new cons( array.buffer, byteOffset, attr.array.length );\n\t\ttemp.set( attr.array );\n\n\t}\n\n}\n\n// Adds the \"matrix\" multiplied by \"scale\" to \"target\"\nfunction addScaledMatrix( target, matrix, scale ) {\n\n\tconst targetArray = target.elements;\n\tconst matrixArray = matrix.elements;\n\tfor ( let i = 0, l = matrixArray.length; i < l; i ++ ) {\n\n\t\ttargetArray[ i ] += matrixArray[ i ] * scale;\n\n\t}\n\n}\n\n// A version of \"SkinnedMesh.boneTransform\" for normals\nfunction boneNormalTransform( mesh, index, target ) {\n\n\tconst skeleton = mesh.skeleton;\n\tconst geometry = mesh.geometry;\n\tconst bones = skeleton.bones;\n\tconst boneInverses = skeleton.boneInverses;\n\n\t_skinIndex.fromBufferAttribute( geometry.attributes.skinIndex, index );\n\t_skinWeight.fromBufferAttribute( geometry.attributes.skinWeight, index );\n\n\t_matrix.elements.fill( 0 );\n\n\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\tconst weight = _skinWeight.getComponent( i );\n\n\t\tif ( weight !== 0 ) {\n\n\t\t\tconst boneIndex = _skinIndex.getComponent( i );\n\t\t\t_boneMatrix.multiplyMatrices( bones[ boneIndex ].matrixWorld, boneInverses[ boneIndex ] );\n\n\t\t\taddScaledMatrix( _matrix, _boneMatrix, weight );\n\n\t\t}\n\n\t}\n\n\t_matrix.multiply( mesh.bindMatrix ).premultiply( mesh.bindMatrixInverse );\n\ttarget.transformDirection( _matrix );\n\n\treturn target;\n\n}\n\n// Applies the morph target data to the target vector\nfunction applyMorphTarget( morphData, morphInfluences, morphTargetsRelative, i, target ) {\n\n\t_morphVector.set( 0, 0, 0 );\n\tfor ( let j = 0, jl = morphData.length; j < jl; j ++ ) {\n\n\t\tconst influence = morphInfluences[ j ];\n\t\tconst morphAttribute = morphData[ j ];\n\n\t\tif ( influence === 0 ) continue;\n\n\t\t_temp.fromBufferAttribute( morphAttribute, i );\n\n\t\tif ( morphTargetsRelative ) {\n\n\t\t\t_morphVector.addScaledVector( _temp, influence );\n\n\t\t} else {\n\n\t\t\t_morphVector.addScaledVector( _temp.sub( target ), influence );\n\n\t\t}\n\n\t}\n\n\ttarget.add( _morphVector );\n\n}\n\n// Modified version of BufferGeometryUtils.mergeBufferGeometries that ignores morph targets and updates a attributes in place\nfunction mergeBufferGeometries( geometries, options = { useGroups: false, updateIndex: false, skipAttributes: [] }, targetGeometry = new BufferGeometry() ) {\n\n\tconst isIndexed = geometries[ 0 ].index !== null;\n\tconst { useGroups = false, updateIndex = false, skipAttributes = [] } = options;\n\n\tconst attributesUsed = new Set( Object.keys( geometries[ 0 ].attributes ) );\n\tconst attributes = {};\n\n\tlet offset = 0;\n\n\ttargetGeometry.clearGroups();\n\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\tconst geometry = geometries[ i ];\n\t\tlet attributesCount = 0;\n\n\t\t// ensure that all geometries are indexed, or none\n\t\tif ( isIndexed !== ( geometry.index !== null ) ) {\n\n\t\t\tthrow new Error( 'StaticGeometryGenerator: All geometries must have compatible attributes; make sure index attribute exists among all geometries, or in none of them.' );\n\n\t\t}\n\n\t\t// gather attributes, exit early if they're different\n\t\tfor ( const name in geometry.attributes ) {\n\n\t\t\tif ( ! attributesUsed.has( name ) ) {\n\n\t\t\t\tthrow new Error( 'StaticGeometryGenerator: All geometries must have compatible attributes; make sure \"' + name + '\" attribute exists among all geometries, or in none of them.' );\n\n\t\t\t}\n\n\t\t\tif ( attributes[ name ] === undefined ) {\n\n\t\t\t\tattributes[ name ] = [];\n\n\t\t\t}\n\n\t\t\tattributes[ name ].push( geometry.attributes[ name ] );\n\t\t\tattributesCount ++;\n\n\t\t}\n\n\t\t// ensure geometries have the same number of attributes\n\t\tif ( attributesCount !== attributesUsed.size ) {\n\n\t\t\tthrow new Error( 'StaticGeometryGenerator: Make sure all geometries have the same number of attributes.' );\n\n\t\t}\n\n\t\tif ( useGroups ) {\n\n\t\t\tlet count;\n\t\t\tif ( isIndexed ) {\n\n\t\t\t\tcount = geometry.index.count;\n\n\t\t\t} else if ( geometry.attributes.position !== undefined ) {\n\n\t\t\t\tcount = geometry.attributes.position.count;\n\n\t\t\t} else {\n\n\t\t\t\tthrow new Error( 'StaticGeometryGenerator: The geometry must have either an index or a position attribute' );\n\n\t\t\t}\n\n\t\t\ttargetGeometry.addGroup( offset, count, i );\n\t\t\toffset += count;\n\n\t\t}\n\n\t}\n\n\t// merge indices\n\tif ( isIndexed ) {\n\n\t\tlet forceUpdateIndex = false;\n\t\tif ( ! targetGeometry.index ) {\n\n\t\t\tlet indexCount = 0;\n\t\t\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\t\t\tindexCount += geometries[ i ].index.count;\n\n\t\t\t}\n\n\t\t\ttargetGeometry.setIndex( new BufferAttribute( new Uint32Array( indexCount ), 1, false ) );\n\t\t\tforceUpdateIndex = true;\n\n\t\t}\n\n\t\tif ( updateIndex || forceUpdateIndex ) {\n\n\t\t\tconst targetIndex = targetGeometry.index;\n\t\t\tlet targetOffset = 0;\n\t\t\tlet indexOffset = 0;\n\t\t\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\t\t\tconst geometry = geometries[ i ];\n\t\t\t\tconst index = geometry.index;\n\t\t\t\tif ( skipAttributes[ i ] !== true ) {\n\n\t\t\t\t\tfor ( let j = 0; j < index.count; ++ j ) {\n\n\t\t\t\t\t\ttargetIndex.setX( targetOffset, index.getX( j ) + indexOffset );\n\t\t\t\t\t\ttargetOffset ++;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tindexOffset += geometry.attributes.position.count;\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t// merge attributes\n\tfor ( const name in attributes ) {\n\n\t\tconst attrList = attributes[ name ];\n\t\tif ( ! ( name in targetGeometry.attributes ) ) {\n\n\t\t\tlet count = 0;\n\t\t\tfor ( const key in attrList ) {\n\n\t\t\t\tcount += attrList[ key ].count;\n\n\t\t\t}\n\n\t\t\ttargetGeometry.setAttribute( name, createAttributeClone( attributes[ name ][ 0 ], count ) );\n\n\t\t}\n\n\t\tconst targetAttribute = targetGeometry.attributes[ name ];\n\t\tlet offset = 0;\n\t\tfor ( let i = 0, l = attrList.length; i < l; i ++ ) {\n\n\t\t\tconst attr = attrList[ i ];\n\t\t\tif ( skipAttributes[ i ] !== true ) {\n\n\t\t\t\tcopyAttributeContents( attr, targetAttribute, offset );\n\n\t\t\t}\n\n\t\t\toffset += attr.count;\n\n\t\t}\n\n\t}\n\n\treturn targetGeometry;\n\n}\n\nfunction checkTypedArrayEquality( a, b ) {\n\n\tif ( a === null || b === null ) {\n\n\t\treturn a === b;\n\n\t}\n\n\tif ( a.length !== b.length ) {\n\n\t\treturn false;\n\n\t}\n\n\tfor ( let i = 0, l = a.length; i < l; i ++ ) {\n\n\t\tif ( a[ i ] !== b[ i ] ) {\n\n\t\t\treturn false;\n\n\t\t}\n\n\t}\n\n\treturn true;\n\n}\n\n// Checks whether the geometry changed between this and last evaluation\nclass GeometryDiff {\n\n\tconstructor( mesh ) {\n\n\t\tthis.matrixWorld = new Matrix4();\n\t\tthis.geometryHash = null;\n\t\tthis.boneMatrices = null;\n\t\tthis.primitiveCount = - 1;\n\t\tthis.mesh = mesh;\n\n\t\tthis.update();\n\n\t}\n\n\tupdate() {\n\n\t\tconst mesh = this.mesh;\n\t\tconst geometry = mesh.geometry;\n\t\tconst skeleton = mesh.skeleton;\n\t\tconst primitiveCount = ( geometry.index ? geometry.index.count : geometry.attributes.position.count ) / 3;\n\t\tthis.matrixWorld.copy( mesh.matrixWorld );\n\t\tthis.geometryHash = geometry.attributes.position.version;\n\t\tthis.primitiveCount = primitiveCount;\n\n\t\tif ( skeleton ) {\n\n\t\t\t// ensure the bone matrix array is updated to the appropriate length\n\t\t\tif ( ! skeleton.boneTexture ) {\n\n\t\t\t\tskeleton.computeBoneTexture();\n\n\t\t\t}\n\n\t\t\tskeleton.update();\n\n\t\t\t// copy data if possible otherwise clone it\n\t\t\tconst boneMatrices = skeleton.boneMatrices;\n\t\t\tif ( ! this.boneMatrices || this.boneMatrices.length !== boneMatrices.length ) {\n\n\t\t\t\tthis.boneMatrices = boneMatrices.slice();\n\n\t\t\t} else {\n\n\t\t\t\tthis.boneMatrices.set( boneMatrices );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tthis.boneMatrices = null;\n\n\t\t}\n\n\t}\n\n\tdidChange() {\n\n\t\tconst mesh = this.mesh;\n\t\tconst geometry = mesh.geometry;\n\t\tconst primitiveCount = ( geometry.index ? geometry.index.count : geometry.attributes.position.count ) / 3;\n\t\tconst identical =\n\t\t\tthis.matrixWorld.equals( mesh.matrixWorld ) &&\n\t\t\tthis.geometryHash === geometry.attributes.position.version &&\n\t\t\tcheckTypedArrayEquality( mesh.skeleton && mesh.skeleton.boneMatrices || null, this.boneMatrices ) &&\n\t\t\tthis.primitiveCount === primitiveCount;\n\n\t\treturn ! identical;\n\n\t}\n\n}\n\nexport class StaticGeometryGenerator {\n\n\tconstructor( meshes ) {\n\n\t\tif ( ! Array.isArray( meshes ) ) {\n\n\t\t\tmeshes = [ meshes ];\n\n\t\t}\n\n\t\tconst finalMeshes = [];\n\t\tmeshes.forEach( object => {\n\n\t\t\tobject.traverseVisible( c => {\n\n\t\t\t\tif ( c.isMesh ) {\n\n\t\t\t\t\tfinalMeshes.push( c );\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t} );\n\n\t\tthis.meshes = finalMeshes;\n\t\tthis.useGroups = true;\n\t\tthis.applyWorldTransforms = true;\n\t\tthis.attributes = [ 'position', 'normal', 'color', 'tangent', 'uv', 'uv2' ];\n\t\tthis._intermediateGeometry = new Array( finalMeshes.length ).fill().map( () => new BufferGeometry() );\n\t\tthis._diffMap = new WeakMap();\n\n\t}\n\n\tgetMaterials() {\n\n\t\tconst materials = [];\n\t\tthis.meshes.forEach( mesh => {\n\n\t\t\tif ( Array.isArray( mesh.material ) ) {\n\n\t\t\t\tmaterials.push( ...mesh.material );\n\n\t\t\t} else {\n\n\t\t\t\tmaterials.push( mesh.material );\n\n\t\t\t}\n\n\t\t} );\n\t\treturn materials;\n\n\t}\n\n\tgenerate( targetGeometry = new BufferGeometry() ) {\n\n\t\t// track which attributes have been updated and which to skip to avoid unnecessary attribute copies\n\t\tlet skipAttributes = [];\n\t\tconst { meshes, useGroups, _intermediateGeometry, _diffMap } = this;\n\t\tfor ( let i = 0, l = meshes.length; i < l; i ++ ) {\n\n\t\t\tconst mesh = meshes[ i ];\n\t\t\tconst geom = _intermediateGeometry[ i ];\n\t\t\tconst diff = _diffMap.get( mesh );\n\t\t\tif ( ! diff || diff.didChange( mesh ) ) {\n\n\t\t\t\tthis._convertToStaticGeometry( mesh, geom );\n\t\t\t\tskipAttributes.push( false );\n\n\t\t\t\tif ( ! diff ) {\n\n\t\t\t\t\t_diffMap.set( mesh, new GeometryDiff( mesh ) );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tdiff.update();\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tskipAttributes.push( true );\n\n\t\t\t}\n\n\t\t}\n\n\t\tmergeBufferGeometries( _intermediateGeometry, { useGroups, skipAttributes }, targetGeometry );\n\n\t\tfor ( const key in targetGeometry.attributes ) {\n\n\t\t\ttargetGeometry.attributes[ key ].needsUpdate = true;\n\n\t\t}\n\n\t\treturn targetGeometry;\n\n\t}\n\n\t_convertToStaticGeometry( mesh, targetGeometry = new BufferGeometry() ) {\n\n\t\tconst geometry = mesh.geometry;\n\t\tconst applyWorldTransforms = this.applyWorldTransforms;\n\t\tconst includeNormal = this.attributes.includes( 'normal' );\n\t\tconst includeTangent = this.attributes.includes( 'tangent' );\n\t\tconst attributes = geometry.attributes;\n\t\tconst targetAttributes = targetGeometry.attributes;\n\n\t\t// initialize the attributes if they don't exist\n\t\tif ( ! targetGeometry.index ) {\n\n\t\t\ttargetGeometry.index = geometry.index;\n\n\t\t}\n\n\t\tif ( ! targetAttributes.position ) {\n\n\t\t\ttargetGeometry.setAttribute( 'position', createAttributeClone( attributes.position ) );\n\n\t\t}\n\n\t\tif ( includeNormal && ! targetAttributes.normal && attributes.normal ) {\n\n\t\t\ttargetGeometry.setAttribute( 'normal', createAttributeClone( attributes.normal ) );\n\n\t\t}\n\n\t\tif ( includeTangent && ! targetAttributes.tangent && attributes.tangent ) {\n\n\t\t\ttargetGeometry.setAttribute( 'tangent', createAttributeClone( attributes.tangent ) );\n\n\t\t}\n\n\t\t// ensure the attributes are consistent\n\t\tvalidateAttributes( geometry.index, targetGeometry.index );\n\t\tvalidateAttributes( attributes.position, targetAttributes.position );\n\n\t\tif ( includeNormal ) {\n\n\t\t\tvalidateAttributes( attributes.normal, targetAttributes.normal );\n\n\t\t}\n\n\t\tif ( includeTangent ) {\n\n\t\t\tvalidateAttributes( attributes.tangent, targetAttributes.tangent );\n\n\t\t}\n\n\t\t// generate transformed vertex attribute data\n\t\tconst position = attributes.position;\n\t\tconst normal = includeNormal ? attributes.normal : null;\n\t\tconst tangent = includeTangent ? attributes.tangent : null;\n\t\tconst morphPosition = geometry.morphAttributes.position;\n\t\tconst morphNormal = geometry.morphAttributes.normal;\n\t\tconst morphTangent = geometry.morphAttributes.tangent;\n\t\tconst morphTargetsRelative = geometry.morphTargetsRelative;\n\t\tconst morphInfluences = mesh.morphTargetInfluences;\n\t\tconst normalMatrix = new Matrix3();\n\t\tnormalMatrix.getNormalMatrix( mesh.matrixWorld );\n\n\t\tfor ( let i = 0, l = attributes.position.count; i < l; i ++ ) {\n\n\t\t\t_positionVector.fromBufferAttribute( position, i );\n\t\t\tif ( normal ) {\n\n\t\t\t\t_normalVector.fromBufferAttribute( normal, i );\n\n\t\t\t}\n\n\t\t\tif ( tangent ) {\n\n\t\t\t\t_tangentVector4.fromBufferAttribute( tangent, i );\n\t\t\t\t_tangentVector.fromBufferAttribute( tangent, i );\n\n\t\t\t}\n\n\t\t\t// apply morph target transform\n\t\t\tif ( morphInfluences ) {\n\n\t\t\t\tif ( morphPosition ) {\n\n\t\t\t\t\tapplyMorphTarget( morphPosition, morphInfluences, morphTargetsRelative, i, _positionVector );\n\n\t\t\t\t}\n\n\t\t\t\tif ( morphNormal ) {\n\n\t\t\t\t\tapplyMorphTarget( morphNormal, morphInfluences, morphTargetsRelative, i, _normalVector );\n\n\t\t\t\t}\n\n\t\t\t\tif ( morphTangent ) {\n\n\t\t\t\t\tapplyMorphTarget( morphTangent, morphInfluences, morphTargetsRelative, i, _tangentVector );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// apply bone transform\n\t\t\tif ( mesh.isSkinnedMesh ) {\n\n\t\t\t\tmesh.boneTransform( i, _positionVector );\n\t\t\t\tif ( normal ) {\n\n\t\t\t\t\tboneNormalTransform( mesh, i, _normalVector );\n\n\t\t\t\t}\n\n\t\t\t\tif ( tangent ) {\n\n\t\t\t\t\tboneNormalTransform( mesh, i, _tangentVector );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// update the vectors of the attributes\n\t\t\tif ( applyWorldTransforms ) {\n\n\t\t\t\t_positionVector.applyMatrix4( mesh.matrixWorld );\n\n\t\t\t}\n\n\t\t\ttargetAttributes.position.setXYZ( i, _positionVector.x, _positionVector.y, _positionVector.z );\n\n\t\t\tif ( normal ) {\n\n\t\t\t\tif ( applyWorldTransforms ) {\n\n\t\t\t\t\t_normalVector.applyNormalMatrix( normalMatrix );\n\n\t\t\t\t}\n\n\t\t\t\ttargetAttributes.normal.setXYZ( i, _normalVector.x, _normalVector.y, _normalVector.z );\n\n\t\t\t}\n\n\t\t\tif ( tangent ) {\n\n\t\t\t\tif ( applyWorldTransforms ) {\n\n\t\t\t\t\t_tangentVector.transformDirection( mesh.matrixWorld );\n\n\t\t\t\t}\n\n\t\t\t\ttargetAttributes.tangent.setXYZW( i, _tangentVector.x, _tangentVector.y, _tangentVector.z, _tangentVector4.w );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// copy other attributes over\n\t\tfor ( const i in this.attributes ) {\n\n\t\t\tconst key = this.attributes[ i ];\n\t\t\tif ( key === 'position' || key === 'tangent' || key === 'normal' || ! ( key in attributes ) ) {\n\n\t\t\t\tcontinue;\n\n\t\t\t}\n\n\t\t\tif ( ! targetAttributes[ key ] ) {\n\n\t\t\t\ttargetGeometry.setAttribute( key, createAttributeClone( attributes[ key ] ) );\n\n\t\t\t}\n\n\t\t\tvalidateAttributes( attributes[ key ], targetAttributes[ key ] );\n\t\t\tcopyAttributeContents( attributes[ key ], targetAttributes[ key ] );\n\n\t\t}\n\n\t\treturn targetGeometry;\n\n\t}\n\n}\n"], "names": ["BufferAttribute", "Vector3", "Vector2", "Plane", "Line3", "Triangle", "Sphere", "Matrix4", "BackSide", "DoubleSide", "boundingBox", "Box3", "FrontSide", "Object3D", "BufferGeometry", "Group", "LineBasicMaterial", "MeshBasicMaterial", "<PERSON>", "<PERSON><PERSON>", "RedFormat", "RGFormat", "RGBAFormat", "RedIntegerFormat", "RGIntegerFormat", "RGBAIntegerFormat", "DataTexture", "NearestFilter", "FloatType", "UnsignedIntType", "IntType", "UnsignedByteType", "ByteType", "ShortType", "UnsignedShortType", "Vector4", "Matrix3"], "mappings": ";;;;;;CAAA;AACY,OAAC,MAAM,GAAG,EAAE;AACZ,OAAC,OAAO,GAAG,EAAE;AACb,OAAC,GAAG,GAAG,EAAE;AACrB;CACA;AACY,OAAC,eAAe,GAAG,EAAE;AACrB,OAAC,WAAW,GAAG,EAAE;AACjB,OAAC,SAAS,GAAG,EAAE;AAC3B;CACA;CACA;CACA;CACA;CACO,MAAM,uBAAuB,GAAG,IAAI,CAAC;CACrC,MAAM,cAAc,GAAG,CAAC,CAAC;AAChC;AACA;CACA;CACO,MAAM,cAAc,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CACrC,MAAM,gBAAgB,GAAG,MAAM,CAAC;AACvC;CACA;CACA;CACO,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE;;CCxB3C,MAAM,WAAW,CAAC;AACzB;CACA,CAAC,WAAW,GAAG;AACf;CACA;CACA;AACA;CACA,EAAE;AACF;CACA;;CCTO,SAAS,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,GAAG;AACzD;CACA,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,EAAE,CAAC;CACrC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;CACzC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AACzC;CACA,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;CACzC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;CACzC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AACzC;CACA,CAAC,OAAO,MAAM,CAAC;AACf;CACA,CAAC;AACD;CACO,SAAS,mBAAmB,EAAE,MAAM,GAAG;AAC9C;CACA,CAAC,IAAI,WAAW,GAAG,EAAE,CAAC,CAAC;CACvB,CAAC,IAAI,SAAS,GAAG,EAAE,QAAQ,CAAC;AAC5B;CACA,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChC;CACA,EAAE,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CAC7C,EAAE,KAAK,IAAI,GAAG,SAAS,GAAG;AAC1B;CACA,GAAG,SAAS,GAAG,IAAI,CAAC;CACpB,GAAG,WAAW,GAAG,CAAC,CAAC;AACnB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,WAAW,CAAC;AACpB;CACA,CAAC;AACD;CACA;CACO,SAAS,UAAU,EAAE,MAAM,EAAE,MAAM,GAAG;AAC7C;CACA,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC;AACtB;CACA,CAAC;AACD;CACA;CACO,SAAS,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG;AAC5C;CACA,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC;CAChB,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChC;CACA,EAAE,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AACnB;CACA;CACA,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;CAChB,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;CAChB,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAC1C;CACA;CACA,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;CACjB,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;CACjB,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAC3C;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA;CACO,SAAS,sBAAsB,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,GAAG;AAC7E;CACA,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChC;CACA,EAAE,MAAM,OAAO,GAAG,cAAc,EAAE,UAAU,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACvD,EAAE,MAAM,KAAK,GAAG,cAAc,EAAE,UAAU,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACzD;CACA,EAAE,MAAM,IAAI,GAAG,OAAO,GAAG,KAAK,CAAC;CAC/B,EAAE,MAAM,IAAI,GAAG,OAAO,GAAG,KAAK,CAAC;AAC/B;CACA,EAAE,KAAK,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG;AAC5B;CACA,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AACtB;CACA,GAAG;AACH;CACA,EAAE,KAAK,IAAI,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG;AAChC;CACA,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC1B;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA;CACO,SAAS,kBAAkB,EAAE,MAAM,GAAG;AAC7C;CACA,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CACtC,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CACtC,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;AACtC;CACA,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC5C;CACA;;CC5FA,SAAS,WAAW,EAAE,GAAG,EAAE,OAAO,GAAG;AACrC;CACA,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG;AACpB;CACA,EAAE,MAAM,WAAW,GAAG,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;CACpD,EAAE,MAAM,iBAAiB,GAAG,OAAO,CAAC,oBAAoB,GAAG,iBAAiB,GAAG,WAAW,CAAC;CAC3F,EAAE,IAAI,KAAK,CAAC;CACZ,EAAE,KAAK,WAAW,GAAG,KAAK,GAAG;AAC7B;CACA,GAAG,KAAK,GAAG,IAAI,WAAW,EAAE,IAAI,iBAAiB,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC;AACvE;CACA,GAAG,MAAM;AACT;CACA,GAAG,KAAK,GAAG,IAAI,WAAW,EAAE,IAAI,iBAAiB,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC;AACvE;CACA,GAAG;AACH;CACA,EAAE,GAAG,CAAC,QAAQ,EAAE,IAAIA,qBAAe,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;AAClD;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,GAAG;AAC3C;CACA,GAAG,KAAK,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AAClB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,SAAS,kBAAkB,EAAE,GAAG,GAAG;AACnC;CACA,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG;AAC5C;CACA,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,EAAE,CAAC;AACvD;CACA,EAAE;AACF;CACA,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC;CACnB,CAAC,MAAM,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;CACnC,CAAC,MAAM,MAAM,KAAK,IAAI,GAAG,CAAC,MAAM,GAAG;AACnC;CACA,EAAE,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;CACrC,EAAE,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AACnD;CACA,EAAE;AACF;CACA;CACA,CAAC,MAAM,gBAAgB,GAAG,KAAK,CAAC,IAAI,EAAE,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;CAC3F,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC1D;CACA,EAAE,MAAM,KAAK,GAAG,gBAAgB,EAAE,CAAC,EAAE,EAAE,GAAG,GAAG,gBAAgB,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACvE,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,KAAK,GAAG,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,GAAG,GAAG,KAAK,KAAK,CAAC,EAAE,EAAE,CAAC;AACvE;CACA,EAAE;AACF;CACA,CAAC,OAAO,MAAM,CAAC;AACf;CACA,CAAC;AACD;CACA;CACA;CACA;CACA,SAAS,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,cAAc,GAAG,IAAI,GAAG;AACnF;CACA,CAAC,IAAI,IAAI,GAAG,QAAQ,CAAC;CACrB,CAAC,IAAI,IAAI,GAAG,QAAQ,CAAC;CACrB,CAAC,IAAI,IAAI,GAAG,QAAQ,CAAC;CACrB,CAAC,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;CACvB,CAAC,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;CACvB,CAAC,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;AACvB;CACA,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC;CACtB,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC;CACtB,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC;CACtB,CAAC,IAAI,KAAK,GAAG,EAAE,QAAQ,CAAC;CACxB,CAAC,IAAI,KAAK,GAAG,EAAE,QAAQ,CAAC;CACxB,CAAC,IAAI,KAAK,GAAG,EAAE,QAAQ,CAAC;AACxB;CACA,CAAC,MAAM,eAAe,GAAG,cAAc,KAAK,IAAI,CAAC;CACjD,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,GAAG,GAAG,EAAE,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG;AAC3E;CACA,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACrC,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACrC,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACrB,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACrB,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;CAC7B,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;CAC7B,EAAE,KAAK,eAAe,IAAI,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;CAClD,EAAE,KAAK,eAAe,IAAI,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAClD;CACA,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACrC,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACrC,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACrB,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACrB,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;CAC7B,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;CAC7B,EAAE,KAAK,eAAe,IAAI,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;CAClD,EAAE,KAAK,eAAe,IAAI,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAClD;CACA,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACrC,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACrC,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACrB,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACrB,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;CAC7B,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;CAC7B,EAAE,KAAK,eAAe,IAAI,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;CAClD,EAAE,KAAK,eAAe,IAAI,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAClD;CACA,EAAE;AACF;CACA,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;CACpB,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;CACpB,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AACpB;CACA,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;CACpB,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;CACpB,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AACpB;CACA,CAAC,KAAK,eAAe,GAAG;AACxB;CACA,EAAE,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;CAC9B,EAAE,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;CAC9B,EAAE,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC9B;CACA,EAAE,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;CAC9B,EAAE,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;CAC9B,EAAE,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC9B;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA;CACA,SAAS,iBAAiB,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,cAAc,GAAG;AAC5E;CACA,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC;CACtB,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC;CACtB,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC;CACtB,CAAC,IAAI,KAAK,GAAG,EAAE,QAAQ,CAAC;CACxB,CAAC,IAAI,KAAK,GAAG,EAAE,QAAQ,CAAC;CACxB,CAAC,IAAI,KAAK,GAAG,EAAE,QAAQ,CAAC;AACxB;CACA,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,GAAG,GAAG,EAAE,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG;AAC3E;CACA,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACrC,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;CAC/B,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAC/B;CACA,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACrC,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;CAC/B,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAC/B;CACA,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACrC,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;CAC/B,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAC/B;CACA,EAAE;AACF;CACA,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;CAC7B,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;CAC7B,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC7B;CACA,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;CAC7B,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;CAC7B,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC7B;CACA,CAAC;AACD;AACA;CACA;CACA;CACA;CACA,SAAS,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG;AAClE;CACA,CAAC,IAAI,IAAI,GAAG,MAAM,CAAC;CACnB,CAAC,IAAI,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;CAChC,CAAC,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;CACvB,CAAC,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;AACnC;CACA;CACA,CAAC,QAAQ,IAAI,GAAG;AAChB;CACA,EAAE,QAAQ,IAAI,IAAI,KAAK,IAAI,cAAc,EAAE,IAAI,GAAG,CAAC,GAAG,UAAU,EAAE,GAAG,GAAG,GAAG;AAC3E;CACA,GAAG,IAAI,GAAG,CAAC;AACX;CACA,GAAG;AACH;AACA;CACA;CACA,EAAE,QAAQ,IAAI,IAAI,KAAK,IAAI,cAAc,EAAE,KAAK,GAAG,CAAC,GAAG,UAAU,EAAE,IAAI,GAAG,GAAG;AAC7E;CACA,GAAG,KAAK,GAAG,CAAC;AACZ;CACA,GAAG;AACH;CACA,EAAE,KAAK,IAAI,GAAG,KAAK,GAAG;AACtB;CACA;CACA;CACA;AACA;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,IAAI,IAAI,EAAE,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACnC,IAAI,KAAK,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,KAAK,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACnD,IAAI,KAAK,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;AAChC;CACA,IAAI,IAAI,EAAE,GAAG,cAAc,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACpD,IAAI,cAAc,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,cAAc,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACrF,IAAI,cAAc,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;AACjD;CACA,IAAI,IAAI,EAAE,GAAG,cAAc,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACpD,IAAI,cAAc,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,cAAc,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACrF,IAAI,cAAc,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;AACjD;CACA,IAAI;AACJ;CACA,GAAG,IAAI,GAAG,CAAC;CACX,GAAG,KAAK,GAAG,CAAC;AACZ;CACA,GAAG,MAAM;AACT;CACA,GAAG,OAAO,IAAI,CAAC;AACf;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,MAAM,SAAS,GAAG,EAAE,CAAC;CACrB,MAAM,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;CACvD,MAAM,OAAO,GAAG,IAAI,KAAK,EAAE,SAAS,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM;AACzD;CACA,CAAC,OAAO;AACR;CACA,EAAE,KAAK,EAAE,CAAC;CACV,EAAE,MAAM,EAAE,IAAI,YAAY,EAAE,CAAC,EAAE;CAC/B,EAAE,gBAAgB,EAAE,IAAI,YAAY,EAAE,CAAC,EAAE;CACzC,EAAE,eAAe,EAAE,IAAI,YAAY,EAAE,CAAC,EAAE;CACxC,EAAE,SAAS,EAAE,CAAC;AACd;CACA,EAAE,CAAC;AACH;CACA,CAAC,EAAE,CAAC;CACJ,MAAM,UAAU,GAAG,IAAI,YAAY,EAAE,CAAC,EAAE,CAAC;AACzC;CACA,SAAS,eAAe,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,GAAG;AAC5G;CACA,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;CAChB,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;AACb;CACA;CACA,CAAC,KAAK,QAAQ,KAAK,MAAM,GAAG;AAC5B;CACA,EAAE,IAAI,GAAG,mBAAmB,EAAE,oBAAoB,EAAE,CAAC;CACrD,EAAE,KAAK,IAAI,KAAK,EAAE,CAAC,GAAG;AACtB;CACA,GAAG,GAAG,GAAG,EAAE,oBAAoB,EAAE,IAAI,EAAE,GAAG,oBAAoB,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;AACjF;CACA,GAAG;AACH;CACA,EAAE,MAAM,KAAK,QAAQ,KAAK,OAAO,GAAG;AACpC;CACA,EAAE,IAAI,GAAG,mBAAmB,EAAE,gBAAgB,EAAE,CAAC;CACjD,EAAE,KAAK,IAAI,KAAK,EAAE,CAAC,GAAG;AACtB;CACA,GAAG,GAAG,GAAG,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;AAC3D;CACA,GAAG;AACH;CACA,EAAE,MAAM,KAAK,QAAQ,KAAK,GAAG,GAAG;AAChC;CACA,EAAE,MAAM,eAAe,GAAG,kBAAkB,EAAE,gBAAgB,EAAE,CAAC;CACjE,EAAE,IAAI,QAAQ,GAAG,uBAAuB,GAAG,KAAK,CAAC;AACjD;CACA;CACA,EAAE,MAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC;CAC5B,EAAE,MAAM,IAAI,GAAG,EAAE,MAAM,GAAG,KAAK,KAAK,CAAC,CAAC;CACtC,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,QAAQ,GAAG,oBAAoB,EAAE,CAAC,EAAE,CAAC;CAC9C,GAAG,MAAM,SAAS,GAAG,oBAAoB,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACnD,GAAG,MAAM,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAC;CAC3C,GAAG,MAAM,QAAQ,GAAG,UAAU,GAAG,SAAS,CAAC;AAC3C;CACA;CACA;CACA,GAAG,KAAK,KAAK,GAAG,SAAS,GAAG,CAAC,GAAG;AAChC;CACA;CACA,IAAI,MAAM,aAAa,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;CACzC,IAAI,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC;AACjC;CACA;CACA,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;CACd,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;CACA,KAAK,MAAM,GAAG,GAAG,aAAa,EAAE,CAAC,EAAE,CAAC;CACpC,KAAK,GAAG,CAAC,SAAS,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACjD,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;AACnB;CACA,KAAK,MAAM;CACX,MAAM,MAAM;CACZ,MAAM,eAAe;CACrB,MAAM,gBAAgB;CACtB,MAAM,GAAG,GAAG,CAAC;CACb,KAAK,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACpC;CACA,MAAM,gBAAgB,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC;CACvC,MAAM,gBAAgB,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC;AAC7C;CACA,MAAM,eAAe,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC;CACtC,MAAM,eAAe,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC;AAC5C;CACA,MAAM,MAAM,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC;CAC7B,MAAM,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC;AACnC;CACA,MAAM;AACN;CACA,KAAK,sBAAsB,EAAE,CAAC,EAAE,cAAc,EAAE,MAAM,EAAE,CAAC;AACzD;CACA,KAAK;AACL;CACA,IAAI,aAAa,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC;AACnC;CACA;CACA,IAAI,IAAI,UAAU,GAAG,KAAK,CAAC;CAC3B,IAAI,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,EAAE,EAAE,GAAG,GAAG;AAC/C;CACA,KAAK,MAAM,GAAG,GAAG,aAAa,EAAE,EAAE,EAAE,CAAC;CACrC,KAAK,QAAQ,EAAE,GAAG,CAAC,GAAG,UAAU,IAAI,aAAa,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,SAAS,KAAK,GAAG,CAAC,SAAS,GAAG;AAC1F;CACA,MAAM,aAAa,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;CACxC,MAAM,UAAU,GAAG,CAAC;AACpB;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA;CACA,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG;AAC7C;CACA,KAAK,MAAM,MAAM,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CAChD,KAAK,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,EAAE,EAAE,GAAG,GAAG;AAChD;CACA,MAAM,MAAM,GAAG,GAAG,aAAa,EAAE,EAAE,EAAE,CAAC;CACtC,MAAM,KAAK,MAAM,IAAI,GAAG,CAAC,SAAS,GAAG;AACrC;CACA,OAAO,sBAAsB,EAAE,CAAC,EAAE,cAAc,EAAE,GAAG,CAAC,gBAAgB,EAAE,CAAC;AACzE;CACA,OAAO,MAAM;AACb;CACA,OAAO,sBAAsB,EAAE,CAAC,EAAE,cAAc,EAAE,GAAG,CAAC,eAAe,EAAE,CAAC;CACxE,OAAO,GAAG,CAAC,KAAK,GAAG,CAAC;AACpB;CACA,OAAO;AACP;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA;CACA,IAAI,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,EAAE,EAAE,GAAG,GAAG;AAC/C;CACA,KAAK,MAAM,GAAG,GAAG,aAAa,EAAE,EAAE,EAAE,CAAC;CACrC,KAAK,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC;CACjC,KAAK,MAAM,UAAU,GAAG,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;AAC1C;CACA;CACA,KAAK,MAAM,UAAU,GAAG,GAAG,CAAC,eAAe,CAAC;CAC5C,KAAK,MAAM,WAAW,GAAG,GAAG,CAAC,gBAAgB,CAAC;AAC9C;CACA,KAAK,IAAI,QAAQ,GAAG,CAAC,CAAC;CACtB,KAAK,KAAK,SAAS,KAAK,CAAC,GAAG;AAC5B;CACA,MAAM,QAAQ,GAAG,kBAAkB,EAAE,UAAU,EAAE,GAAG,eAAe,CAAC;AACpE;CACA,MAAM;AACN;CACA,KAAK,IAAI,SAAS,GAAG,CAAC,CAAC;CACvB,KAAK,KAAK,UAAU,KAAK,CAAC,GAAG;AAC7B;CACA,MAAM,SAAS,GAAG,kBAAkB,EAAE,WAAW,EAAE,GAAG,eAAe,CAAC;AACtE;CACA,MAAM;AACN;CACA,KAAK,MAAM,IAAI,GAAG,cAAc,GAAG,uBAAuB;CAC1D,MAAM,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,UAAU;CACnD,MAAM,CAAC;AACP;CACA,KAAK,KAAK,IAAI,GAAG,QAAQ,GAAG;AAC5B;CACA,MAAM,IAAI,GAAG,CAAC,CAAC;CACf,MAAM,QAAQ,GAAG,IAAI,CAAC;CACtB,MAAM,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC;AAC1B;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA,IAAI,MAAM;AACV;CACA;CACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,GAAG,GAAG;AAC3C;CACA,KAAK,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC9B,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;CACnB,KAAK,GAAG,CAAC,SAAS,GAAG,QAAQ,GAAG,QAAQ,GAAG,CAAC,GAAG,QAAQ,CAAC;AACxD;CACA,KAAK,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;CAC/B,KAAK,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACpC;CACA,MAAM,MAAM,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC;CAC7B,MAAM,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC;AACnC;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA;CACA,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG;AAC7C;CACA,KAAK,MAAM,SAAS,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACnD,KAAK,MAAM,cAAc,GAAG,SAAS,GAAG,QAAQ,CAAC;AACjD;CACA;CACA;CACA,KAAK,IAAI,QAAQ,GAAG,EAAE,IAAI,cAAc,GAAG,QAAQ,EAAE,CAAC;CACtD,KAAK,KAAK,QAAQ,IAAI,SAAS,GAAG,QAAQ,GAAG,SAAS,GAAG,CAAC,CAAC;AAC3D;CACA,KAAK,MAAM,GAAG,GAAG,OAAO,EAAE,QAAQ,EAAE,CAAC;CACrC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC;AAClB;CACA,KAAK,sBAAsB,EAAE,CAAC,EAAE,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC;AAC7D;CACA,KAAK;AACL;CACA;CACA,IAAI,MAAM,OAAO,GAAG,OAAO,EAAE,SAAS,GAAG,CAAC,EAAE,CAAC;CAC7C,IAAI,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,gBAAgB,EAAE,CAAC;CAC3D,IAAI,MAAM,IAAI,CAAC,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AAChD;CACA,KAAK,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC9B,KAAK,MAAM,OAAO,GAAG,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACtC,KAAK,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,gBAAgB,EAAE,GAAG,CAAC,gBAAgB,EAAE,CAAC;AAC/E;CACA,KAAK;AACL;CACA,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC;CACtB,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC/C;CACA,KAAK,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC9B,KAAK,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC;CAChC,KAAK,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;AAC/B;CACA,KAAK,MAAM,OAAO,GAAG,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACtC,KAAK,MAAM,WAAW,GAAG,OAAO,CAAC,gBAAgB,CAAC;AAClD;CACA;CACA,KAAK,KAAK,QAAQ,KAAK,CAAC,GAAG;AAC3B;CACA,MAAM,KAAK,SAAS,KAAK,CAAC,GAAG;AAC7B;CACA,OAAO,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;AACxC;CACA,OAAO,MAAM;AACb;CACA,OAAO,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;AACrD;CACA,OAAO;AACP;CACA,MAAM;AACN;CACA,KAAK,SAAS,IAAI,QAAQ,CAAC;AAC3B;CACA;CACA,KAAK,IAAI,QAAQ,GAAG,CAAC,CAAC;CACtB,KAAK,IAAI,SAAS,GAAG,CAAC,CAAC;AACvB;CACA,KAAK,KAAK,SAAS,KAAK,CAAC,GAAG;AAC5B;CACA,MAAM,QAAQ,GAAG,kBAAkB,EAAE,UAAU,EAAE,GAAG,eAAe,CAAC;AACpE;CACA,MAAM;AACN;CACA,KAAK,MAAM,UAAU,GAAG,KAAK,GAAG,SAAS,CAAC;CAC1C,KAAK,KAAK,UAAU,KAAK,CAAC,GAAG;AAC7B;CACA,MAAM,SAAS,GAAG,kBAAkB,EAAE,WAAW,EAAE,GAAG,eAAe,CAAC;AACtE;CACA,MAAM;AACN;CACA,KAAK,MAAM,IAAI,GAAG,cAAc,GAAG,uBAAuB;CAC1D,MAAM,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,UAAU;CACnD,MAAM,CAAC;AACP;CACA,KAAK,KAAK,IAAI,GAAG,QAAQ,GAAG;AAC5B;CACA,MAAM,IAAI,GAAG,CAAC,CAAC;CACf,MAAM,QAAQ,GAAG,IAAI,CAAC;CACtB,MAAM,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC;AAC1B;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,MAAM;AACR;CACA,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,sCAAsC,GAAG,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC;AAC9E;CACA,EAAE;AACF;CACA,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AACtB;CACA,CAAC;AACD;CACA;CACA,SAAS,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,GAAG;AAC3D;CACA,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;CACb,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG;AAC7D;CACA,EAAE,GAAG,IAAI,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC;AAC5C;CACA,EAAE;AACF;CACA,CAAC,OAAO,GAAG,GAAG,KAAK,CAAC;AACpB;CACA,CAAC;AACD;CACA;CACA;CACA;CACA;CACA,SAAS,qBAAqB,EAAE,GAAG,EAAE,UAAU,GAAG;AAClD;CACA,CAAC,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC;CACzC,CAAC,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;CAC/B,CAAC,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;CACnC,CAAC,MAAM,cAAc,GAAG,IAAI,YAAY,EAAE,QAAQ,GAAG,CAAC,EAAE,CAAC;CACzD,CAAC,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;AACvC;CACA;CACA,CAAC,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;AAC9B;CACA;CACA,CAAC,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;CAC1C,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;CAChB,CAAC,KAAK,OAAO,CAAC,4BAA4B,GAAG;AAC7C;CACA,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/B;CACA,EAAE;AACF;CACA;CACA,CAAC,MAAM,OAAO,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC5C;CACA,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ,EAAE,GAAG,GAAG,GAAG;AAC7C;CACA,EAAE,MAAM,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;CACvB,EAAE,MAAM,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;AACvB;CACA,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACjB;CACA,EAAE,KAAK,UAAU,GAAG;AACpB;CACA,GAAG,EAAE,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC;CAC1B,GAAG,EAAE,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC;CAC1B,GAAG,EAAE,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC;AAC1B;CACA,GAAG,MAAM;AACT;CACA,GAAG,EAAE,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,EAAE,GAAG,MAAM,GAAG,YAAY,CAAC;CAClD,GAAG,EAAE,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,EAAE,GAAG,MAAM,GAAG,YAAY,CAAC;CAClD,GAAG,EAAE,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,EAAE,GAAG,MAAM,GAAG,YAAY,CAAC;AAClD;CACA,GAAG;AACH;CACA,EAAE,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG;AACpC;CACA,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACf;CACA,GAAG,KAAK,UAAU,GAAG;AACrB;CACA,IAAI,CAAC,GAAG,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;CACvC,IAAI,CAAC,GAAG,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;CACvC,IAAI,CAAC,GAAG,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACvC;CACA,IAAI,MAAM;AACV;CACA,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;CAC1B,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;CAC1B,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC1B;CACA,IAAI;AACJ;CACA,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;CACf,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;CAC1B,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAC1B;CACA,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;CACf,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;CAC1B,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAC1B;CACA;CACA;CACA;CACA,GAAG,MAAM,WAAW,GAAG,EAAE,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;CACzC,GAAG,MAAM,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;CACtB,GAAG,cAAc,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,WAAW,CAAC;CACxD,GAAG,cAAc,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,WAAW,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,WAAW,KAAK,eAAe,CAAC;AACxG;CACA,GAAG,KAAK,GAAG,GAAG,UAAU,EAAE,EAAE,EAAE,GAAG,UAAU,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC;CACxD,GAAG,KAAK,GAAG,GAAG,UAAU,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,UAAU,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC;AAChE;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,cAAc,CAAC;AACvB;CACA,CAAC;AACD;CACO,SAAS,SAAS,EAAE,GAAG,EAAE,OAAO,GAAG;AAC1C;CACA,CAAC,SAAS,eAAe,EAAE,kBAAkB,GAAG;AAChD;CACA,EAAE,KAAK,UAAU,GAAG;AACpB;CACA,GAAG,UAAU,EAAE,kBAAkB,GAAG,cAAc,EAAE,CAAC;AACrD;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;CACA;CACA,CAAC,SAAS,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,oBAAoB,GAAG,IAAI,EAAE,KAAK,GAAG,CAAC,GAAG;AACnF;CACA,EAAE,KAAK,EAAE,eAAe,IAAI,KAAK,IAAI,QAAQ,GAAG;AAChD;CACA,GAAG,eAAe,GAAG,IAAI,CAAC;CAC1B,GAAG,KAAK,OAAO,GAAG;AAClB;CACA,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,sBAAsB,GAAG,QAAQ,EAAE,2DAA2D,CAAC,EAAE,CAAC;CACrH,IAAI,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AACxB;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA,EAAE,KAAK,KAAK,IAAI,WAAW,IAAI,KAAK,IAAI,QAAQ,GAAG;AACnD;CACA,GAAG,eAAe,EAAE,MAAM,GAAG,KAAK,EAAE,CAAC;CACrC,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;CACxB,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACtB,GAAG,OAAO,IAAI,CAAC;AACf;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,KAAK,GAAG,eAAe,EAAE,IAAI,CAAC,YAAY,EAAE,oBAAoB,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;CACpH,EAAE,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE,CAAC,GAAG;AAC5B;CACA,GAAG,eAAe,EAAE,MAAM,GAAG,KAAK,EAAE,CAAC;CACrC,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;CACxB,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACtB,GAAG,OAAO,IAAI,CAAC;AACf;CACA,GAAG;AACH;CACA,EAAE,MAAM,WAAW,GAAG,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AACpF;CACA;CACA,EAAE,KAAK,WAAW,KAAK,MAAM,IAAI,WAAW,KAAK,MAAM,GAAG,KAAK,GAAG;AAClE;CACA,GAAG,eAAe,EAAE,MAAM,GAAG,KAAK,EAAE,CAAC;CACrC,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;CACxB,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACtB;CACA,GAAG,MAAM;AACT;CACA,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;AAC/B;CACA;CACA,GAAG,MAAM,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC;CAClC,GAAG,MAAM,MAAM,GAAG,MAAM,CAAC;CACzB,GAAG,MAAM,MAAM,GAAG,WAAW,GAAG,MAAM,CAAC;CACvC,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;CACpB,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC,EAAE,CAAC;AAC7C;CACA,GAAG,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,yBAAyB,EAAE,CAAC;CAC7F,GAAG,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,yBAAyB,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;AAC3E;CACA;CACA,GAAG,MAAM,KAAK,GAAG,IAAI,WAAW,EAAE,CAAC;CACnC,GAAG,MAAM,MAAM,GAAG,WAAW,CAAC;CAC9B,GAAG,MAAM,MAAM,GAAG,KAAK,GAAG,MAAM,CAAC;CACjC,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACtB,GAAG,KAAK,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC,EAAE,CAAC;AAC9C;CACA,GAAG,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,YAAY,EAAE,yBAAyB,EAAE,CAAC;CAC9F,GAAG,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,yBAAyB,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;AAC5E;CACA,GAAG;AACH;CACA,EAAE,OAAO,IAAI,CAAC;AACd;CACA,EAAE;AACF;CACA,CAAC,WAAW,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;AAC7B;CACA;CACA;CACA;CACA,CAAC,MAAM,UAAU,GAAG,IAAI,YAAY,EAAE,CAAC,EAAE,CAAC;CAC1C,CAAC,MAAM,yBAAyB,GAAG,IAAI,YAAY,EAAE,CAAC,EAAE,CAAC;CACzD,CAAC,MAAM,cAAc,GAAG,qBAAqB,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;CACjE,CAAC,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;CACpC,CAAC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;CACnC,CAAC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;CACjC,CAAC,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;CACzC,CAAC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;CACnC,CAAC,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;CACvC,CAAC,MAAM,cAAc,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;CAC5C,CAAC,IAAI,eAAe,GAAG,KAAK,CAAC;AAC7B;CACA,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;CAClB,CAAC,MAAM,MAAM,GAAG,kBAAkB,EAAE,GAAG,EAAE,CAAC;AAC1C;CACA,CAAC,KAAK,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG;AAC5B;CACA,EAAE,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CAC5B,EAAE,MAAM,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC;CACjC,EAAE,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC;CACjC,EAAE,iBAAiB,EAAE,cAAc,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,yBAAyB,EAAE,CAAC;AAC5F;CACA,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,yBAAyB,EAAE,CAAC;CAC1E,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AACrB;CACA,EAAE,MAAM;AACR;CACA,EAAE,MAAM,IAAI,KAAK,IAAI,MAAM,GAAG;AAC9B;CACA,GAAG,MAAM,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC;CAClC,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC,EAAE,CAAC;CAC7C,GAAG,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,yBAAyB,EAAE,CAAC;AACxG;CACA,GAAG,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,yBAAyB,EAAE,CAAC;CAC3E,GAAG,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AACtB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,KAAK,CAAC;AACd;CACA,CAAC;AACD;CACO,SAAS,eAAe,EAAE,GAAG,EAAE,OAAO,GAAG;AAChD;CACA;CACA;CACA;CACA,CAAC,MAAM,KAAK,GAAG,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;AACzC;CACA,CAAC,IAAI,YAAY,CAAC;CAClB,CAAC,IAAI,WAAW,CAAC;CACjB,CAAC,IAAI,WAAW,CAAC;CACjB,CAAC,MAAM,WAAW,GAAG,EAAE,CAAC;CACxB,CAAC,MAAM,iBAAiB,GAAG,OAAO,CAAC,oBAAoB,GAAG,iBAAiB,GAAG,WAAW,CAAC;CAC1F,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG;AAC3C;CACA,EAAE,MAAM,IAAI,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC;CAC1B,EAAE,IAAI,SAAS,GAAG,UAAU,EAAE,IAAI,EAAE,CAAC;AACrC;CACA,EAAE,MAAM,MAAM,GAAG,IAAI,iBAAiB,EAAE,cAAc,GAAG,SAAS,EAAE,CAAC;CACrE,EAAE,YAAY,GAAG,IAAI,YAAY,EAAE,MAAM,EAAE,CAAC;CAC5C,EAAE,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;CAC1C,EAAE,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;CAC1C,EAAE,cAAc,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC;CAC5B,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC7B;CACA,EAAE;AACF;CACA,CAAC,OAAO,WAAW,CAAC;AACpB;CACA,CAAC,SAAS,UAAU,EAAE,IAAI,GAAG;AAC7B;CACA,EAAE,KAAK,IAAI,CAAC,KAAK,GAAG;AACpB;CACA,GAAG,OAAO,CAAC,CAAC;AACZ;CACA,GAAG,MAAM;AACT;CACA,GAAG,OAAO,CAAC,GAAG,UAAU,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,UAAU,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AACjE;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,SAAS,cAAc,EAAE,UAAU,EAAE,IAAI,GAAG;AAC7C;CACA,EAAE,MAAM,aAAa,GAAG,UAAU,GAAG,CAAC,CAAC;CACvC,EAAE,MAAM,aAAa,GAAG,UAAU,GAAG,CAAC,CAAC;CACvC,EAAE,MAAM,MAAM,GAAG,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC;CAChC,EAAE,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;CACzC,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,YAAY,EAAE,aAAa,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;AACzD;CACA,GAAG;AACH;CACA,EAAE,KAAK,MAAM,GAAG;AAChB;CACA,GAAG,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;CAC9B,GAAG,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;CAC5B,GAAG,WAAW,EAAE,aAAa,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC;CAC7C,GAAG,WAAW,EAAE,aAAa,GAAG,EAAE,EAAE,GAAG,KAAK,CAAC;CAC7C,GAAG,WAAW,EAAE,aAAa,GAAG,EAAE,EAAE,GAAG,gBAAgB,CAAC;CACxD,GAAG,OAAO,UAAU,GAAG,cAAc,CAAC;AACtC;CACA,GAAG,MAAM;AACT;CACA,GAAG,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CAC1B,GAAG,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;CAC5B,GAAG,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC;CACA,GAAG,IAAI,iBAAiB,CAAC;CACzB,GAAG,iBAAiB,GAAG,cAAc,EAAE,UAAU,GAAG,cAAc,EAAE,IAAI,EAAE,CAAC;AAC3E;CACA,GAAG,KAAK,EAAE,iBAAiB,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG;AACxD;CACA,IAAI,MAAM,IAAI,KAAK,EAAE,2DAA2D,EAAE,CAAC;AACnF;CACA,IAAI;AACJ;CACA,GAAG,WAAW,EAAE,aAAa,GAAG,CAAC,EAAE,GAAG,iBAAiB,GAAG,CAAC,CAAC;CAC5D,GAAG,iBAAiB,GAAG,cAAc,EAAE,iBAAiB,EAAE,KAAK,EAAE,CAAC;AAClE;CACA,GAAG,WAAW,EAAE,aAAa,GAAG,CAAC,EAAE,GAAG,SAAS,CAAC;CAChD,GAAG,OAAO,iBAAiB,CAAC;AAC5B;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;;CCv2BO,MAAM,oBAAoB,CAAC;AAClC;CACA,CAAC,WAAW,GAAG;AACf;CACA,EAAE,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC;CACtB,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,QAAQ,CAAC;AACxB;CACA,EAAE;AACF;CACA,CAAC,kBAAkB,EAAE,MAAM,EAAE,KAAK,GAAG;AACrC;CACA,EAAE,IAAI,GAAG,GAAG,QAAQ,CAAC;CACrB,EAAE,IAAI,GAAG,GAAG,EAAE,QAAQ,CAAC;CACvB,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACpD;CACA,GAAG,MAAM,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CACzB,GAAG,MAAM,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC;CAC1B,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;CAC/B,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/B;CACA,GAAG;AACH;CACA,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;CACjB,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB;CACA,EAAE;AACF;CACA,CAAC,aAAa,EAAE,IAAI,EAAE,MAAM,GAAG;AAC/B;CACA,EAAE,IAAI,GAAG,GAAG,QAAQ,CAAC;CACrB,EAAE,IAAI,GAAG,GAAG,EAAE,QAAQ,CAAC;CACvB,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACpD;CACA,GAAG,MAAM,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CACzB,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;CAC7B,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;CAC/B,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/B;CACA,GAAG;AACH;CACA,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;CACjB,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB;CACA,EAAE;AACF;CACA,CAAC,WAAW,EAAE,KAAK,GAAG;AACtB;CACA,EAAE,OAAO,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACtD;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,oBAAoB,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,YAAY;AAC1D;CACA,CAAC,MAAM,CAAC,GAAG,IAAIC,aAAO,EAAE,CAAC;CACzB,CAAC,OAAO,SAAS,UAAU,EAAE,IAAI,EAAE,GAAG,GAAG;AACzC;CACA,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC;CACzB,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC;CACzB,EAAE,IAAI,GAAG,GAAG,QAAQ,CAAC;CACrB,EAAE,IAAI,GAAG,GAAG,EAAE,QAAQ,CAAC;CACvB,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACnC;CACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACpC;CACA,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;CAC/C,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;CAC/C,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;AAC/C;CACA,KAAK,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;CAC/B,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CAChC,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAChC;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;CACjB,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;CACO,MAAM,eAAe,GAAG,EAAE,YAAY;AAC7C;CACA,CAAC,MAAM,cAAc,GAAG,IAAI,oBAAoB,EAAE,CAAC;CACnD,CAAC,OAAO,SAAS,eAAe,EAAE,MAAM,EAAE,MAAM,GAAG;AACnD;CACA,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;CAChC,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;CAClC,EAAE,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;AACtC;CACA,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;CAChC,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;CAClC,EAAE,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;AACtC;CACA;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;CAC9B,GAAG,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;CAC5B,GAAG,cAAc,CAAC,aAAa,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC;CAC/C,GAAG,KAAK,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,GAAG,OAAO,KAAK,CAAC;AACxD;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;CAC9B,GAAG,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;CAC5B,GAAG,cAAc,CAAC,aAAa,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC;CAC/C,GAAG,KAAK,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,GAAG,OAAO,KAAK,CAAC;AACxD;CACA,GAAG;AACH;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI;;CC5HE,MAAM,sBAAsB,GAAG,EAAE,YAAY;AACpD;CACA;CACA,CAAC,MAAM,IAAI,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC5B,CAAC,MAAM,IAAI,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC5B,CAAC,MAAM,GAAG,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC3B,CAAC,OAAO,SAAS,sBAAsB,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,GAAG;AAC1D;CACA,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC;CACtB,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC;CACnB,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC;CACtB,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC;AACnB;CACA,EAAE,GAAG,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;CAC3B,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC;CACtC,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC;AACtC;CACA;CACA,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC/B;CACA;CACA,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC/B;CACA;CACA,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC/B;CACA;CACA,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC/B;CACA;CACA,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC/B;CACA;CACA,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAC9C;CACA,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;CACZ,EAAE,KAAK,KAAK,KAAK,CAAC,GAAG;AACrB;CACA,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,KAAK,KAAK,CAAC;AACjD;CACA,GAAG,MAAM;AACT;CACA,GAAG,CAAC,GAAG,CAAC,CAAC;AACT;CACA,GAAG;AACH;CACA,EAAE,EAAE,GAAG,EAAE,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,KAAK,CAAC;AACrC;CACA,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;CACf,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;AAChB;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;CACO,MAAM,6BAA6B,GAAG,EAAE,YAAY;AAC3D;CACA;CACA,CAAC,MAAM,WAAW,GAAG,IAAIC,aAAO,EAAE,CAAC;CACnC,CAAC,MAAM,KAAK,GAAG,IAAID,aAAO,EAAE,CAAC;CAC7B,CAAC,MAAM,KAAK,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC7B,CAAC,OAAO,SAAS,6BAA6B,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO,GAAG;AAC3E;CACA,EAAE,sBAAsB,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,CAAC;AAChD;CACA,EAAE,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;CACxB,EAAE,IAAI,EAAE,GAAG,WAAW,CAAC,CAAC,CAAC;CACzB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG;AAChD;CACA,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;CACvB,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC;AACxB;CACA,GAAG,OAAO;AACV;CACA,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;AACjC;CACA;CACA,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG;AACjB;CACA,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;AACxB;CACA,IAAI,MAAM;AACV;CACA,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;AACxB;CACA,IAAI;AACJ;CACA,GAAG,EAAE,CAAC,mBAAmB,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;CACpD,GAAG,OAAO;AACV;CACA,GAAG,MAAM,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG;AACnC;CACA;CACA,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG;AAChB;CACA,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;AACxB;CACA,IAAI,MAAM;AACV;CACA,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;AACxB;CACA,IAAI;AACJ;CACA,GAAG,EAAE,CAAC,mBAAmB,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;CACpD,GAAG,OAAO;AACV;CACA,GAAG,MAAM;AACT;CACA;CACA,GAAG,IAAI,CAAC,CAAC;CACT,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG;AAChB;CACA,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;AACjB;CACA,IAAI,MAAM;AACV;CACA,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;AACf;CACA,IAAI;AACJ;CACA,GAAG,IAAI,EAAE,CAAC;CACV,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG;AACjB;CACA,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC;AAClB;CACA,IAAI,MAAM;AACV;CACA,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;AAChB;CACA,IAAI;AACJ;CACA,GAAG,MAAM,YAAY,GAAG,KAAK,CAAC;CAC9B,GAAG,MAAM,aAAa,GAAG,KAAK,CAAC;CAC/B,GAAG,EAAE,CAAC,mBAAmB,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;CAC7C,GAAG,EAAE,CAAC,mBAAmB,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AAC5C;CACA,GAAG,KAAK,YAAY,CAAC,iBAAiB,EAAE,EAAE,EAAE,IAAI,aAAa,CAAC,iBAAiB,EAAE,CAAC,EAAE,GAAG;AACvF;CACA,IAAI,OAAO,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC;CACjC,IAAI,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CACvB,IAAI,OAAO;AACX;CACA,IAAI,MAAM;AACV;CACA,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;CACtB,IAAI,OAAO,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC;CAClC,IAAI,OAAO;AACX;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;AACA;CACO,MAAM,uBAAuB,GAAG,EAAE,YAAY;AACrD;CACA;CACA,CAAC,MAAM,gBAAgB,GAAG,IAAIA,aAAO,EAAE,CAAC;CACxC,CAAC,MAAM,kBAAkB,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC1C,CAAC,MAAM,SAAS,GAAG,IAAIE,WAAK,EAAE,CAAC;CAC/B,CAAC,MAAM,QAAQ,GAAG,IAAIC,WAAK,EAAE,CAAC;CAC9B,CAAC,OAAO,SAAS,uBAAuB,EAAE,MAAM,EAAE,QAAQ,GAAG;AAC7D;CACA,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;CACpC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC/B;CACA;CACA,EAAE,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;CACrB,EAAE,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC;CACnB,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,mBAAmB,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC;CACvF,EAAE,KAAK,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,MAAM,GAAG,OAAO,IAAI,CAAC;AAClE;CACA,EAAE,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;CACrB,EAAE,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC;CACnB,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,mBAAmB,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC;CACvF,EAAE,KAAK,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,MAAM,GAAG,OAAO,IAAI,CAAC;AAClE;CACA,EAAE,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;CACrB,EAAE,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC;CACnB,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,mBAAmB,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC;CACvF,EAAE,KAAK,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,MAAM,GAAG,OAAO,IAAI,CAAC;AAClE;CACA;CACA,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC;CAC/C,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,eAAe,EAAE,MAAM,EAAE,EAAE,CAAC;CACzD,EAAE,KAAK,EAAE,IAAI,MAAM,GAAG;AACtB;CACA,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,YAAY,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC;CAC/D,GAAG,MAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,EAAE,EAAE,EAAE,CAAC;CAC3C,GAAG,KAAK,EAAE,GAAG,OAAO,IAAI,CAAC;AACzB;CACA,GAAG;AACH;CACA,EAAE,OAAO,KAAK,CAAC;AACf;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI;;CCtML,MAAM,YAAY,GAAG,KAAK,CAAC;CAC3B,SAAS,UAAU,EAAE,KAAK,GAAG;AAC7B;CACA,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,YAAY,CAAC;AACzC;CACA,CAAC;AACD;CACO,MAAM,gBAAgB,SAASC,cAAQ,CAAC;AAC/C;CACA,CAAC,WAAW,EAAE,GAAG,IAAI,GAAG;AACxB;CACA,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,CAAC;AACnB;CACA,EAAE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;CACjC,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAIJ,aAAO,EAAE,EAAE,CAAC;CAClE,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAI,oBAAoB,EAAE,EAAE,CAAC;CACjF,EAAE,IAAI,CAAC,MAAM,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;CAC3C,EAAE,IAAI,CAAC,MAAM,GAAG,IAAIK,YAAM,EAAE,CAAC;CAC7B,EAAE,IAAI,CAAC,KAAK,GAAG,IAAIH,WAAK,EAAE,CAAC;CAC3B,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B;CACA,EAAE;AACF;CACA,CAAC,gBAAgB,EAAE,MAAM,GAAG;AAC5B;CACA,EAAE,OAAO,uBAAuB,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACjD;CACA,EAAE;AACF;CACA,CAAC,MAAM,GAAG;AACV;CACA,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;CACnB,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;CACnB,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;CACnB,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B;CACA,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;CAC/B,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACnC;CACA,EAAE,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC7B,EAAE,MAAM,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC9B,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC;CAC1B,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACtC;CACA,EAAE,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC7B,EAAE,MAAM,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC9B,EAAE,KAAK,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;CAC3B,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACtC;CACA,EAAE,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC7B,EAAE,MAAM,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC9B,EAAE,KAAK,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;CAC3B,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACtC;CACA,EAAE,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC7B,EAAE,MAAM,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC9B,EAAE,KAAK,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;CAC3B,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACtC;CACA,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;CAC3C,EAAE,IAAI,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;CACvD,EAAE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC3B;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,gBAAgB,CAAC,SAAS,CAAC,qBAAqB,GAAG,EAAE,YAAY;AACjE;CACA,CAAC,MAAM,MAAM,GAAG,IAAIF,aAAO,EAAE,CAAC;CAC9B,CAAC,MAAM,MAAM,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC9B,CAAC,MAAM,IAAI,GAAG,IAAIG,WAAK,EAAE,CAAC;AAC1B;CACA,CAAC,OAAO,SAAS,iBAAiB,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,GAAG;AAC9E;CACA,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC;CACjC,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;CAC7B,EAAE,IAAI,MAAM,CAAC;CACb,EAAE,IAAI,iBAAiB,GAAG,QAAQ,CAAC;AACnC;CACA;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,KAAK,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CAC/B,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;CAClC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;AACpC;CACA,GAAG,6BAA6B,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAClE;CACA,GAAG,MAAM,GAAG,MAAM,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;CAC/C,GAAG,KAAK,MAAM,GAAG,iBAAiB,GAAG;AACrC;CACA,IAAI,iBAAiB,GAAG,MAAM,CAAC;CAC/B,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;CAC1C,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC1C;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA,EAAE,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;CAC5C,EAAE,MAAM,GAAG,KAAK,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;CAC7C,EAAE,KAAK,MAAM,GAAG,iBAAiB,GAAG;AACpC;CACA,GAAG,iBAAiB,GAAG,MAAM,CAAC;CAC9B,GAAG,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;CACzC,GAAG,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACxC;CACA,GAAG;AACH;CACA,EAAE,IAAI,CAAC,mBAAmB,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC;CAC1C,EAAE,MAAM,GAAG,GAAG,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;CAC3C,EAAE,KAAK,MAAM,GAAG,iBAAiB,GAAG;AACpC;CACA,GAAG,iBAAiB,GAAG,MAAM,CAAC;CAC9B,GAAG,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;CACzC,GAAG,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AACtC;CACA,GAAG;AACH;CACA,EAAE,OAAO,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC;AACxC;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;CACA,gBAAgB,CAAC,SAAS,CAAC,kBAAkB,GAAG,EAAE,YAAY;AAC9D;CACA,CAAC,MAAM,MAAM,GAAG,IAAI,gBAAgB,EAAE,CAAC;CACvC,CAAC,MAAM,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC;CAC7B,CAAC,MAAM,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC;CAC7B,CAAC,MAAM,eAAe,GAAG,IAAI,oBAAoB,EAAE,CAAC;CACpD,CAAC,MAAM,gBAAgB,GAAG,IAAI,oBAAoB,EAAE,CAAC;CACrD,CAAC,MAAM,UAAU,GAAG,IAAIH,aAAO,EAAE,CAAC;CAClC,CAAC,MAAM,IAAI,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC5B,CAAC,MAAM,IAAI,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC5B,CAAC,MAAM,OAAO,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC/B,CAAC,MAAM,IAAI,GAAG,IAAIG,WAAK,EAAE,CAAC;CAC1B,CAAC,MAAM,KAAK,GAAG,IAAIA,WAAK,EAAE,CAAC;CAC3B,CAAC,MAAM,KAAK,GAAG,IAAIA,WAAK,EAAE,CAAC;AAC3B;CACA;CACA;CACA,CAAC,OAAO,SAAS,kBAAkB,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI,EAAE,WAAW,GAAG,KAAK,GAAG;AACjF;CACA,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG;AAC1B;CACA,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;CACA,GAAG;AACH;CACA,EAAE,KAAK,EAAE,KAAK,CAAC,kBAAkB,GAAG;AACpC;CACA,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;CACxB,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;CACnB,GAAG,KAAK,GAAG,MAAM,CAAC;AAClB;CACA,GAAG,MAAM,KAAK,KAAK,CAAC,WAAW,GAAG;AAClC;CACA,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;AAClB;CACA,GAAG;AACH;CACA,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;CAC5B,EAAE,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;AAC7B;CACA,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,EAAE,EAAE,GAAG,GAAG,GAAG,KAAK,GAAG;AACtE;CACA;CACA,GAAG,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;CACrC,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC;CACjC,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;CACvB,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;CACvB,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;CACvB,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,IAAI,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;CAC/B,IAAI,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;CAC7B,IAAI,eAAe,CAAC,aAAa,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC;CAC9C,IAAI,KAAK,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,GAAG,OAAO,KAAK,CAAC;AAC1D;CACA,IAAI;AACJ;CACA,GAAG,MAAM,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC;CACtC,GAAG,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;CAClC,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;CACtB,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;CACtB,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;CACtB,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,IAAI,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;CAC/B,IAAI,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;CAC7B,IAAI,eAAe,CAAC,aAAa,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC;CAC9C,IAAI,KAAK,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,GAAG,OAAO,KAAK,CAAC;AAC1D;CACA,IAAI;AACJ;CACA;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,IAAI,MAAM,GAAG,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;CAC9B,IAAI,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG;AACtC;CACA,KAAK,MAAM,GAAG,GAAG,QAAQ,EAAE,EAAE,EAAE,CAAC;CAChC,KAAK,UAAU,CAAC,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACzC,KAAK,eAAe,CAAC,aAAa,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;CACvD,KAAK,gBAAgB,CAAC,aAAa,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;CACxD,KAAK,KAAK,eAAe,CAAC,WAAW,EAAE,gBAAgB,EAAE,GAAG,OAAO,KAAK,CAAC;AACzE;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG,KAAK,MAAM,GAAG;AACjB;CACA;CACA,IAAI,KAAK,EAAE,WAAW,GAAG;AACzB;CACA,KAAK,OAAO,CAAC,IAAI,EAAE,6HAA6H,EAAE,CAAC;AACnJ;CACA,KAAK;AACL;CACA,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;CAChC,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAC9B;CACA,IAAI;AACJ;CACA,GAAG,OAAO,IAAI,CAAC;AACf;CACA,GAAG,MAAM;AACT;CACA;CACA,GAAG,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;CAC/B,GAAG,IAAI,MAAM,GAAG,KAAK,CAAC;CACtB,GAAG,IAAI,MAAM,GAAG,CAAC,CAAC;CAClB,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,IAAI,MAAM,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC3B,IAAI,MAAM,KAAK,GAAG,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;AAC3C;CACA,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;CACzB,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;CAC3B,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;AACvB;CACA,IAAI,MAAM,WAAW,GAAG,MAAM,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;CACzD,IAAI,MAAM,eAAe,GAAG,UAAU,EAAE,MAAM,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC;CACtE,IAAI,KAAK,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,IAAI,eAAe,GAAG;AACtE;CACA;CACA,KAAK,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;CACxB,KAAK,MAAM,GAAG,CAAC,CAAC;CAChB,KAAK,MAAM;AACX;CACA,KAAK;AACL;CACA;CACA,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,eAAe,CAAC;CACvF,IAAI,KAAK,aAAa,IAAI,EAAE,UAAU,EAAE,WAAW,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE,GAAG;AAC5E;CACA,KAAK,MAAM,GAAG,CAAC;CACf,KAAK,KAAK,MAAM,GAAG;AACnB;CACA,MAAM,MAAM;AACZ;CACA,MAAM;AACN;CACA,KAAK,MAAM,GAAG,IAAI,CAAC;AACnB;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG,KAAK,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG;AAC3D;CACA,IAAI,KAAK,MAAM,GAAG;AAClB;CACA,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;CACpC,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AAClC;CACA,KAAK;AACL;CACA,IAAI,OAAO,IAAI,CAAC;AAChB;CACA,IAAI,MAAM,KAAK,MAAM,KAAK,CAAC,GAAG;AAC9B;CACA,IAAI,OAAO,KAAK,CAAC;AACjB;CACA,IAAI;AACJ;CACA;CACA,GAAG,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;CAChC,GAAG,IAAI,MAAM,GAAG,KAAK,CAAC;CACtB,GAAG,IAAI,MAAM,GAAG,CAAC,CAAC;CAClB,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,IAAI,MAAM,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC3B,IAAI,MAAM,KAAK,GAAG,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;AAC3C;CACA,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;CACzB,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;CAC3B,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;AACvB;CACA,IAAI,MAAM,WAAW,GAAG,MAAM,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;CACzD,IAAI,MAAM,eAAe,GAAG,UAAU,EAAE,MAAM,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC;CACtE,IAAI,KAAK,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,IAAI,eAAe,GAAG;AACtE;CACA;CACA,KAAK,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;CACxB,KAAK,MAAM,GAAG,CAAC,CAAC;CAChB,KAAK,MAAM;AACX;CACA,KAAK;AACL;CACA;CACA,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,eAAe,CAAC;CACvF,IAAI,KAAK,aAAa,IAAI,EAAE,UAAU,EAAE,WAAW,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE,GAAG;AAC5E;CACA,KAAK,MAAM,GAAG,CAAC;CACf,KAAK,KAAK,MAAM,GAAG;AACnB;CACA,MAAM,MAAM;AACZ;CACA,MAAM;AACN;CACA,KAAK,MAAM,GAAG,IAAI,CAAC;AACnB;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG,KAAK,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG;AAC1D;CACA,IAAI,KAAK,MAAM,GAAG;AAClB;CACA,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;CACpC,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AAClC;CACA,KAAK;AACL;CACA,IAAI,OAAO,IAAI,CAAC;AAChB;CACA,IAAI,MAAM,KAAK,MAAM,KAAK,CAAC,GAAG;AAC9B;CACA,IAAI,OAAO,KAAK,CAAC;AACjB;CACA,IAAI;AACJ;CACA;CACA,GAAG,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;CACvB,GAAG,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;AACvB;CACA,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG;AAC/B;CACA,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC;CAC1B,IAAI,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;CAC5B,IAAI,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;AACpB;CACA,IAAI;AACJ;CACA;CACA,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;CACtC,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;CACpC,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;CACtC,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;CACpC,GAAG,MAAM,UAAU,GAAG,EAAE,GAAG,EAAE,CAAC;CAC9B,GAAG,MAAM,UAAU,GAAG,EAAE,GAAG,EAAE,CAAC;AAC9B;CACA,GAAG,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,UAAU,KAAK,UAAU,GAAG;AAC9D;CACA,IAAI,OAAO,KAAK,CAAC;AACjB;CACA,IAAI;AACJ;CACA;CACA,GAAG,KAAK,MAAM,GAAG;AACjB;CACA,IAAI,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;CACnD,IAAI,KAAK,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG;AACnC;CACA,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;AACtC;CACA,KAAK,MAAM;AACX;CACA,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;AACtC;CACA,KAAK;AACL;CACA,IAAI,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;CAC/C,IAAI,KAAK,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG;AACnC;CACA,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AAClC;CACA,KAAK,MAAM;AACX;CACA,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AAClC;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG,OAAO,IAAI,CAAC;AACf;CACA,GAAG;AACH;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;AACA;CACA,gBAAgB,CAAC,SAAS,CAAC,eAAe,GAAG,EAAE,YAAY;AAC3D;CACA,CAAC,MAAM,MAAM,GAAG,IAAIH,aAAO,EAAE,CAAC;CAC9B,CAAC,OAAO,SAAS,eAAe,EAAE,KAAK,GAAG;AAC1C;CACA,EAAE,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;CAC5C,EAAE,OAAO,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;AACpC;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;AACA;CACA,gBAAgB,CAAC,SAAS,CAAC,kBAAkB,GAAG,EAAE,YAAY;AAC9D;CACA,CAAC,MAAM,KAAK,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC7B,CAAC,MAAM,MAAM,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC9B,CAAC,MAAM,YAAY,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACxC,CAAC,MAAM,KAAK,GAAG,IAAIG,WAAK,EAAE,CAAC;CAC3B,CAAC,MAAM,KAAK,GAAG,IAAIA,WAAK,EAAE,CAAC;AAC3B;CACA,CAAC,OAAO,SAAS,kBAAkB,EAAE,KAAK,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,GAAG;AAC7E;CACA,EAAE,MAAM,UAAU,GAAG,OAAO,IAAI,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC;CACvD,EAAE,KAAK,IAAI,CAAC,kBAAkB,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;AACtD;CACA,GAAG,KAAK,OAAO,IAAI,OAAO,GAAG;AAC7B;CACA,IAAI,KAAK,OAAO,GAAG,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC;CACnD,IAAI,KAAK,OAAO,GAAG,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC;AACnD;CACA,IAAI;AACJ;CACA,GAAG,OAAO,CAAC,CAAC;AACZ;CACA,GAAG;AACH;CACA,EAAE,IAAI,iBAAiB,GAAG,QAAQ,CAAC;AACnC;CACA;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,IAAI,IAAI,CAAC;CACZ,GAAG,MAAM,KAAK,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;CACnC,GAAG,MAAM,QAAQ,GAAG,KAAK,EAAE,KAAK,EAAE,CAAC;CACnC,GAAG,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;AAC/C;CACA,GAAG,IAAI,GAAG,QAAQ,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC;AAC9C;CACA,GAAG,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACnC;CACA,IAAI,iBAAiB,GAAG,IAAI,CAAC;CAC7B,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;CACzC,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC;AAC5C;CACA,IAAI;AACJ;AACA;CACA,GAAG,MAAM,OAAO,GAAG,IAAI,EAAE,KAAK,EAAE,CAAC;CACjC,GAAG,KAAK,CAAC,mBAAmB,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;AAC/C;CACA,GAAG,IAAI,GAAG,OAAO,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC;AAC7C;CACA,GAAG,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACnC;CACA,IAAI,iBAAiB,GAAG,IAAI,CAAC;CAC7B,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC;CAC3C,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACzC;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,GAAG,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;CACjC,GAAG,MAAM,GAAG,GAAG,YAAY,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;CAC7C,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;CACzC,GAAG,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG;AACrC;CACA,IAAI,MAAM,GAAG,GAAG,YAAY,EAAE,EAAE,EAAE,CAAC;CACnC,IAAI,MAAM,GAAG,GAAG,YAAY,EAAE,EAAE,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;CAC/C,IAAI,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC;AAC5C;CACA,IAAI,6BAA6B,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACjE;CACA,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;CACnD,IAAI,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACpC;CACA,KAAK,iBAAiB,GAAG,IAAI,CAAC;CAC9B,KAAK,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;CAC1C,KAAK,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC3C;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,OAAO,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC;AACxC;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI;;CChgBE,MAAM,WAAW,CAAC;AACzB;CACA,CAAC,WAAW,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,GAAG;AACjC;CACA,EAAE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;CAC5B,EAAE,IAAI,CAAC,GAAG,GAAG,IAAIH,aAAO,EAAE,CAAC;CAC3B,EAAE,IAAI,CAAC,GAAG,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC3B,EAAE,IAAI,CAAC,MAAM,GAAG,IAAIM,aAAO,EAAE,CAAC;CAC9B,EAAE,IAAI,CAAC,SAAS,GAAG,IAAIA,aAAO,EAAE,CAAC;CACjC,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAIN,aAAO,EAAE,EAAE,CAAC;CACjE,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAIA,aAAO,EAAE,EAAE,CAAC;CAClE,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAI,oBAAoB,EAAE,EAAE,CAAC;CACjF,EAAE,IAAI,CAAC,gBAAgB,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAI,oBAAoB,EAAE,EAAE,CAAC;CACxF,EAAE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC3B;CACA,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;CAClC,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;CAClC,EAAE,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC3C;CACA,EAAE;AACF;CACA,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,GAAG;AACzB;CACA,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;CACvB,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;CACvB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;CAC7B,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B;CACA,EAAE;AACF;CACA,CAAC,IAAI,EAAE,KAAK,GAAG;AACf;CACA,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;CAC7B,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;CAC7B,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC;CACnC,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE,YAAY;AAC7C;CACA,CAAC,OAAO,SAAS,MAAM,GAAG;AAC1B;CACA,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;CAC7B,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;CACvB,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACvB;CACA,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;CAC7B,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACnC;CACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACpC;CACA,KAAK,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;CAC5E,KAAK,MAAM,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CAC3B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CAC7B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CAC7B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAC7B;CACA,KAAK,CAAC,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC;AAC9B;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;CACnC,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;CAC/B,EAAE,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CAC7B,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC7B,GAAG,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC7B,GAAG,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;CACxB,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,KAAK,EAAE,CAAC;AAC9B;CACA,GAAG,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;CACjC,GAAG,EAAE,CAAC,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;AACpC;CACA,GAAG;AACH;CACA,EAAE,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;CACjD,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,kBAAkB,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;CAC1D,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,kBAAkB,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;CAC1D,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,kBAAkB,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1D;CACA,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC;CAC9C,EAAE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC3B;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;CACA,WAAW,CAAC,SAAS,CAAC,aAAa,GAAG,EAAE,YAAY;AACpD;CACA,CAAC,MAAM,UAAU,GAAG,IAAI,oBAAoB,EAAE,CAAC;CAC/C,CAAC,OAAO,SAAS,aAAa,EAAE,GAAG,GAAG;AACtC;CACA;CACA,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG;AAC1B;CACA,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;CACA,GAAG;AACH;CACA,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;CACtB,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;CACtB,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;CACnC,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;CAC/B,EAAE,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACjD;CACA,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;CACzB,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;CACzB,EAAE,KAAK,gBAAgB,EAAE,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,GAAG,OAAO,KAAK,CAAC;AACtE;CACA,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;CACzB,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;CACzB,EAAE,KAAK,gBAAgB,EAAE,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,GAAG,OAAO,KAAK,CAAC;AACtE;CACA,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;CACzB,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;CACzB,EAAE,KAAK,gBAAgB,EAAE,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,GAAG,OAAO,KAAK,CAAC;AACtE;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC7B,GAAG,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC7B,GAAG,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;CACtC,GAAG,KAAK,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,GAAG,OAAO,KAAK,CAAC;AACpD;CACA,GAAG;AACH;CACA,EAAE,OAAO,IAAI,CAAC;AACd;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;CACA,WAAW,CAAC,SAAS,CAAC,kBAAkB,GAAG,EAAE,YAAY;AACzD;CACA,CAAC,MAAM,KAAK,GAAG,IAAI,gBAAgB,EAAE,CAAC;CACtC,CAAC,MAAM,SAAS,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC;CAClC,CAAC,MAAM,eAAe,GAAG,IAAI,oBAAoB,EAAE,CAAC;CACpD,CAAC,MAAM,gBAAgB,GAAG,IAAI,oBAAoB,EAAE,CAAC;CACrD,CAAC,MAAM,UAAU,GAAG,IAAIA,aAAO,EAAE,CAAC;CAClC,CAAC,OAAO,SAAS,kBAAkB,EAAE,QAAQ,GAAG;AAChD;CACA,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG;AAC1B;CACA,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;CACA,GAAG;AACH;CACA,EAAE,KAAK,EAAE,QAAQ,CAAC,kBAAkB,GAAG;AACvC;CACA,GAAG,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC;CAC1B,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;CAClB,GAAG,QAAQ,GAAG,KAAK,CAAC;AACpB;CACA,GAAG,MAAM,KAAK,QAAQ,CAAC,WAAW,GAAG;AACrC;CACA,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;AACrB;CACA,GAAG;AACH;CACA,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;CACnC,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;CACA,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;CAC9B,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;CAC9B,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;AAC9B;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC7B,GAAG,MAAM,EAAE,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC3B,GAAG,eAAe,CAAC,aAAa,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC;CAClD,GAAG,KAAK,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,GAAG,OAAO,KAAK,CAAC;AACzD;CACA,GAAG;AACH;CACA,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC;CAC1C,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC;CACtC,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;CAC7B,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,EAAE,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;CAChC,GAAG,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;CAC9B,GAAG,eAAe,CAAC,aAAa,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC;CAC/C,GAAG,KAAK,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,GAAG,OAAO,KAAK,CAAC;AACzD;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;CAC5B,GAAG,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG;AACrC;CACA,IAAI,MAAM,GAAG,GAAG,UAAU,EAAE,EAAE,EAAE,CAAC;CACjC,IAAI,UAAU,CAAC,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACxC,IAAI,eAAe,CAAC,aAAa,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;CAC3D,IAAI,gBAAgB,CAAC,aAAa,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC;CACzD,IAAI,KAAK,eAAe,CAAC,WAAW,EAAE,gBAAgB,EAAE,GAAG,OAAO,KAAK,CAAC;AACxE;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,OAAO,IAAI,CAAC;AACd;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;CACA,WAAW,CAAC,SAAS,CAAC,mBAAmB,GAAG,EAAE,YAAY;AAC1D;CACA,CAAC,OAAO,SAAS,mBAAmB,EAAE,KAAK,EAAE,OAAO,GAAG;AACvD;CACA,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG;AAC1B;CACA,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;CACA,GAAG;AACH;CACA,EAAE,OAAO;CACT,IAAI,IAAI,EAAE,KAAK,EAAE;CACjB,IAAI,YAAY,EAAE,IAAI,CAAC,SAAS,EAAE;CAClC,IAAI,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE;CAC/B,IAAI,YAAY,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;AAChC;CACA,EAAE,OAAO,OAAO,CAAC;AACjB;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;CACA,WAAW,CAAC,SAAS,CAAC,eAAe,GAAG,EAAE,YAAY;AACtD;CACA,CAAC,MAAM,MAAM,GAAG,IAAIA,aAAO,EAAE,CAAC;CAC9B,CAAC,OAAO,SAAS,eAAe,EAAE,KAAK,GAAG;AAC1C;CACA,EAAE,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;CAC5C,EAAE,OAAO,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;AACpC;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;CACA,WAAW,CAAC,SAAS,CAAC,aAAa,GAAG,EAAE,YAAY;AACpD;CACA,CAAC,MAAM,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CACrC,CAAC,MAAM,SAAS,GAAG,IAAI,KAAK,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAIG,WAAK,EAAE,EAAE,CAAC;CACnE,CAAC,MAAM,SAAS,GAAG,IAAI,KAAK,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAIA,WAAK,EAAE,EAAE,CAAC;AACnE;CACA,CAAC,MAAM,MAAM,GAAG,IAAIH,aAAO,EAAE,CAAC;CAC9B,CAAC,MAAM,MAAM,GAAG,IAAIA,aAAO,EAAE,CAAC;AAC9B;CACA;CACA,CAAC,OAAO,SAAS,aAAa,EAAE,GAAG,EAAE,SAAS,GAAG,CAAC,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,GAAG;AACrF;CACA,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG;AAC1B;CACA,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;CACA,GAAG;AACH;CACA,EAAE,KAAK,IAAI,CAAC,aAAa,EAAE,GAAG,EAAE,GAAG;AACnC;CACA,GAAG,KAAK,OAAO,IAAI,OAAO,GAAG;AAC7B;CACA,IAAI,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC;CAC5B,IAAI,IAAI,CAAC,mBAAmB,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;CAC/C,IAAI,GAAG,CAAC,mBAAmB,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC9C;CACA,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;CAC1C,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC1C;CACA,IAAI;AACJ;CACA,GAAG,OAAO,CAAC,CAAC;AACZ;CACA,GAAG;AACH;CACA,EAAE,MAAM,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;CAC3C,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;CACtB,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;CACtB,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B;AACA;CACA;CACA,EAAE,IAAI,iBAAiB,GAAG,QAAQ,CAAC;AACnC;CACA;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CACzB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACtC;CACA,GAAG,MAAM,IAAI,GAAG,CAAC,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;CAC9C,GAAG,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACnC;CACA,IAAI,iBAAiB,GAAG,IAAI,CAAC;CAC7B,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;CACrC,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC1C;CACA,IAAI,KAAK,IAAI,GAAG,UAAU,GAAG,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AACtD;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC;CAChB,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,GAAG;AACtC;CACA,IAAI,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,GAAG;AACvC;CACA,KAAK,MAAM,SAAS,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CACrC,KAAK,MAAM,UAAU,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACtC;CACA;CACA,KAAK,MAAM,KAAK,GAAG,EAAE,IAAI,SAAS,GAAG,EAAE,IAAI,UAAU,CAAC;CACtD,KAAK,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,SAAS,GAAG,EAAE,IAAI,UAAU,CAAC;CAChE,KAAK,MAAM,EAAE,GAAG,MAAM,EAAE,KAAK,EAAE,CAAC;CAChC,KAAK,MAAM,EAAE,GAAG,MAAM,EAAE,MAAM,EAAE,CAAC;CACjC,KAAK,MAAM,KAAK,GAAG,SAAS,EAAE,KAAK,EAAE,CAAC;CACtC,KAAK,KAAK,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACzB;AACA;CACA;CACA,KAAK,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC/B,KAAK,MAAM,EAAE,GAAG,SAAS,EAAE,SAAS,EAAE,CAAC;CACvC,KAAK,MAAM,EAAE,GAAG,SAAS,EAAE,UAAU,EAAE,CAAC;CACxC,KAAK,MAAM,KAAK,GAAG,SAAS,EAAE,KAAK,EAAE,CAAC;CACtC,KAAK,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;CAC/B,KAAK,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;AAC3B;CACA,KAAK,KAAK,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;CAC7B,KAAK,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;CAC9C,KAAK,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;AAC9C;CACA,KAAK,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;CAC3B,KAAK,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;CAC5C,KAAK,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;AAC5C;CACA,KAAK,KAAK,GAAG,CAAC;AACd;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACnC;CACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACpC;CACA,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CAClC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CAClC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAClC;CACA,KAAK,IAAI,CAAC,mBAAmB,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;CAChD,KAAK,MAAM,IAAI,GAAG,MAAM,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;CACrD,KAAK,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACrC;CACA,MAAM,iBAAiB,GAAG,IAAI,CAAC;CAC/B,MAAM,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;CAC5C,MAAM,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC5C;CACA,MAAM,KAAK,IAAI,GAAG,UAAU,GAAG,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AACxD;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG;AAClC;CACA,GAAG,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;CAC7B,GAAG,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG;AACtC;CACA,IAAI,MAAM,EAAE,GAAG,SAAS,EAAE,EAAE,EAAE,CAAC;CAC/B,IAAI,6BAA6B,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;CAC5D,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;CACpD,IAAI,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACpC;CACA,KAAK,iBAAiB,GAAG,IAAI,CAAC;CAC9B,KAAK,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;CAC3C,KAAK,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC3C;CACA,KAAK,KAAK,IAAI,GAAG,UAAU,GAAG,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AACvD;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,OAAO,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC;AACxC;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI;;CClaL;CACA;CACA,MAAM,EAAE,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CACzC,MAAM,EAAE,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CACzC,MAAM,EAAE,mBAAmB,IAAIA,aAAO,EAAE,CAAC;AACzC;CACA,MAAM,GAAG,mBAAmB,IAAIC,aAAO,EAAE,CAAC;CAC1C,MAAM,GAAG,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CAC1C,MAAM,GAAG,mBAAmB,IAAIA,aAAO,EAAE,CAAC;AAC1C;CACA,MAAM,iBAAiB,mBAAmB,IAAID,aAAO,EAAE,CAAC;CACxD,SAAS,iBAAiB,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,GAAG;AAC3D;CACA,CAAC,IAAI,SAAS,CAAC;CACf,CAAC,KAAK,IAAI,KAAKO,cAAQ,GAAG;AAC1B;CACA,EAAE,SAAS,GAAG,GAAG,CAAC,iBAAiB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AAC/D;CACA,EAAE,MAAM;AACR;CACA,EAAE,SAAS,GAAG,GAAG,CAAC,iBAAiB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,KAAKC,gBAAU,EAAE,KAAK,EAAE,CAAC;AAC9E;CACA,EAAE;AACF;CACA,CAAC,KAAK,SAAS,KAAK,IAAI,GAAG,OAAO,IAAI,CAAC;AACvC;CACA,CAAC,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC;AACjD;CACA,CAAC,OAAO;AACR;CACA,EAAE,QAAQ,EAAE,QAAQ;CACpB,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE;AACtB;CACA,EAAE,CAAC;AACH;CACA,CAAC;AACD;CACA,SAAS,+BAA+B,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG;AAC7E;CACA,CAAC,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;CACvC,CAAC,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;CACvC,CAAC,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;AACvC;CACA,CAAC,MAAM,YAAY,GAAG,iBAAiB,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,iBAAiB,EAAE,IAAI,EAAE,CAAC;AACpF;CACA,CAAC,KAAK,YAAY,GAAG;AACrB;CACA,EAAE,KAAK,EAAE,GAAG;AACZ;CACA,GAAG,GAAG,CAAC,mBAAmB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;CACpC,GAAG,GAAG,CAAC,mBAAmB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;CACpC,GAAG,GAAG,CAAC,mBAAmB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;AACpC;CACA,GAAG,YAAY,CAAC,EAAE,GAAGJ,cAAQ,CAAC,KAAK,EAAE,iBAAiB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAIH,aAAO,GAAG,EAAE,CAAC;AACpG;CACA,GAAG;AACH;CACA,EAAE,MAAM,IAAI,GAAG;CACf,GAAG,CAAC,EAAE,CAAC;CACP,GAAG,CAAC,EAAE,CAAC;CACP,GAAG,CAAC,EAAE,CAAC;CACP,GAAG,MAAM,EAAE,IAAID,aAAO,EAAE;CACxB,GAAG,aAAa,EAAE,CAAC;CACnB,GAAG,CAAC;AACJ;CACA,EAAEI,cAAQ,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;AAChD;CACA,EAAE,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC;CAC3B,EAAE,YAAY,CAAC,SAAS,GAAG,CAAC,CAAC;AAC7B;CACA,EAAE;AACF;CACA,CAAC,OAAO,YAAY,CAAC;AACrB;CACA,CAAC;AACD;CACA;CACA,SAAS,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,aAAa,GAAG;AAC5D;CACA,CAAC,MAAM,SAAS,GAAG,GAAG,GAAG,CAAC,CAAC;CAC3B,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC;CACvC,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,GAAG,CAAC,EAAE,CAAC;CAC3C,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,GAAG,CAAC,EAAE,CAAC;AAC3C;CACA,CAAC,MAAM,YAAY,GAAG,+BAA+B,EAAE,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC;AACxH;CACA,CAAC,KAAK,YAAY,GAAG;AACrB;CACA,EAAE,YAAY,CAAC,SAAS,GAAG,GAAG,CAAC;CAC/B,EAAE,KAAK,aAAa,GAAG,aAAa,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC;CAC1D,EAAE,OAAO,YAAY,CAAC;AACtB;CACA,EAAE;AACF;CACA,CAAC,OAAO,IAAI,CAAC;AACb;CACA;;CChGO,SAAS,aAAa,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,aAAa,GAAG;AAC9E;CACA,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG;AAC7D;CACA,EAAE,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC;AACnD;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,SAAS,mBAAmB,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,GAAG;AACrE;CACA,CAAC,IAAI,IAAI,GAAG,QAAQ,CAAC;CACrB,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC;CAChB,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG;AAC7D;CACA,EAAE,MAAM,YAAY,GAAG,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;CACzD,EAAE,KAAK,YAAY,IAAI,YAAY,CAAC,QAAQ,GAAG,IAAI,GAAG;AACtD;CACA,GAAG,GAAG,GAAG,YAAY,CAAC;CACtB,GAAG,IAAI,GAAG,YAAY,CAAC,QAAQ,CAAC;AAChC;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,GAAG,CAAC;AACZ;CACA,CAAC;AACD;CACA;CACA;CACO,SAAS,uBAAuB,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,GAAG;AAClE;CACA,CAAC,KAAK,GAAG,KAAK,IAAI,GAAG;AACrB;CACA,EAAE,OAAO,IAAI,CAAC;AACd;CACA,EAAE;AACF;CACA,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC;CAC9C,CAAC,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;CAC7D,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB;CACA,CAAC,KAAK,GAAG,CAAC,QAAQ,GAAG,SAAS,CAAC,IAAI,IAAI,GAAG,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,GAAG;AACtE;CACA,EAAE,OAAO,IAAI,CAAC;AACd;CACA,EAAE,MAAM;AACR;CACA,EAAE,OAAO,GAAG,CAAC;AACb;CACA,EAAE;AACF;CACA;;CCrDA;CACO,SAAS,WAAW,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,GAAG;AAClD;CACA,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;CAClB,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;CAClB,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAClB;CACA,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;CACZ,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;CAChB,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;CAChB,CAAC,KAAK,KAAK,GAAG;AACd;CACA,EAAE,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;CACvB,EAAE,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CAC3B,EAAE,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AAC3B;CACA,EAAE;AACF;CACA,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB;CACA,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB;CACA,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;CACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB;CACA,CAAC;AACD;CACO,SAAS,oBAAoB;CACpC,CAAC,MAAM;CACP,CAAC,KAAK;CACN,CAAC,QAAQ;CACT,CAAC,sBAAsB;CACvB,CAAC,SAAS;CACV,CAAC,KAAK;CACN,CAAC,QAAQ;CACT,EAAE;AACF;CACA,CAAC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;CAC9B,CAAC,MAAM,GAAG,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;CAC1C,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,KAAK,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACzD;CACA,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;CAC7C,EAAE,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AAC9B;CACA,EAAE,KAAK,sBAAsB,EAAE,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;AACjE;CACA,GAAG,OAAO,IAAI,CAAC;AACf;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,KAAK,CAAC;AACd;CACA,CAAC;AACD;CACA,MAAM,MAAM,mBAAmB,IAAIJ,aAAO,EAAE,CAAC;CAC7C,MAAM,MAAM,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CAC7C,MAAM,MAAM,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CAC7C,MAAM,OAAO,mBAAmB,IAAIC,aAAO,EAAE,CAAC;CAC9C,MAAM,OAAO,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CAC9C,MAAM,OAAO,mBAAmB,IAAIA,aAAO,EAAE,CAAC;AAC9C;CACO,SAAS,uBAAuB,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,GAAG;AAClF;CACA,CAAC,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC;CAC3C,CAAC,MAAM,SAAS,GAAG,QAAQ,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;CACvD,CAAC,MAAM,GAAG,GAAG,QAAQ,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC;AAC3C;CACA,CAAC,MAAM,CAAC,GAAG,OAAO,EAAE,aAAa,GAAG,CAAC,EAAE,CAAC;CACxC,CAAC,MAAM,CAAC,GAAG,OAAO,EAAE,aAAa,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CAC5C,CAAC,MAAM,CAAC,GAAG,OAAO,EAAE,aAAa,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AAC5C;CACA,CAAC,MAAM,CAAC,mBAAmB,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;CAC5C,CAAC,MAAM,CAAC,mBAAmB,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;CAC5C,CAAC,MAAM,CAAC,mBAAmB,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;AAC5C;CACA;CACA,CAAC,IAAI,aAAa,GAAG,CAAC,CAAC;CACvB,CAAC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;CAChC,CAAC,MAAM,gBAAgB,GAAG,aAAa,GAAG,CAAC,CAAC;CAC5C,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;CACA,EAAE,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CAC5B,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;CACjC,EAAE,KAAK,gBAAgB,IAAI,KAAK,IAAI,gBAAgB,GAAG,KAAK,GAAG,KAAK,GAAG;AACvE;CACA,GAAG,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;CACvC,GAAG,MAAM;AACT;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;CACA,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC;CACf,CAAC,KAAK,GAAG,GAAG;AACZ;CACA,EAAE,OAAO,CAAC,mBAAmB,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;CACxC,EAAE,OAAO,CAAC,mBAAmB,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;CACxC,EAAE,OAAO,CAAC,mBAAmB,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACxC;CACA,EAAE,KAAK,MAAM,IAAI,MAAM,CAAC,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;CAC5C,OAAO,EAAE,GAAG,IAAIA,aAAO,EAAE,CAAC;AAC1B;CACA,EAAEG,cAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;AACjF;CACA,EAAE;AACF;CACA;CACA,CAAC,KAAK,MAAM,GAAG;AACf;CACA,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC;CACzC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;CACpB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;CACpB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;CACpB,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;CAC5C,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,IAAIJ,aAAO,EAAE,CAAC;CACjE,EAAEI,cAAQ,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AACnE;CACA,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC;AAC3B;CACA,EAAE,OAAO,MAAM,CAAC;AAChB;CACA,EAAE,MAAM;AACR;CACA,EAAE,OAAO;CACT,GAAG,IAAI,EAAE;CACT,IAAI,CAAC,EAAE,CAAC;CACR,IAAI,CAAC,EAAE,CAAC;CACR,IAAI,CAAC,EAAE,CAAC;CACR,IAAI,aAAa,EAAE,aAAa;CAChC,IAAI,MAAM,EAAEA,cAAQ,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAIJ,aAAO,EAAE,EAAE;CACvE,IAAI;CACJ,GAAG,EAAE,EAAE,EAAE;CACT,GAAG,CAAC;AACJ;CACA,EAAE;AACF;CACA;;CCnJO,MAAM,aAAa,CAAC;AAC3B;CACA,CAAC,WAAW,EAAE,eAAe,GAAG;AAChC;CACA,EAAE,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;CAC1C,EAAE,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACxB;CACA,EAAE;AACF;CACA,CAAC,YAAY,GAAG;AAChB;CACA,EAAE,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;CACtC,EAAE,KAAK,UAAU,CAAC,MAAM,KAAK,CAAC,GAAG;AACjC;CACA,GAAG,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAClC;CACA,GAAG,MAAM;AACT;CACA,GAAG,OAAO,UAAU,CAAC,GAAG,EAAE,CAAC;AAC3B;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,gBAAgB,EAAE,SAAS,GAAG;AAC/B;CACA,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC;AACrC;CACA,EAAE;AACF;CACA;;CC9BO,SAAS,OAAO,EAAE,GAAG,EAAE,WAAW,GAAG;AAC5C;CACA,CAAC,OAAO,WAAW,EAAE,GAAG,GAAG,EAAE,EAAE,KAAK,MAAM,CAAC;AAC3C;CACA,CAAC;AACD;CACO,SAAS,MAAM,EAAE,GAAG,EAAE,WAAW,GAAG;AAC3C;CACA,CAAC,OAAO,WAAW,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;AAC/B;CACA,CAAC;AACD;CACO,SAAS,KAAK,EAAE,GAAG,EAAE,WAAW,GAAG;AAC1C;CACA,CAAC,OAAO,WAAW,EAAE,GAAG,GAAG,EAAE,EAAE,CAAC;AAChC;CACA,CAAC;AACD;CACO,SAAS,SAAS,EAAE,GAAG,GAAG;AACjC;CACA,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC;AAChB;CACA,CAAC;AACD;CACO,SAAS,UAAU,EAAE,GAAG,EAAE,WAAW,GAAG;AAC/C;CACA,CAAC,OAAO,WAAW,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;AAC/B;CACA,CAAC;AACD;CACO,SAAS,UAAU,EAAE,GAAG,EAAE,WAAW,GAAG;AAC/C;CACA,CAAC,OAAO,WAAW,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;AAC/B;CACA,CAAC;AACD;CACO,SAAS,mBAAmB,EAAE,GAAG,GAAG;AAC3C;CACA,CAAC,OAAO,GAAG,CAAC;AACZ;CACA;;CC7BA,MAAMS,aAAW,GAAG,IAAIC,UAAI,EAAE,CAAC;CAC/B,MAAM,eAAe,GAAG,IAAIV,aAAO,EAAE,CAAC;CACtC,MAAM,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACpC;CACO,SAAS,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,GAAG;AACxE;CACA,CAAC,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,EAAE,YAAY,GAAG,aAAa,EAAE,WAAW,GAAG,YAAY,EAAE,WAAW,GAAG,YAAY,CAAC;AACzH;CACA,CAAC,MAAM,MAAM,GAAG,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,CAAC,KAAK,MAAM,GAAG;AACf;CACA,EAAE,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,EAAE,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAClD;CACA,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;AAClE;CACA,EAAE,MAAM;AACR;CACA,EAAE,MAAM,SAAS,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;CAC7C,EAAE,KAAK,YAAY,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,EAAE,eAAe,EAAE,GAAG;AACvE;CACA,GAAG,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;AACzD;CACA,GAAG;AACH;CACA,EAAE,MAAM,UAAU,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CAC5D,EAAE,KAAK,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,EAAE,eAAe,EAAE,GAAG;AACxE;CACA,GAAG,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;AAC1D;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,SAAS,YAAY,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,GAAG;AACjE;CACA,CAAC,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,EAAE,YAAY,GAAG,aAAa,EAAE,WAAW,GAAG,YAAY,EAAE,WAAW,GAAG,YAAY,CAAC;AACzH;CACA,CAAC,MAAM,MAAM,GAAG,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,CAAC,KAAK,MAAM,GAAG;AACf;CACA,EAAE,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACpD,EAAE,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CAClD,EAAE,OAAO,mBAAmB,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AACnE;CACA,EAAE,MAAM;AACR;CACA;CACA;CACA,EAAE,MAAM,SAAS,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CAC3D,EAAE,MAAM,OAAO,GAAG,SAAS,EAAE,SAAS,EAAE,CAAC;CACzC,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC;CAC1C,EAAE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,CAAC;AAClC;CACA;CACA,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;CACb,EAAE,KAAK,WAAW,GAAG;AACrB;CACA,GAAG,EAAE,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;CACjC,GAAG,EAAE,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAC/C;CACA,GAAG,MAAM;AACT;CACA,GAAG,EAAE,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CAC/C,GAAG,EAAE,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;AACjC;CACA,GAAG;AACH;CACA,EAAE,MAAM,cAAc,GAAG,YAAY,EAAE,EAAE,EAAE,YAAY,EAAE,GAAG,EAAE,eAAe,EAAE,CAAC;CAChF,EAAE,MAAM,QAAQ,GAAG,cAAc,GAAG,YAAY,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AACnF;CACA;CACA;CACA,EAAE,KAAK,QAAQ,GAAG;AAClB;CACA;CACA;CACA,GAAG,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC;CAC3C,GAAG,MAAM,SAAS,GAAG,WAAW;CAChC,IAAI,KAAK,IAAI,YAAY,EAAE,EAAE,GAAG,SAAS,EAAE;CAC3C,IAAI,KAAK,IAAI,YAAY,EAAE,EAAE,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC;AAChD;CACA,GAAG,KAAK,SAAS,GAAG;AACpB;CACA,IAAI,OAAO,QAAQ,CAAC;AACpB;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA;CACA,EAAE,MAAM,cAAc,GAAG,YAAY,EAAE,EAAE,EAAE,YAAY,EAAE,GAAG,EAAE,eAAe,EAAE,CAAC;CAChF,EAAE,MAAM,QAAQ,GAAG,cAAc,GAAG,YAAY,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AACnF;CACA,EAAE,KAAK,QAAQ,IAAI,QAAQ,GAAG;AAC9B;CACA,GAAG,OAAO,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AACvE;CACA,GAAG,MAAM;AACT;CACA,GAAG,OAAO,QAAQ,IAAI,QAAQ,IAAI,IAAI,CAAC;AACvC;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,MAAM,SAAS,GAAG,EAAE,YAAY;AACvC;CACA,CAAC,IAAI,KAAK,EAAE,KAAK,CAAC;CAClB,CAAC,MAAM,QAAQ,GAAG,EAAE,CAAC;CACrB,CAAC,MAAM,OAAO,GAAG,IAAI,aAAa,EAAE,MAAM,IAAIU,UAAI,EAAE,EAAE,CAAC;AACvD;CACA,CAAC,OAAO,SAAS,SAAS,EAAE,GAAG,IAAI,GAAG;AACtC;CACA,EAAE,KAAK,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;CACjC,EAAE,KAAK,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;CACjC,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AAChC;CACA,EAAE,MAAM,MAAM,GAAG,iBAAiB,EAAE,GAAG,IAAI,EAAE,CAAC;AAC9C;CACA,EAAE,OAAO,CAAC,gBAAgB,EAAE,KAAK,EAAE,CAAC;CACpC,EAAE,OAAO,CAAC,gBAAgB,EAAE,KAAK,EAAE,CAAC;CACpC,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC;CACjB,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC;AACjB;CACA,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;CACjC,EAAE,KAAK,MAAM,GAAG,CAAC,GAAG;AACpB;CACA,GAAG,KAAK,GAAG,QAAQ,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;CAClC,GAAG,KAAK,GAAG,QAAQ,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;AAClC;CACA,GAAG;AACH;CACA,EAAE,OAAO,MAAM,CAAC;AAChB;CACA,EAAE,CAAC;AACH;CACA,CAAC,SAAS,iBAAiB;CAC3B,EAAE,WAAW;CACb,EAAE,QAAQ;CACV,EAAE,oBAAoB;CACtB,EAAE,mBAAmB;CACrB,EAAE,aAAa,GAAG,IAAI;CACtB,EAAE,mBAAmB,GAAG,CAAC;CACzB,EAAE,KAAK,GAAG,CAAC;CACX,GAAG;AACH;CACA;CACA;CACA,EAAE,SAAS,aAAa,EAAE,WAAW,GAAG;AACxC;CACA,GAAG,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,YAAY,EAAE,WAAW,GAAG,YAAY,CAAC;AAC7F;CACA;CACA,GAAG,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;AACnD;CACA,IAAI,WAAW,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;CAC3C,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;AAClC;CACA,IAAI;AACJ;CACA,GAAG,OAAO,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAC7C;CACA,GAAG;AACH;CACA,EAAE,SAAS,iBAAiB,EAAE,WAAW,GAAG;AAC5C;CACA,GAAG,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,YAAY,EAAE,WAAW,GAAG,YAAY,CAAC;AAC7F;CACA;CACA,GAAG,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;AACnD;CACA;CACA,IAAI,WAAW,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACzD,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;AAClC;CACA,IAAI;AACJ;CACA;CACA,GAAG,OAAO,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACjF;CACA,GAAG;AACH;CACA,EAAE,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,EAAE,YAAY,GAAG,aAAa,EAAE,WAAW,GAAG,YAAY,EAAE,WAAW,GAAG,YAAY,CAAC;AAC1H;CACA,EAAE,MAAM,MAAM,GAAG,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACrD,EAAE,KAAK,MAAM,GAAG;AAChB;CACA,GAAG,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACrD,GAAG,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACnD,GAAG,UAAU,EAAE,mBAAmB,EAAE,WAAW,EAAE,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;CACzE,GAAG,OAAO,mBAAmB,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,GAAG,WAAW,EAAE,KAAK,EAAE,CAAC;AACvG;CACA,GAAG,MAAM;AACT;CACA,GAAG,MAAM,IAAI,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;CACzC,GAAG,MAAM,KAAK,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACxD,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC;CACjB,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC;AAClB;CACA,GAAG,IAAI,MAAM,EAAE,MAAM,CAAC;CACtB,GAAG,IAAI,IAAI,EAAE,IAAI,CAAC;CAClB,GAAG,KAAK,aAAa,GAAG;AACxB;CACA,IAAI,IAAI,GAAG,KAAK,CAAC;CACjB,IAAI,IAAI,GAAG,KAAK,CAAC;AACjB;CACA;CACA,IAAI,UAAU,EAAE,mBAAmB,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;CAChE,IAAI,UAAU,EAAE,mBAAmB,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;AAChE;CACA,IAAI,MAAM,GAAG,aAAa,EAAE,IAAI,EAAE,CAAC;CACnC,IAAI,MAAM,GAAG,aAAa,EAAE,IAAI,EAAE,CAAC;AACnC;CACA,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG;AAC3B;CACA,KAAK,EAAE,GAAG,KAAK,CAAC;CAChB,KAAK,EAAE,GAAG,IAAI,CAAC;AACf;CACA,KAAK,MAAM,IAAI,GAAG,MAAM,CAAC;CACzB,KAAK,MAAM,GAAG,MAAM,CAAC;CACrB,KAAK,MAAM,GAAG,IAAI,CAAC;AACnB;CACA,KAAK,IAAI,GAAG,IAAI,CAAC;CACjB;AACA;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA;CACA,GAAG,KAAK,EAAE,IAAI,GAAG;AACjB;CACA,IAAI,IAAI,GAAG,KAAK,CAAC;CACjB,IAAI,UAAU,EAAE,mBAAmB,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;AAChE;CACA,IAAI;AACJ;CACA,GAAG,MAAM,QAAQ,GAAG,OAAO,EAAE,EAAE,GAAG,CAAC,EAAE,WAAW,EAAE,CAAC;CACnD,GAAG,MAAM,cAAc,GAAG,oBAAoB,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,mBAAmB,GAAG,EAAE,EAAE,CAAC;AAC9G;CACA,GAAG,IAAI,eAAe,CAAC;CACvB,GAAG,KAAK,cAAc,KAAK,SAAS,GAAG;AACvC;CACA,IAAI,MAAM,MAAM,GAAG,aAAa,EAAE,EAAE,EAAE,CAAC;CACvC,IAAI,MAAM,GAAG,GAAG,iBAAiB,EAAE,EAAE,EAAE,CAAC;CACxC,IAAI,MAAM,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC;AAC/B;CACA,IAAI,eAAe,GAAG,mBAAmB,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,mBAAmB,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC;AAC5G;CACA,IAAI,MAAM;AACV;CACA,IAAI,eAAe;CACnB,KAAK,cAAc;CACnB,KAAK,iBAAiB;CACtB,MAAM,EAAE;CACR,MAAM,QAAQ;CACd,MAAM,oBAAoB;CAC1B,MAAM,mBAAmB;CACzB,MAAM,aAAa;CACnB,MAAM,mBAAmB;CACzB,MAAM,KAAK,GAAG,CAAC;CACf,MAAM,CAAC;AACP;CACA,IAAI;AACJ;CACA,GAAG,KAAK,eAAe,GAAG,OAAO,IAAI,CAAC;AACtC;CACA;CACA;CACA,GAAG,IAAI,GAAG,KAAK,CAAC;CAChB,GAAG,UAAU,EAAE,mBAAmB,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;AAC/D;CACA,GAAG,MAAM,QAAQ,GAAG,OAAO,EAAE,EAAE,GAAG,CAAC,EAAE,WAAW,EAAE,CAAC;CACnD,GAAG,MAAM,cAAc,GAAG,oBAAoB,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,mBAAmB,GAAG,EAAE,EAAE,CAAC;AAC9G;CACA,GAAG,IAAI,eAAe,CAAC;CACvB,GAAG,KAAK,cAAc,KAAK,SAAS,GAAG;AACvC;CACA,IAAI,MAAM,MAAM,GAAG,aAAa,EAAE,EAAE,EAAE,CAAC;CACvC,IAAI,MAAM,GAAG,GAAG,iBAAiB,EAAE,EAAE,EAAE,CAAC;CACxC,IAAI,MAAM,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC;AAC/B;CACA,IAAI,eAAe,GAAG,mBAAmB,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,mBAAmB,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC;AAC5G;CACA,IAAI,MAAM;AACV;CACA,IAAI,eAAe;CACnB,KAAK,cAAc;CACnB,KAAK,iBAAiB;CACtB,MAAM,EAAE;CACR,MAAM,QAAQ;CACd,MAAM,oBAAoB;CAC1B,MAAM,mBAAmB;CACzB,MAAM,aAAa;CACnB,MAAM,mBAAmB;CACzB,MAAM,KAAK,GAAG,CAAC;CACf,MAAM,CAAC;AACP;CACA,IAAI;AACJ;CACA,GAAG,KAAK,eAAe,GAAG,OAAO,IAAI,CAAC;AACtC;CACA,GAAG,OAAO,KAAK,CAAC;AAChB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,IAAI,CAAC;AACN;CACO,MAAM,kBAAkB,GAAG,EAAE,YAAY;AAChD;CACA,CAAC,MAAM,QAAQ,GAAG,IAAI,gBAAgB,EAAE,CAAC;CACzC,CAAC,MAAM,SAAS,GAAG,IAAI,gBAAgB,EAAE,CAAC;CAC1C,CAAC,MAAM,WAAW,GAAG,IAAIJ,aAAO,EAAE,CAAC;AACnC;CACA,CAAC,MAAM,GAAG,GAAG,IAAI,WAAW,EAAE,CAAC;CAC/B,CAAC,MAAM,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC;AAChC;CACA,CAAC,OAAO,SAAS,kBAAkB,EAAE,WAAW,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,GAAG,IAAI,GAAG;AAC7G;CACA,EAAE,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,EAAE,YAAY,GAAG,aAAa,EAAE,WAAW,GAAG,YAAY,EAAE,WAAW,GAAG,YAAY,CAAC;AAC1H;CACA,EAAE,KAAK,SAAS,KAAK,IAAI,GAAG;AAC5B;CACA,GAAG,KAAK,EAAE,aAAa,CAAC,WAAW,GAAG;AACtC;CACA,IAAI,aAAa,CAAC,kBAAkB,EAAE,CAAC;AACvC;CACA,IAAI;AACJ;CACA,GAAG,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,EAAE,CAAC;CAC1F,GAAG,SAAS,GAAG,GAAG,CAAC;AACnB;CACA,GAAG;AACH;CACA,EAAE,MAAM,MAAM,GAAG,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACrD,EAAE,KAAK,MAAM,GAAG;AAChB;CACA,GAAG,MAAM,YAAY,GAAG,QAAQ,CAAC;CACjC,GAAG,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC;CACxC,GAAG,MAAM,OAAO,GAAG,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC;AACpD;CACA,GAAG,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;CACrC,GAAG,MAAM,GAAG,GAAG,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;AACjD;CACA,GAAG,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACrD,GAAG,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACnD;CACA;CACA;CACA;CACA,GAAG,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC;AAC9C;CACA,GAAG,KAAK,aAAa,CAAC,UAAU,GAAG;AACnC;CACA,IAAI,UAAU,EAAE,mBAAmB,EAAE,WAAW,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;CACzE,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;CACpC,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B;CACA,IAAI,MAAM,GAAG,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,EAAE;AACpD;CACA,KAAK,gBAAgB,EAAE,GAAG,IAAI,IAAI,CAAC,aAAa,EAAE,GAAG,EAAE;AACvD;CACA,KAAK,kBAAkB,EAAE,GAAG,IAAI;AAChC;CACA,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAC1C,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAC1C,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAC1C,MAAM,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC;AAC7B;CACA,MAAM,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AAC5E;CACA;CACA,OAAO,WAAW,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;CACvD,OAAO,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;CACpC,OAAO,KAAK,GAAG,CAAC,kBAAkB,EAAE,SAAS,EAAE,GAAG;AAClD;CACA,QAAQ,OAAO,IAAI,CAAC;AACpB;CACA,QAAQ;AACR;CACA,OAAO;AACP;CACA,MAAM,OAAO,KAAK,CAAC;AACnB;CACA,MAAM;AACN;CACA,KAAK,EAAE,CAAC;AACR;CACA,IAAI,OAAO,GAAG,CAAC;AACf;CACA,IAAI,MAAM;AACV;CACA,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,KAAK,KAAK,GAAG,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AAC1E;CACA;CACA,KAAK,WAAW,EAAE,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;CACpD,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,EAAE,WAAW,EAAE,CAAC;CAC5C,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,EAAE,WAAW,EAAE,CAAC;CAC5C,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,EAAE,WAAW,EAAE,CAAC;CAC5C,KAAK,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AACjC;CACA,KAAK,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG;AAC5D;CACA,MAAM,WAAW,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;CAC/C,MAAM,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;AACnC;CACA,MAAM,KAAK,QAAQ,CAAC,kBAAkB,EAAE,SAAS,EAAE,GAAG;AACtD;CACA,OAAO,OAAO,IAAI,CAAC;AACnB;CACA,OAAO;AACP;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG,MAAM;AACT;CACA,GAAG,MAAM,IAAI,GAAG,WAAW,GAAG,CAAC,CAAC;CAChC,GAAG,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AAChD;CACA,GAAG,UAAU,EAAE,mBAAmB,EAAE,IAAI,EAAE,EAAE,YAAY,EAAEG,aAAW,EAAE,CAAC;CACxE,GAAG,MAAM,gBAAgB;CACzB,IAAI,SAAS,CAAC,aAAa,EAAEA,aAAW,EAAE;CAC1C,IAAI,kBAAkB,EAAE,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;AAClF;CACA,GAAG,KAAK,gBAAgB,GAAG,OAAO,IAAI,CAAC;AACvC;CACA,GAAG,UAAU,EAAE,mBAAmB,EAAE,KAAK,EAAE,EAAE,YAAY,EAAEA,aAAW,EAAE,CAAC;CACzE,GAAG,MAAM,iBAAiB;CAC1B,IAAI,SAAS,CAAC,aAAa,EAAEA,aAAW,EAAE;CAC1C,IAAI,kBAAkB,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;AACnF;CACA,GAAG,KAAK,iBAAiB,GAAG,OAAO,IAAI,CAAC;AACxC;CACA,GAAG,OAAO,KAAK,CAAC;AAChB;CACA,GAAG;AACH;CACA,EAAE,CAAC;AACH;CACA,CAAC,IAAI,CAAC;AACN;CACA,SAAS,YAAY,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,GAAG;AACzD;CACA,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,EAAEA,aAAW,EAAE,CAAC;CAC/C,CAAC,OAAO,GAAG,CAAC,YAAY,EAAEA,aAAW,EAAE,MAAM,EAAE,CAAC;AAChD;CACA,CAAC;AACD;CACA,MAAM,WAAW,GAAG,EAAE,CAAC;CACvB,IAAI,WAAW,CAAC;CAChB,IAAI,aAAa,CAAC;CAClB,IAAI,YAAY,CAAC;CACjB,IAAI,YAAY,CAAC;CACV,SAAS,SAAS,EAAE,MAAM,GAAG;AACpC;CACA,CAAC,KAAK,WAAW,GAAG;AACpB;CACA,EAAE,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAClC;CACA,EAAE;AACF;CACA,CAAC,WAAW,GAAG,MAAM,CAAC;CACtB,CAAC,aAAa,GAAG,IAAI,YAAY,EAAE,MAAM,EAAE,CAAC;CAC5C,CAAC,YAAY,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;CAC1C,CAAC,YAAY,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;AAC1C;CACA,CAAC;AACD;CACO,SAAS,WAAW,GAAG;AAC9B;CACA,CAAC,WAAW,GAAG,IAAI,CAAC;CACpB,CAAC,aAAa,GAAG,IAAI,CAAC;CACtB,CAAC,YAAY,GAAG,IAAI,CAAC;CACrB,CAAC,YAAY,GAAG,IAAI,CAAC;AACrB;CACA,CAAC,KAAK,WAAW,CAAC,MAAM,GAAG;AAC3B;CACA,EAAE,SAAS,EAAE,WAAW,CAAC,GAAG,EAAE,EAAE,CAAC;AACjC;CACA,EAAE;AACF;CACA;;CCveA,MAAM,eAAe,GAAG,MAAM,EAAE,sBAAsB,EAAE,CAAC;AACzD;CACA,MAAM,IAAI,mBAAmB,IAAIC,UAAI,EAAE,CAAC;CACxC,MAAM,KAAK,mBAAmB,IAAIA,UAAI,EAAE,CAAC;CACzC,MAAM,UAAU,mBAAmB,IAAIJ,aAAO,EAAE,CAAC;CACjD,MAAM,GAAG,mBAAmB,IAAI,WAAW,EAAE,CAAC;CAC9C,MAAM,IAAI,mBAAmB,IAAI,WAAW,EAAE,CAAC;CAC/C,MAAM,IAAI,mBAAmB,IAAIN,aAAO,EAAE,CAAC;CAC3C,MAAM,KAAK,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CAC5C,MAAM,KAAK,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CAC5C,MAAM,KAAK,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CAC5C,MAAM,KAAK,mBAAmB,IAAIA,aAAO,EAAE,CAAC;CAC5C,MAAM,OAAO,mBAAmB,IAAIU,UAAI,EAAE,CAAC;CAC3C,MAAM,YAAY,mBAAmB,IAAI,aAAa,EAAE,MAAM,IAAI,gBAAgB,EAAE,EAAE,CAAC;AACvF;CACO,MAAM,OAAO,CAAC;AACrB;CACA,CAAC,OAAO,SAAS,EAAE,GAAG,EAAE,OAAO,GAAG,EAAE,GAAG;AACvC;CACA,EAAE,KAAK,OAAO,CAAC,gBAAgB,GAAG;AAClC;CACA,GAAG,OAAO,CAAC,IAAI,EAAE,sGAAsG,EAAE,CAAC;AAC1H;CACA,GAAG,OAAO,OAAO,CAAC,SAAS;CAC3B,IAAI,SAAS,EAAE,CAAC,EAAE;CAClB,IAAI;CACJ,KAAK,YAAY,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,SAAS,GAAG,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE;CACvE,KAAK;CACL,IAAI,CAAC;AACL;CACA,GAAG;AACH;CACA,EAAE,OAAO,GAAG;CACZ,GAAG,YAAY,EAAE,IAAI;CACrB,GAAG,GAAG,OAAO;CACb,GAAG,CAAC;AACJ;CACA,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;CAChC,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC;CAC9B,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;CAC7C,EAAE,IAAI,MAAM,CAAC;CACb,EAAE,KAAK,OAAO,CAAC,YAAY,GAAG;AAC9B;CACA,GAAG,MAAM,GAAG;CACZ,IAAI,KAAK,EAAE,QAAQ,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE;CAC/C,IAAI,KAAK,EAAE,cAAc,CAAC,KAAK,CAAC,KAAK,EAAE;CACvC,IAAI,CAAC;AACL;CACA,GAAG,MAAM;AACT;CACA,GAAG,MAAM,GAAG;CACZ,IAAI,KAAK,EAAE,QAAQ;CACnB,IAAI,KAAK,EAAE,cAAc,CAAC,KAAK;CAC/B,IAAI,CAAC;AACL;CACA,GAAG;AACH;CACA,EAAE,OAAO,MAAM,CAAC;AAChB;CACA,EAAE;AACF;CACA,CAAC,OAAO,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,GAAG,EAAE,GAAG;AACpD;CACA,EAAE,KAAK,OAAO,OAAO,KAAK,SAAS,GAAG;AACtC;CACA,GAAG,OAAO,CAAC,IAAI,EAAE,wGAAwG,EAAE,CAAC;AAC5H;CACA,GAAG,OAAO,OAAO,CAAC,WAAW;CAC7B,IAAI,SAAS,EAAE,CAAC,EAAE;CAClB,IAAI,SAAS,EAAE,CAAC,EAAE;CAClB,IAAI;CACJ,KAAK,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,SAAS,GAAG,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE;CACnE,KAAK;CACL,IAAI,CAAC;AACL;CACA,GAAG;AACH;CACA,EAAE,OAAO,GAAG;CACZ,GAAG,QAAQ,EAAE,IAAI;CACjB,GAAG,GAAG,OAAO;CACb,GAAG,CAAC;AACJ;CACA,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;CAChC,EAAE,MAAM,GAAG,GAAG,IAAI,OAAO,EAAE,QAAQ,EAAE,EAAE,GAAG,OAAO,EAAE,EAAE,eAAe,IAAI,IAAI,EAAE,EAAE,CAAC;CACjF,EAAE,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC;AACrB;CACA,EAAE,KAAK,OAAO,CAAC,QAAQ,GAAG;AAC1B;CACA,GAAG,MAAM,cAAc,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;CAC9C,GAAG,KAAK,cAAc,KAAK,IAAI,GAAG;AAClC;CACA,IAAI,MAAM,QAAQ,GAAG,IAAIX,qBAAe,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC;CACjE,IAAI,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC;AAClC;CACA,IAAI,MAAM,KAAK,cAAc,CAAC,KAAK,KAAK,KAAK,GAAG;AAChD;CACA,IAAI,cAAc,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC;CACtC,IAAI,cAAc,CAAC,WAAW,GAAG,IAAI,CAAC;AACtC;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,OAAO,GAAG,CAAC;AACb;CACA,EAAE;AACF;CACA,CAAC,WAAW,EAAE,QAAQ,EAAE,OAAO,GAAG,EAAE,GAAG;AACvC;CACA,EAAE,KAAK,EAAE,QAAQ,CAAC,gBAAgB,GAAG;AACrC;CACA,GAAG,MAAM,IAAI,KAAK,EAAE,+CAA+C,EAAE,CAAC;AACtE;CACA,GAAG,MAAM,KAAK,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,4BAA4B,GAAG;AAC9E;CACA,GAAG,MAAM,IAAI,KAAK,EAAE,+EAA+E,EAAE,CAAC;AACtG;CACA,GAAG;AACH;CACA;CACA,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE;AAC3B;CACA,GAAG,QAAQ,EAAE,MAAM;CACnB,GAAG,QAAQ,EAAE,EAAE;CACf,GAAG,WAAW,EAAE,EAAE;CAClB,GAAG,OAAO,EAAE,IAAI;CAChB,GAAG,oBAAoB,EAAE,KAAK;CAC9B,GAAG,cAAc,EAAE,IAAI;CACvB,GAAG,UAAU,EAAE,IAAI;AACnB;CACA;AACA;CACA;CACA,GAAG,EAAE,eAAe,IAAI,KAAK;AAC7B;CACA,GAAG,EAAE,OAAO,EAAE,CAAC;AACf;CACA,EAAE,KAAK,OAAO,CAAC,oBAAoB,IAAI,OAAO,iBAAiB,KAAK,WAAW,GAAG;AAClF;CACA,GAAG,MAAM,IAAI,KAAK,EAAE,8CAA8C,EAAE,CAAC;AACrE;CACA,GAAG;AACH;CACA,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;CACrB,EAAE,KAAK,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG;AACtC;CACA,GAAG,IAAI,CAAC,MAAM,GAAG,eAAe,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;AACtD;CACA,GAAG,KAAK,EAAE,QAAQ,CAAC,WAAW,IAAI,OAAO,CAAC,cAAc,GAAG;AAC3D;CACA,IAAI,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,IAAIW,UAAI,EAAE,EAAE,CAAC;AAC7D;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA;CACA,EAAE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC3B;CACA,EAAE;AACF;CACA,CAAC,KAAK,EAAE,WAAW,GAAG,IAAI,GAAG;AAC7B;CACA,EAAE,KAAK,WAAW,IAAI,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,GAAG;AACrD;CACA,GAAG,WAAW,GAAG,IAAI,GAAG,EAAE,WAAW,EAAE,CAAC;AACxC;CACA,GAAG;AACH;CACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC;CACxC,EAAE,MAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;AAC/C;CACA,EAAE,IAAI,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;CACrD,EAAE,IAAI,UAAU,GAAG,CAAC,CAAC;CACrB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;CAC5B,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;CACA,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC;CACvB,GAAG,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;CAC3C,GAAG,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;CAC3C,GAAG,YAAY,GAAG,IAAI,YAAY,EAAE,MAAM,EAAE,CAAC;AAC7C;CACA,GAAG,SAAS,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC;CAC9B,GAAG,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC;AACnC;CACA,GAAG;AACH;CACA,EAAE,SAAS,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,GAAG,KAAK,GAAG;AAC/D;CACA,GAAG,MAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;CACvC,GAAG,MAAM,MAAM,GAAG,WAAW,EAAE,WAAW,GAAG,EAAE,EAAE,KAAK,gBAAgB,CAAC;CACvE,GAAG,KAAK,MAAM,GAAG;AACjB;CACA,IAAI,MAAM,MAAM,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;CAClD,IAAI,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,EAAE,EAAE,CAAC;AAClD;CACA,IAAI,IAAI,IAAI,GAAG,QAAQ,CAAC;CACxB,IAAI,IAAI,IAAI,GAAG,QAAQ,CAAC;CACxB,IAAI,IAAI,IAAI,GAAG,QAAQ,CAAC;CACxB,IAAI,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;CAC1B,IAAI,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;CAC1B,IAAI,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;AAC1B;CACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,KAAK,MAAM,GAAG,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACxE;CACA,KAAK,MAAM,KAAK,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;CACjC,KAAK,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;CACrC,KAAK,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;CACrC,KAAK,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACrC;CACA,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;CAC9B,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AAC9B;CACA,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;CAC9B,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AAC9B;CACA,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;CAC9B,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AAC9B;CACA,KAAK;AACL;CACA,IAAI;CACJ,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;CAC7C,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;CAC7C,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;AAC7C;CACA,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;CAC7C,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;CAC7C,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;CAC7C,MAAM;AACN;CACA,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;CAC5C,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;CAC5C,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC5C;CACA,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;CAC5C,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;CAC5C,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC5C;CACA,KAAK,OAAO,IAAI,CAAC;AACjB;CACA,KAAK,MAAM;AACX;CACA,KAAK,OAAO,KAAK,CAAC;AAClB;CACA,KAAK;AACL;CACA,IAAI,MAAM;AACV;CACA,IAAI,MAAM,IAAI,GAAG,WAAW,GAAG,CAAC,CAAC;CACjC,IAAI,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AACjD;CACA;CACA;CACA,IAAI,MAAM,UAAU,GAAG,IAAI,GAAG,UAAU,CAAC;CACzC,IAAI,MAAM,WAAW,GAAG,KAAK,GAAG,UAAU,CAAC;CAC3C,IAAI,IAAI,aAAa,GAAG,KAAK,CAAC;CAC9B,IAAI,IAAI,YAAY,GAAG,KAAK,CAAC;CAC7B,IAAI,IAAI,aAAa,GAAG,KAAK,CAAC;AAC9B;CACA,IAAI,KAAK,WAAW,GAAG;AACvB;CACA;CACA;CACA,KAAK,KAAK,EAAE,aAAa,GAAG;AAC5B;CACA,MAAM,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE,UAAU,EAAE,CAAC;CACnD,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,WAAW,EAAE,CAAC;CACrD,MAAM,aAAa,GAAG,EAAE,YAAY,IAAI,EAAE,aAAa,CAAC;AACxD;CACA,MAAM;AACN;CACA,KAAK,MAAM;AACX;CACA,KAAK,YAAY,GAAG,IAAI,CAAC;CACzB,KAAK,aAAa,GAAG,IAAI,CAAC;AAC1B;CACA,KAAK;AACL;CACA,IAAI,MAAM,YAAY,GAAG,aAAa,IAAI,YAAY,CAAC;CACvD,IAAI,MAAM,aAAa,GAAG,aAAa,IAAI,aAAa,CAAC;AACzD;CACA,IAAI,IAAI,UAAU,GAAG,KAAK,CAAC;CAC3B,IAAI,KAAK,YAAY,GAAG;AACxB;CACA,KAAK,UAAU,GAAG,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;AAC/D;CACA,KAAK;AACL;CACA,IAAI,IAAI,WAAW,GAAG,KAAK,CAAC;CAC5B,IAAI,KAAK,aAAa,GAAG;AACzB;CACA,KAAK,WAAW,GAAG,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;AACjE;CACA,KAAK;AACL;CACA,IAAI,MAAM,SAAS,GAAG,UAAU,IAAI,WAAW,CAAC;CAChD,IAAI,KAAK,SAAS,GAAG;AACrB;CACA,KAAK,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACpC;CACA,MAAM,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;CAC7B,MAAM,MAAM,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;CAC/B,MAAM,MAAM,YAAY,GAAG,YAAY,EAAE,KAAK,EAAE,CAAC;CACjD,MAAM,MAAM,YAAY,GAAG,YAAY,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;CACrD,MAAM,MAAM,aAAa,GAAG,YAAY,EAAE,MAAM,EAAE,CAAC;CACnD,MAAM,MAAM,aAAa,GAAG,YAAY,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;AACvD;CACA,MAAM,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,YAAY,GAAG,aAAa,GAAG,YAAY,GAAG,aAAa,CAAC;CACpG,MAAM,YAAY,EAAE,WAAW,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,GAAG,aAAa,GAAG,YAAY,GAAG,aAAa,CAAC;AACxG;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA,IAAI,OAAO,SAAS,CAAC;AACrB;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,GAAG,CAAC,GAAG;AACrC;CACA,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC;CAC1C,EAAE,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;CAChD,EAAE,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;CAChD,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;AACjB;CACA,EAAE,SAAS,SAAS,EAAE,WAAW,EAAE,KAAK,GAAG,CAAC,GAAG;AAC/C;CACA,GAAG,MAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;CACvC,GAAG,MAAM,MAAM,GAAG,WAAW,EAAE,WAAW,GAAG,EAAE,EAAE,KAAK,gBAAgB,CAAC;CACvE,GAAG,KAAK,MAAM,GAAG;AACjB;CACA,IAAI,MAAM,MAAM,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;CAClD,IAAI,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,EAAE,EAAE,CAAC;CAClD,IAAI,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,YAAY,EAAE,MAAM,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AAC7F;CACA,IAAI,MAAM;AACV;CACA;CACA,IAAI,MAAM,IAAI,GAAG,WAAW,GAAG,cAAc,GAAG,CAAC,CAAC;CAClD,IAAI,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;CACjD,IAAI,MAAM,SAAS,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;CACrD,IAAI,MAAM,aAAa,GAAG,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,YAAY,EAAE,MAAM,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC;AAC/G;CACA,IAAI,KAAK,EAAE,aAAa,GAAG;AAC3B;CACA,KAAK,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;CAClC,KAAK,SAAS,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;AACnC;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;CACA,CAAC,OAAO,EAAE,GAAG,EAAE,cAAc,GAAGC,eAAS,GAAG;AAC5C;CACA,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;CAC5B,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,UAAU,GAAG,EAAE,CAAC;CACxB,EAAE,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;CAC/C,EAAE,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,EAAE,cAAc,EAAE,CAAC;AAC1D;CACA,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;CACjC,EAAE,MAAM,IAAI,GAAG,UAAU,GAAG,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC;CACjE,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;CACA,GAAG,MAAM,YAAY,GAAG,eAAe,GAAG,cAAc,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;CAClG,GAAG,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;AACxC;CACA,GAAG,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;CAC3B,GAAG,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;CACzD,GAAG,WAAW,EAAE,CAAC;AACjB;CACA,GAAG,KAAK,eAAe,GAAG;AAC1B;CACA,IAAI,MAAM,aAAa,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,aAAa,CAAC;CACpD,IAAI,MAAM,IAAI,CAAC,GAAG,UAAU,EAAE,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG;AACrE;CACA,KAAK,UAAU,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACxD;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,OAAO,UAAU,CAAC;AACpB;CACA,EAAE;AACF;CACA,CAAC,YAAY,EAAE,GAAG,EAAE,cAAc,GAAGA,eAAS,GAAG;AACjD;CACA,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;CAC5B,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;CAC/C,EAAE,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,EAAE,cAAc,EAAE,CAAC;AAC1D;CACA,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC;AAC3B;CACA,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;CACjC,EAAE,MAAM,IAAI,GAAG,UAAU,GAAG,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC;CACjE,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;CACA,GAAG,MAAM,YAAY,GAAG,eAAe,GAAG,cAAc,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;AAClG;CACA,GAAG,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;CAC3B,GAAG,MAAM,MAAM,GAAG,YAAY,EAAE,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,EAAE,CAAC;CACjE,GAAG,WAAW,EAAE,CAAC;AACjB;CACA,GAAG,KAAK,MAAM,IAAI,IAAI,MAAM,aAAa,IAAI,IAAI,IAAI,MAAM,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,EAAE,GAAG;AAClG;CACA,IAAI,aAAa,GAAG,MAAM,CAAC;CAC3B,IAAI,KAAK,eAAe,GAAG;AAC3B;CACA,KAAK,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,aAAa,CAAC;AAC3D;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,OAAO,aAAa,CAAC;AACvB;CACA,EAAE;AACF;CACA,CAAC,kBAAkB,EAAE,aAAa,EAAE,UAAU,GAAG;AACjD;CACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC;CACrB,EAAE,MAAM,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG;AACpC;CACA,GAAG,SAAS,EAAE,IAAI,EAAE,CAAC;CACrB,GAAG,MAAM,GAAG,kBAAkB,EAAE,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;CACzE,GAAG,WAAW,EAAE,CAAC;AACjB;CACA,GAAG,KAAK,MAAM,GAAG;AACjB;CACA,IAAI,MAAM;AACV;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,OAAO,MAAM,CAAC;AAChB;CACA,EAAE;AACF;CACA,CAAC,SAAS,EAAE,SAAS,EAAE,uBAAuB,EAAE,eAAe,GAAG;AAClE;CACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,KAAK,SAAS,YAAY,QAAQ,GAAG;AACvC;CACA,GAAG,KAAK,uBAAuB,GAAG;AAClC;CACA;CACA;CACA,IAAI,MAAM,oBAAoB,GAAG,uBAAuB,CAAC;CACzD,IAAI,uBAAuB,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,MAAM;AAClE;CACA,KAAK,MAAM,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC;CAC1B,KAAK,OAAO,oBAAoB,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;AAC9E;CACA,KAAK,CAAC;AACN;AACA;CACA,IAAI;AACJ;CACA,GAAG,SAAS,GAAG;AACf;CACA,IAAI,mBAAmB,EAAE,eAAe;CACxC,IAAI,gBAAgB,EAAE,SAAS;CAC/B,IAAI,kBAAkB,EAAE,uBAAuB;CAC/C,IAAI,eAAe,EAAE,IAAI;AACzB;CACA,IAAI,CAAC;AACL;CACA,GAAG,OAAO,CAAC,IAAI,EAAE,0IAA0I,EAAE,CAAC;AAC9J;CACA,GAAG;AACH;CACA,EAAE,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;CAC/C,EAAE,IAAI;CACN,GAAG,mBAAmB;CACtB,GAAG,gBAAgB;CACnB,GAAG,eAAe;CAClB,GAAG,kBAAkB;CACrB,GAAG,GAAG,SAAS,CAAC;AAChB;CACA,EAAE,KAAK,eAAe,IAAI,kBAAkB,GAAG;AAC/C;CACA,GAAG,MAAM,uBAAuB,GAAG,eAAe,CAAC;CACnD,GAAG,eAAe,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,MAAM;AACvE;CACA,IAAI,KAAK,EAAE,uBAAuB,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG;AACnF;CACA,KAAK,OAAO,oBAAoB,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,kBAAkB,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;AAC5G;CACA,KAAK;AACL;CACA,IAAI,OAAO,IAAI,CAAC;AAChB;CACA,IAAI,CAAC;AACL;CACA,GAAG,MAAM,KAAK,EAAE,eAAe,GAAG;AAClC;CACA,GAAG,KAAK,kBAAkB,GAAG;AAC7B;CACA,IAAI,eAAe,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,MAAM;AAC7D;CACA,KAAK,OAAO,oBAAoB,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,kBAAkB,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;AAC5G;CACA,KAAK,CAAC;AACN;CACA,IAAI,MAAM;AACV;CACA,IAAI,eAAe,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,MAAM;AACtD;CACA,KAAK,OAAO,SAAS,CAAC;AACtB;CACA,KAAK,CAAC;AACN;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC;CACrB,EAAE,IAAI,UAAU,GAAG,CAAC,CAAC;CACrB,EAAE,MAAM,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG;AACpC;CACA,GAAG,SAAS,EAAE,IAAI,EAAE,CAAC;CACrB,GAAG,MAAM,GAAG,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,EAAE,UAAU,EAAE,CAAC;CACzG,GAAG,WAAW,EAAE,CAAC;AACjB;CACA,GAAG,KAAK,MAAM,GAAG;AACjB;CACA,IAAI,MAAM;AACV;CACA,IAAI;AACJ;CACA,GAAG,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC;AACjC;CACA,GAAG;AACH;CACA,EAAE,YAAY,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC;AAC5C;CACA,EAAE,OAAO,MAAM,CAAC;AAChB;CACA,EAAE;AACF;CACA,CAAC,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,GAAG;AAC/C;CACA;CACA;AACA;CACA,EAAE,IAAI;CACN,GAAG,gBAAgB;CACnB,GAAG,mBAAmB;CACtB,GAAG,GAAG,SAAS,CAAC;AAChB;CACA,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;CACxC,EAAE,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;AACzD;CACA,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;CACjD,EAAE,MAAM,iBAAiB,GAAG,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;AAClE;CACA,EAAE,UAAU,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC;AAC5C;CACA,EAAE,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;CAC/C,EAAE,MAAM,SAAS,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;AAChD;CACA,EAAE,KAAK,mBAAmB,GAAG;AAC7B;CACA,GAAG,SAAS,0BAA0B,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG;AAC3G;CACA,IAAI,MAAM,IAAI,EAAE,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,GAAG,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG;AACpE;CACA,KAAK,WAAW,EAAE,SAAS,EAAE,EAAE,GAAG,CAAC,EAAE,cAAc,EAAE,iBAAiB,EAAE,CAAC;CACzE,KAAK,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAC/C,KAAK,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAC/C,KAAK,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CAC/C,KAAK,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;AAClC;CACA,KAAK,MAAM,IAAI,EAAE,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,GAAG,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG;AACrE;CACA,MAAM,WAAW,EAAE,QAAQ,EAAE,EAAE,GAAG,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC;CAC/D,MAAM,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AAClC;CACA,MAAM,KAAK,mBAAmB,EAAE,QAAQ,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG;AAChG;CACA,OAAO,OAAO,IAAI,CAAC;AACnB;CACA,OAAO;AACP;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA,IAAI,OAAO,KAAK,CAAC;AACjB;CACA,IAAI;AACJ;CACA,GAAG,KAAK,gBAAgB,GAAG;AAC3B;CACA,IAAI,MAAM,wBAAwB,GAAG,gBAAgB,CAAC;CACtD,IAAI,gBAAgB,GAAG,WAAW,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG;AACrG;CACA,KAAK,KAAK,EAAE,wBAAwB,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG;AAC3G;CACA,MAAM,OAAO,0BAA0B,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC5G;CACA,MAAM;AACN;CACA,KAAK,OAAO,IAAI,CAAC;AACjB;CACA,KAAK,CAAC;AACN;CACA,IAAI,MAAM;AACV;CACA,IAAI,gBAAgB,GAAG,0BAA0B,CAAC;AAClD;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,QAAQ,CAAC,cAAc,EAAE,KAAK,EAAE,CAAC;CACnC,EAAE,KAAK,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CACtC,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE;AACjC;CACA,GAAG,gBAAgB,EAAE,GAAG,IAAI,KAAK,CAAC,aAAa,EAAE,GAAG,EAAE;AACtD;CACA,GAAG,eAAe,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM;AAC/E;CACA,IAAI,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;CACrB,IAAI,IAAI,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;CACpC,IAAI,OAAO,QAAQ,CAAC,SAAS,EAAE;AAC/B;CACA,KAAK,gBAAgB,EAAE,GAAG,IAAI,IAAI,CAAC,aAAa,EAAE,GAAG,EAAE;AACvD;CACA,KAAK,eAAe,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,MAAM;AAC5E;CACA,MAAM,OAAO,gBAAgB,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;AAC1G;CACA,MAAM;AACN;CACA,KAAK,EAAE,CAAC;AACR;CACA,IAAI;AACJ;CACA,GAAG,EAAE,CAAC;AACN;CACA,EAAE,YAAY,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC;CAC5C,EAAE,YAAY,CAAC,gBAAgB,EAAE,SAAS,EAAE,CAAC;CAC7C,EAAE,OAAO,MAAM,CAAC;AAChB;CACA,EAAE;AACF;CACA;CACA,CAAC,aAAa,EAAE,GAAG,EAAE,SAAS,GAAG;AACjC;CACA,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,SAAS,EAAE,CAAC;CACzC,EAAE,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC;AACzB;CACA,EAAE,OAAO,IAAI,CAAC,SAAS;CACvB,GAAG;CACH,IAAI,gBAAgB,EAAE,GAAG,IAAI,GAAG,CAAC,aAAa,EAAE,GAAG,EAAE;CACrD,IAAI,kBAAkB,EAAE,GAAG,IAAI,GAAG,CAAC,kBAAkB,EAAE,GAAG,EAAE;CAC5D,IAAI;CACJ,GAAG,CAAC;AACJ;CACA,EAAE;AACF;CACA,CAAC,gBAAgB,EAAE,MAAM,GAAG;AAC5B;CACA,EAAE,OAAO,IAAI,CAAC,SAAS;CACvB,GAAG;CACH,IAAI,gBAAgB,EAAE,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,GAAG,EAAE;CACxD,IAAI,kBAAkB,EAAE,GAAG,IAAI,GAAG,CAAC,gBAAgB,EAAE,MAAM,EAAE;CAC7D,IAAI;CACJ,GAAG,CAAC;AACJ;CACA,EAAE;AACF;CACA,CAAC,sBAAsB,EAAE,aAAa,EAAE,aAAa,EAAE,OAAO,GAAG,GAAG,EAAE,OAAO,GAAG,GAAG,EAAE,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,QAAQ,GAAG;AACjI;CACA,EAAE,KAAK,EAAE,aAAa,CAAC,WAAW,GAAG;AACrC;CACA,GAAG,aAAa,CAAC,kBAAkB,EAAE,CAAC;AACtC;CACA,GAAG;AACH;CACA,EAAE,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,EAAE,CAAC;CACzF,EAAE,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC;AACzB;CACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;CAC3C,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;CAC/B,EAAE,MAAM,QAAQ,GAAG,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;CACrD,EAAE,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC;CACzC,EAAE,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;CAC/C,EAAE,MAAM,SAAS,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;AAChD;CACA,EAAE,IAAI,WAAW,GAAG,KAAK,CAAC;CAC1B,EAAE,IAAI,eAAe,GAAG,KAAK,CAAC;CAC9B,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC;CACzB,EAAE,IAAI,eAAe,GAAG,IAAI,CAAC;AAC7B;CACA,EAAE,KAAK,OAAO,GAAG;AACjB;CACA,GAAG,WAAW,GAAG,KAAK,CAAC;CACvB,GAAG,eAAe,GAAG,KAAK,CAAC;AAC3B;CACA,GAAG;AACH;CACA,EAAE,IAAI,eAAe,GAAG,QAAQ,CAAC;CACjC,EAAE,IAAI,uBAAuB,GAAG,IAAI,CAAC;CACrC,EAAE,IAAI,4BAA4B,GAAG,IAAI,CAAC;CAC1C,EAAE,UAAU,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC;CAC5C,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC;CACjC,EAAE,IAAI,CAAC,SAAS;CAChB,GAAG;AACH;CACA,IAAI,mBAAmB,EAAE,GAAG,IAAI;AAChC;CACA,KAAK,OAAO,GAAG,CAAC,aAAa,EAAE,GAAG,EAAE,CAAC;AACrC;CACA,KAAK;AACL;CACA,IAAI,gBAAgB,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,MAAM;AAChD;CACA,KAAK,KAAK,KAAK,GAAG,eAAe,IAAI,KAAK,GAAG,YAAY,GAAG;AAC5D;CACA;CACA;CACA,MAAM,KAAK,MAAM,GAAG;AACpB;CACA,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;CAChC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;CAChC,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC/B;CACA,OAAO;AACP;CACA,MAAM,OAAO,IAAI,CAAC;AAClB;CACA,MAAM;AACN;CACA,KAAK,OAAO,KAAK,CAAC;AAClB;CACA,KAAK;AACL;CACA,IAAI,eAAe,EAAE,EAAE,MAAM,EAAE,KAAK,MAAM;AAC1C;CACA,KAAK,KAAK,aAAa,CAAC,UAAU,GAAG;AACrC;CACA;CACA;CACA,MAAM,OAAO,aAAa,CAAC,UAAU,CAAC,SAAS,EAAE;CACjD,OAAO,mBAAmB,EAAE,GAAG,IAAI;AACnC;CACA,QAAQ,OAAO,IAAI,CAAC,aAAa,EAAE,GAAG,EAAE,CAAC;AACzC;CACA,QAAQ;AACR;CACA,OAAO,gBAAgB,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,MAAM;AACnD;CACA,QAAQ,OAAO,KAAK,GAAG,eAAe,IAAI,KAAK,GAAG,YAAY,CAAC;AAC/D;CACA,QAAQ;AACR;CACA,OAAO,eAAe,EAAE,EAAE,WAAW,EAAE,UAAU,MAAM;AACvD;CACA,QAAQ,MAAM,IAAI,EAAE,GAAG,WAAW,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,WAAW,GAAG,UAAU,KAAK,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG;AAClG;CACA,SAAS,WAAW,EAAE,SAAS,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC;CAC5D,SAAS,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CACnD,SAAS,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CACnD,SAAS,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CACnD,SAAS,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;AACtC;CACA,SAAS,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AAC/E;CACA,UAAU,WAAW,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;CACjD,UAAU,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AACtC;CACA,UAAU,MAAM,IAAI,GAAG,QAAQ,CAAC,kBAAkB,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CAC1F,UAAU,KAAK,IAAI,GAAG,eAAe,GAAG;AACxC;CACA,WAAW,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAC/C;CACA,WAAW,KAAK,eAAe,GAAG;AAClC;CACA,YAAY,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAChD;CACA,YAAY;AACZ;CACA,WAAW,eAAe,GAAG,IAAI,CAAC;CAClC,WAAW,uBAAuB,GAAG,CAAC,GAAG,CAAC,CAAC;CAC3C,WAAW,4BAA4B,GAAG,EAAE,GAAG,CAAC,CAAC;AACjD;CACA,WAAW;AACX;CACA;CACA,UAAU,KAAK,IAAI,GAAG,YAAY,GAAG;AACrC;CACA,WAAW,OAAO,IAAI,CAAC;AACvB;CACA,WAAW;AACX;CACA,UAAU;AACV;CACA,SAAS;AACT;CACA,QAAQ;CACR,OAAO,EAAE,CAAC;AACV;CACA,MAAM,MAAM;AACZ;CACA;CACA,MAAM,MAAM,QAAQ,GAAG,UAAU,GAAG,UAAU,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;CACtE,MAAM,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,QAAQ,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG;AAC1D;CACA,OAAO,WAAW,EAAE,SAAS,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC;CAC1D,OAAO,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CACjD,OAAO,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CACjD,OAAO,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;CACjD,OAAO,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;AACpC;CACA,OAAO,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AAC7E;CACA,QAAQ,WAAW,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;CAC/C,QAAQ,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AACpC;CACA,QAAQ,MAAM,IAAI,GAAG,QAAQ,CAAC,kBAAkB,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACxF,QAAQ,KAAK,IAAI,GAAG,eAAe,GAAG;AACtC;CACA,SAAS,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAC7C;CACA,SAAS,KAAK,eAAe,GAAG;AAChC;CACA,UAAU,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAC9C;CACA,UAAU;AACV;CACA,SAAS,eAAe,GAAG,IAAI,CAAC;CAChC,SAAS,uBAAuB,GAAG,CAAC,GAAG,CAAC,CAAC;CACzC,SAAS,4BAA4B,GAAG,EAAE,GAAG,CAAC,CAAC;AAC/C;CACA,SAAS;AACT;CACA;CACA,QAAQ,KAAK,IAAI,GAAG,YAAY,GAAG;AACnC;CACA,SAAS,OAAO,IAAI,CAAC;AACrB;CACA,SAAS;AACT;CACA,QAAQ;AACR;CACA,OAAO;AACP;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG,CAAC;AACJ;CACA,EAAE,YAAY,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC;CAC5C,EAAE,YAAY,CAAC,gBAAgB,EAAE,SAAS,EAAE,CAAC;AAC7C;CACA,EAAE,KAAK,eAAe,KAAK,QAAQ,GAAG,OAAO,IAAI,CAAC;AAClD;CACA,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC;CACjE,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC;CAC7C,EAAE,OAAO,CAAC,QAAQ,GAAG,eAAe;CACpC,EAAE,OAAO,CAAC,SAAS,GAAG,uBAAuB,CAAC;AAC9C;CACA,EAAE,KAAK,OAAO,GAAG;AACjB;CACA,GAAG,KAAK,EAAE,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC;CAClE,QAAQ,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC;CAC9C,GAAG,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;CAC5C,GAAG,eAAe,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;CAC9C,GAAG,OAAO,CAAC,QAAQ,GAAG,eAAe,CAAC,GAAG,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;CACpE,GAAG,OAAO,CAAC,SAAS,GAAG,4BAA4B,CAAC;AACpD;CACA,GAAG;AACH;CACA,EAAE,OAAO,OAAO,CAAC;AACjB;CACA,EAAE;AACF;CACA,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG,EAAE,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,QAAQ,GAAG;AACvF;CACA;CACA;CACA;CACA;CACA,EAAE,MAAM,cAAc,GAAG,YAAY,GAAG,YAAY,CAAC;CACrD,EAAE,MAAM,cAAc,GAAG,YAAY,GAAG,YAAY,CAAC;CACrD,EAAE,IAAI,iBAAiB,GAAG,QAAQ,CAAC;CACnC,EAAE,IAAI,uBAAuB,GAAG,IAAI,CAAC;CACrC,EAAE,IAAI,CAAC,SAAS;AAChB;CACA,GAAG;AACH;CACA,IAAI,mBAAmB,EAAE,GAAG,IAAI;AAChC;CACA,KAAK,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;CAClD,KAAK,OAAO,IAAI,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC;AAC5C;CACA,KAAK;AACL;CACA,IAAI,gBAAgB,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,MAAM;AAChD;CACA,KAAK,OAAO,KAAK,GAAG,iBAAiB,IAAI,KAAK,GAAG,cAAc,CAAC;AAChE;CACA,KAAK;AACL;CACA,IAAI,kBAAkB,EAAE,EAAE,GAAG,EAAE,QAAQ,MAAM;AAC7C;CACA,KAAK,GAAG,CAAC,mBAAmB,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;CAC5C,KAAK,MAAM,MAAM,GAAG,KAAK,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC;CACpD,KAAK,KAAK,MAAM,GAAG,iBAAiB,GAAG;AACvC;CACA,MAAM,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;CACzB,MAAM,iBAAiB,GAAG,MAAM,CAAC;CACjC,MAAM,uBAAuB,GAAG,QAAQ,CAAC;AACzC;CACA,MAAM;AACN;CACA,KAAK,KAAK,MAAM,GAAG,cAAc,GAAG;AACpC;CACA,MAAM,OAAO,IAAI,CAAC;AAClB;CACA,MAAM,MAAM;AACZ;CACA,MAAM,OAAO,KAAK,CAAC;AACnB;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG,CAAC;AACJ;CACA,EAAE,KAAK,iBAAiB,KAAK,QAAQ,GAAG,OAAO,IAAI,CAAC;AACpD;CACA,EAAE,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC;AACzD;CACA,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;CACrD,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;CAClC,EAAE,MAAM,CAAC,QAAQ,GAAG,eAAe;CACnC,EAAE,MAAM,CAAC,SAAS,GAAG,uBAAuB,CAAC;AAC7C;CACA,EAAE,OAAO,MAAM,CAAC;AAChB;CACA,EAAE;AACF;CACA,CAAC,cAAc,EAAE,MAAM,GAAG;AAC1B;CACA,EAAE,MAAM,CAAC,SAAS,EAAE,CAAC;AACrB;CACA,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;CAC5B,EAAE,KAAK,CAAC,OAAO,EAAE,MAAM,IAAI;AAC3B;CACA,GAAG,UAAU,EAAE,CAAC,EAAE,IAAI,YAAY,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,CAAC;CACxD,GAAG,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC;AAC3B;CACA,GAAG,EAAE,CAAC;AACN;CACA,EAAE,OAAO,MAAM,CAAC;AAChB;CACA,EAAE;AACF;CACA;;CCz+BA,MAAM,WAAW,mBAAmB,IAAID,UAAI,EAAE,CAAC;CAC/C,MAAM,qBAAqB,SAASE,cAAQ,CAAC;AAC7C;CACA,CAAC,IAAI,MAAM,GAAG;AACd;CACA,EAAE,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC;AAC7B;CACA,EAAE;AACF;CACA,CAAC,IAAI,cAAc,GAAG;AACtB;CACA,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC;AAC3B;CACA,EAAE;AACF;CACA,CAAC,IAAI,MAAM,GAAG;AACd;CACA,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC;AAC3B;CACA,EAAE;AACF;CACA,CAAC,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,GAAG,EAAE,EAAE,KAAK,GAAG,CAAC,GAAG;AACtD;CACA,EAAE,KAAK,EAAE,CAAC;AACV;CACA,EAAE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;CAC3B,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAIC,oBAAc,EAAE,CAAC;CACvC,EAAE,IAAI,CAAC,IAAI,GAAG,uBAAuB,CAAC;CACtC,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACrB,EAAE,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;CAC9B,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;CACnB,EAAE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;CAC3B,EAAE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACtB;CACA,EAAE;AACF;CACA,CAAC,OAAO,GAAG,EAAE;AACb;CACA,CAAC,MAAM,GAAG;AACV;CACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;CACnD,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;CAC5B,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC;CACrB,EAAE,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;CACvB,EAAE,KAAK,UAAU,GAAG;AACpB;CACA;CACA,GAAG,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;CACtC,GAAG,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;CAC9C,GAAG,IAAI,WAAW,GAAG,CAAC,CAAC;CACvB,GAAG,UAAU,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,MAAM;AAC7C;CACA,IAAI,KAAK,KAAK,KAAK,WAAW,IAAI,MAAM,GAAG;AAC3C;CACA,KAAK,WAAW,GAAG,CAAC;CACpB,KAAK,OAAO,IAAI,CAAC;AACjB;CACA,KAAK,MAAM,KAAK,cAAc,GAAG;AACjC;CACA,KAAK,WAAW,GAAG,CAAC;AACpB;CACA,KAAK;AACL;CACA,IAAI,EAAE,KAAK,EAAE,CAAC;AACd;CACA;CACA,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAC;CACpB,GAAG,MAAM,aAAa,GAAG,IAAI,YAAY,EAAE,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC;CACjE,GAAG,UAAU,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,MAAM;AAC3D;CACA,IAAI,MAAM,SAAS,GAAG,KAAK,KAAK,WAAW,IAAI,MAAM,CAAC;CACtD,IAAI,KAAK,SAAS,IAAI,cAAc,GAAG;AACvC;CACA,KAAK,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,WAAW,EAAE,CAAC;AAChD;CACA,KAAK,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,WAAW,CAAC;CACtC,KAAK,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AACzC;CACA,MAAM,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CACzC,MAAM,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AAC1C;CACA,OAAO,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CAC1C,OAAO,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AAC3C;CACA,QAAQ,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CAC3C,QAAQ,aAAa,EAAE,QAAQ,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;CAC7C,QAAQ,aAAa,EAAE,QAAQ,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;CAC7C,QAAQ,aAAa,EAAE,QAAQ,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC7C;CACA,QAAQ,QAAQ,IAAI,CAAC,CAAC;AACtB;CACA,QAAQ;AACR;CACA,OAAO;AACP;CACA,MAAM;AACN;CACA,KAAK,OAAO,SAAS,CAAC;AACtB;CACA,KAAK;AACL;CACA,IAAI,EAAE,KAAK,EAAE,CAAC;AACd;CACA,GAAG,IAAI,UAAU,CAAC;CAClB,GAAG,IAAI,OAAO,CAAC;CACf,GAAG,KAAK,IAAI,CAAC,YAAY,GAAG;AAC5B;CACA;CACA,IAAI,OAAO,GAAG,IAAI,UAAU,EAAE;CAC9B;CACA,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;AACT;CACA;CACA,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;AACT;CACA;CACA,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,CAAC,EAAE,CAAC;CACT,KAAK,EAAE,CAAC;AACR;CACA,IAAI,MAAM;AACV;CACA,IAAI,OAAO,GAAG,IAAI,UAAU,EAAE;AAC9B;CACA;CACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;CACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;CACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;CACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;CACA;CACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;CACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;CACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;CACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;CACA;CACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;CACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;CACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;CACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;CACA,KAAK,EAAE,CAAC;AACR;CACA,IAAI;AACJ;CACA,GAAG,KAAK,aAAa,CAAC,MAAM,GAAG,KAAK,GAAG;AACvC;CACA,IAAI,UAAU,GAAG,IAAI,WAAW,EAAE,OAAO,CAAC,MAAM,GAAG,WAAW,EAAE,CAAC;AACjE;CACA,IAAI,MAAM;AACV;CACA,IAAI,UAAU,GAAG,IAAI,WAAW,EAAE,OAAO,CAAC,MAAM,GAAG,WAAW,EAAE,CAAC;AACjE;CACA,IAAI;AACJ;CACA,GAAG,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC;CACtC,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,GAAG;AAC5C;CACA,IAAI,MAAM,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;CAC5B,IAAI,MAAM,WAAW,GAAG,CAAC,GAAG,WAAW,CAAC;CACxC,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,GAAG;AAC7C;CACA,KAAK,UAAU,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,SAAS,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;AAC9D;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA;CACA,GAAG,QAAQ,CAAC,QAAQ;CACpB,IAAI,IAAId,qBAAe,EAAE,UAAU,EAAE,CAAC,EAAE,KAAK,EAAE;CAC/C,IAAI,CAAC;CACL,GAAG,QAAQ,CAAC,YAAY;CACxB,IAAI,UAAU;CACd,IAAI,IAAIA,qBAAe,EAAE,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE;CAClD,IAAI,CAAC;CACL,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACvB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,MAAM,iBAAiB,SAASe,WAAK,CAAC;AACtC;CACA,CAAC,IAAI,KAAK,GAAG;AACb;CACA,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;AACjC;CACA,EAAE;AACF;CACA,CAAC,IAAI,OAAO,GAAG;AACf;CACA,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;AACnC;CACA,EAAE;AACF;CACA,CAAC,IAAI,OAAO,EAAE,CAAC,GAAG;AAClB;CACA,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,CAAC;CAChC,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,CAAC;AAChC;CACA,EAAE;AACF;CACA,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE,GAAG;AACjC;CACA,EAAE,KAAK,EAAE,CAAC;AACV;CACA,EAAE,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC;CAClC,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACrB,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;CACnB,EAAE,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;CAC9B,EAAE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;CAC3B,EAAE,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AACnB;CACA,EAAE,MAAM,YAAY,GAAG,IAAIC,uBAAiB,EAAE;CAC9C,GAAG,KAAK,EAAE,QAAQ;CAClB,GAAG,WAAW,EAAE,IAAI;CACpB,GAAG,OAAO,EAAE,GAAG;CACf,GAAG,UAAU,EAAE,KAAK;CACpB,GAAG,EAAE,CAAC;AACN;CACA,EAAE,MAAM,YAAY,GAAG,IAAIC,uBAAiB,EAAE;CAC9C,GAAG,KAAK,EAAE,QAAQ;CAClB,GAAG,WAAW,EAAE,IAAI;CACpB,GAAG,OAAO,EAAE,GAAG;CACf,GAAG,UAAU,EAAE,KAAK;CACpB,GAAG,EAAE,CAAC;AACN;CACA,EAAE,YAAY,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;AAC1C;CACA,EAAE,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;CACnC,EAAE,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACnC;CACA,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;AAChB;CACA,EAAE;AACF;CACA,CAAC,MAAM,GAAG;AACV;CACA,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;CAC5C,EAAE,MAAM,UAAU,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;CACjD,EAAE,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,GAAG;AAC5C;CACA,GAAG,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;CAClC,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;CAC3B,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC;AACvB;CACA,GAAG;AACH;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,GAAG;AAC1C;CACA,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;AAClC;CACA,IAAI,MAAM,IAAI,GAAG,IAAI,qBAAqB,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;CAC1F,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;CACrB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AAC7B;CACA,IAAI;AACJ;CACA,GAAG,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC;CACjC,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;CAC3B,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CACzB,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;CAC7C,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;CACzC,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;CAC7E,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,iBAAiB,EAAE,GAAG,IAAI,GAAG;AAC9B;CACA,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;CAC3C,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;CAC3C,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;AACrC;CACA,EAAE,KAAK,CAAC,iBAAiB,EAAE,GAAG,IAAI,EAAE,CAAC;AACrC;CACA,EAAE;AACF;CACA,CAAC,IAAI,EAAE,MAAM,GAAG;AAChB;CACA,EAAE,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;CAC5B,EAAE,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;AAC1B;CACA,EAAE;AACF;CACA,CAAC,KAAK,GAAG;AACT;CACA,EAAE,OAAO,IAAI,iBAAiB,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AACxD;CACA,EAAE;AACF;CACA,CAAC,OAAO,GAAG;AACX;CACA,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;CAC9B,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;AAC9B;CACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACtD;CACA,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;AACpC;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;;CClUA,MAAM,KAAK,mBAAmB,IAAIN,UAAI,EAAE,CAAC;CACzC,MAAM,KAAK,mBAAmB,IAAIA,UAAI,EAAE,CAAC;CACzC,MAAM,IAAI,mBAAmB,IAAIV,aAAO,EAAE,CAAC;AAC3C;CACA;CACA,SAAS,gBAAgB,EAAE,EAAE,GAAG;AAChC;CACA,CAAC,SAAS,OAAO,EAAE;AACnB;CACA,EAAE,KAAK,QAAQ;CACf,GAAG,OAAO,CAAC,CAAC;CACZ,EAAE,KAAK,QAAQ;CACf,GAAG,OAAO,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;CACxB,EAAE,KAAK,SAAS;CAChB,GAAG,OAAO,CAAC,CAAC;CACZ,EAAE;CACF,GAAG,OAAO,CAAC,CAAC;AACZ;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,SAAS,YAAY,EAAE,GAAG,GAAG;AAC7B;CACA,CAAC,MAAM,KAAK,GAAG,gCAAgC,CAAC;CAChD,CAAC,OAAO,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;AAC3C;CACA,CAAC;AACD;CACA,SAAS,eAAe,EAAE,GAAG,EAAE,KAAK,GAAG;AACvC;CACA,CAAC,MAAM,MAAM,GAAG;CAChB,EAAE,SAAS,EAAE,CAAC;CACd,EAAE,aAAa,EAAE,CAAC;AAClB;CACA,EAAE,KAAK,EAAE;CACT,GAAG,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE,QAAQ;CACjC,GAAG;CACH,EAAE,IAAI,EAAE;CACR,GAAG,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE,QAAQ;CACjC,GAAG;CACH,EAAE,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;CACrB,EAAE,gBAAgB,EAAE,CAAC;CACrB,EAAE,CAAC;AACH;CACA,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,KAAK,MAAM;AACxE;CACA,EAAE,MAAM,EAAE,GAAG,YAAY,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;CACvD,EAAE,MAAM,EAAE,GAAG,YAAY,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;CACvD,EAAE,MAAM,EAAE,GAAG,YAAY,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;AACvD;CACA,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC1D;CACA,EAAE,MAAM,CAAC,SAAS,GAAG,CAAC;CACtB,EAAE,KAAK,MAAM,GAAG;AAChB;CACA,GAAG,MAAM,CAAC,aAAa,GAAG,CAAC;AAC3B;CACA,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;CAC1D,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AAC1D;CACA,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;CACxD,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AACxD;CACA,GAAG,MAAM,CAAC,gBAAgB,IAAI,WAAW,GAAG,uBAAuB,GAAG,KAAK,CAAC;AAC5E;CACA,GAAG,MAAM;AACT;CACA,GAAG,MAAM,CAAC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAC;AACrC;CACA,GAAG,MAAM,CAAC,gBAAgB,IAAI,WAAW,GAAG,cAAc,CAAC;AAC3D;CACA,GAAG;AACH;CACA,EAAE,EAAE,KAAK,EAAE,CAAC;AACZ;CACA;CACA,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,QAAQ,GAAG;AACrC;CACA,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;CACtB,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACtB;CACA,EAAE;AACF;CACA,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,QAAQ,GAAG;AACtC;CACA,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;CACvB,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AACvB;CACA,EAAE;AACF;CACA,CAAC,OAAO,MAAM,CAAC;AACf;CACA,CAAC;AACD;CACA,SAAS,cAAc,EAAE,GAAG,GAAG;AAC/B;CACA,CAAC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC,MAAM,eAAe,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;AACnE;CACA,CAAC;AACD;CACA,SAAS,qBAAqB,EAAE,GAAG,GAAG;AACtC;CACA,CAAC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;CAC7B,CAAC,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;CACvB,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC;AACf;CACA,CAAC,QAAQ,KAAK,CAAC,MAAM,GAAG;AACxB;CACA,EAAE,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;CAC3B,EAAE,KAAK,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG;AAC/B;CACA,GAAG,SAAS;AACZ;CACA,GAAG;AACH;CACA,EAAE,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;AACxB;CACA,EAAE,MAAM,IAAI,GAAG,IAAI,IAAI,GAAG;AAC1B;CACA,GAAG,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,GAAG,EAAE,GAAG;AACvC;CACA,IAAI,SAAS;AACb;CACA,IAAI;AACJ;CACA,GAAG,KAAK,IAAI,gBAAgB,EAAE,GAAG,EAAE,CAAC;AACpC;CACA,GAAG,MAAM,KAAK,GAAG,IAAI,EAAE,GAAG,EAAE,CAAC;CAC7B,GAAG,KAAK,KAAK,MAAM,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,GAAG;AAChF;CACA,IAAI,KAAK,YAAY,EAAE,KAAK,EAAE,GAAG;AACjC;CACA,KAAK,KAAK,IAAI,KAAK,CAAC,UAAU,CAAC;AAC/B;CACA,KAAK,MAAM,KAAK,KAAK,YAAY,WAAW,GAAG;AAC/C;CACA,KAAK,KAAK,IAAI,KAAK,CAAC,UAAU,CAAC;AAC/B;CACA,KAAK,MAAM;AACX;CACA,KAAK,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACzB;CACA,KAAK;AACL;CACA,IAAI,MAAM;AACV;CACA,IAAI,KAAK,IAAI,gBAAgB,EAAE,KAAK,EAAE,CAAC;AACvC;CACA,IAAI;AACJ;AACA;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,KAAK,CAAC;AACd;CACA,CAAC;AACD;CACA,SAAS,cAAc,EAAE,GAAG,GAAG;AAC/B;CACA,CAAC,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;CAC/B,CAAC,MAAM,UAAU,GAAG,EAAE,CAAC;CACvB,CAAC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;CAC9B,CAAC,MAAM,QAAQ,GAAG,QAAQ,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;CACtD,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC;AACnB;CACA,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,MAAM;AACjE;CACA,EAAE,MAAM,IAAI,GAAG;CACf,GAAG,KAAK;CACR,GAAG,MAAM;CACT,GAAG,YAAY;CACf,GAAG,MAAM;CACT,GAAG,KAAK;CACR,GAAG,CAAC;CACJ,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;AAC7B;CACA,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;CACvC,EAAE,MAAM,MAAM,GAAG,UAAU,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;AACzC;CACA,EAAE,KAAK,MAAM,GAAG;AAChB;CACA;CACA,GAAG,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AACzE;CACA,IAAI,MAAM,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;CAC/B,IAAI,MAAM,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;CACnC,IAAI,MAAM,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AACnC;CACA,IAAI,IAAI,WAAW,CAAC;AACpB;CACA,IAAI,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;CAC7C,IAAI,WAAW,GAAG,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC;AAC9C;CACA,IAAI,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;CAC7C,IAAI,WAAW,GAAG,WAAW,IAAI,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC;AAC7D;CACA,IAAI,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;CAC7C,IAAI,WAAW,GAAG,WAAW,IAAI,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC;AAC7D;CACA,IAAI,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,8CAA8C,EAAE,CAAC;CAClF,IAAI,MAAM,GAAG,MAAM,IAAI,WAAW,CAAC;AACnC;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,KAAK,MAAM,GAAG;AAChB;CACA;CACA,GAAG,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;AACxC;CACA,GAAG,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC;CAClD,GAAG,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,6CAA6C,EAAE,CAAC;CAChF,GAAG,MAAM,GAAG,MAAM,IAAI,WAAW,CAAC;AAClC;CACA,GAAG;AACH;CACA,EAAE,EAAE,CAAC;AACL;CACA,CAAC,OAAO,MAAM,CAAC;AACf;CACA,CAAC;AACD;CACA;CACA,SAAS,gBAAgB,EAAE,GAAG,GAAG;AACjC;CACA,CAAC,MAAM,UAAU,GAAG,EAAE,CAAC;AACvB;CACA,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,MAAM;AACjE;CACA,EAAE,MAAM,IAAI,GAAG;CACf,GAAG,MAAM,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,IAAIU,UAAI,EAAE,EAAE;CACpD,GAAG,CAAC;AACJ;CACA,EAAE,KAAK,MAAM,GAAG;AAChB;CACA,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACtB,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACxB;CACA,GAAG,MAAM;AACT;CACA,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;CACpB,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACrB;CACA,GAAG;AACH;CACA,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;AAC7B;CACA;CACA,EAAE,MAAM,MAAM,GAAG,UAAU,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;CACzC,EAAE,KAAK,MAAM,GAAG;AAChB;CACA,GAAG,KAAK,MAAM,CAAC,IAAI,KAAK,IAAI,GAAG;AAC/B;CACA,IAAI,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;AACvB;CACA,IAAI,MAAM;AACV;CACA,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;AACxB;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,EAAE,CAAC;AACL;CACA,CAAC,OAAO,UAAU,EAAE,CAAC,EAAE,CAAC;AACxB;CACA;;CC/QA,MAAM,GAAG,mBAAmB,IAAIO,SAAG,EAAE,CAAC;CACtC,MAAM,gBAAgB,mBAAmB,IAAIX,aAAO,EAAE,CAAC;CACvD,MAAM,mBAAmB,GAAGY,UAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AACnD;CACO,SAAS,kBAAkB,EAAE,SAAS,EAAE,UAAU,GAAG;AAC5D;CACA,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG;AACjC;CACA,EAAE,KAAK,IAAI,CAAC,QAAQ,KAAK,SAAS,GAAG,OAAO;AAC5C;CACA,EAAE,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,CAAC;CACrD,EAAE,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,gBAAgB,EAAE,CAAC;AAC7D;CACA,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;CACvC,EAAE,KAAK,SAAS,CAAC,YAAY,KAAK,IAAI,GAAG;AACzC;CACA,GAAG,MAAM,GAAG,GAAG,uBAAuB,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;CAClG,GAAG,KAAK,GAAG,GAAG;AACd;CACA,IAAI,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AAC3B;CACA,IAAI;AACJ;CACA,GAAG,MAAM;AACT;CACA,GAAG,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;CAClD,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;CACA,IAAI,MAAM,GAAG,GAAG,uBAAuB,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;CACtE,IAAI,KAAK,GAAG,GAAG;AACf;CACA,KAAK,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AAC5B;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,MAAM;AACR;CACA,EAAE,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;AAC1D;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,SAAS,iBAAiB,EAAE,OAAO,GAAG;AAC7C;CACA,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;CAChD,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC;AACxB;CACA,CAAC;AACD;CACO,SAAS,iBAAiB,GAAG;AACpC;CACA,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACxB;CACA;;CCzCA,SAAS,mBAAmB,EAAE,KAAK,GAAG;AACtC;CACA,CAAC,SAAS,KAAK;AACf;CACA,EAAE,KAAK,CAAC,EAAE,OAAO,GAAG,CAAC;CACrB,EAAE,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC;CACtB,EAAE,KAAK,CAAC,EAAE,OAAO,MAAM,CAAC;CACxB,EAAE,KAAK,CAAC,EAAE,OAAO,MAAM,CAAC;AACxB;CACA,EAAE;AACF;CACA,CAAC,MAAM,IAAI,KAAK,EAAE,CAAC;AACnB;CACA,CAAC;AACD;CACA,SAAS,aAAa,EAAE,KAAK,GAAG;AAChC;CACA,CAAC,SAAS,KAAK;AACf;CACA,EAAE,KAAK,CAAC,EAAE,OAAOC,eAAS,CAAC;CAC3B,EAAE,KAAK,CAAC,EAAE,OAAOC,cAAQ,CAAC;CAC1B,EAAE,KAAK,CAAC,EAAE,OAAOC,gBAAU,CAAC;CAC5B,EAAE,KAAK,CAAC,EAAE,OAAOA,gBAAU,CAAC;AAC5B;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA,SAAS,gBAAgB,EAAE,KAAK,GAAG;AACnC;CACA,CAAC,SAAS,KAAK;AACf;CACA,EAAE,KAAK,CAAC,EAAE,OAAOC,sBAAgB,CAAC;CAClC,EAAE,KAAK,CAAC,EAAE,OAAOC,qBAAe,CAAC;CACjC,EAAE,KAAK,CAAC,EAAE,OAAOC,uBAAiB,CAAC;CACnC,EAAE,KAAK,CAAC,EAAE,OAAOA,uBAAiB,CAAC;AACnC;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,MAAM,sBAAsB,SAASC,iBAAW,CAAC;AACxD;CACA,CAAC,WAAW,GAAG;AACf;CACA,EAAE,KAAK,EAAE,CAAC;CACV,EAAE,IAAI,CAAC,SAAS,GAAGC,mBAAa,CAAC;CACjC,EAAE,IAAI,CAAC,SAAS,GAAGA,mBAAa,CAAC;CACjC,EAAE,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;CAC/B,EAAE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;CAC/B,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B;CACA,EAAE;AACF;CACA,CAAC,UAAU,EAAE,IAAI,GAAG;AACpB;CACA,EAAE,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;CACjD,EAAE,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;CACzC,EAAE,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC;CACnC,EAAE,KAAK,gBAAgB,KAAK,IAAI,GAAG;AACnC;CACA,GAAG,KAAK,EAAE,gBAAgB,GAAG,aAAa,KAAK,gBAAgB,KAAK,GAAG,GAAG;AAC1E;CACA,IAAI,MAAM,IAAI,KAAK,EAAE,iFAAiF,EAAE,CAAC;AACzG;CACA,IAAI;AACJ;CACA,GAAG,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC;CACpC,GAAG,IAAI,CAAC,KAAK,GAAG,aAAa,GAAG,gBAAgB,GAAG,gBAAgB,CAAC;AACpE;CACA,GAAG;AACH;CACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;CAC3B,EAAE,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;CACrC,EAAE,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;CACpD,EAAE,MAAM,SAAS,GAAG,kBAAkB,CAAC,iBAAiB,CAAC;CACzD,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;CACpC,EAAE,IAAI,WAAW,GAAG,QAAQ,CAAC;AAC7B;CACA;CACA,EAAE,KAAK,UAAU,KAAK,IAAI,GAAG;AAC7B;CACA,GAAG,SAAS,kBAAkB;AAC9B;CACA,IAAI,KAAK,YAAY;CACrB,KAAK,UAAU,GAAGC,eAAS,CAAC;CAC5B,KAAK,MAAM;AACX;CACA,IAAI,KAAK,UAAU,CAAC;CACpB,IAAI,KAAK,WAAW,CAAC;CACrB,IAAI,KAAK,WAAW;CACpB,KAAK,UAAU,GAAGC,qBAAe,CAAC;CAClC,KAAK,MAAM;AACX;CACA,IAAI,KAAK,SAAS,CAAC;CACnB,IAAI,KAAK,UAAU,CAAC;CACpB,IAAI,KAAK,UAAU;CACnB,KAAK,UAAU,GAAGC,aAAO,CAAC;CAC1B,KAAK,MAAM;AACX;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA,EAAE,IAAI,IAAI,EAAE,MAAM,EAAE,cAAc,EAAE,gBAAgB,CAAC;CACrD,EAAE,IAAI,cAAc,GAAG,mBAAmB,EAAE,QAAQ,EAAE,CAAC;CACvD,EAAE,SAAS,UAAU;AACrB;CACA,GAAG,KAAKF,eAAS;CACjB,IAAI,cAAc,GAAG,GAAG,CAAC;CACzB,IAAI,MAAM,GAAG,aAAa,EAAE,QAAQ,EAAE,CAAC;AACvC;CACA,IAAI,KAAK,UAAU,IAAI,SAAS,KAAK,CAAC,GAAG;AACzC;CACA,KAAK,gBAAgB,GAAG,kBAAkB,CAAC;CAC3C,KAAK,cAAc,IAAI,GAAG,CAAC;AAC3B;CACA,KAAK,KAAK,kBAAkB,KAAK,UAAU,GAAG;AAC9C;CACA,MAAM,IAAI,GAAGG,sBAAgB,CAAC;AAC9B;CACA,MAAM,MAAM;AACZ;CACA,MAAM,IAAI,GAAGC,cAAQ,CAAC;CACtB,MAAM,cAAc,IAAI,QAAQ,CAAC;AACjC;CACA,MAAM;AACN;CACA,KAAK,MAAM;AACX;CACA,KAAK,gBAAgB,GAAG,YAAY,CAAC;CACrC,KAAK,cAAc,IAAI,KAAK,CAAC;CAC7B,KAAK,IAAI,GAAGJ,eAAS,CAAC;AACtB;CACA,KAAK;AACL;CACA,IAAI,MAAM;AACV;CACA,GAAG,KAAKE,aAAO;CACf,IAAI,cAAc,IAAI,SAAS,GAAG,CAAC,GAAG,GAAG,CAAC;CAC1C,IAAI,cAAc,GAAG,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC;CACpG,IAAI,MAAM,GAAG,gBAAgB,EAAE,QAAQ,EAAE,CAAC;AAC1C;CACA,IAAI,KAAK,SAAS,KAAK,CAAC,GAAG;AAC3B;CACA,KAAK,gBAAgB,GAAG,SAAS,CAAC;CAClC,KAAK,IAAI,GAAGE,cAAQ,CAAC;AACrB;CACA,KAAK,MAAM,KAAK,SAAS,KAAK,CAAC,GAAG;AAClC;CACA,KAAK,gBAAgB,GAAG,UAAU,CAAC;CACnC,KAAK,IAAI,GAAGC,eAAS,CAAC;AACtB;CACA,KAAK,MAAM;AACX;CACA,KAAK,gBAAgB,GAAG,UAAU,CAAC;CACnC,KAAK,IAAI,GAAGH,aAAO,CAAC;AACpB;CACA,KAAK;AACL;CACA,IAAI,MAAM;AACV;CACA,GAAG,KAAKD,qBAAe;CACvB,IAAI,cAAc,IAAI,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC;CAC3C,IAAI,cAAc,GAAG,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC;CACpG,IAAI,MAAM,GAAG,gBAAgB,EAAE,QAAQ,EAAE,CAAC;AAC1C;CACA,IAAI,KAAK,SAAS,KAAK,CAAC,GAAG;AAC3B;CACA,KAAK,gBAAgB,GAAG,UAAU,CAAC;CACnC,KAAK,IAAI,GAAGE,sBAAgB,CAAC;AAC7B;CACA,KAAK,MAAM,KAAK,SAAS,KAAK,CAAC,GAAG;AAClC;CACA,KAAK,gBAAgB,GAAG,WAAW,CAAC;CACpC,KAAK,IAAI,GAAGG,uBAAiB,CAAC;AAC9B;CACA,KAAK,MAAM;AACX;CACA,KAAK,gBAAgB,GAAG,WAAW,CAAC;CACpC,KAAK,IAAI,GAAGL,qBAAe,CAAC;AAC5B;CACA,KAAK;AACL;CACA,IAAI,MAAM;AACV;CACA,GAAG;AACH;CACA;CACA;CACA,EAAE,KAAK,WAAW,KAAK,CAAC,MAAM,MAAM,KAAKP,gBAAU,IAAI,MAAM,KAAKG,uBAAiB,EAAE,GAAG;AACxF;CACA,GAAG,WAAW,GAAG,CAAC,CAAC;AACnB;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;CACpD,EAAE,MAAM,MAAM,GAAG,WAAW,GAAG,SAAS,GAAG,SAAS,CAAC;CACrD,EAAE,MAAM,SAAS,GAAG,IAAI,gBAAgB,EAAE,MAAM,EAAE,CAAC;AACnD;CACA;CACA,EAAE,MAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC;CAC7C,EAAE,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;CAC1B,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG;AACrC;CACA,GAAG,MAAM,EAAE,GAAG,WAAW,GAAG,CAAC,CAAC;CAC9B,GAAG,SAAS,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,cAAc,CAAC;AACrD;CACA,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG;AACxB;CACA,IAAI,SAAS,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,cAAc,CAAC;AAC1D;CACA,IAAI;AACJ;CACA,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG;AACxB;CACA,IAAI,SAAS,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,cAAc,CAAC;AAC1D;CACA,IAAI,KAAK,WAAW,KAAK,CAAC,GAAG;AAC7B;CACA,KAAK,SAAS,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC;AAC/B;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG;AACxB;CACA,IAAI,SAAS,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,cAAc,CAAC;AAC1D;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,IAAI,CAAC,UAAU,GAAG,kBAAkB,CAAC;AACvC;CACA,EAAE,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;CACvC,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;CACvB,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;CACnB,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;CAC/B,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;CAChC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC;CAC9B,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;CAC1B,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;AACjB;CACA,EAAE,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC;CACnC,EAAE,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC;AAC7B;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,MAAM,0BAA0B,SAAS,sBAAsB,CAAC;AACvE;CACA,CAAC,WAAW,GAAG;AACf;CACA,EAAE,KAAK,EAAE,CAAC;CACV,EAAE,IAAI,CAAC,WAAW,GAAGI,qBAAe,CAAC;AACrC;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,MAAM,yBAAyB,SAAS,sBAAsB,CAAC;AACtE;CACA,CAAC,WAAW,GAAG;AACf;CACA,EAAE,KAAK,EAAE,CAAC;CACV,EAAE,IAAI,CAAC,WAAW,GAAGC,aAAO,CAAC;AAC7B;CACA,EAAE;AACF;AACA;CACA,CAAC;AACD;CACO,MAAM,2BAA2B,SAAS,sBAAsB,CAAC;AACxE;CACA,CAAC,WAAW,GAAG;AACf;CACA,EAAE,KAAK,EAAE,CAAC;CACV,EAAE,IAAI,CAAC,WAAW,GAAGF,eAAS,CAAC;AAC/B;CACA,EAAE;AACF;CACA;;CC9RA,SAAS,aAAa,EAAE,GAAG,EAAE,aAAa,EAAE,eAAe,GAAG;AAC9D;CACA,CAAC,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC;AAC1B;CACA,CAAC,KAAK,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG;AAC3B;CACA,EAAE,MAAM,IAAI,KAAK,EAAE,sDAAsD,EAAE,CAAC;AAC5E;CACA,EAAE;AACF;CACA,CAAC,MAAM,IAAI,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC;CACzB,CAAC,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,IAAI,EAAE,CAAC;CAC7C,CAAC,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,IAAI,EAAE,CAAC;CAC7C,CAAC,MAAM,YAAY,GAAG,IAAI,YAAY,EAAE,IAAI,EAAE,CAAC;AAC/C;CACA;CACA;CACA,CAAC,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC;CACpD,CAAC,MAAM,eAAe,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,SAAS,GAAG,CAAC,EAAE,EAAE,CAAC;CACrE,CAAC,MAAM,WAAW,GAAG,IAAI,YAAY,EAAE,CAAC,GAAG,eAAe,GAAG,eAAe,EAAE,CAAC;AAC/E;CACA,CAAC,MAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,CAAC;CAC/D,CAAC,MAAM,aAAa,GAAG,IAAI,WAAW,EAAE,CAAC,GAAG,iBAAiB,GAAG,iBAAiB,EAAE,CAAC;AACpF;CACA,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,GAAG,GAAG;AACxC;CACA,EAAE,MAAM,WAAW,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,CAAC;CAC7C,EAAE,MAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;CACtC,EAAE,MAAM,WAAW,GAAG,mBAAmB,EAAE,WAAW,EAAE,CAAC;CACzD,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;CACA,GAAG,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,WAAW,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;CACtE,GAAG,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,WAAW,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACtE;CACA,GAAG;AACH;CACA,EAAE,KAAK,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;AAC7C;CACA,GAAG,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;CACnD,GAAG,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACrD;CACA,GAAG,MAAM,eAAe,GAAG,UAAU,GAAG,KAAK,CAAC;CAC9C,GAAG,aAAa,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,eAAe,CAAC;CAChD,GAAG,aAAa,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC;AACvC;CACA,GAAG,MAAM;AACT;CACA,GAAG,MAAM,UAAU,GAAG,CAAC,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,cAAc,CAAC;CAClF,GAAG,MAAM,SAAS,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAC5D;CACA,GAAG,aAAa,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,SAAS,CAAC;CAC1C,GAAG,aAAa,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC;AAC3C;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,GAAG,WAAW,CAAC;CACxC,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,eAAe,CAAC;CAC7C,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,eAAe,CAAC;CAC9C,CAAC,aAAa,CAAC,MAAM,GAAGN,gBAAU,CAAC;CACnC,CAAC,aAAa,CAAC,IAAI,GAAGM,eAAS,CAAC;CAChC,CAAC,aAAa,CAAC,cAAc,GAAG,SAAS,CAAC;CAC1C,CAAC,aAAa,CAAC,SAAS,GAAGD,mBAAa,CAAC;CACzC,CAAC,aAAa,CAAC,SAAS,GAAGA,mBAAa,CAAC;CACzC,CAAC,aAAa,CAAC,eAAe,GAAG,KAAK,CAAC;CACvC,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC;CAClC,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;AACzB;CACA,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC;CAC5C,CAAC,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,iBAAiB,CAAC;CACjD,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG,iBAAiB,CAAC;CAClD,CAAC,eAAe,CAAC,MAAM,GAAGH,qBAAe,CAAC;CAC1C,CAAC,eAAe,CAAC,IAAI,GAAGK,qBAAe,CAAC;CACxC,CAAC,eAAe,CAAC,cAAc,GAAG,QAAQ,CAAC;CAC3C,CAAC,eAAe,CAAC,SAAS,GAAGF,mBAAa,CAAC;CAC3C,CAAC,eAAe,CAAC,SAAS,GAAGA,mBAAa,CAAC;CAC3C,CAAC,eAAe,CAAC,eAAe,GAAG,KAAK,CAAC;CACzC,CAAC,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC;CACpC,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;AAC3B;CACA,CAAC;AACD;CACO,MAAM,oBAAoB,CAAC;AAClC;CACA,CAAC,WAAW,GAAG;AACf;CACA,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;CAC1B,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,0BAA0B,EAAE,CAAC;CAChD,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,2BAA2B,EAAE,CAAC;CACpD,EAAE,IAAI,CAAC,SAAS,GAAG,IAAID,iBAAW,EAAE,CAAC;CACrC,EAAE,IAAI,CAAC,WAAW,GAAG,IAAIA,iBAAW,EAAE,CAAC;AACvC;CACA,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,CAAC;AAClC;CACA,EAAE;AACF;CACA,CAAC,UAAU,EAAE,GAAG,GAAG;AACnB;CACA,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;AAC3B;CACA,EAAE,aAAa,EAAE,GAAG,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;AACzD;CACA,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,KAAK,EAAE,CAAC;CAC1C,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAC3D;CACA,EAAE;AACF;CACA,CAAC,OAAO,GAAG;AACX;CACA,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;AAC3D;CACA,EAAE,KAAK,KAAK,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;CAC/B,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;CACrC,EAAE,KAAK,SAAS,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;CACvC,EAAE,KAAK,WAAW,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;AAC3C;CACA,EAAE;AACF;CACA;;CC7IA;CACA;CACA;AACY,OAAC,aAAa,aAAa,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACY,OAAC,uBAAuB,aAAa,CAAC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;CACA;AACY,OAAC,sBAAsB,aAAa,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;CClcA,MAAM,eAAe,iBAAiB,IAAIzB,aAAO,EAAE,CAAC;CACpD,MAAM,aAAa,iBAAiB,IAAIA,aAAO,EAAE,CAAC;CAClD,MAAM,cAAc,iBAAiB,IAAIA,aAAO,EAAE,CAAC;CACnD,MAAM,eAAe,iBAAiB,IAAIkC,aAAO,EAAE,CAAC;AACpD;CACA,MAAM,YAAY,iBAAiB,IAAIlC,aAAO,EAAE,CAAC;CACjD,MAAM,KAAK,iBAAiB,IAAIA,aAAO,EAAE,CAAC;AAC1C;CACA,MAAM,UAAU,iBAAiB,IAAIkC,aAAO,EAAE,CAAC;CAC/C,MAAM,WAAW,iBAAiB,IAAIA,aAAO,EAAE,CAAC;CAChD,MAAM,OAAO,iBAAiB,IAAI5B,aAAO,EAAE,CAAC;CAC5C,MAAM,WAAW,iBAAiB,IAAIA,aAAO,EAAE,CAAC;AAChD;CACA;CACA,SAAS,kBAAkB,EAAE,KAAK,EAAE,KAAK,GAAG;AAC5C;CACA,CAAC,KAAK,EAAE,KAAK,IAAI,EAAE,KAAK,GAAG;AAC3B;CACA,EAAE,OAAO;AACT;CACA,EAAE;AACF;CACA,CAAC,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,CAAC;CAC/C,CAAC,MAAM,cAAc,GAAG,KAAK,CAAC,UAAU,KAAK,KAAK,CAAC,UAAU,CAAC;CAC9D,CAAC,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,KAAK,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC;CACtE,CAAC,MAAM,YAAY,GAAG,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC;AACxD;CACA,CAAC,KAAK,EAAE,SAAS,IAAI,EAAE,cAAc,IAAI,EAAE,QAAQ,IAAI,EAAE,YAAY,GAAG;AACxE;CACA,EAAE,MAAM,IAAI,KAAK,EAAE,CAAC;AACpB;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA;CACA,SAAS,oBAAoB,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,GAAG;AAC5D;CACA,CAAC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;CACrC,CAAC,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;CACpC,CAAC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CAChC,CAAC,MAAM,KAAK,GAAG,aAAa,KAAK,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC;AACnE;CACA,CAAC,OAAO,IAAIP,qBAAe,EAAE,IAAI,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;AAClF;CACA,CAAC;AACD;CACA;CACA;CACA,SAAS,qBAAqB,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,GAAG,CAAC,GAAG;AACjE;CACA,CAAC,KAAK,IAAI,CAAC,4BAA4B,GAAG;AAC1C;CACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjD;CACA,GAAG,MAAM,EAAE,GAAG,CAAC,GAAG,YAAY,CAAC;CAC/B,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;CACrC,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;CAC1D,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;CAC1D,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;AAC1D;CACA,GAAG;AACH;CACA,EAAE,MAAM;AACR;CACA,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;CAC7B,EAAE,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC;CACjC,EAAE,MAAM,UAAU,GAAG,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC;CAC5E,EAAE,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;CACvE,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AACzB;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA;CACA,SAAS,eAAe,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,GAAG;AAClD;CACA,CAAC,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC;CACrC,CAAC,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC;CACrC,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACxD;CACA,EAAE,WAAW,EAAE,CAAC,EAAE,IAAI,WAAW,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC/C;CACA,EAAE;AACF;CACA,CAAC;AACD;CACA;CACA,SAAS,mBAAmB,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,GAAG;AACpD;CACA,CAAC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CAChC,CAAC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CAChC,CAAC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;CAC9B,CAAC,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;AAC5C;CACA,CAAC,UAAU,CAAC,mBAAmB,EAAE,QAAQ,CAAC,UAAU,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC;CACxE,CAAC,WAAW,CAAC,mBAAmB,EAAE,QAAQ,CAAC,UAAU,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC;AAC1E;CACA,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAC5B;CACA,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChC;CACA,EAAE,MAAM,MAAM,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC;AAC/C;CACA,EAAE,KAAK,MAAM,KAAK,CAAC,GAAG;AACtB;CACA,GAAG,MAAM,SAAS,GAAG,UAAU,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC;CAClD,GAAG,WAAW,CAAC,gBAAgB,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,EAAE,CAAC;AAC7F;CACA,GAAG,eAAe,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;AACnD;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC;CAC3E,CAAC,MAAM,CAAC,kBAAkB,EAAE,OAAO,EAAE,CAAC;AACtC;CACA,CAAC,OAAO,MAAM,CAAC;AACf;CACA,CAAC;AACD;CACA;CACA,SAAS,gBAAgB,EAAE,SAAS,EAAE,eAAe,EAAE,oBAAoB,EAAE,CAAC,EAAE,MAAM,GAAG;AACzF;CACA,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;CAC7B,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG;AACxD;CACA,EAAE,MAAM,SAAS,GAAG,eAAe,EAAE,CAAC,EAAE,CAAC;CACzC,EAAE,MAAM,cAAc,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;AACxC;CACA,EAAE,KAAK,SAAS,KAAK,CAAC,GAAG,SAAS;AAClC;CACA,EAAE,KAAK,CAAC,mBAAmB,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC;AACjD;CACA,EAAE,KAAK,oBAAoB,GAAG;AAC9B;CACA,GAAG,YAAY,CAAC,eAAe,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACpD;CACA,GAAG,MAAM;AACT;CACA,GAAG,YAAY,CAAC,eAAe,EAAE,KAAK,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,CAAC;AAClE;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,MAAM,CAAC,GAAG,EAAE,YAAY,EAAE,CAAC;AAC5B;CACA,CAAC;AACD;CACA;CACA,SAAS,qBAAqB,EAAE,UAAU,EAAE,OAAO,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,EAAE,cAAc,GAAG,IAAIc,oBAAc,EAAE,GAAG;AAC5J;CACA,CAAC,MAAM,SAAS,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC;CAClD,CAAC,MAAM,EAAE,SAAS,GAAG,KAAK,EAAE,WAAW,GAAG,KAAK,EAAE,cAAc,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjF;CACA,CAAC,MAAM,cAAc,GAAG,IAAI,GAAG,EAAE,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC;CAC7E,CAAC,MAAM,UAAU,GAAG,EAAE,CAAC;AACvB;CACA,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;AAChB;CACA,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;CAC9B,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG;AAChD;CACA,EAAE,MAAM,QAAQ,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;CACnC,EAAE,IAAI,eAAe,GAAG,CAAC,CAAC;AAC1B;CACA;CACA,EAAE,KAAK,SAAS,OAAO,QAAQ,CAAC,KAAK,KAAK,IAAI,EAAE,GAAG;AACnD;CACA,GAAG,MAAM,IAAI,KAAK,EAAE,qJAAqJ,EAAE,CAAC;AAC5K;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,MAAM,IAAI,IAAI,QAAQ,CAAC,UAAU,GAAG;AAC5C;CACA,GAAG,KAAK,EAAE,cAAc,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG;AACvC;CACA,IAAI,MAAM,IAAI,KAAK,EAAE,sFAAsF,GAAG,IAAI,GAAG,8DAA8D,EAAE,CAAC;AACtL;CACA,IAAI;AACJ;CACA,GAAG,KAAK,UAAU,EAAE,IAAI,EAAE,KAAK,SAAS,GAAG;AAC3C;CACA,IAAI,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AAC5B;CACA,IAAI;AACJ;CACA,GAAG,UAAU,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,CAAC;CAC1D,GAAG,eAAe,GAAG,CAAC;AACtB;CACA,GAAG;AACH;CACA;CACA,EAAE,KAAK,eAAe,KAAK,cAAc,CAAC,IAAI,GAAG;AACjD;CACA,GAAG,MAAM,IAAI,KAAK,EAAE,uFAAuF,EAAE,CAAC;AAC9G;CACA,GAAG;AACH;CACA,EAAE,KAAK,SAAS,GAAG;AACnB;CACA,GAAG,IAAI,KAAK,CAAC;CACb,GAAG,KAAK,SAAS,GAAG;AACpB;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC;AACjC;CACA,IAAI,MAAM,KAAK,QAAQ,CAAC,UAAU,CAAC,QAAQ,KAAK,SAAS,GAAG;AAC5D;CACA,IAAI,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;AAC/C;CACA,IAAI,MAAM;AACV;CACA,IAAI,MAAM,IAAI,KAAK,EAAE,yFAAyF,EAAE,CAAC;AACjH;CACA,IAAI;AACJ;CACA,GAAG,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;CAC/C,GAAG,MAAM,IAAI,KAAK,CAAC;AACnB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;CACA,CAAC,KAAK,SAAS,GAAG;AAClB;CACA,EAAE,IAAI,gBAAgB,GAAG,KAAK,CAAC;CAC/B,EAAE,KAAK,EAAE,cAAc,CAAC,KAAK,GAAG;AAChC;CACA,GAAG,IAAI,UAAU,GAAG,CAAC,CAAC;CACtB,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG;AAClD;CACA,IAAI,UAAU,IAAI,UAAU,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;AAC9C;CACA,IAAI;AACJ;CACA,GAAG,cAAc,CAAC,QAAQ,EAAE,IAAId,qBAAe,EAAE,IAAI,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;CAC7F,GAAG,gBAAgB,GAAG,IAAI,CAAC;AAC3B;CACA,GAAG;AACH;CACA,EAAE,KAAK,WAAW,IAAI,gBAAgB,GAAG;AACzC;CACA,GAAG,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC;CAC5C,GAAG,IAAI,YAAY,GAAG,CAAC,CAAC;CACxB,GAAG,IAAI,WAAW,GAAG,CAAC,CAAC;CACvB,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG;AAClD;CACA,IAAI,MAAM,QAAQ,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;CACrC,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;CACjC,IAAI,KAAK,cAAc,EAAE,CAAC,EAAE,KAAK,IAAI,GAAG;AACxC;CACA,KAAK,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG;AAC9C;CACA,MAAM,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,WAAW,EAAE,CAAC;CACtE,MAAM,YAAY,GAAG,CAAC;AACtB;CACA,MAAM;AACN;CACA,KAAK;AACL;CACA,IAAI,WAAW,IAAI,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;AACtD;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA;CACA,CAAC,MAAM,MAAM,IAAI,IAAI,UAAU,GAAG;AAClC;CACA,EAAE,MAAM,QAAQ,GAAG,UAAU,EAAE,IAAI,EAAE,CAAC;CACtC,EAAE,KAAK,IAAI,IAAI,IAAI,cAAc,CAAC,UAAU,EAAE,GAAG;AACjD;CACA,GAAG,IAAI,KAAK,GAAG,CAAC,CAAC;CACjB,GAAG,MAAM,MAAM,GAAG,IAAI,QAAQ,GAAG;AACjC;CACA,IAAI,KAAK,IAAI,QAAQ,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC;AACnC;CACA,IAAI;AACJ;CACA,GAAG,cAAc,CAAC,YAAY,EAAE,IAAI,EAAE,oBAAoB,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC;AAC/F;CACA,GAAG;AACH;CACA,EAAE,MAAM,eAAe,GAAG,cAAc,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC;CAC5D,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;CACjB,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACtD;CACA,GAAG,MAAM,IAAI,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;CAC9B,GAAG,KAAK,cAAc,EAAE,CAAC,EAAE,KAAK,IAAI,GAAG;AACvC;CACA,IAAI,qBAAqB,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,CAAC;AAC3D;CACA,IAAI;AACJ;CACA,GAAG,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC;AACxB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,cAAc,CAAC;AACvB;CACA,CAAC;AACD;CACA,SAAS,uBAAuB,EAAE,CAAC,EAAE,CAAC,GAAG;AACzC;CACA,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,GAAG;AACjC;CACA,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;AACjB;CACA,EAAE;AACF;CACA,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,GAAG;AAC9B;CACA,EAAE,OAAO,KAAK,CAAC;AACf;CACA,EAAE;AACF;CACA,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC9C;CACA,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,GAAG;AAC3B;CACA,GAAG,OAAO,KAAK,CAAC;AAChB;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,OAAO,IAAI,CAAC;AACb;CACA,CAAC;AACD;CACA;CACA,MAAM,YAAY,CAAC;AACnB;CACA,CAAC,WAAW,EAAE,IAAI,GAAG;AACrB;CACA,EAAE,IAAI,CAAC,WAAW,GAAG,IAAIO,aAAO,EAAE,CAAC;CACnC,EAAE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;CAC3B,EAAE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;CAC3B,EAAE,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,CAAC;CAC5B,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACnB;CACA,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;AAChB;CACA,EAAE;AACF;CACA,CAAC,MAAM,GAAG;AACV;CACA,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CACzB,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,cAAc,GAAG,EAAE,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC,CAAC;CAC5G,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;CAC5C,EAAE,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;CAC3D,EAAE,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;AACvC;CACA,EAAE,KAAK,QAAQ,GAAG;AAClB;CACA;CACA,GAAG,KAAK,EAAE,QAAQ,CAAC,WAAW,GAAG;AACjC;CACA,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;AAClC;CACA,IAAI;AACJ;CACA,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;AACrB;CACA;CACA,GAAG,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;CAC9C,GAAG,KAAK,EAAE,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM,GAAG;AAClF;CACA,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,KAAK,EAAE,CAAC;AAC7C;CACA,IAAI,MAAM;AACV;CACA,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,EAAE,CAAC;AAC1C;CACA,IAAI;AACJ;CACA,GAAG,MAAM;AACT;CACA,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC5B;CACA,GAAG;AACH;CACA,EAAE;AACF;CACA,CAAC,SAAS,GAAG;AACb;CACA,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CACzB,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,cAAc,GAAG,EAAE,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC,CAAC;CAC5G,EAAE,MAAM,SAAS;CACjB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE;CAC9C,GAAG,IAAI,CAAC,YAAY,KAAK,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO;CAC7D,GAAG,uBAAuB,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE;CACpG,GAAG,IAAI,CAAC,cAAc,KAAK,cAAc,CAAC;AAC1C;CACA,EAAE,OAAO,EAAE,SAAS,CAAC;AACrB;CACA,EAAE;AACF;CACA,CAAC;AACD;CACO,MAAM,uBAAuB,CAAC;AACrC;CACA,CAAC,WAAW,EAAE,MAAM,GAAG;AACvB;CACA,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG;AACnC;CACA,GAAG,MAAM,GAAG,EAAE,MAAM,EAAE,CAAC;AACvB;CACA,GAAG;AACH;CACA,EAAE,MAAM,WAAW,GAAG,EAAE,CAAC;CACzB,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,IAAI;AAC5B;CACA,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC,IAAI;AAChC;CACA,IAAI,KAAK,CAAC,CAAC,MAAM,GAAG;AACpB;CACA,KAAK,WAAW,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAC3B;CACA,KAAK;AACL;CACA,IAAI,EAAE,CAAC;AACP;CACA,GAAG,EAAE,CAAC;AACN;CACA,EAAE,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;CAC5B,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;CACxB,EAAE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;CACnC,EAAE,IAAI,CAAC,UAAU,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;CAC9E,EAAE,IAAI,CAAC,qBAAqB,GAAG,IAAI,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAIO,oBAAc,EAAE,EAAE,CAAC;CACxG,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,EAAE,CAAC;AAChC;CACA,EAAE;AACF;CACA,CAAC,YAAY,GAAG;AAChB;CACA,EAAE,MAAM,SAAS,GAAG,EAAE,CAAC;CACvB,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,IAAI;AAC/B;CACA,GAAG,KAAK,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,GAAG;AACzC;CACA,IAAI,SAAS,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AACvC;CACA,IAAI,MAAM;AACV;CACA,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;AACpC;CACA,IAAI;AACJ;CACA,GAAG,EAAE,CAAC;CACN,EAAE,OAAO,SAAS,CAAC;AACnB;CACA,EAAE;AACF;CACA,CAAC,QAAQ,EAAE,cAAc,GAAG,IAAIA,oBAAc,EAAE,GAAG;AACnD;CACA;CACA,EAAE,IAAI,cAAc,GAAG,EAAE,CAAC;CAC1B,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,qBAAqB,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;CACtE,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACpD;CACA,GAAG,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;CAC5B,GAAG,MAAM,IAAI,GAAG,qBAAqB,EAAE,CAAC,EAAE,CAAC;CAC3C,GAAG,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;CACrC,GAAG,KAAK,EAAE,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,GAAG;AAC3C;CACA,IAAI,IAAI,CAAC,wBAAwB,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;CAChD,IAAI,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACjC;CACA,IAAI,KAAK,EAAE,IAAI,GAAG;AAClB;CACA,KAAK,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,YAAY,EAAE,IAAI,EAAE,EAAE,CAAC;AACpD;CACA,KAAK,MAAM;AACX;CACA,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;AACnB;CACA,KAAK;AACL;CACA,IAAI,MAAM;AACV;CACA,IAAI,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AAChC;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,EAAE,SAAS,EAAE,cAAc,EAAE,EAAE,cAAc,EAAE,CAAC;AAChG;CACA,EAAE,MAAM,MAAM,GAAG,IAAI,cAAc,CAAC,UAAU,GAAG;AACjD;CACA,GAAG,cAAc,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC;AACvD;CACA,GAAG;AACH;CACA,EAAE,OAAO,cAAc,CAAC;AACxB;CACA,EAAE;AACF;CACA,CAAC,wBAAwB,EAAE,IAAI,EAAE,cAAc,GAAG,IAAIA,oBAAc,EAAE,GAAG;AACzE;CACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;CACjC,EAAE,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;CACzD,EAAE,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC;CAC7D,EAAE,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC;CAC/D,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;CACzC,EAAE,MAAM,gBAAgB,GAAG,cAAc,CAAC,UAAU,CAAC;AACrD;CACA;CACA,EAAE,KAAK,EAAE,cAAc,CAAC,KAAK,GAAG;AAChC;CACA,GAAG,cAAc,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AACzC;CACA,GAAG;AACH;CACA,EAAE,KAAK,EAAE,gBAAgB,CAAC,QAAQ,GAAG;AACrC;CACA,GAAG,cAAc,CAAC,YAAY,EAAE,UAAU,EAAE,oBAAoB,EAAE,UAAU,CAAC,QAAQ,EAAE,EAAE,CAAC;AAC1F;CACA,GAAG;AACH;CACA,EAAE,KAAK,aAAa,IAAI,EAAE,gBAAgB,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,GAAG;AACzE;CACA,GAAG,cAAc,CAAC,YAAY,EAAE,QAAQ,EAAE,oBAAoB,EAAE,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;AACtF;CACA,GAAG;AACH;CACA,EAAE,KAAK,cAAc,IAAI,EAAE,gBAAgB,CAAC,OAAO,IAAI,UAAU,CAAC,OAAO,GAAG;AAC5E;CACA,GAAG,cAAc,CAAC,YAAY,EAAE,SAAS,EAAE,oBAAoB,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC;AACxF;CACA,GAAG;AACH;CACA;CACA,EAAE,kBAAkB,EAAE,QAAQ,CAAC,KAAK,EAAE,cAAc,CAAC,KAAK,EAAE,CAAC;CAC7D,EAAE,kBAAkB,EAAE,UAAU,CAAC,QAAQ,EAAE,gBAAgB,CAAC,QAAQ,EAAE,CAAC;AACvE;CACA,EAAE,KAAK,aAAa,GAAG;AACvB;CACA,GAAG,kBAAkB,EAAE,UAAU,CAAC,MAAM,EAAE,gBAAgB,CAAC,MAAM,EAAE,CAAC;AACpE;CACA,GAAG;AACH;CACA,EAAE,KAAK,cAAc,GAAG;AACxB;CACA,GAAG,kBAAkB,EAAE,UAAU,CAAC,OAAO,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC;AACtE;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;CACvC,EAAE,MAAM,MAAM,GAAG,aAAa,GAAG,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;CAC1D,EAAE,MAAM,OAAO,GAAG,cAAc,GAAG,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;CAC7D,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC;CAC1D,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,eAAe,CAAC,MAAM,CAAC;CACtD,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC;CACxD,EAAE,MAAM,oBAAoB,GAAG,QAAQ,CAAC,oBAAoB,CAAC;CAC7D,EAAE,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC;CACrD,EAAE,MAAM,YAAY,GAAG,IAAIsB,aAAO,EAAE,CAAC;CACrC,EAAE,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;AACnD;CACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChE;CACA,GAAG,eAAe,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;CACtD,GAAG,KAAK,MAAM,GAAG;AACjB;CACA,IAAI,aAAa,CAAC,mBAAmB,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;AACnD;CACA,IAAI;AACJ;CACA,GAAG,KAAK,OAAO,GAAG;AAClB;CACA,IAAI,eAAe,CAAC,mBAAmB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;CACtD,IAAI,cAAc,CAAC,mBAAmB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;AACrD;CACA,IAAI;AACJ;CACA;CACA,GAAG,KAAK,eAAe,GAAG;AAC1B;CACA,IAAI,KAAK,aAAa,GAAG;AACzB;CACA,KAAK,gBAAgB,EAAE,aAAa,EAAE,eAAe,EAAE,oBAAoB,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC;AAClG;CACA,KAAK;AACL;CACA,IAAI,KAAK,WAAW,GAAG;AACvB;CACA,KAAK,gBAAgB,EAAE,WAAW,EAAE,eAAe,EAAE,oBAAoB,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC;AAC9F;CACA,KAAK;AACL;CACA,IAAI,KAAK,YAAY,GAAG;AACxB;CACA,KAAK,gBAAgB,EAAE,YAAY,EAAE,eAAe,EAAE,oBAAoB,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC;AAChG;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA;CACA,GAAG,KAAK,IAAI,CAAC,aAAa,GAAG;AAC7B;CACA,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC;CAC7C,IAAI,KAAK,MAAM,GAAG;AAClB;CACA,KAAK,mBAAmB,EAAE,IAAI,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC;AACnD;CACA,KAAK;AACL;CACA,IAAI,KAAK,OAAO,GAAG;AACnB;CACA,KAAK,mBAAmB,EAAE,IAAI,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC;AACpD;CACA,KAAK;AACL;CACA,IAAI;AACJ;CACA;CACA,GAAG,KAAK,oBAAoB,GAAG;AAC/B;CACA,IAAI,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;AACrD;CACA,IAAI;AACJ;CACA,GAAG,gBAAgB,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC;AAClG;CACA,GAAG,KAAK,MAAM,GAAG;AACjB;CACA,IAAI,KAAK,oBAAoB,GAAG;AAChC;CACA,KAAK,aAAa,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;AACrD;CACA,KAAK;AACL;CACA,IAAI,gBAAgB,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,CAAC;AAC3F;CACA,IAAI;AACJ;CACA,GAAG,KAAK,OAAO,GAAG;AAClB;CACA,IAAI,KAAK,oBAAoB,GAAG;AAChC;CACA,KAAK,cAAc,CAAC,kBAAkB,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;AAC3D;CACA,KAAK;AACL;CACA,IAAI,gBAAgB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC;AACnH;CACA,IAAI;AACJ;CACA,GAAG;AACH;CACA;CACA,EAAE,MAAM,MAAM,CAAC,IAAI,IAAI,CAAC,UAAU,GAAG;AACrC;CACA,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC;CACpC,GAAG,KAAK,GAAG,KAAK,UAAU,IAAI,GAAG,KAAK,SAAS,IAAI,GAAG,KAAK,QAAQ,IAAI,IAAI,GAAG,IAAI,UAAU,EAAE,GAAG;AACjG;CACA,IAAI,SAAS;AACb;CACA,IAAI;AACJ;CACA,GAAG,KAAK,EAAE,gBAAgB,EAAE,GAAG,EAAE,GAAG;AACpC;CACA,IAAI,cAAc,CAAC,YAAY,EAAE,GAAG,EAAE,oBAAoB,EAAE,UAAU,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;AAClF;CACA,IAAI;AACJ;CACA,GAAG,kBAAkB,EAAE,UAAU,EAAE,GAAG,EAAE,EAAE,gBAAgB,EAAE,GAAG,EAAE,EAAE,CAAC;CACpE,GAAG,qBAAqB,EAAE,UAAU,EAAE,GAAG,EAAE,EAAE,gBAAgB,EAAE,GAAG,EAAE,EAAE,CAAC;AACvE;CACA,GAAG;AACH;CACA,EAAE,OAAO,cAAc,CAAC;AACxB;CACA,EAAE;AACF;CACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}