{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { DeviceOrientationControls as DeviceOrientationControls$1 } from 'three-stdlib';\nconst DeviceOrientationControls = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    camera,\n    onChange,\n    makeDefault,\n    ...rest\n  } = props;\n  const defaultCamera = useThree(state => state.camera);\n  const invalidate = useThree(state => state.invalidate);\n  const get = useThree(state => state.get);\n  const set = useThree(state => state.set);\n  const explCamera = camera || defaultCamera;\n  const [controls] = React.useState(() => new DeviceOrientationControls$1(explCamera));\n  React.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (onChange) onChange(e);\n    };\n    controls == null ? void 0 : controls.addEventListener == null ? void 0 : controls.addEventListener('change', callback);\n    return () => controls == null ? void 0 : controls.removeEventListener == null ? void 0 : controls.removeEventListener('change', callback);\n  }, [onChange, controls, invalidate]);\n  useFrame(() => controls == null ? void 0 : controls.update(), -1);\n  React.useEffect(() => {\n    const current = controls;\n    current == null ? void 0 : current.connect();\n    return () => current == null ? void 0 : current.dispose();\n  }, [controls]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return controls ? /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls\n  }, rest)) : null;\n});\nexport { DeviceOrientationControls };", "map": {"version": 3, "names": ["_extends", "useThree", "useFrame", "React", "DeviceOrientationControls", "DeviceOrientationControls$1", "forwardRef", "props", "ref", "camera", "onChange", "makeDefault", "rest", "defaultCamera", "state", "invalidate", "get", "set", "explCamera", "controls", "useState", "useEffect", "callback", "e", "addEventListener", "removeEventListener", "update", "current", "connect", "dispose", "old", "createElement", "object"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/DeviceOrientationControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { DeviceOrientationControls as DeviceOrientationControls$1 } from 'three-stdlib';\n\nconst DeviceOrientationControls = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    camera,\n    onChange,\n    makeDefault,\n    ...rest\n  } = props;\n  const defaultCamera = useThree(state => state.camera);\n  const invalidate = useThree(state => state.invalidate);\n  const get = useThree(state => state.get);\n  const set = useThree(state => state.set);\n  const explCamera = camera || defaultCamera;\n  const [controls] = React.useState(() => new DeviceOrientationControls$1(explCamera));\n  React.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (onChange) onChange(e);\n    };\n\n    controls == null ? void 0 : controls.addEventListener == null ? void 0 : controls.addEventListener('change', callback);\n    return () => controls == null ? void 0 : controls.removeEventListener == null ? void 0 : controls.removeEventListener('change', callback);\n  }, [onChange, controls, invalidate]);\n  useFrame(() => controls == null ? void 0 : controls.update(), -1);\n  React.useEffect(() => {\n    const current = controls;\n    current == null ? void 0 : current.connect();\n    return () => current == null ? void 0 : current.dispose();\n  }, [controls]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return controls ? /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls\n  }, rest)) : null;\n});\n\nexport { DeviceOrientationControls };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,yBAAyB,IAAIC,2BAA2B,QAAQ,cAAc;AAEvF,MAAMD,yBAAyB,GAAG,aAAaD,KAAK,CAACG,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC9E,MAAM;IACJC,MAAM;IACNC,QAAQ;IACRC,WAAW;IACX,GAAGC;EACL,CAAC,GAAGL,KAAK;EACT,MAAMM,aAAa,GAAGZ,QAAQ,CAACa,KAAK,IAAIA,KAAK,CAACL,MAAM,CAAC;EACrD,MAAMM,UAAU,GAAGd,QAAQ,CAACa,KAAK,IAAIA,KAAK,CAACC,UAAU,CAAC;EACtD,MAAMC,GAAG,GAAGf,QAAQ,CAACa,KAAK,IAAIA,KAAK,CAACE,GAAG,CAAC;EACxC,MAAMC,GAAG,GAAGhB,QAAQ,CAACa,KAAK,IAAIA,KAAK,CAACG,GAAG,CAAC;EACxC,MAAMC,UAAU,GAAGT,MAAM,IAAII,aAAa;EAC1C,MAAM,CAACM,QAAQ,CAAC,GAAGhB,KAAK,CAACiB,QAAQ,CAAC,MAAM,IAAIf,2BAA2B,CAACa,UAAU,CAAC,CAAC;EACpFf,KAAK,CAACkB,SAAS,CAAC,MAAM;IACpB,MAAMC,QAAQ,GAAGC,CAAC,IAAI;MACpBR,UAAU,CAAC,CAAC;MACZ,IAAIL,QAAQ,EAAEA,QAAQ,CAACa,CAAC,CAAC;IAC3B,CAAC;IAEDJ,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACK,gBAAgB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGL,QAAQ,CAACK,gBAAgB,CAAC,QAAQ,EAAEF,QAAQ,CAAC;IACtH,OAAO,MAAMH,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACM,mBAAmB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGN,QAAQ,CAACM,mBAAmB,CAAC,QAAQ,EAAEH,QAAQ,CAAC;EAC3I,CAAC,EAAE,CAACZ,QAAQ,EAAES,QAAQ,EAAEJ,UAAU,CAAC,CAAC;EACpCb,QAAQ,CAAC,MAAMiB,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACO,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACjEvB,KAAK,CAACkB,SAAS,CAAC,MAAM;IACpB,MAAMM,OAAO,GAAGR,QAAQ;IACxBQ,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,OAAO,CAAC,CAAC;IAC5C,OAAO,MAAMD,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACE,OAAO,CAAC,CAAC;EAC3D,CAAC,EAAE,CAACV,QAAQ,CAAC,CAAC;EACdhB,KAAK,CAACkB,SAAS,CAAC,MAAM;IACpB,IAAIV,WAAW,EAAE;MACf,MAAMmB,GAAG,GAAGd,GAAG,CAAC,CAAC,CAACG,QAAQ;MAC1BF,GAAG,CAAC;QACFE;MACF,CAAC,CAAC;MACF,OAAO,MAAMF,GAAG,CAAC;QACfE,QAAQ,EAAEW;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACnB,WAAW,EAAEQ,QAAQ,CAAC,CAAC;EAC3B,OAAOA,QAAQ,GAAG,aAAahB,KAAK,CAAC4B,aAAa,CAAC,WAAW,EAAE/B,QAAQ,CAAC;IACvEQ,GAAG,EAAEA,GAAG;IACRwB,MAAM,EAAEb;EACV,CAAC,EAAEP,IAAI,CAAC,CAAC,GAAG,IAAI;AAClB,CAAC,CAAC;AAEF,SAASR,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}