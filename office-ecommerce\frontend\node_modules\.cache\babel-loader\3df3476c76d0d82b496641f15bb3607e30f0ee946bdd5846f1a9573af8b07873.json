{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\nimport { useFBO } from './useFBO.js';\nconst isFunction = node => typeof node === 'function';\nconst OrthographicCamera = /*#__PURE__*/React.forwardRef(({\n  envMap,\n  resolution = 256,\n  frames = Infinity,\n  children,\n  makeDefault,\n  ...props\n}, ref) => {\n  const set = useThree(({\n    set\n  }) => set);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const size = useThree(({\n    size\n  }) => size);\n  const cameraRef = React.useRef(null);\n  const groupRef = React.useRef(null);\n  const fbo = useFBO(resolution);\n  React.useLayoutEffect(() => {\n    if (!props.manual) {\n      cameraRef.current.updateProjectionMatrix();\n    }\n  }, [size, props]);\n  React.useLayoutEffect(() => {\n    cameraRef.current.updateProjectionMatrix();\n  });\n  React.useLayoutEffect(() => {\n    if (makeDefault) {\n      const oldCam = camera;\n      set(() => ({\n        camera: cameraRef.current\n      }));\n      return () => set(() => ({\n        camera: oldCam\n      }));\n    } // The camera should not be part of the dependency list because this components camera is a stable reference\n    // that must exchange the default, and clean up after itself on unmount.\n  }, [cameraRef, makeDefault, set]);\n  let count = 0;\n  let oldEnvMap = null;\n  const functional = isFunction(children);\n  useFrame(state => {\n    if (functional && (frames === Infinity || count < frames)) {\n      groupRef.current.visible = false;\n      state.gl.setRenderTarget(fbo);\n      oldEnvMap = state.scene.background;\n      if (envMap) state.scene.background = envMap;\n      state.gl.render(state.scene, cameraRef.current);\n      state.scene.background = oldEnvMap;\n      state.gl.setRenderTarget(null);\n      groupRef.current.visible = true;\n      count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"orthographicCamera\", _extends({\n    left: size.width / -2,\n    right: size.width / 2,\n    top: size.height / 2,\n    bottom: size.height / -2,\n    ref: mergeRefs([cameraRef, ref])\n  }, props), !functional && children), /*#__PURE__*/React.createElement(\"group\", {\n    ref: groupRef\n  }, functional && children(fbo.texture)));\n});\nexport { OrthographicCamera };", "map": {"version": 3, "names": ["_extends", "React", "useThree", "useFrame", "mergeRefs", "useFBO", "isFunction", "node", "OrthographicCamera", "forwardRef", "envMap", "resolution", "frames", "Infinity", "children", "makeDefault", "props", "ref", "set", "camera", "size", "cameraRef", "useRef", "groupRef", "fbo", "useLayoutEffect", "manual", "current", "updateProjectionMatrix", "oldCam", "count", "oldEnvMap", "functional", "state", "visible", "gl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scene", "background", "render", "createElement", "Fragment", "left", "width", "right", "top", "height", "bottom", "texture"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/OrthographicCamera.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\nimport { useFBO } from './useFBO.js';\n\nconst isFunction = node => typeof node === 'function';\n\nconst OrthographicCamera = /*#__PURE__*/React.forwardRef(({\n  envMap,\n  resolution = 256,\n  frames = Infinity,\n  children,\n  makeDefault,\n  ...props\n}, ref) => {\n  const set = useThree(({\n    set\n  }) => set);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const size = useThree(({\n    size\n  }) => size);\n  const cameraRef = React.useRef(null);\n  const groupRef = React.useRef(null);\n  const fbo = useFBO(resolution);\n  React.useLayoutEffect(() => {\n    if (!props.manual) {\n      cameraRef.current.updateProjectionMatrix();\n    }\n  }, [size, props]);\n  React.useLayoutEffect(() => {\n    cameraRef.current.updateProjectionMatrix();\n  });\n  React.useLayoutEffect(() => {\n    if (makeDefault) {\n      const oldCam = camera;\n      set(() => ({\n        camera: cameraRef.current\n      }));\n      return () => set(() => ({\n        camera: oldCam\n      }));\n    } // The camera should not be part of the dependency list because this components camera is a stable reference\n    // that must exchange the default, and clean up after itself on unmount.\n\n  }, [cameraRef, makeDefault, set]);\n  let count = 0;\n  let oldEnvMap = null;\n  const functional = isFunction(children);\n  useFrame(state => {\n    if (functional && (frames === Infinity || count < frames)) {\n      groupRef.current.visible = false;\n      state.gl.setRenderTarget(fbo);\n      oldEnvMap = state.scene.background;\n      if (envMap) state.scene.background = envMap;\n      state.gl.render(state.scene, cameraRef.current);\n      state.scene.background = oldEnvMap;\n      state.gl.setRenderTarget(null);\n      groupRef.current.visible = true;\n      count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"orthographicCamera\", _extends({\n    left: size.width / -2,\n    right: size.width / 2,\n    top: size.height / 2,\n    bottom: size.height / -2,\n    ref: mergeRefs([cameraRef, ref])\n  }, props), !functional && children), /*#__PURE__*/React.createElement(\"group\", {\n    ref: groupRef\n  }, functional && children(fbo.texture)));\n});\n\nexport { OrthographicCamera };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,OAAOC,SAAS,MAAM,kBAAkB;AACxC,SAASC,MAAM,QAAQ,aAAa;AAEpC,MAAMC,UAAU,GAAGC,IAAI,IAAI,OAAOA,IAAI,KAAK,UAAU;AAErD,MAAMC,kBAAkB,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAC;EACxDC,MAAM;EACNC,UAAU,GAAG,GAAG;EAChBC,MAAM,GAAGC,QAAQ;EACjBC,QAAQ;EACRC,WAAW;EACX,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,GAAG,GAAGhB,QAAQ,CAAC,CAAC;IACpBgB;EACF,CAAC,KAAKA,GAAG,CAAC;EACV,MAAMC,MAAM,GAAGjB,QAAQ,CAAC,CAAC;IACvBiB;EACF,CAAC,KAAKA,MAAM,CAAC;EACb,MAAMC,IAAI,GAAGlB,QAAQ,CAAC,CAAC;IACrBkB;EACF,CAAC,KAAKA,IAAI,CAAC;EACX,MAAMC,SAAS,GAAGpB,KAAK,CAACqB,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMC,QAAQ,GAAGtB,KAAK,CAACqB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAME,GAAG,GAAGnB,MAAM,CAACM,UAAU,CAAC;EAC9BV,KAAK,CAACwB,eAAe,CAAC,MAAM;IAC1B,IAAI,CAACT,KAAK,CAACU,MAAM,EAAE;MACjBL,SAAS,CAACM,OAAO,CAACC,sBAAsB,CAAC,CAAC;IAC5C;EACF,CAAC,EAAE,CAACR,IAAI,EAAEJ,KAAK,CAAC,CAAC;EACjBf,KAAK,CAACwB,eAAe,CAAC,MAAM;IAC1BJ,SAAS,CAACM,OAAO,CAACC,sBAAsB,CAAC,CAAC;EAC5C,CAAC,CAAC;EACF3B,KAAK,CAACwB,eAAe,CAAC,MAAM;IAC1B,IAAIV,WAAW,EAAE;MACf,MAAMc,MAAM,GAAGV,MAAM;MACrBD,GAAG,CAAC,OAAO;QACTC,MAAM,EAAEE,SAAS,CAACM;MACpB,CAAC,CAAC,CAAC;MACH,OAAO,MAAMT,GAAG,CAAC,OAAO;QACtBC,MAAM,EAAEU;MACV,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IACF;EAEF,CAAC,EAAE,CAACR,SAAS,EAAEN,WAAW,EAAEG,GAAG,CAAC,CAAC;EACjC,IAAIY,KAAK,GAAG,CAAC;EACb,IAAIC,SAAS,GAAG,IAAI;EACpB,MAAMC,UAAU,GAAG1B,UAAU,CAACQ,QAAQ,CAAC;EACvCX,QAAQ,CAAC8B,KAAK,IAAI;IAChB,IAAID,UAAU,KAAKpB,MAAM,KAAKC,QAAQ,IAAIiB,KAAK,GAAGlB,MAAM,CAAC,EAAE;MACzDW,QAAQ,CAACI,OAAO,CAACO,OAAO,GAAG,KAAK;MAChCD,KAAK,CAACE,EAAE,CAACC,eAAe,CAACZ,GAAG,CAAC;MAC7BO,SAAS,GAAGE,KAAK,CAACI,KAAK,CAACC,UAAU;MAClC,IAAI5B,MAAM,EAAEuB,KAAK,CAACI,KAAK,CAACC,UAAU,GAAG5B,MAAM;MAC3CuB,KAAK,CAACE,EAAE,CAACI,MAAM,CAACN,KAAK,CAACI,KAAK,EAAEhB,SAAS,CAACM,OAAO,CAAC;MAC/CM,KAAK,CAACI,KAAK,CAACC,UAAU,GAAGP,SAAS;MAClCE,KAAK,CAACE,EAAE,CAACC,eAAe,CAAC,IAAI,CAAC;MAC9Bb,QAAQ,CAACI,OAAO,CAACO,OAAO,GAAG,IAAI;MAC/BJ,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACF,OAAO,aAAa7B,KAAK,CAACuC,aAAa,CAACvC,KAAK,CAACwC,QAAQ,EAAE,IAAI,EAAE,aAAaxC,KAAK,CAACuC,aAAa,CAAC,oBAAoB,EAAExC,QAAQ,CAAC;IAC5H0C,IAAI,EAAEtB,IAAI,CAACuB,KAAK,GAAG,CAAC,CAAC;IACrBC,KAAK,EAAExB,IAAI,CAACuB,KAAK,GAAG,CAAC;IACrBE,GAAG,EAAEzB,IAAI,CAAC0B,MAAM,GAAG,CAAC;IACpBC,MAAM,EAAE3B,IAAI,CAAC0B,MAAM,GAAG,CAAC,CAAC;IACxB7B,GAAG,EAAEb,SAAS,CAAC,CAACiB,SAAS,EAAEJ,GAAG,CAAC;EACjC,CAAC,EAAED,KAAK,CAAC,EAAE,CAACgB,UAAU,IAAIlB,QAAQ,CAAC,EAAE,aAAab,KAAK,CAACuC,aAAa,CAAC,OAAO,EAAE;IAC7EvB,GAAG,EAAEM;EACP,CAAC,EAAES,UAAU,IAAIlB,QAAQ,CAACU,GAAG,CAACwB,OAAO,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC;AAEF,SAASxC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}