const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const logger = require('../utils/logger');

class FileUploadService {
  constructor() {
    this.uploadBasePath = path.join(__dirname, '../uploads');
    this.maxFileSize = {
      image: 10 * 1024 * 1024, // 10MB
      model: 50 * 1024 * 1024, // 50MB
      document: 5 * 1024 * 1024 // 5MB
    };
    
    this.allowedTypes = {
      image: ['image/jpeg', 'image/png', 'image/webp'],
      model: ['model/gltf-binary', 'model/gltf+json', 'application/octet-stream'],
      document: ['application/pdf', 'text/plain', 'application/msword']
    };
    
    this.allowedExtensions = {
      image: ['.jpg', '.jpeg', '.png', '.webp'],
      model: ['.glb', '.gltf'],
      document: ['.pdf', '.txt', '.doc', '.docx']
    };
  }

  /**
   * Initialize upload directories
   */
  async initializeDirectories() {
    try {
      const directories = [
        path.join(this.uploadBasePath, 'images'),
        path.join(this.uploadBasePath, 'models'),
        path.join(this.uploadBasePath, 'documents'),
        path.join(this.uploadBasePath, 'temp'),
        path.join(this.uploadBasePath, 'thumbnails')
      ];

      for (const dir of directories) {
        try {
          await fs.access(dir);
        } catch {
          await fs.mkdir(dir, { recursive: true });
          logger.info(`Created directory: ${dir}`);
        }
      }
    } catch (error) {
      logger.error('Error initializing upload directories:', error);
      throw error;
    }
  }

  /**
   * Validate file type and size
   */
  validateFile(file, fileType) {
    const errors = [];

    // Check file type
    if (!this.allowedTypes[fileType]) {
      errors.push(`Invalid file type category: ${fileType}`);
      return errors;
    }

    // Check MIME type
    if (!this.allowedTypes[fileType].includes(file.mimetype)) {
      errors.push(`Invalid MIME type. Allowed: ${this.allowedTypes[fileType].join(', ')}`);
    }

    // Check file extension
    const ext = path.extname(file.originalname).toLowerCase();
    if (!this.allowedExtensions[fileType].includes(ext)) {
      errors.push(`Invalid file extension. Allowed: ${this.allowedExtensions[fileType].join(', ')}`);
    }

    // Check file size
    if (file.size > this.maxFileSize[fileType]) {
      errors.push(`File too large. Maximum size: ${this.maxFileSize[fileType] / (1024 * 1024)}MB`);
    }

    return errors;
  }

  /**
   * Generate unique filename
   */
  generateUniqueFilename(originalName) {
    const ext = path.extname(originalName);
    const baseName = path.basename(originalName, ext).replace(/[^a-zA-Z0-9]/g, '_');
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(6).toString('hex');
    return `${baseName}_${timestamp}_${randomString}${ext}`;
  }

  /**
   * Save uploaded file
   */
  async saveFile(file, fileType, subDirectory = '') {
    try {
      // Validate file
      const validationErrors = this.validateFile(file, fileType);
      if (validationErrors.length > 0) {
        throw new Error(`File validation failed: ${validationErrors.join(', ')}`);
      }

      // Generate unique filename
      const uniqueFilename = this.generateUniqueFilename(file.originalname);
      
      // Determine destination path
      const destinationDir = path.join(this.uploadBasePath, fileType, subDirectory);
      await fs.mkdir(destinationDir, { recursive: true });
      
      const destinationPath = path.join(destinationDir, uniqueFilename);

      // Save file
      await fs.writeFile(destinationPath, file.buffer);

      // Generate file metadata
      const fileMetadata = {
        filename: uniqueFilename,
        originalName: file.originalname,
        path: destinationPath,
        relativePath: path.relative(this.uploadBasePath, destinationPath),
        size: file.size,
        mimeType: file.mimetype,
        extension: path.extname(file.originalname).toLowerCase(),
        uploadedAt: new Date().toISOString()
      };

      logger.info(`File saved successfully: ${destinationPath}`);
      return fileMetadata;
    } catch (error) {
      logger.error('Error saving file:', error);
      throw error;
    }
  }

  /**
   * Delete file
   */
  async deleteFile(filePath) {
    try {
      const fullPath = path.isAbsolute(filePath) ? filePath : path.join(this.uploadBasePath, filePath);
      
      try {
        await fs.access(fullPath);
        await fs.unlink(fullPath);
        logger.info(`File deleted successfully: ${fullPath}`);
        return true;
      } catch (error) {
        if (error.code === 'ENOENT') {
          logger.warn(`File not found for deletion: ${fullPath}`);
          return false;
        }
        throw error;
      }
    } catch (error) {
      logger.error('Error deleting file:', error);
      throw error;
    }
  }

  /**
   * Move file from temp to permanent location
   */
  async moveFile(sourcePath, destinationPath) {
    try {
      const fullSourcePath = path.isAbsolute(sourcePath) ? sourcePath : path.join(this.uploadBasePath, sourcePath);
      const fullDestinationPath = path.isAbsolute(destinationPath) ? destinationPath : path.join(this.uploadBasePath, destinationPath);

      // Ensure destination directory exists
      const destinationDir = path.dirname(fullDestinationPath);
      await fs.mkdir(destinationDir, { recursive: true });

      // Move file
      await fs.rename(fullSourcePath, fullDestinationPath);
      
      logger.info(`File moved from ${fullSourcePath} to ${fullDestinationPath}`);
      return fullDestinationPath;
    } catch (error) {
      logger.error('Error moving file:', error);
      throw error;
    }
  }

  /**
   * Get file information
   */
  async getFileInfo(filePath) {
    try {
      const fullPath = path.isAbsolute(filePath) ? filePath : path.join(this.uploadBasePath, filePath);
      const stats = await fs.stat(fullPath);
      
      return {
        path: fullPath,
        relativePath: path.relative(this.uploadBasePath, fullPath),
        size: stats.size,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime,
        isFile: stats.isFile(),
        isDirectory: stats.isDirectory()
      };
    } catch (error) {
      logger.error('Error getting file info:', error);
      throw error;
    }
  }

  /**
   * Generate thumbnail for image (placeholder implementation)
   */
  async generateThumbnail(imagePath, thumbnailPath, options = {}) {
    try {
      const { width = 300, height = 300, quality = 80 } = options;
      
      // This would require a library like sharp for actual image processing
      // For now, we'll just copy the original file as a placeholder
      const fullImagePath = path.isAbsolute(imagePath) ? imagePath : path.join(this.uploadBasePath, imagePath);
      const fullThumbnailPath = path.isAbsolute(thumbnailPath) ? thumbnailPath : path.join(this.uploadBasePath, thumbnailPath);
      
      // Ensure thumbnail directory exists
      const thumbnailDir = path.dirname(fullThumbnailPath);
      await fs.mkdir(thumbnailDir, { recursive: true });
      
      // Copy file as placeholder (in real implementation, use sharp to resize)
      await fs.copyFile(fullImagePath, fullThumbnailPath);
      
      logger.info(`Thumbnail generated: ${fullThumbnailPath}`);
      return fullThumbnailPath;
    } catch (error) {
      logger.error('Error generating thumbnail:', error);
      throw error;
    }
  }

  /**
   * Clean up old temporary files
   */
  async cleanupTempFiles(maxAge = 24 * 60 * 60 * 1000) { // 24 hours default
    try {
      const tempDir = path.join(this.uploadBasePath, 'temp');
      const files = await fs.readdir(tempDir);
      const now = Date.now();
      let deletedCount = 0;

      for (const file of files) {
        const filePath = path.join(tempDir, file);
        const stats = await fs.stat(filePath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          await fs.unlink(filePath);
          deletedCount++;
        }
      }

      logger.info(`Cleaned up ${deletedCount} temporary files`);
      return deletedCount;
    } catch (error) {
      logger.error('Error cleaning up temp files:', error);
      throw error;
    }
  }

  /**
   * Get upload statistics
   */
  async getUploadStats() {
    try {
      const stats = {
        totalFiles: 0,
        totalSize: 0,
        fileTypes: {}
      };

      const directories = ['images', 'models', 'documents'];
      
      for (const dir of directories) {
        const dirPath = path.join(this.uploadBasePath, dir);
        try {
          const files = await fs.readdir(dirPath, { recursive: true });
          let dirSize = 0;
          let fileCount = 0;

          for (const file of files) {
            const filePath = path.join(dirPath, file);
            const fileStat = await fs.stat(filePath);
            
            if (fileStat.isFile()) {
              dirSize += fileStat.size;
              fileCount++;
            }
          }

          stats.fileTypes[dir] = {
            count: fileCount,
            size: dirSize
          };
          
          stats.totalFiles += fileCount;
          stats.totalSize += dirSize;
        } catch (error) {
          // Directory might not exist
          stats.fileTypes[dir] = { count: 0, size: 0 };
        }
      }

      return stats;
    } catch (error) {
      logger.error('Error getting upload stats:', error);
      throw error;
    }
  }

  /**
   * Validate and process multiple files
   */
  async processMultipleFiles(files, fileType, subDirectory = '') {
    try {
      const results = [];
      const errors = [];

      for (let i = 0; i < files.length; i++) {
        try {
          const fileMetadata = await this.saveFile(files[i], fileType, subDirectory);
          results.push(fileMetadata);
        } catch (error) {
          errors.push({
            file: files[i].originalname,
            error: error.message
          });
        }
      }

      return {
        successful: results,
        failed: errors,
        totalProcessed: files.length,
        successCount: results.length,
        errorCount: errors.length
      };
    } catch (error) {
      logger.error('Error processing multiple files:', error);
      throw error;
    }
  }
}

module.exports = new FileUploadService();
