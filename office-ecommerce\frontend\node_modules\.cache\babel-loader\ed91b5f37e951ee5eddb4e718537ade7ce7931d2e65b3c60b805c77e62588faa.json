{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { SelectionBox } from 'three-stdlib';\nimport { useThree } from '@react-three/fiber';\nimport shallow from 'zustand/shallow';\nconst context = /*#__PURE__*/React.createContext([]);\nfunction Select({\n  box,\n  multiple,\n  children,\n  onChange,\n  onChangePointerUp,\n  border = '1px solid #55aaff',\n  backgroundColor = 'rgba(75, 160, 255, 0.1)',\n  filter: customFilter = item => item,\n  ...props\n}) {\n  const [downed, down] = React.useState(false);\n  const {\n    setEvents,\n    camera,\n    raycaster,\n    gl,\n    controls,\n    size,\n    get\n  } = useThree();\n  const [hovered, hover] = React.useState(false);\n  const [active, dispatch] = React.useReducer((state, {\n    object,\n    shift\n  }) => {\n    if (object === undefined) return [];else if (Array.isArray(object)) return object;else if (!shift) return state[0] === object ? [] : [object];else if (state.includes(object)) return state.filter(o => o !== object);else return [object, ...state];\n  }, []);\n  React.useEffect(() => {\n    if (downed) onChange == null ? void 0 : onChange(active);else onChangePointerUp == null ? void 0 : onChangePointerUp(active);\n  }, [active, downed]);\n  const onClick = React.useCallback(e => {\n    e.stopPropagation();\n    dispatch({\n      object: customFilter([e.object])[0],\n      shift: multiple && e.shiftKey\n    });\n  }, []);\n  const onPointerMissed = React.useCallback(e => !hovered && dispatch({}), [hovered]);\n  const ref = React.useRef(null);\n  React.useEffect(() => {\n    if (!box || !multiple) return;\n    const selBox = new SelectionBox(camera, ref.current);\n    const element = document.createElement('div');\n    element.style.pointerEvents = 'none';\n    element.style.border = border;\n    element.style.backgroundColor = backgroundColor;\n    element.style.position = 'fixed';\n    const startPoint = new THREE.Vector2();\n    const pointTopLeft = new THREE.Vector2();\n    const pointBottomRight = new THREE.Vector2();\n    const oldRaycasterEnabled = get().events.enabled;\n    const oldControlsEnabled = controls == null ? void 0 : controls.enabled;\n    let isDown = false;\n    function prepareRay(event, vec) {\n      const {\n        offsetX,\n        offsetY\n      } = event;\n      const {\n        width,\n        height\n      } = size;\n      vec.set(offsetX / width * 2 - 1, -(offsetY / height) * 2 + 1);\n    }\n    function onSelectStart(event) {\n      var _gl$domElement$parent;\n      if (controls) controls.enabled = false;\n      setEvents({\n        enabled: false\n      });\n      down(isDown = true);\n      (_gl$domElement$parent = gl.domElement.parentElement) == null ? void 0 : _gl$domElement$parent.appendChild(element);\n      element.style.left = `${event.clientX}px`;\n      element.style.top = `${event.clientY}px`;\n      element.style.width = '0px';\n      element.style.height = '0px';\n      startPoint.x = event.clientX;\n      startPoint.y = event.clientY;\n    }\n    function onSelectMove(event) {\n      pointBottomRight.x = Math.max(startPoint.x, event.clientX);\n      pointBottomRight.y = Math.max(startPoint.y, event.clientY);\n      pointTopLeft.x = Math.min(startPoint.x, event.clientX);\n      pointTopLeft.y = Math.min(startPoint.y, event.clientY);\n      element.style.left = `${pointTopLeft.x}px`;\n      element.style.top = `${pointTopLeft.y}px`;\n      element.style.width = `${pointBottomRight.x - pointTopLeft.x}px`;\n      element.style.height = `${pointBottomRight.y - pointTopLeft.y}px`;\n    }\n    function onSelectOver() {\n      if (isDown) {\n        var _element$parentElemen;\n        if (controls) controls.enabled = oldControlsEnabled;\n        setEvents({\n          enabled: oldRaycasterEnabled\n        });\n        down(isDown = false);\n        (_element$parentElemen = element.parentElement) == null ? void 0 : _element$parentElemen.removeChild(element);\n      }\n    }\n    function pointerDown(event) {\n      if (event.shiftKey) {\n        onSelectStart(event);\n        prepareRay(event, selBox.startPoint);\n      }\n    }\n    let previous = [];\n    function pointerMove(event) {\n      if (isDown) {\n        onSelectMove(event);\n        prepareRay(event, selBox.endPoint);\n        const allSelected = selBox.select().sort(o => o.uuid).filter(o => o.isMesh);\n        if (!shallow(allSelected, previous)) {\n          previous = allSelected;\n          dispatch({\n            object: customFilter(allSelected)\n          });\n        }\n      }\n    }\n    function pointerUp(event) {\n      if (isDown) onSelectOver();\n    }\n    document.addEventListener('pointerdown', pointerDown, {\n      passive: true\n    });\n    document.addEventListener('pointermove', pointerMove, {\n      passive: true,\n      capture: true\n    });\n    document.addEventListener('pointerup', pointerUp, {\n      passive: true\n    });\n    return () => {\n      document.removeEventListener('pointerdown', pointerDown);\n      document.removeEventListener('pointermove', pointerMove);\n      document.removeEventListener('pointerup', pointerUp);\n    };\n  }, [size.width, size.height, raycaster, camera, controls, gl]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref,\n    onClick: onClick,\n    onPointerOver: () => hover(true),\n    onPointerOut: () => hover(false),\n    onPointerMissed: onPointerMissed\n  }, props), /*#__PURE__*/React.createElement(context.Provider, {\n    value: active\n  }, children));\n}\nfunction useSelect() {\n  return React.useContext(context);\n}\nexport { Select, useSelect };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "SelectionBox", "useThree", "shallow", "context", "createContext", "Select", "box", "multiple", "children", "onChange", "onChangePointerUp", "border", "backgroundColor", "filter", "customFilter", "item", "props", "downed", "down", "useState", "setEvents", "camera", "raycaster", "gl", "controls", "size", "get", "hovered", "hover", "active", "dispatch", "useReducer", "state", "object", "shift", "undefined", "Array", "isArray", "includes", "o", "useEffect", "onClick", "useCallback", "e", "stopPropagation", "shift<PERSON>ey", "onPointerMissed", "ref", "useRef", "selBox", "current", "element", "document", "createElement", "style", "pointerEvents", "position", "startPoint", "Vector2", "pointTopLeft", "pointBottomRight", "oldRaycasterEnabled", "events", "enabled", "oldControlsEnabled", "isDown", "prepareRay", "event", "vec", "offsetX", "offsetY", "width", "height", "set", "onSelectStart", "_gl$domElement$parent", "dom<PERSON>lement", "parentElement", "append<PERSON><PERSON><PERSON>", "left", "clientX", "top", "clientY", "x", "y", "onSelectMove", "Math", "max", "min", "onSelectOver", "_element$parentElemen", "<PERSON><PERSON><PERSON><PERSON>", "pointerDown", "previous", "pointer<PERSON><PERSON>", "endPoint", "allSelected", "select", "sort", "uuid", "<PERSON><PERSON><PERSON>", "pointerUp", "addEventListener", "passive", "capture", "removeEventListener", "onPointerOver", "onPointerOut", "Provider", "value", "useSelect", "useContext"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/web/Select.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { SelectionBox } from 'three-stdlib';\nimport { useThree } from '@react-three/fiber';\nimport shallow from 'zustand/shallow';\n\nconst context = /*#__PURE__*/React.createContext([]);\nfunction Select({\n  box,\n  multiple,\n  children,\n  onChange,\n  onChangePointerUp,\n  border = '1px solid #55aaff',\n  backgroundColor = 'rgba(75, 160, 255, 0.1)',\n  filter: customFilter = item => item,\n  ...props\n}) {\n  const [downed, down] = React.useState(false);\n  const {\n    setEvents,\n    camera,\n    raycaster,\n    gl,\n    controls,\n    size,\n    get\n  } = useThree();\n  const [hovered, hover] = React.useState(false);\n  const [active, dispatch] = React.useReducer((state, {\n    object,\n    shift\n  }) => {\n    if (object === undefined) return [];else if (Array.isArray(object)) return object;else if (!shift) return state[0] === object ? [] : [object];else if (state.includes(object)) return state.filter(o => o !== object);else return [object, ...state];\n  }, []);\n  React.useEffect(() => {\n    if (downed) onChange == null ? void 0 : onChange(active);else onChangePointerUp == null ? void 0 : onChangePointerUp(active);\n  }, [active, downed]);\n  const onClick = React.useCallback(e => {\n    e.stopPropagation();\n    dispatch({\n      object: customFilter([e.object])[0],\n      shift: multiple && e.shiftKey\n    });\n  }, []);\n  const onPointerMissed = React.useCallback(e => !hovered && dispatch({}), [hovered]);\n  const ref = React.useRef(null);\n  React.useEffect(() => {\n    if (!box || !multiple) return;\n    const selBox = new SelectionBox(camera, ref.current);\n    const element = document.createElement('div');\n    element.style.pointerEvents = 'none';\n    element.style.border = border;\n    element.style.backgroundColor = backgroundColor;\n    element.style.position = 'fixed';\n    const startPoint = new THREE.Vector2();\n    const pointTopLeft = new THREE.Vector2();\n    const pointBottomRight = new THREE.Vector2();\n    const oldRaycasterEnabled = get().events.enabled;\n    const oldControlsEnabled = controls == null ? void 0 : controls.enabled;\n    let isDown = false;\n\n    function prepareRay(event, vec) {\n      const {\n        offsetX,\n        offsetY\n      } = event;\n      const {\n        width,\n        height\n      } = size;\n      vec.set(offsetX / width * 2 - 1, -(offsetY / height) * 2 + 1);\n    }\n\n    function onSelectStart(event) {\n      var _gl$domElement$parent;\n\n      if (controls) controls.enabled = false;\n      setEvents({\n        enabled: false\n      });\n      down(isDown = true);\n      (_gl$domElement$parent = gl.domElement.parentElement) == null ? void 0 : _gl$domElement$parent.appendChild(element);\n      element.style.left = `${event.clientX}px`;\n      element.style.top = `${event.clientY}px`;\n      element.style.width = '0px';\n      element.style.height = '0px';\n      startPoint.x = event.clientX;\n      startPoint.y = event.clientY;\n    }\n\n    function onSelectMove(event) {\n      pointBottomRight.x = Math.max(startPoint.x, event.clientX);\n      pointBottomRight.y = Math.max(startPoint.y, event.clientY);\n      pointTopLeft.x = Math.min(startPoint.x, event.clientX);\n      pointTopLeft.y = Math.min(startPoint.y, event.clientY);\n      element.style.left = `${pointTopLeft.x}px`;\n      element.style.top = `${pointTopLeft.y}px`;\n      element.style.width = `${pointBottomRight.x - pointTopLeft.x}px`;\n      element.style.height = `${pointBottomRight.y - pointTopLeft.y}px`;\n    }\n\n    function onSelectOver() {\n      if (isDown) {\n        var _element$parentElemen;\n\n        if (controls) controls.enabled = oldControlsEnabled;\n        setEvents({\n          enabled: oldRaycasterEnabled\n        });\n        down(isDown = false);\n        (_element$parentElemen = element.parentElement) == null ? void 0 : _element$parentElemen.removeChild(element);\n      }\n    }\n\n    function pointerDown(event) {\n      if (event.shiftKey) {\n        onSelectStart(event);\n        prepareRay(event, selBox.startPoint);\n      }\n    }\n\n    let previous = [];\n\n    function pointerMove(event) {\n      if (isDown) {\n        onSelectMove(event);\n        prepareRay(event, selBox.endPoint);\n        const allSelected = selBox.select().sort(o => o.uuid).filter(o => o.isMesh);\n\n        if (!shallow(allSelected, previous)) {\n          previous = allSelected;\n          dispatch({\n            object: customFilter(allSelected)\n          });\n        }\n      }\n    }\n\n    function pointerUp(event) {\n      if (isDown) onSelectOver();\n    }\n\n    document.addEventListener('pointerdown', pointerDown, {\n      passive: true\n    });\n    document.addEventListener('pointermove', pointerMove, {\n      passive: true,\n      capture: true\n    });\n    document.addEventListener('pointerup', pointerUp, {\n      passive: true\n    });\n    return () => {\n      document.removeEventListener('pointerdown', pointerDown);\n      document.removeEventListener('pointermove', pointerMove);\n      document.removeEventListener('pointerup', pointerUp);\n    };\n  }, [size.width, size.height, raycaster, camera, controls, gl]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref,\n    onClick: onClick,\n    onPointerOver: () => hover(true),\n    onPointerOut: () => hover(false),\n    onPointerMissed: onPointerMissed\n  }, props), /*#__PURE__*/React.createElement(context.Provider, {\n    value: active\n  }, children));\n}\nfunction useSelect() {\n  return React.useContext(context);\n}\n\nexport { Select, useSelect };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAOC,OAAO,MAAM,iBAAiB;AAErC,MAAMC,OAAO,GAAG,aAAaL,KAAK,CAACM,aAAa,CAAC,EAAE,CAAC;AACpD,SAASC,MAAMA,CAAC;EACdC,GAAG;EACHC,QAAQ;EACRC,QAAQ;EACRC,QAAQ;EACRC,iBAAiB;EACjBC,MAAM,GAAG,mBAAmB;EAC5BC,eAAe,GAAG,yBAAyB;EAC3CC,MAAM,EAAEC,YAAY,GAAGC,IAAI,IAAIA,IAAI;EACnC,GAAGC;AACL,CAAC,EAAE;EACD,MAAM,CAACC,MAAM,EAAEC,IAAI,CAAC,GAAGpB,KAAK,CAACqB,QAAQ,CAAC,KAAK,CAAC;EAC5C,MAAM;IACJC,SAAS;IACTC,MAAM;IACNC,SAAS;IACTC,EAAE;IACFC,QAAQ;IACRC,IAAI;IACJC;EACF,CAAC,GAAGzB,QAAQ,CAAC,CAAC;EACd,MAAM,CAAC0B,OAAO,EAAEC,KAAK,CAAC,GAAG9B,KAAK,CAACqB,QAAQ,CAAC,KAAK,CAAC;EAC9C,MAAM,CAACU,MAAM,EAAEC,QAAQ,CAAC,GAAGhC,KAAK,CAACiC,UAAU,CAAC,CAACC,KAAK,EAAE;IAClDC,MAAM;IACNC;EACF,CAAC,KAAK;IACJ,IAAID,MAAM,KAAKE,SAAS,EAAE,OAAO,EAAE,CAAC,KAAK,IAAIC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE,OAAOA,MAAM,CAAC,KAAK,IAAI,CAACC,KAAK,EAAE,OAAOF,KAAK,CAAC,CAAC,CAAC,KAAKC,MAAM,GAAG,EAAE,GAAG,CAACA,MAAM,CAAC,CAAC,KAAK,IAAID,KAAK,CAACM,QAAQ,CAACL,MAAM,CAAC,EAAE,OAAOD,KAAK,CAACnB,MAAM,CAAC0B,CAAC,IAAIA,CAAC,KAAKN,MAAM,CAAC,CAAC,KAAK,OAAO,CAACA,MAAM,EAAE,GAAGD,KAAK,CAAC;EACtP,CAAC,EAAE,EAAE,CAAC;EACNlC,KAAK,CAAC0C,SAAS,CAAC,MAAM;IACpB,IAAIvB,MAAM,EAAER,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACoB,MAAM,CAAC,CAAC,KAAKnB,iBAAiB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACmB,MAAM,CAAC;EAC9H,CAAC,EAAE,CAACA,MAAM,EAAEZ,MAAM,CAAC,CAAC;EACpB,MAAMwB,OAAO,GAAG3C,KAAK,CAAC4C,WAAW,CAACC,CAAC,IAAI;IACrCA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnBd,QAAQ,CAAC;MACPG,MAAM,EAAEnB,YAAY,CAAC,CAAC6B,CAAC,CAACV,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACnCC,KAAK,EAAE3B,QAAQ,IAAIoC,CAAC,CAACE;IACvB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,eAAe,GAAGhD,KAAK,CAAC4C,WAAW,CAACC,CAAC,IAAI,CAAChB,OAAO,IAAIG,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAACH,OAAO,CAAC,CAAC;EACnF,MAAMoB,GAAG,GAAGjD,KAAK,CAACkD,MAAM,CAAC,IAAI,CAAC;EAC9BlD,KAAK,CAAC0C,SAAS,CAAC,MAAM;IACpB,IAAI,CAAClC,GAAG,IAAI,CAACC,QAAQ,EAAE;IACvB,MAAM0C,MAAM,GAAG,IAAIjD,YAAY,CAACqB,MAAM,EAAE0B,GAAG,CAACG,OAAO,CAAC;IACpD,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC7CF,OAAO,CAACG,KAAK,CAACC,aAAa,GAAG,MAAM;IACpCJ,OAAO,CAACG,KAAK,CAAC3C,MAAM,GAAGA,MAAM;IAC7BwC,OAAO,CAACG,KAAK,CAAC1C,eAAe,GAAGA,eAAe;IAC/CuC,OAAO,CAACG,KAAK,CAACE,QAAQ,GAAG,OAAO;IAChC,MAAMC,UAAU,GAAG,IAAI1D,KAAK,CAAC2D,OAAO,CAAC,CAAC;IACtC,MAAMC,YAAY,GAAG,IAAI5D,KAAK,CAAC2D,OAAO,CAAC,CAAC;IACxC,MAAME,gBAAgB,GAAG,IAAI7D,KAAK,CAAC2D,OAAO,CAAC,CAAC;IAC5C,MAAMG,mBAAmB,GAAGnC,GAAG,CAAC,CAAC,CAACoC,MAAM,CAACC,OAAO;IAChD,MAAMC,kBAAkB,GAAGxC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACuC,OAAO;IACvE,IAAIE,MAAM,GAAG,KAAK;IAElB,SAASC,UAAUA,CAACC,KAAK,EAAEC,GAAG,EAAE;MAC9B,MAAM;QACJC,OAAO;QACPC;MACF,CAAC,GAAGH,KAAK;MACT,MAAM;QACJI,KAAK;QACLC;MACF,CAAC,GAAG/C,IAAI;MACR2C,GAAG,CAACK,GAAG,CAACJ,OAAO,GAAGE,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,EAAED,OAAO,GAAGE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/D;IAEA,SAASE,aAAaA,CAACP,KAAK,EAAE;MAC5B,IAAIQ,qBAAqB;MAEzB,IAAInD,QAAQ,EAAEA,QAAQ,CAACuC,OAAO,GAAG,KAAK;MACtC3C,SAAS,CAAC;QACR2C,OAAO,EAAE;MACX,CAAC,CAAC;MACF7C,IAAI,CAAC+C,MAAM,GAAG,IAAI,CAAC;MACnB,CAACU,qBAAqB,GAAGpD,EAAE,CAACqD,UAAU,CAACC,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,qBAAqB,CAACG,WAAW,CAAC3B,OAAO,CAAC;MACnHA,OAAO,CAACG,KAAK,CAACyB,IAAI,GAAG,GAAGZ,KAAK,CAACa,OAAO,IAAI;MACzC7B,OAAO,CAACG,KAAK,CAAC2B,GAAG,GAAG,GAAGd,KAAK,CAACe,OAAO,IAAI;MACxC/B,OAAO,CAACG,KAAK,CAACiB,KAAK,GAAG,KAAK;MAC3BpB,OAAO,CAACG,KAAK,CAACkB,MAAM,GAAG,KAAK;MAC5Bf,UAAU,CAAC0B,CAAC,GAAGhB,KAAK,CAACa,OAAO;MAC5BvB,UAAU,CAAC2B,CAAC,GAAGjB,KAAK,CAACe,OAAO;IAC9B;IAEA,SAASG,YAAYA,CAAClB,KAAK,EAAE;MAC3BP,gBAAgB,CAACuB,CAAC,GAAGG,IAAI,CAACC,GAAG,CAAC9B,UAAU,CAAC0B,CAAC,EAAEhB,KAAK,CAACa,OAAO,CAAC;MAC1DpB,gBAAgB,CAACwB,CAAC,GAAGE,IAAI,CAACC,GAAG,CAAC9B,UAAU,CAAC2B,CAAC,EAAEjB,KAAK,CAACe,OAAO,CAAC;MAC1DvB,YAAY,CAACwB,CAAC,GAAGG,IAAI,CAACE,GAAG,CAAC/B,UAAU,CAAC0B,CAAC,EAAEhB,KAAK,CAACa,OAAO,CAAC;MACtDrB,YAAY,CAACyB,CAAC,GAAGE,IAAI,CAACE,GAAG,CAAC/B,UAAU,CAAC2B,CAAC,EAAEjB,KAAK,CAACe,OAAO,CAAC;MACtD/B,OAAO,CAACG,KAAK,CAACyB,IAAI,GAAG,GAAGpB,YAAY,CAACwB,CAAC,IAAI;MAC1ChC,OAAO,CAACG,KAAK,CAAC2B,GAAG,GAAG,GAAGtB,YAAY,CAACyB,CAAC,IAAI;MACzCjC,OAAO,CAACG,KAAK,CAACiB,KAAK,GAAG,GAAGX,gBAAgB,CAACuB,CAAC,GAAGxB,YAAY,CAACwB,CAAC,IAAI;MAChEhC,OAAO,CAACG,KAAK,CAACkB,MAAM,GAAG,GAAGZ,gBAAgB,CAACwB,CAAC,GAAGzB,YAAY,CAACyB,CAAC,IAAI;IACnE;IAEA,SAASK,YAAYA,CAAA,EAAG;MACtB,IAAIxB,MAAM,EAAE;QACV,IAAIyB,qBAAqB;QAEzB,IAAIlE,QAAQ,EAAEA,QAAQ,CAACuC,OAAO,GAAGC,kBAAkB;QACnD5C,SAAS,CAAC;UACR2C,OAAO,EAAEF;QACX,CAAC,CAAC;QACF3C,IAAI,CAAC+C,MAAM,GAAG,KAAK,CAAC;QACpB,CAACyB,qBAAqB,GAAGvC,OAAO,CAAC0B,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGa,qBAAqB,CAACC,WAAW,CAACxC,OAAO,CAAC;MAC/G;IACF;IAEA,SAASyC,WAAWA,CAACzB,KAAK,EAAE;MAC1B,IAAIA,KAAK,CAACtB,QAAQ,EAAE;QAClB6B,aAAa,CAACP,KAAK,CAAC;QACpBD,UAAU,CAACC,KAAK,EAAElB,MAAM,CAACQ,UAAU,CAAC;MACtC;IACF;IAEA,IAAIoC,QAAQ,GAAG,EAAE;IAEjB,SAASC,WAAWA,CAAC3B,KAAK,EAAE;MAC1B,IAAIF,MAAM,EAAE;QACVoB,YAAY,CAAClB,KAAK,CAAC;QACnBD,UAAU,CAACC,KAAK,EAAElB,MAAM,CAAC8C,QAAQ,CAAC;QAClC,MAAMC,WAAW,GAAG/C,MAAM,CAACgD,MAAM,CAAC,CAAC,CAACC,IAAI,CAAC3D,CAAC,IAAIA,CAAC,CAAC4D,IAAI,CAAC,CAACtF,MAAM,CAAC0B,CAAC,IAAIA,CAAC,CAAC6D,MAAM,CAAC;QAE3E,IAAI,CAAClG,OAAO,CAAC8F,WAAW,EAAEH,QAAQ,CAAC,EAAE;UACnCA,QAAQ,GAAGG,WAAW;UACtBlE,QAAQ,CAAC;YACPG,MAAM,EAAEnB,YAAY,CAACkF,WAAW;UAClC,CAAC,CAAC;QACJ;MACF;IACF;IAEA,SAASK,SAASA,CAAClC,KAAK,EAAE;MACxB,IAAIF,MAAM,EAAEwB,YAAY,CAAC,CAAC;IAC5B;IAEArC,QAAQ,CAACkD,gBAAgB,CAAC,aAAa,EAAEV,WAAW,EAAE;MACpDW,OAAO,EAAE;IACX,CAAC,CAAC;IACFnD,QAAQ,CAACkD,gBAAgB,CAAC,aAAa,EAAER,WAAW,EAAE;MACpDS,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;IACX,CAAC,CAAC;IACFpD,QAAQ,CAACkD,gBAAgB,CAAC,WAAW,EAAED,SAAS,EAAE;MAChDE,OAAO,EAAE;IACX,CAAC,CAAC;IACF,OAAO,MAAM;MACXnD,QAAQ,CAACqD,mBAAmB,CAAC,aAAa,EAAEb,WAAW,CAAC;MACxDxC,QAAQ,CAACqD,mBAAmB,CAAC,aAAa,EAAEX,WAAW,CAAC;MACxD1C,QAAQ,CAACqD,mBAAmB,CAAC,WAAW,EAAEJ,SAAS,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,CAAC5E,IAAI,CAAC8C,KAAK,EAAE9C,IAAI,CAAC+C,MAAM,EAAElD,SAAS,EAAED,MAAM,EAAEG,QAAQ,EAAED,EAAE,CAAC,CAAC;EAC9D,OAAO,aAAazB,KAAK,CAACuD,aAAa,CAAC,OAAO,EAAExD,QAAQ,CAAC;IACxDkD,GAAG,EAAEA,GAAG;IACRN,OAAO,EAAEA,OAAO;IAChBiE,aAAa,EAAEA,CAAA,KAAM9E,KAAK,CAAC,IAAI,CAAC;IAChC+E,YAAY,EAAEA,CAAA,KAAM/E,KAAK,CAAC,KAAK,CAAC;IAChCkB,eAAe,EAAEA;EACnB,CAAC,EAAE9B,KAAK,CAAC,EAAE,aAAalB,KAAK,CAACuD,aAAa,CAAClD,OAAO,CAACyG,QAAQ,EAAE;IAC5DC,KAAK,EAAEhF;EACT,CAAC,EAAErB,QAAQ,CAAC,CAAC;AACf;AACA,SAASsG,SAASA,CAAA,EAAG;EACnB,OAAOhH,KAAK,CAACiH,UAAU,CAAC5G,OAAO,CAAC;AAClC;AAEA,SAASE,MAAM,EAAEyG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}