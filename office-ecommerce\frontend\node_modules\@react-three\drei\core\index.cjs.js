"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("./Billboard.cjs.js"),r=require("./ScreenSpace.cjs.js"),s=require("./QuadraticBezierLine.cjs.js"),t=require("./CubicBezierLine.cjs.js"),o=require("./CatmullRomLine.cjs.js"),i=require("./Line.cjs.js"),a=require("./PositionalAudio.cjs.js"),u=require("./Text.cjs.js"),c=require("./Text3D.cjs.js"),n=require("./Effects.cjs.js"),j=require("./GradientTexture.cjs.js"),p=require("./Image.cjs.js"),x=require("./Edges.cjs.js"),l=require("./Trail.cjs.js"),q=require("./Sampler.cjs.js"),d=require("./ComputedAttribute.cjs.js"),m=require("./Clone.cjs.js"),h=require("./MarchingCubes.cjs.js"),C=require("./Decal.cjs.js"),M=require("./Svg.cjs.js"),S=require("./Gltf.cjs.js"),T=require("./AsciiRenderer.cjs.js"),b=require("./OrthographicCamera.cjs.js"),f=require("./PerspectiveCamera.cjs.js"),B=require("./CubeCamera.cjs.js"),P=require("./DeviceOrientationControls.cjs.js"),g=require("./FlyControls.cjs.js"),v=require("./MapControls.cjs.js"),A=require("./OrbitControls.cjs.js"),L=require("./TrackballControls.cjs.js"),D=require("./ArcballControls.cjs.js"),E=require("./TransformControls.cjs.js"),R=require("./PointerLockControls.cjs.js"),k=require("./FirstPersonControls.cjs.js"),F=require("./CameraControls.cjs.js"),G=require("./GizmoHelper.cjs.js"),w=require("./GizmoViewcube.cjs.js"),z=require("./GizmoViewport.cjs.js"),O=require("./Grid.cjs.js"),I=require("./useCubeTexture.cjs.js"),H=require("./useFBX.cjs.js"),V=require("./useGLTF.cjs.js"),y=require("./useKTX2.cjs.js"),W=require("./useProgress.cjs.js"),Q=require("./useTexture.cjs.js"),X=require("./useVideoTexture.cjs.js"),K=require("./useFont.cjs.js"),N=require("./Stats.cjs.js"),U=require("./useDepthBuffer.cjs.js"),_=require("./useAspect.cjs.js"),J=require("./useCamera.cjs.js"),Y=require("./useDetectGPU.cjs.js"),Z=require("./useHelper.cjs.js"),$=require("./useBVH.cjs.js"),ee=require("./useContextBridge.cjs.js"),re=require("./useAnimations.cjs.js"),se=require("./useFBO.cjs.js"),te=require("./useIntersect.cjs.js"),oe=require("./useBoxProjectedEnv.cjs.js"),ie=require("./BBAnchor.cjs.js"),ae=require("./useTrailTexture.cjs.js"),ue=require("./useCubeCamera.cjs.js"),ce=require("./Example.cjs.js"),ne=require("./SpriteAnimator.cjs.js"),je=require("./CurveModifier.cjs.js"),pe=require("./MeshDistortMaterial.cjs.js"),xe=require("./MeshWobbleMaterial.cjs.js"),le=require("./MeshReflectorMaterial.cjs.js"),qe=require("./MeshRefractionMaterial.cjs.js"),de=require("./MeshTransmissionMaterial.cjs.js"),me=require("./MeshDiscardMaterial.cjs.js"),he=require("./PointMaterial.cjs.js"),Ce=require("./shaderMaterial.cjs.js"),Me=require("./softShadows.cjs.js"),Se=require("./shapes.cjs.js"),Te=require("./Facemesh.cjs.js"),be=require("./RoundedBox.cjs.js"),fe=require("./ScreenQuad.cjs.js"),Be=require("./Center.cjs.js"),Pe=require("./Resize.cjs.js"),ge=require("./Bounds.cjs.js"),ve=require("./CameraShake.cjs.js"),Ae=require("./Float.cjs.js"),Le=require("./Stage.cjs.js"),De=require("./Backdrop.cjs.js"),Ee=require("./Shadow.cjs.js"),Re=require("./Caustics.cjs.js"),ke=require("./ContactShadows.cjs.js"),Fe=require("./AccumulativeShadows.cjs.js"),Ge=require("./Reflector.cjs.js"),we=require("./SpotLight.cjs.js"),ze=require("./Environment.cjs.js"),Oe=require("./Lightformer.cjs.js"),Ie=require("./Sky.cjs.js"),He=require("./Stars.cjs.js"),Ve=require("./Cloud.cjs.js"),ye=require("./Sparkles.cjs.js"),We=require("./useEnvironment.cjs.js"),Qe=require("./useMatcapTexture.cjs.js"),Xe=require("./useNormalTexture.cjs.js"),Ke=require("./Wireframe.cjs.js"),Ne=require("./Points.cjs.js"),Ue=require("./Instances.cjs.js"),_e=require("./Segments.cjs.js"),Je=require("./Detailed.cjs.js"),Ye=require("./Preload.cjs.js"),Ze=require("./BakeShadows.cjs.js"),$e=require("./meshBounds.cjs.js"),er=require("./AdaptiveDpr.cjs.js"),rr=require("./AdaptiveEvents.cjs.js"),sr=require("./PerformanceMonitor.cjs.js"),tr=require("./RenderTexture.cjs.js"),or=require("./Mask.cjs.js"),ir=require("./Hud.cjs.js");require("@babel/runtime/helpers/extends"),require("react"),require("@react-three/fiber"),require("react-merge-refs"),require("three"),require("three-stdlib"),require("troika-three-text"),require("suspend-react"),require("meshline"),require("lodash.pick"),require("lodash.omit"),require("camera-controls"),require("zustand"),require("stats.js"),require("../helpers/useEffectfulState.cjs.js"),require("detect-gpu"),require("three-mesh-bvh"),require("../materials/BlurPass.cjs.js"),require("../materials/ConvolutionMaterial.cjs.js"),require("../materials/MeshReflectorMaterial.cjs.js"),require("../materials/MeshRefractionMaterial.cjs.js"),require("../materials/DiscardMaterial.cjs.js"),require("../helpers/environment-assets.cjs.js"),require("../materials/SpotLightMaterial.cjs.js"),require("../materials/WireframeMaterial.cjs.js"),require("react-composer"),exports.Billboard=e.Billboard,exports.ScreenSpace=r.ScreenSpace,exports.QuadraticBezierLine=s.QuadraticBezierLine,exports.CubicBezierLine=t.CubicBezierLine,exports.CatmullRomLine=o.CatmullRomLine,exports.Line=i.Line,exports.PositionalAudio=a.PositionalAudio,exports.Text=u.Text,exports.Text3D=c.Text3D,exports.Effects=n.Effects,exports.isWebGL2Available=n.isWebGL2Available,exports.GradientTexture=j.GradientTexture,exports.Image=p.Image,exports.Edges=x.Edges,exports.Trail=l.Trail,exports.useTrail=l.useTrail,exports.Sampler=q.Sampler,exports.useSurfaceSampler=q.useSurfaceSampler,exports.ComputedAttribute=d.ComputedAttribute,exports.Clone=m.Clone,exports.MarchingCube=h.MarchingCube,exports.MarchingCubes=h.MarchingCubes,exports.MarchingPlane=h.MarchingPlane,exports.Decal=C.Decal,exports.Svg=M.Svg,exports.Gltf=S.Gltf,exports.AsciiRenderer=T.AsciiRenderer,exports.OrthographicCamera=b.OrthographicCamera,exports.PerspectiveCamera=f.PerspectiveCamera,exports.CubeCamera=B.CubeCamera,exports.DeviceOrientationControls=P.DeviceOrientationControls,exports.FlyControls=g.FlyControls,exports.MapControls=v.MapControls,exports.OrbitControls=A.OrbitControls,exports.TrackballControls=L.TrackballControls,exports.ArcballControls=D.ArcballControls,exports.TransformControls=E.TransformControls,exports.PointerLockControls=R.PointerLockControls,exports.FirstPersonControls=k.FirstPersonControls,exports.CameraControls=F.CameraControls,exports.GizmoHelper=G.GizmoHelper,exports.useGizmoContext=G.useGizmoContext,exports.GizmoViewcube=w.GizmoViewcube,exports.GizmoViewport=z.GizmoViewport,exports.Grid=O.Grid,exports.useCubeTexture=I.useCubeTexture,exports.useFBX=H.useFBX,exports.useGLTF=V.useGLTF,exports.useKTX2=y.useKTX2,exports.useProgress=W.useProgress,exports.IsObject=Q.IsObject,exports.useTexture=Q.useTexture,exports.useVideoTexture=X.useVideoTexture,exports.useFont=K.useFont,exports.Stats=N.Stats,exports.useDepthBuffer=U.useDepthBuffer,exports.useAspect=_.useAspect,exports.useCamera=J.useCamera,exports.useDetectGPU=Y.useDetectGPU,exports.useHelper=Z.useHelper,exports.Bvh=$.Bvh,exports.useBVH=$.useBVH,exports.useContextBridge=ee.useContextBridge,exports.useAnimations=re.useAnimations,exports.useFBO=se.useFBO,exports.useIntersect=te.useIntersect,exports.useBoxProjectedEnv=oe.useBoxProjectedEnv,exports.BBAnchor=ie.BBAnchor,exports.useTrailTexture=ae.useTrailTexture,exports.useCubeCamera=ue.useCubeCamera,exports.Example=ce.Example,exports.SpriteAnimator=ne.SpriteAnimator,exports.CurveModifier=je.CurveModifier,exports.MeshDistortMaterial=pe.MeshDistortMaterial,exports.MeshWobbleMaterial=xe.MeshWobbleMaterial,exports.MeshReflectorMaterial=le.MeshReflectorMaterial,exports.MeshRefractionMaterial=qe.MeshRefractionMaterial,exports.MeshTransmissionMaterial=de.MeshTransmissionMaterial,exports.MeshDiscardMaterial=me.MeshDiscardMaterial,exports.PointMaterial=he.PointMaterial,exports.PointMaterialImpl=he.PointMaterialImpl,exports.shaderMaterial=Ce.shaderMaterial,exports.SoftShadows=Me.SoftShadows,exports.Box=Se.Box,exports.Capsule=Se.Capsule,exports.Circle=Se.Circle,exports.Cone=Se.Cone,exports.Cylinder=Se.Cylinder,exports.Dodecahedron=Se.Dodecahedron,exports.Extrude=Se.Extrude,exports.Icosahedron=Se.Icosahedron,exports.Lathe=Se.Lathe,exports.Octahedron=Se.Octahedron,exports.Plane=Se.Plane,exports.Polyhedron=Se.Polyhedron,exports.Ring=Se.Ring,exports.Shape=Se.Shape,exports.Sphere=Se.Sphere,exports.Tetrahedron=Se.Tetrahedron,exports.Torus=Se.Torus,exports.TorusKnot=Se.TorusKnot,exports.Tube=Se.Tube,exports.Facemesh=Te.Facemesh,exports.FacemeshDatas=Te.FacemeshDatas,exports.RoundedBox=be.RoundedBox,exports.ScreenQuad=fe.ScreenQuad,exports.Center=Be.Center,exports.Resize=Pe.Resize,exports.Bounds=ge.Bounds,exports.useBounds=ge.useBounds,exports.CameraShake=ve.CameraShake,exports.Float=Ae.Float,exports.Stage=Le.Stage,exports.Backdrop=De.Backdrop,exports.Shadow=Ee.Shadow,exports.Caustics=Re.Caustics,exports.ContactShadows=ke.ContactShadows,exports.AccumulativeShadows=Fe.AccumulativeShadows,exports.RandomizedLight=Fe.RandomizedLight,exports.accumulativeContext=Fe.accumulativeContext,exports.Reflector=Ge.Reflector,exports.SpotLight=we.SpotLight,exports.SpotLightShadow=we.SpotLightShadow,exports.Environment=ze.Environment,exports.EnvironmentCube=ze.EnvironmentCube,exports.EnvironmentMap=ze.EnvironmentMap,exports.EnvironmentPortal=ze.EnvironmentPortal,exports.Lightformer=Oe.Lightformer,exports.Sky=Ie.Sky,exports.calcPosFromAngles=Ie.calcPosFromAngles,exports.Stars=He.Stars,exports.Cloud=Ve.Cloud,exports.Sparkles=ye.Sparkles,exports.useEnvironment=We.useEnvironment,exports.useMatcapTexture=Qe.useMatcapTexture,exports.useNormalTexture=Xe.useNormalTexture,exports.Wireframe=Ke.Wireframe,exports.Point=Ne.Point,exports.Points=Ne.Points,exports.PointsBuffer=Ne.PointsBuffer,exports.PositionPoint=Ne.PositionPoint,exports.Instance=Ue.Instance,exports.Instances=Ue.Instances,exports.Merged=Ue.Merged,exports.Segment=_e.Segment,exports.SegmentObject=_e.SegmentObject,exports.Segments=_e.Segments,exports.Detailed=Je.Detailed,exports.Preload=Ye.Preload,exports.BakeShadows=Ze.BakeShadows,exports.meshBounds=$e.meshBounds,exports.AdaptiveDpr=er.AdaptiveDpr,exports.AdaptiveEvents=rr.AdaptiveEvents,exports.PerformanceMonitor=sr.PerformanceMonitor,exports.usePerformanceMonitor=sr.usePerformanceMonitor,exports.RenderTexture=tr.RenderTexture,exports.Mask=or.Mask,exports.useMask=or.useMask,exports.Hud=ir.Hud;
