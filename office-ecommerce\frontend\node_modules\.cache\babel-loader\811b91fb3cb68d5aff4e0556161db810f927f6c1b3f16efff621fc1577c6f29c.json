{"ast": null, "code": "import * as React from 'react';\nfunction useCursor(hovered, onPointerOver = 'pointer', onPointerOut = 'auto') {\n  React.useEffect(() => {\n    if (hovered) {\n      document.body.style.cursor = onPointerOver;\n      return () => void (document.body.style.cursor = onPointerOut);\n    }\n  }, [hovered]);\n}\nexport { useCursor };", "map": {"version": 3, "names": ["React", "useCursor", "hovered", "onPointerOver", "onPointerOut", "useEffect", "document", "body", "style", "cursor"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/web/useCursor.js"], "sourcesContent": ["import * as React from 'react';\n\nfunction useCursor(hovered, onPointerOver = 'pointer', onPointerOut = 'auto') {\n  React.useEffect(() => {\n    if (hovered) {\n      document.body.style.cursor = onPointerOver;\n      return () => void (document.body.style.cursor = onPointerOut);\n    }\n  }, [hovered]);\n}\n\nexport { useCursor };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,SAASA,CAACC,OAAO,EAAEC,aAAa,GAAG,SAAS,EAAEC,YAAY,GAAG,MAAM,EAAE;EAC5EJ,KAAK,CAACK,SAAS,CAAC,MAAM;IACpB,IAAIH,OAAO,EAAE;MACXI,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,MAAM,GAAGN,aAAa;MAC1C,OAAO,MAAM,MAAMG,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,MAAM,GAAGL,YAAY,CAAC;IAC/D;EACF,CAAC,EAAE,CAACF,OAAO,CAAC,CAAC;AACf;AAEA,SAASD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}