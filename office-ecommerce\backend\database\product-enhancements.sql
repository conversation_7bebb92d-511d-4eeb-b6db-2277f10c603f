-- =============================================
-- PRODUCT MANAGEMENT ENHANCEMENTS
-- Enhanced schema for comprehensive product management
-- with 3D models, images, and file upload support
-- =============================================

USE OfficeEcommerce;
GO

-- =============================================
-- EXTEND PRODUCTS TABLE
-- =============================================

-- Add new columns to existing Products table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Products') AND name = 'Status')
BEGIN
    ALTER TABLE Products ADD Status NVARCHAR(50) DEFAULT 'Draft';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Products') AND name = 'Tags')
BEGIN
    ALTER TABLE Products ADD Tags NVARCHAR(MAX); -- JSON array of tags
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Products') AND name = 'MetaData')
BEGIN
    ALTER TABLE Products ADD MetaData NVARCHAR(MAX); -- JSON for additional metadata
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Products') AND name = 'CreatedBy')
BEGIN
    ALTER TABLE Products ADD CreatedBy UNIQUEIDENTIFIER;
    ALTER TABLE Products ADD CONSTRAINT FK_Products_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserID);
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Products') AND name = 'UpdatedBy')
BEGIN
    ALTER TABLE Products ADD UpdatedBy UNIQUEIDENTIFIER;
    ALTER TABLE Products ADD CONSTRAINT FK_Products_UpdatedBy FOREIGN KEY (UpdatedBy) REFERENCES Users(UserID);
END

-- =============================================
-- PRODUCT 3D MODELS TABLE
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Product3DModels')
BEGIN
    CREATE TABLE Product3DModels (
        ModelID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        ProductID UNIQUEIDENTIFIER NOT NULL,
        FileName NVARCHAR(255) NOT NULL,
        OriginalFileName NVARCHAR(255) NOT NULL,
        FilePath NVARCHAR(500) NOT NULL,
        FileSize BIGINT NOT NULL,
        FileType NVARCHAR(50) NOT NULL, -- GLB, GLTF
        MimeType NVARCHAR(100) NOT NULL,
        IsActive BIT DEFAULT 1,
        IsPrimary BIT DEFAULT 0,
        ModelMetadata NVARCHAR(MAX), -- JSON: vertices, faces, textures, etc.
        UploadedBy UNIQUEIDENTIFIER NOT NULL,
        CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
        UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
        
        CONSTRAINT FK_Product3DModels_Product FOREIGN KEY (ProductID) REFERENCES Products(ProductID) ON DELETE CASCADE,
        CONSTRAINT FK_Product3DModels_UploadedBy FOREIGN KEY (UploadedBy) REFERENCES Users(UserID)
    );

    -- Index for performance
    CREATE INDEX IX_Product3DModels_ProductID ON Product3DModels(ProductID);
    CREATE INDEX IX_Product3DModels_IsActive ON Product3DModels(IsActive);
    CREATE INDEX IX_Product3DModels_IsPrimary ON Product3DModels(IsPrimary);
END

-- =============================================
-- PRODUCT IMAGES TABLE
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'ProductImages')
BEGIN
    CREATE TABLE ProductImages (
        ImageID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        ProductID UNIQUEIDENTIFIER NOT NULL,
        FileName NVARCHAR(255) NOT NULL,
        OriginalFileName NVARCHAR(255) NOT NULL,
        FilePath NVARCHAR(500) NOT NULL,
        ThumbnailPath NVARCHAR(500),
        FileSize BIGINT NOT NULL,
        FileType NVARCHAR(50) NOT NULL, -- JPEG, PNG, WebP
        MimeType NVARCHAR(100) NOT NULL,
        Width INT,
        Height INT,
        IsActive BIT DEFAULT 1,
        IsPrimary BIT DEFAULT 0,
        DisplayOrder INT DEFAULT 0,
        AltText NVARCHAR(255),
        Caption NVARCHAR(500),
        UploadedBy UNIQUEIDENTIFIER NOT NULL,
        CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
        UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
        
        CONSTRAINT FK_ProductImages_Product FOREIGN KEY (ProductID) REFERENCES Products(ProductID) ON DELETE CASCADE,
        CONSTRAINT FK_ProductImages_UploadedBy FOREIGN KEY (UploadedBy) REFERENCES Users(UserID)
    );

    -- Indexes for performance
    CREATE INDEX IX_ProductImages_ProductID ON ProductImages(ProductID);
    CREATE INDEX IX_ProductImages_IsActive ON ProductImages(IsActive);
    CREATE INDEX IX_ProductImages_IsPrimary ON ProductImages(IsPrimary);
    CREATE INDEX IX_ProductImages_DisplayOrder ON ProductImages(DisplayOrder);
END

-- =============================================
-- PRODUCT COMPONENTS/PARTS RELATIONSHIP
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'ProductComponents')
BEGIN
    CREATE TABLE ProductComponents (
        ComponentID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        ProductID UNIQUEIDENTIFIER NOT NULL,
        PartID UNIQUEIDENTIFIER NOT NULL,
        Quantity DECIMAL(10,4) NOT NULL DEFAULT 1,
        IsOptional BIT DEFAULT 0,
        IsCustomizable BIT DEFAULT 0,
        DefaultConfiguration NVARCHAR(MAX), -- JSON for default settings
        Notes NVARCHAR(500),
        CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
        UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
        
        CONSTRAINT FK_ProductComponents_Product FOREIGN KEY (ProductID) REFERENCES Products(ProductID) ON DELETE CASCADE,
        CONSTRAINT FK_ProductComponents_Part FOREIGN KEY (PartID) REFERENCES Parts(PartID),
        CONSTRAINT UQ_ProductComponents_ProductPart UNIQUE (ProductID, PartID)
    );

    -- Index for performance
    CREATE INDEX IX_ProductComponents_ProductID ON ProductComponents(ProductID);
    CREATE INDEX IX_ProductComponents_PartID ON ProductComponents(PartID);
END

-- =============================================
-- PRODUCT COLOR OPTIONS
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'ProductColors')
BEGIN
    CREATE TABLE ProductColors (
        ColorID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        ProductID UNIQUEIDENTIFIER NOT NULL,
        ColorName NVARCHAR(100) NOT NULL,
        ColorCode NVARCHAR(20), -- Hex color code
        ColorFamily NVARCHAR(50), -- Primary, Secondary, Neutral, etc.
        PriceAdjustment DECIMAL(10,2) DEFAULT 0,
        IsActive BIT DEFAULT 1,
        IsDefault BIT DEFAULT 0,
        SortOrder INT DEFAULT 0,
        CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
        
        CONSTRAINT FK_ProductColors_Product FOREIGN KEY (ProductID) REFERENCES Products(ProductID) ON DELETE CASCADE
    );

    -- Index for performance
    CREATE INDEX IX_ProductColors_ProductID ON ProductColors(ProductID);
    CREATE INDEX IX_ProductColors_IsActive ON ProductColors(IsActive);
END

-- =============================================
-- PRODUCT AUDIT TRAIL
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'ProductAuditTrail')
BEGIN
    CREATE TABLE ProductAuditTrail (
        AuditID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        ProductID UNIQUEIDENTIFIER NOT NULL,
        Action NVARCHAR(50) NOT NULL, -- CREATE, UPDATE, DELETE, STATUS_CHANGE, etc.
        FieldName NVARCHAR(100),
        OldValue NVARCHAR(MAX),
        NewValue NVARCHAR(MAX),
        ChangeReason NVARCHAR(500),
        ChangedBy UNIQUEIDENTIFIER NOT NULL,
        ChangedAt DATETIME2 DEFAULT GETUTCDATE(),
        IPAddress NVARCHAR(45),
        UserAgent NVARCHAR(500),
        
        CONSTRAINT FK_ProductAuditTrail_Product FOREIGN KEY (ProductID) REFERENCES Products(ProductID),
        CONSTRAINT FK_ProductAuditTrail_ChangedBy FOREIGN KEY (ChangedBy) REFERENCES Users(UserID)
    );

    -- Indexes for performance
    CREATE INDEX IX_ProductAuditTrail_ProductID ON ProductAuditTrail(ProductID);
    CREATE INDEX IX_ProductAuditTrail_ChangedAt ON ProductAuditTrail(ChangedAt);
    CREATE INDEX IX_ProductAuditTrail_Action ON ProductAuditTrail(Action);
END

-- =============================================
-- PRODUCT FILE UPLOADS TRACKING
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'ProductFileUploads')
BEGIN
    CREATE TABLE ProductFileUploads (
        UploadID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        ProductID UNIQUEIDENTIFIER NOT NULL,
        FileType NVARCHAR(50) NOT NULL, -- 3D_MODEL, IMAGE, DOCUMENT
        ReferenceID UNIQUEIDENTIFIER NOT NULL, -- ModelID or ImageID
        UploadStatus NVARCHAR(50) DEFAULT 'Pending', -- Pending, Processing, Completed, Failed
        UploadProgress INT DEFAULT 0, -- 0-100
        ErrorMessage NVARCHAR(MAX),
        ProcessingMetadata NVARCHAR(MAX), -- JSON for processing details
        UploadedBy UNIQUEIDENTIFIER NOT NULL,
        StartedAt DATETIME2 DEFAULT GETUTCDATE(),
        CompletedAt DATETIME2,
        
        CONSTRAINT FK_ProductFileUploads_Product FOREIGN KEY (ProductID) REFERENCES Products(ProductID) ON DELETE CASCADE,
        CONSTRAINT FK_ProductFileUploads_UploadedBy FOREIGN KEY (UploadedBy) REFERENCES Users(UserID)
    );

    -- Index for performance
    CREATE INDEX IX_ProductFileUploads_ProductID ON ProductFileUploads(ProductID);
    CREATE INDEX IX_ProductFileUploads_Status ON ProductFileUploads(UploadStatus);
    CREATE INDEX IX_ProductFileUploads_StartedAt ON ProductFileUploads(StartedAt);
END

-- =============================================
-- UPDATE EXISTING CONSTRAINTS AND INDEXES
-- =============================================

-- Add check constraint for product status
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_Products_Status')
BEGIN
    ALTER TABLE Products ADD CONSTRAINT CK_Products_Status 
    CHECK (Status IN ('Draft', 'Active', 'Inactive', 'Discontinued', 'Pending Review'));
END

-- Ensure only one primary 3D model per product
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Product3DModels_PrimaryUnique')
BEGIN
    CREATE UNIQUE INDEX IX_Product3DModels_PrimaryUnique 
    ON Product3DModels(ProductID) 
    WHERE IsPrimary = 1;
END

-- Ensure only one primary image per product
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ProductImages_PrimaryUnique')
BEGIN
    CREATE UNIQUE INDEX IX_ProductImages_PrimaryUnique 
    ON ProductImages(ProductID) 
    WHERE IsPrimary = 1;
END

-- Ensure only one default color per product
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ProductColors_DefaultUnique')
BEGIN
    CREATE UNIQUE INDEX IX_ProductColors_DefaultUnique 
    ON ProductColors(ProductID) 
    WHERE IsDefault = 1;
END

PRINT 'Product management enhancements completed successfully.';
GO
