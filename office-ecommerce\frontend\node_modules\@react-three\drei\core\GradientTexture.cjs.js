"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react");function r(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function n(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var a=r(e),c=n(t);exports.GradientTexture=function({stops:e,colors:t,size:r=1024,...n}){const o=c.useMemo((()=>{const n=document.createElement("canvas"),a=n.getContext("2d");n.width=16,n.height=r;const c=a.createLinearGradient(0,0,0,r);let o=e.length;for(;o--;)c.addColorStop(e[o],t[o]);return a.fillStyle=c,a.fillRect(0,0,16,r),n}),[e]);return c.createElement("canvasTexture",a.default({args:[o],attach:"map"},n))};
