{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport mergeRefs from 'react-merge-refs';\nimport { MarchingCubes as MarchingCubes$1 } from 'three-stdlib';\nimport { useFrame } from '@react-three/fiber';\nconst globalContext = /*#__PURE__*/React.createContext(null);\nconst MarchingCubes = /*#__PURE__*/React.forwardRef(({\n  resolution = 28,\n  maxPolyCount = 10000,\n  enableUvs = false,\n  enableColors = false,\n  children,\n  ...props\n}, ref) => {\n  const marchingCubesRef = React.useRef(null);\n  const marchingCubes = React.useMemo(() => new MarchingCubes$1(resolution, null, enableUvs, enableColors, maxPolyCount), [resolution, maxPolyCount, enableUvs, enableColors]);\n  const api = React.useMemo(() => ({\n    getParent: () => marchingCubesRef\n  }), []);\n  useFrame(() => {\n    marchingCubes.reset();\n  }, -1); // To make sure the reset runs before the balls or planes are added\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: marchingCubes,\n    ref: mergeRefs([marchingCubesRef, ref])\n  }, props), /*#__PURE__*/React.createElement(globalContext.Provider, {\n    value: api\n  }, children)));\n});\nconst MarchingCube = /*#__PURE__*/React.forwardRef(({\n  strength = 0.5,\n  subtract = 12,\n  color,\n  ...props\n}, ref) => {\n  const {\n    getParent\n  } = React.useContext(globalContext);\n  const parentRef = React.useMemo(() => getParent(), [getParent]);\n  const cubeRef = React.useRef();\n  const vec = new THREE.Vector3();\n  useFrame(state => {\n    if (!parentRef.current || !cubeRef.current) return;\n    cubeRef.current.getWorldPosition(vec);\n    parentRef.current.addBall(0.5 + vec.x * 0.5, 0.5 + vec.y * 0.5, 0.5 + vec.z * 0.5, strength, subtract, color);\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: mergeRefs([ref, cubeRef])\n  }, props));\n});\nconst MarchingPlane = /*#__PURE__*/React.forwardRef(({\n  planeType: _planeType = 'x',\n  strength = 0.5,\n  subtract = 12,\n  ...props\n}, ref) => {\n  const {\n    getParent\n  } = React.useContext(globalContext);\n  const parentRef = React.useMemo(() => getParent(), [getParent]);\n  const wallRef = React.useRef();\n  const planeType = React.useMemo(() => _planeType === 'x' ? 'addPlaneX' : _planeType === 'y' ? 'addPlaneY' : 'addPlaneZ', [_planeType]);\n  useFrame(() => {\n    if (!parentRef.current || !wallRef.current) return;\n    parentRef.current[planeType](strength, subtract);\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: mergeRefs([ref, wallRef])\n  }, props));\n});\nexport { MarchingCube, MarchingCubes, MarchingPlane };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "mergeRefs", "MarchingCubes", "MarchingCubes$1", "useFrame", "globalContext", "createContext", "forwardRef", "resolution", "maxPolyCount", "enableUvs", "enableColors", "children", "props", "ref", "marchingCubesRef", "useRef", "marchingCubes", "useMemo", "api", "getParent", "reset", "createElement", "Fragment", "object", "Provider", "value", "MarchingCube", "strength", "subtract", "color", "useContext", "parentRef", "cubeRef", "vec", "Vector3", "state", "current", "getWorldPosition", "addBall", "x", "y", "z", "MarchingPlane", "planeType", "_planeType", "wallRef"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/MarchingCubes.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport mergeRefs from 'react-merge-refs';\nimport { MarchingCubes as MarchingCubes$1 } from 'three-stdlib';\nimport { useFrame } from '@react-three/fiber';\n\nconst globalContext = /*#__PURE__*/React.createContext(null);\nconst MarchingCubes = /*#__PURE__*/React.forwardRef(({\n  resolution = 28,\n  maxPolyCount = 10000,\n  enableUvs = false,\n  enableColors = false,\n  children,\n  ...props\n}, ref) => {\n  const marchingCubesRef = React.useRef(null);\n  const marchingCubes = React.useMemo(() => new MarchingCubes$1(resolution, null, enableUvs, enableColors, maxPolyCount), [resolution, maxPolyCount, enableUvs, enableColors]);\n  const api = React.useMemo(() => ({\n    getParent: () => marchingCubesRef\n  }), []);\n  useFrame(() => {\n    marchingCubes.reset();\n  }, -1); // To make sure the reset runs before the balls or planes are added\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: marchingCubes,\n    ref: mergeRefs([marchingCubesRef, ref])\n  }, props), /*#__PURE__*/React.createElement(globalContext.Provider, {\n    value: api\n  }, children)));\n});\nconst MarchingCube = /*#__PURE__*/React.forwardRef(({\n  strength = 0.5,\n  subtract = 12,\n  color,\n  ...props\n}, ref) => {\n  const {\n    getParent\n  } = React.useContext(globalContext);\n  const parentRef = React.useMemo(() => getParent(), [getParent]);\n  const cubeRef = React.useRef();\n  const vec = new THREE.Vector3();\n  useFrame(state => {\n    if (!parentRef.current || !cubeRef.current) return;\n    cubeRef.current.getWorldPosition(vec);\n    parentRef.current.addBall(0.5 + vec.x * 0.5, 0.5 + vec.y * 0.5, 0.5 + vec.z * 0.5, strength, subtract, color);\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: mergeRefs([ref, cubeRef])\n  }, props));\n});\nconst MarchingPlane = /*#__PURE__*/React.forwardRef(({\n  planeType: _planeType = 'x',\n  strength = 0.5,\n  subtract = 12,\n  ...props\n}, ref) => {\n  const {\n    getParent\n  } = React.useContext(globalContext);\n  const parentRef = React.useMemo(() => getParent(), [getParent]);\n  const wallRef = React.useRef();\n  const planeType = React.useMemo(() => _planeType === 'x' ? 'addPlaneX' : _planeType === 'y' ? 'addPlaneY' : 'addPlaneZ', [_planeType]);\n  useFrame(() => {\n    if (!parentRef.current || !wallRef.current) return;\n    parentRef.current[planeType](strength, subtract);\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: mergeRefs([ref, wallRef])\n  }, props));\n});\n\nexport { MarchingCube, MarchingCubes, MarchingPlane };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,kBAAkB;AACxC,SAASC,aAAa,IAAIC,eAAe,QAAQ,cAAc;AAC/D,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,MAAMC,aAAa,GAAG,aAAaL,KAAK,CAACM,aAAa,CAAC,IAAI,CAAC;AAC5D,MAAMJ,aAAa,GAAG,aAAaF,KAAK,CAACO,UAAU,CAAC,CAAC;EACnDC,UAAU,GAAG,EAAE;EACfC,YAAY,GAAG,KAAK;EACpBC,SAAS,GAAG,KAAK;EACjBC,YAAY,GAAG,KAAK;EACpBC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,gBAAgB,GAAGf,KAAK,CAACgB,MAAM,CAAC,IAAI,CAAC;EAC3C,MAAMC,aAAa,GAAGjB,KAAK,CAACkB,OAAO,CAAC,MAAM,IAAIf,eAAe,CAACK,UAAU,EAAE,IAAI,EAAEE,SAAS,EAAEC,YAAY,EAAEF,YAAY,CAAC,EAAE,CAACD,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,YAAY,CAAC,CAAC;EAC5K,MAAMQ,GAAG,GAAGnB,KAAK,CAACkB,OAAO,CAAC,OAAO;IAC/BE,SAAS,EAAEA,CAAA,KAAML;EACnB,CAAC,CAAC,EAAE,EAAE,CAAC;EACPX,QAAQ,CAAC,MAAM;IACba,aAAa,CAACI,KAAK,CAAC,CAAC;EACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;EAER,OAAO,aAAarB,KAAK,CAACsB,aAAa,CAACtB,KAAK,CAACuB,QAAQ,EAAE,IAAI,EAAE,aAAavB,KAAK,CAACsB,aAAa,CAAC,WAAW,EAAExB,QAAQ,CAAC;IACnH0B,MAAM,EAAEP,aAAa;IACrBH,GAAG,EAAEb,SAAS,CAAC,CAACc,gBAAgB,EAAED,GAAG,CAAC;EACxC,CAAC,EAAED,KAAK,CAAC,EAAE,aAAab,KAAK,CAACsB,aAAa,CAACjB,aAAa,CAACoB,QAAQ,EAAE;IAClEC,KAAK,EAAEP;EACT,CAAC,EAAEP,QAAQ,CAAC,CAAC,CAAC;AAChB,CAAC,CAAC;AACF,MAAMe,YAAY,GAAG,aAAa3B,KAAK,CAACO,UAAU,CAAC,CAAC;EAClDqB,QAAQ,GAAG,GAAG;EACdC,QAAQ,GAAG,EAAE;EACbC,KAAK;EACL,GAAGjB;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM;IACJM;EACF,CAAC,GAAGpB,KAAK,CAAC+B,UAAU,CAAC1B,aAAa,CAAC;EACnC,MAAM2B,SAAS,GAAGhC,KAAK,CAACkB,OAAO,CAAC,MAAME,SAAS,CAAC,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAC/D,MAAMa,OAAO,GAAGjC,KAAK,CAACgB,MAAM,CAAC,CAAC;EAC9B,MAAMkB,GAAG,GAAG,IAAInC,KAAK,CAACoC,OAAO,CAAC,CAAC;EAC/B/B,QAAQ,CAACgC,KAAK,IAAI;IAChB,IAAI,CAACJ,SAAS,CAACK,OAAO,IAAI,CAACJ,OAAO,CAACI,OAAO,EAAE;IAC5CJ,OAAO,CAACI,OAAO,CAACC,gBAAgB,CAACJ,GAAG,CAAC;IACrCF,SAAS,CAACK,OAAO,CAACE,OAAO,CAAC,GAAG,GAAGL,GAAG,CAACM,CAAC,GAAG,GAAG,EAAE,GAAG,GAAGN,GAAG,CAACO,CAAC,GAAG,GAAG,EAAE,GAAG,GAAGP,GAAG,CAACQ,CAAC,GAAG,GAAG,EAAEd,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,CAAC;EAC/G,CAAC,CAAC;EACF,OAAO,aAAa9B,KAAK,CAACsB,aAAa,CAAC,OAAO,EAAExB,QAAQ,CAAC;IACxDgB,GAAG,EAAEb,SAAS,CAAC,CAACa,GAAG,EAAEmB,OAAO,CAAC;EAC/B,CAAC,EAAEpB,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACF,MAAM8B,aAAa,GAAG,aAAa3C,KAAK,CAACO,UAAU,CAAC,CAAC;EACnDqC,SAAS,EAAEC,UAAU,GAAG,GAAG;EAC3BjB,QAAQ,GAAG,GAAG;EACdC,QAAQ,GAAG,EAAE;EACb,GAAGhB;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM;IACJM;EACF,CAAC,GAAGpB,KAAK,CAAC+B,UAAU,CAAC1B,aAAa,CAAC;EACnC,MAAM2B,SAAS,GAAGhC,KAAK,CAACkB,OAAO,CAAC,MAAME,SAAS,CAAC,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAC/D,MAAM0B,OAAO,GAAG9C,KAAK,CAACgB,MAAM,CAAC,CAAC;EAC9B,MAAM4B,SAAS,GAAG5C,KAAK,CAACkB,OAAO,CAAC,MAAM2B,UAAU,KAAK,GAAG,GAAG,WAAW,GAAGA,UAAU,KAAK,GAAG,GAAG,WAAW,GAAG,WAAW,EAAE,CAACA,UAAU,CAAC,CAAC;EACtIzC,QAAQ,CAAC,MAAM;IACb,IAAI,CAAC4B,SAAS,CAACK,OAAO,IAAI,CAACS,OAAO,CAACT,OAAO,EAAE;IAC5CL,SAAS,CAACK,OAAO,CAACO,SAAS,CAAC,CAAChB,QAAQ,EAAEC,QAAQ,CAAC;EAClD,CAAC,CAAC;EACF,OAAO,aAAa7B,KAAK,CAACsB,aAAa,CAAC,OAAO,EAAExB,QAAQ,CAAC;IACxDgB,GAAG,EAAEb,SAAS,CAAC,CAACa,GAAG,EAAEgC,OAAO,CAAC;EAC/B,CAAC,EAAEjC,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF,SAASc,YAAY,EAAEzB,aAAa,EAAEyC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}