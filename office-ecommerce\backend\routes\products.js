const express = require('express');
const { body, validationResult } = require('express-validator');
const router = express.Router();

// Mock products data
const products = [
  {
    id: 'PROD001',
    name: 'Executive Office Chair',
    sku: 'EOC-001',
    category: 'Chairs',
    description: 'Premium executive office chair with ergonomic design',
    basePrice: 299.99,
    costPrice: 180.00,
    weight: 25.5,
    dimensions: {
      width: 65,
      depth: 70,
      height: 120
    },
    materials: ['Leather', 'Steel', 'Foam'],
    colors: ['Black', 'Brown', 'White'],
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'PROD002',
    name: 'Standing Desk',
    sku: 'SD-002',
    category: 'Desks',
    description: 'Height-adjustable standing desk for modern offices',
    basePrice: 599.99,
    costPrice: 350.00,
    weight: 45.0,
    dimensions: {
      width: 120,
      depth: 60,
      height: 75
    },
    materials: ['Wood', 'Steel'],
    colors: ['Oak', 'Walnut', 'White'],
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'PROD003',
    name: 'Conference Table',
    sku: 'CT-003',
    category: 'Tables',
    description: 'Large conference table for meeting rooms',
    basePrice: 899.99,
    costPrice: 500.00,
    weight: 80.0,
    dimensions: {
      width: 240,
      depth: 120,
      height: 75
    },
    materials: ['Wood', 'Steel'],
    colors: ['Oak', 'Mahogany'],
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// @route   GET /api/products
// @desc    Get all products
// @access  Private
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, search, category, isActive } = req.query;
    
    let filteredProducts = [...products];
    
    // Apply filters
    if (search) {
      filteredProducts = filteredProducts.filter(product => 
        product.name.toLowerCase().includes(search.toLowerCase()) ||
        product.sku.toLowerCase().includes(search.toLowerCase()) ||
        product.description.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    if (category) {
      filteredProducts = filteredProducts.filter(product => product.category === category);
    }
    
    if (isActive !== undefined) {
      filteredProducts = filteredProducts.filter(product => product.isActive === (isActive === 'true'));
    }
    
    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      data: {
        products: paginatedProducts,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(filteredProducts.length / limit),
          totalItems: filteredProducts.length,
          itemsPerPage: parseInt(limit)
        }
      }
    });
    
  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/products/categories
// @desc    Get all product categories
// @access  Private
router.get('/categories', async (req, res) => {
  try {
    const categories = [...new Set(products.map(p => p.category))];

    res.json({
      success: true,
      data: categories
    });

  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/products/:id
// @desc    Get product by ID
// @access  Private
router.get('/:id', async (req, res) => {
  try {
    const product = products.find(p => p.id === req.params.id);
    
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    res.json({
      success: true,
      data: product
    });
    
  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/products
// @desc    Create new product
// @access  Private
router.post('/', [
  body('name').notEmpty().trim(),
  body('sku').notEmpty().trim(),
  body('category').notEmpty().trim(),
  body('basePrice').isFloat({ min: 0 }),
  body('costPrice').isFloat({ min: 0 }),
  body('description').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { name, sku, category, description, basePrice, costPrice, weight, dimensions, materials, colors } = req.body;
    
    // Check if SKU already exists
    const existingProduct = products.find(p => p.sku === sku);
    if (existingProduct) {
      return res.status(400).json({
        success: false,
        message: 'Product with this SKU already exists'
      });
    }
    
    const newProduct = {
      id: `PROD${String(products.length + 1).padStart(3, '0')}`,
      name,
      sku,
      category,
      description: description || '',
      basePrice: parseFloat(basePrice),
      costPrice: parseFloat(costPrice),
      weight: weight || 0,
      dimensions: dimensions || { width: 0, depth: 0, height: 0 },
      materials: materials || [],
      colors: colors || [],
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    products.push(newProduct);
    
    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: newProduct
    });
    
  } catch (error) {
    console.error('Create product error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/products/:id
// @desc    Update product
// @access  Private
router.put('/:id', [
  body('name').optional().notEmpty().trim(),
  body('sku').optional().notEmpty().trim(),
  body('category').optional().notEmpty().trim(),
  body('basePrice').optional().isFloat({ min: 0 }),
  body('costPrice').optional().isFloat({ min: 0 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const productIndex = products.findIndex(p => p.id === req.params.id);
    if (productIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    // Update product
    const updatedProduct = {
      ...products[productIndex],
      ...req.body,
      updatedAt: new Date().toISOString()
    };
    
    products[productIndex] = updatedProduct;
    
    res.json({
      success: true,
      message: 'Product updated successfully',
      data: updatedProduct
    });
    
  } catch (error) {
    console.error('Update product error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   DELETE /api/products/:id
// @desc    Delete product
// @access  Private
router.delete('/:id', async (req, res) => {
  try {
    const productIndex = products.findIndex(p => p.id === req.params.id);
    if (productIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    // Soft delete - set isActive to false
    products[productIndex].isActive = false;
    products[productIndex].updatedAt = new Date().toISOString();
    
    res.json({
      success: true,
      message: 'Product deleted successfully'
    });
    
  } catch (error) {
    console.error('Delete product error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/products/:id/models
// @desc    Upload 3D model for product
// @access  Private
router.post('/:id/models', async (req, res) => {
  try {
    // Mock file upload response
    res.status(201).json({
      success: true,
      message: '3D model uploaded successfully',
      data: {
        FileName: 'uploaded-model.glb',
        FileSize: 2048000,
        IsPrimary: true
      }
    });
  } catch (error) {
    console.error('Upload model error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/products/:id/images
// @desc    Upload images for product
// @access  Private
router.post('/:id/images', async (req, res) => {
  try {
    // Mock file upload response
    res.status(201).json({
      success: true,
      message: 'Images uploaded successfully',
      data: [
        {
          FileName: 'uploaded-image.jpg',
          FileSize: 512000,
          IsPrimary: true
        }
      ]
    });
  } catch (error) {
    console.error('Upload images error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
