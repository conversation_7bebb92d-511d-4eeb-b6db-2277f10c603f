{"name": "three-mesh-bvh", "version": "0.5.24", "description": "A BVH implementation to speed up raycasting against three.js meshes.", "module": "src/index.js", "main": "build/index.umd.cjs", "type": "module", "types": "src/index.d.ts", "sideEffects": false, "scripts": {"start": "concurrently \"cd example && parcel watch ./*.html --dist-dir ./dev-bundle/ --public-url . --no-cache --no-hmr\" \"static-server\"", "build": "rollup -c", "build-examples": "cd example && parcel build ./*.html --dist-dir ./bundle/ --public-url . --no-cache --no-content-hash", "test": "cd test && jest", "lint": "eslint \"./src/**/*.{js,ts}\" \"./test/**/*.{js,ts}\" \"./example/*.js\" && tsc --noEmit", "benchmark": "node benchmark/run-benchmark.js", "prepublishOnly": "npm run build"}, "files": ["src/*", "build/*"], "keywords": ["graphics", "raycast", "tree", "bounds", "threejs", "three-js", "bounds-hierarchy", "performance", "raytracing", "pathtracing", "geometry", "mesh", "distance", "intersection", "acceleration", "bvh", "webvr", "webxr"], "repository": {"type": "git", "url": "git+https://github.com/gkjo<PERSON>son/three-mesh-bvh.git"}, "author": "<PERSON> <garrett.k<PERSON><PERSON><PERSON>@gmail.com>", "license": "MIT", "bugs": {"url": "https://github.com/gk<PERSON><PERSON>son/three-mesh-bvh/issues"}, "homepage": "https://github.com/gk<PERSON><PERSON>son/three-mesh-bvh#readme", "peerDependencies": {"three": ">= 0.123.0"}, "devDependencies": {"@babel/core": "^7.15.5", "@babel/preset-env": "^7.15.4", "@types/eslint": "^7.28.1", "@types/jest": "^27.0.2", "@types/three": "^0.139.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "babel-jest": "^27.2.4", "concurrently": "^5.3.0", "eslint": "^7.32.0", "eslint-config-mdcs": "^5.0.0", "eslint-plugin-jest": "^23.20.0", "jest": "^27.2.4", "parcel": "^2.0.0", "rollup": "^2.58.0", "script-loader": "^0.7.2", "simplex-noise": "^2.4.0", "static-server": "^2.2.1", "stats.js": "^0.17.0", "three": "^0.146.0", "typescript": "^4.4.3"}}