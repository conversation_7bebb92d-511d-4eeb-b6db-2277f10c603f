{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree } from '@react-three/fiber';\nimport omit from 'lodash.omit';\nimport pick from 'lodash.pick';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { TransformControls as TransformControls$1 } from 'three-stdlib';\nconst TransformControls = /*#__PURE__*/React.forwardRef(({\n  children,\n  domElement,\n  onChange,\n  onMouseDown,\n  onMouseUp,\n  onObjectChange,\n  object,\n  makeDefault,\n  ...props\n}, ref) => {\n  const transformOnlyPropNames = ['enabled', 'axis', 'mode', 'translationSnap', 'rotationSnap', 'scaleSnap', 'space', 'size', 'showX', 'showY', 'showZ'];\n  const {\n    camera,\n    ...rest\n  } = props;\n  const transformProps = pick(rest, transformOnlyPropNames);\n  const objectProps = omit(rest, transformOnlyPropNames); // @ts-expect-error new in @react-three/fiber@7.0.5\n\n  const defaultControls = useThree(state => state.controls);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const defaultCamera = useThree(state => state.camera);\n  const invalidate = useThree(state => state.invalidate);\n  const get = useThree(state => state.get);\n  const set = useThree(state => state.set);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = React.useMemo(() => new TransformControls$1(explCamera, explDomElement), [explCamera, explDomElement]);\n  const group = React.useRef();\n  React.useLayoutEffect(() => {\n    if (object) {\n      controls.attach(object instanceof THREE.Object3D ? object : object.current);\n    } else if (group.current instanceof THREE.Object3D) {\n      controls.attach(group.current);\n    }\n    return () => void controls.detach();\n  }, [object, children, controls]);\n  React.useEffect(() => {\n    if (defaultControls) {\n      const callback = event => defaultControls.enabled = !event.value;\n      controls.addEventListener('dragging-changed', callback);\n      return () => controls.removeEventListener('dragging-changed', callback);\n    }\n  }, [controls, defaultControls]);\n  const onChangeRef = React.useRef();\n  const onMouseDownRef = React.useRef();\n  const onMouseUpRef = React.useRef();\n  const onObjectChangeRef = React.useRef();\n  React.useLayoutEffect(() => void (onChangeRef.current = onChange), [onChange]);\n  React.useLayoutEffect(() => void (onMouseDownRef.current = onMouseDown), [onMouseDown]);\n  React.useLayoutEffect(() => void (onMouseUpRef.current = onMouseUp), [onMouseUp]);\n  React.useLayoutEffect(() => void (onObjectChangeRef.current = onObjectChange), [onObjectChange]);\n  React.useEffect(() => {\n    const onChange = e => {\n      invalidate();\n      onChangeRef.current == null ? void 0 : onChangeRef.current(e);\n    };\n    const onMouseDown = e => onMouseDownRef.current == null ? void 0 : onMouseDownRef.current(e);\n    const onMouseUp = e => onMouseUpRef.current == null ? void 0 : onMouseUpRef.current(e);\n    const onObjectChange = e => onObjectChangeRef.current == null ? void 0 : onObjectChangeRef.current(e);\n    controls.addEventListener('change', onChange);\n    controls.addEventListener('mouseDown', onMouseDown);\n    controls.addEventListener('mouseUp', onMouseUp);\n    controls.addEventListener('objectChange', onObjectChange);\n    return () => {\n      controls.removeEventListener('change', onChange);\n      controls.removeEventListener('mouseDown', onMouseDown);\n      controls.removeEventListener('mouseUp', onMouseUp);\n      controls.removeEventListener('objectChange', onObjectChange);\n    };\n  }, [invalidate, controls]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return controls ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls\n  }, transformProps)), /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: group\n  }, objectProps), children)) : null;\n});\nexport { TransformControls };", "map": {"version": 3, "names": ["_extends", "useThree", "omit", "pick", "React", "THREE", "TransformControls", "TransformControls$1", "forwardRef", "children", "dom<PERSON>lement", "onChange", "onMouseDown", "onMouseUp", "onObjectChange", "object", "makeDefault", "props", "ref", "transformOnlyPropNames", "camera", "rest", "transformProps", "objectProps", "defaultControls", "state", "controls", "gl", "events", "defaultCamera", "invalidate", "get", "set", "explCamera", "explDomElement", "connected", "useMemo", "group", "useRef", "useLayoutEffect", "attach", "Object3D", "current", "detach", "useEffect", "callback", "event", "enabled", "value", "addEventListener", "removeEventListener", "onChangeRef", "onMouseDownRef", "onMouseUpRef", "onObjectChangeRef", "e", "old", "createElement", "Fragment"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/TransformControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree } from '@react-three/fiber';\nimport omit from 'lodash.omit';\nimport pick from 'lodash.pick';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { TransformControls as TransformControls$1 } from 'three-stdlib';\n\nconst TransformControls = /*#__PURE__*/React.forwardRef(({\n  children,\n  domElement,\n  onChange,\n  onMouseDown,\n  onMouseUp,\n  onObjectChange,\n  object,\n  makeDefault,\n  ...props\n}, ref) => {\n  const transformOnlyPropNames = ['enabled', 'axis', 'mode', 'translationSnap', 'rotationSnap', 'scaleSnap', 'space', 'size', 'showX', 'showY', 'showZ'];\n  const {\n    camera,\n    ...rest\n  } = props;\n  const transformProps = pick(rest, transformOnlyPropNames);\n  const objectProps = omit(rest, transformOnlyPropNames); // @ts-expect-error new in @react-three/fiber@7.0.5\n\n  const defaultControls = useThree(state => state.controls);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const defaultCamera = useThree(state => state.camera);\n  const invalidate = useThree(state => state.invalidate);\n  const get = useThree(state => state.get);\n  const set = useThree(state => state.set);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = React.useMemo(() => new TransformControls$1(explCamera, explDomElement), [explCamera, explDomElement]);\n  const group = React.useRef();\n  React.useLayoutEffect(() => {\n    if (object) {\n      controls.attach(object instanceof THREE.Object3D ? object : object.current);\n    } else if (group.current instanceof THREE.Object3D) {\n      controls.attach(group.current);\n    }\n\n    return () => void controls.detach();\n  }, [object, children, controls]);\n  React.useEffect(() => {\n    if (defaultControls) {\n      const callback = event => defaultControls.enabled = !event.value;\n\n      controls.addEventListener('dragging-changed', callback);\n      return () => controls.removeEventListener('dragging-changed', callback);\n    }\n  }, [controls, defaultControls]);\n  const onChangeRef = React.useRef();\n  const onMouseDownRef = React.useRef();\n  const onMouseUpRef = React.useRef();\n  const onObjectChangeRef = React.useRef();\n  React.useLayoutEffect(() => void (onChangeRef.current = onChange), [onChange]);\n  React.useLayoutEffect(() => void (onMouseDownRef.current = onMouseDown), [onMouseDown]);\n  React.useLayoutEffect(() => void (onMouseUpRef.current = onMouseUp), [onMouseUp]);\n  React.useLayoutEffect(() => void (onObjectChangeRef.current = onObjectChange), [onObjectChange]);\n  React.useEffect(() => {\n    const onChange = e => {\n      invalidate();\n      onChangeRef.current == null ? void 0 : onChangeRef.current(e);\n    };\n\n    const onMouseDown = e => onMouseDownRef.current == null ? void 0 : onMouseDownRef.current(e);\n\n    const onMouseUp = e => onMouseUpRef.current == null ? void 0 : onMouseUpRef.current(e);\n\n    const onObjectChange = e => onObjectChangeRef.current == null ? void 0 : onObjectChangeRef.current(e);\n\n    controls.addEventListener('change', onChange);\n    controls.addEventListener('mouseDown', onMouseDown);\n    controls.addEventListener('mouseUp', onMouseUp);\n    controls.addEventListener('objectChange', onObjectChange);\n    return () => {\n      controls.removeEventListener('change', onChange);\n      controls.removeEventListener('mouseDown', onMouseDown);\n      controls.removeEventListener('mouseUp', onMouseUp);\n      controls.removeEventListener('objectChange', onObjectChange);\n    };\n  }, [invalidate, controls]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return controls ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls\n  }, transformProps)), /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: group\n  }, objectProps), children)) : null;\n});\n\nexport { TransformControls };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,IAAIC,mBAAmB,QAAQ,cAAc;AAEvE,MAAMD,iBAAiB,GAAG,aAAaF,KAAK,CAACI,UAAU,CAAC,CAAC;EACvDC,QAAQ;EACRC,UAAU;EACVC,QAAQ;EACRC,WAAW;EACXC,SAAS;EACTC,cAAc;EACdC,MAAM;EACNC,WAAW;EACX,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,sBAAsB,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE,cAAc,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACtJ,MAAM;IACJC,MAAM;IACN,GAAGC;EACL,CAAC,GAAGJ,KAAK;EACT,MAAMK,cAAc,GAAGnB,IAAI,CAACkB,IAAI,EAAEF,sBAAsB,CAAC;EACzD,MAAMI,WAAW,GAAGrB,IAAI,CAACmB,IAAI,EAAEF,sBAAsB,CAAC,CAAC,CAAC;;EAExD,MAAMK,eAAe,GAAGvB,QAAQ,CAACwB,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;EACzD,MAAMC,EAAE,GAAG1B,QAAQ,CAACwB,KAAK,IAAIA,KAAK,CAACE,EAAE,CAAC;EACtC,MAAMC,MAAM,GAAG3B,QAAQ,CAACwB,KAAK,IAAIA,KAAK,CAACG,MAAM,CAAC;EAC9C,MAAMC,aAAa,GAAG5B,QAAQ,CAACwB,KAAK,IAAIA,KAAK,CAACL,MAAM,CAAC;EACrD,MAAMU,UAAU,GAAG7B,QAAQ,CAACwB,KAAK,IAAIA,KAAK,CAACK,UAAU,CAAC;EACtD,MAAMC,GAAG,GAAG9B,QAAQ,CAACwB,KAAK,IAAIA,KAAK,CAACM,GAAG,CAAC;EACxC,MAAMC,GAAG,GAAG/B,QAAQ,CAACwB,KAAK,IAAIA,KAAK,CAACO,GAAG,CAAC;EACxC,MAAMC,UAAU,GAAGb,MAAM,IAAIS,aAAa;EAC1C,MAAMK,cAAc,GAAGxB,UAAU,IAAIkB,MAAM,CAACO,SAAS,IAAIR,EAAE,CAACjB,UAAU;EACtE,MAAMgB,QAAQ,GAAGtB,KAAK,CAACgC,OAAO,CAAC,MAAM,IAAI7B,mBAAmB,CAAC0B,UAAU,EAAEC,cAAc,CAAC,EAAE,CAACD,UAAU,EAAEC,cAAc,CAAC,CAAC;EACvH,MAAMG,KAAK,GAAGjC,KAAK,CAACkC,MAAM,CAAC,CAAC;EAC5BlC,KAAK,CAACmC,eAAe,CAAC,MAAM;IAC1B,IAAIxB,MAAM,EAAE;MACVW,QAAQ,CAACc,MAAM,CAACzB,MAAM,YAAYV,KAAK,CAACoC,QAAQ,GAAG1B,MAAM,GAAGA,MAAM,CAAC2B,OAAO,CAAC;IAC7E,CAAC,MAAM,IAAIL,KAAK,CAACK,OAAO,YAAYrC,KAAK,CAACoC,QAAQ,EAAE;MAClDf,QAAQ,CAACc,MAAM,CAACH,KAAK,CAACK,OAAO,CAAC;IAChC;IAEA,OAAO,MAAM,KAAKhB,QAAQ,CAACiB,MAAM,CAAC,CAAC;EACrC,CAAC,EAAE,CAAC5B,MAAM,EAAEN,QAAQ,EAAEiB,QAAQ,CAAC,CAAC;EAChCtB,KAAK,CAACwC,SAAS,CAAC,MAAM;IACpB,IAAIpB,eAAe,EAAE;MACnB,MAAMqB,QAAQ,GAAGC,KAAK,IAAItB,eAAe,CAACuB,OAAO,GAAG,CAACD,KAAK,CAACE,KAAK;MAEhEtB,QAAQ,CAACuB,gBAAgB,CAAC,kBAAkB,EAAEJ,QAAQ,CAAC;MACvD,OAAO,MAAMnB,QAAQ,CAACwB,mBAAmB,CAAC,kBAAkB,EAAEL,QAAQ,CAAC;IACzE;EACF,CAAC,EAAE,CAACnB,QAAQ,EAAEF,eAAe,CAAC,CAAC;EAC/B,MAAM2B,WAAW,GAAG/C,KAAK,CAACkC,MAAM,CAAC,CAAC;EAClC,MAAMc,cAAc,GAAGhD,KAAK,CAACkC,MAAM,CAAC,CAAC;EACrC,MAAMe,YAAY,GAAGjD,KAAK,CAACkC,MAAM,CAAC,CAAC;EACnC,MAAMgB,iBAAiB,GAAGlD,KAAK,CAACkC,MAAM,CAAC,CAAC;EACxClC,KAAK,CAACmC,eAAe,CAAC,MAAM,MAAMY,WAAW,CAACT,OAAO,GAAG/B,QAAQ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAC9EP,KAAK,CAACmC,eAAe,CAAC,MAAM,MAAMa,cAAc,CAACV,OAAO,GAAG9B,WAAW,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EACvFR,KAAK,CAACmC,eAAe,CAAC,MAAM,MAAMc,YAAY,CAACX,OAAO,GAAG7B,SAAS,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EACjFT,KAAK,CAACmC,eAAe,CAAC,MAAM,MAAMe,iBAAiB,CAACZ,OAAO,GAAG5B,cAAc,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EAChGV,KAAK,CAACwC,SAAS,CAAC,MAAM;IACpB,MAAMjC,QAAQ,GAAG4C,CAAC,IAAI;MACpBzB,UAAU,CAAC,CAAC;MACZqB,WAAW,CAACT,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGS,WAAW,CAACT,OAAO,CAACa,CAAC,CAAC;IAC/D,CAAC;IAED,MAAM3C,WAAW,GAAG2C,CAAC,IAAIH,cAAc,CAACV,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGU,cAAc,CAACV,OAAO,CAACa,CAAC,CAAC;IAE5F,MAAM1C,SAAS,GAAG0C,CAAC,IAAIF,YAAY,CAACX,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGW,YAAY,CAACX,OAAO,CAACa,CAAC,CAAC;IAEtF,MAAMzC,cAAc,GAAGyC,CAAC,IAAID,iBAAiB,CAACZ,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGY,iBAAiB,CAACZ,OAAO,CAACa,CAAC,CAAC;IAErG7B,QAAQ,CAACuB,gBAAgB,CAAC,QAAQ,EAAEtC,QAAQ,CAAC;IAC7Ce,QAAQ,CAACuB,gBAAgB,CAAC,WAAW,EAAErC,WAAW,CAAC;IACnDc,QAAQ,CAACuB,gBAAgB,CAAC,SAAS,EAAEpC,SAAS,CAAC;IAC/Ca,QAAQ,CAACuB,gBAAgB,CAAC,cAAc,EAAEnC,cAAc,CAAC;IACzD,OAAO,MAAM;MACXY,QAAQ,CAACwB,mBAAmB,CAAC,QAAQ,EAAEvC,QAAQ,CAAC;MAChDe,QAAQ,CAACwB,mBAAmB,CAAC,WAAW,EAAEtC,WAAW,CAAC;MACtDc,QAAQ,CAACwB,mBAAmB,CAAC,SAAS,EAAErC,SAAS,CAAC;MAClDa,QAAQ,CAACwB,mBAAmB,CAAC,cAAc,EAAEpC,cAAc,CAAC;IAC9D,CAAC;EACH,CAAC,EAAE,CAACgB,UAAU,EAAEJ,QAAQ,CAAC,CAAC;EAC1BtB,KAAK,CAACwC,SAAS,CAAC,MAAM;IACpB,IAAI5B,WAAW,EAAE;MACf,MAAMwC,GAAG,GAAGzB,GAAG,CAAC,CAAC,CAACL,QAAQ;MAC1BM,GAAG,CAAC;QACFN;MACF,CAAC,CAAC;MACF,OAAO,MAAMM,GAAG,CAAC;QACfN,QAAQ,EAAE8B;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACxC,WAAW,EAAEU,QAAQ,CAAC,CAAC;EAC3B,OAAOA,QAAQ,GAAG,aAAatB,KAAK,CAACqD,aAAa,CAACrD,KAAK,CAACsD,QAAQ,EAAE,IAAI,EAAE,aAAatD,KAAK,CAACqD,aAAa,CAAC,WAAW,EAAEzD,QAAQ,CAAC;IAC9HkB,GAAG,EAAEA,GAAG;IACRH,MAAM,EAAEW;EACV,CAAC,EAAEJ,cAAc,CAAC,CAAC,EAAE,aAAalB,KAAK,CAACqD,aAAa,CAAC,OAAO,EAAEzD,QAAQ,CAAC;IACtEkB,GAAG,EAAEmB;EACP,CAAC,EAAEd,WAAW,CAAC,EAAEd,QAAQ,CAAC,CAAC,GAAG,IAAI;AACpC,CAAC,CAAC;AAEF,SAASH,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}