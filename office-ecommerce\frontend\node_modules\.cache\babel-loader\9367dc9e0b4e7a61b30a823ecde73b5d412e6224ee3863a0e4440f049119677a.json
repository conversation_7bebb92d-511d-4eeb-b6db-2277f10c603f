{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { BufferAttribute } from 'three';\n\n/**\n * Used exclusively as a child of a BufferGeometry.\n * Computes the BufferAttribute by calling the `compute` function\n * and attaches the attribute to the geometry.\n */\nconst ComputedAttribute = ({\n  compute,\n  name,\n  ...props\n}) => {\n  const [bufferAttribute] = React.useState(() => new BufferAttribute(new Float32Array(0), 1));\n  const primitive = React.useRef(null);\n  React.useLayoutEffect(() => {\n    if (primitive.current) {\n      var _ref;\n\n      // @ts-expect-error brittle\n      const parent = (_ref = primitive.current.parent) !== null && _ref !== void 0 ? _ref : primitive.current.__r3f.parent;\n      const attr = compute(parent);\n      primitive.current.copy(attr);\n    }\n  }, [compute]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: primitive,\n    object: bufferAttribute,\n    attach: `attributes-${name}`\n  }, props));\n};\nexport { ComputedAttribute };", "map": {"version": 3, "names": ["_extends", "React", "BufferAttribute", "ComputedAttribute", "compute", "name", "props", "bufferAttribute", "useState", "Float32Array", "primitive", "useRef", "useLayoutEffect", "current", "_ref", "parent", "__r3f", "attr", "copy", "createElement", "ref", "object", "attach"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/ComputedAttribute.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { BufferAttribute } from 'three';\n\n/**\n * Used exclusively as a child of a BufferGeometry.\n * Computes the BufferAttribute by calling the `compute` function\n * and attaches the attribute to the geometry.\n */\nconst ComputedAttribute = ({\n  compute,\n  name,\n  ...props\n}) => {\n  const [bufferAttribute] = React.useState(() => new BufferAttribute(new Float32Array(0), 1));\n  const primitive = React.useRef(null);\n  React.useLayoutEffect(() => {\n    if (primitive.current) {\n      var _ref;\n\n      // @ts-expect-error brittle\n      const parent = (_ref = primitive.current.parent) !== null && _ref !== void 0 ? _ref : primitive.current.__r3f.parent;\n      const attr = compute(parent);\n      primitive.current.copy(attr);\n    }\n  }, [compute]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: primitive,\n    object: bufferAttribute,\n    attach: `attributes-${name}`\n  }, props));\n};\n\nexport { ComputedAttribute };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,QAAQ,OAAO;;AAEvC;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAGA,CAAC;EACzBC,OAAO;EACPC,IAAI;EACJ,GAAGC;AACL,CAAC,KAAK;EACJ,MAAM,CAACC,eAAe,CAAC,GAAGN,KAAK,CAACO,QAAQ,CAAC,MAAM,IAAIN,eAAe,CAAC,IAAIO,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3F,MAAMC,SAAS,GAAGT,KAAK,CAACU,MAAM,CAAC,IAAI,CAAC;EACpCV,KAAK,CAACW,eAAe,CAAC,MAAM;IAC1B,IAAIF,SAAS,CAACG,OAAO,EAAE;MACrB,IAAIC,IAAI;;MAER;MACA,MAAMC,MAAM,GAAG,CAACD,IAAI,GAAGJ,SAAS,CAACG,OAAO,CAACE,MAAM,MAAM,IAAI,IAAID,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGJ,SAAS,CAACG,OAAO,CAACG,KAAK,CAACD,MAAM;MACpH,MAAME,IAAI,GAAGb,OAAO,CAACW,MAAM,CAAC;MAC5BL,SAAS,CAACG,OAAO,CAACK,IAAI,CAACD,IAAI,CAAC;IAC9B;EACF,CAAC,EAAE,CAACb,OAAO,CAAC,CAAC;EACb,OAAO,aAAaH,KAAK,CAACkB,aAAa,CAAC,WAAW,EAAEnB,QAAQ,CAAC;IAC5DoB,GAAG,EAAEV,SAAS;IACdW,MAAM,EAAEd,eAAe;IACvBe,MAAM,EAAE,cAAcjB,IAAI;EAC5B,CAAC,EAAEC,KAAK,CAAC,CAAC;AACZ,CAAC;AAED,SAASH,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}