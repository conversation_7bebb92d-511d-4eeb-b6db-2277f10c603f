{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport mergeRefs from 'react-merge-refs';\nimport { useFrame } from '@react-three/fiber';\nconst ScreenSpace = /*#__PURE__*/React.forwardRef(({\n  children,\n  depth = -1,\n  ...rest\n}, ref) => {\n  const localRef = React.useRef(null);\n  useFrame(({\n    camera\n  }) => {\n    localRef.current.quaternion.copy(camera.quaternion);\n    localRef.current.position.copy(camera.position);\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: mergeRefs([ref, localRef])\n  }, rest), /*#__PURE__*/React.createElement(\"group\", {\n    \"position-z\": -depth\n  }, children));\n});\nexport { ScreenSpace };", "map": {"version": 3, "names": ["_extends", "React", "mergeRefs", "useFrame", "ScreenSpace", "forwardRef", "children", "depth", "rest", "ref", "localRef", "useRef", "camera", "current", "quaternion", "copy", "position", "createElement"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/ScreenSpace.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport mergeRefs from 'react-merge-refs';\nimport { useFrame } from '@react-three/fiber';\n\nconst ScreenSpace = /*#__PURE__*/React.forwardRef(({\n  children,\n  depth = -1,\n  ...rest\n}, ref) => {\n  const localRef = React.useRef(null);\n  useFrame(({\n    camera\n  }) => {\n    localRef.current.quaternion.copy(camera.quaternion);\n    localRef.current.position.copy(camera.position);\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: mergeRefs([ref, localRef])\n  }, rest), /*#__PURE__*/React.createElement(\"group\", {\n    \"position-z\": -depth\n  }, children));\n});\n\nexport { ScreenSpace };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,MAAMC,WAAW,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,CAAC;EACjDC,QAAQ;EACRC,KAAK,GAAG,CAAC,CAAC;EACV,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,QAAQ,GAAGT,KAAK,CAACU,MAAM,CAAC,IAAI,CAAC;EACnCR,QAAQ,CAAC,CAAC;IACRS;EACF,CAAC,KAAK;IACJF,QAAQ,CAACG,OAAO,CAACC,UAAU,CAACC,IAAI,CAACH,MAAM,CAACE,UAAU,CAAC;IACnDJ,QAAQ,CAACG,OAAO,CAACG,QAAQ,CAACD,IAAI,CAACH,MAAM,CAACI,QAAQ,CAAC;EACjD,CAAC,CAAC;EACF,OAAO,aAAaf,KAAK,CAACgB,aAAa,CAAC,OAAO,EAAEjB,QAAQ,CAAC;IACxDS,GAAG,EAAEP,SAAS,CAAC,CAACO,GAAG,EAAEC,QAAQ,CAAC;EAChC,CAAC,EAAEF,IAAI,CAAC,EAAE,aAAaP,KAAK,CAACgB,aAAa,CAAC,OAAO,EAAE;IAClD,YAAY,EAAE,CAACV;EACjB,CAAC,EAAED,QAAQ,CAAC,CAAC;AACf,CAAC,CAAC;AAEF,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}