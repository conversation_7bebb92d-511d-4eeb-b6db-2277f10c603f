{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\admin\\\\modals\\\\ProductDetailsModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport ThreeJSPreview from '../components/ThreeJSPreview';\nimport './ProductDetailsModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductDetailsModal = ({\n  product,\n  onClose,\n  onEdit\n}) => {\n  _s();\n  var _product$product, _product$product2, _product$product3, _product$product4, _product$product5, _product$product6, _product$product7, _product$product8, _product$product9, _product$product0, _product$product1, _product$product10, _product$product11, _product$product12, _product$product13, _product$product14, _product$product15, _product$product16, _product$product17, _product$product18, _product$models, _product$images, _product$components, _product$colors;\n  const [activeTab, setActiveTab] = useState('details');\n  if (!product) return null;\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n  const formatDate = dateString => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const getStatusBadgeColor = status => {\n    switch (status) {\n      case 'Active':\n        return '#27ae60';\n      case 'Draft':\n        return '#f39c12';\n      case 'Inactive':\n        return '#95a5a6';\n      case 'Discontinued':\n        return '#e74c3c';\n      case 'Pending Review':\n        return '#3498db';\n      default:\n        return '#95a5a6';\n    }\n  };\n  const tabs = [{\n    id: 'details',\n    label: 'Product Details',\n    icon: '📝'\n  }, {\n    id: 'files',\n    label: 'Files & Media',\n    icon: '📁'\n  }, {\n    id: 'components',\n    label: 'Components',\n    icon: '🔧'\n  }, {\n    id: 'audit',\n    label: 'Audit Trail',\n    icon: '📊'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-details-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: ((_product$product = product.product) === null || _product$product === void 0 ? void 0 : _product$product.ProductName) || 'Product Details'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-badge\",\n            style: {\n              backgroundColor: getStatusBadgeColor((_product$product2 = product.product) === null || _product$product2 === void 0 ? void 0 : _product$product2.Status)\n            },\n            children: (_product$product3 = product.product) === null || _product$product3 === void 0 ? void 0 : _product$product3.Status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-secondary\",\n            onClick: onEdit,\n            children: \"\\u270F\\uFE0F Edit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"modal-close\",\n            onClick: onClose,\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-tabs\",\n        children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-button ${activeTab === tab.id ? 'active' : ''}`,\n          onClick: () => setActiveTab(tab.id),\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tab-icon\",\n            children: tab.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this), tab.label]\n        }, tab.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-body\",\n        children: [activeTab === 'details' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"details-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Basic Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Product Code:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 88,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: ((_product$product4 = product.product) === null || _product$product4 === void 0 ? void 0 : _product$product4.ProductCode) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 89,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Product Name:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 92,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: ((_product$product5 = product.product) === null || _product$product5 === void 0 ? void 0 : _product$product5.ProductName) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 93,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Category:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 96,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: ((_product$product6 = product.product) === null || _product$product6 === void 0 ? void 0 : _product$product6.CategoryName) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 95,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Base Price:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 100,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: (_product$product7 = product.product) !== null && _product$product7 !== void 0 && _product$product7.BasePrice ? formatCurrency(product.product.BasePrice) : 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 101,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Physical Properties\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Weight:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 110,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: (_product$product8 = product.product) !== null && _product$product8 !== void 0 && _product$product8.Weight ? `${product.product.Weight} kg` : 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 111,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Dimensions:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 114,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: ((_product$product9 = product.product) === null || _product$product9 === void 0 ? void 0 : _product$product9.Dimensions) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Material:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: ((_product$product0 = product.product) === null || _product$product0 === void 0 ? void 0 : _product$product0.Material) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 119,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Color:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 122,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: ((_product$product1 = product.product) === null || _product$product1 === void 0 ? void 0 : _product$product1.Color) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-section full-width\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"description-content\",\n                children: ((_product$product10 = product.product) === null || _product$product10 === void 0 ? void 0 : _product$product10.Description) || 'No description available.'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Customizable:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `boolean-badge ${(_product$product11 = product.product) !== null && _product$product11 !== void 0 && _product$product11.IsCustomizable ? 'true' : 'false'}`,\n                    children: (_product$product12 = product.product) !== null && _product$product12 !== void 0 && _product$product12.IsCustomizable ? 'Yes' : 'No'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Active:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `boolean-badge ${(_product$product13 = product.product) !== null && _product$product13 !== void 0 && _product$product13.IsActive ? 'true' : 'false'}`,\n                    children: (_product$product14 = product.product) !== null && _product$product14 !== void 0 && _product$product14.IsActive ? 'Yes' : 'No'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Timestamps\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Created:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: formatDate((_product$product15 = product.product) === null || _product$product15 === void 0 ? void 0 : _product$product15.CreatedAt)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Updated:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: formatDate((_product$product16 = product.product) === null || _product$product16 === void 0 ? void 0 : _product$product16.UpdatedAt)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Created By:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: ((_product$product17 = product.product) === null || _product$product17 === void 0 ? void 0 : _product$product17.CreatedBy) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Updated By:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: ((_product$product18 = product.product) === null || _product$product18 === void 0 ? void 0 : _product$product18.UpdatedBy) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 13\n        }, this), activeTab === 'files' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"files-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"file-category\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: [\"3D Models (\", ((_product$models = product.models) === null || _product$models === void 0 ? void 0 : _product$models.length) || 0, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this), product.models && product.models.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"files-grid\",\n                children: product.models.map((model, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-card\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"file-icon\",\n                    children: \"\\uD83C\\uDFAF\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"file-info\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"file-name\",\n                      children: model.FileName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"file-details\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [(model.FileSize / 1024 / 1024).toFixed(2), \" MB\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 191,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: model.IsPrimary ? 'Primary' : 'Secondary'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 192,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"file-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn-icon\",\n                      title: \"Download\",\n                      children: \"\\u2B07\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn-icon\",\n                      title: \"Preview\",\n                      children: \"\\uD83D\\uDC41\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 197,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"no-files\",\n                children: \"No 3D models uploaded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"file-category\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: [\"Images (\", ((_product$images = product.images) === null || _product$images === void 0 ? void 0 : _product$images.length) || 0, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this), product.images && product.images.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"images-grid\",\n                children: product.images.map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"image-card\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"image-preview\",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: `/api/files/images/${image.FileName}`,\n                      alt: image.AltText || 'Product image',\n                      onError: e => {\n                        e.target.src = '/placeholder-image.png';\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"image-info\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"image-name\",\n                      children: image.FileName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 223,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"image-details\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [(image.FileSize / 1024).toFixed(1), \" KB\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 225,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: image.IsPrimary ? 'Primary' : 'Secondary'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 226,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"no-files\",\n                children: \"No images uploaded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 17\n            }, this), product.models && product.models.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"file-category\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"3D Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"preview-container\",\n                children: /*#__PURE__*/_jsxDEV(ThreeJSPreview, {\n                  modelUrl: `/api/files/models/${product.models[0].FileName}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this), activeTab === 'components' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"components-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"Product Components (\", ((_product$components = product.components) === null || _product$components === void 0 ? void 0 : _product$components.length) || 0, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this), product.components && product.components.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"components-table\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Component Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 261,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Quantity\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Unit Cost\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 263,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Total Cost\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 264,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Supplier\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 265,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: product.components.map((component, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      children: component.ComponentName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: component.Quantity\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: formatCurrency(component.UnitCost)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: formatCurrency(component.Quantity * component.UnitCost)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 274,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: component.Supplier || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 29\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-data\",\n              children: \"No components defined for this product\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"colors-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: [\"Available Colors (\", ((_product$colors = product.colors) === null || _product$colors === void 0 ? void 0 : _product$colors.length) || 0, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this), product.colors && product.colors.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"colors-grid\",\n                children: product.colors.map((color, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"color-card\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"color-swatch\",\n                    style: {\n                      backgroundColor: color.HexCode\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"color-info\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"color-name\",\n                      children: color.ColorName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 296,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"color-code\",\n                      children: color.HexCode\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 297,\n                      columnNumber: 29\n                    }, this), color.PriceModifier !== 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"price-modifier\",\n                      children: [color.PriceModifier > 0 ? '+' : '', formatCurrency(color.PriceModifier)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 299,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"no-data\",\n                children: \"No color options defined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this), activeTab === 'audit' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"audit-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Audit Trail\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this), product.auditTrail && product.auditTrail.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"audit-timeline\",\n              children: product.auditTrail.map((entry, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"audit-entry\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"audit-timestamp\",\n                  children: formatDate(entry.ChangeDate)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"audit-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"audit-action\",\n                    children: entry.Action\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"audit-user\",\n                    children: [\"by \", entry.ChangedBy]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 27\n                  }, this), entry.ChangeDetails && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"audit-details\",\n                    children: entry.ChangeDetails\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-data\",\n              children: \"No audit trail available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-footer\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-secondary\",\n          onClick: onClose,\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: onEdit,\n          children: \"Edit Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetailsModal, \"7WiPVAxIvYEBJn1wcZrXYHo3rOc=\");\n_c = ProductDetailsModal;\nexport default ProductDetailsModal;\nvar _c;\n$RefreshReg$(_c, \"ProductDetailsModal\");", "map": {"version": 3, "names": ["React", "useState", "ThreeJSPreview", "jsxDEV", "_jsxDEV", "ProductDetailsModal", "product", "onClose", "onEdit", "_s", "_product$product", "_product$product2", "_product$product3", "_product$product4", "_product$product5", "_product$product6", "_product$product7", "_product$product8", "_product$product9", "_product$product0", "_product$product1", "_product$product10", "_product$product11", "_product$product12", "_product$product13", "_product$product14", "_product$product15", "_product$product16", "_product$product17", "_product$product18", "_product$models", "_product$images", "_product$components", "_product$colors", "activeTab", "setActiveTab", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "getStatusBadgeColor", "status", "tabs", "id", "label", "icon", "className", "children", "ProductName", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "backgroundColor", "Status", "onClick", "map", "tab", "ProductCode", "CategoryName", "BasePrice", "Weight", "Dimensions", "Material", "Color", "Description", "IsCustomizable", "IsActive", "CreatedAt", "UpdatedAt", "CreatedBy", "UpdatedBy", "models", "length", "model", "index", "FileName", "FileSize", "toFixed", "IsPrimary", "title", "images", "image", "src", "alt", "AltText", "onError", "e", "target", "modelUrl", "components", "component", "ComponentName", "Quantity", "UnitCost", "Supplier", "colors", "color", "HexCode", "ColorName", "PriceModifier", "auditTrail", "entry", "ChangeDate", "Action", "ChangedBy", "ChangeDetails", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/admin/modals/ProductDetailsModal.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport ThreeJSPreview from '../components/ThreeJSPreview';\nimport './ProductDetailsModal.css';\n\nconst ProductDetailsModal = ({ product, onClose, onEdit }) => {\n  const [activeTab, setActiveTab] = useState('details');\n\n  if (!product) return null;\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const getStatusBadgeColor = (status) => {\n    switch (status) {\n      case 'Active': return '#27ae60';\n      case 'Draft': return '#f39c12';\n      case 'Inactive': return '#95a5a6';\n      case 'Discontinued': return '#e74c3c';\n      case 'Pending Review': return '#3498db';\n      default: return '#95a5a6';\n    }\n  };\n\n  const tabs = [\n    { id: 'details', label: 'Product Details', icon: '📝' },\n    { id: 'files', label: 'Files & Media', icon: '📁' },\n    { id: 'components', label: 'Components', icon: '🔧' },\n    { id: 'audit', label: 'Audit Trail', icon: '📊' }\n  ];\n\n  return (\n    <div className=\"modal-overlay\">\n      <div className=\"product-details-modal\">\n        <div className=\"modal-header\">\n          <div className=\"header-content\">\n            <h2>{product.product?.ProductName || 'Product Details'}</h2>\n            <span \n              className=\"status-badge\" \n              style={{ backgroundColor: getStatusBadgeColor(product.product?.Status) }}\n            >\n              {product.product?.Status}\n            </span>\n          </div>\n          <div className=\"header-actions\">\n            <button className=\"btn btn-secondary\" onClick={onEdit}>\n              ✏️ Edit\n            </button>\n            <button className=\"modal-close\" onClick={onClose}>×</button>\n          </div>\n        </div>\n\n        <div className=\"modal-tabs\">\n          {tabs.map(tab => (\n            <button\n              key={tab.id}\n              className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}\n              onClick={() => setActiveTab(tab.id)}\n            >\n              <span className=\"tab-icon\">{tab.icon}</span>\n              {tab.label}\n            </button>\n          ))}\n        </div>\n\n        <div className=\"modal-body\">\n          {activeTab === 'details' && (\n            <div className=\"tab-content\">\n              <div className=\"details-grid\">\n                <div className=\"detail-section\">\n                  <h3>Basic Information</h3>\n                  <div className=\"detail-group\">\n                    <div className=\"detail-item\">\n                      <label>Product Code:</label>\n                      <span>{product.product?.ProductCode || 'N/A'}</span>\n                    </div>\n                    <div className=\"detail-item\">\n                      <label>Product Name:</label>\n                      <span>{product.product?.ProductName || 'N/A'}</span>\n                    </div>\n                    <div className=\"detail-item\">\n                      <label>Category:</label>\n                      <span>{product.product?.CategoryName || 'N/A'}</span>\n                    </div>\n                    <div className=\"detail-item\">\n                      <label>Base Price:</label>\n                      <span>{product.product?.BasePrice ? formatCurrency(product.product.BasePrice) : 'N/A'}</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"detail-section\">\n                  <h3>Physical Properties</h3>\n                  <div className=\"detail-group\">\n                    <div className=\"detail-item\">\n                      <label>Weight:</label>\n                      <span>{product.product?.Weight ? `${product.product.Weight} kg` : 'N/A'}</span>\n                    </div>\n                    <div className=\"detail-item\">\n                      <label>Dimensions:</label>\n                      <span>{product.product?.Dimensions || 'N/A'}</span>\n                    </div>\n                    <div className=\"detail-item\">\n                      <label>Material:</label>\n                      <span>{product.product?.Material || 'N/A'}</span>\n                    </div>\n                    <div className=\"detail-item\">\n                      <label>Color:</label>\n                      <span>{product.product?.Color || 'N/A'}</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"detail-section full-width\">\n                  <h3>Description</h3>\n                  <div className=\"description-content\">\n                    {product.product?.Description || 'No description available.'}\n                  </div>\n                </div>\n\n                <div className=\"detail-section\">\n                  <h3>Settings</h3>\n                  <div className=\"detail-group\">\n                    <div className=\"detail-item\">\n                      <label>Customizable:</label>\n                      <span className={`boolean-badge ${product.product?.IsCustomizable ? 'true' : 'false'}`}>\n                        {product.product?.IsCustomizable ? 'Yes' : 'No'}\n                      </span>\n                    </div>\n                    <div className=\"detail-item\">\n                      <label>Active:</label>\n                      <span className={`boolean-badge ${product.product?.IsActive ? 'true' : 'false'}`}>\n                        {product.product?.IsActive ? 'Yes' : 'No'}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"detail-section\">\n                  <h3>Timestamps</h3>\n                  <div className=\"detail-group\">\n                    <div className=\"detail-item\">\n                      <label>Created:</label>\n                      <span>{formatDate(product.product?.CreatedAt)}</span>\n                    </div>\n                    <div className=\"detail-item\">\n                      <label>Updated:</label>\n                      <span>{formatDate(product.product?.UpdatedAt)}</span>\n                    </div>\n                    <div className=\"detail-item\">\n                      <label>Created By:</label>\n                      <span>{product.product?.CreatedBy || 'N/A'}</span>\n                    </div>\n                    <div className=\"detail-item\">\n                      <label>Updated By:</label>\n                      <span>{product.product?.UpdatedBy || 'N/A'}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'files' && (\n            <div className=\"tab-content\">\n              <div className=\"files-section\">\n                <div className=\"file-category\">\n                  <h3>3D Models ({product.models?.length || 0})</h3>\n                  {product.models && product.models.length > 0 ? (\n                    <div className=\"files-grid\">\n                      {product.models.map((model, index) => (\n                        <div key={index} className=\"file-card\">\n                          <div className=\"file-icon\">🎯</div>\n                          <div className=\"file-info\">\n                            <div className=\"file-name\">{model.FileName}</div>\n                            <div className=\"file-details\">\n                              <span>{(model.FileSize / 1024 / 1024).toFixed(2)} MB</span>\n                              <span>{model.IsPrimary ? 'Primary' : 'Secondary'}</span>\n                            </div>\n                          </div>\n                          <div className=\"file-actions\">\n                            <button className=\"btn-icon\" title=\"Download\">⬇️</button>\n                            <button className=\"btn-icon\" title=\"Preview\">👁️</button>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : (\n                    <div className=\"no-files\">No 3D models uploaded</div>\n                  )}\n                </div>\n\n                <div className=\"file-category\">\n                  <h3>Images ({product.images?.length || 0})</h3>\n                  {product.images && product.images.length > 0 ? (\n                    <div className=\"images-grid\">\n                      {product.images.map((image, index) => (\n                        <div key={index} className=\"image-card\">\n                          <div className=\"image-preview\">\n                            <img \n                              src={`/api/files/images/${image.FileName}`} \n                              alt={image.AltText || 'Product image'}\n                              onError={(e) => {\n                                e.target.src = '/placeholder-image.png';\n                              }}\n                            />\n                          </div>\n                          <div className=\"image-info\">\n                            <div className=\"image-name\">{image.FileName}</div>\n                            <div className=\"image-details\">\n                              <span>{(image.FileSize / 1024).toFixed(1)} KB</span>\n                              <span>{image.IsPrimary ? 'Primary' : 'Secondary'}</span>\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : (\n                    <div className=\"no-files\">No images uploaded</div>\n                  )}\n                </div>\n\n                {/* 3D Preview Section */}\n                {product.models && product.models.length > 0 && (\n                  <div className=\"file-category\">\n                    <h3>3D Preview</h3>\n                    <div className=\"preview-container\">\n                      <ThreeJSPreview \n                        modelUrl={`/api/files/models/${product.models[0].FileName}`}\n                      />\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'components' && (\n            <div className=\"tab-content\">\n              <div className=\"components-section\">\n                <h3>Product Components ({product.components?.length || 0})</h3>\n                {product.components && product.components.length > 0 ? (\n                  <div className=\"components-table\">\n                    <table>\n                      <thead>\n                        <tr>\n                          <th>Component Name</th>\n                          <th>Quantity</th>\n                          <th>Unit Cost</th>\n                          <th>Total Cost</th>\n                          <th>Supplier</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {product.components.map((component, index) => (\n                          <tr key={index}>\n                            <td>{component.ComponentName}</td>\n                            <td>{component.Quantity}</td>\n                            <td>{formatCurrency(component.UnitCost)}</td>\n                            <td>{formatCurrency(component.Quantity * component.UnitCost)}</td>\n                            <td>{component.Supplier || 'N/A'}</td>\n                          </tr>\n                        ))}\n                      </tbody>\n                    </table>\n                  </div>\n                ) : (\n                  <div className=\"no-data\">No components defined for this product</div>\n                )}\n\n                <div className=\"colors-section\">\n                  <h3>Available Colors ({product.colors?.length || 0})</h3>\n                  {product.colors && product.colors.length > 0 ? (\n                    <div className=\"colors-grid\">\n                      {product.colors.map((color, index) => (\n                        <div key={index} className=\"color-card\">\n                          <div \n                            className=\"color-swatch\" \n                            style={{ backgroundColor: color.HexCode }}\n                          ></div>\n                          <div className=\"color-info\">\n                            <div className=\"color-name\">{color.ColorName}</div>\n                            <div className=\"color-code\">{color.HexCode}</div>\n                            {color.PriceModifier !== 0 && (\n                              <div className=\"price-modifier\">\n                                {color.PriceModifier > 0 ? '+' : ''}{formatCurrency(color.PriceModifier)}\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : (\n                    <div className=\"no-data\">No color options defined</div>\n                  )}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'audit' && (\n            <div className=\"tab-content\">\n              <div className=\"audit-section\">\n                <h3>Audit Trail</h3>\n                {product.auditTrail && product.auditTrail.length > 0 ? (\n                  <div className=\"audit-timeline\">\n                    {product.auditTrail.map((entry, index) => (\n                      <div key={index} className=\"audit-entry\">\n                        <div className=\"audit-timestamp\">\n                          {formatDate(entry.ChangeDate)}\n                        </div>\n                        <div className=\"audit-content\">\n                          <div className=\"audit-action\">{entry.Action}</div>\n                          <div className=\"audit-user\">by {entry.ChangedBy}</div>\n                          {entry.ChangeDetails && (\n                            <div className=\"audit-details\">{entry.ChangeDetails}</div>\n                          )}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"no-data\">No audit trail available</div>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n\n        <div className=\"modal-footer\">\n          <button className=\"btn btn-secondary\" onClick={onClose}>\n            Close\n          </button>\n          <button className=\"btn btn-primary\" onClick={onEdit}>\n            Edit Product\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductDetailsModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAO,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnC,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,OAAO;EAAEC,OAAO;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,mBAAA,EAAAC,eAAA;EAC5D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,SAAS,CAAC;EAErD,IAAI,CAACK,OAAO,EAAE,OAAO,IAAI;EAEzB,MAAM8B,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,mBAAmB,GAAIC,MAAM,IAAK;IACtC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,cAAc;QAAE,OAAO,SAAS;MACrC,KAAK,gBAAgB;QAAE,OAAO,SAAS;MACvC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE;EAAK,CAAC,EACvD;IAAEF,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE;EAAK,CAAC,EACnD;IAAEF,EAAE,EAAE,YAAY;IAAEC,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAK,CAAC,EACrD;IAAEF,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,CAClD;EAED,oBACErD,OAAA;IAAKsD,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5BvD,OAAA;MAAKsD,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBACpCvD,OAAA;QAAKsD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BvD,OAAA;UAAKsD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BvD,OAAA;YAAAuD,QAAA,EAAK,EAAAjD,gBAAA,GAAAJ,OAAO,CAACA,OAAO,cAAAI,gBAAA,uBAAfA,gBAAA,CAAiBkD,WAAW,KAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5D5D,OAAA;YACEsD,SAAS,EAAC,cAAc;YACxBlB,KAAK,EAAE;cAAEyB,eAAe,EAAEb,mBAAmB,EAAAzC,iBAAA,GAACL,OAAO,CAACA,OAAO,cAAAK,iBAAA,uBAAfA,iBAAA,CAAiBuD,MAAM;YAAE,CAAE;YAAAP,QAAA,GAAA/C,iBAAA,GAExEN,OAAO,CAACA,OAAO,cAAAM,iBAAA,uBAAfA,iBAAA,CAAiBsD;UAAM;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN5D,OAAA;UAAKsD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BvD,OAAA;YAAQsD,SAAS,EAAC,mBAAmB;YAACS,OAAO,EAAE3D,MAAO;YAAAmD,QAAA,EAAC;UAEvD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5D,OAAA;YAAQsD,SAAS,EAAC,aAAa;YAACS,OAAO,EAAE5D,OAAQ;YAAAoD,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5D,OAAA;QAAKsD,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxBL,IAAI,CAACc,GAAG,CAACC,GAAG,iBACXjE,OAAA;UAEEsD,SAAS,EAAE,cAAcxB,SAAS,KAAKmC,GAAG,CAACd,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;UAChEY,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAACkC,GAAG,CAACd,EAAE,CAAE;UAAAI,QAAA,gBAEpCvD,OAAA;YAAMsD,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEU,GAAG,CAACZ;UAAI;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAC3CK,GAAG,CAACb,KAAK;QAAA,GALLa,GAAG,CAACd,EAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAML,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN5D,OAAA;QAAKsD,SAAS,EAAC,YAAY;QAAAC,QAAA,GACxBzB,SAAS,KAAK,SAAS,iBACtB9B,OAAA;UAAKsD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BvD,OAAA;YAAKsD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvD,OAAA;cAAKsD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BvD,OAAA;gBAAAuD,QAAA,EAAI;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1B5D,OAAA;gBAAKsD,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BvD,OAAA;kBAAKsD,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BvD,OAAA;oBAAAuD,QAAA,EAAO;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5B5D,OAAA;oBAAAuD,QAAA,EAAO,EAAA9C,iBAAA,GAAAP,OAAO,CAACA,OAAO,cAAAO,iBAAA,uBAAfA,iBAAA,CAAiByD,WAAW,KAAI;kBAAK;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACN5D,OAAA;kBAAKsD,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BvD,OAAA;oBAAAuD,QAAA,EAAO;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5B5D,OAAA;oBAAAuD,QAAA,EAAO,EAAA7C,iBAAA,GAAAR,OAAO,CAACA,OAAO,cAAAQ,iBAAA,uBAAfA,iBAAA,CAAiB8C,WAAW,KAAI;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACN5D,OAAA;kBAAKsD,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BvD,OAAA;oBAAAuD,QAAA,EAAO;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxB5D,OAAA;oBAAAuD,QAAA,EAAO,EAAA5C,iBAAA,GAAAT,OAAO,CAACA,OAAO,cAAAS,iBAAA,uBAAfA,iBAAA,CAAiBwD,YAAY,KAAI;kBAAK;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACN5D,OAAA;kBAAKsD,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BvD,OAAA;oBAAAuD,QAAA,EAAO;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC1B5D,OAAA;oBAAAuD,QAAA,EAAO,CAAA3C,iBAAA,GAAAV,OAAO,CAACA,OAAO,cAAAU,iBAAA,eAAfA,iBAAA,CAAiBwD,SAAS,GAAGpC,cAAc,CAAC9B,OAAO,CAACA,OAAO,CAACkE,SAAS,CAAC,GAAG;kBAAK;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5D,OAAA;cAAKsD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BvD,OAAA;gBAAAuD,QAAA,EAAI;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5B5D,OAAA;gBAAKsD,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BvD,OAAA;kBAAKsD,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BvD,OAAA;oBAAAuD,QAAA,EAAO;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtB5D,OAAA;oBAAAuD,QAAA,EAAO,CAAA1C,iBAAA,GAAAX,OAAO,CAACA,OAAO,cAAAW,iBAAA,eAAfA,iBAAA,CAAiBwD,MAAM,GAAG,GAAGnE,OAAO,CAACA,OAAO,CAACmE,MAAM,KAAK,GAAG;kBAAK;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eACN5D,OAAA;kBAAKsD,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BvD,OAAA;oBAAAuD,QAAA,EAAO;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC1B5D,OAAA;oBAAAuD,QAAA,EAAO,EAAAzC,iBAAA,GAAAZ,OAAO,CAACA,OAAO,cAAAY,iBAAA,uBAAfA,iBAAA,CAAiBwD,UAAU,KAAI;kBAAK;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACN5D,OAAA;kBAAKsD,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BvD,OAAA;oBAAAuD,QAAA,EAAO;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxB5D,OAAA;oBAAAuD,QAAA,EAAO,EAAAxC,iBAAA,GAAAb,OAAO,CAACA,OAAO,cAAAa,iBAAA,uBAAfA,iBAAA,CAAiBwD,QAAQ,KAAI;kBAAK;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACN5D,OAAA;kBAAKsD,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BvD,OAAA;oBAAAuD,QAAA,EAAO;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACrB5D,OAAA;oBAAAuD,QAAA,EAAO,EAAAvC,iBAAA,GAAAd,OAAO,CAACA,OAAO,cAAAc,iBAAA,uBAAfA,iBAAA,CAAiBwD,KAAK,KAAI;kBAAK;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5D,OAAA;cAAKsD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCvD,OAAA;gBAAAuD,QAAA,EAAI;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpB5D,OAAA;gBAAKsD,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EACjC,EAAAtC,kBAAA,GAAAf,OAAO,CAACA,OAAO,cAAAe,kBAAA,uBAAfA,kBAAA,CAAiBwD,WAAW,KAAI;cAA2B;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5D,OAAA;cAAKsD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BvD,OAAA;gBAAAuD,QAAA,EAAI;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjB5D,OAAA;gBAAKsD,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BvD,OAAA;kBAAKsD,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BvD,OAAA;oBAAAuD,QAAA,EAAO;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5B5D,OAAA;oBAAMsD,SAAS,EAAE,iBAAiB,CAAApC,kBAAA,GAAAhB,OAAO,CAACA,OAAO,cAAAgB,kBAAA,eAAfA,kBAAA,CAAiBwD,cAAc,GAAG,MAAM,GAAG,OAAO,EAAG;oBAAAnB,QAAA,EACpF,CAAApC,kBAAA,GAAAjB,OAAO,CAACA,OAAO,cAAAiB,kBAAA,eAAfA,kBAAA,CAAiBuD,cAAc,GAAG,KAAK,GAAG;kBAAI;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN5D,OAAA;kBAAKsD,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BvD,OAAA;oBAAAuD,QAAA,EAAO;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtB5D,OAAA;oBAAMsD,SAAS,EAAE,iBAAiB,CAAAlC,kBAAA,GAAAlB,OAAO,CAACA,OAAO,cAAAkB,kBAAA,eAAfA,kBAAA,CAAiBuD,QAAQ,GAAG,MAAM,GAAG,OAAO,EAAG;oBAAApB,QAAA,EAC9E,CAAAlC,kBAAA,GAAAnB,OAAO,CAACA,OAAO,cAAAmB,kBAAA,eAAfA,kBAAA,CAAiBsD,QAAQ,GAAG,KAAK,GAAG;kBAAI;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5D,OAAA;cAAKsD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BvD,OAAA;gBAAAuD,QAAA,EAAI;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnB5D,OAAA;gBAAKsD,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BvD,OAAA;kBAAKsD,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BvD,OAAA;oBAAAuD,QAAA,EAAO;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvB5D,OAAA;oBAAAuD,QAAA,EAAOhB,UAAU,EAAAjB,kBAAA,GAACpB,OAAO,CAACA,OAAO,cAAAoB,kBAAA,uBAAfA,kBAAA,CAAiBsD,SAAS;kBAAC;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACN5D,OAAA;kBAAKsD,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BvD,OAAA;oBAAAuD,QAAA,EAAO;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACvB5D,OAAA;oBAAAuD,QAAA,EAAOhB,UAAU,EAAAhB,kBAAA,GAACrB,OAAO,CAACA,OAAO,cAAAqB,kBAAA,uBAAfA,kBAAA,CAAiBsD,SAAS;kBAAC;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACN5D,OAAA;kBAAKsD,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BvD,OAAA;oBAAAuD,QAAA,EAAO;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC1B5D,OAAA;oBAAAuD,QAAA,EAAO,EAAA/B,kBAAA,GAAAtB,OAAO,CAACA,OAAO,cAAAsB,kBAAA,uBAAfA,kBAAA,CAAiBsD,SAAS,KAAI;kBAAK;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACN5D,OAAA;kBAAKsD,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BvD,OAAA;oBAAAuD,QAAA,EAAO;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC1B5D,OAAA;oBAAAuD,QAAA,EAAO,EAAA9B,kBAAA,GAAAvB,OAAO,CAACA,OAAO,cAAAuB,kBAAA,uBAAfA,kBAAA,CAAiBsD,SAAS,KAAI;kBAAK;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA9B,SAAS,KAAK,OAAO,iBACpB9B,OAAA;UAAKsD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BvD,OAAA;YAAKsD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BvD,OAAA;cAAKsD,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BvD,OAAA;gBAAAuD,QAAA,GAAI,aAAW,EAAC,EAAA7B,eAAA,GAAAxB,OAAO,CAAC8E,MAAM,cAAAtD,eAAA,uBAAdA,eAAA,CAAgBuD,MAAM,KAAI,CAAC,EAAC,GAAC;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACjD1D,OAAO,CAAC8E,MAAM,IAAI9E,OAAO,CAAC8E,MAAM,CAACC,MAAM,GAAG,CAAC,gBAC1CjF,OAAA;gBAAKsD,SAAS,EAAC,YAAY;gBAAAC,QAAA,EACxBrD,OAAO,CAAC8E,MAAM,CAAChB,GAAG,CAAC,CAACkB,KAAK,EAAEC,KAAK,kBAC/BnF,OAAA;kBAAiBsD,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACpCvD,OAAA;oBAAKsD,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAC;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnC5D,OAAA;oBAAKsD,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBvD,OAAA;sBAAKsD,SAAS,EAAC,WAAW;sBAAAC,QAAA,EAAE2B,KAAK,CAACE;oBAAQ;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjD5D,OAAA;sBAAKsD,SAAS,EAAC,cAAc;sBAAAC,QAAA,gBAC3BvD,OAAA;wBAAAuD,QAAA,GAAO,CAAC2B,KAAK,CAACG,QAAQ,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;sBAAA;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC3D5D,OAAA;wBAAAuD,QAAA,EAAO2B,KAAK,CAACK,SAAS,GAAG,SAAS,GAAG;sBAAW;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN5D,OAAA;oBAAKsD,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BvD,OAAA;sBAAQsD,SAAS,EAAC,UAAU;sBAACkC,KAAK,EAAC,UAAU;sBAAAjC,QAAA,EAAC;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACzD5D,OAAA;sBAAQsD,SAAS,EAAC,UAAU;sBAACkC,KAAK,EAAC,SAAS;sBAAAjC,QAAA,EAAC;oBAAG;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC;gBAAA,GAZEuB,KAAK;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAaV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAEN5D,OAAA;gBAAKsD,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACrD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN5D,OAAA;cAAKsD,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BvD,OAAA;gBAAAuD,QAAA,GAAI,UAAQ,EAAC,EAAA5B,eAAA,GAAAzB,OAAO,CAACuF,MAAM,cAAA9D,eAAA,uBAAdA,eAAA,CAAgBsD,MAAM,KAAI,CAAC,EAAC,GAAC;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAC9C1D,OAAO,CAACuF,MAAM,IAAIvF,OAAO,CAACuF,MAAM,CAACR,MAAM,GAAG,CAAC,gBAC1CjF,OAAA;gBAAKsD,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACzBrD,OAAO,CAACuF,MAAM,CAACzB,GAAG,CAAC,CAAC0B,KAAK,EAAEP,KAAK,kBAC/BnF,OAAA;kBAAiBsD,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACrCvD,OAAA;oBAAKsD,SAAS,EAAC,eAAe;oBAAAC,QAAA,eAC5BvD,OAAA;sBACE2F,GAAG,EAAE,qBAAqBD,KAAK,CAACN,QAAQ,EAAG;sBAC3CQ,GAAG,EAAEF,KAAK,CAACG,OAAO,IAAI,eAAgB;sBACtCC,OAAO,EAAGC,CAAC,IAAK;wBACdA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,wBAAwB;sBACzC;oBAAE;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACN5D,OAAA;oBAAKsD,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzBvD,OAAA;sBAAKsD,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAEmC,KAAK,CAACN;oBAAQ;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAClD5D,OAAA;sBAAKsD,SAAS,EAAC,eAAe;sBAAAC,QAAA,gBAC5BvD,OAAA;wBAAAuD,QAAA,GAAO,CAACmC,KAAK,CAACL,QAAQ,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;sBAAA;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACpD5D,OAAA;wBAAAuD,QAAA,EAAOmC,KAAK,CAACH,SAAS,GAAG,SAAS,GAAG;sBAAW;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAhBEuB,KAAK;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiBV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAEN5D,OAAA;gBAAKsD,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAClD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGL1D,OAAO,CAAC8E,MAAM,IAAI9E,OAAO,CAAC8E,MAAM,CAACC,MAAM,GAAG,CAAC,iBAC1CjF,OAAA;cAAKsD,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BvD,OAAA;gBAAAuD,QAAA,EAAI;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnB5D,OAAA;gBAAKsD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,eAChCvD,OAAA,CAACF,cAAc;kBACbmG,QAAQ,EAAE,qBAAqB/F,OAAO,CAAC8E,MAAM,CAAC,CAAC,CAAC,CAACI,QAAQ;gBAAG;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA9B,SAAS,KAAK,YAAY,iBACzB9B,OAAA;UAAKsD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BvD,OAAA;YAAKsD,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCvD,OAAA;cAAAuD,QAAA,GAAI,sBAAoB,EAAC,EAAA3B,mBAAA,GAAA1B,OAAO,CAACgG,UAAU,cAAAtE,mBAAA,uBAAlBA,mBAAA,CAAoBqD,MAAM,KAAI,CAAC,EAAC,GAAC;YAAA;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC9D1D,OAAO,CAACgG,UAAU,IAAIhG,OAAO,CAACgG,UAAU,CAACjB,MAAM,GAAG,CAAC,gBAClDjF,OAAA;cAAKsD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/BvD,OAAA;gBAAAuD,QAAA,gBACEvD,OAAA;kBAAAuD,QAAA,eACEvD,OAAA;oBAAAuD,QAAA,gBACEvD,OAAA;sBAAAuD,QAAA,EAAI;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvB5D,OAAA;sBAAAuD,QAAA,EAAI;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjB5D,OAAA;sBAAAuD,QAAA,EAAI;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClB5D,OAAA;sBAAAuD,QAAA,EAAI;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnB5D,OAAA;sBAAAuD,QAAA,EAAI;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACR5D,OAAA;kBAAAuD,QAAA,EACGrD,OAAO,CAACgG,UAAU,CAAClC,GAAG,CAAC,CAACmC,SAAS,EAAEhB,KAAK,kBACvCnF,OAAA;oBAAAuD,QAAA,gBACEvD,OAAA;sBAAAuD,QAAA,EAAK4C,SAAS,CAACC;oBAAa;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAClC5D,OAAA;sBAAAuD,QAAA,EAAK4C,SAAS,CAACE;oBAAQ;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7B5D,OAAA;sBAAAuD,QAAA,EAAKvB,cAAc,CAACmE,SAAS,CAACG,QAAQ;oBAAC;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7C5D,OAAA;sBAAAuD,QAAA,EAAKvB,cAAc,CAACmE,SAAS,CAACE,QAAQ,GAAGF,SAAS,CAACG,QAAQ;oBAAC;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAClE5D,OAAA;sBAAAuD,QAAA,EAAK4C,SAAS,CAACI,QAAQ,IAAI;oBAAK;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA,GAL/BuB,KAAK;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMV,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,gBAEN5D,OAAA;cAAKsD,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAsC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACrE,eAED5D,OAAA;cAAKsD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BvD,OAAA;gBAAAuD,QAAA,GAAI,oBAAkB,EAAC,EAAA1B,eAAA,GAAA3B,OAAO,CAACsG,MAAM,cAAA3E,eAAA,uBAAdA,eAAA,CAAgBoD,MAAM,KAAI,CAAC,EAAC,GAAC;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACxD1D,OAAO,CAACsG,MAAM,IAAItG,OAAO,CAACsG,MAAM,CAACvB,MAAM,GAAG,CAAC,gBAC1CjF,OAAA;gBAAKsD,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACzBrD,OAAO,CAACsG,MAAM,CAACxC,GAAG,CAAC,CAACyC,KAAK,EAAEtB,KAAK,kBAC/BnF,OAAA;kBAAiBsD,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACrCvD,OAAA;oBACEsD,SAAS,EAAC,cAAc;oBACxBlB,KAAK,EAAE;sBAAEyB,eAAe,EAAE4C,KAAK,CAACC;oBAAQ;kBAAE;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACP5D,OAAA;oBAAKsD,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzBvD,OAAA;sBAAKsD,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAEkD,KAAK,CAACE;oBAAS;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnD5D,OAAA;sBAAKsD,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAEkD,KAAK,CAACC;oBAAO;sBAAAjD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAChD6C,KAAK,CAACG,aAAa,KAAK,CAAC,iBACxB5G,OAAA;sBAAKsD,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,GAC5BkD,KAAK,CAACG,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE5E,cAAc,CAACyE,KAAK,CAACG,aAAa,CAAC;oBAAA;sBAAAnD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrE,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,GAbEuB,KAAK;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAEN5D,OAAA;gBAAKsD,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACvD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA9B,SAAS,KAAK,OAAO,iBACpB9B,OAAA;UAAKsD,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BvD,OAAA;YAAKsD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BvD,OAAA;cAAAuD,QAAA,EAAI;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACnB1D,OAAO,CAAC2G,UAAU,IAAI3G,OAAO,CAAC2G,UAAU,CAAC5B,MAAM,GAAG,CAAC,gBAClDjF,OAAA;cAAKsD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5BrD,OAAO,CAAC2G,UAAU,CAAC7C,GAAG,CAAC,CAAC8C,KAAK,EAAE3B,KAAK,kBACnCnF,OAAA;gBAAiBsD,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACtCvD,OAAA;kBAAKsD,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAC7BhB,UAAU,CAACuE,KAAK,CAACC,UAAU;gBAAC;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACN5D,OAAA;kBAAKsD,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BvD,OAAA;oBAAKsD,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAEuD,KAAK,CAACE;kBAAM;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClD5D,OAAA;oBAAKsD,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAC,KAAG,EAACuD,KAAK,CAACG,SAAS;kBAAA;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EACrDkD,KAAK,CAACI,aAAa,iBAClBlH,OAAA;oBAAKsD,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAEuD,KAAK,CAACI;kBAAa;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAC1D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GAVEuB,KAAK;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAEN5D,OAAA;cAAKsD,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACvD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN5D,OAAA;QAAKsD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BvD,OAAA;UAAQsD,SAAS,EAAC,mBAAmB;UAACS,OAAO,EAAE5D,OAAQ;UAAAoD,QAAA,EAAC;QAExD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5D,OAAA;UAAQsD,SAAS,EAAC,iBAAiB;UAACS,OAAO,EAAE3D,MAAO;UAAAmD,QAAA,EAAC;QAErD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvD,EAAA,CA9VIJ,mBAAmB;AAAAkH,EAAA,GAAnBlH,mBAAmB;AAgWzB,eAAeA,mBAAmB;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}