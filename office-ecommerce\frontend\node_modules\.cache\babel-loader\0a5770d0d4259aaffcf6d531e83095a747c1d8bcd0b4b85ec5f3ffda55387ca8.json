{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { AudioListener, AudioLoader } from 'three';\nimport { useThree, useLoader } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\nconst PositionalAudio = /*#__PURE__*/React.forwardRef(({\n  url,\n  distance = 1,\n  loop = true,\n  autoplay,\n  ...props\n}, ref) => {\n  const sound = React.useRef();\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const [listener] = React.useState(() => new AudioListener());\n  const buffer = useLoader(AudioLoader, url);\n  React.useEffect(() => {\n    const _sound = sound.current;\n    if (_sound) {\n      _sound.setBuffer(buffer);\n      _sound.setRefDistance(distance);\n      _sound.setLoop(loop);\n      if (autoplay && !_sound.isPlaying) _sound.play();\n    }\n  }, [buffer, camera, distance, loop]);\n  React.useEffect(() => {\n    const _sound = sound.current;\n    camera.add(listener);\n    return () => {\n      camera.remove(listener);\n      if (_sound) {\n        if (_sound.isPlaying) _sound.stop();\n        if (_sound.source && _sound.source._connected) _sound.disconnect();\n      }\n    };\n  }, []);\n  return /*#__PURE__*/React.createElement(\"positionalAudio\", _extends({\n    ref: mergeRefs([sound, ref]),\n    args: [listener]\n  }, props));\n});\nexport { PositionalAudio };", "map": {"version": 3, "names": ["_extends", "React", "AudioListener", "AudioLoader", "useThree", "useLoader", "mergeRefs", "PositionalAudio", "forwardRef", "url", "distance", "loop", "autoplay", "props", "ref", "sound", "useRef", "camera", "listener", "useState", "buffer", "useEffect", "_sound", "current", "<PERSON><PERSON><PERSON><PERSON>", "setRefDistance", "setLoop", "isPlaying", "play", "add", "remove", "stop", "source", "_connected", "disconnect", "createElement", "args"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/PositionalAudio.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { AudioListener, AudioLoader } from 'three';\nimport { useThree, useLoader } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\n\nconst PositionalAudio = /*#__PURE__*/React.forwardRef(({\n  url,\n  distance = 1,\n  loop = true,\n  autoplay,\n  ...props\n}, ref) => {\n  const sound = React.useRef();\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const [listener] = React.useState(() => new AudioListener());\n  const buffer = useLoader(AudioLoader, url);\n  React.useEffect(() => {\n    const _sound = sound.current;\n\n    if (_sound) {\n      _sound.setBuffer(buffer);\n\n      _sound.setRefDistance(distance);\n\n      _sound.setLoop(loop);\n\n      if (autoplay && !_sound.isPlaying) _sound.play();\n    }\n  }, [buffer, camera, distance, loop]);\n  React.useEffect(() => {\n    const _sound = sound.current;\n    camera.add(listener);\n    return () => {\n      camera.remove(listener);\n\n      if (_sound) {\n        if (_sound.isPlaying) _sound.stop();\n        if (_sound.source && _sound.source._connected) _sound.disconnect();\n      }\n    };\n  }, []);\n  return /*#__PURE__*/React.createElement(\"positionalAudio\", _extends({\n    ref: mergeRefs([sound, ref]),\n    args: [listener]\n  }, props));\n});\n\nexport { PositionalAudio };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,WAAW,QAAQ,OAAO;AAClD,SAASC,QAAQ,EAAEC,SAAS,QAAQ,oBAAoB;AACxD,OAAOC,SAAS,MAAM,kBAAkB;AAExC,MAAMC,eAAe,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAC;EACrDC,GAAG;EACHC,QAAQ,GAAG,CAAC;EACZC,IAAI,GAAG,IAAI;EACXC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,KAAK,GAAGd,KAAK,CAACe,MAAM,CAAC,CAAC;EAC5B,MAAMC,MAAM,GAAGb,QAAQ,CAAC,CAAC;IACvBa;EACF,CAAC,KAAKA,MAAM,CAAC;EACb,MAAM,CAACC,QAAQ,CAAC,GAAGjB,KAAK,CAACkB,QAAQ,CAAC,MAAM,IAAIjB,aAAa,CAAC,CAAC,CAAC;EAC5D,MAAMkB,MAAM,GAAGf,SAAS,CAACF,WAAW,EAAEM,GAAG,CAAC;EAC1CR,KAAK,CAACoB,SAAS,CAAC,MAAM;IACpB,MAAMC,MAAM,GAAGP,KAAK,CAACQ,OAAO;IAE5B,IAAID,MAAM,EAAE;MACVA,MAAM,CAACE,SAAS,CAACJ,MAAM,CAAC;MAExBE,MAAM,CAACG,cAAc,CAACf,QAAQ,CAAC;MAE/BY,MAAM,CAACI,OAAO,CAACf,IAAI,CAAC;MAEpB,IAAIC,QAAQ,IAAI,CAACU,MAAM,CAACK,SAAS,EAAEL,MAAM,CAACM,IAAI,CAAC,CAAC;IAClD;EACF,CAAC,EAAE,CAACR,MAAM,EAAEH,MAAM,EAAEP,QAAQ,EAAEC,IAAI,CAAC,CAAC;EACpCV,KAAK,CAACoB,SAAS,CAAC,MAAM;IACpB,MAAMC,MAAM,GAAGP,KAAK,CAACQ,OAAO;IAC5BN,MAAM,CAACY,GAAG,CAACX,QAAQ,CAAC;IACpB,OAAO,MAAM;MACXD,MAAM,CAACa,MAAM,CAACZ,QAAQ,CAAC;MAEvB,IAAII,MAAM,EAAE;QACV,IAAIA,MAAM,CAACK,SAAS,EAAEL,MAAM,CAACS,IAAI,CAAC,CAAC;QACnC,IAAIT,MAAM,CAACU,MAAM,IAAIV,MAAM,CAACU,MAAM,CAACC,UAAU,EAAEX,MAAM,CAACY,UAAU,CAAC,CAAC;MACpE;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAajC,KAAK,CAACkC,aAAa,CAAC,iBAAiB,EAAEnC,QAAQ,CAAC;IAClEc,GAAG,EAAER,SAAS,CAAC,CAACS,KAAK,EAAED,GAAG,CAAC,CAAC;IAC5BsB,IAAI,EAAE,CAAClB,QAAQ;EACjB,CAAC,EAAEL,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF,SAASN,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}