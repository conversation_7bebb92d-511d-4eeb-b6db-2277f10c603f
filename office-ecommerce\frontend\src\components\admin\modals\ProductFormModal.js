import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import FileUploadZone from '../components/FileUploadZone';
import ThreeJSPreview from '../components/ThreeJSPreview';
import { productsApi } from '../../../services/api';
import websocketService from '../../../services/websocketService';
import './ProductFormModal.css';

const ProductFormModal = ({ product, categories, onSave, onClose }) => {
  const [formData, setFormData] = useState({
    ProductCode: '',
    ProductName: '',
    CategoryID: '',
    Description: '',
    BasePrice: '',
    Weight: '',
    Dimensions: '',
    Material: '',
    Color: '',
    Status: 'Draft',
    Tags: '',
    IsCustomizable: false,
    IsActive: true
  });

  const [uploadedFiles, setUploadedFiles] = useState({
    models: [],
    images: []
  });

  const [previewModel, setPreviewModel] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  useEffect(() => {
    if (product) {
      setFormData({
        ProductCode: product.ProductCode || '',
        ProductName: product.ProductName || '',
        CategoryID: product.CategoryID || '',
        Description: product.Description || '',
        BasePrice: product.BasePrice || '',
        Weight: product.Weight || '',
        Dimensions: product.Dimensions || '',
        Material: product.Material || '',
        Color: product.Color || '',
        Status: product.Status || 'Draft',
        Tags: product.Tags || '',
        IsCustomizable: product.IsCustomizable || false,
        IsActive: product.IsActive !== undefined ? product.IsActive : true
      });
    }
  }, [product]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleFileUpload = (files, fileType) => {
    setUploadedFiles(prev => ({
      ...prev,
      [fileType]: [...prev[fileType], ...files]
    }));

    // Set preview for first 3D model
    if (fileType === 'models' && files.length > 0 && !previewModel) {
      setPreviewModel(files[0]);
    }
  };

  const handleRemoveFile = (index, fileType) => {
    setUploadedFiles(prev => ({
      ...prev,
      [fileType]: prev[fileType].filter((_, i) => i !== index)
    }));

    // Update preview if removing the current preview model
    if (fileType === 'models' && previewModel && uploadedFiles.models[index] === previewModel) {
      const remainingModels = uploadedFiles.models.filter((_, i) => i !== index);
      setPreviewModel(remainingModels.length > 0 ? remainingModels[0] : null);
    }
  };

  const validateForm = () => {
    const errors = [];

    if (!formData.ProductCode.trim()) errors.push('Product code is required');
    if (!formData.ProductName.trim()) errors.push('Product name is required');
    if (!formData.CategoryID) errors.push('Category is required');
    if (!formData.BasePrice || parseFloat(formData.BasePrice) <= 0) errors.push('Valid base price is required');

    return errors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const errors = validateForm();
    if (errors.length > 0) {
      toast.error(errors.join(', '));
      return;
    }

    setLoading(true);

    try {
      // Create or update product
      const productData = {
        ...formData,
        BasePrice: parseFloat(formData.BasePrice),
        Weight: formData.Weight ? parseFloat(formData.Weight) : null,
        Tags: formData.Tags ? JSON.stringify(formData.Tags.split(',').map(tag => tag.trim())) : null
      };

      if (product) {
        productData.ProductID = product.ProductID;
      }

      const response = await productsApi.createOrUpdateProduct(productData);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to save product');
      }

      const savedProduct = response.data;

      // Upload files if any
      if (uploadedFiles.models.length > 0) {
        await uploadFiles(savedProduct.ProductID, uploadedFiles.models, 'models');
      }

      if (uploadedFiles.images.length > 0) {
        await uploadFiles(savedProduct.ProductID, uploadedFiles.images, 'images');
      }

      onSave(savedProduct);
    } catch (error) {
      console.error('Error saving product:', error);
      toast.error(error.message || 'Failed to save product');
    } finally {
      setLoading(false);
    }
  };

  const uploadFiles = async (productId, files, fileType) => {
    const formData = new FormData();

    try {
      if (fileType === 'models') {
        files.forEach(file => {
          formData.append('model', file);
        });
        const response = await productsApi.uploadModel(productId, formData);

        // Emit WebSocket event for file upload
        if (response.success) {
          websocketService.notifyProductFileUploaded(productId, {
            fileType: 'model',
            fileName: files[0].name,
            fileSize: files[0].size
          });
        }
      } else if (fileType === 'images') {
        files.forEach(file => {
          formData.append('images', file);
        });
        const response = await productsApi.uploadImages(productId, formData);

        // Emit WebSocket event for file upload
        if (response.success) {
          websocketService.notifyProductFileUploaded(productId, {
            fileType: 'images',
            fileCount: files.length,
            totalSize: files.reduce((sum, file) => sum + file.size, 0)
          });
        }
      }
    } catch (error) {
      console.error(`Error uploading ${fileType}:`, error);
      throw error;
    }
  };

  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: '📝' },
    { id: 'files', label: 'Files & Media', icon: '📁' },
    { id: 'preview', label: '3D Preview', icon: '🎯' }
  ];

  return (
    <div className="modal-overlay">
      <div className="product-form-modal">
        <div className="modal-header">
          <h2>{product ? 'Edit Product' : 'Add New Product'}</h2>
          <button className="modal-close" onClick={onClose}>×</button>
        </div>

        <div className="modal-tabs">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => setActiveTab(tab.id)}
            >
              <span className="tab-icon">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>

        <form onSubmit={handleSubmit} className="modal-body">
          {activeTab === 'basic' && (
            <div className="tab-content">
              <div className="form-grid">
                <div className="form-group">
                  <label htmlFor="ProductCode">Product Code *</label>
                  <input
                    type="text"
                    id="ProductCode"
                    name="ProductCode"
                    value={formData.ProductCode}
                    onChange={handleInputChange}
                    className="form-input"
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="ProductName">Product Name *</label>
                  <input
                    type="text"
                    id="ProductName"
                    name="ProductName"
                    value={formData.ProductName}
                    onChange={handleInputChange}
                    className="form-input"
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="CategoryID">Category *</label>
                  <select
                    id="CategoryID"
                    name="CategoryID"
                    value={formData.CategoryID}
                    onChange={handleInputChange}
                    className="form-select"
                    required
                  >
                    <option value="">Select Category</option>
                    {categories.map(category => (
                      <option key={category.CategoryID} value={category.CategoryID}>
                        {category.CategoryName}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label htmlFor="BasePrice">Base Price (PHP) *</label>
                  <input
                    type="number"
                    id="BasePrice"
                    name="BasePrice"
                    value={formData.BasePrice}
                    onChange={handleInputChange}
                    className="form-input"
                    min="0"
                    step="0.01"
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="Weight">Weight (kg)</label>
                  <input
                    type="number"
                    id="Weight"
                    name="Weight"
                    value={formData.Weight}
                    onChange={handleInputChange}
                    className="form-input"
                    min="0"
                    step="0.01"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="Dimensions">Dimensions</label>
                  <input
                    type="text"
                    id="Dimensions"
                    name="Dimensions"
                    value={formData.Dimensions}
                    onChange={handleInputChange}
                    className="form-input"
                    placeholder="e.g., 120cm x 60cm x 75cm"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="Material">Material</label>
                  <input
                    type="text"
                    id="Material"
                    name="Material"
                    value={formData.Material}
                    onChange={handleInputChange}
                    className="form-input"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="Color">Color</label>
                  <input
                    type="text"
                    id="Color"
                    name="Color"
                    value={formData.Color}
                    onChange={handleInputChange}
                    className="form-input"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="Status">Status</label>
                  <select
                    id="Status"
                    name="Status"
                    value={formData.Status}
                    onChange={handleInputChange}
                    className="form-select"
                  >
                    <option value="Draft">Draft</option>
                    <option value="Active">Active</option>
                    <option value="Inactive">Inactive</option>
                    <option value="Pending Review">Pending Review</option>
                  </select>
                </div>

                <div className="form-group">
                  <label htmlFor="Tags">Tags (comma-separated)</label>
                  <input
                    type="text"
                    id="Tags"
                    name="Tags"
                    value={formData.Tags}
                    onChange={handleInputChange}
                    className="form-input"
                    placeholder="e.g., office, chair, ergonomic"
                  />
                </div>
              </div>

              <div className="form-group full-width">
                <label htmlFor="Description">Description</label>
                <textarea
                  id="Description"
                  name="Description"
                  value={formData.Description}
                  onChange={handleInputChange}
                  className="form-textarea"
                  rows="4"
                />
              </div>

              <div className="form-checkboxes">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    name="IsCustomizable"
                    checked={formData.IsCustomizable}
                    onChange={handleInputChange}
                  />
                  <span className="checkbox-text">Is Customizable</span>
                </label>

                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    name="IsActive"
                    checked={formData.IsActive}
                    onChange={handleInputChange}
                  />
                  <span className="checkbox-text">Is Active</span>
                </label>
              </div>
            </div>
          )}

          {activeTab === 'files' && (
            <div className="tab-content">
              <div className="file-upload-section">
                <h3>3D Models</h3>
                <FileUploadZone
                  accept=".glb,.gltf"
                  multiple={false}
                  onFilesSelected={(files) => handleFileUpload(files, 'models')}
                  fileType="3D Model"
                />
                {uploadedFiles.models.length > 0 && (
                  <div className="uploaded-files">
                    {uploadedFiles.models.map((file, index) => (
                      <div key={index} className="uploaded-file">
                        <span>{file.name}</span>
                        <button
                          type="button"
                          onClick={() => handleRemoveFile(index, 'models')}
                          className="remove-file"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className="file-upload-section">
                <h3>Product Images</h3>
                <FileUploadZone
                  accept=".jpg,.jpeg,.png,.webp"
                  multiple={true}
                  onFilesSelected={(files) => handleFileUpload(files, 'images')}
                  fileType="Image"
                />
                {uploadedFiles.images.length > 0 && (
                  <div className="uploaded-files">
                    {uploadedFiles.images.map((file, index) => (
                      <div key={index} className="uploaded-file">
                        <span>{file.name}</span>
                        <button
                          type="button"
                          onClick={() => handleRemoveFile(index, 'images')}
                          className="remove-file"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'preview' && (
            <div className="tab-content">
              <div className="preview-section">
                {previewModel ? (
                  <ThreeJSPreview modelFile={previewModel} />
                ) : (
                  <div className="no-preview">
                    <p>Upload a 3D model to see preview</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </form>

        <div className="modal-footer">
          <button
            type="button"
            className="btn btn-secondary"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? 'Saving...' : (product ? 'Update Product' : 'Create Product')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProductFormModal;
