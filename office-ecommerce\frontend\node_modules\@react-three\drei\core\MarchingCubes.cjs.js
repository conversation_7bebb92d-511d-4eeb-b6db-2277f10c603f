"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("three"),t=require("react"),n=require("react-merge-refs"),u=require("three-stdlib"),a=require("@react-three/fiber");function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function o(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var c=l(e),s=o(r),f=o(t),i=l(n);const d=f.createContext(null),b=f.forwardRef((({resolution:e=28,maxPolyCount:r=1e4,enableUvs:t=!1,enableColors:n=!1,children:l,...o},s)=>{const b=f.useRef(null),m=f.useMemo((()=>new u.MarchingCubes(e,null,t,n,r)),[e,r,t,n]),g=f.useMemo((()=>({getParent:()=>b})),[]);return a.useFrame((()=>{m.reset()}),-1),f.createElement(f.Fragment,null,f.createElement("primitive",c.default({object:m,ref:i.default([b,s])},o),f.createElement(d.Provider,{value:g},l)))})),m=f.forwardRef((({strength:e=.5,subtract:r=12,color:t,...n},u)=>{const{getParent:l}=f.useContext(d),o=f.useMemo((()=>l()),[l]),b=f.useRef(),m=new s.Vector3;return a.useFrame((n=>{o.current&&b.current&&(b.current.getWorldPosition(m),o.current.addBall(.5+.5*m.x,.5+.5*m.y,.5+.5*m.z,e,r,t))})),f.createElement("group",c.default({ref:i.default([u,b])},n))})),g=f.forwardRef((({planeType:e="x",strength:r=.5,subtract:t=12,...n},u)=>{const{getParent:l}=f.useContext(d),o=f.useMemo((()=>l()),[l]),s=f.useRef(),b=f.useMemo((()=>"x"===e?"addPlaneX":"y"===e?"addPlaneY":"addPlaneZ"),[e]);return a.useFrame((()=>{o.current&&s.current&&o.current[b](r,t)})),f.createElement("group",c.default({ref:i.default([u,s])},n))}));exports.MarchingCube=m,exports.MarchingCubes=b,exports.MarchingPlane=g;
