"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../core/Billboard.cjs.js"),r=require("../core/ScreenSpace.cjs.js"),s=require("../core/QuadraticBezierLine.cjs.js"),o=require("../core/CubicBezierLine.cjs.js"),t=require("../core/CatmullRomLine.cjs.js"),i=require("../core/Line.cjs.js"),c=require("../core/PositionalAudio.cjs.js"),a=require("../core/Text.cjs.js"),u=require("../core/Text3D.cjs.js"),n=require("../core/Effects.cjs.js"),j=require("../core/GradientTexture.cjs.js"),p=require("../core/Image.cjs.js"),x=require("../core/Edges.cjs.js"),l=require("../core/Trail.cjs.js"),q=require("../core/Sampler.cjs.js"),d=require("../core/ComputedAttribute.cjs.js"),m=require("../core/Clone.cjs.js"),h=require("../core/MarchingCubes.cjs.js"),C=require("../core/Decal.cjs.js"),M=require("../core/Svg.cjs.js"),S=require("../core/Gltf.cjs.js"),T=require("../core/AsciiRenderer.cjs.js"),b=require("../core/OrthographicCamera.cjs.js"),f=require("../core/PerspectiveCamera.cjs.js"),B=require("../core/CubeCamera.cjs.js"),P=require("../core/DeviceOrientationControls.cjs.js"),g=require("../core/FlyControls.cjs.js"),v=require("../core/MapControls.cjs.js"),A=require("../core/OrbitControls.cjs.js"),L=require("../core/TrackballControls.cjs.js"),D=require("../core/ArcballControls.cjs.js"),E=require("../core/TransformControls.cjs.js"),R=require("../core/PointerLockControls.cjs.js"),k=require("../core/FirstPersonControls.cjs.js"),F=require("../core/CameraControls.cjs.js"),G=require("../core/GizmoHelper.cjs.js"),w=require("../core/GizmoViewcube.cjs.js"),z=require("../core/GizmoViewport.cjs.js"),O=require("../core/Grid.cjs.js"),I=require("../core/useCubeTexture.cjs.js"),H=require("../core/useFBX.cjs.js"),V=require("../core/useGLTF.cjs.js"),y=require("../core/useKTX2.cjs.js"),W=require("../core/useProgress.cjs.js"),Q=require("../core/useTexture.cjs.js"),X=require("../core/useVideoTexture.cjs.js"),K=require("../core/useFont.cjs.js"),N=require("../core/Stats.cjs.js"),U=require("../core/useDepthBuffer.cjs.js"),_=require("../core/useAspect.cjs.js"),J=require("../core/useCamera.cjs.js"),Y=require("../core/useDetectGPU.cjs.js"),Z=require("../core/useHelper.cjs.js"),$=require("../core/useBVH.cjs.js"),ee=require("../core/useContextBridge.cjs.js"),re=require("../core/useAnimations.cjs.js"),se=require("../core/useFBO.cjs.js"),oe=require("../core/useIntersect.cjs.js"),te=require("../core/useBoxProjectedEnv.cjs.js"),ie=require("../core/BBAnchor.cjs.js"),ce=require("../core/useTrailTexture.cjs.js"),ae=require("../core/useCubeCamera.cjs.js"),ue=require("../core/Example.cjs.js"),ne=require("../core/SpriteAnimator.cjs.js"),je=require("../core/CurveModifier.cjs.js"),pe=require("../core/MeshDistortMaterial.cjs.js"),xe=require("../core/MeshWobbleMaterial.cjs.js"),le=require("../core/MeshReflectorMaterial.cjs.js"),qe=require("../core/MeshRefractionMaterial.cjs.js"),de=require("../core/MeshTransmissionMaterial.cjs.js"),me=require("../core/MeshDiscardMaterial.cjs.js"),he=require("../core/PointMaterial.cjs.js"),Ce=require("../core/shaderMaterial.cjs.js"),Me=require("../core/softShadows.cjs.js"),Se=require("../core/shapes.cjs.js"),Te=require("../core/Facemesh.cjs.js"),be=require("../core/RoundedBox.cjs.js"),fe=require("../core/ScreenQuad.cjs.js"),Be=require("../core/Center.cjs.js"),Pe=require("../core/Resize.cjs.js"),ge=require("../core/Bounds.cjs.js"),ve=require("../core/CameraShake.cjs.js"),Ae=require("../core/Float.cjs.js"),Le=require("../core/Stage.cjs.js"),De=require("../core/Backdrop.cjs.js"),Ee=require("../core/Shadow.cjs.js"),Re=require("../core/Caustics.cjs.js"),ke=require("../core/ContactShadows.cjs.js"),Fe=require("../core/AccumulativeShadows.cjs.js"),Ge=require("../core/Reflector.cjs.js"),we=require("../core/SpotLight.cjs.js"),ze=require("../core/Environment.cjs.js"),Oe=require("../core/Lightformer.cjs.js"),Ie=require("../core/Sky.cjs.js"),He=require("../core/Stars.cjs.js"),Ve=require("../core/Cloud.cjs.js"),ye=require("../core/Sparkles.cjs.js"),We=require("../core/useEnvironment.cjs.js"),Qe=require("../core/useMatcapTexture.cjs.js"),Xe=require("../core/useNormalTexture.cjs.js"),Ke=require("../core/Wireframe.cjs.js"),Ne=require("../core/Points.cjs.js"),Ue=require("../core/Instances.cjs.js"),_e=require("../core/Segments.cjs.js"),Je=require("../core/Detailed.cjs.js"),Ye=require("../core/Preload.cjs.js"),Ze=require("../core/BakeShadows.cjs.js"),$e=require("../core/meshBounds.cjs.js"),er=require("../core/AdaptiveDpr.cjs.js"),rr=require("../core/AdaptiveEvents.cjs.js"),sr=require("../core/PerformanceMonitor.cjs.js"),or=require("../core/RenderTexture.cjs.js"),tr=require("../core/Mask.cjs.js"),ir=require("../core/Hud.cjs.js");require("@babel/runtime/helpers/extends"),require("react"),require("@react-three/fiber"),require("react-merge-refs"),require("three"),require("three-stdlib"),require("troika-three-text"),require("suspend-react"),require("meshline"),require("lodash.pick"),require("lodash.omit"),require("camera-controls"),require("zustand"),require("stats.js"),require("../helpers/useEffectfulState.cjs.js"),require("detect-gpu"),require("three-mesh-bvh"),require("../materials/BlurPass.cjs.js"),require("../materials/ConvolutionMaterial.cjs.js"),require("../materials/MeshReflectorMaterial.cjs.js"),require("../materials/MeshRefractionMaterial.cjs.js"),require("../materials/DiscardMaterial.cjs.js"),require("../helpers/environment-assets.cjs.js"),require("../materials/SpotLightMaterial.cjs.js"),require("../materials/WireframeMaterial.cjs.js"),require("react-composer"),exports.Billboard=e.Billboard,exports.ScreenSpace=r.ScreenSpace,exports.QuadraticBezierLine=s.QuadraticBezierLine,exports.CubicBezierLine=o.CubicBezierLine,exports.CatmullRomLine=t.CatmullRomLine,exports.Line=i.Line,exports.PositionalAudio=c.PositionalAudio,exports.Text=a.Text,exports.Text3D=u.Text3D,exports.Effects=n.Effects,exports.isWebGL2Available=n.isWebGL2Available,exports.GradientTexture=j.GradientTexture,exports.Image=p.Image,exports.Edges=x.Edges,exports.Trail=l.Trail,exports.useTrail=l.useTrail,exports.Sampler=q.Sampler,exports.useSurfaceSampler=q.useSurfaceSampler,exports.ComputedAttribute=d.ComputedAttribute,exports.Clone=m.Clone,exports.MarchingCube=h.MarchingCube,exports.MarchingCubes=h.MarchingCubes,exports.MarchingPlane=h.MarchingPlane,exports.Decal=C.Decal,exports.Svg=M.Svg,exports.Gltf=S.Gltf,exports.AsciiRenderer=T.AsciiRenderer,exports.OrthographicCamera=b.OrthographicCamera,exports.PerspectiveCamera=f.PerspectiveCamera,exports.CubeCamera=B.CubeCamera,exports.DeviceOrientationControls=P.DeviceOrientationControls,exports.FlyControls=g.FlyControls,exports.MapControls=v.MapControls,exports.OrbitControls=A.OrbitControls,exports.TrackballControls=L.TrackballControls,exports.ArcballControls=D.ArcballControls,exports.TransformControls=E.TransformControls,exports.PointerLockControls=R.PointerLockControls,exports.FirstPersonControls=k.FirstPersonControls,exports.CameraControls=F.CameraControls,exports.GizmoHelper=G.GizmoHelper,exports.useGizmoContext=G.useGizmoContext,exports.GizmoViewcube=w.GizmoViewcube,exports.GizmoViewport=z.GizmoViewport,exports.Grid=O.Grid,exports.useCubeTexture=I.useCubeTexture,exports.useFBX=H.useFBX,exports.useGLTF=V.useGLTF,exports.useKTX2=y.useKTX2,exports.useProgress=W.useProgress,exports.IsObject=Q.IsObject,exports.useTexture=Q.useTexture,exports.useVideoTexture=X.useVideoTexture,exports.useFont=K.useFont,exports.Stats=N.Stats,exports.useDepthBuffer=U.useDepthBuffer,exports.useAspect=_.useAspect,exports.useCamera=J.useCamera,exports.useDetectGPU=Y.useDetectGPU,exports.useHelper=Z.useHelper,exports.Bvh=$.Bvh,exports.useBVH=$.useBVH,exports.useContextBridge=ee.useContextBridge,exports.useAnimations=re.useAnimations,exports.useFBO=se.useFBO,exports.useIntersect=oe.useIntersect,exports.useBoxProjectedEnv=te.useBoxProjectedEnv,exports.BBAnchor=ie.BBAnchor,exports.useTrailTexture=ce.useTrailTexture,exports.useCubeCamera=ae.useCubeCamera,exports.Example=ue.Example,exports.SpriteAnimator=ne.SpriteAnimator,exports.CurveModifier=je.CurveModifier,exports.MeshDistortMaterial=pe.MeshDistortMaterial,exports.MeshWobbleMaterial=xe.MeshWobbleMaterial,exports.MeshReflectorMaterial=le.MeshReflectorMaterial,exports.MeshRefractionMaterial=qe.MeshRefractionMaterial,exports.MeshTransmissionMaterial=de.MeshTransmissionMaterial,exports.MeshDiscardMaterial=me.MeshDiscardMaterial,exports.PointMaterial=he.PointMaterial,exports.PointMaterialImpl=he.PointMaterialImpl,exports.shaderMaterial=Ce.shaderMaterial,exports.SoftShadows=Me.SoftShadows,exports.Box=Se.Box,exports.Capsule=Se.Capsule,exports.Circle=Se.Circle,exports.Cone=Se.Cone,exports.Cylinder=Se.Cylinder,exports.Dodecahedron=Se.Dodecahedron,exports.Extrude=Se.Extrude,exports.Icosahedron=Se.Icosahedron,exports.Lathe=Se.Lathe,exports.Octahedron=Se.Octahedron,exports.Plane=Se.Plane,exports.Polyhedron=Se.Polyhedron,exports.Ring=Se.Ring,exports.Shape=Se.Shape,exports.Sphere=Se.Sphere,exports.Tetrahedron=Se.Tetrahedron,exports.Torus=Se.Torus,exports.TorusKnot=Se.TorusKnot,exports.Tube=Se.Tube,exports.Facemesh=Te.Facemesh,exports.FacemeshDatas=Te.FacemeshDatas,exports.RoundedBox=be.RoundedBox,exports.ScreenQuad=fe.ScreenQuad,exports.Center=Be.Center,exports.Resize=Pe.Resize,exports.Bounds=ge.Bounds,exports.useBounds=ge.useBounds,exports.CameraShake=ve.CameraShake,exports.Float=Ae.Float,exports.Stage=Le.Stage,exports.Backdrop=De.Backdrop,exports.Shadow=Ee.Shadow,exports.Caustics=Re.Caustics,exports.ContactShadows=ke.ContactShadows,exports.AccumulativeShadows=Fe.AccumulativeShadows,exports.RandomizedLight=Fe.RandomizedLight,exports.accumulativeContext=Fe.accumulativeContext,exports.Reflector=Ge.Reflector,exports.SpotLight=we.SpotLight,exports.SpotLightShadow=we.SpotLightShadow,exports.Environment=ze.Environment,exports.EnvironmentCube=ze.EnvironmentCube,exports.EnvironmentMap=ze.EnvironmentMap,exports.EnvironmentPortal=ze.EnvironmentPortal,exports.Lightformer=Oe.Lightformer,exports.Sky=Ie.Sky,exports.calcPosFromAngles=Ie.calcPosFromAngles,exports.Stars=He.Stars,exports.Cloud=Ve.Cloud,exports.Sparkles=ye.Sparkles,exports.useEnvironment=We.useEnvironment,exports.useMatcapTexture=Qe.useMatcapTexture,exports.useNormalTexture=Xe.useNormalTexture,exports.Wireframe=Ke.Wireframe,exports.Point=Ne.Point,exports.Points=Ne.Points,exports.PointsBuffer=Ne.PointsBuffer,exports.PositionPoint=Ne.PositionPoint,exports.Instance=Ue.Instance,exports.Instances=Ue.Instances,exports.Merged=Ue.Merged,exports.Segment=_e.Segment,exports.SegmentObject=_e.SegmentObject,exports.Segments=_e.Segments,exports.Detailed=Je.Detailed,exports.Preload=Ye.Preload,exports.BakeShadows=Ze.BakeShadows,exports.meshBounds=$e.meshBounds,exports.AdaptiveDpr=er.AdaptiveDpr,exports.AdaptiveEvents=rr.AdaptiveEvents,exports.PerformanceMonitor=sr.PerformanceMonitor,exports.usePerformanceMonitor=sr.usePerformanceMonitor,exports.RenderTexture=or.RenderTexture,exports.Mask=tr.Mask,exports.useMask=tr.useMask,exports.Hud=ir.Hud;
