{"ast": null, "code": "import * as React from 'react';\nimport create from 'zustand';\nimport { subscribeWithSelector } from 'zustand/middleware';\nconst context = /*@__PURE__*/React.createContext(null);\nfunction KeyboardControls({\n  map,\n  children,\n  onChange,\n  domElement\n}) {\n  const key = map.map(item => item.name + item.keys).join('-');\n  const useControls = React.useMemo(() => {\n    return create(subscribeWithSelector(() => map.reduce((prev, cur) => ({\n      ...prev,\n      [cur.name]: false\n    }), {})));\n  }, [key]);\n  const api = React.useMemo(() => [useControls.subscribe, useControls.getState, useControls], [key]);\n  const set = useControls.setState;\n  React.useEffect(() => {\n    const config = map.map(({\n      name,\n      keys,\n      up\n    }) => ({\n      keys,\n      up,\n      fn: value => {\n        // Set zustand state\n        set({\n          [name]: value\n        }); // Inform callback\n\n        if (onChange) onChange(name, value, api[1]());\n      }\n    }));\n    const keyMap = config.reduce((out, {\n      keys,\n      fn,\n      up = true\n    }) => {\n      keys.forEach(key => out[key] = {\n        fn,\n        pressed: false,\n        up\n      });\n      return out;\n    }, {});\n    const downHandler = ({\n      key,\n      code\n    }) => {\n      const obj = keyMap[key] || keyMap[code];\n      if (!obj) return;\n      const {\n        fn,\n        pressed,\n        up\n      } = obj;\n      obj.pressed = true;\n      if (up || !pressed) fn(true);\n    };\n    const upHandler = ({\n      key,\n      code\n    }) => {\n      const obj = keyMap[key] || keyMap[code];\n      if (!obj) return;\n      const {\n        fn,\n        up\n      } = obj;\n      obj.pressed = false;\n      if (up) fn(false);\n    };\n    const source = domElement || window;\n    source.addEventListener('keydown', downHandler, {\n      passive: true\n    });\n    source.addEventListener('keyup', upHandler, {\n      passive: true\n    });\n    return () => {\n      source.removeEventListener('keydown', downHandler);\n      source.removeEventListener('keyup', upHandler);\n    };\n  }, [domElement, key]);\n  return /*#__PURE__*/React.createElement(context.Provider, {\n    value: api,\n    children: children\n  });\n}\nfunction useKeyboardControls(sel) {\n  const [sub, get, store] = React.useContext(context);\n  if (sel) return store(sel);else return [sub, get];\n}\nexport { KeyboardControls, useKeyboardControls };", "map": {"version": 3, "names": ["React", "create", "subscribeWithSelector", "context", "createContext", "KeyboardControls", "map", "children", "onChange", "dom<PERSON>lement", "key", "item", "name", "keys", "join", "useControls", "useMemo", "reduce", "prev", "cur", "api", "subscribe", "getState", "set", "setState", "useEffect", "config", "up", "fn", "value", "keyMap", "out", "for<PERSON>ach", "pressed", "downHandler", "code", "obj", "up<PERSON><PERSON><PERSON>", "source", "window", "addEventListener", "passive", "removeEventListener", "createElement", "Provider", "useKeyboardControls", "sel", "sub", "get", "store", "useContext"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/web/KeyboardControls.js"], "sourcesContent": ["import * as React from 'react';\nimport create from 'zustand';\nimport { subscribeWithSelector } from 'zustand/middleware';\n\nconst context = /*@__PURE__*/React.createContext(null);\nfunction KeyboardControls({\n  map,\n  children,\n  onChange,\n  domElement\n}) {\n  const key = map.map(item => item.name + item.keys).join('-');\n  const useControls = React.useMemo(() => {\n    return create(subscribeWithSelector(() => map.reduce((prev, cur) => ({ ...prev,\n      [cur.name]: false\n    }), {})));\n  }, [key]);\n  const api = React.useMemo(() => [useControls.subscribe, useControls.getState, useControls], [key]);\n  const set = useControls.setState;\n  React.useEffect(() => {\n    const config = map.map(({\n      name,\n      keys,\n      up\n    }) => ({\n      keys,\n      up,\n      fn: value => {\n        // Set zustand state\n        set({\n          [name]: value\n        }); // Inform callback\n\n        if (onChange) onChange(name, value, api[1]());\n      }\n    }));\n    const keyMap = config.reduce((out, {\n      keys,\n      fn,\n      up = true\n    }) => {\n      keys.forEach(key => out[key] = {\n        fn,\n        pressed: false,\n        up\n      });\n      return out;\n    }, {});\n\n    const downHandler = ({\n      key,\n      code\n    }) => {\n      const obj = keyMap[key] || keyMap[code];\n      if (!obj) return;\n      const {\n        fn,\n        pressed,\n        up\n      } = obj;\n      obj.pressed = true;\n      if (up || !pressed) fn(true);\n    };\n\n    const upHandler = ({\n      key,\n      code\n    }) => {\n      const obj = keyMap[key] || keyMap[code];\n      if (!obj) return;\n      const {\n        fn,\n        up\n      } = obj;\n      obj.pressed = false;\n      if (up) fn(false);\n    };\n\n    const source = domElement || window;\n    source.addEventListener('keydown', downHandler, {\n      passive: true\n    });\n    source.addEventListener('keyup', upHandler, {\n      passive: true\n    });\n    return () => {\n      source.removeEventListener('keydown', downHandler);\n      source.removeEventListener('keyup', upHandler);\n    };\n  }, [domElement, key]);\n  return /*#__PURE__*/React.createElement(context.Provider, {\n    value: api,\n    children: children\n  });\n}\nfunction useKeyboardControls(sel) {\n  const [sub, get, store] = React.useContext(context);\n  if (sel) return store(sel);else return [sub, get];\n}\n\nexport { KeyboardControls, useKeyboardControls };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,SAAS;AAC5B,SAASC,qBAAqB,QAAQ,oBAAoB;AAE1D,MAAMC,OAAO,GAAG,aAAaH,KAAK,CAACI,aAAa,CAAC,IAAI,CAAC;AACtD,SAASC,gBAAgBA,CAAC;EACxBC,GAAG;EACHC,QAAQ;EACRC,QAAQ;EACRC;AACF,CAAC,EAAE;EACD,MAAMC,GAAG,GAAGJ,GAAG,CAACA,GAAG,CAACK,IAAI,IAAIA,IAAI,CAACC,IAAI,GAAGD,IAAI,CAACE,IAAI,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAC5D,MAAMC,WAAW,GAAGf,KAAK,CAACgB,OAAO,CAAC,MAAM;IACtC,OAAOf,MAAM,CAACC,qBAAqB,CAAC,MAAMI,GAAG,CAACW,MAAM,CAAC,CAACC,IAAI,EAAEC,GAAG,MAAM;MAAE,GAAGD,IAAI;MAC5E,CAACC,GAAG,CAACP,IAAI,GAAG;IACd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,EAAE,CAACF,GAAG,CAAC,CAAC;EACT,MAAMU,GAAG,GAAGpB,KAAK,CAACgB,OAAO,CAAC,MAAM,CAACD,WAAW,CAACM,SAAS,EAAEN,WAAW,CAACO,QAAQ,EAAEP,WAAW,CAAC,EAAE,CAACL,GAAG,CAAC,CAAC;EAClG,MAAMa,GAAG,GAAGR,WAAW,CAACS,QAAQ;EAChCxB,KAAK,CAACyB,SAAS,CAAC,MAAM;IACpB,MAAMC,MAAM,GAAGpB,GAAG,CAACA,GAAG,CAAC,CAAC;MACtBM,IAAI;MACJC,IAAI;MACJc;IACF,CAAC,MAAM;MACLd,IAAI;MACJc,EAAE;MACFC,EAAE,EAAEC,KAAK,IAAI;QACX;QACAN,GAAG,CAAC;UACF,CAACX,IAAI,GAAGiB;QACV,CAAC,CAAC,CAAC,CAAC;;QAEJ,IAAIrB,QAAQ,EAAEA,QAAQ,CAACI,IAAI,EAAEiB,KAAK,EAAET,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/C;IACF,CAAC,CAAC,CAAC;IACH,MAAMU,MAAM,GAAGJ,MAAM,CAACT,MAAM,CAAC,CAACc,GAAG,EAAE;MACjClB,IAAI;MACJe,EAAE;MACFD,EAAE,GAAG;IACP,CAAC,KAAK;MACJd,IAAI,CAACmB,OAAO,CAACtB,GAAG,IAAIqB,GAAG,CAACrB,GAAG,CAAC,GAAG;QAC7BkB,EAAE;QACFK,OAAO,EAAE,KAAK;QACdN;MACF,CAAC,CAAC;MACF,OAAOI,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;IAEN,MAAMG,WAAW,GAAGA,CAAC;MACnBxB,GAAG;MACHyB;IACF,CAAC,KAAK;MACJ,MAAMC,GAAG,GAAGN,MAAM,CAACpB,GAAG,CAAC,IAAIoB,MAAM,CAACK,IAAI,CAAC;MACvC,IAAI,CAACC,GAAG,EAAE;MACV,MAAM;QACJR,EAAE;QACFK,OAAO;QACPN;MACF,CAAC,GAAGS,GAAG;MACPA,GAAG,CAACH,OAAO,GAAG,IAAI;MAClB,IAAIN,EAAE,IAAI,CAACM,OAAO,EAAEL,EAAE,CAAC,IAAI,CAAC;IAC9B,CAAC;IAED,MAAMS,SAAS,GAAGA,CAAC;MACjB3B,GAAG;MACHyB;IACF,CAAC,KAAK;MACJ,MAAMC,GAAG,GAAGN,MAAM,CAACpB,GAAG,CAAC,IAAIoB,MAAM,CAACK,IAAI,CAAC;MACvC,IAAI,CAACC,GAAG,EAAE;MACV,MAAM;QACJR,EAAE;QACFD;MACF,CAAC,GAAGS,GAAG;MACPA,GAAG,CAACH,OAAO,GAAG,KAAK;MACnB,IAAIN,EAAE,EAAEC,EAAE,CAAC,KAAK,CAAC;IACnB,CAAC;IAED,MAAMU,MAAM,GAAG7B,UAAU,IAAI8B,MAAM;IACnCD,MAAM,CAACE,gBAAgB,CAAC,SAAS,EAAEN,WAAW,EAAE;MAC9CO,OAAO,EAAE;IACX,CAAC,CAAC;IACFH,MAAM,CAACE,gBAAgB,CAAC,OAAO,EAAEH,SAAS,EAAE;MAC1CI,OAAO,EAAE;IACX,CAAC,CAAC;IACF,OAAO,MAAM;MACXH,MAAM,CAACI,mBAAmB,CAAC,SAAS,EAAER,WAAW,CAAC;MAClDI,MAAM,CAACI,mBAAmB,CAAC,OAAO,EAAEL,SAAS,CAAC;IAChD,CAAC;EACH,CAAC,EAAE,CAAC5B,UAAU,EAAEC,GAAG,CAAC,CAAC;EACrB,OAAO,aAAaV,KAAK,CAAC2C,aAAa,CAACxC,OAAO,CAACyC,QAAQ,EAAE;IACxDf,KAAK,EAAET,GAAG;IACVb,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ;AACA,SAASsC,mBAAmBA,CAACC,GAAG,EAAE;EAChC,MAAM,CAACC,GAAG,EAAEC,GAAG,EAAEC,KAAK,CAAC,GAAGjD,KAAK,CAACkD,UAAU,CAAC/C,OAAO,CAAC;EACnD,IAAI2C,GAAG,EAAE,OAAOG,KAAK,CAACH,GAAG,CAAC,CAAC,KAAK,OAAO,CAACC,GAAG,EAAEC,GAAG,CAAC;AACnD;AAEA,SAAS3C,gBAAgB,EAAEwC,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}