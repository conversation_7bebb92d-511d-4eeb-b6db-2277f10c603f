"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("three"),r=require("react"),n=require("@react-three/fiber"),o=require("camera-controls");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function a(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var u=s(e),c=a(t),i=a(r),d=s(o);const l=r.forwardRef(((e,t)=>{r.useMemo((()=>{d.default.install({THREE:c}),n.extend({CameraControlsImpl:d.default})}),[]);const{camera:o,domElement:s,makeDefault:a,onStart:l,onEnd:f,onChange:v,regress:m,...E}=e,p=n.useThree((e=>e.camera)),b=n.useThree((e=>e.gl)),h=n.useThree((e=>e.invalidate)),L=n.useThree((e=>e.events)),T=n.useThree((e=>e.setEvents)),g=n.useThree((e=>e.set)),j=n.useThree((e=>e.get)),O=n.useThree((e=>e.performance)),q=o||p,w=s||L.connected||b.domElement,y=r.useMemo((()=>new d.default(q)),[q]);return n.useFrame(((e,t)=>{y.enabled&&y.update(t)}),-1),r.useEffect((()=>(y.connect(w),()=>{y.disconnect()})),[w,y]),i.useEffect((()=>{const e=e=>{h(),m&&O.regress(),v&&v(e)},t=e=>{l&&l(e)},r=e=>{f&&f(e)};return y.addEventListener("update",e),y.addEventListener("controlstart",t),y.addEventListener("controlend",r),y.addEventListener("control",e),y.addEventListener("transitionstart",e),y.addEventListener("wake",e),()=>{y.removeEventListener("update",e),y.removeEventListener("controlstart",t),y.removeEventListener("controlend",r),y.removeEventListener("control",e),y.removeEventListener("transitionstart",e),y.removeEventListener("wake",e)}}),[y,l,f,h,T,m,v]),r.useEffect((()=>{if(a){const e=j().controls;return g({controls:y}),()=>g({controls:e})}}),[a,y]),i.createElement("primitive",u.default({ref:t,object:y},E))}));exports.CameraControls=l;
