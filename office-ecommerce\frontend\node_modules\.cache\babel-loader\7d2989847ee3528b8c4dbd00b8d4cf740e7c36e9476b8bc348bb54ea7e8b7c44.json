{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\hooks\\\\useAuth.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authService } from '../services/auth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const savedToken = localStorage.getItem('token');\n      const savedUser = localStorage.getItem('user');\n      if (savedToken && savedUser) {\n        try {\n          const userData = JSON.parse(savedUser);\n          setUser(userData);\n          setToken(savedToken);\n        } catch (error) {\n          console.error('Failed to initialize auth:', error);\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          setToken(null);\n        }\n      }\n      setLoading(false);\n    };\n    initializeAuth();\n  }, []);\n  const login = async (email, password) => {\n    try {\n      const response = await authService.login(email, password);\n      const {\n        token: newToken,\n        user: userData\n      } = response.data.data;\n      localStorage.setItem('token', newToken);\n      localStorage.setItem('user', JSON.stringify(userData));\n      setToken(newToken);\n      setUser(userData);\n      return {\n        success: true,\n        user: userData\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Login failed:', error);\n      return {\n        success: false,\n        error: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed'\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      const response = await authService.register(userData);\n      const {\n        token: newToken,\n        user: newUser\n      } = response;\n      localStorage.setItem('token', newToken);\n      setToken(newToken);\n      setUser(newUser);\n      return {\n        success: true,\n        user: newUser\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Registration failed:', error);\n      return {\n        success: false,\n        error: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Registration failed'\n      };\n    }\n  };\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setToken(null);\n    setUser(null);\n  };\n  const updateProfile = async profileData => {\n    try {\n      const response = await authService.updateProfile(profileData);\n      setUser(response.user);\n      return {\n        success: true,\n        user: response.user\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('Profile update failed:', error);\n      return {\n        success: false,\n        error: ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Profile update failed'\n      };\n    }\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    updateProfile,\n    isAuthenticated: !!user\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 9\n  }, this);\n};\n_s2(AuthProvider, \"/pbUqy0QsBvMqKPYubk3+KKKH8I=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "setToken", "localStorage", "getItem", "initializeAuth", "savedToken", "savedUser", "userData", "JSON", "parse", "error", "console", "removeItem", "login", "email", "password", "response", "newToken", "data", "setItem", "stringify", "success", "_error$response", "_error$response$data", "message", "register", "newUser", "_error$response2", "_error$response2$data", "logout", "updateProfile", "profileData", "_error$response3", "_error$response3$data", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/hooks/useAuth.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authService } from '../services/auth';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n    const context = useContext(AuthContext);\n    if (!context) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n    const [user, setUser] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [token, setToken] = useState(localStorage.getItem('token'));\n\n    useEffect(() => {\n        const initializeAuth = async () => {\n            const savedToken = localStorage.getItem('token');\n            const savedUser = localStorage.getItem('user');\n            if (savedToken && savedUser) {\n                try {\n                    const userData = JSON.parse(savedUser);\n                    setUser(userData);\n                    setToken(savedToken);\n                } catch (error) {\n                    console.error('Failed to initialize auth:', error);\n                    localStorage.removeItem('token');\n                    localStorage.removeItem('user');\n                    setToken(null);\n                }\n            }\n            setLoading(false);\n        };\n\n        initializeAuth();\n    }, []);\n\n    const login = async (email, password) => {\n        try {\n            const response = await authService.login(email, password);\n            const { token: newToken, user: userData } = response.data.data;\n\n            localStorage.setItem('token', newToken);\n            localStorage.setItem('user', JSON.stringify(userData));\n            setToken(newToken);\n            setUser(userData);\n\n            return { success: true, user: userData };\n        } catch (error) {\n            console.error('Login failed:', error);\n            return {\n                success: false,\n                error: error.response?.data?.message || 'Login failed'\n            };\n        }\n    };\n\n    const register = async (userData) => {\n        try {\n            const response = await authService.register(userData);\n            const { token: newToken, user: newUser } = response;\n            \n            localStorage.setItem('token', newToken);\n            setToken(newToken);\n            setUser(newUser);\n            \n            return { success: true, user: newUser };\n        } catch (error) {\n            console.error('Registration failed:', error);\n            return { \n                success: false, \n                error: error.response?.data?.message || 'Registration failed' \n            };\n        }\n    };\n\n    const logout = () => {\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        setToken(null);\n        setUser(null);\n    };\n\n    const updateProfile = async (profileData) => {\n        try {\n            const response = await authService.updateProfile(profileData);\n            setUser(response.user);\n            return { success: true, user: response.user };\n        } catch (error) {\n            console.error('Profile update failed:', error);\n            return { \n                success: false, \n                error: error.response?.data?.message || 'Profile update failed' \n            };\n        }\n    };\n\n    const value = {\n        user,\n        token,\n        loading,\n        login,\n        register,\n        logout,\n        updateProfile,\n        isAuthenticated: !!user\n    };\n\n    return (\n        <AuthContext.Provider value={value}>\n            {children}\n        </AuthContext.Provider>\n    );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACV,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAClE;EACA,OAAOD,OAAO;AAClB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC1C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAACmB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;EAEjEnB,SAAS,CAAC,MAAM;IACZ,MAAMoB,cAAc,GAAG,MAAAA,CAAA,KAAY;MAC/B,MAAMC,UAAU,GAAGH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAChD,MAAMG,SAAS,GAAGJ,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC9C,IAAIE,UAAU,IAAIC,SAAS,EAAE;QACzB,IAAI;UACA,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,SAAS,CAAC;UACtCT,OAAO,CAACU,QAAQ,CAAC;UACjBN,QAAQ,CAACI,UAAU,CAAC;QACxB,CAAC,CAAC,OAAOK,KAAK,EAAE;UACZC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClDR,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;UAChCV,YAAY,CAACU,UAAU,CAAC,MAAM,CAAC;UAC/BX,QAAQ,CAAC,IAAI,CAAC;QAClB;MACJ;MACAF,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC;IAEDK,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACrC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAM/B,WAAW,CAAC4B,KAAK,CAACC,KAAK,EAAEC,QAAQ,CAAC;MACzD,MAAM;QAAEf,KAAK,EAAEiB,QAAQ;QAAErB,IAAI,EAAEW;MAAS,CAAC,GAAGS,QAAQ,CAACE,IAAI,CAACA,IAAI;MAE9DhB,YAAY,CAACiB,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAAC;MACvCf,YAAY,CAACiB,OAAO,CAAC,MAAM,EAAEX,IAAI,CAACY,SAAS,CAACb,QAAQ,CAAC,CAAC;MACtDN,QAAQ,CAACgB,QAAQ,CAAC;MAClBpB,OAAO,CAACU,QAAQ,CAAC;MAEjB,OAAO;QAAEc,OAAO,EAAE,IAAI;QAAEzB,IAAI,EAAEW;MAAS,CAAC;IAC5C,CAAC,CAAC,OAAOG,KAAK,EAAE;MAAA,IAAAY,eAAA,EAAAC,oBAAA;MACZZ,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,OAAO;QACHW,OAAO,EAAE,KAAK;QACdX,KAAK,EAAE,EAAAY,eAAA,GAAAZ,KAAK,CAACM,QAAQ,cAAAM,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBJ,IAAI,cAAAK,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI;MAC5C,CAAC;IACL;EACJ,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAOlB,QAAQ,IAAK;IACjC,IAAI;MACA,MAAMS,QAAQ,GAAG,MAAM/B,WAAW,CAACwC,QAAQ,CAAClB,QAAQ,CAAC;MACrD,MAAM;QAAEP,KAAK,EAAEiB,QAAQ;QAAErB,IAAI,EAAE8B;MAAQ,CAAC,GAAGV,QAAQ;MAEnDd,YAAY,CAACiB,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAAC;MACvChB,QAAQ,CAACgB,QAAQ,CAAC;MAClBpB,OAAO,CAAC6B,OAAO,CAAC;MAEhB,OAAO;QAAEL,OAAO,EAAE,IAAI;QAAEzB,IAAI,EAAE8B;MAAQ,CAAC;IAC3C,CAAC,CAAC,OAAOhB,KAAK,EAAE;MAAA,IAAAiB,gBAAA,EAAAC,qBAAA;MACZjB,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO;QACHW,OAAO,EAAE,KAAK;QACdX,KAAK,EAAE,EAAAiB,gBAAA,GAAAjB,KAAK,CAACM,QAAQ,cAAAW,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBT,IAAI,cAAAU,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI;MAC5C,CAAC;IACL;EACJ,CAAC;EAED,MAAMK,MAAM,GAAGA,CAAA,KAAM;IACjB3B,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;IAChCV,YAAY,CAACU,UAAU,CAAC,MAAM,CAAC;IAC/BX,QAAQ,CAAC,IAAI,CAAC;IACdJ,OAAO,CAAC,IAAI,CAAC;EACjB,CAAC;EAED,MAAMiC,aAAa,GAAG,MAAOC,WAAW,IAAK;IACzC,IAAI;MACA,MAAMf,QAAQ,GAAG,MAAM/B,WAAW,CAAC6C,aAAa,CAACC,WAAW,CAAC;MAC7DlC,OAAO,CAACmB,QAAQ,CAACpB,IAAI,CAAC;MACtB,OAAO;QAAEyB,OAAO,EAAE,IAAI;QAAEzB,IAAI,EAAEoB,QAAQ,CAACpB;MAAK,CAAC;IACjD,CAAC,CAAC,OAAOc,KAAK,EAAE;MAAA,IAAAsB,gBAAA,EAAAC,qBAAA;MACZtB,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO;QACHW,OAAO,EAAE,KAAK;QACdX,KAAK,EAAE,EAAAsB,gBAAA,GAAAtB,KAAK,CAACM,QAAQ,cAAAgB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBd,IAAI,cAAAe,qBAAA,uBAApBA,qBAAA,CAAsBT,OAAO,KAAI;MAC5C,CAAC;IACL;EACJ,CAAC;EAED,MAAMU,KAAK,GAAG;IACVtC,IAAI;IACJI,KAAK;IACLF,OAAO;IACPe,KAAK;IACLY,QAAQ;IACRI,MAAM;IACNC,aAAa;IACbK,eAAe,EAAE,CAAC,CAACvC;EACvB,CAAC;EAED,oBACIT,OAAA,CAACC,WAAW,CAACgD,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAxC,QAAA,EAC9BA;EAAQ;IAAA2C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CAAC;AAE/B,CAAC;AAAC7C,GAAA,CAvGWF,YAAY;AAAAgD,EAAA,GAAZhD,YAAY;AAAA,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}