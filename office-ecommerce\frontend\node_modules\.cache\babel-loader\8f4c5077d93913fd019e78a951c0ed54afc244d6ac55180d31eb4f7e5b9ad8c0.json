{"ast": null, "code": "import { use<PERSON>rame, useThree, createPortal } from '@react-three/fiber';\nimport * as React from 'react';\nimport { Vector3, Object3D, Vector2 } from 'three';\nimport { MeshLineGeometry, MeshLineMaterial } from 'meshline';\nconst defaults = {\n  width: 0.2,\n  length: 1,\n  decay: 1,\n  local: false,\n  stride: 0,\n  interval: 1\n};\nconst shiftLeft = (collection, steps = 1) => {\n  collection.set(collection.subarray(steps));\n  collection.fill(-Infinity, -steps);\n  return collection;\n};\nfunction useTrail(target, settings) {\n  const {\n    length,\n    local,\n    decay,\n    interval,\n    stride\n  } = {\n    ...defaults,\n    ...settings\n  };\n  const points = React.useRef();\n  const [worldPosition] = React.useState(() => new Vector3());\n  React.useLayoutEffect(() => {\n    if (target) {\n      points.current = Float32Array.from({\n        length: length * 10 * 3\n      }, (_, i) => target.position.getComponent(i % 3));\n    }\n  }, [length, target]);\n  const prevPosition = React.useRef(new Vector3());\n  const frameCount = React.useRef(0);\n  useFrame(() => {\n    if (!target) return;\n    if (!points.current) return;\n    if (frameCount.current === 0) {\n      let newPosition;\n      if (local) {\n        newPosition = target.position;\n      } else {\n        target.getWorldPosition(worldPosition);\n        newPosition = worldPosition;\n      }\n      const steps = 1 * decay;\n      for (let i = 0; i < steps; i++) {\n        if (newPosition.distanceTo(prevPosition.current) < stride) continue;\n        shiftLeft(points.current, 3);\n        points.current.set(newPosition.toArray(), points.current.length - 3);\n      }\n      prevPosition.current.copy(newPosition);\n    }\n    frameCount.current++;\n    frameCount.current = frameCount.current % interval;\n  });\n  return points;\n}\nconst Trail = /*#__PURE__*/React.forwardRef((props, forwardRef) => {\n  const {\n    children\n  } = props;\n  const {\n    width,\n    length,\n    decay,\n    local,\n    stride,\n    interval\n  } = {\n    ...defaults,\n    ...props\n  };\n  const {\n    color = 'hotpink',\n    attenuation,\n    target\n  } = props;\n  const size = useThree(s => s.size);\n  const scene = useThree(s => s.scene);\n  const ref = React.useRef(null);\n  const [anchor, setAnchor] = React.useState(null);\n  const points = useTrail(anchor, {\n    length,\n    decay,\n    local,\n    stride,\n    interval\n  });\n  React.useEffect(() => {\n    const t = (target == null ? void 0 : target.current) || ref.current.children.find(o => {\n      return o instanceof Object3D;\n    });\n    if (t) {\n      setAnchor(t);\n    }\n  }, [points, target]);\n  const geo = React.useMemo(() => new MeshLineGeometry(), []);\n  const mat = React.useMemo(() => {\n    var _matOverride;\n    const m = new MeshLineMaterial({\n      lineWidth: 0.1 * width,\n      color: color,\n      sizeAttenuation: 1,\n      resolution: new Vector2(size.width, size.height)\n    }); // Get and apply first <meshLineMaterial /> from children\n\n    let matOverride;\n    if (children) {\n      if (Array.isArray(children)) {\n        matOverride = children.find(child => {\n          const c = child;\n          return typeof c.type === 'string' && c.type === 'meshLineMaterial';\n        });\n      } else {\n        const c = children;\n        if (typeof c.type === 'string' && c.type === 'meshLineMaterial') {\n          matOverride = c;\n        }\n      }\n    }\n    if (typeof ((_matOverride = matOverride) == null ? void 0 : _matOverride.props) === 'object') {\n      m.setValues(matOverride.props);\n    }\n    return m;\n  }, [width, color, size, children]);\n  React.useEffect(() => {\n    mat.uniforms.resolution.value.set(size.width, size.height);\n  }, [size]);\n  useFrame(() => {\n    if (!points.current) return;\n    geo.setPoints(points.current, attenuation);\n  });\n  return /*#__PURE__*/React.createElement(\"group\", null, createPortal(/*#__PURE__*/React.createElement(\"mesh\", {\n    ref: forwardRef,\n    geometry: geo,\n    material: mat\n  }), scene), /*#__PURE__*/React.createElement(\"group\", {\n    ref: ref\n  }, children));\n});\nexport { Trail, useTrail };", "map": {"version": 3, "names": ["useFrame", "useThree", "createPortal", "React", "Vector3", "Object3D", "Vector2", "MeshLineGeometry", "MeshLineMaterial", "defaults", "width", "length", "decay", "local", "stride", "interval", "shiftLeft", "collection", "steps", "set", "subarray", "fill", "Infinity", "useTrail", "target", "settings", "points", "useRef", "worldPosition", "useState", "useLayoutEffect", "current", "Float32Array", "from", "_", "i", "position", "getComponent", "prevPosition", "frameCount", "newPosition", "getWorldPosition", "distanceTo", "toArray", "copy", "Trail", "forwardRef", "props", "children", "color", "attenuation", "size", "s", "scene", "ref", "anchor", "setAnchor", "useEffect", "t", "find", "o", "geo", "useMemo", "mat", "_matOverride", "m", "lineWidth", "sizeAttenuation", "resolution", "height", "matOverride", "Array", "isArray", "child", "c", "type", "set<PERSON><PERSON><PERSON>", "uniforms", "value", "setPoints", "createElement", "geometry", "material"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Trail.js"], "sourcesContent": ["import { use<PERSON>rame, useThree, createPortal } from '@react-three/fiber';\nimport * as React from 'react';\nimport { Vector3, Object3D, Vector2 } from 'three';\nimport { MeshLineGeometry, MeshLineMaterial } from 'meshline';\n\nconst defaults = {\n  width: 0.2,\n  length: 1,\n  decay: 1,\n  local: false,\n  stride: 0,\n  interval: 1\n};\n\nconst shiftLeft = (collection, steps = 1) => {\n  collection.set(collection.subarray(steps));\n  collection.fill(-Infinity, -steps);\n  return collection;\n};\n\nfunction useTrail(target, settings) {\n  const {\n    length,\n    local,\n    decay,\n    interval,\n    stride\n  } = { ...defaults,\n    ...settings\n  };\n  const points = React.useRef();\n  const [worldPosition] = React.useState(() => new Vector3());\n  React.useLayoutEffect(() => {\n    if (target) {\n      points.current = Float32Array.from({\n        length: length * 10 * 3\n      }, (_, i) => target.position.getComponent(i % 3));\n    }\n  }, [length, target]);\n  const prevPosition = React.useRef(new Vector3());\n  const frameCount = React.useRef(0);\n  useFrame(() => {\n    if (!target) return;\n    if (!points.current) return;\n\n    if (frameCount.current === 0) {\n      let newPosition;\n\n      if (local) {\n        newPosition = target.position;\n      } else {\n        target.getWorldPosition(worldPosition);\n        newPosition = worldPosition;\n      }\n\n      const steps = 1 * decay;\n\n      for (let i = 0; i < steps; i++) {\n        if (newPosition.distanceTo(prevPosition.current) < stride) continue;\n        shiftLeft(points.current, 3);\n        points.current.set(newPosition.toArray(), points.current.length - 3);\n      }\n\n      prevPosition.current.copy(newPosition);\n    }\n\n    frameCount.current++;\n    frameCount.current = frameCount.current % interval;\n  });\n  return points;\n}\nconst Trail = /*#__PURE__*/React.forwardRef((props, forwardRef) => {\n  const {\n    children\n  } = props;\n  const {\n    width,\n    length,\n    decay,\n    local,\n    stride,\n    interval\n  } = { ...defaults,\n    ...props\n  };\n  const {\n    color = 'hotpink',\n    attenuation,\n    target\n  } = props;\n  const size = useThree(s => s.size);\n  const scene = useThree(s => s.scene);\n  const ref = React.useRef(null);\n  const [anchor, setAnchor] = React.useState(null);\n  const points = useTrail(anchor, {\n    length,\n    decay,\n    local,\n    stride,\n    interval\n  });\n  React.useEffect(() => {\n    const t = (target == null ? void 0 : target.current) || ref.current.children.find(o => {\n      return o instanceof Object3D;\n    });\n\n    if (t) {\n      setAnchor(t);\n    }\n  }, [points, target]);\n  const geo = React.useMemo(() => new MeshLineGeometry(), []);\n  const mat = React.useMemo(() => {\n    var _matOverride;\n\n    const m = new MeshLineMaterial({\n      lineWidth: 0.1 * width,\n      color: color,\n      sizeAttenuation: 1,\n      resolution: new Vector2(size.width, size.height)\n    }); // Get and apply first <meshLineMaterial /> from children\n\n    let matOverride;\n\n    if (children) {\n      if (Array.isArray(children)) {\n        matOverride = children.find(child => {\n          const c = child;\n          return typeof c.type === 'string' && c.type === 'meshLineMaterial';\n        });\n      } else {\n        const c = children;\n\n        if (typeof c.type === 'string' && c.type === 'meshLineMaterial') {\n          matOverride = c;\n        }\n      }\n    }\n\n    if (typeof ((_matOverride = matOverride) == null ? void 0 : _matOverride.props) === 'object') {\n      m.setValues(matOverride.props);\n    }\n\n    return m;\n  }, [width, color, size, children]);\n  React.useEffect(() => {\n    mat.uniforms.resolution.value.set(size.width, size.height);\n  }, [size]);\n  useFrame(() => {\n    if (!points.current) return;\n    geo.setPoints(points.current, attenuation);\n  });\n  return /*#__PURE__*/React.createElement(\"group\", null, createPortal( /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: forwardRef,\n    geometry: geo,\n    material: mat\n  }), scene), /*#__PURE__*/React.createElement(\"group\", {\n    ref: ref\n  }, children));\n});\n\nexport { Trail, useTrail };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,oBAAoB;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAClD,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,UAAU;AAE7D,MAAMC,QAAQ,GAAG;EACfC,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,KAAK;EACZC,MAAM,EAAE,CAAC;EACTC,QAAQ,EAAE;AACZ,CAAC;AAED,MAAMC,SAAS,GAAGA,CAACC,UAAU,EAAEC,KAAK,GAAG,CAAC,KAAK;EAC3CD,UAAU,CAACE,GAAG,CAACF,UAAU,CAACG,QAAQ,CAACF,KAAK,CAAC,CAAC;EAC1CD,UAAU,CAACI,IAAI,CAAC,CAACC,QAAQ,EAAE,CAACJ,KAAK,CAAC;EAClC,OAAOD,UAAU;AACnB,CAAC;AAED,SAASM,QAAQA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAClC,MAAM;IACJd,MAAM;IACNE,KAAK;IACLD,KAAK;IACLG,QAAQ;IACRD;EACF,CAAC,GAAG;IAAE,GAAGL,QAAQ;IACf,GAAGgB;EACL,CAAC;EACD,MAAMC,MAAM,GAAGvB,KAAK,CAACwB,MAAM,CAAC,CAAC;EAC7B,MAAM,CAACC,aAAa,CAAC,GAAGzB,KAAK,CAAC0B,QAAQ,CAAC,MAAM,IAAIzB,OAAO,CAAC,CAAC,CAAC;EAC3DD,KAAK,CAAC2B,eAAe,CAAC,MAAM;IAC1B,IAAIN,MAAM,EAAE;MACVE,MAAM,CAACK,OAAO,GAAGC,YAAY,CAACC,IAAI,CAAC;QACjCtB,MAAM,EAAEA,MAAM,GAAG,EAAE,GAAG;MACxB,CAAC,EAAE,CAACuB,CAAC,EAAEC,CAAC,KAAKX,MAAM,CAACY,QAAQ,CAACC,YAAY,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC;IACnD;EACF,CAAC,EAAE,CAACxB,MAAM,EAAEa,MAAM,CAAC,CAAC;EACpB,MAAMc,YAAY,GAAGnC,KAAK,CAACwB,MAAM,CAAC,IAAIvB,OAAO,CAAC,CAAC,CAAC;EAChD,MAAMmC,UAAU,GAAGpC,KAAK,CAACwB,MAAM,CAAC,CAAC,CAAC;EAClC3B,QAAQ,CAAC,MAAM;IACb,IAAI,CAACwB,MAAM,EAAE;IACb,IAAI,CAACE,MAAM,CAACK,OAAO,EAAE;IAErB,IAAIQ,UAAU,CAACR,OAAO,KAAK,CAAC,EAAE;MAC5B,IAAIS,WAAW;MAEf,IAAI3B,KAAK,EAAE;QACT2B,WAAW,GAAGhB,MAAM,CAACY,QAAQ;MAC/B,CAAC,MAAM;QACLZ,MAAM,CAACiB,gBAAgB,CAACb,aAAa,CAAC;QACtCY,WAAW,GAAGZ,aAAa;MAC7B;MAEA,MAAMV,KAAK,GAAG,CAAC,GAAGN,KAAK;MAEvB,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,KAAK,EAAEiB,CAAC,EAAE,EAAE;QAC9B,IAAIK,WAAW,CAACE,UAAU,CAACJ,YAAY,CAACP,OAAO,CAAC,GAAGjB,MAAM,EAAE;QAC3DE,SAAS,CAACU,MAAM,CAACK,OAAO,EAAE,CAAC,CAAC;QAC5BL,MAAM,CAACK,OAAO,CAACZ,GAAG,CAACqB,WAAW,CAACG,OAAO,CAAC,CAAC,EAAEjB,MAAM,CAACK,OAAO,CAACpB,MAAM,GAAG,CAAC,CAAC;MACtE;MAEA2B,YAAY,CAACP,OAAO,CAACa,IAAI,CAACJ,WAAW,CAAC;IACxC;IAEAD,UAAU,CAACR,OAAO,EAAE;IACpBQ,UAAU,CAACR,OAAO,GAAGQ,UAAU,CAACR,OAAO,GAAGhB,QAAQ;EACpD,CAAC,CAAC;EACF,OAAOW,MAAM;AACf;AACA,MAAMmB,KAAK,GAAG,aAAa1C,KAAK,CAAC2C,UAAU,CAAC,CAACC,KAAK,EAAED,UAAU,KAAK;EACjE,MAAM;IACJE;EACF,CAAC,GAAGD,KAAK;EACT,MAAM;IACJrC,KAAK;IACLC,MAAM;IACNC,KAAK;IACLC,KAAK;IACLC,MAAM;IACNC;EACF,CAAC,GAAG;IAAE,GAAGN,QAAQ;IACf,GAAGsC;EACL,CAAC;EACD,MAAM;IACJE,KAAK,GAAG,SAAS;IACjBC,WAAW;IACX1B;EACF,CAAC,GAAGuB,KAAK;EACT,MAAMI,IAAI,GAAGlD,QAAQ,CAACmD,CAAC,IAAIA,CAAC,CAACD,IAAI,CAAC;EAClC,MAAME,KAAK,GAAGpD,QAAQ,CAACmD,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC;EACpC,MAAMC,GAAG,GAAGnD,KAAK,CAACwB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAAC4B,MAAM,EAAEC,SAAS,CAAC,GAAGrD,KAAK,CAAC0B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAMH,MAAM,GAAGH,QAAQ,CAACgC,MAAM,EAAE;IAC9B5C,MAAM;IACNC,KAAK;IACLC,KAAK;IACLC,MAAM;IACNC;EACF,CAAC,CAAC;EACFZ,KAAK,CAACsD,SAAS,CAAC,MAAM;IACpB,MAAMC,CAAC,GAAG,CAAClC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACO,OAAO,KAAKuB,GAAG,CAACvB,OAAO,CAACiB,QAAQ,CAACW,IAAI,CAACC,CAAC,IAAI;MACrF,OAAOA,CAAC,YAAYvD,QAAQ;IAC9B,CAAC,CAAC;IAEF,IAAIqD,CAAC,EAAE;MACLF,SAAS,CAACE,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAAChC,MAAM,EAAEF,MAAM,CAAC,CAAC;EACpB,MAAMqC,GAAG,GAAG1D,KAAK,CAAC2D,OAAO,CAAC,MAAM,IAAIvD,gBAAgB,CAAC,CAAC,EAAE,EAAE,CAAC;EAC3D,MAAMwD,GAAG,GAAG5D,KAAK,CAAC2D,OAAO,CAAC,MAAM;IAC9B,IAAIE,YAAY;IAEhB,MAAMC,CAAC,GAAG,IAAIzD,gBAAgB,CAAC;MAC7B0D,SAAS,EAAE,GAAG,GAAGxD,KAAK;MACtBuC,KAAK,EAAEA,KAAK;MACZkB,eAAe,EAAE,CAAC;MAClBC,UAAU,EAAE,IAAI9D,OAAO,CAAC6C,IAAI,CAACzC,KAAK,EAAEyC,IAAI,CAACkB,MAAM;IACjD,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAIC,WAAW;IAEf,IAAItB,QAAQ,EAAE;MACZ,IAAIuB,KAAK,CAACC,OAAO,CAACxB,QAAQ,CAAC,EAAE;QAC3BsB,WAAW,GAAGtB,QAAQ,CAACW,IAAI,CAACc,KAAK,IAAI;UACnC,MAAMC,CAAC,GAAGD,KAAK;UACf,OAAO,OAAOC,CAAC,CAACC,IAAI,KAAK,QAAQ,IAAID,CAAC,CAACC,IAAI,KAAK,kBAAkB;QACpE,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAMD,CAAC,GAAG1B,QAAQ;QAElB,IAAI,OAAO0B,CAAC,CAACC,IAAI,KAAK,QAAQ,IAAID,CAAC,CAACC,IAAI,KAAK,kBAAkB,EAAE;UAC/DL,WAAW,GAAGI,CAAC;QACjB;MACF;IACF;IAEA,IAAI,QAAQ,CAACV,YAAY,GAAGM,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGN,YAAY,CAACjB,KAAK,CAAC,KAAK,QAAQ,EAAE;MAC5FkB,CAAC,CAACW,SAAS,CAACN,WAAW,CAACvB,KAAK,CAAC;IAChC;IAEA,OAAOkB,CAAC;EACV,CAAC,EAAE,CAACvD,KAAK,EAAEuC,KAAK,EAAEE,IAAI,EAAEH,QAAQ,CAAC,CAAC;EAClC7C,KAAK,CAACsD,SAAS,CAAC,MAAM;IACpBM,GAAG,CAACc,QAAQ,CAACT,UAAU,CAACU,KAAK,CAAC3D,GAAG,CAACgC,IAAI,CAACzC,KAAK,EAAEyC,IAAI,CAACkB,MAAM,CAAC;EAC5D,CAAC,EAAE,CAAClB,IAAI,CAAC,CAAC;EACVnD,QAAQ,CAAC,MAAM;IACb,IAAI,CAAC0B,MAAM,CAACK,OAAO,EAAE;IACrB8B,GAAG,CAACkB,SAAS,CAACrD,MAAM,CAACK,OAAO,EAAEmB,WAAW,CAAC;EAC5C,CAAC,CAAC;EACF,OAAO,aAAa/C,KAAK,CAAC6E,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE9E,YAAY,CAAE,aAAaC,KAAK,CAAC6E,aAAa,CAAC,MAAM,EAAE;IAC5G1B,GAAG,EAAER,UAAU;IACfmC,QAAQ,EAAEpB,GAAG;IACbqB,QAAQ,EAAEnB;EACZ,CAAC,CAAC,EAAEV,KAAK,CAAC,EAAE,aAAalD,KAAK,CAAC6E,aAAa,CAAC,OAAO,EAAE;IACpD1B,GAAG,EAAEA;EACP,CAAC,EAAEN,QAAQ,CAAC,CAAC;AACf,CAAC,CAAC;AAEF,SAASH,KAAK,EAAEtB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}