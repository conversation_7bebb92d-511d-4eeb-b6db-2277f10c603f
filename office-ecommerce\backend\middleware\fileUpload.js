const multer = require('multer');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const logger = require('../utils/logger');

// Ensure upload directories exist
const uploadDirs = {
  models: path.join(__dirname, '../uploads/models'),
  images: path.join(__dirname, '../uploads/images'),
  temp: path.join(__dirname, '../uploads/temp')
};

Object.values(uploadDirs).forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// File type configurations
const fileTypes = {
  models: {
    allowedExtensions: ['.glb', '.gltf'],
    allowedMimeTypes: ['model/gltf-binary', 'model/gltf+json', 'application/octet-stream'],
    maxSize: 50 * 1024 * 1024, // 50MB
    destination: uploadDirs.models
  },
  images: {
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.webp'],
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
    maxSize: 10 * 1024 * 1024, // 10MB
    destination: uploadDirs.images
  }
};

// Storage configuration
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    let destination = uploadDirs.temp;
    
    // Determine destination based on file type
    if (file.fieldname === 'model' || file.fieldname === 'models') {
      destination = fileTypes.models.destination;
    } else if (file.fieldname === 'image' || file.fieldname === 'images') {
      destination = fileTypes.images.destination;
    }
    
    cb(null, destination);
  },
  filename: function (req, file, cb) {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + crypto.randomBytes(6).toString('hex');
    const ext = path.extname(file.originalname).toLowerCase();
    const baseName = path.basename(file.originalname, ext).replace(/[^a-zA-Z0-9]/g, '_');
    const filename = `${baseName}_${uniqueSuffix}${ext}`;
    
    cb(null, filename);
  }
});

// File filter function
const fileFilter = (req, file, cb) => {
  try {
    let config = null;
    
    // Determine file type configuration
    if (file.fieldname === 'model' || file.fieldname === 'models') {
      config = fileTypes.models;
    } else if (file.fieldname === 'image' || file.fieldname === 'images') {
      config = fileTypes.images;
    }
    
    if (!config) {
      return cb(new Error('Invalid field name'), false);
    }
    
    // Check file extension
    const ext = path.extname(file.originalname).toLowerCase();
    if (!config.allowedExtensions.includes(ext)) {
      return cb(new Error(`Invalid file extension. Allowed: ${config.allowedExtensions.join(', ')}`), false);
    }
    
    // Check MIME type
    if (!config.allowedMimeTypes.includes(file.mimetype)) {
      return cb(new Error(`Invalid MIME type. Allowed: ${config.allowedMimeTypes.join(', ')}`), false);
    }
    
    cb(null, true);
  } catch (error) {
    logger.error('File filter error:', error);
    cb(error, false);
  }
};

// Create multer instance
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: Math.max(fileTypes.models.maxSize, fileTypes.images.maxSize), // Use the larger limit
    files: 10 // Maximum number of files
  }
});

// Error handling middleware
const handleMulterError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        return res.status(400).json({
          success: false,
          message: 'File too large',
          error: 'File size exceeds the maximum allowed limit'
        });
      case 'LIMIT_FILE_COUNT':
        return res.status(400).json({
          success: false,
          message: 'Too many files',
          error: 'Number of files exceeds the maximum allowed limit'
        });
      case 'LIMIT_UNEXPECTED_FILE':
        return res.status(400).json({
          success: false,
          message: 'Unexpected file field',
          error: 'Unexpected file field name'
        });
      default:
        return res.status(400).json({
          success: false,
          message: 'File upload error',
          error: error.message
        });
    }
  }
  
  if (error.message.includes('Invalid file extension') || 
      error.message.includes('Invalid MIME type') ||
      error.message.includes('Invalid field name')) {
    return res.status(400).json({
      success: false,
      message: 'Invalid file',
      error: error.message
    });
  }
  
  next(error);
};

// Utility functions
const getFileInfo = (file) => {
  return {
    filename: file.filename,
    originalname: file.originalname,
    mimetype: file.mimetype,
    size: file.size,
    path: file.path,
    destination: file.destination
  };
};

const deleteFile = (filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      logger.info(`File deleted: ${filePath}`);
      return true;
    }
    return false;
  } catch (error) {
    logger.error(`Error deleting file ${filePath}:`, error);
    return false;
  }
};

const moveFile = (sourcePath, destinationPath) => {
  try {
    // Ensure destination directory exists
    const destDir = path.dirname(destinationPath);
    if (!fs.existsSync(destDir)) {
      fs.mkdirSync(destDir, { recursive: true });
    }
    
    fs.renameSync(sourcePath, destinationPath);
    logger.info(`File moved from ${sourcePath} to ${destinationPath}`);
    return true;
  } catch (error) {
    logger.error(`Error moving file from ${sourcePath} to ${destinationPath}:`, error);
    return false;
  }
};

const validateFileSize = (file, maxSize) => {
  return file.size <= maxSize;
};

const getFileExtension = (filename) => {
  return path.extname(filename).toLowerCase();
};

const generateThumbnail = async (imagePath, thumbnailPath, width = 300, height = 300) => {
  // This would require a library like sharp for image processing
  // For now, return a placeholder implementation
  try {
    // const sharp = require('sharp');
    // await sharp(imagePath)
    //   .resize(width, height, { fit: 'cover' })
    //   .jpeg({ quality: 80 })
    //   .toFile(thumbnailPath);
    
    logger.info(`Thumbnail generation placeholder for ${imagePath}`);
    return true;
  } catch (error) {
    logger.error('Error generating thumbnail:', error);
    return false;
  }
};

// Export configured upload middleware and utilities
module.exports = {
  upload,
  handleMulterError,
  getFileInfo,
  deleteFile,
  moveFile,
  validateFileSize,
  getFileExtension,
  generateThumbnail,
  fileTypes,
  uploadDirs,
  
  // Convenience methods for different file types
  single: (fieldName) => upload.single(fieldName),
  array: (fieldName, maxCount) => upload.array(fieldName, maxCount),
  fields: (fields) => upload.fields(fields),
  
  // Specific upload configurations
  modelUpload: upload.single('model'),
  imageUpload: upload.single('image'),
  imagesUpload: upload.array('images', 10),
  mixedUpload: upload.fields([
    { name: 'model', maxCount: 1 },
    { name: 'images', maxCount: 10 }
  ])
};
