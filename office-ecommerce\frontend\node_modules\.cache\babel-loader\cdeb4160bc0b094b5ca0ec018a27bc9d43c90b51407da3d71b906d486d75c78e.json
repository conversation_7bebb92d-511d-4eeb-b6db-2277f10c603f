{"ast": null, "code": "import * as React from 'react';\n\n/* eslint-disable react-hooks/rules-of-hooks */\nfunction useContextBridge(...contexts) {\n  const cRef = React.useRef([]);\n  cRef.current = contexts.map(context => React.useContext(context));\n  return React.useMemo(() => ({\n    children\n  }) => contexts.reduceRight((acc, Context, i) => /*#__PURE__*/React.createElement(Context.Provider, {\n    value: cRef.current[i],\n    children: acc\n  }), children\n  /*\n   * done this way in reference to:\n   * https://github.com/DefinitelyTyped/DefinitelyTyped/issues/44572#issuecomment-625878049\n   * https://github.com/microsoft/TypeScript/issues/14729\n   */), []);\n}\nexport { useContextBridge };", "map": {"version": 3, "names": ["React", "useContextBridge", "contexts", "cRef", "useRef", "current", "map", "context", "useContext", "useMemo", "children", "reduceRight", "acc", "Context", "i", "createElement", "Provider", "value"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useContextBridge.js"], "sourcesContent": ["import * as React from 'react';\n\n/* eslint-disable react-hooks/rules-of-hooks */\nfunction useContextBridge(...contexts) {\n  const cRef = React.useRef([]);\n  cRef.current = contexts.map(context => React.useContext(context));\n  return React.useMemo(() => ({\n    children\n  }) => contexts.reduceRight((acc, Context, i) => /*#__PURE__*/React.createElement(Context.Provider, {\n    value: cRef.current[i],\n    children: acc\n  }), children\n  /*\n   * done this way in reference to:\n   * https://github.com/DefinitelyTyped/DefinitelyTyped/issues/44572#issuecomment-625878049\n   * https://github.com/microsoft/TypeScript/issues/14729\n   */\n  ), []);\n}\n\nexport { useContextBridge };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA,SAASC,gBAAgBA,CAAC,GAAGC,QAAQ,EAAE;EACrC,MAAMC,IAAI,GAAGH,KAAK,CAACI,MAAM,CAAC,EAAE,CAAC;EAC7BD,IAAI,CAACE,OAAO,GAAGH,QAAQ,CAACI,GAAG,CAACC,OAAO,IAAIP,KAAK,CAACQ,UAAU,CAACD,OAAO,CAAC,CAAC;EACjE,OAAOP,KAAK,CAACS,OAAO,CAAC,MAAM,CAAC;IAC1BC;EACF,CAAC,KAAKR,QAAQ,CAACS,WAAW,CAAC,CAACC,GAAG,EAAEC,OAAO,EAAEC,CAAC,KAAK,aAAad,KAAK,CAACe,aAAa,CAACF,OAAO,CAACG,QAAQ,EAAE;IACjGC,KAAK,EAAEd,IAAI,CAACE,OAAO,CAACS,CAAC,CAAC;IACtBJ,QAAQ,EAAEE;EACZ,CAAC,CAAC,EAAEF;EACJ;AACF;AACA;AACA;AACA,KACE,CAAC,EAAE,EAAE,CAAC;AACR;AAEA,SAAST,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}