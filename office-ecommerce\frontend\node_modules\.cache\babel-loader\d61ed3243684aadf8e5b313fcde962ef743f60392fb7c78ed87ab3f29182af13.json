{"ast": null, "code": "import { useLoader } from '@react-three/fiber';\nimport { CubeReflectionMapping, EquirectangularReflectionMapping, CubeTextureLoader } from 'three';\nimport { RGBELoader, EXRLoader } from 'three-stdlib';\nimport { presetsObj } from '../helpers/environment-assets.js';\nconst CUBEMAP_ROOT = 'https://raw.githack.com/pmndrs/drei-assets/456060a26bbeb8fdf79326f224b6d99b8bcce736/hdri/';\nconst isArray = arr => Array.isArray(arr);\nfunction useEnvironment({\n  files = ['/px.png', '/nx.png', '/py.png', '/ny.png', '/pz.png', '/nz.png'],\n  path = '',\n  preset = undefined,\n  encoding = undefined,\n  extensions\n} = {}) {\n  var _files$split$pop, _files$split$pop$spli, _files$split$pop$spli2;\n  let loader = null;\n  let isCubeMap = false;\n  let extension;\n  if (preset) {\n    if (!(preset in presetsObj)) throw new Error('Preset must be one of: ' + Object.keys(presetsObj).join(', '));\n    files = presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  } // Everything else\n\n  isCubeMap = isArray(files);\n  extension = isArray(files) ? 'cube' : files.startsWith('data:application/exr') ? 'exr' : files.startsWith('data:application/hdr') ? 'hdr' : (_files$split$pop = files.split('.').pop()) == null ? void 0 : (_files$split$pop$spli = _files$split$pop.split('?')) == null ? void 0 : (_files$split$pop$spli2 = _files$split$pop$spli.shift()) == null ? void 0 : _files$split$pop$spli2.toLowerCase();\n  loader = isCubeMap ? CubeTextureLoader : extension === 'hdr' ? RGBELoader : extension === 'exr' ? EXRLoader : null;\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  const loaderResult = useLoader(\n  // @ts-expect-error\n  loader, isCubeMap ? [files] : files, loader => {\n    loader.setPath == null ? void 0 : loader.setPath(path);\n    if (extensions) extensions(loader);\n  });\n  const texture = isCubeMap ?\n  // @ts-ignore\n  loaderResult[0] : loaderResult;\n  texture.mapping = isCubeMap ? CubeReflectionMapping : EquirectangularReflectionMapping;\n  const sRGBEncoding = 3001;\n  const LinearEncoding = 3000;\n  if ('colorSpace' in texture) texture.colorSpace = (encoding !== null && encoding !== void 0 ? encoding : isCubeMap) ? 'srgb' : 'srgb-linear';else texture.encoding = (encoding !== null && encoding !== void 0 ? encoding : isCubeMap) ? sRGBEncoding : LinearEncoding;\n  return texture;\n}\nexport { useEnvironment };", "map": {"version": 3, "names": ["useLoader", "CubeReflectionMapping", "EquirectangularReflectionMapping", "CubeTextureLoader", "RGBELoader", "EXRLoader", "presetsObj", "CUBEMAP_ROOT", "isArray", "arr", "Array", "useEnvironment", "files", "path", "preset", "undefined", "encoding", "extensions", "_files$split$pop", "_files$split$pop$spli", "_files$split$pop$spli2", "loader", "isCubeMap", "extension", "Error", "Object", "keys", "join", "startsWith", "split", "pop", "shift", "toLowerCase", "loaderResult", "set<PERSON>ath", "texture", "mapping", "sRGBEncoding", "LinearEncoding", "colorSpace"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useEnvironment.js"], "sourcesContent": ["import { useLoader } from '@react-three/fiber';\nimport { CubeReflectionMapping, EquirectangularReflectionMapping, CubeTextureLoader } from 'three';\nimport { RGBELoader, EXRLoader } from 'three-stdlib';\nimport { presetsObj } from '../helpers/environment-assets.js';\n\nconst CUBEMAP_ROOT = 'https://raw.githack.com/pmndrs/drei-assets/456060a26bbeb8fdf79326f224b6d99b8bcce736/hdri/';\n\nconst isArray = arr => Array.isArray(arr);\n\nfunction useEnvironment({\n  files = ['/px.png', '/nx.png', '/py.png', '/ny.png', '/pz.png', '/nz.png'],\n  path = '',\n  preset = undefined,\n  encoding = undefined,\n  extensions\n} = {}) {\n  var _files$split$pop, _files$split$pop$spli, _files$split$pop$spli2;\n\n  let loader = null;\n  let isCubeMap = false;\n  let extension;\n\n  if (preset) {\n    if (!(preset in presetsObj)) throw new Error('Preset must be one of: ' + Object.keys(presetsObj).join(', '));\n    files = presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  } // Everything else\n\n\n  isCubeMap = isArray(files);\n  extension = isArray(files) ? 'cube' : files.startsWith('data:application/exr') ? 'exr' : files.startsWith('data:application/hdr') ? 'hdr' : (_files$split$pop = files.split('.').pop()) == null ? void 0 : (_files$split$pop$spli = _files$split$pop.split('?')) == null ? void 0 : (_files$split$pop$spli2 = _files$split$pop$spli.shift()) == null ? void 0 : _files$split$pop$spli2.toLowerCase();\n  loader = isCubeMap ? CubeTextureLoader : extension === 'hdr' ? RGBELoader : extension === 'exr' ? EXRLoader : null;\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  const loaderResult = useLoader( // @ts-expect-error\n  loader, isCubeMap ? [files] : files, loader => {\n    loader.setPath == null ? void 0 : loader.setPath(path);\n    if (extensions) extensions(loader);\n  });\n  const texture = isCubeMap ? // @ts-ignore\n  loaderResult[0] : loaderResult;\n  texture.mapping = isCubeMap ? CubeReflectionMapping : EquirectangularReflectionMapping;\n  const sRGBEncoding = 3001;\n  const LinearEncoding = 3000;\n  if ('colorSpace' in texture) texture.colorSpace = (encoding !== null && encoding !== void 0 ? encoding : isCubeMap) ? 'srgb' : 'srgb-linear';else texture.encoding = (encoding !== null && encoding !== void 0 ? encoding : isCubeMap) ? sRGBEncoding : LinearEncoding;\n  return texture;\n}\n\nexport { useEnvironment };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,qBAAqB,EAAEC,gCAAgC,EAAEC,iBAAiB,QAAQ,OAAO;AAClG,SAASC,UAAU,EAAEC,SAAS,QAAQ,cAAc;AACpD,SAASC,UAAU,QAAQ,kCAAkC;AAE7D,MAAMC,YAAY,GAAG,2FAA2F;AAEhH,MAAMC,OAAO,GAAGC,GAAG,IAAIC,KAAK,CAACF,OAAO,CAACC,GAAG,CAAC;AAEzC,SAASE,cAAcA,CAAC;EACtBC,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAC1EC,IAAI,GAAG,EAAE;EACTC,MAAM,GAAGC,SAAS;EAClBC,QAAQ,GAAGD,SAAS;EACpBE;AACF,CAAC,GAAG,CAAC,CAAC,EAAE;EACN,IAAIC,gBAAgB,EAAEC,qBAAqB,EAAEC,sBAAsB;EAEnE,IAAIC,MAAM,GAAG,IAAI;EACjB,IAAIC,SAAS,GAAG,KAAK;EACrB,IAAIC,SAAS;EAEb,IAAIT,MAAM,EAAE;IACV,IAAI,EAAEA,MAAM,IAAIR,UAAU,CAAC,EAAE,MAAM,IAAIkB,KAAK,CAAC,yBAAyB,GAAGC,MAAM,CAACC,IAAI,CAACpB,UAAU,CAAC,CAACqB,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5Gf,KAAK,GAAGN,UAAU,CAACQ,MAAM,CAAC;IAC1BD,IAAI,GAAGN,YAAY;EACrB,CAAC,CAAC;;EAGFe,SAAS,GAAGd,OAAO,CAACI,KAAK,CAAC;EAC1BW,SAAS,GAAGf,OAAO,CAACI,KAAK,CAAC,GAAG,MAAM,GAAGA,KAAK,CAACgB,UAAU,CAAC,sBAAsB,CAAC,GAAG,KAAK,GAAGhB,KAAK,CAACgB,UAAU,CAAC,sBAAsB,CAAC,GAAG,KAAK,GAAG,CAACV,gBAAgB,GAAGN,KAAK,CAACiB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAACX,qBAAqB,GAAGD,gBAAgB,CAACW,KAAK,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAACT,sBAAsB,GAAGD,qBAAqB,CAACY,KAAK,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGX,sBAAsB,CAACY,WAAW,CAAC,CAAC;EACpYX,MAAM,GAAGC,SAAS,GAAGnB,iBAAiB,GAAGoB,SAAS,KAAK,KAAK,GAAGnB,UAAU,GAAGmB,SAAS,KAAK,KAAK,GAAGlB,SAAS,GAAG,IAAI;EAClH,IAAI,CAACgB,MAAM,EAAE,MAAM,IAAIG,KAAK,CAAC,+CAA+C,GAAGZ,KAAK,CAAC;EACrF,MAAMqB,YAAY,GAAGjC,SAAS;EAAE;EAChCqB,MAAM,EAAEC,SAAS,GAAG,CAACV,KAAK,CAAC,GAAGA,KAAK,EAAES,MAAM,IAAI;IAC7CA,MAAM,CAACa,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGb,MAAM,CAACa,OAAO,CAACrB,IAAI,CAAC;IACtD,IAAII,UAAU,EAAEA,UAAU,CAACI,MAAM,CAAC;EACpC,CAAC,CAAC;EACF,MAAMc,OAAO,GAAGb,SAAS;EAAG;EAC5BW,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY;EAC9BE,OAAO,CAACC,OAAO,GAAGd,SAAS,GAAGrB,qBAAqB,GAAGC,gCAAgC;EACtF,MAAMmC,YAAY,GAAG,IAAI;EACzB,MAAMC,cAAc,GAAG,IAAI;EAC3B,IAAI,YAAY,IAAIH,OAAO,EAAEA,OAAO,CAACI,UAAU,GAAG,CAACvB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGM,SAAS,IAAI,MAAM,GAAG,aAAa,CAAC,KAAKa,OAAO,CAACnB,QAAQ,GAAG,CAACA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGM,SAAS,IAAIe,YAAY,GAAGC,cAAc;EACtQ,OAAOH,OAAO;AAChB;AAEA,SAASxB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}