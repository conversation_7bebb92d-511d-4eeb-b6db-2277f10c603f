import type { PerspectiveCamera, OrthographicCamera } from 'three';
import * as React from 'react';
import { ReactThreeFiber } from '@react-three/fiber';
import CameraControlsImpl from 'camera-controls';
export declare type CameraControlsProps = Omit<ReactThreeFiber.Overwrite<ReactThreeFiber.Node<CameraControlsImpl, typeof CameraControlsImpl>, {
    camera?: PerspectiveCamera | OrthographicCamera;
    domElement?: HTMLElement;
    makeDefault?: boolean;
    onStart?: (e?: {
        type: 'controlstart';
    }) => void;
    onEnd?: (e?: {
        type: 'controlend';
    }) => void;
    onChange?: (e?: {
        type: 'update';
    }) => void;
    events?: boolean;
    regress?: boolean;
}>, 'ref'>;
export declare const CameraControls: React.ForwardRefExoticComponent<CameraControlsProps & React.RefAttributes<CameraControlsImpl>>;
export declare type CameraControls = CameraControlsImpl;
