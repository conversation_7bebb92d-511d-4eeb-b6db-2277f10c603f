{"ast": null, "code": "// Note that a struct cannot be used for the hit record including faceIndices, faceNormal, barycoord,\n// side, and dist because on some mobile GPUS (such as Adreno) numbers are afforded less precision specifically\n// when in a struct leading to inaccurate hit results. See KhronosGroup/WebGL#3351 for more details.\nexport const shaderStructs = /* glsl */`\n#ifndef TRI_INTERSECT_EPSILON\n#define TRI_INTERSECT_EPSILON 1e-5\n#endif\n\n#ifndef INFINITY\n#define INFINITY 1e20\n#endif\n\nstruct BVH {\n\n\tusampler2D index;\n\tsampler2D position;\n\n\tsampler2D bvhBounds;\n\tusampler2D bvhContents;\n\n};\n`;\nexport const shaderIntersectFunction = /* glsl */`\n\n// Utilities\nuvec4 uTexelFetch1D( usampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nivec4 iTexelFetch1D( isampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nvec4 texelFetch1D( sampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nvec4 textureSampleBarycoord( sampler2D tex, vec3 barycoord, uvec3 faceIndices ) {\n\n\treturn\n\t\tbarycoord.x * texelFetch1D( tex, faceIndices.x ) +\n\t\tbarycoord.y * texelFetch1D( tex, faceIndices.y ) +\n\t\tbarycoord.z * texelFetch1D( tex, faceIndices.z );\n\n}\n\nvoid ndcToCameraRay(\n\tvec2 coord, mat4 cameraWorld, mat4 invProjectionMatrix,\n\tout vec3 rayOrigin, out vec3 rayDirection\n) {\n\n\t// get camera look direction and near plane for camera clipping\n\tvec4 lookDirection = cameraWorld * vec4( 0.0, 0.0, - 1.0, 0.0 );\n\tvec4 nearVector = invProjectionMatrix * vec4( 0.0, 0.0, - 1.0, 1.0 );\n\tfloat near = abs( nearVector.z / nearVector.w );\n\n\t// get the camera direction and position from camera matrices\n\tvec4 origin = cameraWorld * vec4( 0.0, 0.0, 0.0, 1.0 );\n\tvec4 direction = invProjectionMatrix * vec4( coord, 0.5, 1.0 );\n\tdirection /= direction.w;\n\tdirection = cameraWorld * direction - origin;\n\n\t// slide the origin along the ray until it sits at the near clip plane position\n\torigin.xyz += direction.xyz * near / dot( direction, lookDirection );\n\n\trayOrigin = origin.xyz;\n\trayDirection = direction.xyz;\n\n}\n\n// Raycasting\nfloat intersectsBounds( vec3 rayOrigin, vec3 rayDirection, vec3 boundsMin, vec3 boundsMax ) {\n\n\t// https://www.reddit.com/r/opengl/comments/8ntzz5/fast_glsl_ray_box_intersection/\n\t// https://tavianator.com/2011/ray_box.html\n\tvec3 invDir = 1.0 / rayDirection;\n\n\t// find intersection distances for each plane\n\tvec3 tMinPlane = invDir * ( boundsMin - rayOrigin );\n\tvec3 tMaxPlane = invDir * ( boundsMax - rayOrigin );\n\n\t// get the min and max distances from each intersection\n\tvec3 tMinHit = min( tMaxPlane, tMinPlane );\n\tvec3 tMaxHit = max( tMaxPlane, tMinPlane );\n\n\t// get the furthest hit distance\n\tvec2 t = max( tMinHit.xx, tMinHit.yz );\n\tfloat t0 = max( t.x, t.y );\n\n\t// get the minimum hit distance\n\tt = min( tMaxHit.xx, tMaxHit.yz );\n\tfloat t1 = min( t.x, t.y );\n\n\t// set distance to 0.0 if the ray starts inside the box\n\tfloat dist = max( t0, 0.0 );\n\n\treturn t1 >= dist ? dist : INFINITY;\n\n}\n\nbool intersectsTriangle(\n\tvec3 rayOrigin, vec3 rayDirection, vec3 a, vec3 b, vec3 c,\n\tout vec3 barycoord, out vec3 norm, out float dist, out float side\n) {\n\n\t// https://stackoverflow.com/questions/42740765/intersection-between-line-and-triangle-in-3d\n\tvec3 edge1 = b - a;\n\tvec3 edge2 = c - a;\n\tnorm = cross( edge1, edge2 );\n\n\tfloat det = - dot( rayDirection, norm );\n\tfloat invdet = 1.0 / det;\n\n\tvec3 AO = rayOrigin - a;\n\tvec3 DAO = cross( AO, rayDirection );\n\n\tvec4 uvt;\n\tuvt.x = dot( edge2, DAO ) * invdet;\n\tuvt.y = - dot( edge1, DAO ) * invdet;\n\tuvt.z = dot( AO, norm ) * invdet;\n\tuvt.w = 1.0 - uvt.x - uvt.y;\n\n\t// set the hit information\n\tbarycoord = uvt.wxy; // arranged in A, B, C order\n\tdist = uvt.z;\n\tside = sign( det );\n\tnorm = side * normalize( norm );\n\n\t// add an epsilon to avoid misses between triangles\n\tuvt += vec4( TRI_INTERSECT_EPSILON );\n\n\treturn all( greaterThanEqual( uvt, vec4( 0.0 ) ) );\n\n}\n\nbool intersectTriangles(\n\tBVH bvh, vec3 rayOrigin, vec3 rayDirection, uint offset, uint count,\n\tinout float minDistance,\n\n\t// output variables\n\tout uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord,\n\tout float side, out float dist\n) {\n\n\tbool found = false;\n\tvec3 localBarycoord, localNormal;\n\tfloat localDist, localSide;\n\tfor ( uint i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\tuvec3 indices = uTexelFetch1D( bvh.index, i ).xyz;\n\t\tvec3 a = texelFetch1D( bvh.position, indices.x ).rgb;\n\t\tvec3 b = texelFetch1D( bvh.position, indices.y ).rgb;\n\t\tvec3 c = texelFetch1D( bvh.position, indices.z ).rgb;\n\n\t\tif (\n\t\t\tintersectsTriangle( rayOrigin, rayDirection, a, b, c, localBarycoord, localNormal, localDist, localSide )\n\t\t\t&& localDist < minDistance\n\t\t) {\n\n\t\t\tfound = true;\n\t\t\tminDistance = localDist;\n\n\t\t\tfaceIndices = uvec4( indices.xyz, i );\n\t\t\tfaceNormal = localNormal;\n\n\t\t\tside = localSide;\n\t\t\tbarycoord = localBarycoord;\n\t\t\tdist = localDist;\n\n\t\t}\n\n\t}\n\n\treturn found;\n\n}\n\nfloat intersectsBVHNodeBounds( vec3 rayOrigin, vec3 rayDirection, BVH bvh, uint currNodeIndex ) {\n\n\tvec3 boundsMin = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 0u ).xyz;\n\tvec3 boundsMax = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 1u ).xyz;\n\treturn intersectsBounds( rayOrigin, rayDirection, boundsMin, boundsMax );\n\n}\n\nbool bvhIntersectFirstHit(\n\tBVH bvh, vec3 rayOrigin, vec3 rayDirection,\n\n\t// output variables\n\tout uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord,\n\tout float side, out float dist\n) {\n\n\t// stack needs to be twice as long as the deepest tree we expect because\n\t// we push both the left and right child onto the stack every traversal\n\tint ptr = 0;\n\tuint stack[ 60 ];\n\tstack[ 0 ] = 0u;\n\n\tfloat triangleDistance = 1e20;\n\tbool found = false;\n\twhile ( ptr > - 1 && ptr < 60 ) {\n\n\t\tuint currNodeIndex = stack[ ptr ];\n\t\tptr --;\n\n\t\t// check if we intersect the current bounds\n\t\tfloat boundsHitDistance = intersectsBVHNodeBounds( rayOrigin, rayDirection, bvh, currNodeIndex );\n\t\tif ( boundsHitDistance == INFINITY || boundsHitDistance > triangleDistance ) {\n\n\t\t\tcontinue;\n\n\t\t}\n\n\t\tuvec2 boundsInfo = uTexelFetch1D( bvh.bvhContents, currNodeIndex ).xy;\n\t\tbool isLeaf = bool( boundsInfo.x & 0xffff0000u );\n\n\t\tif ( isLeaf ) {\n\n\t\t\tuint count = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint offset = boundsInfo.y;\n\n\t\t\tfound = intersectTriangles(\n\t\t\t\tbvh, rayOrigin, rayDirection, offset, count, triangleDistance,\n\t\t\t\tfaceIndices, faceNormal, barycoord, side, dist\n\t\t\t) || found;\n\n\t\t} else {\n\n\t\t\tuint leftIndex = currNodeIndex + 1u;\n\t\t\tuint splitAxis = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint rightIndex = boundsInfo.y;\n\n\t\t\tbool leftToRight = rayDirection[ splitAxis ] >= 0.0;\n\t\t\tuint c1 = leftToRight ? leftIndex : rightIndex;\n\t\t\tuint c2 = leftToRight ? rightIndex : leftIndex;\n\n\t\t\t// set c2 in the stack so we traverse it later. We need to keep track of a pointer in\n\t\t\t// the stack while we traverse. The second pointer added is the one that will be\n\t\t\t// traversed first\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c2;\n\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c1;\n\n\t\t}\n\n\t}\n\n\treturn found;\n\n}\n`;\n\n// Distance to Point\nexport const shaderDistanceFunction = /* glsl */`\n\nfloat dot2( in vec3 v ) {\n\n\treturn dot( v, v );\n\n}\n\n\n// https://www.shadertoy.com/view/ttfGWl\nvec3 closestPointToTriangle( vec3 p, vec3 v0, vec3 v1, vec3 v2, out vec3 barycoord ) {\n\n    vec3 v10 = v1 - v0;\n    vec3 v21 = v2 - v1;\n    vec3 v02 = v0 - v2;\n\n\tvec3 p0 = p - v0;\n\tvec3 p1 = p - v1;\n\tvec3 p2 = p - v2;\n\n    vec3 nor = cross( v10, v02 );\n\n    // method 2, in barycentric space\n    vec3  q = cross( nor, p0 );\n    float d = 1.0 / dot2( nor );\n    float u = d * dot( q, v02 );\n    float v = d * dot( q, v10 );\n    float w = 1.0 - u - v;\n\n\tif( u < 0.0 ) {\n\n\t\tw = clamp( dot( p2, v02 ) / dot2( v02 ), 0.0, 1.0 );\n\t\tu = 0.0;\n\t\tv = 1.0 - w;\n\n\t} else if( v < 0.0 ) {\n\n\t\tu = clamp( dot( p0, v10 ) / dot2( v10 ), 0.0, 1.0 );\n\t\tv = 0.0;\n\t\tw = 1.0 - u;\n\n\t} else if( w < 0.0 ) {\n\n\t\tv = clamp( dot( p1, v21 ) / dot2( v21 ), 0.0, 1.0 );\n\t\tw = 0.0;\n\t\tu = 1.0-v;\n\n\t}\n\n\tbarycoord = vec3( u, v, w );\n    return u * v1 + v * v2 + w * v0;\n\n}\n\nfloat distanceToTriangles(\n\tBVH bvh, vec3 point, uint offset, uint count, float closestDistanceSquared,\n\n\tout uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord, out float side, out vec3 outPoint\n) {\n\n\tbool found = false;\n\tuvec3 localIndices;\n\tvec3 localBarycoord;\n\tvec3 localNormal;\n\tfor ( uint i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\tuvec3 indices = uTexelFetch1D( bvh.index, i ).xyz;\n\t\tvec3 a = texelFetch1D( bvh.position, indices.x ).rgb;\n\t\tvec3 b = texelFetch1D( bvh.position, indices.y ).rgb;\n\t\tvec3 c = texelFetch1D( bvh.position, indices.z ).rgb;\n\n\t\t// get the closest point and barycoord\n\t\tvec3 closestPoint = closestPointToTriangle( point, a, b, c, localBarycoord );\n\t\tvec3 delta = point - closestPoint;\n\t\tfloat sqDist = dot2( delta );\n\t\tif ( sqDist < closestDistanceSquared ) {\n\n\t\t\t// set the output results\n\t\t\tclosestDistanceSquared = sqDist;\n\t\t\tfaceIndices = uvec4( indices.xyz, i );\n\t\t\tfaceNormal = normalize( cross( a - b, b - c ) );\n\t\t\tbarycoord = localBarycoord;\n\t\t\toutPoint = closestPoint;\n\t\t\tside = sign( dot( faceNormal, delta ) );\n\n\t\t}\n\n\t}\n\n\treturn closestDistanceSquared;\n\n}\n\nfloat distanceSqToBounds( vec3 point, vec3 boundsMin, vec3 boundsMax ) {\n\n\tvec3 clampedPoint = clamp( point, boundsMin, boundsMax );\n\tvec3 delta = point - clampedPoint;\n\treturn dot( delta, delta );\n\n}\n\nfloat distanceSqToBVHNodeBoundsPoint( vec3 point, BVH bvh, uint currNodeIndex ) {\n\n\tvec3 boundsMin = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 0u ).xyz;\n\tvec3 boundsMax = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 1u ).xyz;\n\treturn distanceSqToBounds( point, boundsMin, boundsMax );\n\n}\n\nfloat bvhClosestPointToPoint(\n\tBVH bvh, vec3 point,\n\n\t// output variables\n\tout uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord,\n\tout float side, out vec3 outPoint\n ) {\n\n\t// stack needs to be twice as long as the deepest tree we expect because\n\t// we push both the left and right child onto the stack every traversal\n\tint ptr = 0;\n\tuint stack[ 60 ];\n\tstack[ 0 ] = 0u;\n\tfloat closestDistanceSquared = pow( 100000.0, 2.0 );\n\tbool found = false;\n\twhile ( ptr > - 1 && ptr < 60 ) {\n\n\t\tuint currNodeIndex = stack[ ptr ];\n\t\tptr --;\n\n\t\t// check if we intersect the current bounds\n\t\tfloat boundsHitDistance = distanceSqToBVHNodeBoundsPoint( point, bvh, currNodeIndex );\n\t\tif ( boundsHitDistance > closestDistanceSquared ) {\n\n\t\t\tcontinue;\n\n\t\t}\n\n\t\tuvec2 boundsInfo = uTexelFetch1D( bvh.bvhContents, currNodeIndex ).xy;\n\t\tbool isLeaf = bool( boundsInfo.x & 0xffff0000u );\n\t\tif ( isLeaf ) {\n\n\t\t\tuint count = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint offset = boundsInfo.y;\n\t\t\tclosestDistanceSquared = distanceToTriangles(\n\t\t\t\tbvh, point, offset, count, closestDistanceSquared,\n\n\t\t\t\t// outputs\n\t\t\t\tfaceIndices, faceNormal, barycoord, side, outPoint\n\t\t\t);\n\n\t\t} else {\n\n\t\t\tuint leftIndex = currNodeIndex + 1u;\n\t\t\tuint splitAxis = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint rightIndex = boundsInfo.y;\n\t\t\tbool leftToRight = distanceSqToBVHNodeBoundsPoint( point, bvh, leftIndex ) < distanceSqToBVHNodeBoundsPoint( point, bvh, rightIndex );//rayDirection[ splitAxis ] >= 0.0;\n\t\t\tuint c1 = leftToRight ? leftIndex : rightIndex;\n\t\t\tuint c2 = leftToRight ? rightIndex : leftIndex;\n\n\t\t\t// set c2 in the stack so we traverse it later. We need to keep track of a pointer in\n\t\t\t// the stack while we traverse. The second pointer added is the one that will be\n\t\t\t// traversed first\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c2;\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c1;\n\n\t\t}\n\n\t}\n\n\treturn sqrt( closestDistanceSquared );\n\n}\n`;", "map": {"version": 3, "names": ["shaderStructs", "shaderIntersectFunction", "shaderDistanceFunction"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/three-mesh-bvh/src/gpu/shaderFunctions.js"], "sourcesContent": ["// Note that a struct cannot be used for the hit record including faceIndices, faceNormal, barycoord,\n// side, and dist because on some mobile GPUS (such as Adreno) numbers are afforded less precision specifically\n// when in a struct leading to inaccurate hit results. See KhronosGroup/WebGL#3351 for more details.\nexport const shaderStructs = /* glsl */`\n#ifndef TRI_INTERSECT_EPSILON\n#define TRI_INTERSECT_EPSILON 1e-5\n#endif\n\n#ifndef INFINITY\n#define INFINITY 1e20\n#endif\n\nstruct BVH {\n\n\tusampler2D index;\n\tsampler2D position;\n\n\tsampler2D bvhBounds;\n\tusampler2D bvhContents;\n\n};\n`;\n\nexport const shaderIntersectFunction = /* glsl */`\n\n// Utilities\nuvec4 uTexelFetch1D( usampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nivec4 iTexelFetch1D( isampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nvec4 texelFetch1D( sampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nvec4 textureSampleBarycoord( sampler2D tex, vec3 barycoord, uvec3 faceIndices ) {\n\n\treturn\n\t\tbarycoord.x * texelFetch1D( tex, faceIndices.x ) +\n\t\tbarycoord.y * texelFetch1D( tex, faceIndices.y ) +\n\t\tbarycoord.z * texelFetch1D( tex, faceIndices.z );\n\n}\n\nvoid ndcToCameraRay(\n\tvec2 coord, mat4 cameraWorld, mat4 invProjectionMatrix,\n\tout vec3 rayOrigin, out vec3 rayDirection\n) {\n\n\t// get camera look direction and near plane for camera clipping\n\tvec4 lookDirection = cameraWorld * vec4( 0.0, 0.0, - 1.0, 0.0 );\n\tvec4 nearVector = invProjectionMatrix * vec4( 0.0, 0.0, - 1.0, 1.0 );\n\tfloat near = abs( nearVector.z / nearVector.w );\n\n\t// get the camera direction and position from camera matrices\n\tvec4 origin = cameraWorld * vec4( 0.0, 0.0, 0.0, 1.0 );\n\tvec4 direction = invProjectionMatrix * vec4( coord, 0.5, 1.0 );\n\tdirection /= direction.w;\n\tdirection = cameraWorld * direction - origin;\n\n\t// slide the origin along the ray until it sits at the near clip plane position\n\torigin.xyz += direction.xyz * near / dot( direction, lookDirection );\n\n\trayOrigin = origin.xyz;\n\trayDirection = direction.xyz;\n\n}\n\n// Raycasting\nfloat intersectsBounds( vec3 rayOrigin, vec3 rayDirection, vec3 boundsMin, vec3 boundsMax ) {\n\n\t// https://www.reddit.com/r/opengl/comments/8ntzz5/fast_glsl_ray_box_intersection/\n\t// https://tavianator.com/2011/ray_box.html\n\tvec3 invDir = 1.0 / rayDirection;\n\n\t// find intersection distances for each plane\n\tvec3 tMinPlane = invDir * ( boundsMin - rayOrigin );\n\tvec3 tMaxPlane = invDir * ( boundsMax - rayOrigin );\n\n\t// get the min and max distances from each intersection\n\tvec3 tMinHit = min( tMaxPlane, tMinPlane );\n\tvec3 tMaxHit = max( tMaxPlane, tMinPlane );\n\n\t// get the furthest hit distance\n\tvec2 t = max( tMinHit.xx, tMinHit.yz );\n\tfloat t0 = max( t.x, t.y );\n\n\t// get the minimum hit distance\n\tt = min( tMaxHit.xx, tMaxHit.yz );\n\tfloat t1 = min( t.x, t.y );\n\n\t// set distance to 0.0 if the ray starts inside the box\n\tfloat dist = max( t0, 0.0 );\n\n\treturn t1 >= dist ? dist : INFINITY;\n\n}\n\nbool intersectsTriangle(\n\tvec3 rayOrigin, vec3 rayDirection, vec3 a, vec3 b, vec3 c,\n\tout vec3 barycoord, out vec3 norm, out float dist, out float side\n) {\n\n\t// https://stackoverflow.com/questions/42740765/intersection-between-line-and-triangle-in-3d\n\tvec3 edge1 = b - a;\n\tvec3 edge2 = c - a;\n\tnorm = cross( edge1, edge2 );\n\n\tfloat det = - dot( rayDirection, norm );\n\tfloat invdet = 1.0 / det;\n\n\tvec3 AO = rayOrigin - a;\n\tvec3 DAO = cross( AO, rayDirection );\n\n\tvec4 uvt;\n\tuvt.x = dot( edge2, DAO ) * invdet;\n\tuvt.y = - dot( edge1, DAO ) * invdet;\n\tuvt.z = dot( AO, norm ) * invdet;\n\tuvt.w = 1.0 - uvt.x - uvt.y;\n\n\t// set the hit information\n\tbarycoord = uvt.wxy; // arranged in A, B, C order\n\tdist = uvt.z;\n\tside = sign( det );\n\tnorm = side * normalize( norm );\n\n\t// add an epsilon to avoid misses between triangles\n\tuvt += vec4( TRI_INTERSECT_EPSILON );\n\n\treturn all( greaterThanEqual( uvt, vec4( 0.0 ) ) );\n\n}\n\nbool intersectTriangles(\n\tBVH bvh, vec3 rayOrigin, vec3 rayDirection, uint offset, uint count,\n\tinout float minDistance,\n\n\t// output variables\n\tout uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord,\n\tout float side, out float dist\n) {\n\n\tbool found = false;\n\tvec3 localBarycoord, localNormal;\n\tfloat localDist, localSide;\n\tfor ( uint i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\tuvec3 indices = uTexelFetch1D( bvh.index, i ).xyz;\n\t\tvec3 a = texelFetch1D( bvh.position, indices.x ).rgb;\n\t\tvec3 b = texelFetch1D( bvh.position, indices.y ).rgb;\n\t\tvec3 c = texelFetch1D( bvh.position, indices.z ).rgb;\n\n\t\tif (\n\t\t\tintersectsTriangle( rayOrigin, rayDirection, a, b, c, localBarycoord, localNormal, localDist, localSide )\n\t\t\t&& localDist < minDistance\n\t\t) {\n\n\t\t\tfound = true;\n\t\t\tminDistance = localDist;\n\n\t\t\tfaceIndices = uvec4( indices.xyz, i );\n\t\t\tfaceNormal = localNormal;\n\n\t\t\tside = localSide;\n\t\t\tbarycoord = localBarycoord;\n\t\t\tdist = localDist;\n\n\t\t}\n\n\t}\n\n\treturn found;\n\n}\n\nfloat intersectsBVHNodeBounds( vec3 rayOrigin, vec3 rayDirection, BVH bvh, uint currNodeIndex ) {\n\n\tvec3 boundsMin = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 0u ).xyz;\n\tvec3 boundsMax = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 1u ).xyz;\n\treturn intersectsBounds( rayOrigin, rayDirection, boundsMin, boundsMax );\n\n}\n\nbool bvhIntersectFirstHit(\n\tBVH bvh, vec3 rayOrigin, vec3 rayDirection,\n\n\t// output variables\n\tout uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord,\n\tout float side, out float dist\n) {\n\n\t// stack needs to be twice as long as the deepest tree we expect because\n\t// we push both the left and right child onto the stack every traversal\n\tint ptr = 0;\n\tuint stack[ 60 ];\n\tstack[ 0 ] = 0u;\n\n\tfloat triangleDistance = 1e20;\n\tbool found = false;\n\twhile ( ptr > - 1 && ptr < 60 ) {\n\n\t\tuint currNodeIndex = stack[ ptr ];\n\t\tptr --;\n\n\t\t// check if we intersect the current bounds\n\t\tfloat boundsHitDistance = intersectsBVHNodeBounds( rayOrigin, rayDirection, bvh, currNodeIndex );\n\t\tif ( boundsHitDistance == INFINITY || boundsHitDistance > triangleDistance ) {\n\n\t\t\tcontinue;\n\n\t\t}\n\n\t\tuvec2 boundsInfo = uTexelFetch1D( bvh.bvhContents, currNodeIndex ).xy;\n\t\tbool isLeaf = bool( boundsInfo.x & 0xffff0000u );\n\n\t\tif ( isLeaf ) {\n\n\t\t\tuint count = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint offset = boundsInfo.y;\n\n\t\t\tfound = intersectTriangles(\n\t\t\t\tbvh, rayOrigin, rayDirection, offset, count, triangleDistance,\n\t\t\t\tfaceIndices, faceNormal, barycoord, side, dist\n\t\t\t) || found;\n\n\t\t} else {\n\n\t\t\tuint leftIndex = currNodeIndex + 1u;\n\t\t\tuint splitAxis = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint rightIndex = boundsInfo.y;\n\n\t\t\tbool leftToRight = rayDirection[ splitAxis ] >= 0.0;\n\t\t\tuint c1 = leftToRight ? leftIndex : rightIndex;\n\t\t\tuint c2 = leftToRight ? rightIndex : leftIndex;\n\n\t\t\t// set c2 in the stack so we traverse it later. We need to keep track of a pointer in\n\t\t\t// the stack while we traverse. The second pointer added is the one that will be\n\t\t\t// traversed first\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c2;\n\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c1;\n\n\t\t}\n\n\t}\n\n\treturn found;\n\n}\n`;\n\n// Distance to Point\nexport const shaderDistanceFunction = /* glsl */`\n\nfloat dot2( in vec3 v ) {\n\n\treturn dot( v, v );\n\n}\n\n\n// https://www.shadertoy.com/view/ttfGWl\nvec3 closestPointToTriangle( vec3 p, vec3 v0, vec3 v1, vec3 v2, out vec3 barycoord ) {\n\n    vec3 v10 = v1 - v0;\n    vec3 v21 = v2 - v1;\n    vec3 v02 = v0 - v2;\n\n\tvec3 p0 = p - v0;\n\tvec3 p1 = p - v1;\n\tvec3 p2 = p - v2;\n\n    vec3 nor = cross( v10, v02 );\n\n    // method 2, in barycentric space\n    vec3  q = cross( nor, p0 );\n    float d = 1.0 / dot2( nor );\n    float u = d * dot( q, v02 );\n    float v = d * dot( q, v10 );\n    float w = 1.0 - u - v;\n\n\tif( u < 0.0 ) {\n\n\t\tw = clamp( dot( p2, v02 ) / dot2( v02 ), 0.0, 1.0 );\n\t\tu = 0.0;\n\t\tv = 1.0 - w;\n\n\t} else if( v < 0.0 ) {\n\n\t\tu = clamp( dot( p0, v10 ) / dot2( v10 ), 0.0, 1.0 );\n\t\tv = 0.0;\n\t\tw = 1.0 - u;\n\n\t} else if( w < 0.0 ) {\n\n\t\tv = clamp( dot( p1, v21 ) / dot2( v21 ), 0.0, 1.0 );\n\t\tw = 0.0;\n\t\tu = 1.0-v;\n\n\t}\n\n\tbarycoord = vec3( u, v, w );\n    return u * v1 + v * v2 + w * v0;\n\n}\n\nfloat distanceToTriangles(\n\tBVH bvh, vec3 point, uint offset, uint count, float closestDistanceSquared,\n\n\tout uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord, out float side, out vec3 outPoint\n) {\n\n\tbool found = false;\n\tuvec3 localIndices;\n\tvec3 localBarycoord;\n\tvec3 localNormal;\n\tfor ( uint i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\tuvec3 indices = uTexelFetch1D( bvh.index, i ).xyz;\n\t\tvec3 a = texelFetch1D( bvh.position, indices.x ).rgb;\n\t\tvec3 b = texelFetch1D( bvh.position, indices.y ).rgb;\n\t\tvec3 c = texelFetch1D( bvh.position, indices.z ).rgb;\n\n\t\t// get the closest point and barycoord\n\t\tvec3 closestPoint = closestPointToTriangle( point, a, b, c, localBarycoord );\n\t\tvec3 delta = point - closestPoint;\n\t\tfloat sqDist = dot2( delta );\n\t\tif ( sqDist < closestDistanceSquared ) {\n\n\t\t\t// set the output results\n\t\t\tclosestDistanceSquared = sqDist;\n\t\t\tfaceIndices = uvec4( indices.xyz, i );\n\t\t\tfaceNormal = normalize( cross( a - b, b - c ) );\n\t\t\tbarycoord = localBarycoord;\n\t\t\toutPoint = closestPoint;\n\t\t\tside = sign( dot( faceNormal, delta ) );\n\n\t\t}\n\n\t}\n\n\treturn closestDistanceSquared;\n\n}\n\nfloat distanceSqToBounds( vec3 point, vec3 boundsMin, vec3 boundsMax ) {\n\n\tvec3 clampedPoint = clamp( point, boundsMin, boundsMax );\n\tvec3 delta = point - clampedPoint;\n\treturn dot( delta, delta );\n\n}\n\nfloat distanceSqToBVHNodeBoundsPoint( vec3 point, BVH bvh, uint currNodeIndex ) {\n\n\tvec3 boundsMin = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 0u ).xyz;\n\tvec3 boundsMax = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 1u ).xyz;\n\treturn distanceSqToBounds( point, boundsMin, boundsMax );\n\n}\n\nfloat bvhClosestPointToPoint(\n\tBVH bvh, vec3 point,\n\n\t// output variables\n\tout uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord,\n\tout float side, out vec3 outPoint\n ) {\n\n\t// stack needs to be twice as long as the deepest tree we expect because\n\t// we push both the left and right child onto the stack every traversal\n\tint ptr = 0;\n\tuint stack[ 60 ];\n\tstack[ 0 ] = 0u;\n\tfloat closestDistanceSquared = pow( 100000.0, 2.0 );\n\tbool found = false;\n\twhile ( ptr > - 1 && ptr < 60 ) {\n\n\t\tuint currNodeIndex = stack[ ptr ];\n\t\tptr --;\n\n\t\t// check if we intersect the current bounds\n\t\tfloat boundsHitDistance = distanceSqToBVHNodeBoundsPoint( point, bvh, currNodeIndex );\n\t\tif ( boundsHitDistance > closestDistanceSquared ) {\n\n\t\t\tcontinue;\n\n\t\t}\n\n\t\tuvec2 boundsInfo = uTexelFetch1D( bvh.bvhContents, currNodeIndex ).xy;\n\t\tbool isLeaf = bool( boundsInfo.x & 0xffff0000u );\n\t\tif ( isLeaf ) {\n\n\t\t\tuint count = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint offset = boundsInfo.y;\n\t\t\tclosestDistanceSquared = distanceToTriangles(\n\t\t\t\tbvh, point, offset, count, closestDistanceSquared,\n\n\t\t\t\t// outputs\n\t\t\t\tfaceIndices, faceNormal, barycoord, side, outPoint\n\t\t\t);\n\n\t\t} else {\n\n\t\t\tuint leftIndex = currNodeIndex + 1u;\n\t\t\tuint splitAxis = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint rightIndex = boundsInfo.y;\n\t\t\tbool leftToRight = distanceSqToBVHNodeBoundsPoint( point, bvh, leftIndex ) < distanceSqToBVHNodeBoundsPoint( point, bvh, rightIndex );//rayDirection[ splitAxis ] >= 0.0;\n\t\t\tuint c1 = leftToRight ? leftIndex : rightIndex;\n\t\t\tuint c2 = leftToRight ? rightIndex : leftIndex;\n\n\t\t\t// set c2 in the stack so we traverse it later. We need to keep track of a pointer in\n\t\t\t// the stack while we traverse. The second pointer added is the one that will be\n\t\t\t// traversed first\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c2;\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c1;\n\n\t\t}\n\n\t}\n\n\treturn sqrt( closestDistanceSquared );\n\n}\n`;\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,MAAMA,aAAa,GAAG,UAAU;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,OAAO,MAAMC,uBAAuB,GAAG,UAAU;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,OAAO,MAAMC,sBAAsB,GAAG,UAAU;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}