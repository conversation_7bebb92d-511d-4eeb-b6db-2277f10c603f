{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { useFBO } from './useFBO.js';\nimport { useHelper } from './useHelper.js';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { Edges } from './Edges.js';\nimport { FullScreenQuad } from 'three-stdlib';\nfunction createNormalMaterial(side = THREE.FrontSide) {\n  const viewMatrix = {\n    value: new THREE.Matrix4()\n  };\n  return Object.assign(new THREE.MeshNormalMaterial({\n    side\n  }), {\n    viewMatrix,\n    onBeforeCompile: shader => {\n      shader.uniforms.viewMatrix = viewMatrix;\n      shader.fragmentShader = `vec3 inverseTransformDirection( in vec3 dir, in mat4 matrix ) {\n           return normalize( ( vec4( dir, 0.0 ) * matrix ).xyz );\n         }\\n` + shader.fragmentShader.replace('#include <normal_fragment_maps>', `#include <normal_fragment_maps>\n           normal = inverseTransformDirection( normal, viewMatrix );\\n`);\n    }\n  });\n}\nconst CausticsProjectionMaterial = shaderMaterial({\n  causticsTexture: null,\n  causticsTextureB: null,\n  color: new THREE.Color(),\n  lightProjMatrix: new THREE.Matrix4(),\n  lightViewMatrix: new THREE.Matrix4()\n}, `varying vec3 vWorldPosition;   \n   void main() {\n     gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n     vec4 worldPosition = modelMatrix * vec4(position, 1.);\n     vWorldPosition = worldPosition.xyz;\n   }`, `varying vec3 vWorldPosition;\n  uniform vec3 color;\n  uniform sampler2D causticsTexture; \n  uniform sampler2D causticsTextureB; \n  uniform mat4 lightProjMatrix;\n  uniform mat4 lightViewMatrix;\n   void main() {\n    // Apply caustics  \n    vec4 lightSpacePos = lightProjMatrix * lightViewMatrix * vec4(vWorldPosition, 1.0);\n    lightSpacePos.xyz /= lightSpacePos.w;\n    lightSpacePos.xyz = lightSpacePos.xyz * 0.5 + 0.5; \n    vec3 front = texture2D(causticsTexture, lightSpacePos.xy).rgb;\n    vec3 back = texture2D(causticsTextureB, lightSpacePos.xy).rgb;\n    gl_FragColor = vec4((front + back) * color, 1.0);\n    #include <tonemapping_fragment>\n    #include <encodings_fragment>\n   }`);\nconst CausticsMaterial = shaderMaterial({\n  cameraMatrixWorld: new THREE.Matrix4(),\n  cameraProjectionMatrixInv: new THREE.Matrix4(),\n  normalTexture: null,\n  depthTexture: null,\n  lightDir: new THREE.Vector3(0, 1, 0),\n  lightPlaneNormal: new THREE.Vector3(0, 1, 0),\n  lightPlaneConstant: 0,\n  near: 0.1,\n  far: 100,\n  modelMatrix: new THREE.Matrix4(),\n  worldRadius: 1 / 40,\n  ior: 1.1,\n  bounces: 0,\n  resolution: 1024,\n  size: 10,\n  intensity: 0.5\n}, /* glsl */\n`\n  varying vec2 vUv;\n  void main() {\n      vUv = uv;\n      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n  }`, /* glsl */\n`  \n  uniform mat4 cameraMatrixWorld;\n  uniform mat4 cameraProjectionMatrixInv;\n  uniform vec3 lightDir;\n  uniform vec3 lightPlaneNormal;\n  uniform float lightPlaneConstant;\n  uniform float near;\n  uniform float far;\n  uniform float time;\n  uniform float worldRadius;\n  uniform float resolution;\n  uniform float size;\n  uniform float intensity;\n  uniform float ior;\n  precision highp isampler2D;\n  precision highp usampler2D;\n  uniform sampler2D normalTexture;\n  uniform sampler2D depthTexture;\n  uniform float bounces;\n  varying vec2 vUv;\n  vec3 WorldPosFromDepth(float depth, vec2 coord) {\n    float z = depth * 2.0 - 1.0;\n    vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n    vec4 viewSpacePosition = cameraProjectionMatrixInv * clipSpacePosition;\n    // Perspective division\n    viewSpacePosition /= viewSpacePosition.w;\n    vec4 worldSpacePosition = cameraMatrixWorld * viewSpacePosition;\n    return worldSpacePosition.xyz;\n  }                  \n  float sdPlane( vec3 p, vec3 n, float h ) {\n    // n must be normalized\n    return dot(p,n) + h;\n  }\n  float planeIntersect( vec3 ro, vec3 rd, vec4 p ) {\n    return -(dot(ro,p.xyz)+p.w)/dot(rd,p.xyz);\n  }\n  vec3 totalInternalReflection(vec3 ro, vec3 rd, vec3 pos, vec3 normal, float ior, out vec3 rayOrigin, out vec3 rayDirection) {\n    rayOrigin = ro;\n    rayDirection = rd;\n    rayDirection = refract(rayDirection, normal, 1.0 / ior);\n    rayOrigin = pos + rayDirection * 0.1;\n    return rayDirection;\n  }\n  void main() {\n    // Each sample consists of random offset in the x and y direction\n    float caustic = 0.0;\n    float causticTexelSize = (1.0 / resolution) * size * 2.0;\n    float texelsNeeded = worldRadius / causticTexelSize;\n    float sampleRadius = texelsNeeded / resolution;\n    float sum = 0.0;\n    if (texture2D(depthTexture, vUv).x == 1.0) {\n      gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\n      return;\n    }\n    vec2 offset1 = vec2(-0.5, -0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset2 = vec2(-0.5, 0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset3 = vec2(0.5, 0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset4 = vec2(0.5, -0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 uv1 = vUv + offset1 * sampleRadius;\n    vec2 uv2 = vUv + offset2 * sampleRadius;\n    vec2 uv3 = vUv + offset3 * sampleRadius;\n    vec2 uv4 = vUv + offset4 * sampleRadius;\n    vec3 normal1 = texture2D(normalTexture, uv1, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal2 = texture2D(normalTexture, uv2, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal3 = texture2D(normalTexture, uv3, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal4 = texture2D(normalTexture, uv4, -10.0).rgb * 2.0 - 1.0;\n    float depth1 = texture2D(depthTexture, uv1, -10.0).x;\n    float depth2 = texture2D(depthTexture, uv2, -10.0).x;\n    float depth3 = texture2D(depthTexture, uv3, -10.0).x;\n    float depth4 = texture2D(depthTexture, uv4, -10.0).x;\n    // Sanity check the depths\n    if (depth1 == 1.0 || depth2 == 1.0 || depth3 == 1.0 || depth4 == 1.0) {\n      gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\n      return;\n    }\n    vec3 pos1 = WorldPosFromDepth(depth1, uv1);\n    vec3 pos2 = WorldPosFromDepth(depth2, uv2);\n    vec3 pos3 = WorldPosFromDepth(depth3, uv3);\n    vec3 pos4 = WorldPosFromDepth(depth4, uv4);\n    vec3 originPos1 = WorldPosFromDepth(0.0, uv1);\n    vec3 originPos2 = WorldPosFromDepth(0.0, uv2);\n    vec3 originPos3 = WorldPosFromDepth(0.0, uv3);\n    vec3 originPos4 = WorldPosFromDepth(0.0, uv4);\n    vec3 endPos1, endPos2, endPos3, endPos4;\n    vec3 endDir1, endDir2, endDir3, endDir4;\n    totalInternalReflection(originPos1, lightDir, pos1, normal1, ior, endPos1, endDir1);\n    totalInternalReflection(originPos2, lightDir, pos2, normal2, ior, endPos2, endDir2);\n    totalInternalReflection(originPos3, lightDir, pos3, normal3, ior, endPos3, endDir3);\n    totalInternalReflection(originPos4, lightDir, pos4, normal4, ior, endPos4, endDir4);\n    float lightPosArea = length(cross(originPos2 - originPos1, originPos3 - originPos1)) + length(cross(originPos3 - originPos1, originPos4 - originPos1));\n    float t1 = planeIntersect(endPos1, endDir1, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t2 = planeIntersect(endPos2, endDir2, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t3 = planeIntersect(endPos3, endDir3, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t4 = planeIntersect(endPos4, endDir4, vec4(lightPlaneNormal, lightPlaneConstant));\n    vec3 finalPos1 = endPos1 + endDir1 * t1;\n    vec3 finalPos2 = endPos2 + endDir2 * t2;\n    vec3 finalPos3 = endPos3 + endDir3 * t3;\n    vec3 finalPos4 = endPos4 + endDir4 * t4;\n    float finalArea = length(cross(finalPos2 - finalPos1, finalPos3 - finalPos1)) + length(cross(finalPos3 - finalPos1, finalPos4 - finalPos1));\n    caustic += intensity * (lightPosArea / finalArea);\n    // Calculate the area of the triangle in light spaces\n    gl_FragColor = vec4(vec3(max(caustic, 0.0)), 1.0);\n  }`);\nconst NORMALPROPS = {\n  depth: true,\n  minFilter: THREE.LinearFilter,\n  magFilter: THREE.LinearFilter,\n  encoding: THREE.LinearEncoding,\n  type: THREE.UnsignedByteType\n};\nconst CAUSTICPROPS = {\n  minFilter: THREE.LinearMipmapLinearFilter,\n  magFilter: THREE.LinearFilter,\n  encoding: THREE.LinearEncoding,\n  format: THREE.RGBAFormat,\n  type: THREE.FloatType,\n  generateMipmaps: true\n};\nconst Caustics = /*#__PURE__*/React.forwardRef(({\n  debug,\n  children,\n  frames = 1,\n  ior = 1.1,\n  color = 'white',\n  causticsOnly = false,\n  backside = false,\n  backsideIOR = 1.1,\n  worldRadius = 0.3125,\n  intensity = 0.05,\n  resolution = 2024,\n  lightSource = [5, 5, 5],\n  ...props\n}, fref) => {\n  extend({\n    CausticsProjectionMaterial\n  });\n  const ref = React.useRef(null);\n  const camera = React.useRef(null);\n  const scene = React.useRef(null);\n  const plane = React.useRef(null);\n  const gl = useThree(state => state.gl);\n  const helper = useHelper(debug && camera, THREE.CameraHelper); // Buffers for front and back faces\n\n  const normalTarget = useFBO(resolution, resolution, NORMALPROPS);\n  const normalTargetB = useFBO(resolution, resolution, NORMALPROPS);\n  const causticsTarget = useFBO(resolution, resolution, CAUSTICPROPS);\n  const causticsTargetB = useFBO(resolution, resolution, CAUSTICPROPS); // Normal materials for front and back faces\n\n  const [normalMat] = React.useState(() => createNormalMaterial());\n  const [normalMatB] = React.useState(() => createNormalMaterial(THREE.BackSide)); // The quad that catches the caustics\n\n  const [causticsMaterial] = React.useState(() => new CausticsMaterial());\n  const [causticsQuad] = React.useState(() => new FullScreenQuad(causticsMaterial));\n  React.useLayoutEffect(() => {\n    ref.current.updateWorldMatrix(false, true);\n  });\n  let count = 0;\n  const v = new THREE.Vector3();\n  const lpF = new THREE.Frustum();\n  const lpM = new THREE.Matrix4();\n  const lpP = new THREE.Plane();\n  const lightDir = new THREE.Vector3();\n  const lightDirInv = new THREE.Vector3();\n  const bounds = new THREE.Box3();\n  const focusPos = new THREE.Vector3();\n  useFrame((state, delta) => {\n    if (frames === Infinity || count++ < frames) {\n      var _scene$current$parent, _helper$current;\n      if (Array.isArray(lightSource)) lightDir.fromArray(lightSource).normalize();else lightDir.copy(ref.current.worldToLocal(lightSource.current.getWorldPosition(v)).normalize());\n      lightDirInv.copy(lightDir).multiplyScalar(-1);\n      let boundsVertices = [];\n      (_scene$current$parent = scene.current.parent) == null ? void 0 : _scene$current$parent.matrixWorld.identity();\n      bounds.setFromObject(scene.current, true);\n      boundsVertices.push(new THREE.Vector3(bounds.min.x, bounds.min.y, bounds.min.z));\n      boundsVertices.push(new THREE.Vector3(bounds.min.x, bounds.min.y, bounds.max.z));\n      boundsVertices.push(new THREE.Vector3(bounds.min.x, bounds.max.y, bounds.min.z));\n      boundsVertices.push(new THREE.Vector3(bounds.min.x, bounds.max.y, bounds.max.z));\n      boundsVertices.push(new THREE.Vector3(bounds.max.x, bounds.min.y, bounds.min.z));\n      boundsVertices.push(new THREE.Vector3(bounds.max.x, bounds.min.y, bounds.max.z));\n      boundsVertices.push(new THREE.Vector3(bounds.max.x, bounds.max.y, bounds.min.z));\n      boundsVertices.push(new THREE.Vector3(bounds.max.x, bounds.max.y, bounds.max.z));\n      const worldVerts = boundsVertices.map(v => v.clone());\n      bounds.getCenter(focusPos);\n      boundsVertices = boundsVertices.map(v => v.clone().sub(focusPos));\n      const lightPlane = lpP.set(lightDirInv, 0);\n      const projectedVerts = boundsVertices.map(v => lightPlane.projectPoint(v, new THREE.Vector3()));\n      const centralVert = projectedVerts.reduce((a, b) => a.add(b), v.set(0, 0, 0)).divideScalar(projectedVerts.length);\n      const radius = projectedVerts.map(v => v.distanceTo(centralVert)).reduce((a, b) => Math.max(a, b));\n      const dirLength = boundsVertices.map(x => x.dot(lightDir)).reduce((a, b) => Math.max(a, b)); // Shadows\n\n      camera.current.position.copy(lightDir.clone().multiplyScalar(dirLength).add(focusPos));\n      camera.current.lookAt(scene.current.localToWorld(focusPos.clone()));\n      const dirMatrix = lpM.lookAt(camera.current.position, focusPos, v.set(0, 1, 0));\n      camera.current.left = -radius;\n      camera.current.right = radius;\n      camera.current.top = radius;\n      camera.current.bottom = -radius;\n      const yOffset = v.set(0, radius, 0).applyMatrix4(dirMatrix);\n      const yTime = (camera.current.position.y + yOffset.y) / lightDir.y;\n      camera.current.near = 0.1;\n      camera.current.far = yTime;\n      camera.current.updateProjectionMatrix();\n      camera.current.updateMatrixWorld(); // Now find size of ground plane\n\n      const groundProjectedCoords = worldVerts.map(v => v.add(lightDir.clone().multiplyScalar(-v.y / lightDir.y)));\n      const centerPos = groundProjectedCoords.reduce((a, b) => a.add(b), v.set(0, 0, 0)).divideScalar(groundProjectedCoords.length);\n      const maxSize = 2 * groundProjectedCoords.map(v => Math.hypot(v.x - centerPos.x, v.z - centerPos.z)).reduce((a, b) => Math.max(a, b));\n      plane.current.scale.setScalar(maxSize);\n      plane.current.position.copy(centerPos);\n      if (debug) (_helper$current = helper.current) == null ? void 0 : _helper$current.update(); // Inject uniforms\n\n      normalMatB.viewMatrix.value = normalMat.viewMatrix.value = camera.current.matrixWorldInverse;\n      const dirLightNearPlane = lpF.setFromProjectionMatrix(lpM.multiplyMatrices(camera.current.projectionMatrix, camera.current.matrixWorldInverse)).planes[4];\n      causticsMaterial.cameraMatrixWorld = camera.current.matrixWorld;\n      causticsMaterial.cameraProjectionMatrixInv = camera.current.projectionMatrixInverse;\n      causticsMaterial.lightDir = lightDirInv;\n      causticsMaterial.lightPlaneNormal = dirLightNearPlane.normal;\n      causticsMaterial.lightPlaneConstant = dirLightNearPlane.constant;\n      causticsMaterial.near = camera.current.near;\n      causticsMaterial.far = camera.current.far;\n      causticsMaterial.resolution = resolution;\n      causticsMaterial.size = radius;\n      causticsMaterial.intensity = intensity;\n      causticsMaterial.worldRadius = worldRadius; // Switch the scene on\n\n      scene.current.visible = true; // Render front face normals\n\n      gl.setRenderTarget(normalTarget);\n      gl.clear();\n      scene.current.overrideMaterial = normalMat;\n      gl.render(scene.current, camera.current); // Render back face normals, if enabled\n\n      gl.setRenderTarget(normalTargetB);\n      gl.clear();\n      if (backside) {\n        scene.current.overrideMaterial = normalMatB;\n        gl.render(scene.current, camera.current);\n      } // Remove the override material\n\n      scene.current.overrideMaterial = null; // Render front face caustics\n\n      causticsMaterial.ior = ior;\n      plane.current.material.lightProjMatrix = camera.current.projectionMatrix;\n      plane.current.material.lightViewMatrix = camera.current.matrixWorldInverse;\n      causticsMaterial.normalTexture = normalTarget.texture;\n      causticsMaterial.depthTexture = normalTarget.depthTexture;\n      gl.setRenderTarget(causticsTarget);\n      gl.clear();\n      causticsQuad.render(gl); // Render back face caustics, if enabled\n\n      causticsMaterial.ior = backsideIOR;\n      causticsMaterial.normalTexture = normalTargetB.texture;\n      causticsMaterial.depthTexture = normalTargetB.depthTexture;\n      gl.setRenderTarget(causticsTargetB);\n      gl.clear();\n      if (backside) causticsQuad.render(gl); // Reset render target\n\n      gl.setRenderTarget(null); // Switch the scene off if caustics is all that's wanted\n\n      if (causticsOnly) scene.current.visible = false;\n    }\n  });\n  React.useImperativeHandle(fref, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props), /*#__PURE__*/React.createElement(\"scene\", {\n    ref: scene\n  }, /*#__PURE__*/React.createElement(\"orthographicCamera\", {\n    ref: camera,\n    up: [0, 1, 0]\n  }), children), /*#__PURE__*/React.createElement(\"mesh\", {\n    renderOrder: 2,\n    ref: plane,\n    \"rotation-x\": -Math.PI / 2\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"causticsProjectionMaterial\", {\n    transparent: true,\n    color: color,\n    causticsTexture: causticsTarget.texture,\n    causticsTextureB: causticsTargetB.texture,\n    blending: THREE.CustomBlending,\n    blendSrc: THREE.OneFactor,\n    blendDst: THREE.SrcAlphaFactor,\n    depthWrite: false\n  }), debug && /*#__PURE__*/React.createElement(Edges, null, /*#__PURE__*/React.createElement(\"lineBasicMaterial\", {\n    color: \"#ffff00\",\n    toneMapped: false\n  }))));\n});\nexport { Caustics };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "extend", "useThree", "useFrame", "useFBO", "useHelper", "shaderMaterial", "<PERSON>s", "FullScreenQuad", "createNormalMaterial", "side", "FrontSide", "viewMatrix", "value", "Matrix4", "Object", "assign", "MeshNormalMaterial", "onBeforeCompile", "shader", "uniforms", "fragmentShader", "replace", "CausticsProjectionMaterial", "causticsTexture", "causticsTextureB", "color", "Color", "lightProjMatrix", "lightViewMatrix", "CausticsMaterial", "cameraMatrixWorld", "cameraProjectionMatrixInv", "normalTexture", "depthTexture", "lightDir", "Vector3", "lightPlaneNormal", "lightPlaneConstant", "near", "far", "modelMatrix", "worldRadius", "ior", "bounces", "resolution", "size", "intensity", "NORMALPROPS", "depth", "minFilter", "LinearFilter", "magFilter", "encoding", "LinearEncoding", "type", "UnsignedByteType", "CAUSTICPROPS", "LinearMipmapLinearFilter", "format", "RGBAFormat", "FloatType", "generateMipmaps", "Caustics", "forwardRef", "debug", "children", "frames", "causticsOnly", "backside", "backsideIOR", "lightSource", "props", "fref", "ref", "useRef", "camera", "scene", "plane", "gl", "state", "helper", "CameraHelper", "normalTarget", "normalTargetB", "causticsTarget", "causticsTargetB", "normalMat", "useState", "normalMatB", "BackSide", "causticsMaterial", "causticsQuad", "useLayoutEffect", "current", "updateWorldMatrix", "count", "v", "lpF", "Frustum", "lpM", "lpP", "Plane", "lightDirInv", "bounds", "Box3", "focusPos", "delta", "Infinity", "_scene$current$parent", "_helper$current", "Array", "isArray", "fromArray", "normalize", "copy", "worldToLocal", "getWorldPosition", "multiplyScalar", "boundsVertices", "parent", "matrixWorld", "identity", "setFromObject", "push", "min", "x", "y", "z", "max", "worldVerts", "map", "clone", "getCenter", "sub", "lightPlane", "set", "<PERSON><PERSON><PERSON><PERSON>", "projectPoint", "centralVert", "reduce", "a", "b", "add", "divideScalar", "length", "radius", "distanceTo", "Math", "<PERSON><PERSON><PERSON><PERSON>", "dot", "position", "lookAt", "localToWorld", "dirMatrix", "left", "right", "top", "bottom", "yOffset", "applyMatrix4", "yTime", "updateProjectionMatrix", "updateMatrixWorld", "groundProjectedCoords", "centerPos", "maxSize", "hypot", "scale", "setScalar", "update", "matrixWorldInverse", "dirLightNearPlane", "setFromProjectionMatrix", "multiplyMatrices", "projectionMatrix", "planes", "projectionMatrixInverse", "normal", "constant", "visible", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "overrideMaterial", "render", "material", "texture", "useImperativeHandle", "createElement", "up", "renderOrder", "PI", "transparent", "blending", "CustomBlending", "blendSrc", "OneFactor", "blendDst", "SrcAlphaFactor", "depthWrite", "toneMapped"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Caustics.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { useFBO } from './useFBO.js';\nimport { useHelper } from './useHelper.js';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { Edges } from './Edges.js';\nimport { FullScreenQuad } from 'three-stdlib';\n\nfunction createNormalMaterial(side = THREE.FrontSide) {\n  const viewMatrix = {\n    value: new THREE.Matrix4()\n  };\n  return Object.assign(new THREE.MeshNormalMaterial({\n    side\n  }), {\n    viewMatrix,\n    onBeforeCompile: shader => {\n      shader.uniforms.viewMatrix = viewMatrix;\n      shader.fragmentShader = `vec3 inverseTransformDirection( in vec3 dir, in mat4 matrix ) {\n           return normalize( ( vec4( dir, 0.0 ) * matrix ).xyz );\n         }\\n` + shader.fragmentShader.replace('#include <normal_fragment_maps>', `#include <normal_fragment_maps>\n           normal = inverseTransformDirection( normal, viewMatrix );\\n`);\n    }\n  });\n}\n\nconst CausticsProjectionMaterial = shaderMaterial({\n  causticsTexture: null,\n  causticsTextureB: null,\n  color: new THREE.Color(),\n  lightProjMatrix: new THREE.Matrix4(),\n  lightViewMatrix: new THREE.Matrix4()\n}, `varying vec3 vWorldPosition;   \n   void main() {\n     gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n     vec4 worldPosition = modelMatrix * vec4(position, 1.);\n     vWorldPosition = worldPosition.xyz;\n   }`, `varying vec3 vWorldPosition;\n  uniform vec3 color;\n  uniform sampler2D causticsTexture; \n  uniform sampler2D causticsTextureB; \n  uniform mat4 lightProjMatrix;\n  uniform mat4 lightViewMatrix;\n   void main() {\n    // Apply caustics  \n    vec4 lightSpacePos = lightProjMatrix * lightViewMatrix * vec4(vWorldPosition, 1.0);\n    lightSpacePos.xyz /= lightSpacePos.w;\n    lightSpacePos.xyz = lightSpacePos.xyz * 0.5 + 0.5; \n    vec3 front = texture2D(causticsTexture, lightSpacePos.xy).rgb;\n    vec3 back = texture2D(causticsTextureB, lightSpacePos.xy).rgb;\n    gl_FragColor = vec4((front + back) * color, 1.0);\n    #include <tonemapping_fragment>\n    #include <encodings_fragment>\n   }`);\nconst CausticsMaterial = shaderMaterial({\n  cameraMatrixWorld: new THREE.Matrix4(),\n  cameraProjectionMatrixInv: new THREE.Matrix4(),\n  normalTexture: null,\n  depthTexture: null,\n  lightDir: new THREE.Vector3(0, 1, 0),\n  lightPlaneNormal: new THREE.Vector3(0, 1, 0),\n  lightPlaneConstant: 0,\n  near: 0.1,\n  far: 100,\n  modelMatrix: new THREE.Matrix4(),\n  worldRadius: 1 / 40,\n  ior: 1.1,\n  bounces: 0,\n  resolution: 1024,\n  size: 10,\n  intensity: 0.5\n},\n/* glsl */\n`\n  varying vec2 vUv;\n  void main() {\n      vUv = uv;\n      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n  }`,\n/* glsl */\n`  \n  uniform mat4 cameraMatrixWorld;\n  uniform mat4 cameraProjectionMatrixInv;\n  uniform vec3 lightDir;\n  uniform vec3 lightPlaneNormal;\n  uniform float lightPlaneConstant;\n  uniform float near;\n  uniform float far;\n  uniform float time;\n  uniform float worldRadius;\n  uniform float resolution;\n  uniform float size;\n  uniform float intensity;\n  uniform float ior;\n  precision highp isampler2D;\n  precision highp usampler2D;\n  uniform sampler2D normalTexture;\n  uniform sampler2D depthTexture;\n  uniform float bounces;\n  varying vec2 vUv;\n  vec3 WorldPosFromDepth(float depth, vec2 coord) {\n    float z = depth * 2.0 - 1.0;\n    vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n    vec4 viewSpacePosition = cameraProjectionMatrixInv * clipSpacePosition;\n    // Perspective division\n    viewSpacePosition /= viewSpacePosition.w;\n    vec4 worldSpacePosition = cameraMatrixWorld * viewSpacePosition;\n    return worldSpacePosition.xyz;\n  }                  \n  float sdPlane( vec3 p, vec3 n, float h ) {\n    // n must be normalized\n    return dot(p,n) + h;\n  }\n  float planeIntersect( vec3 ro, vec3 rd, vec4 p ) {\n    return -(dot(ro,p.xyz)+p.w)/dot(rd,p.xyz);\n  }\n  vec3 totalInternalReflection(vec3 ro, vec3 rd, vec3 pos, vec3 normal, float ior, out vec3 rayOrigin, out vec3 rayDirection) {\n    rayOrigin = ro;\n    rayDirection = rd;\n    rayDirection = refract(rayDirection, normal, 1.0 / ior);\n    rayOrigin = pos + rayDirection * 0.1;\n    return rayDirection;\n  }\n  void main() {\n    // Each sample consists of random offset in the x and y direction\n    float caustic = 0.0;\n    float causticTexelSize = (1.0 / resolution) * size * 2.0;\n    float texelsNeeded = worldRadius / causticTexelSize;\n    float sampleRadius = texelsNeeded / resolution;\n    float sum = 0.0;\n    if (texture2D(depthTexture, vUv).x == 1.0) {\n      gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\n      return;\n    }\n    vec2 offset1 = vec2(-0.5, -0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset2 = vec2(-0.5, 0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset3 = vec2(0.5, 0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset4 = vec2(0.5, -0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 uv1 = vUv + offset1 * sampleRadius;\n    vec2 uv2 = vUv + offset2 * sampleRadius;\n    vec2 uv3 = vUv + offset3 * sampleRadius;\n    vec2 uv4 = vUv + offset4 * sampleRadius;\n    vec3 normal1 = texture2D(normalTexture, uv1, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal2 = texture2D(normalTexture, uv2, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal3 = texture2D(normalTexture, uv3, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal4 = texture2D(normalTexture, uv4, -10.0).rgb * 2.0 - 1.0;\n    float depth1 = texture2D(depthTexture, uv1, -10.0).x;\n    float depth2 = texture2D(depthTexture, uv2, -10.0).x;\n    float depth3 = texture2D(depthTexture, uv3, -10.0).x;\n    float depth4 = texture2D(depthTexture, uv4, -10.0).x;\n    // Sanity check the depths\n    if (depth1 == 1.0 || depth2 == 1.0 || depth3 == 1.0 || depth4 == 1.0) {\n      gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\n      return;\n    }\n    vec3 pos1 = WorldPosFromDepth(depth1, uv1);\n    vec3 pos2 = WorldPosFromDepth(depth2, uv2);\n    vec3 pos3 = WorldPosFromDepth(depth3, uv3);\n    vec3 pos4 = WorldPosFromDepth(depth4, uv4);\n    vec3 originPos1 = WorldPosFromDepth(0.0, uv1);\n    vec3 originPos2 = WorldPosFromDepth(0.0, uv2);\n    vec3 originPos3 = WorldPosFromDepth(0.0, uv3);\n    vec3 originPos4 = WorldPosFromDepth(0.0, uv4);\n    vec3 endPos1, endPos2, endPos3, endPos4;\n    vec3 endDir1, endDir2, endDir3, endDir4;\n    totalInternalReflection(originPos1, lightDir, pos1, normal1, ior, endPos1, endDir1);\n    totalInternalReflection(originPos2, lightDir, pos2, normal2, ior, endPos2, endDir2);\n    totalInternalReflection(originPos3, lightDir, pos3, normal3, ior, endPos3, endDir3);\n    totalInternalReflection(originPos4, lightDir, pos4, normal4, ior, endPos4, endDir4);\n    float lightPosArea = length(cross(originPos2 - originPos1, originPos3 - originPos1)) + length(cross(originPos3 - originPos1, originPos4 - originPos1));\n    float t1 = planeIntersect(endPos1, endDir1, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t2 = planeIntersect(endPos2, endDir2, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t3 = planeIntersect(endPos3, endDir3, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t4 = planeIntersect(endPos4, endDir4, vec4(lightPlaneNormal, lightPlaneConstant));\n    vec3 finalPos1 = endPos1 + endDir1 * t1;\n    vec3 finalPos2 = endPos2 + endDir2 * t2;\n    vec3 finalPos3 = endPos3 + endDir3 * t3;\n    vec3 finalPos4 = endPos4 + endDir4 * t4;\n    float finalArea = length(cross(finalPos2 - finalPos1, finalPos3 - finalPos1)) + length(cross(finalPos3 - finalPos1, finalPos4 - finalPos1));\n    caustic += intensity * (lightPosArea / finalArea);\n    // Calculate the area of the triangle in light spaces\n    gl_FragColor = vec4(vec3(max(caustic, 0.0)), 1.0);\n  }`);\nconst NORMALPROPS = {\n  depth: true,\n  minFilter: THREE.LinearFilter,\n  magFilter: THREE.LinearFilter,\n  encoding: THREE.LinearEncoding,\n  type: THREE.UnsignedByteType\n};\nconst CAUSTICPROPS = {\n  minFilter: THREE.LinearMipmapLinearFilter,\n  magFilter: THREE.LinearFilter,\n  encoding: THREE.LinearEncoding,\n  format: THREE.RGBAFormat,\n  type: THREE.FloatType,\n  generateMipmaps: true\n};\nconst Caustics = /*#__PURE__*/React.forwardRef(({\n  debug,\n  children,\n  frames = 1,\n  ior = 1.1,\n  color = 'white',\n  causticsOnly = false,\n  backside = false,\n  backsideIOR = 1.1,\n  worldRadius = 0.3125,\n  intensity = 0.05,\n  resolution = 2024,\n  lightSource = [5, 5, 5],\n  ...props\n}, fref) => {\n  extend({\n    CausticsProjectionMaterial\n  });\n  const ref = React.useRef(null);\n  const camera = React.useRef(null);\n  const scene = React.useRef(null);\n  const plane = React.useRef(null);\n  const gl = useThree(state => state.gl);\n  const helper = useHelper(debug && camera, THREE.CameraHelper); // Buffers for front and back faces\n\n  const normalTarget = useFBO(resolution, resolution, NORMALPROPS);\n  const normalTargetB = useFBO(resolution, resolution, NORMALPROPS);\n  const causticsTarget = useFBO(resolution, resolution, CAUSTICPROPS);\n  const causticsTargetB = useFBO(resolution, resolution, CAUSTICPROPS); // Normal materials for front and back faces\n\n  const [normalMat] = React.useState(() => createNormalMaterial());\n  const [normalMatB] = React.useState(() => createNormalMaterial(THREE.BackSide)); // The quad that catches the caustics\n\n  const [causticsMaterial] = React.useState(() => new CausticsMaterial());\n  const [causticsQuad] = React.useState(() => new FullScreenQuad(causticsMaterial));\n  React.useLayoutEffect(() => {\n    ref.current.updateWorldMatrix(false, true);\n  });\n  let count = 0;\n  const v = new THREE.Vector3();\n  const lpF = new THREE.Frustum();\n  const lpM = new THREE.Matrix4();\n  const lpP = new THREE.Plane();\n  const lightDir = new THREE.Vector3();\n  const lightDirInv = new THREE.Vector3();\n  const bounds = new THREE.Box3();\n  const focusPos = new THREE.Vector3();\n  useFrame((state, delta) => {\n    if (frames === Infinity || count++ < frames) {\n      var _scene$current$parent, _helper$current;\n\n      if (Array.isArray(lightSource)) lightDir.fromArray(lightSource).normalize();else lightDir.copy(ref.current.worldToLocal(lightSource.current.getWorldPosition(v)).normalize());\n      lightDirInv.copy(lightDir).multiplyScalar(-1);\n      let boundsVertices = [];\n      (_scene$current$parent = scene.current.parent) == null ? void 0 : _scene$current$parent.matrixWorld.identity();\n      bounds.setFromObject(scene.current, true);\n      boundsVertices.push(new THREE.Vector3(bounds.min.x, bounds.min.y, bounds.min.z));\n      boundsVertices.push(new THREE.Vector3(bounds.min.x, bounds.min.y, bounds.max.z));\n      boundsVertices.push(new THREE.Vector3(bounds.min.x, bounds.max.y, bounds.min.z));\n      boundsVertices.push(new THREE.Vector3(bounds.min.x, bounds.max.y, bounds.max.z));\n      boundsVertices.push(new THREE.Vector3(bounds.max.x, bounds.min.y, bounds.min.z));\n      boundsVertices.push(new THREE.Vector3(bounds.max.x, bounds.min.y, bounds.max.z));\n      boundsVertices.push(new THREE.Vector3(bounds.max.x, bounds.max.y, bounds.min.z));\n      boundsVertices.push(new THREE.Vector3(bounds.max.x, bounds.max.y, bounds.max.z));\n      const worldVerts = boundsVertices.map(v => v.clone());\n      bounds.getCenter(focusPos);\n      boundsVertices = boundsVertices.map(v => v.clone().sub(focusPos));\n      const lightPlane = lpP.set(lightDirInv, 0);\n      const projectedVerts = boundsVertices.map(v => lightPlane.projectPoint(v, new THREE.Vector3()));\n      const centralVert = projectedVerts.reduce((a, b) => a.add(b), v.set(0, 0, 0)).divideScalar(projectedVerts.length);\n      const radius = projectedVerts.map(v => v.distanceTo(centralVert)).reduce((a, b) => Math.max(a, b));\n      const dirLength = boundsVertices.map(x => x.dot(lightDir)).reduce((a, b) => Math.max(a, b)); // Shadows\n\n      camera.current.position.copy(lightDir.clone().multiplyScalar(dirLength).add(focusPos));\n      camera.current.lookAt(scene.current.localToWorld(focusPos.clone()));\n      const dirMatrix = lpM.lookAt(camera.current.position, focusPos, v.set(0, 1, 0));\n      camera.current.left = -radius;\n      camera.current.right = radius;\n      camera.current.top = radius;\n      camera.current.bottom = -radius;\n      const yOffset = v.set(0, radius, 0).applyMatrix4(dirMatrix);\n      const yTime = (camera.current.position.y + yOffset.y) / lightDir.y;\n      camera.current.near = 0.1;\n      camera.current.far = yTime;\n      camera.current.updateProjectionMatrix();\n      camera.current.updateMatrixWorld(); // Now find size of ground plane\n\n      const groundProjectedCoords = worldVerts.map(v => v.add(lightDir.clone().multiplyScalar(-v.y / lightDir.y)));\n      const centerPos = groundProjectedCoords.reduce((a, b) => a.add(b), v.set(0, 0, 0)).divideScalar(groundProjectedCoords.length);\n      const maxSize = 2 * groundProjectedCoords.map(v => Math.hypot(v.x - centerPos.x, v.z - centerPos.z)).reduce((a, b) => Math.max(a, b));\n      plane.current.scale.setScalar(maxSize);\n      plane.current.position.copy(centerPos);\n      if (debug) (_helper$current = helper.current) == null ? void 0 : _helper$current.update(); // Inject uniforms\n\n      normalMatB.viewMatrix.value = normalMat.viewMatrix.value = camera.current.matrixWorldInverse;\n      const dirLightNearPlane = lpF.setFromProjectionMatrix(lpM.multiplyMatrices(camera.current.projectionMatrix, camera.current.matrixWorldInverse)).planes[4];\n      causticsMaterial.cameraMatrixWorld = camera.current.matrixWorld;\n      causticsMaterial.cameraProjectionMatrixInv = camera.current.projectionMatrixInverse;\n      causticsMaterial.lightDir = lightDirInv;\n      causticsMaterial.lightPlaneNormal = dirLightNearPlane.normal;\n      causticsMaterial.lightPlaneConstant = dirLightNearPlane.constant;\n      causticsMaterial.near = camera.current.near;\n      causticsMaterial.far = camera.current.far;\n      causticsMaterial.resolution = resolution;\n      causticsMaterial.size = radius;\n      causticsMaterial.intensity = intensity;\n      causticsMaterial.worldRadius = worldRadius; // Switch the scene on\n\n      scene.current.visible = true; // Render front face normals\n\n      gl.setRenderTarget(normalTarget);\n      gl.clear();\n      scene.current.overrideMaterial = normalMat;\n      gl.render(scene.current, camera.current); // Render back face normals, if enabled\n\n      gl.setRenderTarget(normalTargetB);\n      gl.clear();\n\n      if (backside) {\n        scene.current.overrideMaterial = normalMatB;\n        gl.render(scene.current, camera.current);\n      } // Remove the override material\n\n\n      scene.current.overrideMaterial = null; // Render front face caustics\n\n      causticsMaterial.ior = ior;\n      plane.current.material.lightProjMatrix = camera.current.projectionMatrix;\n      plane.current.material.lightViewMatrix = camera.current.matrixWorldInverse;\n      causticsMaterial.normalTexture = normalTarget.texture;\n      causticsMaterial.depthTexture = normalTarget.depthTexture;\n      gl.setRenderTarget(causticsTarget);\n      gl.clear();\n      causticsQuad.render(gl); // Render back face caustics, if enabled\n\n      causticsMaterial.ior = backsideIOR;\n      causticsMaterial.normalTexture = normalTargetB.texture;\n      causticsMaterial.depthTexture = normalTargetB.depthTexture;\n      gl.setRenderTarget(causticsTargetB);\n      gl.clear();\n      if (backside) causticsQuad.render(gl); // Reset render target\n\n      gl.setRenderTarget(null); // Switch the scene off if caustics is all that's wanted\n\n      if (causticsOnly) scene.current.visible = false;\n    }\n  });\n  React.useImperativeHandle(fref, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props), /*#__PURE__*/React.createElement(\"scene\", {\n    ref: scene\n  }, /*#__PURE__*/React.createElement(\"orthographicCamera\", {\n    ref: camera,\n    up: [0, 1, 0]\n  }), children), /*#__PURE__*/React.createElement(\"mesh\", {\n    renderOrder: 2,\n    ref: plane,\n    \"rotation-x\": -Math.PI / 2\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"causticsProjectionMaterial\", {\n    transparent: true,\n    color: color,\n    causticsTexture: causticsTarget.texture,\n    causticsTextureB: causticsTargetB.texture,\n    blending: THREE.CustomBlending,\n    blendSrc: THREE.OneFactor,\n    blendDst: THREE.SrcAlphaFactor,\n    depthWrite: false\n  }), debug && /*#__PURE__*/React.createElement(Edges, null, /*#__PURE__*/React.createElement(\"lineBasicMaterial\", {\n    color: \"#ffff00\",\n    toneMapped: false\n  }))));\n});\n\nexport { Caustics };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAC/D,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,cAAc,QAAQ,cAAc;AAE7C,SAASC,oBAAoBA,CAACC,IAAI,GAAGX,KAAK,CAACY,SAAS,EAAE;EACpD,MAAMC,UAAU,GAAG;IACjBC,KAAK,EAAE,IAAId,KAAK,CAACe,OAAO,CAAC;EAC3B,CAAC;EACD,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAIjB,KAAK,CAACkB,kBAAkB,CAAC;IAChDP;EACF,CAAC,CAAC,EAAE;IACFE,UAAU;IACVM,eAAe,EAAEC,MAAM,IAAI;MACzBA,MAAM,CAACC,QAAQ,CAACR,UAAU,GAAGA,UAAU;MACvCO,MAAM,CAACE,cAAc,GAAG;AAC9B;AACA,aAAa,GAAGF,MAAM,CAACE,cAAc,CAACC,OAAO,CAAC,iCAAiC,EAAE;AACjF,uEAAuE,CAAC;IACpE;EACF,CAAC,CAAC;AACJ;AAEA,MAAMC,0BAA0B,GAAGjB,cAAc,CAAC;EAChDkB,eAAe,EAAE,IAAI;EACrBC,gBAAgB,EAAE,IAAI;EACtBC,KAAK,EAAE,IAAI3B,KAAK,CAAC4B,KAAK,CAAC,CAAC;EACxBC,eAAe,EAAE,IAAI7B,KAAK,CAACe,OAAO,CAAC,CAAC;EACpCe,eAAe,EAAE,IAAI9B,KAAK,CAACe,OAAO,CAAC;AACrC,CAAC,EAAE;AACH;AACA;AACA;AACA;AACA,KAAK,EAAE;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,CAAC;AACN,MAAMgB,gBAAgB,GAAGxB,cAAc,CAAC;EACtCyB,iBAAiB,EAAE,IAAIhC,KAAK,CAACe,OAAO,CAAC,CAAC;EACtCkB,yBAAyB,EAAE,IAAIjC,KAAK,CAACe,OAAO,CAAC,CAAC;EAC9CmB,aAAa,EAAE,IAAI;EACnBC,YAAY,EAAE,IAAI;EAClBC,QAAQ,EAAE,IAAIpC,KAAK,CAACqC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACpCC,gBAAgB,EAAE,IAAItC,KAAK,CAACqC,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC5CE,kBAAkB,EAAE,CAAC;EACrBC,IAAI,EAAE,GAAG;EACTC,GAAG,EAAE,GAAG;EACRC,WAAW,EAAE,IAAI1C,KAAK,CAACe,OAAO,CAAC,CAAC;EAChC4B,WAAW,EAAE,CAAC,GAAG,EAAE;EACnBC,GAAG,EAAE,GAAG;EACRC,OAAO,EAAE,CAAC;EACVC,UAAU,EAAE,IAAI;EAChBC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAE;AACb,CAAC,EACD;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,EACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC;AACL,MAAMC,WAAW,GAAG;EAClBC,KAAK,EAAE,IAAI;EACXC,SAAS,EAAEnD,KAAK,CAACoD,YAAY;EAC7BC,SAAS,EAAErD,KAAK,CAACoD,YAAY;EAC7BE,QAAQ,EAAEtD,KAAK,CAACuD,cAAc;EAC9BC,IAAI,EAAExD,KAAK,CAACyD;AACd,CAAC;AACD,MAAMC,YAAY,GAAG;EACnBP,SAAS,EAAEnD,KAAK,CAAC2D,wBAAwB;EACzCN,SAAS,EAAErD,KAAK,CAACoD,YAAY;EAC7BE,QAAQ,EAAEtD,KAAK,CAACuD,cAAc;EAC9BK,MAAM,EAAE5D,KAAK,CAAC6D,UAAU;EACxBL,IAAI,EAAExD,KAAK,CAAC8D,SAAS;EACrBC,eAAe,EAAE;AACnB,CAAC;AACD,MAAMC,QAAQ,GAAG,aAAa/D,KAAK,CAACgE,UAAU,CAAC,CAAC;EAC9CC,KAAK;EACLC,QAAQ;EACRC,MAAM,GAAG,CAAC;EACVxB,GAAG,GAAG,GAAG;EACTjB,KAAK,GAAG,OAAO;EACf0C,YAAY,GAAG,KAAK;EACpBC,QAAQ,GAAG,KAAK;EAChBC,WAAW,GAAG,GAAG;EACjB5B,WAAW,GAAG,MAAM;EACpBK,SAAS,GAAG,IAAI;EAChBF,UAAU,GAAG,IAAI;EACjB0B,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACvB,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACVxE,MAAM,CAAC;IACLsB;EACF,CAAC,CAAC;EACF,MAAMmD,GAAG,GAAG1E,KAAK,CAAC2E,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,MAAM,GAAG5E,KAAK,CAAC2E,MAAM,CAAC,IAAI,CAAC;EACjC,MAAME,KAAK,GAAG7E,KAAK,CAAC2E,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMG,KAAK,GAAG9E,KAAK,CAAC2E,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMI,EAAE,GAAG7E,QAAQ,CAAC8E,KAAK,IAAIA,KAAK,CAACD,EAAE,CAAC;EACtC,MAAME,MAAM,GAAG5E,SAAS,CAAC4D,KAAK,IAAIW,MAAM,EAAE7E,KAAK,CAACmF,YAAY,CAAC,CAAC,CAAC;;EAE/D,MAAMC,YAAY,GAAG/E,MAAM,CAACyC,UAAU,EAAEA,UAAU,EAAEG,WAAW,CAAC;EAChE,MAAMoC,aAAa,GAAGhF,MAAM,CAACyC,UAAU,EAAEA,UAAU,EAAEG,WAAW,CAAC;EACjE,MAAMqC,cAAc,GAAGjF,MAAM,CAACyC,UAAU,EAAEA,UAAU,EAAEY,YAAY,CAAC;EACnE,MAAM6B,eAAe,GAAGlF,MAAM,CAACyC,UAAU,EAAEA,UAAU,EAAEY,YAAY,CAAC,CAAC,CAAC;;EAEtE,MAAM,CAAC8B,SAAS,CAAC,GAAGvF,KAAK,CAACwF,QAAQ,CAAC,MAAM/E,oBAAoB,CAAC,CAAC,CAAC;EAChE,MAAM,CAACgF,UAAU,CAAC,GAAGzF,KAAK,CAACwF,QAAQ,CAAC,MAAM/E,oBAAoB,CAACV,KAAK,CAAC2F,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEjF,MAAM,CAACC,gBAAgB,CAAC,GAAG3F,KAAK,CAACwF,QAAQ,CAAC,MAAM,IAAI1D,gBAAgB,CAAC,CAAC,CAAC;EACvE,MAAM,CAAC8D,YAAY,CAAC,GAAG5F,KAAK,CAACwF,QAAQ,CAAC,MAAM,IAAIhF,cAAc,CAACmF,gBAAgB,CAAC,CAAC;EACjF3F,KAAK,CAAC6F,eAAe,CAAC,MAAM;IAC1BnB,GAAG,CAACoB,OAAO,CAACC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC;EAC5C,CAAC,CAAC;EACF,IAAIC,KAAK,GAAG,CAAC;EACb,MAAMC,CAAC,GAAG,IAAIlG,KAAK,CAACqC,OAAO,CAAC,CAAC;EAC7B,MAAM8D,GAAG,GAAG,IAAInG,KAAK,CAACoG,OAAO,CAAC,CAAC;EAC/B,MAAMC,GAAG,GAAG,IAAIrG,KAAK,CAACe,OAAO,CAAC,CAAC;EAC/B,MAAMuF,GAAG,GAAG,IAAItG,KAAK,CAACuG,KAAK,CAAC,CAAC;EAC7B,MAAMnE,QAAQ,GAAG,IAAIpC,KAAK,CAACqC,OAAO,CAAC,CAAC;EACpC,MAAMmE,WAAW,GAAG,IAAIxG,KAAK,CAACqC,OAAO,CAAC,CAAC;EACvC,MAAMoE,MAAM,GAAG,IAAIzG,KAAK,CAAC0G,IAAI,CAAC,CAAC;EAC/B,MAAMC,QAAQ,GAAG,IAAI3G,KAAK,CAACqC,OAAO,CAAC,CAAC;EACpCjC,QAAQ,CAAC,CAAC6E,KAAK,EAAE2B,KAAK,KAAK;IACzB,IAAIxC,MAAM,KAAKyC,QAAQ,IAAIZ,KAAK,EAAE,GAAG7B,MAAM,EAAE;MAC3C,IAAI0C,qBAAqB,EAAEC,eAAe;MAE1C,IAAIC,KAAK,CAACC,OAAO,CAACzC,WAAW,CAAC,EAAEpC,QAAQ,CAAC8E,SAAS,CAAC1C,WAAW,CAAC,CAAC2C,SAAS,CAAC,CAAC,CAAC,KAAK/E,QAAQ,CAACgF,IAAI,CAACzC,GAAG,CAACoB,OAAO,CAACsB,YAAY,CAAC7C,WAAW,CAACuB,OAAO,CAACuB,gBAAgB,CAACpB,CAAC,CAAC,CAAC,CAACiB,SAAS,CAAC,CAAC,CAAC;MAC7KX,WAAW,CAACY,IAAI,CAAChF,QAAQ,CAAC,CAACmF,cAAc,CAAC,CAAC,CAAC,CAAC;MAC7C,IAAIC,cAAc,GAAG,EAAE;MACvB,CAACV,qBAAqB,GAAGhC,KAAK,CAACiB,OAAO,CAAC0B,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGX,qBAAqB,CAACY,WAAW,CAACC,QAAQ,CAAC,CAAC;MAC9GlB,MAAM,CAACmB,aAAa,CAAC9C,KAAK,CAACiB,OAAO,EAAE,IAAI,CAAC;MACzCyB,cAAc,CAACK,IAAI,CAAC,IAAI7H,KAAK,CAACqC,OAAO,CAACoE,MAAM,CAACqB,GAAG,CAACC,CAAC,EAAEtB,MAAM,CAACqB,GAAG,CAACE,CAAC,EAAEvB,MAAM,CAACqB,GAAG,CAACG,CAAC,CAAC,CAAC;MAChFT,cAAc,CAACK,IAAI,CAAC,IAAI7H,KAAK,CAACqC,OAAO,CAACoE,MAAM,CAACqB,GAAG,CAACC,CAAC,EAAEtB,MAAM,CAACqB,GAAG,CAACE,CAAC,EAAEvB,MAAM,CAACyB,GAAG,CAACD,CAAC,CAAC,CAAC;MAChFT,cAAc,CAACK,IAAI,CAAC,IAAI7H,KAAK,CAACqC,OAAO,CAACoE,MAAM,CAACqB,GAAG,CAACC,CAAC,EAAEtB,MAAM,CAACyB,GAAG,CAACF,CAAC,EAAEvB,MAAM,CAACqB,GAAG,CAACG,CAAC,CAAC,CAAC;MAChFT,cAAc,CAACK,IAAI,CAAC,IAAI7H,KAAK,CAACqC,OAAO,CAACoE,MAAM,CAACqB,GAAG,CAACC,CAAC,EAAEtB,MAAM,CAACyB,GAAG,CAACF,CAAC,EAAEvB,MAAM,CAACyB,GAAG,CAACD,CAAC,CAAC,CAAC;MAChFT,cAAc,CAACK,IAAI,CAAC,IAAI7H,KAAK,CAACqC,OAAO,CAACoE,MAAM,CAACyB,GAAG,CAACH,CAAC,EAAEtB,MAAM,CAACqB,GAAG,CAACE,CAAC,EAAEvB,MAAM,CAACqB,GAAG,CAACG,CAAC,CAAC,CAAC;MAChFT,cAAc,CAACK,IAAI,CAAC,IAAI7H,KAAK,CAACqC,OAAO,CAACoE,MAAM,CAACyB,GAAG,CAACH,CAAC,EAAEtB,MAAM,CAACqB,GAAG,CAACE,CAAC,EAAEvB,MAAM,CAACyB,GAAG,CAACD,CAAC,CAAC,CAAC;MAChFT,cAAc,CAACK,IAAI,CAAC,IAAI7H,KAAK,CAACqC,OAAO,CAACoE,MAAM,CAACyB,GAAG,CAACH,CAAC,EAAEtB,MAAM,CAACyB,GAAG,CAACF,CAAC,EAAEvB,MAAM,CAACqB,GAAG,CAACG,CAAC,CAAC,CAAC;MAChFT,cAAc,CAACK,IAAI,CAAC,IAAI7H,KAAK,CAACqC,OAAO,CAACoE,MAAM,CAACyB,GAAG,CAACH,CAAC,EAAEtB,MAAM,CAACyB,GAAG,CAACF,CAAC,EAAEvB,MAAM,CAACyB,GAAG,CAACD,CAAC,CAAC,CAAC;MAChF,MAAME,UAAU,GAAGX,cAAc,CAACY,GAAG,CAAClC,CAAC,IAAIA,CAAC,CAACmC,KAAK,CAAC,CAAC,CAAC;MACrD5B,MAAM,CAAC6B,SAAS,CAAC3B,QAAQ,CAAC;MAC1Ba,cAAc,GAAGA,cAAc,CAACY,GAAG,CAAClC,CAAC,IAAIA,CAAC,CAACmC,KAAK,CAAC,CAAC,CAACE,GAAG,CAAC5B,QAAQ,CAAC,CAAC;MACjE,MAAM6B,UAAU,GAAGlC,GAAG,CAACmC,GAAG,CAACjC,WAAW,EAAE,CAAC,CAAC;MAC1C,MAAMkC,cAAc,GAAGlB,cAAc,CAACY,GAAG,CAAClC,CAAC,IAAIsC,UAAU,CAACG,YAAY,CAACzC,CAAC,EAAE,IAAIlG,KAAK,CAACqC,OAAO,CAAC,CAAC,CAAC,CAAC;MAC/F,MAAMuG,WAAW,GAAGF,cAAc,CAACG,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,GAAG,CAACD,CAAC,CAAC,EAAE7C,CAAC,CAACuC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAACQ,YAAY,CAACP,cAAc,CAACQ,MAAM,CAAC;MACjH,MAAMC,MAAM,GAAGT,cAAc,CAACN,GAAG,CAAClC,CAAC,IAAIA,CAAC,CAACkD,UAAU,CAACR,WAAW,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKM,IAAI,CAACnB,GAAG,CAACY,CAAC,EAAEC,CAAC,CAAC,CAAC;MAClG,MAAMO,SAAS,GAAG9B,cAAc,CAACY,GAAG,CAACL,CAAC,IAAIA,CAAC,CAACwB,GAAG,CAACnH,QAAQ,CAAC,CAAC,CAACyG,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKM,IAAI,CAACnB,GAAG,CAACY,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE7FlE,MAAM,CAACkB,OAAO,CAACyD,QAAQ,CAACpC,IAAI,CAAChF,QAAQ,CAACiG,KAAK,CAAC,CAAC,CAACd,cAAc,CAAC+B,SAAS,CAAC,CAACN,GAAG,CAACrC,QAAQ,CAAC,CAAC;MACtF9B,MAAM,CAACkB,OAAO,CAAC0D,MAAM,CAAC3E,KAAK,CAACiB,OAAO,CAAC2D,YAAY,CAAC/C,QAAQ,CAAC0B,KAAK,CAAC,CAAC,CAAC,CAAC;MACnE,MAAMsB,SAAS,GAAGtD,GAAG,CAACoD,MAAM,CAAC5E,MAAM,CAACkB,OAAO,CAACyD,QAAQ,EAAE7C,QAAQ,EAAET,CAAC,CAACuC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAC/E5D,MAAM,CAACkB,OAAO,CAAC6D,IAAI,GAAG,CAACT,MAAM;MAC7BtE,MAAM,CAACkB,OAAO,CAAC8D,KAAK,GAAGV,MAAM;MAC7BtE,MAAM,CAACkB,OAAO,CAAC+D,GAAG,GAAGX,MAAM;MAC3BtE,MAAM,CAACkB,OAAO,CAACgE,MAAM,GAAG,CAACZ,MAAM;MAC/B,MAAMa,OAAO,GAAG9D,CAAC,CAACuC,GAAG,CAAC,CAAC,EAAEU,MAAM,EAAE,CAAC,CAAC,CAACc,YAAY,CAACN,SAAS,CAAC;MAC3D,MAAMO,KAAK,GAAG,CAACrF,MAAM,CAACkB,OAAO,CAACyD,QAAQ,CAACxB,CAAC,GAAGgC,OAAO,CAAChC,CAAC,IAAI5F,QAAQ,CAAC4F,CAAC;MAClEnD,MAAM,CAACkB,OAAO,CAACvD,IAAI,GAAG,GAAG;MACzBqC,MAAM,CAACkB,OAAO,CAACtD,GAAG,GAAGyH,KAAK;MAC1BrF,MAAM,CAACkB,OAAO,CAACoE,sBAAsB,CAAC,CAAC;MACvCtF,MAAM,CAACkB,OAAO,CAACqE,iBAAiB,CAAC,CAAC,CAAC,CAAC;;MAEpC,MAAMC,qBAAqB,GAAGlC,UAAU,CAACC,GAAG,CAAClC,CAAC,IAAIA,CAAC,CAAC8C,GAAG,CAAC5G,QAAQ,CAACiG,KAAK,CAAC,CAAC,CAACd,cAAc,CAAC,CAACrB,CAAC,CAAC8B,CAAC,GAAG5F,QAAQ,CAAC4F,CAAC,CAAC,CAAC,CAAC;MAC5G,MAAMsC,SAAS,GAAGD,qBAAqB,CAACxB,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,GAAG,CAACD,CAAC,CAAC,EAAE7C,CAAC,CAACuC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAACQ,YAAY,CAACoB,qBAAqB,CAACnB,MAAM,CAAC;MAC7H,MAAMqB,OAAO,GAAG,CAAC,GAAGF,qBAAqB,CAACjC,GAAG,CAAClC,CAAC,IAAImD,IAAI,CAACmB,KAAK,CAACtE,CAAC,CAAC6B,CAAC,GAAGuC,SAAS,CAACvC,CAAC,EAAE7B,CAAC,CAAC+B,CAAC,GAAGqC,SAAS,CAACrC,CAAC,CAAC,CAAC,CAACY,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKM,IAAI,CAACnB,GAAG,CAACY,CAAC,EAAEC,CAAC,CAAC,CAAC;MACrIhE,KAAK,CAACgB,OAAO,CAAC0E,KAAK,CAACC,SAAS,CAACH,OAAO,CAAC;MACtCxF,KAAK,CAACgB,OAAO,CAACyD,QAAQ,CAACpC,IAAI,CAACkD,SAAS,CAAC;MACtC,IAAIpG,KAAK,EAAE,CAAC6C,eAAe,GAAG7B,MAAM,CAACa,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgB,eAAe,CAAC4D,MAAM,CAAC,CAAC,CAAC,CAAC;;MAE3FjF,UAAU,CAAC7E,UAAU,CAACC,KAAK,GAAG0E,SAAS,CAAC3E,UAAU,CAACC,KAAK,GAAG+D,MAAM,CAACkB,OAAO,CAAC6E,kBAAkB;MAC5F,MAAMC,iBAAiB,GAAG1E,GAAG,CAAC2E,uBAAuB,CAACzE,GAAG,CAAC0E,gBAAgB,CAAClG,MAAM,CAACkB,OAAO,CAACiF,gBAAgB,EAAEnG,MAAM,CAACkB,OAAO,CAAC6E,kBAAkB,CAAC,CAAC,CAACK,MAAM,CAAC,CAAC,CAAC;MACzJrF,gBAAgB,CAAC5D,iBAAiB,GAAG6C,MAAM,CAACkB,OAAO,CAAC2B,WAAW;MAC/D9B,gBAAgB,CAAC3D,yBAAyB,GAAG4C,MAAM,CAACkB,OAAO,CAACmF,uBAAuB;MACnFtF,gBAAgB,CAACxD,QAAQ,GAAGoE,WAAW;MACvCZ,gBAAgB,CAACtD,gBAAgB,GAAGuI,iBAAiB,CAACM,MAAM;MAC5DvF,gBAAgB,CAACrD,kBAAkB,GAAGsI,iBAAiB,CAACO,QAAQ;MAChExF,gBAAgB,CAACpD,IAAI,GAAGqC,MAAM,CAACkB,OAAO,CAACvD,IAAI;MAC3CoD,gBAAgB,CAACnD,GAAG,GAAGoC,MAAM,CAACkB,OAAO,CAACtD,GAAG;MACzCmD,gBAAgB,CAAC9C,UAAU,GAAGA,UAAU;MACxC8C,gBAAgB,CAAC7C,IAAI,GAAGoG,MAAM;MAC9BvD,gBAAgB,CAAC5C,SAAS,GAAGA,SAAS;MACtC4C,gBAAgB,CAACjD,WAAW,GAAGA,WAAW,CAAC,CAAC;;MAE5CmC,KAAK,CAACiB,OAAO,CAACsF,OAAO,GAAG,IAAI,CAAC,CAAC;;MAE9BrG,EAAE,CAACsG,eAAe,CAAClG,YAAY,CAAC;MAChCJ,EAAE,CAACuG,KAAK,CAAC,CAAC;MACVzG,KAAK,CAACiB,OAAO,CAACyF,gBAAgB,GAAGhG,SAAS;MAC1CR,EAAE,CAACyG,MAAM,CAAC3G,KAAK,CAACiB,OAAO,EAAElB,MAAM,CAACkB,OAAO,CAAC,CAAC,CAAC;;MAE1Cf,EAAE,CAACsG,eAAe,CAACjG,aAAa,CAAC;MACjCL,EAAE,CAACuG,KAAK,CAAC,CAAC;MAEV,IAAIjH,QAAQ,EAAE;QACZQ,KAAK,CAACiB,OAAO,CAACyF,gBAAgB,GAAG9F,UAAU;QAC3CV,EAAE,CAACyG,MAAM,CAAC3G,KAAK,CAACiB,OAAO,EAAElB,MAAM,CAACkB,OAAO,CAAC;MAC1C,CAAC,CAAC;;MAGFjB,KAAK,CAACiB,OAAO,CAACyF,gBAAgB,GAAG,IAAI,CAAC,CAAC;;MAEvC5F,gBAAgB,CAAChD,GAAG,GAAGA,GAAG;MAC1BmC,KAAK,CAACgB,OAAO,CAAC2F,QAAQ,CAAC7J,eAAe,GAAGgD,MAAM,CAACkB,OAAO,CAACiF,gBAAgB;MACxEjG,KAAK,CAACgB,OAAO,CAAC2F,QAAQ,CAAC5J,eAAe,GAAG+C,MAAM,CAACkB,OAAO,CAAC6E,kBAAkB;MAC1EhF,gBAAgB,CAAC1D,aAAa,GAAGkD,YAAY,CAACuG,OAAO;MACrD/F,gBAAgB,CAACzD,YAAY,GAAGiD,YAAY,CAACjD,YAAY;MACzD6C,EAAE,CAACsG,eAAe,CAAChG,cAAc,CAAC;MAClCN,EAAE,CAACuG,KAAK,CAAC,CAAC;MACV1F,YAAY,CAAC4F,MAAM,CAACzG,EAAE,CAAC,CAAC,CAAC;;MAEzBY,gBAAgB,CAAChD,GAAG,GAAG2B,WAAW;MAClCqB,gBAAgB,CAAC1D,aAAa,GAAGmD,aAAa,CAACsG,OAAO;MACtD/F,gBAAgB,CAACzD,YAAY,GAAGkD,aAAa,CAAClD,YAAY;MAC1D6C,EAAE,CAACsG,eAAe,CAAC/F,eAAe,CAAC;MACnCP,EAAE,CAACuG,KAAK,CAAC,CAAC;MACV,IAAIjH,QAAQ,EAAEuB,YAAY,CAAC4F,MAAM,CAACzG,EAAE,CAAC,CAAC,CAAC;;MAEvCA,EAAE,CAACsG,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;;MAE1B,IAAIjH,YAAY,EAAES,KAAK,CAACiB,OAAO,CAACsF,OAAO,GAAG,KAAK;IACjD;EACF,CAAC,CAAC;EACFpL,KAAK,CAAC2L,mBAAmB,CAAClH,IAAI,EAAE,MAAMC,GAAG,CAACoB,OAAO,EAAE,EAAE,CAAC;EACtD,OAAO,aAAa9F,KAAK,CAAC4L,aAAa,CAAC,OAAO,EAAE9L,QAAQ,CAAC;IACxD4E,GAAG,EAAEA;EACP,CAAC,EAAEF,KAAK,CAAC,EAAE,aAAaxE,KAAK,CAAC4L,aAAa,CAAC,OAAO,EAAE;IACnDlH,GAAG,EAAEG;EACP,CAAC,EAAE,aAAa7E,KAAK,CAAC4L,aAAa,CAAC,oBAAoB,EAAE;IACxDlH,GAAG,EAAEE,MAAM;IACXiH,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EACd,CAAC,CAAC,EAAE3H,QAAQ,CAAC,EAAE,aAAalE,KAAK,CAAC4L,aAAa,CAAC,MAAM,EAAE;IACtDE,WAAW,EAAE,CAAC;IACdpH,GAAG,EAAEI,KAAK;IACV,YAAY,EAAE,CAACsE,IAAI,CAAC2C,EAAE,GAAG;EAC3B,CAAC,EAAE,aAAa/L,KAAK,CAAC4L,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,aAAa5L,KAAK,CAAC4L,aAAa,CAAC,4BAA4B,EAAE;IACzHI,WAAW,EAAE,IAAI;IACjBtK,KAAK,EAAEA,KAAK;IACZF,eAAe,EAAE6D,cAAc,CAACqG,OAAO;IACvCjK,gBAAgB,EAAE6D,eAAe,CAACoG,OAAO;IACzCO,QAAQ,EAAElM,KAAK,CAACmM,cAAc;IAC9BC,QAAQ,EAAEpM,KAAK,CAACqM,SAAS;IACzBC,QAAQ,EAAEtM,KAAK,CAACuM,cAAc;IAC9BC,UAAU,EAAE;EACd,CAAC,CAAC,EAAEtI,KAAK,IAAI,aAAajE,KAAK,CAAC4L,aAAa,CAACrL,KAAK,EAAE,IAAI,EAAE,aAAaP,KAAK,CAAC4L,aAAa,CAAC,mBAAmB,EAAE;IAC/GlK,KAAK,EAAE,SAAS;IAChB8K,UAAU,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAEF,SAASzI,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}