{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nclass PointMaterialImpl extends THREE.PointsMaterial {\n  constructor(props) {\n    super(props);\n    this.onBeforeCompile = (shader, renderer) => {\n      const {\n        isWebGL2\n      } = renderer.capabilities;\n      shader.fragmentShader = shader.fragmentShader.replace('#include <output_fragment>', `\n        ${!isWebGL2 ? '#extension GL_OES_standard_derivatives : enable\\n#include <output_fragment>' : '#include <output_fragment>'}\n      vec2 cxy = 2.0 * gl_PointCoord - 1.0;\n      float r = dot(cxy, cxy);\n      float delta = fwidth(r);     \n      float mask = 1.0 - smoothstep(1.0 - delta, 1.0 + delta, r);\n      gl_FragColor = vec4(gl_FragColor.rgb, mask * gl_FragColor.a );\n      #include <tonemapping_fragment>\n      #include <encodings_fragment>\n      `);\n    };\n  }\n}\nconst PointMaterial = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const [material] = React.useState(() => new PointMaterialImpl(null));\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({}, props, {\n    object: material,\n    ref: ref,\n    attach: \"material\"\n  }));\n});\nexport { PointMaterial, PointMaterialImpl };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "PointMaterialImpl", "PointsMaterial", "constructor", "props", "onBeforeCompile", "shader", "renderer", "isWebGL2", "capabilities", "fragmentShader", "replace", "PointMaterial", "forwardRef", "ref", "material", "useState", "createElement", "object", "attach"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/PointMaterial.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\n\nclass PointMaterialImpl extends THREE.PointsMaterial {\n  constructor(props) {\n    super(props);\n\n    this.onBeforeCompile = (shader, renderer) => {\n      const {\n        isWebGL2\n      } = renderer.capabilities;\n      shader.fragmentShader = shader.fragmentShader.replace('#include <output_fragment>', `\n        ${!isWebGL2 ? '#extension GL_OES_standard_derivatives : enable\\n#include <output_fragment>' : '#include <output_fragment>'}\n      vec2 cxy = 2.0 * gl_PointCoord - 1.0;\n      float r = dot(cxy, cxy);\n      float delta = fwidth(r);     \n      float mask = 1.0 - smoothstep(1.0 - delta, 1.0 + delta, r);\n      gl_FragColor = vec4(gl_FragColor.rgb, mask * gl_FragColor.a );\n      #include <tonemapping_fragment>\n      #include <encodings_fragment>\n      `);\n    };\n  }\n\n}\nconst PointMaterial = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const [material] = React.useState(() => new PointMaterialImpl(null));\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({}, props, {\n    object: material,\n    ref: ref,\n    attach: \"material\"\n  }));\n});\n\nexport { PointMaterial, PointMaterialImpl };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,MAAMC,iBAAiB,SAASF,KAAK,CAACG,cAAc,CAAC;EACnDC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IAEZ,IAAI,CAACC,eAAe,GAAG,CAACC,MAAM,EAAEC,QAAQ,KAAK;MAC3C,MAAM;QACJC;MACF,CAAC,GAAGD,QAAQ,CAACE,YAAY;MACzBH,MAAM,CAACI,cAAc,GAAGJ,MAAM,CAACI,cAAc,CAACC,OAAO,CAAC,4BAA4B,EAAE;AAC1F,UAAU,CAACH,QAAQ,GAAG,6EAA6E,GAAG,4BAA4B;AAClI;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,CAAC;IACJ,CAAC;EACH;AAEF;AACA,MAAMI,aAAa,GAAG,aAAaZ,KAAK,CAACa,UAAU,CAAC,CAACT,KAAK,EAAEU,GAAG,KAAK;EAClE,MAAM,CAACC,QAAQ,CAAC,GAAGf,KAAK,CAACgB,QAAQ,CAAC,MAAM,IAAIf,iBAAiB,CAAC,IAAI,CAAC,CAAC;EACpE,OAAO,aAAaD,KAAK,CAACiB,aAAa,CAAC,WAAW,EAAEnB,QAAQ,CAAC,CAAC,CAAC,EAAEM,KAAK,EAAE;IACvEc,MAAM,EAAEH,QAAQ;IAChBD,GAAG,EAAEA,GAAG;IACRK,MAAM,EAAE;EACV,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASP,aAAa,EAAEX,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}