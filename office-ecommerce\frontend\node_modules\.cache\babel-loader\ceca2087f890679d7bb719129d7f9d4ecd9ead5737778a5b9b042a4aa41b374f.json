{"ast": null, "code": "import { FontLoader } from 'three-stdlib';\nimport { suspend, preload, clear } from 'suspend-react';\nlet fontLoader = null;\nasync function loader(font) {\n  if (!fontLoader) fontLoader = new FontLoader();\n  let data = typeof font === 'string' ? await (await fetch(font)).json() : font;\n  return fontLoader.parse(data);\n}\nfunction useFont(font) {\n  return suspend(loader, [font]);\n}\nuseFont.preload = font => preload(loader, [font]);\nuseFont.clear = font => clear([font]);\nexport { useFont };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "suspend", "preload", "clear", "fontLoader", "loader", "font", "data", "fetch", "json", "parse", "useFont"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useFont.js"], "sourcesContent": ["import { FontLoader } from 'three-stdlib';\nimport { suspend, preload, clear } from 'suspend-react';\n\nlet fontLoader = null;\n\nasync function loader(font) {\n  if (!fontLoader) fontLoader = new FontLoader();\n  let data = typeof font === 'string' ? await (await fetch(font)).json() : font;\n  return fontLoader.parse(data);\n}\n\nfunction useFont(font) {\n  return suspend(loader, [font]);\n}\n\nuseFont.preload = font => preload(loader, [font]);\n\nuseFont.clear = font => clear([font]);\n\nexport { useFont };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,OAAO,EAAEC,OAAO,EAAEC,KAAK,QAAQ,eAAe;AAEvD,IAAIC,UAAU,GAAG,IAAI;AAErB,eAAeC,MAAMA,CAACC,IAAI,EAAE;EAC1B,IAAI,CAACF,UAAU,EAAEA,UAAU,GAAG,IAAIJ,UAAU,CAAC,CAAC;EAC9C,IAAIO,IAAI,GAAG,OAAOD,IAAI,KAAK,QAAQ,GAAG,MAAM,CAAC,MAAME,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,CAAC,CAAC,GAAGH,IAAI;EAC7E,OAAOF,UAAU,CAACM,KAAK,CAACH,IAAI,CAAC;AAC/B;AAEA,SAASI,OAAOA,CAACL,IAAI,EAAE;EACrB,OAAOL,OAAO,CAACI,MAAM,EAAE,CAACC,IAAI,CAAC,CAAC;AAChC;AAEAK,OAAO,CAACT,OAAO,GAAGI,IAAI,IAAIJ,OAAO,CAACG,MAAM,EAAE,CAACC,IAAI,CAAC,CAAC;AAEjDK,OAAO,CAACR,KAAK,GAAGG,IAAI,IAAIH,KAAK,CAAC,CAACG,IAAI,CAAC,CAAC;AAErC,SAASK,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}