{"ast": null, "code": "function shallowEqualArrays(arrA, arrB, equal = (a, b) => a === b) {\n  if (arrA === arrB) return true;\n  if (!arrA || !arrB) return false;\n  const len = arrA.length;\n  if (arrB.length !== len) return false;\n  for (let i = 0; i < len; i++) if (!equal(arrA[i], arrB[i])) return false;\n  return true;\n}\nconst globalCache = [];\nfunction query(fn, keys, preload = false, config = {}) {\n  for (const entry of globalCache) {\n    // Find a match\n    if (shallowEqualArrays(keys, entry.keys, entry.equal)) {\n      // If we're pre-loading and the element is present, just return\n      if (preload) return undefined; // If an error occurred, throw\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'error')) throw entry.error; // If a response was successful, return\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'response')) return entry.response; // If the promise is still unresolved, throw\n\n      if (!preload) throw entry.promise;\n    }\n  } // The request is new or has changed.\n\n  const entry = {\n    keys,\n    equal: config.equal,\n    promise:\n    // Execute the promise\n    fn(...keys) // When it resolves, store its value\n    .then(response => entry.response = response) // Remove the entry if a lifespan was given\n    .then(() => {\n      if (config.lifespan && config.lifespan > 0) {\n        setTimeout(() => {\n          const index = globalCache.indexOf(entry);\n          if (index !== -1) globalCache.splice(index, 1);\n        }, config.lifespan);\n      }\n    }) // Store caught errors, they will be thrown in the render-phase to bubble into an error-bound\n    .catch(error => entry.error = error)\n  }; // Register the entry\n\n  globalCache.push(entry); // And throw the promise, this yields control back to React\n\n  if (!preload) throw entry.promise;\n  return undefined;\n}\nconst suspend = (fn, keys, config) => query(fn, keys, false, config);\nconst preload = (fn, keys, config) => void query(fn, keys, true, config);\nconst peek = keys => {\n  var _globalCache$find;\n  return (_globalCache$find = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal))) == null ? void 0 : _globalCache$find.response;\n};\nconst clear = keys => {\n  if (keys === undefined || keys.length === 0) globalCache.splice(0, globalCache.length);else {\n    const entry = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal));\n    if (entry) {\n      const index = globalCache.indexOf(entry);\n      if (index !== -1) globalCache.splice(index, 1);\n    }\n  }\n};\nexport { clear, peek, preload, suspend };", "map": {"version": 3, "names": ["shallowEqualArrays", "arrA", "arrB", "equal", "a", "b", "len", "length", "i", "globalCache", "query", "fn", "keys", "preload", "config", "entry", "undefined", "Object", "prototype", "hasOwnProperty", "call", "error", "response", "promise", "then", "lifespan", "setTimeout", "index", "indexOf", "splice", "catch", "push", "suspend", "peek", "_globalCache$find", "find", "clear"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/node_modules/suspend-react/dist/index.js"], "sourcesContent": ["function shallowEqualArrays(arrA, arrB, equal = (a, b) => a === b) {\n  if (arrA === arrB) return true;\n  if (!arrA || !arrB) return false;\n  const len = arrA.length;\n  if (arrB.length !== len) return false;\n\n  for (let i = 0; i < len; i++) if (!equal(arrA[i], arrB[i])) return false;\n\n  return true;\n}\n\nconst globalCache = [];\n\nfunction query(fn, keys, preload = false, config = {}) {\n  for (const entry of globalCache) {\n    // Find a match\n    if (shallowEqualArrays(keys, entry.keys, entry.equal)) {\n      // If we're pre-loading and the element is present, just return\n      if (preload) return undefined; // If an error occurred, throw\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'error')) throw entry.error; // If a response was successful, return\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'response')) return entry.response; // If the promise is still unresolved, throw\n\n      if (!preload) throw entry.promise;\n    }\n  } // The request is new or has changed.\n\n\n  const entry = {\n    keys,\n    equal: config.equal,\n    promise: // Execute the promise\n    fn(...keys) // When it resolves, store its value\n    .then(response => entry.response = response) // Remove the entry if a lifespan was given\n    .then(() => {\n      if (config.lifespan && config.lifespan > 0) {\n        setTimeout(() => {\n          const index = globalCache.indexOf(entry);\n          if (index !== -1) globalCache.splice(index, 1);\n        }, config.lifespan);\n      }\n    }) // Store caught errors, they will be thrown in the render-phase to bubble into an error-bound\n    .catch(error => entry.error = error)\n  }; // Register the entry\n\n  globalCache.push(entry); // And throw the promise, this yields control back to React\n\n  if (!preload) throw entry.promise;\n  return undefined;\n}\n\nconst suspend = (fn, keys, config) => query(fn, keys, false, config);\n\nconst preload = (fn, keys, config) => void query(fn, keys, true, config);\n\nconst peek = keys => {\n  var _globalCache$find;\n\n  return (_globalCache$find = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal))) == null ? void 0 : _globalCache$find.response;\n};\n\nconst clear = keys => {\n  if (keys === undefined || keys.length === 0) globalCache.splice(0, globalCache.length);else {\n    const entry = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal));\n\n    if (entry) {\n      const index = globalCache.indexOf(entry);\n      if (index !== -1) globalCache.splice(index, 1);\n    }\n  }\n};\n\nexport { clear, peek, preload, suspend };\n"], "mappings": "AAAA,SAASA,kBAAkBA,CAACC,IAAI,EAAEC,IAAI,EAAEC,KAAK,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKC,CAAC,EAAE;EACjE,IAAIJ,IAAI,KAAKC,IAAI,EAAE,OAAO,IAAI;EAC9B,IAAI,CAACD,IAAI,IAAI,CAACC,IAAI,EAAE,OAAO,KAAK;EAChC,MAAMI,GAAG,GAAGL,IAAI,CAACM,MAAM;EACvB,IAAIL,IAAI,CAACK,MAAM,KAAKD,GAAG,EAAE,OAAO,KAAK;EAErC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE,IAAI,CAACL,KAAK,CAACF,IAAI,CAACO,CAAC,CAAC,EAAEN,IAAI,CAACM,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;EAExE,OAAO,IAAI;AACb;AAEA,MAAMC,WAAW,GAAG,EAAE;AAEtB,SAASC,KAAKA,CAACC,EAAE,EAAEC,IAAI,EAAEC,OAAO,GAAG,KAAK,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAE;EACrD,KAAK,MAAMC,KAAK,IAAIN,WAAW,EAAE;IAC/B;IACA,IAAIT,kBAAkB,CAACY,IAAI,EAAEG,KAAK,CAACH,IAAI,EAAEG,KAAK,CAACZ,KAAK,CAAC,EAAE;MACrD;MACA,IAAIU,OAAO,EAAE,OAAOG,SAAS,CAAC,CAAC;;MAE/B,IAAIC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,KAAK,EAAE,OAAO,CAAC,EAAE,MAAMA,KAAK,CAACM,KAAK,CAAC,CAAC;;MAE7E,IAAIJ,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,KAAK,EAAE,UAAU,CAAC,EAAE,OAAOA,KAAK,CAACO,QAAQ,CAAC,CAAC;;MAEpF,IAAI,CAACT,OAAO,EAAE,MAAME,KAAK,CAACQ,OAAO;IACnC;EACF,CAAC,CAAC;;EAGF,MAAMR,KAAK,GAAG;IACZH,IAAI;IACJT,KAAK,EAAEW,MAAM,CAACX,KAAK;IACnBoB,OAAO;IAAE;IACTZ,EAAE,CAAC,GAAGC,IAAI,CAAC,CAAC;IAAA,CACXY,IAAI,CAACF,QAAQ,IAAIP,KAAK,CAACO,QAAQ,GAAGA,QAAQ,CAAC,CAAC;IAAA,CAC5CE,IAAI,CAAC,MAAM;MACV,IAAIV,MAAM,CAACW,QAAQ,IAAIX,MAAM,CAACW,QAAQ,GAAG,CAAC,EAAE;QAC1CC,UAAU,CAAC,MAAM;UACf,MAAMC,KAAK,GAAGlB,WAAW,CAACmB,OAAO,CAACb,KAAK,CAAC;UACxC,IAAIY,KAAK,KAAK,CAAC,CAAC,EAAElB,WAAW,CAACoB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QAChD,CAAC,EAAEb,MAAM,CAACW,QAAQ,CAAC;MACrB;IACF,CAAC,CAAC,CAAC;IAAA,CACFK,KAAK,CAACT,KAAK,IAAIN,KAAK,CAACM,KAAK,GAAGA,KAAK;EACrC,CAAC,CAAC,CAAC;;EAEHZ,WAAW,CAACsB,IAAI,CAAChB,KAAK,CAAC,CAAC,CAAC;;EAEzB,IAAI,CAACF,OAAO,EAAE,MAAME,KAAK,CAACQ,OAAO;EACjC,OAAOP,SAAS;AAClB;AAEA,MAAMgB,OAAO,GAAGA,CAACrB,EAAE,EAAEC,IAAI,EAAEE,MAAM,KAAKJ,KAAK,CAACC,EAAE,EAAEC,IAAI,EAAE,KAAK,EAAEE,MAAM,CAAC;AAEpE,MAAMD,OAAO,GAAGA,CAACF,EAAE,EAAEC,IAAI,EAAEE,MAAM,KAAK,KAAKJ,KAAK,CAACC,EAAE,EAAEC,IAAI,EAAE,IAAI,EAAEE,MAAM,CAAC;AAExE,MAAMmB,IAAI,GAAGrB,IAAI,IAAI;EACnB,IAAIsB,iBAAiB;EAErB,OAAO,CAACA,iBAAiB,GAAGzB,WAAW,CAAC0B,IAAI,CAACpB,KAAK,IAAIf,kBAAkB,CAACY,IAAI,EAAEG,KAAK,CAACH,IAAI,EAAEG,KAAK,CAACZ,KAAK,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG+B,iBAAiB,CAACZ,QAAQ;AACzJ,CAAC;AAED,MAAMc,KAAK,GAAGxB,IAAI,IAAI;EACpB,IAAIA,IAAI,KAAKI,SAAS,IAAIJ,IAAI,CAACL,MAAM,KAAK,CAAC,EAAEE,WAAW,CAACoB,MAAM,CAAC,CAAC,EAAEpB,WAAW,CAACF,MAAM,CAAC,CAAC,KAAK;IAC1F,MAAMQ,KAAK,GAAGN,WAAW,CAAC0B,IAAI,CAACpB,KAAK,IAAIf,kBAAkB,CAACY,IAAI,EAAEG,KAAK,CAACH,IAAI,EAAEG,KAAK,CAACZ,KAAK,CAAC,CAAC;IAE1F,IAAIY,KAAK,EAAE;MACT,MAAMY,KAAK,GAAGlB,WAAW,CAACmB,OAAO,CAACb,KAAK,CAAC;MACxC,IAAIY,KAAK,KAAK,CAAC,CAAC,EAAElB,WAAW,CAACoB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAChD;EACF;AACF,CAAC;AAED,SAASS,KAAK,EAAEH,IAAI,EAAEpB,OAAO,EAAEmB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}