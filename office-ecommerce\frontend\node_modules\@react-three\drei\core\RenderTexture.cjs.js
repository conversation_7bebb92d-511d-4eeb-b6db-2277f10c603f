"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("three"),t=require("react"),n=require("@react-three/fiber"),a=require("./useFBO.cjs.js");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var c=u(e),s=i(r),o=i(t);const l=o.forwardRef((({children:e,compute:r,width:t,height:u,samples:i=8,renderPriority:l=0,eventPriority:d=0,frames:p=1/0,stencilBuffer:m=!1,depthBuffer:b=!0,generateMipmaps:v=!1,...g},h)=>{const{size:y,viewport:j}=n.useThree(),O=a.useFBO((t||y.width)*j.dpr,(u||y.height)*j.dpr,{samples:i,stencilBuffer:m,depthBuffer:b,generateMipmaps:v}),[x]=o.useState((()=>new s.Scene)),P=o.useCallback(((e,r,t)=>{var n,a;let u=null==(n=O.texture)?void 0:n.__r3f.parent;for(;u&&!(u instanceof s.Object3D);)u=u.__r3f.parent;if(!u)return!1;t.raycaster.camera||t.events.compute(e,t,null==(a=t.previousRoot)?void 0:a.getState());const[i]=t.raycaster.intersectObject(u);if(!i)return!1;const c=i.uv;if(!c)return!1;r.raycaster.setFromCamera(r.pointer.set(2*c.x-1,2*c.y-1),r.camera)}),[]);return o.useImperativeHandle(h,(()=>O.texture),[O]),o.createElement(o.Fragment,null,n.createPortal(o.createElement(f,{renderPriority:l,frames:p,fbo:O},e),x,{events:{compute:r||P,priority:d}}),o.createElement("primitive",c.default({object:O.texture},g)))}));function f({frames:e,renderPriority:r,children:t,fbo:a}){let u=0;return n.useFrame((r=>{(e===1/0||u<e)&&(r.gl.setRenderTarget(a),r.gl.render(r.scene,r.camera),r.gl.setRenderTarget(null),u++)}),r),o.createElement(o.Fragment,null,t)}exports.RenderTexture=l;
