{"ast": null, "code": "import * as React from 'react';\nimport { useProgress } from '../core/useProgress.js';\nconst defaultDataInterpolation = p => `Loading ${p.toFixed(2)}%`;\nfunction Loader({\n  containerStyles,\n  innerStyles,\n  barStyles,\n  dataStyles,\n  dataInterpolation = defaultDataInterpolation,\n  initialState = active => active\n}) {\n  const {\n    active,\n    progress\n  } = useProgress();\n  const progressRef = React.useRef(0);\n  const rafRef = React.useRef(0);\n  const progressSpanRef = React.useRef(null);\n  const [shown, setShown] = React.useState(initialState(active));\n  React.useEffect(() => {\n    let t;\n    if (active !== shown) t = setTimeout(() => setShown(active), 300);\n    return () => clearTimeout(t);\n  }, [shown, active]);\n  const updateProgress = React.useCallback(() => {\n    if (!progressSpanRef.current) return;\n    progressRef.current += (progress - progressRef.current) / 2;\n    if (progressRef.current > 0.95 * progress || progress === 100) progressRef.current = progress;\n    progressSpanRef.current.innerText = dataInterpolation(progressRef.current);\n    if (progressRef.current < progress) rafRef.current = requestAnimationFrame(updateProgress);\n  }, [dataInterpolation, progress]);\n  React.useEffect(() => {\n    updateProgress();\n    return () => cancelAnimationFrame(rafRef.current);\n  }, [updateProgress]);\n  return shown ? /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...styles.container,\n      opacity: active ? 1 : 0,\n      ...containerStyles\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", null, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...styles.inner,\n      ...innerStyles\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...styles.bar,\n      transform: `scaleX(${progress / 100})`,\n      ...barStyles\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    ref: progressSpanRef,\n    style: {\n      ...styles.data,\n      ...dataStyles\n    }\n  })))) : null;\n}\nconst styles = {\n  container: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    background: '#171717',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    transition: 'opacity 300ms ease',\n    zIndex: 1000\n  },\n  inner: {\n    width: 100,\n    height: 3,\n    background: '#272727',\n    textAlign: 'center'\n  },\n  bar: {\n    height: 3,\n    width: '100%',\n    background: 'white',\n    transition: 'transform 200ms',\n    transformOrigin: 'left center'\n  },\n  data: {\n    display: 'inline-block',\n    position: 'relative',\n    fontVariantNumeric: 'tabular-nums',\n    marginTop: '0.8em',\n    color: '#f0f0f0',\n    fontSize: '0.6em',\n    fontFamily: `-apple-system, BlinkMacSystemFont, \"Inter\", \"Segoe UI\", \"Helvetica Neue\", Helvetica, Arial, Roboto, Ubuntu, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\"`,\n    whiteSpace: 'nowrap'\n  }\n};\nexport { Loader };", "map": {"version": 3, "names": ["React", "useProgress", "defaultDataInterpolation", "p", "toFixed", "Loader", "containerStyles", "innerStyles", "barStyles", "dataStyles", "dataInterpolation", "initialState", "active", "progress", "progressRef", "useRef", "rafRef", "progressSpanRef", "shown", "setShown", "useState", "useEffect", "t", "setTimeout", "clearTimeout", "updateProgress", "useCallback", "current", "innerText", "requestAnimationFrame", "cancelAnimationFrame", "createElement", "style", "styles", "container", "opacity", "inner", "bar", "transform", "ref", "data", "position", "top", "left", "width", "height", "background", "display", "alignItems", "justifyContent", "transition", "zIndex", "textAlign", "transform<PERSON><PERSON>in", "fontVariantNumeric", "marginTop", "color", "fontSize", "fontFamily", "whiteSpace"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/web/Loader.js"], "sourcesContent": ["import * as React from 'react';\nimport { useProgress } from '../core/useProgress.js';\n\nconst defaultDataInterpolation = p => `Loading ${p.toFixed(2)}%`;\n\nfunction Loader({\n  containerStyles,\n  innerStyles,\n  barStyles,\n  dataStyles,\n  dataInterpolation = defaultDataInterpolation,\n  initialState = active => active\n}) {\n  const {\n    active,\n    progress\n  } = useProgress();\n  const progressRef = React.useRef(0);\n  const rafRef = React.useRef(0);\n  const progressSpanRef = React.useRef(null);\n  const [shown, setShown] = React.useState(initialState(active));\n  React.useEffect(() => {\n    let t;\n    if (active !== shown) t = setTimeout(() => setShown(active), 300);\n    return () => clearTimeout(t);\n  }, [shown, active]);\n  const updateProgress = React.useCallback(() => {\n    if (!progressSpanRef.current) return;\n    progressRef.current += (progress - progressRef.current) / 2;\n    if (progressRef.current > 0.95 * progress || progress === 100) progressRef.current = progress;\n    progressSpanRef.current.innerText = dataInterpolation(progressRef.current);\n    if (progressRef.current < progress) rafRef.current = requestAnimationFrame(updateProgress);\n  }, [dataInterpolation, progress]);\n  React.useEffect(() => {\n    updateProgress();\n    return () => cancelAnimationFrame(rafRef.current);\n  }, [updateProgress]);\n  return shown ? /*#__PURE__*/React.createElement(\"div\", {\n    style: { ...styles.container,\n      opacity: active ? 1 : 0,\n      ...containerStyles\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", null, /*#__PURE__*/React.createElement(\"div\", {\n    style: { ...styles.inner,\n      ...innerStyles\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: { ...styles.bar,\n      transform: `scaleX(${progress / 100})`,\n      ...barStyles\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    ref: progressSpanRef,\n    style: { ...styles.data,\n      ...dataStyles\n    }\n  })))) : null;\n}\nconst styles = {\n  container: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    background: '#171717',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    transition: 'opacity 300ms ease',\n    zIndex: 1000\n  },\n  inner: {\n    width: 100,\n    height: 3,\n    background: '#272727',\n    textAlign: 'center'\n  },\n  bar: {\n    height: 3,\n    width: '100%',\n    background: 'white',\n    transition: 'transform 200ms',\n    transformOrigin: 'left center'\n  },\n  data: {\n    display: 'inline-block',\n    position: 'relative',\n    fontVariantNumeric: 'tabular-nums',\n    marginTop: '0.8em',\n    color: '#f0f0f0',\n    fontSize: '0.6em',\n    fontFamily: `-apple-system, BlinkMacSystemFont, \"Inter\", \"Segoe UI\", \"Helvetica Neue\", Helvetica, Arial, Roboto, Ubuntu, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\"`,\n    whiteSpace: 'nowrap'\n  }\n};\n\nexport { Loader };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,wBAAwB;AAEpD,MAAMC,wBAAwB,GAAGC,CAAC,IAAI,WAAWA,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG;AAEhE,SAASC,MAAMA,CAAC;EACdC,eAAe;EACfC,WAAW;EACXC,SAAS;EACTC,UAAU;EACVC,iBAAiB,GAAGR,wBAAwB;EAC5CS,YAAY,GAAGC,MAAM,IAAIA;AAC3B,CAAC,EAAE;EACD,MAAM;IACJA,MAAM;IACNC;EACF,CAAC,GAAGZ,WAAW,CAAC,CAAC;EACjB,MAAMa,WAAW,GAAGd,KAAK,CAACe,MAAM,CAAC,CAAC,CAAC;EACnC,MAAMC,MAAM,GAAGhB,KAAK,CAACe,MAAM,CAAC,CAAC,CAAC;EAC9B,MAAME,eAAe,GAAGjB,KAAK,CAACe,MAAM,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACG,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,KAAK,CAACoB,QAAQ,CAACT,YAAY,CAACC,MAAM,CAAC,CAAC;EAC9DZ,KAAK,CAACqB,SAAS,CAAC,MAAM;IACpB,IAAIC,CAAC;IACL,IAAIV,MAAM,KAAKM,KAAK,EAAEI,CAAC,GAAGC,UAAU,CAAC,MAAMJ,QAAQ,CAACP,MAAM,CAAC,EAAE,GAAG,CAAC;IACjE,OAAO,MAAMY,YAAY,CAACF,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACJ,KAAK,EAAEN,MAAM,CAAC,CAAC;EACnB,MAAMa,cAAc,GAAGzB,KAAK,CAAC0B,WAAW,CAAC,MAAM;IAC7C,IAAI,CAACT,eAAe,CAACU,OAAO,EAAE;IAC9Bb,WAAW,CAACa,OAAO,IAAI,CAACd,QAAQ,GAAGC,WAAW,CAACa,OAAO,IAAI,CAAC;IAC3D,IAAIb,WAAW,CAACa,OAAO,GAAG,IAAI,GAAGd,QAAQ,IAAIA,QAAQ,KAAK,GAAG,EAAEC,WAAW,CAACa,OAAO,GAAGd,QAAQ;IAC7FI,eAAe,CAACU,OAAO,CAACC,SAAS,GAAGlB,iBAAiB,CAACI,WAAW,CAACa,OAAO,CAAC;IAC1E,IAAIb,WAAW,CAACa,OAAO,GAAGd,QAAQ,EAAEG,MAAM,CAACW,OAAO,GAAGE,qBAAqB,CAACJ,cAAc,CAAC;EAC5F,CAAC,EAAE,CAACf,iBAAiB,EAAEG,QAAQ,CAAC,CAAC;EACjCb,KAAK,CAACqB,SAAS,CAAC,MAAM;IACpBI,cAAc,CAAC,CAAC;IAChB,OAAO,MAAMK,oBAAoB,CAACd,MAAM,CAACW,OAAO,CAAC;EACnD,CAAC,EAAE,CAACF,cAAc,CAAC,CAAC;EACpB,OAAOP,KAAK,GAAG,aAAalB,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;IACrDC,KAAK,EAAE;MAAE,GAAGC,MAAM,CAACC,SAAS;MAC1BC,OAAO,EAAEvB,MAAM,GAAG,CAAC,GAAG,CAAC;MACvB,GAAGN;IACL;EACF,CAAC,EAAE,aAAaN,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,aAAa/B,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;IACvFC,KAAK,EAAE;MAAE,GAAGC,MAAM,CAACG,KAAK;MACtB,GAAG7B;IACL;EACF,CAAC,EAAE,aAAaP,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;IACzCC,KAAK,EAAE;MAAE,GAAGC,MAAM,CAACI,GAAG;MACpBC,SAAS,EAAE,UAAUzB,QAAQ,GAAG,GAAG,GAAG;MACtC,GAAGL;IACL;EACF,CAAC,CAAC,EAAE,aAAaR,KAAK,CAAC+B,aAAa,CAAC,MAAM,EAAE;IAC3CQ,GAAG,EAAEtB,eAAe;IACpBe,KAAK,EAAE;MAAE,GAAGC,MAAM,CAACO,IAAI;MACrB,GAAG/B;IACL;EACF,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;AACd;AACA,MAAMwB,MAAM,GAAG;EACbC,SAAS,EAAE;IACTO,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,oBAAoB;IAChCC,MAAM,EAAE;EACV,CAAC;EACDf,KAAK,EAAE;IACLQ,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE,SAAS;IACrBM,SAAS,EAAE;EACb,CAAC;EACDf,GAAG,EAAE;IACHQ,MAAM,EAAE,CAAC;IACTD,KAAK,EAAE,MAAM;IACbE,UAAU,EAAE,OAAO;IACnBI,UAAU,EAAE,iBAAiB;IAC7BG,eAAe,EAAE;EACnB,CAAC;EACDb,IAAI,EAAE;IACJO,OAAO,EAAE,cAAc;IACvBN,QAAQ,EAAE,UAAU;IACpBa,kBAAkB,EAAE,cAAc;IAClCC,SAAS,EAAE,OAAO;IAClBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,OAAO;IACjBC,UAAU,EAAE,kLAAkL;IAC9LC,UAAU,EAAE;EACd;AACF,CAAC;AAED,SAAStD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}