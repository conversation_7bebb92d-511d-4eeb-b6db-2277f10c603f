{"ast": null, "code": "import { Box3, Vector3 } from 'three';\nimport { TRAVERSAL_COST, TRIANGLE_INTERSECT_COST } from '../core/Constants.js';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nconst _box1 = /* @__PURE__ */new Box3();\nconst _box2 = /* @__PURE__ */new Box3();\nconst _vec = /* @__PURE__ */new Vector3();\n\n// https://stackoverflow.com/questions/1248302/how-to-get-the-size-of-a-javascript-object\nfunction getPrimitiveSize(el) {\n  switch (typeof el) {\n    case 'number':\n      return 8;\n    case 'string':\n      return el.length * 2;\n    case 'boolean':\n      return 4;\n    default:\n      return 0;\n  }\n}\nfunction isTypedArray(arr) {\n  const regex = /(Uint|Int|Float)(8|16|32)Array/;\n  return regex.test(arr.constructor.name);\n}\nfunction getRootExtremes(bvh, group) {\n  const result = {\n    nodeCount: 0,\n    leafNodeCount: 0,\n    depth: {\n      min: Infinity,\n      max: -Infinity\n    },\n    tris: {\n      min: Infinity,\n      max: -Infinity\n    },\n    splits: [0, 0, 0],\n    surfaceAreaScore: 0\n  };\n  bvh.traverse((depth, isLeaf, boundingData, offsetOrSplit, count) => {\n    const l0 = boundingData[0 + 3] - boundingData[0];\n    const l1 = boundingData[1 + 3] - boundingData[1];\n    const l2 = boundingData[2 + 3] - boundingData[2];\n    const surfaceArea = 2 * (l0 * l1 + l1 * l2 + l2 * l0);\n    result.nodeCount++;\n    if (isLeaf) {\n      result.leafNodeCount++;\n      result.depth.min = Math.min(depth, result.depth.min);\n      result.depth.max = Math.max(depth, result.depth.max);\n      result.tris.min = Math.min(count, result.tris.min);\n      result.tris.max = Math.max(count, result.tris.max);\n      result.surfaceAreaScore += surfaceArea * TRIANGLE_INTERSECT_COST * count;\n    } else {\n      result.splits[offsetOrSplit]++;\n      result.surfaceAreaScore += surfaceArea * TRAVERSAL_COST;\n    }\n  }, group);\n\n  // If there are no leaf nodes because the tree hasn't finished generating yet.\n  if (result.tris.min === Infinity) {\n    result.tris.min = 0;\n    result.tris.max = 0;\n  }\n  if (result.depth.min === Infinity) {\n    result.depth.min = 0;\n    result.depth.max = 0;\n  }\n  return result;\n}\nfunction getBVHExtremes(bvh) {\n  return bvh._roots.map((root, i) => getRootExtremes(bvh, i));\n}\nfunction estimateMemoryInBytes(obj) {\n  const traversed = new Set();\n  const stack = [obj];\n  let bytes = 0;\n  while (stack.length) {\n    const curr = stack.pop();\n    if (traversed.has(curr)) {\n      continue;\n    }\n    traversed.add(curr);\n    for (let key in curr) {\n      if (!curr.hasOwnProperty(key)) {\n        continue;\n      }\n      bytes += getPrimitiveSize(key);\n      const value = curr[key];\n      if (value && (typeof value === 'object' || typeof value === 'function')) {\n        if (isTypedArray(value)) {\n          bytes += value.byteLength;\n        } else if (value instanceof ArrayBuffer) {\n          bytes += value.byteLength;\n        } else {\n          stack.push(value);\n        }\n      } else {\n        bytes += getPrimitiveSize(value);\n      }\n    }\n  }\n  return bytes;\n}\nfunction validateBounds(bvh) {\n  const geometry = bvh.geometry;\n  const depthStack = [];\n  const index = geometry.index;\n  const position = geometry.getAttribute('position');\n  let passes = true;\n  bvh.traverse((depth, isLeaf, boundingData, offset, count) => {\n    const info = {\n      depth,\n      isLeaf,\n      boundingData,\n      offset,\n      count\n    };\n    depthStack[depth] = info;\n    arrayToBox(0, boundingData, _box1);\n    const parent = depthStack[depth - 1];\n    if (isLeaf) {\n      // check triangles\n      for (let i = offset * 3, l = (offset + count) * 3; i < l; i += 3) {\n        const i0 = index.getX(i);\n        const i1 = index.getX(i + 1);\n        const i2 = index.getX(i + 2);\n        let isContained;\n        _vec.fromBufferAttribute(position, i0);\n        isContained = _box1.containsPoint(_vec);\n        _vec.fromBufferAttribute(position, i1);\n        isContained = isContained && _box1.containsPoint(_vec);\n        _vec.fromBufferAttribute(position, i2);\n        isContained = isContained && _box1.containsPoint(_vec);\n        console.assert(isContained, 'Leaf bounds does not fully contain triangle.');\n        passes = passes && isContained;\n      }\n    }\n    if (parent) {\n      // check if my bounds fit in my parents\n      arrayToBox(0, boundingData, _box2);\n      const isContained = _box2.containsBox(_box1);\n      console.assert(isContained, 'Parent bounds does not fully contain child.');\n      passes = passes && isContained;\n    }\n  });\n  return passes;\n}\n\n// Returns a simple, human readable object that represents the BVH.\nfunction getJSONStructure(bvh) {\n  const depthStack = [];\n  bvh.traverse((depth, isLeaf, boundingData, offset, count) => {\n    const info = {\n      bounds: arrayToBox(0, boundingData, new Box3())\n    };\n    if (isLeaf) {\n      info.count = count;\n      info.offset = offset;\n    } else {\n      info.left = null;\n      info.right = null;\n    }\n    depthStack[depth] = info;\n\n    // traversal hits the left then right node\n    const parent = depthStack[depth - 1];\n    if (parent) {\n      if (parent.left === null) {\n        parent.left = info;\n      } else {\n        parent.right = info;\n      }\n    }\n  });\n  return depthStack[0];\n}\nexport { estimateMemoryInBytes, getBVHExtremes, validateBounds, getJSONStructure };", "map": {"version": 3, "names": ["Box3", "Vector3", "TRAVERSAL_COST", "TRIANGLE_INTERSECT_COST", "arrayToBox", "_box1", "_box2", "_vec", "getPrimitiveSize", "el", "length", "isTypedArray", "arr", "regex", "test", "constructor", "name", "getRootExtremes", "bvh", "group", "result", "nodeCount", "leafNodeCount", "depth", "min", "Infinity", "max", "tris", "splits", "surfaceAreaScore", "traverse", "<PERSON><PERSON><PERSON><PERSON>", "boundingData", "offsetOrSplit", "count", "l0", "l1", "l2", "surfaceArea", "Math", "getBVHExtremes", "_roots", "map", "root", "i", "estimateMemoryInBytes", "obj", "traversed", "Set", "stack", "bytes", "curr", "pop", "has", "add", "key", "hasOwnProperty", "value", "byteLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "push", "validateBounds", "geometry", "depthStack", "index", "position", "getAttribute", "passes", "offset", "info", "parent", "l", "i0", "getX", "i1", "i2", "isContained", "fromBufferAttribute", "containsPoint", "console", "assert", "containsBox", "getJSONStructure", "bounds", "left", "right"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/three-mesh-bvh/src/debug/Debug.js"], "sourcesContent": ["import { Box3, Vector3 } from 'three';\nimport { TRAVERSAL_COST, TRIANGLE_INTERSECT_COST } from '../core/Constants.js';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\n\nconst _box1 = /* @__PURE__ */ new Box3();\nconst _box2 = /* @__PURE__ */ new Box3();\nconst _vec = /* @__PURE__ */ new Vector3();\n\n// https://stackoverflow.com/questions/1248302/how-to-get-the-size-of-a-javascript-object\nfunction getPrimitiveSize( el ) {\n\n\tswitch ( typeof el ) {\n\n\t\tcase 'number':\n\t\t\treturn 8;\n\t\tcase 'string':\n\t\t\treturn el.length * 2;\n\t\tcase 'boolean':\n\t\t\treturn 4;\n\t\tdefault:\n\t\t\treturn 0;\n\n\t}\n\n}\n\nfunction isTypedArray( arr ) {\n\n\tconst regex = /(Uint|Int|Float)(8|16|32)Array/;\n\treturn regex.test( arr.constructor.name );\n\n}\n\nfunction getRootExtremes( bvh, group ) {\n\n\tconst result = {\n\t\tnodeCount: 0,\n\t\tleafNodeCount: 0,\n\n\t\tdepth: {\n\t\t\tmin: Infinity, max: - Infinity\n\t\t},\n\t\ttris: {\n\t\t\tmin: Infinity, max: - Infinity\n\t\t},\n\t\tsplits: [ 0, 0, 0 ],\n\t\tsurfaceAreaScore: 0,\n\t};\n\n\tbvh.traverse( ( depth, isLeaf, boundingData, offsetOrSplit, count ) => {\n\n\t\tconst l0 = boundingData[ 0 + 3 ] - boundingData[ 0 ];\n\t\tconst l1 = boundingData[ 1 + 3 ] - boundingData[ 1 ];\n\t\tconst l2 = boundingData[ 2 + 3 ] - boundingData[ 2 ];\n\n\t\tconst surfaceArea = 2 * ( l0 * l1 + l1 * l2 + l2 * l0 );\n\n\t\tresult.nodeCount ++;\n\t\tif ( isLeaf ) {\n\n\t\t\tresult.leafNodeCount ++;\n\n\t\t\tresult.depth.min = Math.min( depth, result.depth.min );\n\t\t\tresult.depth.max = Math.max( depth, result.depth.max );\n\n\t\t\tresult.tris.min = Math.min( count, result.tris.min );\n\t\t\tresult.tris.max = Math.max( count, result.tris.max );\n\n\t\t\tresult.surfaceAreaScore += surfaceArea * TRIANGLE_INTERSECT_COST * count;\n\n\t\t} else {\n\n\t\t\tresult.splits[ offsetOrSplit ] ++;\n\n\t\t\tresult.surfaceAreaScore += surfaceArea * TRAVERSAL_COST;\n\n\t\t}\n\n\t}, group );\n\n\t// If there are no leaf nodes because the tree hasn't finished generating yet.\n\tif ( result.tris.min === Infinity ) {\n\n\t\tresult.tris.min = 0;\n\t\tresult.tris.max = 0;\n\n\t}\n\n\tif ( result.depth.min === Infinity ) {\n\n\t\tresult.depth.min = 0;\n\t\tresult.depth.max = 0;\n\n\t}\n\n\treturn result;\n\n}\n\nfunction getBVHExtremes( bvh ) {\n\n\treturn bvh._roots.map( ( root, i ) => getRootExtremes( bvh, i ) );\n\n}\n\nfunction estimateMemoryInBytes( obj ) {\n\n\tconst traversed = new Set();\n\tconst stack = [ obj ];\n\tlet bytes = 0;\n\n\twhile ( stack.length ) {\n\n\t\tconst curr = stack.pop();\n\t\tif ( traversed.has( curr ) ) {\n\n\t\t\tcontinue;\n\n\t\t}\n\n\t\ttraversed.add( curr );\n\n\t\tfor ( let key in curr ) {\n\n\t\t\tif ( ! curr.hasOwnProperty( key ) ) {\n\n\t\t\t\tcontinue;\n\n\t\t\t}\n\n\t\t\tbytes += getPrimitiveSize( key );\n\n\t\t\tconst value = curr[ key ];\n\t\t\tif ( value && ( typeof value === 'object' || typeof value === 'function' ) ) {\n\n\t\t\t\tif ( isTypedArray( value ) ) {\n\n\t\t\t\t\tbytes += value.byteLength;\n\n\t\t\t\t} else if ( value instanceof ArrayBuffer ) {\n\n\t\t\t\t\tbytes += value.byteLength;\n\n\t\t\t\t} else {\n\n\t\t\t\t\tstack.push( value );\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tbytes += getPrimitiveSize( value );\n\n\t\t\t}\n\n\n\t\t}\n\n\t}\n\n\treturn bytes;\n\n}\n\nfunction validateBounds( bvh ) {\n\n\tconst geometry = bvh.geometry;\n\tconst depthStack = [];\n\tconst index = geometry.index;\n\tconst position = geometry.getAttribute( 'position' );\n\tlet passes = true;\n\n\tbvh.traverse( ( depth, isLeaf, boundingData, offset, count ) => {\n\n\t\tconst info = {\n\t\t\tdepth,\n\t\t\tisLeaf,\n\t\t\tboundingData,\n\t\t\toffset,\n\t\t\tcount,\n\t\t};\n\t\tdepthStack[ depth ] = info;\n\n\t\tarrayToBox( 0, boundingData, _box1 );\n\t\tconst parent = depthStack[ depth - 1 ];\n\n\t\tif ( isLeaf ) {\n\n\t\t\t// check triangles\n\t\t\tfor ( let i = offset * 3, l = ( offset + count ) * 3; i < l; i += 3 ) {\n\n\t\t\t\tconst i0 = index.getX( i );\n\t\t\t\tconst i1 = index.getX( i + 1 );\n\t\t\t\tconst i2 = index.getX( i + 2 );\n\n\t\t\t\tlet isContained;\n\n\t\t\t\t_vec.fromBufferAttribute( position, i0 );\n\t\t\t\tisContained = _box1.containsPoint( _vec );\n\n\t\t\t\t_vec.fromBufferAttribute( position, i1 );\n\t\t\t\tisContained = isContained && _box1.containsPoint( _vec );\n\n\t\t\t\t_vec.fromBufferAttribute( position, i2 );\n\t\t\t\tisContained = isContained && _box1.containsPoint( _vec );\n\n\t\t\t\tconsole.assert( isContained, 'Leaf bounds does not fully contain triangle.' );\n\t\t\t\tpasses = passes && isContained;\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( parent ) {\n\n\t\t\t// check if my bounds fit in my parents\n\t\t\tarrayToBox( 0, boundingData, _box2 );\n\n\t\t\tconst isContained = _box2.containsBox( _box1 );\n\t\t\tconsole.assert( isContained, 'Parent bounds does not fully contain child.' );\n\t\t\tpasses = passes && isContained;\n\n\t\t}\n\n\t} );\n\n\treturn passes;\n\n}\n\n// Returns a simple, human readable object that represents the BVH.\nfunction getJSONStructure( bvh ) {\n\n\tconst depthStack = [];\n\n\tbvh.traverse( ( depth, isLeaf, boundingData, offset, count ) => {\n\n\t\tconst info = {\n\t\t\tbounds: arrayToBox( 0, boundingData, new Box3() ),\n\t\t};\n\n\t\tif ( isLeaf ) {\n\n\t\t\tinfo.count = count;\n\t\t\tinfo.offset = offset;\n\n\t\t} else {\n\n\t\t\tinfo.left = null;\n\t\t\tinfo.right = null;\n\n\t\t}\n\n\t\tdepthStack[ depth ] = info;\n\n\t\t// traversal hits the left then right node\n\t\tconst parent = depthStack[ depth - 1 ];\n\t\tif ( parent ) {\n\n\t\t\tif ( parent.left === null ) {\n\n\t\t\t\tparent.left = info;\n\n\t\t\t} else {\n\n\t\t\t\tparent.right = info;\n\n\t\t\t}\n\n\t\t}\n\n\t} );\n\n\treturn depthStack[ 0 ];\n\n}\n\nexport { estimateMemoryInBytes, getBVHExtremes, validateBounds, getJSONStructure };\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,OAAO,QAAQ,OAAO;AACrC,SAASC,cAAc,EAAEC,uBAAuB,QAAQ,sBAAsB;AAC9E,SAASC,UAAU,QAAQ,+BAA+B;AAE1D,MAAMC,KAAK,GAAG,eAAgB,IAAIL,IAAI,CAAC,CAAC;AACxC,MAAMM,KAAK,GAAG,eAAgB,IAAIN,IAAI,CAAC,CAAC;AACxC,MAAMO,IAAI,GAAG,eAAgB,IAAIN,OAAO,CAAC,CAAC;;AAE1C;AACA,SAASO,gBAAgBA,CAAEC,EAAE,EAAG;EAE/B,QAAS,OAAOA,EAAE;IAEjB,KAAK,QAAQ;MACZ,OAAO,CAAC;IACT,KAAK,QAAQ;MACZ,OAAOA,EAAE,CAACC,MAAM,GAAG,CAAC;IACrB,KAAK,SAAS;MACb,OAAO,CAAC;IACT;MACC,OAAO,CAAC;EAEV;AAED;AAEA,SAASC,YAAYA,CAAEC,GAAG,EAAG;EAE5B,MAAMC,KAAK,GAAG,gCAAgC;EAC9C,OAAOA,KAAK,CAACC,IAAI,CAAEF,GAAG,CAACG,WAAW,CAACC,IAAK,CAAC;AAE1C;AAEA,SAASC,eAAeA,CAAEC,GAAG,EAAEC,KAAK,EAAG;EAEtC,MAAMC,MAAM,GAAG;IACdC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE,CAAC;IAEhBC,KAAK,EAAE;MACNC,GAAG,EAAEC,QAAQ;MAAEC,GAAG,EAAE,CAAED;IACvB,CAAC;IACDE,IAAI,EAAE;MACLH,GAAG,EAAEC,QAAQ;MAAEC,GAAG,EAAE,CAAED;IACvB,CAAC;IACDG,MAAM,EAAE,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE;IACnBC,gBAAgB,EAAE;EACnB,CAAC;EAEDX,GAAG,CAACY,QAAQ,CAAE,CAAEP,KAAK,EAAEQ,MAAM,EAAEC,YAAY,EAAEC,aAAa,EAAEC,KAAK,KAAM;IAEtE,MAAMC,EAAE,GAAGH,YAAY,CAAE,CAAC,GAAG,CAAC,CAAE,GAAGA,YAAY,CAAE,CAAC,CAAE;IACpD,MAAMI,EAAE,GAAGJ,YAAY,CAAE,CAAC,GAAG,CAAC,CAAE,GAAGA,YAAY,CAAE,CAAC,CAAE;IACpD,MAAMK,EAAE,GAAGL,YAAY,CAAE,CAAC,GAAG,CAAC,CAAE,GAAGA,YAAY,CAAE,CAAC,CAAE;IAEpD,MAAMM,WAAW,GAAG,CAAC,IAAKH,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGF,EAAE,CAAE;IAEvDf,MAAM,CAACC,SAAS,EAAG;IACnB,IAAKU,MAAM,EAAG;MAEbX,MAAM,CAACE,aAAa,EAAG;MAEvBF,MAAM,CAACG,KAAK,CAACC,GAAG,GAAGe,IAAI,CAACf,GAAG,CAAED,KAAK,EAAEH,MAAM,CAACG,KAAK,CAACC,GAAI,CAAC;MACtDJ,MAAM,CAACG,KAAK,CAACG,GAAG,GAAGa,IAAI,CAACb,GAAG,CAAEH,KAAK,EAAEH,MAAM,CAACG,KAAK,CAACG,GAAI,CAAC;MAEtDN,MAAM,CAACO,IAAI,CAACH,GAAG,GAAGe,IAAI,CAACf,GAAG,CAAEU,KAAK,EAAEd,MAAM,CAACO,IAAI,CAACH,GAAI,CAAC;MACpDJ,MAAM,CAACO,IAAI,CAACD,GAAG,GAAGa,IAAI,CAACb,GAAG,CAAEQ,KAAK,EAAEd,MAAM,CAACO,IAAI,CAACD,GAAI,CAAC;MAEpDN,MAAM,CAACS,gBAAgB,IAAIS,WAAW,GAAGnC,uBAAuB,GAAG+B,KAAK;IAEzE,CAAC,MAAM;MAENd,MAAM,CAACQ,MAAM,CAAEK,aAAa,CAAE,EAAG;MAEjCb,MAAM,CAACS,gBAAgB,IAAIS,WAAW,GAAGpC,cAAc;IAExD;EAED,CAAC,EAAEiB,KAAM,CAAC;;EAEV;EACA,IAAKC,MAAM,CAACO,IAAI,CAACH,GAAG,KAAKC,QAAQ,EAAG;IAEnCL,MAAM,CAACO,IAAI,CAACH,GAAG,GAAG,CAAC;IACnBJ,MAAM,CAACO,IAAI,CAACD,GAAG,GAAG,CAAC;EAEpB;EAEA,IAAKN,MAAM,CAACG,KAAK,CAACC,GAAG,KAAKC,QAAQ,EAAG;IAEpCL,MAAM,CAACG,KAAK,CAACC,GAAG,GAAG,CAAC;IACpBJ,MAAM,CAACG,KAAK,CAACG,GAAG,GAAG,CAAC;EAErB;EAEA,OAAON,MAAM;AAEd;AAEA,SAASoB,cAAcA,CAAEtB,GAAG,EAAG;EAE9B,OAAOA,GAAG,CAACuB,MAAM,CAACC,GAAG,CAAE,CAAEC,IAAI,EAAEC,CAAC,KAAM3B,eAAe,CAAEC,GAAG,EAAE0B,CAAE,CAAE,CAAC;AAElE;AAEA,SAASC,qBAAqBA,CAAEC,GAAG,EAAG;EAErC,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC3B,MAAMC,KAAK,GAAG,CAAEH,GAAG,CAAE;EACrB,IAAII,KAAK,GAAG,CAAC;EAEb,OAAQD,KAAK,CAACvC,MAAM,EAAG;IAEtB,MAAMyC,IAAI,GAAGF,KAAK,CAACG,GAAG,CAAC,CAAC;IACxB,IAAKL,SAAS,CAACM,GAAG,CAAEF,IAAK,CAAC,EAAG;MAE5B;IAED;IAEAJ,SAAS,CAACO,GAAG,CAAEH,IAAK,CAAC;IAErB,KAAM,IAAII,GAAG,IAAIJ,IAAI,EAAG;MAEvB,IAAK,CAAEA,IAAI,CAACK,cAAc,CAAED,GAAI,CAAC,EAAG;QAEnC;MAED;MAEAL,KAAK,IAAI1C,gBAAgB,CAAE+C,GAAI,CAAC;MAEhC,MAAME,KAAK,GAAGN,IAAI,CAAEI,GAAG,CAAE;MACzB,IAAKE,KAAK,KAAM,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,UAAU,CAAE,EAAG;QAE5E,IAAK9C,YAAY,CAAE8C,KAAM,CAAC,EAAG;UAE5BP,KAAK,IAAIO,KAAK,CAACC,UAAU;QAE1B,CAAC,MAAM,IAAKD,KAAK,YAAYE,WAAW,EAAG;UAE1CT,KAAK,IAAIO,KAAK,CAACC,UAAU;QAE1B,CAAC,MAAM;UAENT,KAAK,CAACW,IAAI,CAAEH,KAAM,CAAC;QAEpB;MAED,CAAC,MAAM;QAENP,KAAK,IAAI1C,gBAAgB,CAAEiD,KAAM,CAAC;MAEnC;IAGD;EAED;EAEA,OAAOP,KAAK;AAEb;AAEA,SAASW,cAAcA,CAAE3C,GAAG,EAAG;EAE9B,MAAM4C,QAAQ,GAAG5C,GAAG,CAAC4C,QAAQ;EAC7B,MAAMC,UAAU,GAAG,EAAE;EACrB,MAAMC,KAAK,GAAGF,QAAQ,CAACE,KAAK;EAC5B,MAAMC,QAAQ,GAAGH,QAAQ,CAACI,YAAY,CAAE,UAAW,CAAC;EACpD,IAAIC,MAAM,GAAG,IAAI;EAEjBjD,GAAG,CAACY,QAAQ,CAAE,CAAEP,KAAK,EAAEQ,MAAM,EAAEC,YAAY,EAAEoC,MAAM,EAAElC,KAAK,KAAM;IAE/D,MAAMmC,IAAI,GAAG;MACZ9C,KAAK;MACLQ,MAAM;MACNC,YAAY;MACZoC,MAAM;MACNlC;IACD,CAAC;IACD6B,UAAU,CAAExC,KAAK,CAAE,GAAG8C,IAAI;IAE1BjE,UAAU,CAAE,CAAC,EAAE4B,YAAY,EAAE3B,KAAM,CAAC;IACpC,MAAMiE,MAAM,GAAGP,UAAU,CAAExC,KAAK,GAAG,CAAC,CAAE;IAEtC,IAAKQ,MAAM,EAAG;MAEb;MACA,KAAM,IAAIa,CAAC,GAAGwB,MAAM,GAAG,CAAC,EAAEG,CAAC,GAAG,CAAEH,MAAM,GAAGlC,KAAK,IAAK,CAAC,EAAEU,CAAC,GAAG2B,CAAC,EAAE3B,CAAC,IAAI,CAAC,EAAG;QAErE,MAAM4B,EAAE,GAAGR,KAAK,CAACS,IAAI,CAAE7B,CAAE,CAAC;QAC1B,MAAM8B,EAAE,GAAGV,KAAK,CAACS,IAAI,CAAE7B,CAAC,GAAG,CAAE,CAAC;QAC9B,MAAM+B,EAAE,GAAGX,KAAK,CAACS,IAAI,CAAE7B,CAAC,GAAG,CAAE,CAAC;QAE9B,IAAIgC,WAAW;QAEfrE,IAAI,CAACsE,mBAAmB,CAAEZ,QAAQ,EAAEO,EAAG,CAAC;QACxCI,WAAW,GAAGvE,KAAK,CAACyE,aAAa,CAAEvE,IAAK,CAAC;QAEzCA,IAAI,CAACsE,mBAAmB,CAAEZ,QAAQ,EAAES,EAAG,CAAC;QACxCE,WAAW,GAAGA,WAAW,IAAIvE,KAAK,CAACyE,aAAa,CAAEvE,IAAK,CAAC;QAExDA,IAAI,CAACsE,mBAAmB,CAAEZ,QAAQ,EAAEU,EAAG,CAAC;QACxCC,WAAW,GAAGA,WAAW,IAAIvE,KAAK,CAACyE,aAAa,CAAEvE,IAAK,CAAC;QAExDwE,OAAO,CAACC,MAAM,CAAEJ,WAAW,EAAE,8CAA+C,CAAC;QAC7ET,MAAM,GAAGA,MAAM,IAAIS,WAAW;MAE/B;IAED;IAEA,IAAKN,MAAM,EAAG;MAEb;MACAlE,UAAU,CAAE,CAAC,EAAE4B,YAAY,EAAE1B,KAAM,CAAC;MAEpC,MAAMsE,WAAW,GAAGtE,KAAK,CAAC2E,WAAW,CAAE5E,KAAM,CAAC;MAC9C0E,OAAO,CAACC,MAAM,CAAEJ,WAAW,EAAE,6CAA8C,CAAC;MAC5ET,MAAM,GAAGA,MAAM,IAAIS,WAAW;IAE/B;EAED,CAAE,CAAC;EAEH,OAAOT,MAAM;AAEd;;AAEA;AACA,SAASe,gBAAgBA,CAAEhE,GAAG,EAAG;EAEhC,MAAM6C,UAAU,GAAG,EAAE;EAErB7C,GAAG,CAACY,QAAQ,CAAE,CAAEP,KAAK,EAAEQ,MAAM,EAAEC,YAAY,EAAEoC,MAAM,EAAElC,KAAK,KAAM;IAE/D,MAAMmC,IAAI,GAAG;MACZc,MAAM,EAAE/E,UAAU,CAAE,CAAC,EAAE4B,YAAY,EAAE,IAAIhC,IAAI,CAAC,CAAE;IACjD,CAAC;IAED,IAAK+B,MAAM,EAAG;MAEbsC,IAAI,CAACnC,KAAK,GAAGA,KAAK;MAClBmC,IAAI,CAACD,MAAM,GAAGA,MAAM;IAErB,CAAC,MAAM;MAENC,IAAI,CAACe,IAAI,GAAG,IAAI;MAChBf,IAAI,CAACgB,KAAK,GAAG,IAAI;IAElB;IAEAtB,UAAU,CAAExC,KAAK,CAAE,GAAG8C,IAAI;;IAE1B;IACA,MAAMC,MAAM,GAAGP,UAAU,CAAExC,KAAK,GAAG,CAAC,CAAE;IACtC,IAAK+C,MAAM,EAAG;MAEb,IAAKA,MAAM,CAACc,IAAI,KAAK,IAAI,EAAG;QAE3Bd,MAAM,CAACc,IAAI,GAAGf,IAAI;MAEnB,CAAC,MAAM;QAENC,MAAM,CAACe,KAAK,GAAGhB,IAAI;MAEpB;IAED;EAED,CAAE,CAAC;EAEH,OAAON,UAAU,CAAE,CAAC,CAAE;AAEvB;AAEA,SAASlB,qBAAqB,EAAEL,cAAc,EAAEqB,cAAc,EAAEqB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}