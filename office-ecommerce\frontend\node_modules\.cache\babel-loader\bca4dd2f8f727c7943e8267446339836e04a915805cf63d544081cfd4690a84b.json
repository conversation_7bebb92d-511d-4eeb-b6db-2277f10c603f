{"ast": null, "code": "import * as React from 'react';\nimport { Raycaster, Camera } from 'three';\nimport { useThree, applyProps } from '@react-three/fiber';\nfunction useCamera(camera, props) {\n  const pointer = useThree(state => state.pointer);\n  const [raycast] = React.useState(() => {\n    const raycaster = new Raycaster();\n    /**\n     * applyProps is an internal method of r3f and\n     * therefore requires its first arg to be an\n     * \"Instance\" a term used with the Reconciler\n     * so we have an expect error to mask this\n     */\n    // @ts-expect-error\n\n    if (props) applyProps(raycaster, props, {});\n    return function (_, intersects) {\n      raycaster.setFromCamera(pointer, camera instanceof Camera ? camera : camera.current);\n      const rc = this.constructor.prototype.raycast.bind(this);\n      if (rc) rc(raycaster, intersects);\n    };\n  });\n  return raycast;\n}\nexport { useCamera };", "map": {"version": 3, "names": ["React", "Raycaster", "Camera", "useThree", "applyProps", "useCamera", "camera", "props", "pointer", "state", "raycast", "useState", "raycaster", "_", "intersects", "setFromCamera", "current", "rc", "constructor", "prototype", "bind"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useCamera.js"], "sourcesContent": ["import * as React from 'react';\nimport { Raycaster, Camera } from 'three';\nimport { useThree, applyProps } from '@react-three/fiber';\n\nfunction useCamera(camera, props) {\n  const pointer = useThree(state => state.pointer);\n  const [raycast] = React.useState(() => {\n    const raycaster = new Raycaster();\n    /**\n     * applyProps is an internal method of r3f and\n     * therefore requires its first arg to be an\n     * \"Instance\" a term used with the Reconciler\n     * so we have an expect error to mask this\n     */\n    // @ts-expect-error\n\n    if (props) applyProps(raycaster, props, {});\n    return function (_, intersects) {\n      raycaster.setFromCamera(pointer, camera instanceof Camera ? camera : camera.current);\n      const rc = this.constructor.prototype.raycast.bind(this);\n      if (rc) rc(raycaster, intersects);\n    };\n  });\n  return raycast;\n}\n\nexport { useCamera };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,SAASC,QAAQ,EAAEC,UAAU,QAAQ,oBAAoB;AAEzD,SAASC,SAASA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAChC,MAAMC,OAAO,GAAGL,QAAQ,CAACM,KAAK,IAAIA,KAAK,CAACD,OAAO,CAAC;EAChD,MAAM,CAACE,OAAO,CAAC,GAAGV,KAAK,CAACW,QAAQ,CAAC,MAAM;IACrC,MAAMC,SAAS,GAAG,IAAIX,SAAS,CAAC,CAAC;IACjC;AACJ;AACA;AACA;AACA;AACA;IACI;;IAEA,IAAIM,KAAK,EAAEH,UAAU,CAACQ,SAAS,EAAEL,KAAK,EAAE,CAAC,CAAC,CAAC;IAC3C,OAAO,UAAUM,CAAC,EAAEC,UAAU,EAAE;MAC9BF,SAAS,CAACG,aAAa,CAACP,OAAO,EAAEF,MAAM,YAAYJ,MAAM,GAAGI,MAAM,GAAGA,MAAM,CAACU,OAAO,CAAC;MACpF,MAAMC,EAAE,GAAG,IAAI,CAACC,WAAW,CAACC,SAAS,CAACT,OAAO,CAACU,IAAI,CAAC,IAAI,CAAC;MACxD,IAAIH,EAAE,EAAEA,EAAE,CAACL,SAAS,EAAEE,UAAU,CAAC;IACnC,CAAC;EACH,CAAC,CAAC;EACF,OAAOJ,OAAO;AAChB;AAEA,SAASL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}