{"ast": null, "code": "import { CubeTextureLoader } from 'three';\nimport { useLoader } from '@react-three/fiber';\nfunction useCubeTexture(files, {\n  path\n}) {\n  // @ts-ignore\n  const [cubeTexture] = useLoader(\n  // @ts-ignore\n  CubeTextureLoader, [files], loader => loader.setPath(path));\n  return cubeTexture;\n}\nuseCubeTexture.preload = (files, {\n  path\n}) => useLoader.preload(\n// @ts-ignore\nCubeTextureLoader, [files], loader => loader.setPath(path));\nexport { useCubeTexture };", "map": {"version": 3, "names": ["CubeTextureLoader", "useLoader", "useCubeTexture", "files", "path", "cubeTexture", "loader", "set<PERSON>ath", "preload"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useCubeTexture.js"], "sourcesContent": ["import { CubeTextureLoader } from 'three';\nimport { useLoader } from '@react-three/fiber';\n\nfunction useCubeTexture(files, {\n  path\n}) {\n  // @ts-ignore\n  const [cubeTexture] = useLoader( // @ts-ignore\n  CubeTextureLoader, [files], loader => loader.setPath(path));\n  return cubeTexture;\n}\n\nuseCubeTexture.preload = (files, {\n  path\n}) => useLoader.preload( // @ts-ignore\nCubeTextureLoader, [files], loader => loader.setPath(path));\n\nexport { useCubeTexture };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,OAAO;AACzC,SAASC,SAAS,QAAQ,oBAAoB;AAE9C,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7BC;AACF,CAAC,EAAE;EACD;EACA,MAAM,CAACC,WAAW,CAAC,GAAGJ,SAAS;EAAE;EACjCD,iBAAiB,EAAE,CAACG,KAAK,CAAC,EAAEG,MAAM,IAAIA,MAAM,CAACC,OAAO,CAACH,IAAI,CAAC,CAAC;EAC3D,OAAOC,WAAW;AACpB;AAEAH,cAAc,CAACM,OAAO,GAAG,CAACL,KAAK,EAAE;EAC/BC;AACF,CAAC,KAAKH,SAAS,CAACO,OAAO;AAAE;AACzBR,iBAAiB,EAAE,CAACG,KAAK,CAAC,EAAEG,MAAM,IAAIA,MAAM,CAACC,OAAO,CAACH,IAAI,CAAC,CAAC;AAE3D,SAASF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}