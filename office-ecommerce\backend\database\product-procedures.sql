-- =============================================
-- STORED PROCEDURES FOR PRODUCT MANAGEMENT
-- Comprehensive procedures for product CRUD operations
-- with file management and audit trails
-- =============================================

USE OfficeEcommerce;
GO

-- =============================================
-- CREATE OR UPDATE PRODUCT
-- =============================================

IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_CreateOrUpdateProduct')
    DROP PROCEDURE sp_CreateOrUpdateProduct;
GO

CREATE PROCEDURE sp_CreateOrUpdateProduct
    @ProductID UNIQUEIDENTIFIER = NULL,
    @ProductCode NVARCHAR(50),
    @ProductName NVARCHAR(255),
    @CategoryID UNIQUEIDENTIFIER,
    @Description NVARCHAR(MAX) = NULL,
    @BasePrice DECIMAL(10,2),
    @Weight DECIMAL(8,2) = NULL,
    @Dimensions NVARCHAR(100) = NULL,
    @Material NVARCHAR(100) = NULL,
    @Color NVARCHAR(50) = NULL,
    @Status NVARCHAR(50) = 'Draft',
    @Tags NVARCHAR(MAX) = NULL,
    @MetaData NVARCHAR(MAX) = NULL,
    @IsCustomizable BIT = 0,
    @IsActive BIT = 1,
    @CreatedBy UNIQUEIDENTIFIER,
    @ChangeReason NVARCHAR(500) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @IsUpdate BIT = 0;
    DECLARE @NewProductID UNIQUEIDENTIFIER;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Check if this is an update
        IF @ProductID IS NOT NULL AND EXISTS (SELECT 1 FROM Products WHERE ProductID = @ProductID)
        BEGIN
            SET @IsUpdate = 1;
            SET @NewProductID = @ProductID;
            
            -- Update existing product
            UPDATE Products 
            SET 
                ProductCode = @ProductCode,
                ProductName = @ProductName,
                CategoryID = @CategoryID,
                Description = @Description,
                BasePrice = @BasePrice,
                Weight = @Weight,
                Dimensions = @Dimensions,
                Material = @Material,
                Color = @Color,
                Status = @Status,
                Tags = @Tags,
                MetaData = @MetaData,
                IsCustomizable = @IsCustomizable,
                IsActive = @IsActive,
                UpdatedBy = @CreatedBy,
                UpdatedAt = GETUTCDATE()
            WHERE ProductID = @ProductID;
            
            -- Log audit trail for update
            INSERT INTO ProductAuditTrail (ProductID, Action, ChangeReason, ChangedBy)
            VALUES (@ProductID, 'UPDATE', @ChangeReason, @CreatedBy);
        END
        ELSE
        BEGIN
            -- Create new product
            SET @NewProductID = ISNULL(@ProductID, NEWID());
            
            INSERT INTO Products (
                ProductID, ProductCode, ProductName, CategoryID, Description,
                BasePrice, Weight, Dimensions, Material, Color, Status,
                Tags, MetaData, IsCustomizable, IsActive, CreatedBy, UpdatedBy
            )
            VALUES (
                @NewProductID, @ProductCode, @ProductName, @CategoryID, @Description,
                @BasePrice, @Weight, @Dimensions, @Material, @Color, @Status,
                @Tags, @MetaData, @IsCustomizable, @IsActive, @CreatedBy, @CreatedBy
            );
            
            -- Log audit trail for creation
            INSERT INTO ProductAuditTrail (ProductID, Action, ChangeReason, ChangedBy)
            VALUES (@NewProductID, 'CREATE', @ChangeReason, @CreatedBy);
        END
        
        COMMIT TRANSACTION;
        
        -- Return the product details
        SELECT 
            p.*,
            c.CategoryName,
            creator.FirstName + ' ' + creator.LastName as CreatedByName,
            updater.FirstName + ' ' + updater.LastName as UpdatedByName
        FROM Products p
        INNER JOIN Categories c ON p.CategoryID = c.CategoryID
        LEFT JOIN Users creator ON p.CreatedBy = creator.UserID
        LEFT JOIN Users updater ON p.UpdatedBy = updater.UserID
        WHERE p.ProductID = @NewProductID;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO

-- =============================================
-- GET PRODUCTS WITH PAGINATION AND FILTERING
-- =============================================

IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetProducts')
    DROP PROCEDURE sp_GetProducts;
GO

CREATE PROCEDURE sp_GetProducts
    @PageNumber INT = 1,
    @PageSize INT = 20,
    @SearchTerm NVARCHAR(255) = NULL,
    @CategoryID UNIQUEIDENTIFIER = NULL,
    @Status NVARCHAR(50) = NULL,
    @IsActive BIT = NULL,
    @SortBy NVARCHAR(50) = 'ProductName',
    @SortDirection NVARCHAR(10) = 'ASC'
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    DECLARE @SQL NVARCHAR(MAX);
    DECLARE @CountSQL NVARCHAR(MAX);
    DECLARE @WhereClause NVARCHAR(MAX) = '';
    DECLARE @OrderClause NVARCHAR(MAX);
    
    -- Build WHERE clause
    IF @SearchTerm IS NOT NULL
        SET @WhereClause = @WhereClause + ' AND (p.ProductName LIKE ''%' + @SearchTerm + '%'' OR p.ProductCode LIKE ''%' + @SearchTerm + '%'' OR p.Description LIKE ''%' + @SearchTerm + '%'')';
    
    IF @CategoryID IS NOT NULL
        SET @WhereClause = @WhereClause + ' AND p.CategoryID = ''' + CAST(@CategoryID AS NVARCHAR(36)) + '''';
    
    IF @Status IS NOT NULL
        SET @WhereClause = @WhereClause + ' AND p.Status = ''' + @Status + '''';
    
    IF @IsActive IS NOT NULL
        SET @WhereClause = @WhereClause + ' AND p.IsActive = ' + CAST(@IsActive AS NVARCHAR(1));
    
    -- Remove leading AND
    IF LEN(@WhereClause) > 0
        SET @WhereClause = 'WHERE 1=1 ' + @WhereClause;
    ELSE
        SET @WhereClause = 'WHERE 1=1';
    
    -- Build ORDER BY clause
    SET @OrderClause = 'ORDER BY ' + @SortBy + ' ' + @SortDirection;
    
    -- Get total count
    SET @CountSQL = '
        SELECT COUNT(*) as TotalCount
        FROM Products p
        INNER JOIN Categories c ON p.CategoryID = c.CategoryID
        ' + @WhereClause;
    
    -- Get paginated results
    SET @SQL = '
        SELECT 
            p.*,
            c.CategoryName,
            c.Description as CategoryDescription,
            creator.FirstName + '' '' + creator.LastName as CreatedByName,
            updater.FirstName + '' '' + updater.LastName as UpdatedByName,
            (SELECT COUNT(*) FROM Product3DModels WHERE ProductID = p.ProductID AND IsActive = 1) as ModelCount,
            (SELECT COUNT(*) FROM ProductImages WHERE ProductID = p.ProductID AND IsActive = 1) as ImageCount,
            (SELECT COUNT(*) FROM ProductComponents WHERE ProductID = p.ProductID) as ComponentCount
        FROM Products p
        INNER JOIN Categories c ON p.CategoryID = c.CategoryID
        LEFT JOIN Users creator ON p.CreatedBy = creator.UserID
        LEFT JOIN Users updater ON p.UpdatedBy = updater.UserID
        ' + @WhereClause + '
        ' + @OrderClause + '
        OFFSET ' + CAST(@Offset AS NVARCHAR(10)) + ' ROWS
        FETCH NEXT ' + CAST(@PageSize AS NVARCHAR(10)) + ' ROWS ONLY';
    
    -- Execute count query
    EXEC sp_executesql @CountSQL;
    
    -- Execute main query
    EXEC sp_executesql @SQL;
END
GO

-- =============================================
-- GET PRODUCT BY ID WITH FULL DETAILS
-- =============================================

IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetProductById')
    DROP PROCEDURE sp_GetProductById;
GO

CREATE PROCEDURE sp_GetProductById
    @ProductID UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Get product details
    SELECT 
        p.*,
        c.CategoryName,
        c.Description as CategoryDescription,
        creator.FirstName + ' ' + creator.LastName as CreatedByName,
        updater.FirstName + ' ' + updater.LastName as UpdatedByName
    FROM Products p
    INNER JOIN Categories c ON p.CategoryID = c.CategoryID
    LEFT JOIN Users creator ON p.CreatedBy = creator.UserID
    LEFT JOIN Users updater ON p.UpdatedBy = updater.UserID
    WHERE p.ProductID = @ProductID;
    
    -- Get 3D models
    SELECT 
        m.*,
        u.FirstName + ' ' + u.LastName as UploadedByName
    FROM Product3DModels m
    LEFT JOIN Users u ON m.UploadedBy = u.UserID
    WHERE m.ProductID = @ProductID AND m.IsActive = 1
    ORDER BY m.IsPrimary DESC, m.CreatedAt DESC;
    
    -- Get images
    SELECT 
        i.*,
        u.FirstName + ' ' + u.LastName as UploadedByName
    FROM ProductImages i
    LEFT JOIN Users u ON i.UploadedBy = u.UserID
    WHERE i.ProductID = @ProductID AND i.IsActive = 1
    ORDER BY i.IsPrimary DESC, i.DisplayOrder, i.CreatedAt DESC;
    
    -- Get components/parts
    SELECT 
        pc.*,
        p.PartName,
        p.PartType,
        p.UnitCost
    FROM ProductComponents pc
    INNER JOIN Parts p ON pc.PartID = p.PartID
    WHERE pc.ProductID = @ProductID
    ORDER BY p.PartName;
    
    -- Get color options
    SELECT *
    FROM ProductColors
    WHERE ProductID = @ProductID AND IsActive = 1
    ORDER BY IsDefault DESC, SortOrder, ColorName;
END
GO

-- =============================================
-- DELETE PRODUCT (SOFT DELETE)
-- =============================================

IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_DeleteProduct')
    DROP PROCEDURE sp_DeleteProduct;
GO

CREATE PROCEDURE sp_DeleteProduct
    @ProductID UNIQUEIDENTIFIER,
    @DeletedBy UNIQUEIDENTIFIER,
    @DeleteReason NVARCHAR(500) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Check if product exists
        IF NOT EXISTS (SELECT 1 FROM Products WHERE ProductID = @ProductID)
        BEGIN
            RAISERROR('Product not found', 16, 1);
            RETURN;
        END
        
        -- Soft delete the product
        UPDATE Products 
        SET 
            IsActive = 0,
            UpdatedBy = @DeletedBy,
            UpdatedAt = GETUTCDATE()
        WHERE ProductID = @ProductID;
        
        -- Soft delete related 3D models
        UPDATE Product3DModels 
        SET IsActive = 0, UpdatedAt = GETUTCDATE()
        WHERE ProductID = @ProductID;
        
        -- Soft delete related images
        UPDATE ProductImages 
        SET IsActive = 0, UpdatedAt = GETUTCDATE()
        WHERE ProductID = @ProductID;
        
        -- Soft delete color options
        UPDATE ProductColors 
        SET IsActive = 0
        WHERE ProductID = @ProductID;
        
        -- Log audit trail
        INSERT INTO ProductAuditTrail (ProductID, Action, ChangeReason, ChangedBy)
        VALUES (@ProductID, 'DELETE', @DeleteReason, @DeletedBy);
        
        COMMIT TRANSACTION;
        
        SELECT 'Product deleted successfully' as Message;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO

PRINT 'Product management stored procedures created successfully.';
GO
