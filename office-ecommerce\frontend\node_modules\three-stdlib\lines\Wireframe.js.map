{"version": 3, "file": "Wireframe.js", "sources": ["../../src/lines/Wireframe.js"], "sourcesContent": ["import { InstancedInterleavedBuffer, InterleavedBuffer<PERSON>ttribute, Mesh, Vector3, Vector4 } from 'three'\nimport { LineSegmentsGeometry } from '../lines/LineSegmentsGeometry'\nimport { LineMaterial } from '../lines/LineMaterial'\n\nconst _start = /* @__PURE__ */ new Vector3()\nconst _end = /* @__PURE__ */ new Vector3()\nconst _viewport = /* @__PURE__ */ new Vector4()\n\nclass Wireframe extends Mesh {\n  constructor(geometry = new LineSegmentsGeometry(), material = new LineMaterial({ color: Math.random() * 0xffffff })) {\n    super(geometry, material)\n\n    this.isWireframe = true\n\n    this.type = 'Wireframe'\n  }\n\n  // for backwards-compatibility, but could be a method of LineSegmentsGeometry...\n\n  computeLineDistances() {\n    const geometry = this.geometry\n\n    const instanceStart = geometry.attributes.instanceStart\n    const instanceEnd = geometry.attributes.instanceEnd\n    const lineDistances = new Float32Array(2 * instanceStart.count)\n\n    for (let i = 0, j = 0, l = instanceStart.count; i < l; i++, j += 2) {\n      _start.fromBufferAttribute(instanceStart, i)\n      _end.fromBufferAttribute(instanceEnd, i)\n\n      lineDistances[j] = j === 0 ? 0 : lineDistances[j - 1]\n      lineDistances[j + 1] = lineDistances[j] + _start.distanceTo(_end)\n    }\n\n    const instanceDistanceBuffer = new InstancedInterleavedBuffer(lineDistances, 2, 1) // d0, d1\n\n    geometry.setAttribute('instanceDistanceStart', new InterleavedBufferAttribute(instanceDistanceBuffer, 1, 0)) // d0\n    geometry.setAttribute('instanceDistanceEnd', new InterleavedBufferAttribute(instanceDistanceBuffer, 1, 1)) // d1\n\n    return this\n  }\n\n  onBeforeRender(renderer) {\n    const uniforms = this.material.uniforms\n\n    if (uniforms && uniforms.resolution) {\n      renderer.getViewport(_viewport)\n      this.material.uniforms.resolution.value.set(_viewport.z, _viewport.w)\n    }\n  }\n}\n\nexport { Wireframe }\n"], "names": [], "mappings": ";;;AAIA,MAAM,SAAyB,oBAAI,QAAS;AAC5C,MAAM,OAAuB,oBAAI,QAAS;AAC1C,MAAM,YAA4B,oBAAI,QAAS;AAE/C,MAAM,kBAAkB,KAAK;AAAA,EAC3B,YAAY,WAAW,IAAI,qBAAsB,GAAE,WAAW,IAAI,aAAa,EAAE,OAAO,KAAK,WAAW,SAAU,CAAA,GAAG;AACnH,UAAM,UAAU,QAAQ;AAExB,SAAK,cAAc;AAEnB,SAAK,OAAO;AAAA,EACb;AAAA;AAAA,EAID,uBAAuB;AACrB,UAAM,WAAW,KAAK;AAEtB,UAAM,gBAAgB,SAAS,WAAW;AAC1C,UAAM,cAAc,SAAS,WAAW;AACxC,UAAM,gBAAgB,IAAI,aAAa,IAAI,cAAc,KAAK;AAE9D,aAAS,IAAI,GAAG,IAAI,GAAG,IAAI,cAAc,OAAO,IAAI,GAAG,KAAK,KAAK,GAAG;AAClE,aAAO,oBAAoB,eAAe,CAAC;AAC3C,WAAK,oBAAoB,aAAa,CAAC;AAEvC,oBAAc,CAAC,IAAI,MAAM,IAAI,IAAI,cAAc,IAAI,CAAC;AACpD,oBAAc,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI,OAAO,WAAW,IAAI;AAAA,IACjE;AAED,UAAM,yBAAyB,IAAI,2BAA2B,eAAe,GAAG,CAAC;AAEjF,aAAS,aAAa,yBAAyB,IAAI,2BAA2B,wBAAwB,GAAG,CAAC,CAAC;AAC3G,aAAS,aAAa,uBAAuB,IAAI,2BAA2B,wBAAwB,GAAG,CAAC,CAAC;AAEzG,WAAO;AAAA,EACR;AAAA,EAED,eAAe,UAAU;AACvB,UAAM,WAAW,KAAK,SAAS;AAE/B,QAAI,YAAY,SAAS,YAAY;AACnC,eAAS,YAAY,SAAS;AAC9B,WAAK,SAAS,SAAS,WAAW,MAAM,IAAI,UAAU,GAAG,UAAU,CAAC;AAAA,IACrE;AAAA,EACF;AACH;"}