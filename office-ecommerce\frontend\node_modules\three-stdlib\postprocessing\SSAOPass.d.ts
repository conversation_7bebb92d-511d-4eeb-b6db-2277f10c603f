import {
  Camera,
  Color,
  DataTexture,
  Material,
  MeshNormalMaterial,
  Scene,
  ShaderMaterial,
  Vector3,
  WebG<PERSON>enderer,
  WebGLRenderTarget,
} from 'three'

import { Pass, FullScreenQuad } from './Pass'

export enum SSAOPassOUTPUT {
  Default,
  SSAO,
  Blur,
  Beauty,
  Depth,
  Normal,
}

export class SSAOPass extends Pass {
  constructor(scene: Scene, camera: Camera, width?: number, height?: number)
  scene: Scene
  camera: Camera
  width: number
  height: boolean
  clear: boolean
  kernelRadius: number
  kernelSize: number
  kernel: Vector3[]
  noiseTexture: DataTexture
  output: SSAOPassOUTPUT
  minDistance: number
  maxDistance: number
  beautyRenderTarget: WebGLRenderTarget
  normalRenderTarget: WebGLRenderTarget
  ssaoRenderTarget: WebGLRenderTarget
  blurRenderTarget: WebGLRenderTarget
  ssaoMaterial: ShaderMaterial
  normalMaterial: MeshNormalMaterial
  blurMaterial: ShaderMaterial
  depthRenderMaterial: ShaderMaterial
  copyMaterial: ShaderMaterial
  fsQuad: FullScreenQuad
  originalClearColor: Color

  static OUTPUT: SSAOPassOUTPUT

  dipose(): void
  generateSampleKernel(): Vector3[]
  generateRandomKernelRotations(): void
  renderPass(
    renderer: WebGLRenderer,
    passMaterial: Material,
    renderTarget: WebGLRenderTarget,
    clearColor?: Color | string | number,
    clearAlpha?: number,
  ): void
  renderOverride(
    renderer: WebGLRenderer,
    overrideMaterial: Material,
    renderTarget: WebGLRenderTarget,
    clearColor?: Color | string | number,
    clearAlpha?: number,
  ): void
}
