{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nconst Resize = /*#__PURE__*/React.forwardRef(({\n  children,\n  width,\n  height,\n  depth,\n  box3,\n  precise = true,\n  ...props\n}, fRef) => {\n  const ref = React.useRef(null);\n  const outer = React.useRef(null);\n  const inner = React.useRef(null);\n  React.useLayoutEffect(() => {\n    outer.current.matrixWorld.identity();\n    let box = box3 || new THREE.Box3().setFromObject(inner.current, precise);\n    const w = box.max.x - box.min.x;\n    const h = box.max.y - box.min.y;\n    const d = box.max.z - box.min.z;\n    let dimension = Math.max(w, h, d);\n    if (width) dimension = w;\n    if (height) dimension = h;\n    if (depth) dimension = d;\n    outer.current.scale.setScalar(1 / dimension);\n  }, [width, height, depth, box3, precise]);\n  React.useImperativeHandle(fRef, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props), /*#__PURE__*/React.createElement(\"group\", {\n    ref: outer\n  }, /*#__PURE__*/React.createElement(\"group\", {\n    ref: inner\n  }, children)));\n});\nexport { Resize };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "Resize", "forwardRef", "children", "width", "height", "depth", "box3", "precise", "props", "fRef", "ref", "useRef", "outer", "inner", "useLayoutEffect", "current", "matrixWorld", "identity", "box", "Box3", "setFromObject", "w", "max", "x", "min", "h", "y", "d", "z", "dimension", "Math", "scale", "setScalar", "useImperativeHandle", "createElement"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Resize.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\n\nconst Resize = /*#__PURE__*/React.forwardRef(({\n  children,\n  width,\n  height,\n  depth,\n  box3,\n  precise = true,\n  ...props\n}, fRef) => {\n  const ref = React.useRef(null);\n  const outer = React.useRef(null);\n  const inner = React.useRef(null);\n  React.useLayoutEffect(() => {\n    outer.current.matrixWorld.identity();\n    let box = box3 || new THREE.Box3().setFromObject(inner.current, precise);\n    const w = box.max.x - box.min.x;\n    const h = box.max.y - box.min.y;\n    const d = box.max.z - box.min.z;\n    let dimension = Math.max(w, h, d);\n    if (width) dimension = w;\n    if (height) dimension = h;\n    if (depth) dimension = d;\n    outer.current.scale.setScalar(1 / dimension);\n  }, [width, height, depth, box3, precise]);\n  React.useImperativeHandle(fRef, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props), /*#__PURE__*/React.createElement(\"group\", {\n    ref: outer\n  }, /*#__PURE__*/React.createElement(\"group\", {\n    ref: inner\n  }, children)));\n});\n\nexport { Resize };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,MAAMC,MAAM,GAAG,aAAaD,KAAK,CAACE,UAAU,CAAC,CAAC;EAC5CC,QAAQ;EACRC,KAAK;EACLC,MAAM;EACNC,KAAK;EACLC,IAAI;EACJC,OAAO,GAAG,IAAI;EACd,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAMC,GAAG,GAAGX,KAAK,CAACY,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,KAAK,GAAGb,KAAK,CAACY,MAAM,CAAC,IAAI,CAAC;EAChC,MAAME,KAAK,GAAGd,KAAK,CAACY,MAAM,CAAC,IAAI,CAAC;EAChCZ,KAAK,CAACe,eAAe,CAAC,MAAM;IAC1BF,KAAK,CAACG,OAAO,CAACC,WAAW,CAACC,QAAQ,CAAC,CAAC;IACpC,IAAIC,GAAG,GAAGZ,IAAI,IAAI,IAAIR,KAAK,CAACqB,IAAI,CAAC,CAAC,CAACC,aAAa,CAACP,KAAK,CAACE,OAAO,EAAER,OAAO,CAAC;IACxE,MAAMc,CAAC,GAAGH,GAAG,CAACI,GAAG,CAACC,CAAC,GAAGL,GAAG,CAACM,GAAG,CAACD,CAAC;IAC/B,MAAME,CAAC,GAAGP,GAAG,CAACI,GAAG,CAACI,CAAC,GAAGR,GAAG,CAACM,GAAG,CAACE,CAAC;IAC/B,MAAMC,CAAC,GAAGT,GAAG,CAACI,GAAG,CAACM,CAAC,GAAGV,GAAG,CAACM,GAAG,CAACI,CAAC;IAC/B,IAAIC,SAAS,GAAGC,IAAI,CAACR,GAAG,CAACD,CAAC,EAAEI,CAAC,EAAEE,CAAC,CAAC;IACjC,IAAIxB,KAAK,EAAE0B,SAAS,GAAGR,CAAC;IACxB,IAAIjB,MAAM,EAAEyB,SAAS,GAAGJ,CAAC;IACzB,IAAIpB,KAAK,EAAEwB,SAAS,GAAGF,CAAC;IACxBf,KAAK,CAACG,OAAO,CAACgB,KAAK,CAACC,SAAS,CAAC,CAAC,GAAGH,SAAS,CAAC;EAC9C,CAAC,EAAE,CAAC1B,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,OAAO,CAAC,CAAC;EACzCR,KAAK,CAACkC,mBAAmB,CAACxB,IAAI,EAAE,MAAMC,GAAG,CAACK,OAAO,EAAE,EAAE,CAAC;EACtD,OAAO,aAAahB,KAAK,CAACmC,aAAa,CAAC,OAAO,EAAErC,QAAQ,CAAC;IACxDa,GAAG,EAAEA;EACP,CAAC,EAAEF,KAAK,CAAC,EAAE,aAAaT,KAAK,CAACmC,aAAa,CAAC,OAAO,EAAE;IACnDxB,GAAG,EAAEE;EACP,CAAC,EAAE,aAAab,KAAK,CAACmC,aAAa,CAAC,OAAO,EAAE;IAC3CxB,GAAG,EAAEG;EACP,CAAC,EAAEX,QAAQ,CAAC,CAAC,CAAC;AAChB,CAAC,CAAC;AAEF,SAASF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}