{"name": "@react-three/drei", "version": "9.70.0", "private": false, "description": "useful add-ons for react-three-fiber", "keywords": ["react", "three", "threejs", "react-three-fiber"], "repository": {"type": "git", "url": "git+https://github.com/pmndrs/drei.git"}, "license": "MIT", "bugs": {"url": "https://github.com/pmndrs/drei/issues"}, "homepage": "https://github.com/pmndrs/drei", "maintainers": ["<PERSON> (https://github.com/drcmda)", "<PERSON><PERSON><PERSON><PERSON> (https://github.com/gsimone)", "<PERSON> (https://github.com/emmelleppi)", "<PERSON> (https://github.com/joshua<PERSON>s)"], "main": "index.cjs.js", "module": "index.js", "types": "index.d.ts", "sideEffects": false, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"body-max-line-length": [0]}}, "dependencies": {"@babel/runtime": "^7.11.2", "@react-spring/three": "~9.6.1", "@use-gesture/react": "^10.2.24", "camera-controls": "^2.3.1", "detect-gpu": "^5.0.14", "glsl-noise": "^0.0.0", "lodash.clamp": "^4.0.3", "lodash.omit": "^4.5.0", "lodash.pick": "^4.4.0", "maath": "^0.5.2", "meshline": "^3.1.6", "react-composer": "^5.0.3", "react-merge-refs": "^1.1.0", "stats.js": "^0.17.0", "suspend-react": "^0.0.8", "three-mesh-bvh": "^0.5.23", "three-stdlib": "^2.22.10", "troika-three-text": "^0.47.1", "utility-types": "^3.10.0", "zustand": "^3.5.13"}, "peerDependencies": {"@react-three/fiber": ">=8.0", "react": ">=18.0", "react-dom": ">=18.0", "three": ">=0.137"}, "peerDependenciesMeta": {"react-dom": {"optional": true}}}