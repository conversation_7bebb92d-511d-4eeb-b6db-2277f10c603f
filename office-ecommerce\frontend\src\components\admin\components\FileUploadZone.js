import React, { useState, useRef } from 'react';
import './FileUploadZone.css';

const FileUploadZone = ({ 
  accept, 
  multiple = false, 
  onFilesSelected, 
  fileType = 'File',
  maxSize = 50 * 1024 * 1024, // 50MB default
  className = '' 
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(null);
  const fileInputRef = useRef(null);

  const validateFile = (file) => {
    const errors = [];

    // Check file size
    if (file.size > maxSize) {
      errors.push(`File "${file.name}" is too large. Maximum size: ${formatFileSize(maxSize)}`);
    }

    // Check file type if accept is specified
    if (accept) {
      const acceptedTypes = accept.split(',').map(type => type.trim().toLowerCase());
      const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
      const fileMimeType = file.type.toLowerCase();

      const isValidType = acceptedTypes.some(type => {
        if (type.startsWith('.')) {
          return fileExtension === type;
        } else {
          return fileMimeType.startsWith(type.replace('*', ''));
        }
      });

      if (!isValidType) {
        errors.push(`File "${file.name}" has invalid type. Accepted: ${accept}`);
      }
    }

    return errors;
  };

  const handleFiles = (files) => {
    const fileArray = Array.from(files);
    const validFiles = [];
    const errors = [];

    fileArray.forEach(file => {
      const fileErrors = validateFile(file);
      if (fileErrors.length === 0) {
        validFiles.push(file);
      } else {
        errors.push(...fileErrors);
      }
    });

    if (errors.length > 0) {
      alert(errors.join('\n'));
    }

    if (validFiles.length > 0) {
      onFilesSelected(validFiles);
    }
  };

  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFiles(files);
    }
  };

  const handleFileInputChange = (e) => {
    const files = e.target.files;
    if (files.length > 0) {
      handleFiles(files);
    }
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileTypeIcon = () => {
    if (accept?.includes('.glb') || accept?.includes('.gltf')) {
      return '🎯';
    } else if (accept?.includes('image/')) {
      return '🖼️';
    } else if (accept?.includes('.pdf')) {
      return '📄';
    } else {
      return '📁';
    }
  };

  const getAcceptedFormats = () => {
    if (!accept) return 'All files';
    
    const formats = accept.split(',').map(type => type.trim().toUpperCase());
    return formats.join(', ');
  };

  return (
    <div className={`file-upload-zone ${className}`}>
      <div
        className={`upload-area ${isDragOver ? 'drag-over' : ''}`}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          multiple={multiple}
          onChange={handleFileInputChange}
          style={{ display: 'none' }}
        />

        <div className="upload-content">
          <div className="upload-icon">
            {getFileTypeIcon()}
          </div>
          
          <div className="upload-text">
            <h3>
              {isDragOver 
                ? `Drop ${fileType.toLowerCase()}${multiple ? 's' : ''} here` 
                : `Upload ${fileType}${multiple ? 's' : ''}`
              }
            </h3>
            <p>
              Drag and drop {multiple ? 'files' : 'a file'} here, or{' '}
              <span className="upload-link">click to browse</span>
            </p>
          </div>

          <div className="upload-info">
            <div className="info-item">
              <strong>Accepted formats:</strong> {getAcceptedFormats()}
            </div>
            <div className="info-item">
              <strong>Maximum size:</strong> {formatFileSize(maxSize)}
            </div>
            {multiple && (
              <div className="info-item">
                <strong>Multiple files:</strong> Supported
              </div>
            )}
          </div>
        </div>

        {uploadProgress !== null && (
          <div className="upload-progress">
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
            <span className="progress-text">{uploadProgress}%</span>
          </div>
        )}
      </div>

      <div className="upload-tips">
        <h4>Tips for better uploads:</h4>
        <ul>
          {accept?.includes('.glb') && (
            <>
              <li>Use GLB format for better compatibility and smaller file sizes</li>
              <li>Optimize your 3D models before uploading (reduce polygon count if needed)</li>
              <li>Include textures embedded in GLB files</li>
            </>
          )}
          {accept?.includes('image/') && (
            <>
              <li>Use high-quality images for better product presentation</li>
              <li>Recommended resolution: 1200x1200 pixels or higher</li>
              <li>Use JPEG for photos, PNG for graphics with transparency</li>
            </>
          )}
          <li>Ensure file names are descriptive and professional</li>
          <li>Check file integrity before uploading</li>
        </ul>
      </div>
    </div>
  );
};

export default FileUploadZone;
