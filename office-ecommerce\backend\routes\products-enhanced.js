const express = require('express');
const router = express.Router();
const { body, validationResult, query, param } = require('express-validator');
const Product = require('../models/Product');
const auth = require('../middleware/auth');
const rbac = require('../middleware/rbac');
const fileUpload = require('../middleware/fileUpload');
const fileUploadService = require('../services/fileUploadService');
const websocketService = require('../services/websocketService');
const logger = require('../utils/logger');

// =============================================
// PRODUCT CRUD OPERATIONS
// =============================================

// Get products with pagination and filtering
router.get('/', 
  auth,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('search').optional().isLength({ min: 1, max: 255 }).withMessage('Search term must be 1-255 characters'),
    query('category').optional().isUUID().withMessage('Category must be a valid UUID'),
    query('status').optional().isIn(['Draft', 'Active', 'Inactive', 'Discontinued', 'Pending Review']).withMessage('Invalid status'),
    query('sortBy').optional().isIn(['ProductName', 'BasePrice', 'CreatedAt', 'UpdatedAt', 'Status']).withMessage('Invalid sort field'),
    query('sortDirection').optional().isIn(['ASC', 'DESC']).withMessage('Sort direction must be ASC or DESC')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const options = {
        pageNumber: parseInt(req.query.page) || 1,
        pageSize: parseInt(req.query.limit) || 20,
        searchTerm: req.query.search || null,
        categoryId: req.query.category || null,
        status: req.query.status || null,
        isActive: req.query.active !== undefined ? req.query.active === 'true' : null,
        sortBy: req.query.sortBy || 'ProductName',
        sortDirection: req.query.sortDirection || 'ASC'
      };

      const product = new Product();
      const result = await product.getProductsPaginated(options);

      res.json({
        success: true,
        data: result.products,
        pagination: {
          currentPage: options.pageNumber,
          pageSize: options.pageSize,
          totalCount: result.totalCount,
          totalPages: Math.ceil(result.totalCount / options.pageSize)
        }
      });
    } catch (error) {
      logger.error('Error getting products:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve products',
        error: error.message
      });
    }
  }
);

// Get product by ID with full details
router.get('/:id',
  auth,
  [
    param('id').isUUID().withMessage('Product ID must be a valid UUID')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const product = new Product();
      const result = await product.getProductByIdWithDetails(req.params.id);

      if (!result) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      logger.error('Error getting product by ID:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve product',
        error: error.message
      });
    }
  }
);

// Create new product
router.post('/',
  auth,
  rbac(['Admin', 'Employee']),
  [
    body('ProductCode').notEmpty().isLength({ min: 1, max: 50 }).withMessage('Product code is required and must be 1-50 characters'),
    body('ProductName').notEmpty().isLength({ min: 1, max: 255 }).withMessage('Product name is required and must be 1-255 characters'),
    body('CategoryID').isUUID().withMessage('Category ID must be a valid UUID'),
    body('BasePrice').isFloat({ min: 0 }).withMessage('Base price must be a positive number'),
    body('Description').optional().isLength({ max: 5000 }).withMessage('Description must be less than 5000 characters'),
    body('Weight').optional().isFloat({ min: 0 }).withMessage('Weight must be a positive number'),
    body('Dimensions').optional().isLength({ max: 100 }).withMessage('Dimensions must be less than 100 characters'),
    body('Material').optional().isLength({ max: 100 }).withMessage('Material must be less than 100 characters'),
    body('Color').optional().isLength({ max: 50 }).withMessage('Color must be less than 50 characters'),
    body('Status').optional().isIn(['Draft', 'Active', 'Inactive', 'Discontinued', 'Pending Review']).withMessage('Invalid status'),
    body('IsCustomizable').optional().isBoolean().withMessage('IsCustomizable must be a boolean'),
    body('Tags').optional().isJSON().withMessage('Tags must be valid JSON'),
    body('MetaData').optional().isJSON().withMessage('MetaData must be valid JSON')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const productData = {
        ProductCode: req.body.ProductCode,
        ProductName: req.body.ProductName,
        CategoryID: req.body.CategoryID,
        Description: req.body.Description,
        BasePrice: req.body.BasePrice,
        Weight: req.body.Weight,
        Dimensions: req.body.Dimensions,
        Material: req.body.Material,
        Color: req.body.Color,
        Status: req.body.Status || 'Draft',
        Tags: req.body.Tags,
        MetaData: req.body.MetaData,
        IsCustomizable: req.body.IsCustomizable || false,
        IsActive: req.body.IsActive !== undefined ? req.body.IsActive : true
      };

      const product = new Product();
      const newProduct = await product.createOrUpdateProduct(productData, req.user.userId, 'Product created');

      // Emit real-time update
      websocketService.emitInventoryUpdate({
        type: 'product_created',
        product: newProduct,
        timestamp: new Date().toISOString()
      });

      res.status(201).json({
        success: true,
        message: 'Product created successfully',
        data: newProduct
      });
    } catch (error) {
      logger.error('Error creating product:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create product',
        error: error.message
      });
    }
  }
);

// Update product
router.put('/:id',
  auth,
  rbac(['Admin', 'Employee']),
  [
    param('id').isUUID().withMessage('Product ID must be a valid UUID'),
    body('ProductCode').optional().isLength({ min: 1, max: 50 }).withMessage('Product code must be 1-50 characters'),
    body('ProductName').optional().isLength({ min: 1, max: 255 }).withMessage('Product name must be 1-255 characters'),
    body('CategoryID').optional().isUUID().withMessage('Category ID must be a valid UUID'),
    body('BasePrice').optional().isFloat({ min: 0 }).withMessage('Base price must be a positive number'),
    body('Description').optional().isLength({ max: 5000 }).withMessage('Description must be less than 5000 characters'),
    body('Weight').optional().isFloat({ min: 0 }).withMessage('Weight must be a positive number'),
    body('Dimensions').optional().isLength({ max: 100 }).withMessage('Dimensions must be less than 100 characters'),
    body('Material').optional().isLength({ max: 100 }).withMessage('Material must be less than 100 characters'),
    body('Color').optional().isLength({ max: 50 }).withMessage('Color must be less than 50 characters'),
    body('Status').optional().isIn(['Draft', 'Active', 'Inactive', 'Discontinued', 'Pending Review']).withMessage('Invalid status'),
    body('IsCustomizable').optional().isBoolean().withMessage('IsCustomizable must be a boolean'),
    body('IsActive').optional().isBoolean().withMessage('IsActive must be a boolean'),
    body('Tags').optional().isJSON().withMessage('Tags must be valid JSON'),
    body('MetaData').optional().isJSON().withMessage('MetaData must be valid JSON'),
    body('ChangeReason').optional().isLength({ max: 500 }).withMessage('Change reason must be less than 500 characters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const productData = {
        ProductID: req.params.id,
        ...req.body
      };

      const product = new Product();
      const updatedProduct = await product.createOrUpdateProduct(
        productData, 
        req.user.userId, 
        req.body.ChangeReason || 'Product updated'
      );

      // Emit real-time update
      websocketService.emitInventoryUpdate({
        type: 'product_updated',
        product: updatedProduct,
        timestamp: new Date().toISOString()
      });

      res.json({
        success: true,
        message: 'Product updated successfully',
        data: updatedProduct
      });
    } catch (error) {
      logger.error('Error updating product:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update product',
        error: error.message
      });
    }
  }
);

// Delete product (soft delete)
router.delete('/:id',
  auth,
  rbac(['Admin']),
  [
    param('id').isUUID().withMessage('Product ID must be a valid UUID'),
    body('DeleteReason').optional().isLength({ max: 500 }).withMessage('Delete reason must be less than 500 characters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const product = new Product();
      const result = await product.deleteProduct(
        req.params.id, 
        req.user.userId, 
        req.body.DeleteReason || 'Product deleted'
      );

      // Emit real-time update
      websocketService.emitInventoryUpdate({
        type: 'product_deleted',
        productId: req.params.id,
        timestamp: new Date().toISOString()
      });

      res.json({
        success: true,
        message: result.Message
      });
    } catch (error) {
      logger.error('Error deleting product:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete product',
        error: error.message
      });
    }
  }
);

// =============================================
// FILE UPLOAD OPERATIONS
// =============================================

// Upload 3D model for product
router.post('/:id/models',
  auth,
  rbac(['Admin', 'Employee']),
  fileUpload.single('model'),
  [
    param('id').isUUID().withMessage('Product ID must be a valid UUID'),
    body('IsPrimary').optional().isBoolean().withMessage('IsPrimary must be a boolean'),
    body('ModelMetadata').optional().isJSON().withMessage('ModelMetadata must be valid JSON')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'No 3D model file provided'
        });
      }

      // Validate file type
      const allowedTypes = ['model/gltf-binary', 'model/gltf+json'];
      if (!allowedTypes.includes(req.file.mimetype)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid file type. Only GLB and GLTF files are allowed.'
        });
      }

      const modelData = {
        ProductID: req.params.id,
        FileName: req.file.filename,
        OriginalFileName: req.file.originalname,
        FilePath: req.file.path,
        FileSize: req.file.size,
        FileType: req.file.originalname.split('.').pop().toUpperCase(),
        MimeType: req.file.mimetype,
        IsActive: true,
        IsPrimary: req.body.IsPrimary || false,
        ModelMetadata: req.body.ModelMetadata || null,
        UploadedBy: req.user.userId
      };

      const product = new Product();
      const newModel = await product.add3DModel(modelData);

      // Emit real-time update
      websocketService.emitInventoryUpdate({
        type: 'product_model_added',
        productId: req.params.id,
        model: newModel[0],
        timestamp: new Date().toISOString()
      });

      res.status(201).json({
        success: true,
        message: '3D model uploaded successfully',
        data: newModel[0]
      });
    } catch (error) {
      logger.error('Error uploading 3D model:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to upload 3D model',
        error: error.message
      });
    }
  }
);

// Upload images for product
router.post('/:id/images',
  auth,
  rbac(['Admin', 'Employee']),
  fileUpload.array('images', 10), // Allow up to 10 images
  [
    param('id').isUUID().withMessage('Product ID must be a valid UUID'),
    body('IsPrimary').optional().isBoolean().withMessage('IsPrimary must be a boolean'),
    body('DisplayOrder').optional().isInt({ min: 0 }).withMessage('DisplayOrder must be a non-negative integer'),
    body('AltText').optional().isLength({ max: 255 }).withMessage('AltText must be less than 255 characters'),
    body('Caption').optional().isLength({ max: 500 }).withMessage('Caption must be less than 500 characters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'No image files provided'
        });
      }

      // Validate file types
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
      for (const file of req.files) {
        if (!allowedTypes.includes(file.mimetype)) {
          return res.status(400).json({
            success: false,
            message: `Invalid file type for ${file.originalname}. Only JPEG, PNG, and WebP files are allowed.`
          });
        }
      }

      const product = new Product();
      const uploadedImages = [];

      for (let i = 0; i < req.files.length; i++) {
        const file = req.files[i];

        // Get image dimensions (you might want to use a library like sharp for this)
        const imageData = {
          ProductID: req.params.id,
          FileName: file.filename,
          OriginalFileName: file.originalname,
          FilePath: file.path,
          ThumbnailPath: null, // Generate thumbnail path if needed
          FileSize: file.size,
          FileType: file.originalname.split('.').pop().toUpperCase(),
          MimeType: file.mimetype,
          Width: null, // Set if you extract dimensions
          Height: null, // Set if you extract dimensions
          IsActive: true,
          IsPrimary: i === 0 && (req.body.IsPrimary || false), // First image can be primary
          DisplayOrder: (req.body.DisplayOrder || 0) + i,
          AltText: req.body.AltText || file.originalname,
          Caption: req.body.Caption || null,
          UploadedBy: req.user.userId
        };

        const newImage = await product.addImage(imageData);
        uploadedImages.push(newImage[0]);
      }

      // Emit real-time update
      websocketService.emitInventoryUpdate({
        type: 'product_images_added',
        productId: req.params.id,
        images: uploadedImages,
        timestamp: new Date().toISOString()
      });

      res.status(201).json({
        success: true,
        message: `${uploadedImages.length} image(s) uploaded successfully`,
        data: uploadedImages
      });
    } catch (error) {
      logger.error('Error uploading images:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to upload images',
        error: error.message
      });
    }
  }
);

// =============================================
// PRODUCT COMPONENTS AND COLORS
// =============================================

// Add component/part to product
router.post('/:id/components',
  auth,
  rbac(['Admin', 'Employee']),
  [
    param('id').isUUID().withMessage('Product ID must be a valid UUID'),
    body('PartID').isUUID().withMessage('Part ID must be a valid UUID'),
    body('Quantity').isFloat({ min: 0 }).withMessage('Quantity must be a positive number'),
    body('IsOptional').optional().isBoolean().withMessage('IsOptional must be a boolean'),
    body('IsCustomizable').optional().isBoolean().withMessage('IsCustomizable must be a boolean'),
    body('DefaultConfiguration').optional().isJSON().withMessage('DefaultConfiguration must be valid JSON'),
    body('Notes').optional().isLength({ max: 500 }).withMessage('Notes must be less than 500 characters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const componentData = {
        ProductID: req.params.id,
        PartID: req.body.PartID,
        Quantity: req.body.Quantity,
        IsOptional: req.body.IsOptional || false,
        IsCustomizable: req.body.IsCustomizable || false,
        DefaultConfiguration: req.body.DefaultConfiguration || null,
        Notes: req.body.Notes || null
      };

      const product = new Product();
      const newComponent = await product.addComponent(componentData);

      res.status(201).json({
        success: true,
        message: 'Component added successfully',
        data: newComponent[0]
      });
    } catch (error) {
      logger.error('Error adding component:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to add component',
        error: error.message
      });
    }
  }
);

// Add color option to product
router.post('/:id/colors',
  auth,
  rbac(['Admin', 'Employee']),
  [
    param('id').isUUID().withMessage('Product ID must be a valid UUID'),
    body('ColorName').notEmpty().isLength({ min: 1, max: 100 }).withMessage('Color name is required and must be 1-100 characters'),
    body('ColorCode').optional().isLength({ max: 20 }).withMessage('Color code must be less than 20 characters'),
    body('ColorFamily').optional().isLength({ max: 50 }).withMessage('Color family must be less than 50 characters'),
    body('PriceAdjustment').optional().isFloat().withMessage('Price adjustment must be a number'),
    body('IsDefault').optional().isBoolean().withMessage('IsDefault must be a boolean'),
    body('SortOrder').optional().isInt({ min: 0 }).withMessage('Sort order must be a non-negative integer')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const colorData = {
        ProductID: req.params.id,
        ColorName: req.body.ColorName,
        ColorCode: req.body.ColorCode || null,
        ColorFamily: req.body.ColorFamily || null,
        PriceAdjustment: req.body.PriceAdjustment || 0,
        IsActive: true,
        IsDefault: req.body.IsDefault || false,
        SortOrder: req.body.SortOrder || 0
      };

      const product = new Product();
      const newColor = await product.addColor(colorData);

      res.status(201).json({
        success: true,
        message: 'Color option added successfully',
        data: newColor[0]
      });
    } catch (error) {
      logger.error('Error adding color:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to add color option',
        error: error.message
      });
    }
  }
);

// Get product audit trail
router.get('/:id/audit',
  auth,
  rbac(['Admin', 'Employee']),
  [
    param('id').isUUID().withMessage('Product ID must be a valid UUID'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const product = new Product();
      const auditTrail = await product.getAuditTrail(req.params.id, parseInt(req.query.limit) || 50);

      res.json({
        success: true,
        data: auditTrail
      });
    } catch (error) {
      logger.error('Error getting audit trail:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve audit trail',
        error: error.message
      });
    }
  }
);

module.exports = router;
