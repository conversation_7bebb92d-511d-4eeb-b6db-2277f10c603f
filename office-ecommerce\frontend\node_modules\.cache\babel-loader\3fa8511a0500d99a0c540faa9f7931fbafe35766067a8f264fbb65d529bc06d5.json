{"ast": null, "code": "import * as React from 'react';\nimport { Text3D } from './Text3D.js';\nimport { Center } from './Center.js';\n\n/* eslint react-hooks/exhaustive-deps: 1 */\nconst Example = /*#__PURE__*/React.forwardRef(({\n  font,\n  color = '#cbcbcb',\n  bevelSize = 0.04,\n  debug = false,\n  children,\n  ...props\n}, fref) => {\n  const [counter, setCounter] = React.useState(0);\n  const incr = React.useCallback((x = 1) => setCounter(counter + x), [counter]);\n  const decr = React.useCallback((x = 1) => setCounter(counter - x), [counter]); // ref-API\n\n  const api = React.useMemo(() => ({\n    incr,\n    decr\n  }), [incr, decr]);\n  React.useImperativeHandle(fref, () => api, [api]);\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(React.Suspense, {\n    fallback: null\n  }, /*#__PURE__*/React.createElement(Center, {\n    top: true,\n    cacheKey: JSON.stringify({\n      counter,\n      font\n    })\n  }, /*#__PURE__*/React.createElement(Text3D, {\n    bevelEnabled: true,\n    bevelSize: bevelSize,\n    font: font\n  }, debug ? /*#__PURE__*/React.createElement(\"meshNormalMaterial\", {\n    wireframe: true\n  }) : /*#__PURE__*/React.createElement(\"meshStandardMaterial\", {\n    color: color\n  }), counter))), children);\n});\nexport { Example };", "map": {"version": 3, "names": ["React", "Text3D", "Center", "Example", "forwardRef", "font", "color", "bevelSize", "debug", "children", "props", "fref", "counter", "setCounter", "useState", "incr", "useCallback", "x", "decr", "api", "useMemo", "useImperativeHandle", "createElement", "Suspense", "fallback", "top", "cache<PERSON>ey", "JSON", "stringify", "bevelEnabled", "wireframe"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Example.js"], "sourcesContent": ["import * as React from 'react';\nimport { Text3D } from './Text3D.js';\nimport { Center } from './Center.js';\n\n/* eslint react-hooks/exhaustive-deps: 1 */\nconst Example = /*#__PURE__*/React.forwardRef(({\n  font,\n  color = '#cbcbcb',\n  bevelSize = 0.04,\n  debug = false,\n  children,\n  ...props\n}, fref) => {\n  const [counter, setCounter] = React.useState(0);\n  const incr = React.useCallback((x = 1) => setCounter(counter + x), [counter]);\n  const decr = React.useCallback((x = 1) => setCounter(counter - x), [counter]); // ref-API\n\n  const api = React.useMemo(() => ({\n    incr,\n    decr\n  }), [incr, decr]);\n  React.useImperativeHandle(fref, () => api, [api]);\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(React.Suspense, {\n    fallback: null\n  }, /*#__PURE__*/React.createElement(Center, {\n    top: true,\n    cacheKey: JSON.stringify({\n      counter,\n      font\n    })\n  }, /*#__PURE__*/React.createElement(Text3D, {\n    bevelEnabled: true,\n    bevelSize: bevelSize,\n    font: font\n  }, debug ? /*#__PURE__*/React.createElement(\"meshNormalMaterial\", {\n    wireframe: true\n  }) : /*#__PURE__*/React.createElement(\"meshStandardMaterial\", {\n    color: color\n  }), counter))), children);\n});\n\nexport { Example };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA,MAAMC,OAAO,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,CAAC;EAC7CC,IAAI;EACJC,KAAK,GAAG,SAAS;EACjBC,SAAS,GAAG,IAAI;EAChBC,KAAK,GAAG,KAAK;EACbC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGb,KAAK,CAACc,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAMC,IAAI,GAAGf,KAAK,CAACgB,WAAW,CAAC,CAACC,CAAC,GAAG,CAAC,KAAKJ,UAAU,CAACD,OAAO,GAAGK,CAAC,CAAC,EAAE,CAACL,OAAO,CAAC,CAAC;EAC7E,MAAMM,IAAI,GAAGlB,KAAK,CAACgB,WAAW,CAAC,CAACC,CAAC,GAAG,CAAC,KAAKJ,UAAU,CAACD,OAAO,GAAGK,CAAC,CAAC,EAAE,CAACL,OAAO,CAAC,CAAC,CAAC,CAAC;;EAE/E,MAAMO,GAAG,GAAGnB,KAAK,CAACoB,OAAO,CAAC,OAAO;IAC/BL,IAAI;IACJG;EACF,CAAC,CAAC,EAAE,CAACH,IAAI,EAAEG,IAAI,CAAC,CAAC;EACjBlB,KAAK,CAACqB,mBAAmB,CAACV,IAAI,EAAE,MAAMQ,GAAG,EAAE,CAACA,GAAG,CAAC,CAAC;EACjD,OAAO,aAAanB,KAAK,CAACsB,aAAa,CAAC,OAAO,EAAEZ,KAAK,EAAE,aAAaV,KAAK,CAACsB,aAAa,CAACtB,KAAK,CAACuB,QAAQ,EAAE;IACvGC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAaxB,KAAK,CAACsB,aAAa,CAACpB,MAAM,EAAE;IAC1CuB,GAAG,EAAE,IAAI;IACTC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAAC;MACvBhB,OAAO;MACPP;IACF,CAAC;EACH,CAAC,EAAE,aAAaL,KAAK,CAACsB,aAAa,CAACrB,MAAM,EAAE;IAC1C4B,YAAY,EAAE,IAAI;IAClBtB,SAAS,EAAEA,SAAS;IACpBF,IAAI,EAAEA;EACR,CAAC,EAAEG,KAAK,GAAG,aAAaR,KAAK,CAACsB,aAAa,CAAC,oBAAoB,EAAE;IAChEQ,SAAS,EAAE;EACb,CAAC,CAAC,GAAG,aAAa9B,KAAK,CAACsB,aAAa,CAAC,sBAAsB,EAAE;IAC5DhB,KAAK,EAAEA;EACT,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,EAAEH,QAAQ,CAAC;AAC3B,CAAC,CAAC;AAEF,SAASN,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}