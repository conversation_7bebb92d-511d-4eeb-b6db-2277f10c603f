{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Vector3, QuadraticBezierCurve3 } from 'three';\nimport mergeRefs from 'react-merge-refs';\nimport { Line } from './Line.js';\nconst v = new Vector3();\nconst QuadraticBezierLine = /*#__PURE__*/React.forwardRef(function QuadraticBezierLine({\n  start = [0, 0, 0],\n  end = [0, 0, 0],\n  mid,\n  segments = 20,\n  ...rest\n}, forwardref) {\n  const ref = React.useRef(null);\n  const [curve] = React.useState(() => new QuadraticBezierCurve3(undefined, undefined, undefined));\n  const getPoints = React.useCallback((start, end, mid, segments = 20) => {\n    if (start instanceof Vector3) curve.v0.copy(start);else curve.v0.set(...start);\n    if (end instanceof Vector3) curve.v2.copy(end);else curve.v2.set(...end);\n    if (mid instanceof Vector3) {\n      curve.v1.copy(mid);\n    } else if (Array.isArray(mid)) {\n      curve.v1.set(...mid);\n    } else {\n      curve.v1.copy(curve.v0.clone().add(curve.v2.clone().sub(curve.v0)).add(v.set(0, curve.v0.y - curve.v2.y, 0)));\n    }\n    return curve.getPoints(segments);\n  }, []);\n  React.useLayoutEffect(() => {\n    ref.current.setPoints = (start, end, mid) => {\n      const points = getPoints(start, end, mid);\n      if (ref.current.geometry) ref.current.geometry.setPositions(points.map(p => p.toArray()).flat());\n    };\n  }, []);\n  const points = React.useMemo(() => getPoints(start, end, mid, segments), [start, end, mid, segments]);\n  return /*#__PURE__*/React.createElement(Line, _extends({\n    ref: mergeRefs([ref, forwardref]),\n    points: points\n  }, rest));\n});\nexport { QuadraticBezierLine };", "map": {"version": 3, "names": ["_extends", "React", "Vector3", "QuadraticBezierCurve3", "mergeRefs", "Line", "v", "QuadraticBezierLine", "forwardRef", "start", "end", "mid", "segments", "rest", "forwardref", "ref", "useRef", "curve", "useState", "undefined", "getPoints", "useCallback", "v0", "copy", "set", "v2", "v1", "Array", "isArray", "clone", "add", "sub", "y", "useLayoutEffect", "current", "setPoints", "points", "geometry", "setPositions", "map", "p", "toArray", "flat", "useMemo", "createElement"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/QuadraticBezierLine.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Vector3, QuadraticBezierCurve3 } from 'three';\nimport mergeRefs from 'react-merge-refs';\nimport { Line } from './Line.js';\n\nconst v = new Vector3();\nconst QuadraticBezierLine = /*#__PURE__*/React.forwardRef(function QuadraticBezierLine({\n  start = [0, 0, 0],\n  end = [0, 0, 0],\n  mid,\n  segments = 20,\n  ...rest\n}, forwardref) {\n  const ref = React.useRef(null);\n  const [curve] = React.useState(() => new QuadraticBezierCurve3(undefined, undefined, undefined));\n  const getPoints = React.useCallback((start, end, mid, segments = 20) => {\n    if (start instanceof Vector3) curve.v0.copy(start);else curve.v0.set(...start);\n    if (end instanceof Vector3) curve.v2.copy(end);else curve.v2.set(...end);\n\n    if (mid instanceof Vector3) {\n      curve.v1.copy(mid);\n    } else if (Array.isArray(mid)) {\n      curve.v1.set(...mid);\n    } else {\n      curve.v1.copy(curve.v0.clone().add(curve.v2.clone().sub(curve.v0)).add(v.set(0, curve.v0.y - curve.v2.y, 0)));\n    }\n\n    return curve.getPoints(segments);\n  }, []);\n  React.useLayoutEffect(() => {\n    ref.current.setPoints = (start, end, mid) => {\n      const points = getPoints(start, end, mid);\n      if (ref.current.geometry) ref.current.geometry.setPositions(points.map(p => p.toArray()).flat());\n    };\n  }, []);\n  const points = React.useMemo(() => getPoints(start, end, mid, segments), [start, end, mid, segments]);\n  return /*#__PURE__*/React.createElement(Line, _extends({\n    ref: mergeRefs([ref, forwardref]),\n    points: points\n  }, rest));\n});\n\nexport { QuadraticBezierLine };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,qBAAqB,QAAQ,OAAO;AACtD,OAAOC,SAAS,MAAM,kBAAkB;AACxC,SAASC,IAAI,QAAQ,WAAW;AAEhC,MAAMC,CAAC,GAAG,IAAIJ,OAAO,CAAC,CAAC;AACvB,MAAMK,mBAAmB,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,SAASD,mBAAmBA,CAAC;EACrFE,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACjBC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACfC,GAAG;EACHC,QAAQ,GAAG,EAAE;EACb,GAAGC;AACL,CAAC,EAAEC,UAAU,EAAE;EACb,MAAMC,GAAG,GAAGd,KAAK,CAACe,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAACC,KAAK,CAAC,GAAGhB,KAAK,CAACiB,QAAQ,CAAC,MAAM,IAAIf,qBAAqB,CAACgB,SAAS,EAAEA,SAAS,EAAEA,SAAS,CAAC,CAAC;EAChG,MAAMC,SAAS,GAAGnB,KAAK,CAACoB,WAAW,CAAC,CAACZ,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,GAAG,EAAE,KAAK;IACtE,IAAIH,KAAK,YAAYP,OAAO,EAAEe,KAAK,CAACK,EAAE,CAACC,IAAI,CAACd,KAAK,CAAC,CAAC,KAAKQ,KAAK,CAACK,EAAE,CAACE,GAAG,CAAC,GAAGf,KAAK,CAAC;IAC9E,IAAIC,GAAG,YAAYR,OAAO,EAAEe,KAAK,CAACQ,EAAE,CAACF,IAAI,CAACb,GAAG,CAAC,CAAC,KAAKO,KAAK,CAACQ,EAAE,CAACD,GAAG,CAAC,GAAGd,GAAG,CAAC;IAExE,IAAIC,GAAG,YAAYT,OAAO,EAAE;MAC1Be,KAAK,CAACS,EAAE,CAACH,IAAI,CAACZ,GAAG,CAAC;IACpB,CAAC,MAAM,IAAIgB,KAAK,CAACC,OAAO,CAACjB,GAAG,CAAC,EAAE;MAC7BM,KAAK,CAACS,EAAE,CAACF,GAAG,CAAC,GAAGb,GAAG,CAAC;IACtB,CAAC,MAAM;MACLM,KAAK,CAACS,EAAE,CAACH,IAAI,CAACN,KAAK,CAACK,EAAE,CAACO,KAAK,CAAC,CAAC,CAACC,GAAG,CAACb,KAAK,CAACQ,EAAE,CAACI,KAAK,CAAC,CAAC,CAACE,GAAG,CAACd,KAAK,CAACK,EAAE,CAAC,CAAC,CAACQ,GAAG,CAACxB,CAAC,CAACkB,GAAG,CAAC,CAAC,EAAEP,KAAK,CAACK,EAAE,CAACU,CAAC,GAAGf,KAAK,CAACQ,EAAE,CAACO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/G;IAEA,OAAOf,KAAK,CAACG,SAAS,CAACR,QAAQ,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EACNX,KAAK,CAACgC,eAAe,CAAC,MAAM;IAC1BlB,GAAG,CAACmB,OAAO,CAACC,SAAS,GAAG,CAAC1B,KAAK,EAAEC,GAAG,EAAEC,GAAG,KAAK;MAC3C,MAAMyB,MAAM,GAAGhB,SAAS,CAACX,KAAK,EAAEC,GAAG,EAAEC,GAAG,CAAC;MACzC,IAAII,GAAG,CAACmB,OAAO,CAACG,QAAQ,EAAEtB,GAAG,CAACmB,OAAO,CAACG,QAAQ,CAACC,YAAY,CAACF,MAAM,CAACG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;IAClG,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,MAAMN,MAAM,GAAGnC,KAAK,CAAC0C,OAAO,CAAC,MAAMvB,SAAS,CAACX,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,CAAC,EAAE,CAACH,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,CAAC,CAAC;EACrG,OAAO,aAAaX,KAAK,CAAC2C,aAAa,CAACvC,IAAI,EAAEL,QAAQ,CAAC;IACrDe,GAAG,EAAEX,SAAS,CAAC,CAACW,GAAG,EAAED,UAAU,CAAC,CAAC;IACjCsB,MAAM,EAAEA;EACV,CAAC,EAAEvB,IAAI,CAAC,CAAC;AACX,CAAC,CAAC;AAEF,SAASN,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}