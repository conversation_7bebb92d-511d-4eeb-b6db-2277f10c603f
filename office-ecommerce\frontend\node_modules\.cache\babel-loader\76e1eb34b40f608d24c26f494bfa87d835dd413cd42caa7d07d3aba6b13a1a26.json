{"ast": null, "code": "import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\n\n// 👇 uncomment when TS version supports function overloads\n// export function useFBO(settings?: FBOSettings)\nfunction useFBO(/** Width in pixels, or settings (will render fullscreen by default) */\nwidth, /** Height in pixels */\nheight, /**Settings */\nsettings) {\n  const size = useThree(state => state.size);\n  const viewport = useThree(state => state.viewport);\n  const _width = typeof width === 'number' ? width : size.width * viewport.dpr;\n  const _height = typeof height === 'number' ? height : size.height * viewport.dpr;\n  const _settings = (typeof width === 'number' ? settings : width) || {};\n  const {\n    samples = 0,\n    depth,\n    ...targetSettings\n  } = _settings;\n  const target = React.useMemo(() => {\n    const target = new THREE.WebGLRenderTarget(_width, _height, {\n      minFilter: THREE.LinearFilter,\n      magFilter: THREE.LinearFilter,\n      type: THREE.HalfFloatType,\n      ...targetSettings\n    });\n    if (depth) {\n      target.depthTexture = new THREE.DepthTexture(_width, _height, THREE.FloatType);\n    }\n    target.samples = samples;\n    return target;\n  }, []);\n  React.useLayoutEffect(() => {\n    target.setSize(_width, _height);\n    if (samples) target.samples = samples;\n  }, [samples, target, _width, _height]);\n  React.useEffect(() => {\n    return () => target.dispose();\n  }, []);\n  return target;\n}\nexport { useFBO };", "map": {"version": 3, "names": ["React", "THREE", "useThree", "useFBO", "width", "height", "settings", "size", "state", "viewport", "_width", "dpr", "_height", "_settings", "samples", "depth", "targetSettings", "target", "useMemo", "WebGLRenderTarget", "minFilter", "LinearFilter", "magFilter", "type", "HalfFloatType", "depthTexture", "DepthTexture", "FloatType", "useLayoutEffect", "setSize", "useEffect", "dispose"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useFBO.js"], "sourcesContent": ["import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\n\n// 👇 uncomment when TS version supports function overloads\n// export function useFBO(settings?: FBOSettings)\nfunction useFBO(\n/** Width in pixels, or settings (will render fullscreen by default) */\nwidth,\n/** Height in pixels */\nheight,\n/**Settings */\nsettings) {\n  const size = useThree(state => state.size);\n  const viewport = useThree(state => state.viewport);\n\n  const _width = typeof width === 'number' ? width : size.width * viewport.dpr;\n\n  const _height = typeof height === 'number' ? height : size.height * viewport.dpr;\n\n  const _settings = (typeof width === 'number' ? settings : width) || {};\n\n  const {\n    samples = 0,\n    depth,\n    ...targetSettings\n  } = _settings;\n  const target = React.useMemo(() => {\n    const target = new THREE.WebGLRenderTarget(_width, _height, {\n      minFilter: THREE.LinearFilter,\n      magFilter: THREE.LinearFilter,\n      type: THREE.HalfFloatType,\n      ...targetSettings\n    });\n\n    if (depth) {\n      target.depthTexture = new THREE.DepthTexture(_width, _height, THREE.FloatType);\n    }\n\n    target.samples = samples;\n    return target;\n  }, []);\n  React.useLayoutEffect(() => {\n    target.setSize(_width, _height);\n    if (samples) target.samples = samples;\n  }, [samples, target, _width, _height]);\n  React.useEffect(() => {\n    return () => target.dispose();\n  }, []);\n  return target;\n}\n\nexport { useFBO };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;;AAE7C;AACA;AACA,SAASC,MAAMA,CACf;AACAC,KAAK,EACL;AACAC,MAAM,EACN;AACAC,QAAQ,EAAE;EACR,MAAMC,IAAI,GAAGL,QAAQ,CAACM,KAAK,IAAIA,KAAK,CAACD,IAAI,CAAC;EAC1C,MAAME,QAAQ,GAAGP,QAAQ,CAACM,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;EAElD,MAAMC,MAAM,GAAG,OAAON,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGG,IAAI,CAACH,KAAK,GAAGK,QAAQ,CAACE,GAAG;EAE5E,MAAMC,OAAO,GAAG,OAAOP,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGE,IAAI,CAACF,MAAM,GAAGI,QAAQ,CAACE,GAAG;EAEhF,MAAME,SAAS,GAAG,CAAC,OAAOT,KAAK,KAAK,QAAQ,GAAGE,QAAQ,GAAGF,KAAK,KAAK,CAAC,CAAC;EAEtE,MAAM;IACJU,OAAO,GAAG,CAAC;IACXC,KAAK;IACL,GAAGC;EACL,CAAC,GAAGH,SAAS;EACb,MAAMI,MAAM,GAAGjB,KAAK,CAACkB,OAAO,CAAC,MAAM;IACjC,MAAMD,MAAM,GAAG,IAAIhB,KAAK,CAACkB,iBAAiB,CAACT,MAAM,EAAEE,OAAO,EAAE;MAC1DQ,SAAS,EAAEnB,KAAK,CAACoB,YAAY;MAC7BC,SAAS,EAAErB,KAAK,CAACoB,YAAY;MAC7BE,IAAI,EAAEtB,KAAK,CAACuB,aAAa;MACzB,GAAGR;IACL,CAAC,CAAC;IAEF,IAAID,KAAK,EAAE;MACTE,MAAM,CAACQ,YAAY,GAAG,IAAIxB,KAAK,CAACyB,YAAY,CAAChB,MAAM,EAAEE,OAAO,EAAEX,KAAK,CAAC0B,SAAS,CAAC;IAChF;IAEAV,MAAM,CAACH,OAAO,GAAGA,OAAO;IACxB,OAAOG,MAAM;EACf,CAAC,EAAE,EAAE,CAAC;EACNjB,KAAK,CAAC4B,eAAe,CAAC,MAAM;IAC1BX,MAAM,CAACY,OAAO,CAACnB,MAAM,EAAEE,OAAO,CAAC;IAC/B,IAAIE,OAAO,EAAEG,MAAM,CAACH,OAAO,GAAGA,OAAO;EACvC,CAAC,EAAE,CAACA,OAAO,EAAEG,MAAM,EAAEP,MAAM,EAAEE,OAAO,CAAC,CAAC;EACtCZ,KAAK,CAAC8B,SAAS,CAAC,MAAM;IACpB,OAAO,MAAMb,MAAM,CAACc,OAAO,CAAC,CAAC;EAC/B,CAAC,EAAE,EAAE,CAAC;EACN,OAAOd,MAAM;AACf;AAEA,SAASd,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}