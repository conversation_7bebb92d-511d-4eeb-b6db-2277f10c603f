{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { FlyControls as FlyControls$1 } from 'three-stdlib';\nconst FlyControls = /*#__PURE__*/React.forwardRef(({\n  domElement,\n  ...props\n}, fref) => {\n  const {\n    onChange,\n    makeDefault,\n    ...rest\n  } = props;\n  const invalidate = useThree(state => state.invalidate);\n  const camera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const get = useThree(state => state.get);\n  const set = useThree(state => state.set);\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = React.useMemo(() => new FlyControls$1(camera), [camera]);\n  React.useEffect(() => {\n    controls.connect(explDomElement);\n    return () => void controls.dispose();\n  }, [explDomElement, controls, invalidate]);\n  React.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (onChange) onChange(e);\n    };\n    controls.addEventListener == null ? void 0 : controls.addEventListener('change', callback);\n    return () => controls.removeEventListener == null ? void 0 : controls.removeEventListener('change', callback);\n  }, [onChange, invalidate]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  useFrame((_, delta) => controls.update(delta));\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: fref,\n    object: controls,\n    args: [camera, explDomElement]\n  }, rest));\n});\nexport { FlyControls };", "map": {"version": 3, "names": ["_extends", "useThree", "useFrame", "React", "FlyControls", "FlyControls$1", "forwardRef", "dom<PERSON>lement", "props", "fref", "onChange", "makeDefault", "rest", "invalidate", "state", "camera", "gl", "events", "get", "set", "explDomElement", "connected", "controls", "useMemo", "useEffect", "connect", "dispose", "callback", "e", "addEventListener", "removeEventListener", "old", "_", "delta", "update", "createElement", "ref", "object", "args"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/FlyControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { FlyControls as FlyControls$1 } from 'three-stdlib';\n\nconst FlyControls = /*#__PURE__*/React.forwardRef(({\n  domElement,\n  ...props\n}, fref) => {\n  const {\n    onChange,\n    makeDefault,\n    ...rest\n  } = props;\n  const invalidate = useThree(state => state.invalidate);\n  const camera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const get = useThree(state => state.get);\n  const set = useThree(state => state.set);\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = React.useMemo(() => new FlyControls$1(camera), [camera]);\n  React.useEffect(() => {\n    controls.connect(explDomElement);\n    return () => void controls.dispose();\n  }, [explDomElement, controls, invalidate]);\n  React.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (onChange) onChange(e);\n    };\n\n    controls.addEventListener == null ? void 0 : controls.addEventListener('change', callback);\n    return () => controls.removeEventListener == null ? void 0 : controls.removeEventListener('change', callback);\n  }, [onChange, invalidate]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  useFrame((_, delta) => controls.update(delta));\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: fref,\n    object: controls,\n    args: [camera, explDomElement]\n  }, rest));\n});\n\nexport { FlyControls };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,IAAIC,aAAa,QAAQ,cAAc;AAE3D,MAAMD,WAAW,GAAG,aAAaD,KAAK,CAACG,UAAU,CAAC,CAAC;EACjDC,UAAU;EACV,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAM;IACJC,QAAQ;IACRC,WAAW;IACX,GAAGC;EACL,CAAC,GAAGJ,KAAK;EACT,MAAMK,UAAU,GAAGZ,QAAQ,CAACa,KAAK,IAAIA,KAAK,CAACD,UAAU,CAAC;EACtD,MAAME,MAAM,GAAGd,QAAQ,CAACa,KAAK,IAAIA,KAAK,CAACC,MAAM,CAAC;EAC9C,MAAMC,EAAE,GAAGf,QAAQ,CAACa,KAAK,IAAIA,KAAK,CAACE,EAAE,CAAC;EACtC,MAAMC,MAAM,GAAGhB,QAAQ,CAACa,KAAK,IAAIA,KAAK,CAACG,MAAM,CAAC;EAC9C,MAAMC,GAAG,GAAGjB,QAAQ,CAACa,KAAK,IAAIA,KAAK,CAACI,GAAG,CAAC;EACxC,MAAMC,GAAG,GAAGlB,QAAQ,CAACa,KAAK,IAAIA,KAAK,CAACK,GAAG,CAAC;EACxC,MAAMC,cAAc,GAAGb,UAAU,IAAIU,MAAM,CAACI,SAAS,IAAIL,EAAE,CAACT,UAAU;EACtE,MAAMe,QAAQ,GAAGnB,KAAK,CAACoB,OAAO,CAAC,MAAM,IAAIlB,aAAa,CAACU,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACzEZ,KAAK,CAACqB,SAAS,CAAC,MAAM;IACpBF,QAAQ,CAACG,OAAO,CAACL,cAAc,CAAC;IAChC,OAAO,MAAM,KAAKE,QAAQ,CAACI,OAAO,CAAC,CAAC;EACtC,CAAC,EAAE,CAACN,cAAc,EAAEE,QAAQ,EAAET,UAAU,CAAC,CAAC;EAC1CV,KAAK,CAACqB,SAAS,CAAC,MAAM;IACpB,MAAMG,QAAQ,GAAGC,CAAC,IAAI;MACpBf,UAAU,CAAC,CAAC;MACZ,IAAIH,QAAQ,EAAEA,QAAQ,CAACkB,CAAC,CAAC;IAC3B,CAAC;IAEDN,QAAQ,CAACO,gBAAgB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGP,QAAQ,CAACO,gBAAgB,CAAC,QAAQ,EAAEF,QAAQ,CAAC;IAC1F,OAAO,MAAML,QAAQ,CAACQ,mBAAmB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGR,QAAQ,CAACQ,mBAAmB,CAAC,QAAQ,EAAEH,QAAQ,CAAC;EAC/G,CAAC,EAAE,CAACjB,QAAQ,EAAEG,UAAU,CAAC,CAAC;EAC1BV,KAAK,CAACqB,SAAS,CAAC,MAAM;IACpB,IAAIb,WAAW,EAAE;MACf,MAAMoB,GAAG,GAAGb,GAAG,CAAC,CAAC,CAACI,QAAQ;MAC1BH,GAAG,CAAC;QACFG;MACF,CAAC,CAAC;MACF,OAAO,MAAMH,GAAG,CAAC;QACfG,QAAQ,EAAES;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACpB,WAAW,EAAEW,QAAQ,CAAC,CAAC;EAC3BpB,QAAQ,CAAC,CAAC8B,CAAC,EAAEC,KAAK,KAAKX,QAAQ,CAACY,MAAM,CAACD,KAAK,CAAC,CAAC;EAC9C,OAAO,aAAa9B,KAAK,CAACgC,aAAa,CAAC,WAAW,EAAEnC,QAAQ,CAAC;IAC5DoC,GAAG,EAAE3B,IAAI;IACT4B,MAAM,EAAEf,QAAQ;IAChBgB,IAAI,EAAE,CAACvB,MAAM,EAAEK,cAAc;EAC/B,CAAC,EAAER,IAAI,CAAC,CAAC;AACX,CAAC,CAAC;AAEF,SAASR,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}