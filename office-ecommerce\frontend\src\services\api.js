// API service for frontend application
// This handles communication with the backend API
import apiClient from './apiClient';

// Mock data for products
const mockProducts = [
  {
    id: 1,
    name: "Executive Office Table",
    description: "Premium executive office table with modern design and customizable features.",
    basePrice: 500,
    category: "Tables",
    image: "/images/table-1.jpg",
    features: ["Adjustable height", "Premium materials", "Modern design", "Customizable"]
  },
  {
    id: 2,
    name: "Ergonomic Office Chair",
    description: "Comfortable ergonomic office chair with lumbar support and adjustable features.",
    basePrice: 200,
    category: "Chairs",
    image: "/images/chair-1.jpg",
    features: ["Ergonomic design", "Lumbar support", "Adjustable height", "Breathable mesh"]
  },
  {
    id: 3,
    name: "Storage Cabinet",
    description: "Modern storage cabinet with multiple compartments and secure locking system.",
    basePrice: 800,
    category: "Storage",
    image: "/images/cabinet-1.jpg",
    features: ["Multiple compartments", "Secure locks", "Modern design", "Durable materials"]
  }
];

// Mock categories
const mockCategories = [
  { id: 1, name: "Tables", description: "Office tables and desks" },
  { id: 2, name: "Chairs", description: "Office chairs and seating" },
  { id: 3, name: "Storage", description: "Storage solutions and cabinets" },
  { id: 4, name: "Workstations", description: "Complete workstation setups" }
];

// Mock API functions
const api = {
  // Get all products
  getProducts: () => {
    return Promise.resolve({
      success: true,
      data: mockProducts
    });
  },

  // Get product by ID
  getProduct: (id) => {
    const product = mockProducts.find(p => p.id === parseInt(id));
    return Promise.resolve({
      success: true,
      data: product || null
    });
  },

  // Get categories
  getCategories: () => {
    return Promise.resolve({
      success: true,
      data: mockCategories
    });
  }
};

// Enhanced Products API for admin functionality
const productsApi = {
  // Get products with pagination and filtering
  getProducts: async (params = {}) => {
    try {
      const response = await apiClient.get('/api/products', { params });
      return response;
    } catch (error) {
      console.error('Error fetching products:', error);
      // Fallback to mock data if backend is not available
      return {
        success: true,
        data: {
          products: [],
          pagination: {
            currentPage: 1,
            totalPages: 0,
            totalItems: 0,
            itemsPerPage: 20
          }
        }
      };
    }
  },

  // Get product by ID with full details
  getProductById: async (id) => {
    try {
      const response = await apiClient.get(`/api/products/${id}`);
      return response;
    } catch (error) {
      console.error('Error fetching product by ID:', error);
      return {
        success: false,
        message: 'Product not found'
      };
    }
  },

  // Create or update product
  createOrUpdateProduct: async (productData) => {
    try {
      const url = productData.ProductID
        ? `/api/products/${productData.ProductID}`
        : '/api/products';

      const method = productData.ProductID ? 'PUT' : 'POST';

      const response = await apiClient[method.toLowerCase()](url, productData);
      return response;
    } catch (error) {
      console.error('Error creating/updating product:', error);
      throw error;
    }
  },

  // Delete product
  deleteProduct: async (productId) => {
    try {
      const response = await apiClient.delete(`/api/products/${productId}`);
      return response;
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  },

  // Upload 3D model
  uploadModel: async (productId, formData) => {
    try {
      const response = await apiClient.client.post(`/api/products/${productId}/models`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error uploading 3D model:', error);
      throw error;
    }
  },

  // Upload images
  uploadImages: async (productId, formData) => {
    try {
      const response = await apiClient.client.post(`/api/products/${productId}/images`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error uploading images:', error);
      throw error;
    }
  },

  // Get categories
  getCategories: async () => {
    try {
      const response = await apiClient.get('/api/products/categories');
      return response;
    } catch (error) {
      console.error('Error fetching categories:', error);
      // Fallback to mock data if backend is not available
      return {
        success: true,
        data: mockCategories
      };
    }
  }
};

// Original API with authentication
const authApi = {
  // Mock authentication with admin support
  login: (credentials) => {
    // Check for admin credentials
    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {
      return Promise.resolve({
        success: true,
        data: {
          token: 'mock-admin-jwt-token',
          user: {
            id: 1,
            firstName: 'Admin',
            lastName: 'User',
            email: credentials.email,
            role: 'Admin'
          }
        }
      });
    }

    // Check for employee credentials
    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {
      return Promise.resolve({
        success: true,
        data: {
          token: 'mock-employee-jwt-token',
          user: {
            id: 2,
            firstName: 'Manager',
            lastName: 'User',
            email: credentials.email,
            role: 'Employee'
          }
        }
      });
    }

    // Regular customer login
    return Promise.resolve({
      success: true,
      data: {
        token: 'mock-jwt-token',
        user: {
          id: 3,
          firstName: 'Demo',
          lastName: 'User',
          email: credentials.email,
          role: 'Customer'
        }
      }
    });
  },

  // Mock registration (always succeeds)
  register: (userData) => {
    return Promise.resolve({
      success: true,
      data: {
        token: 'mock-jwt-token',
        user: {
          id: 1,
          name: userData.name,
          email: userData.email
        }
      }
    });
  }
};

// Combine APIs
const combinedApi = {
  ...api,
  ...authApi
};

export default combinedApi;
export { productsApi };
