// Mock API service for frontend-only application
// This replaces the backend API with local mock data

// Mock data for products
const mockProducts = [
  {
    id: 1,
    name: "Executive Office Table",
    description: "Premium executive office table with modern design and customizable features.",
    basePrice: 500,
    category: "Tables",
    image: "/images/table-1.jpg",
    features: ["Adjustable height", "Premium materials", "Modern design", "Customizable"]
  },
  {
    id: 2,
    name: "Ergonomic Office Chair",
    description: "Comfortable ergonomic office chair with lumbar support and adjustable features.",
    basePrice: 200,
    category: "Chairs",
    image: "/images/chair-1.jpg",
    features: ["Ergonomic design", "Lumbar support", "Adjustable height", "Breathable mesh"]
  },
  {
    id: 3,
    name: "Storage Cabinet",
    description: "Modern storage cabinet with multiple compartments and secure locking system.",
    basePrice: 800,
    category: "Storage",
    image: "/images/cabinet-1.jpg",
    features: ["Multiple compartments", "Secure locks", "Modern design", "Durable materials"]
  }
];

// Mock categories
const mockCategories = [
  { id: 1, name: "Tables", description: "Office tables and desks" },
  { id: 2, name: "Chairs", description: "Office chairs and seating" },
  { id: 3, name: "Storage", description: "Storage solutions and cabinets" },
  { id: 4, name: "Workstations", description: "Complete workstation setups" }
];

// Mock API functions
const api = {
  // Get all products
  getProducts: () => {
    return Promise.resolve({
      success: true,
      data: mockProducts
    });
  },

  // Get product by ID
  getProduct: (id) => {
    const product = mockProducts.find(p => p.id === parseInt(id));
    return Promise.resolve({
      success: true,
      data: product || null
    });
  },

  // Get categories
  getCategories: () => {
    return Promise.resolve({
      success: true,
      data: mockCategories
    });
  }
};

// Enhanced Products API for admin functionality
const productsApi = {
  // Get products with pagination and filtering
  getProducts: (params = {}) => {
    const {
      page = 1,
      limit = 20,
      search = '',
      category = '',
      status = '',
      sortBy = 'ProductName',
      sortDirection = 'ASC'
    } = params;

    // Simulate API delay
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredProducts = [...mockProducts];

        // Apply search filter
        if (search) {
          filteredProducts = filteredProducts.filter(product =>
            product.name.toLowerCase().includes(search.toLowerCase()) ||
            product.description.toLowerCase().includes(search.toLowerCase())
          );
        }

        // Apply category filter
        if (category) {
          filteredProducts = filteredProducts.filter(product =>
            product.category === category
          );
        }

        // Apply status filter (mock all as Active)
        if (status && status !== 'Active') {
          filteredProducts = [];
        }

        // Apply sorting
        filteredProducts.sort((a, b) => {
          let aValue = a.name;
          let bValue = b.name;

          if (sortBy === 'BasePrice') {
            aValue = a.basePrice;
            bValue = b.basePrice;
          }

          if (sortDirection === 'DESC') {
            return bValue > aValue ? 1 : -1;
          }
          return aValue > bValue ? 1 : -1;
        });

        // Apply pagination
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

        // Transform to match backend structure
        const transformedProducts = paginatedProducts.map(product => ({
          ProductID: product.id,
          ProductName: product.name,
          ProductCode: `PRD-${product.id.toString().padStart(3, '0')}`,
          CategoryName: product.category,
          BasePrice: product.basePrice,
          Status: 'Active',
          ImageCount: 1,
          ModelCount: 1,
          UpdatedAt: new Date().toISOString()
        }));

        resolve({
          success: true,
          data: transformedProducts,
          pagination: {
            currentPage: page,
            totalPages: Math.ceil(filteredProducts.length / limit),
            totalCount: filteredProducts.length,
            pageSize: limit
          }
        });
      }, 500);
    });
  },

  // Get product by ID with full details
  getProductById: (id) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const product = mockProducts.find(p => p.id === parseInt(id));

        if (!product) {
          resolve({
            success: false,
            message: 'Product not found'
          });
          return;
        }

        // Transform to match backend structure with full details
        const transformedProduct = {
          product: {
            ProductID: product.id,
            ProductName: product.name,
            ProductCode: `PRD-${product.id.toString().padStart(3, '0')}`,
            CategoryName: product.category,
            CategoryID: mockCategories.find(c => c.name === product.category)?.id || 1,
            Description: product.description,
            BasePrice: product.basePrice,
            Status: 'Active',
            IsCustomizable: true,
            IsActive: true,
            CreatedAt: new Date().toISOString(),
            UpdatedAt: new Date().toISOString(),
            CreatedBy: 'Admin User',
            UpdatedBy: 'Admin User'
          },
          models: [
            {
              FileName: `${product.name.toLowerCase().replace(/\s+/g, '-')}.glb`,
              FileSize: 2048000,
              IsPrimary: true
            }
          ],
          images: [
            {
              FileName: `${product.name.toLowerCase().replace(/\s+/g, '-')}.jpg`,
              FileSize: 512000,
              IsPrimary: true,
              AltText: product.name
            }
          ],
          components: [],
          colors: [
            {
              ColorName: 'Natural Wood',
              HexCode: '#D2B48C',
              PriceModifier: 0
            },
            {
              ColorName: 'Dark Walnut',
              HexCode: '#5D4037',
              PriceModifier: 50
            }
          ],
          auditTrail: [
            {
              Action: 'Created',
              ChangeDate: new Date().toISOString(),
              ChangedBy: 'Admin User',
              ChangeDetails: 'Product created'
            }
          ]
        };

        resolve({
          success: true,
          data: transformedProduct
        });
      }, 300);
    });
  },

  // Create or update product
  createOrUpdateProduct: (productData) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Simulate validation
        if (!productData.ProductName || !productData.BasePrice) {
          resolve({
            success: false,
            message: 'Product name and base price are required'
          });
          return;
        }

        const newProduct = {
          ProductID: productData.ProductID || Date.now(),
          ...productData,
          CreatedAt: new Date().toISOString(),
          UpdatedAt: new Date().toISOString()
        };

        resolve({
          success: true,
          data: newProduct,
          message: productData.ProductID ? 'Product updated successfully' : 'Product created successfully'
        });
      }, 800);
    });
  },

  // Delete product
  deleteProduct: (productId) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: 'Product deleted successfully'
        });
      }, 500);
    });
  },

  // Upload 3D model
  uploadModel: (productId, formData) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            FileName: 'uploaded-model.glb',
            FileSize: 2048000,
            IsPrimary: true
          },
          message: '3D model uploaded successfully'
        });
      }, 2000);
    });
  },

  // Upload images
  uploadImages: (productId, formData) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: [
            {
              FileName: 'uploaded-image.jpg',
              FileSize: 512000,
              IsPrimary: true
            }
          ],
          message: 'Images uploaded successfully'
        });
      }, 1500);
    });
  },

  // Get categories
  getCategories: () => {
    return Promise.resolve({
      success: true,
      data: mockCategories
    });
  }
};

// Original API with authentication
const authApi = {
  // Mock authentication with admin support
  login: (credentials) => {
    // Check for admin credentials
    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {
      return Promise.resolve({
        success: true,
        data: {
          token: 'mock-admin-jwt-token',
          user: {
            id: 1,
            firstName: 'Admin',
            lastName: 'User',
            email: credentials.email,
            role: 'Admin'
          }
        }
      });
    }

    // Check for employee credentials
    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {
      return Promise.resolve({
        success: true,
        data: {
          token: 'mock-employee-jwt-token',
          user: {
            id: 2,
            firstName: 'Manager',
            lastName: 'User',
            email: credentials.email,
            role: 'Employee'
          }
        }
      });
    }

    // Regular customer login
    return Promise.resolve({
      success: true,
      data: {
        token: 'mock-jwt-token',
        user: {
          id: 3,
          firstName: 'Demo',
          lastName: 'User',
          email: credentials.email,
          role: 'Customer'
        }
      }
    });
  },

  // Mock registration (always succeeds)
  register: (userData) => {
    return Promise.resolve({
      success: true,
      data: {
        token: 'mock-jwt-token',
        user: {
          id: 1,
          name: userData.name,
          email: userData.email
        }
      }
    });
  }
};

// Combine APIs
const combinedApi = {
  ...api,
  ...authApi
};

export default combinedApi;
export { productsApi };
