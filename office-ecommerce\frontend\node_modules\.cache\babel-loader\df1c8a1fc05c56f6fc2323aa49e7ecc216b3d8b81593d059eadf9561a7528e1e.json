{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Vector3, CubicBezierCurve3 } from 'three';\nimport { Line } from './Line.js';\nconst CubicBezierLine = /*#__PURE__*/React.forwardRef(function CubicBezierLine({\n  start,\n  end,\n  midA,\n  midB,\n  segments = 20,\n  ...rest\n}, ref) {\n  const points = React.useMemo(() => {\n    const startV = start instanceof Vector3 ? start : new Vector3(...start);\n    const endV = end instanceof Vector3 ? end : new Vector3(...end);\n    const midAV = midA instanceof Vector3 ? midA : new Vector3(...midA);\n    const midBV = midB instanceof Vector3 ? midB : new Vector3(...midB);\n    const interpolatedV = new CubicBezierCurve3(startV, midAV, midBV, endV).getPoints(segments);\n    return interpolatedV;\n  }, [start, end, midA, midB, segments]);\n  return /*#__PURE__*/React.createElement(Line, _extends({\n    ref: ref,\n    points: points\n  }, rest));\n});\nexport { CubicBezierLine };", "map": {"version": 3, "names": ["_extends", "React", "Vector3", "CubicBezierCurve3", "Line", "CubicBezierLine", "forwardRef", "start", "end", "midA", "midB", "segments", "rest", "ref", "points", "useMemo", "startV", "endV", "midAV", "midBV", "interpolatedV", "getPoints", "createElement"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/CubicBezierLine.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Vector3, CubicBezierCurve3 } from 'three';\nimport { Line } from './Line.js';\n\nconst CubicBezierLine = /*#__PURE__*/React.forwardRef(function CubicBezierLine({\n  start,\n  end,\n  midA,\n  midB,\n  segments = 20,\n  ...rest\n}, ref) {\n  const points = React.useMemo(() => {\n    const startV = start instanceof Vector3 ? start : new Vector3(...start);\n    const endV = end instanceof Vector3 ? end : new Vector3(...end);\n    const midAV = midA instanceof Vector3 ? midA : new Vector3(...midA);\n    const midBV = midB instanceof Vector3 ? midB : new Vector3(...midB);\n    const interpolatedV = new CubicBezierCurve3(startV, midAV, midBV, endV).getPoints(segments);\n    return interpolatedV;\n  }, [start, end, midA, midB, segments]);\n  return /*#__PURE__*/React.createElement(Line, _extends({\n    ref: ref,\n    points: points\n  }, rest));\n});\n\nexport { CubicBezierLine };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,iBAAiB,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,WAAW;AAEhC,MAAMC,eAAe,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,SAASD,eAAeA,CAAC;EAC7EE,KAAK;EACLC,GAAG;EACHC,IAAI;EACJC,IAAI;EACJC,QAAQ,GAAG,EAAE;EACb,GAAGC;AACL,CAAC,EAAEC,GAAG,EAAE;EACN,MAAMC,MAAM,GAAGb,KAAK,CAACc,OAAO,CAAC,MAAM;IACjC,MAAMC,MAAM,GAAGT,KAAK,YAAYL,OAAO,GAAGK,KAAK,GAAG,IAAIL,OAAO,CAAC,GAAGK,KAAK,CAAC;IACvE,MAAMU,IAAI,GAAGT,GAAG,YAAYN,OAAO,GAAGM,GAAG,GAAG,IAAIN,OAAO,CAAC,GAAGM,GAAG,CAAC;IAC/D,MAAMU,KAAK,GAAGT,IAAI,YAAYP,OAAO,GAAGO,IAAI,GAAG,IAAIP,OAAO,CAAC,GAAGO,IAAI,CAAC;IACnE,MAAMU,KAAK,GAAGT,IAAI,YAAYR,OAAO,GAAGQ,IAAI,GAAG,IAAIR,OAAO,CAAC,GAAGQ,IAAI,CAAC;IACnE,MAAMU,aAAa,GAAG,IAAIjB,iBAAiB,CAACa,MAAM,EAAEE,KAAK,EAAEC,KAAK,EAAEF,IAAI,CAAC,CAACI,SAAS,CAACV,QAAQ,CAAC;IAC3F,OAAOS,aAAa;EACtB,CAAC,EAAE,CAACb,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,CAAC,CAAC;EACtC,OAAO,aAAaV,KAAK,CAACqB,aAAa,CAAClB,IAAI,EAAEJ,QAAQ,CAAC;IACrDa,GAAG,EAAEA,GAAG;IACRC,MAAM,EAAEA;EACV,CAAC,EAAEF,IAAI,CAAC,CAAC;AACX,CAAC,CAAC;AAEF,SAASP,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}