{"ast": null, "code": "function mergeRefs(refs) {\n  return function (value) {\n    refs.forEach(function (ref) {\n      if (typeof ref === \"function\") {\n        ref(value);\n      } else if (ref != null) {\n        ref.current = value;\n      }\n    });\n  };\n}\nexport default mergeRefs;", "map": {"version": 3, "names": ["mergeRefs", "refs", "value", "for<PERSON>ach", "ref", "current"], "sources": ["C:\\DesignXcel\\office-ecommerce\\frontend\\node_modules\\react-merge-refs\\src\\index.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nexport default function mergeRefs<T = any>(\n  refs: Array<React.MutableRefObject<T> | React.LegacyRef<T>>\n): React.RefCallback<T> {\n  return (value) => {\n    refs.forEach((ref) => {\n      if (typeof ref === \"function\") {\n        ref(value);\n      } else if (ref != null) {\n        (ref as React.MutableRefObject<T | null>).current = value;\n      }\n    });\n  };\n}\n"], "mappings": "SAEwBA,UACtBC,IAAA;EAEA,OAAO,UAACC,KAAD;IACLD,IAAI,CAACE,OAAL,CAAa,UAACC,GAAD;MACX,IAAI,OAAOA,GAAP,KAAe,UAAnB,EAA+B;QAC7BA,GAAG,CAACF,KAAD,CAAH;MACD,CAFD,MAEO,IAAIE,GAAG,IAAI,IAAX,EAAiB;QACrBA,GAAwC,CAACC,OAAzC,GAAmDH,KAAnD;MACF;IACF,CAND;EAOD,CARD;AASD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}