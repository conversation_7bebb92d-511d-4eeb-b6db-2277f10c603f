{"version": 3, "file": "ShaderPass.cjs", "sources": ["../../src/postprocessing/ShaderPass.ts"], "sourcesContent": ["import { ShaderMaterial, UniformsUtils, WebG<PERSON>enderer, WebGLRenderTarget } from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { Defines, IShader, Uniforms } from '../shaders/types'\n\nclass ShaderPass extends Pass {\n  public textureID: string\n  public uniforms: Uniforms\n  public material: ShaderMaterial\n  public fsQuad: FullScreenQuad\n\n  constructor(shader: ShaderMaterial | IShader<Uniforms, Defines | undefined>, textureID = 'tDiffuse') {\n    super()\n\n    this.textureID = textureID\n\n    if (shader instanceof ShaderMaterial) {\n      this.uniforms = shader.uniforms\n\n      this.material = shader\n    } else {\n      this.uniforms = UniformsUtils.clone(shader.uniforms)\n\n      this.material = new ShaderMaterial({\n        defines: Object.assign({}, shader.defines),\n        uniforms: this.uniforms,\n        vertexShader: shader.vertexShader,\n        fragmentShader: shader.fragmentShader,\n      })\n    }\n\n    this.fsQuad = new FullScreenQuad(this.material)\n  }\n\n  public render(\n    renderer: <PERSON><PERSON><PERSON>enderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget /*, deltaTime, maskActive */,\n  ): void {\n    if (this.uniforms[this.textureID]) {\n      this.uniforms[this.textureID].value = readBuffer.texture\n    }\n\n    this.fsQuad.material = this.material\n\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n      // TODO: Avoid using autoClear properties, see https://github.com/mrdoob/three.js/pull/15571#issuecomment-465669600\n      if (this.clear) renderer.clear(renderer.autoClearColor, renderer.autoClearDepth, renderer.autoClearStencil)\n      this.fsQuad.render(renderer)\n    }\n  }\n\n  public dispose() {\n    this.fsQuad.dispose()\n    this.material.dispose()\n  }\n}\n\nexport { ShaderPass }\n"], "names": ["Pass", "ShaderMaterial", "UniformsUtils", "FullScreenQuad"], "mappings": ";;;;;;;;;;AAIA,MAAM,mBAAmBA,KAAAA,KAAK;AAAA,EAM5B,YAAY,QAAiE,YAAY,YAAY;AAC7F;AAND;AACA;AACA;AACA;AAKL,SAAK,YAAY;AAEjB,QAAI,kBAAkBC,MAAAA,gBAAgB;AACpC,WAAK,WAAW,OAAO;AAEvB,WAAK,WAAW;AAAA,IAAA,OACX;AACL,WAAK,WAAWC,MAAA,cAAc,MAAM,OAAO,QAAQ;AAE9C,WAAA,WAAW,IAAID,qBAAe;AAAA,QACjC,SAAS,OAAO,OAAO,CAAA,GAAI,OAAO,OAAO;AAAA,QACzC,UAAU,KAAK;AAAA,QACf,cAAc,OAAO;AAAA,QACrB,gBAAgB,OAAO;AAAA,MAAA,CACxB;AAAA,IACH;AAEA,SAAK,SAAS,IAAIE,KAAe,eAAA,KAAK,QAAQ;AAAA,EAChD;AAAA,EAEO,OACL,UACA,aACA,YACM;AACN,QAAI,KAAK,SAAS,KAAK,SAAS,GAAG;AACjC,WAAK,SAAS,KAAK,SAAS,EAAE,QAAQ,WAAW;AAAA,IACnD;AAEK,SAAA,OAAO,WAAW,KAAK;AAE5B,QAAI,KAAK,gBAAgB;AACvB,eAAS,gBAAgB,IAAI;AACxB,WAAA,OAAO,OAAO,QAAQ;AAAA,IAAA,OACtB;AACL,eAAS,gBAAgB,WAAW;AAEpC,UAAI,KAAK;AAAO,iBAAS,MAAM,SAAS,gBAAgB,SAAS,gBAAgB,SAAS,gBAAgB;AACrG,WAAA,OAAO,OAAO,QAAQ;AAAA,IAC7B;AAAA,EACF;AAAA,EAEO,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,SAAS;EAChB;AACF;;"}