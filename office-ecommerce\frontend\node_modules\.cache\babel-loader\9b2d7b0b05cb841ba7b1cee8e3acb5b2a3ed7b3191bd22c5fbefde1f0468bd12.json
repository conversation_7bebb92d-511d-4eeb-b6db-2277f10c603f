{"ast": null, "code": "import { DataTexture, FloatType, IntType, UnsignedIntType, ByteType, UnsignedByteType, ShortType, UnsignedShortType, RedFormat, RGFormat, RGBAFormat, RedIntegerFormat, RGIntegerFormat, RGBAIntegerFormat, NearestFilter } from 'three';\nfunction countToStringFormat(count) {\n  switch (count) {\n    case 1:\n      return 'R';\n    case 2:\n      return 'RG';\n    case 3:\n      return 'RGBA';\n    case 4:\n      return 'RGBA';\n  }\n  throw new Error();\n}\nfunction countToFormat(count) {\n  switch (count) {\n    case 1:\n      return RedFormat;\n    case 2:\n      return RGFormat;\n    case 3:\n      return RGBAFormat;\n    case 4:\n      return RGBAFormat;\n  }\n}\nfunction countToIntFormat(count) {\n  switch (count) {\n    case 1:\n      return RedIntegerFormat;\n    case 2:\n      return RGIntegerFormat;\n    case 3:\n      return RGBAIntegerFormat;\n    case 4:\n      return RGBAIntegerFormat;\n  }\n}\nexport class VertexAttributeTexture extends DataTexture {\n  constructor() {\n    super();\n    this.minFilter = NearestFilter;\n    this.magFilter = NearestFilter;\n    this.generateMipmaps = false;\n    this.overrideItemSize = null;\n    this._forcedType = null;\n  }\n  updateFrom(attr) {\n    const overrideItemSize = this.overrideItemSize;\n    const originalItemSize = attr.itemSize;\n    const originalCount = attr.count;\n    if (overrideItemSize !== null) {\n      if (originalItemSize * originalCount % overrideItemSize !== 0.0) {\n        throw new Error('VertexAttributeTexture: overrideItemSize must divide evenly into buffer length.');\n      }\n      attr.itemSize = overrideItemSize;\n      attr.count = originalCount * originalItemSize / overrideItemSize;\n    }\n    const itemSize = attr.itemSize;\n    const count = attr.count;\n    const normalized = attr.normalized;\n    const originalBufferCons = attr.array.constructor;\n    const byteCount = originalBufferCons.BYTES_PER_ELEMENT;\n    let targetType = this._forcedType;\n    let finalStride = itemSize;\n\n    // derive the type of texture this should be in the shader\n    if (targetType === null) {\n      switch (originalBufferCons) {\n        case Float32Array:\n          targetType = FloatType;\n          break;\n        case Uint8Array:\n        case Uint16Array:\n        case Uint32Array:\n          targetType = UnsignedIntType;\n          break;\n        case Int8Array:\n        case Int16Array:\n        case Int32Array:\n          targetType = IntType;\n          break;\n      }\n    }\n\n    // get the target format to store the texture as\n    let type, format, normalizeValue, targetBufferCons;\n    let internalFormat = countToStringFormat(itemSize);\n    switch (targetType) {\n      case FloatType:\n        normalizeValue = 1.0;\n        format = countToFormat(itemSize);\n        if (normalized && byteCount === 1) {\n          targetBufferCons = originalBufferCons;\n          internalFormat += '8';\n          if (originalBufferCons === Uint8Array) {\n            type = UnsignedByteType;\n          } else {\n            type = ByteType;\n            internalFormat += '_SNORM';\n          }\n        } else {\n          targetBufferCons = Float32Array;\n          internalFormat += '32F';\n          type = FloatType;\n        }\n        break;\n      case IntType:\n        internalFormat += byteCount * 8 + 'I';\n        normalizeValue = normalized ? Math.pow(2, originalBufferCons.BYTES_PER_ELEMENT * 8 - 1) : 1.0;\n        format = countToIntFormat(itemSize);\n        if (byteCount === 1) {\n          targetBufferCons = Int8Array;\n          type = ByteType;\n        } else if (byteCount === 2) {\n          targetBufferCons = Int16Array;\n          type = ShortType;\n        } else {\n          targetBufferCons = Int32Array;\n          type = IntType;\n        }\n        break;\n      case UnsignedIntType:\n        internalFormat += byteCount * 8 + 'UI';\n        normalizeValue = normalized ? Math.pow(2, originalBufferCons.BYTES_PER_ELEMENT * 8 - 1) : 1.0;\n        format = countToIntFormat(itemSize);\n        if (byteCount === 1) {\n          targetBufferCons = Uint8Array;\n          type = UnsignedByteType;\n        } else if (byteCount === 2) {\n          targetBufferCons = Uint16Array;\n          type = UnsignedShortType;\n        } else {\n          targetBufferCons = Uint32Array;\n          type = UnsignedIntType;\n        }\n        break;\n    }\n\n    // there will be a mismatch between format length and final length because\n    // RGBFormat and RGBIntegerFormat was removed\n    if (finalStride === 3 && (format === RGBAFormat || format === RGBAIntegerFormat)) {\n      finalStride = 4;\n    }\n\n    // copy the data over to the new texture array\n    const dimension = Math.ceil(Math.sqrt(count));\n    const length = finalStride * dimension * dimension;\n    const dataArray = new targetBufferCons(length);\n\n    // temporarily set the normalized state to false since we have custom normalization logic\n    const originalNormalized = attr.normalized;\n    attr.normalized = false;\n    for (let i = 0; i < count; i++) {\n      const ii = finalStride * i;\n      dataArray[ii] = attr.getX(i) / normalizeValue;\n      if (itemSize >= 2) {\n        dataArray[ii + 1] = attr.getY(i) / normalizeValue;\n      }\n      if (itemSize >= 3) {\n        dataArray[ii + 2] = attr.getZ(i) / normalizeValue;\n        if (finalStride === 4) {\n          dataArray[ii + 3] = 1.0;\n        }\n      }\n      if (itemSize >= 4) {\n        dataArray[ii + 3] = attr.getW(i) / normalizeValue;\n      }\n    }\n    attr.normalized = originalNormalized;\n    this.internalFormat = internalFormat;\n    this.format = format;\n    this.type = type;\n    this.image.width = dimension;\n    this.image.height = dimension;\n    this.image.data = dataArray;\n    this.needsUpdate = true;\n    this.dispose();\n    attr.itemSize = originalItemSize;\n    attr.count = originalCount;\n  }\n}\nexport class UIntVertexAttributeTexture extends VertexAttributeTexture {\n  constructor() {\n    super();\n    this._forcedType = UnsignedIntType;\n  }\n}\nexport class IntVertexAttributeTexture extends VertexAttributeTexture {\n  constructor() {\n    super();\n    this._forcedType = IntType;\n  }\n}\nexport class FloatVertexAttributeTexture extends VertexAttributeTexture {\n  constructor() {\n    super();\n    this._forcedType = FloatType;\n  }\n}", "map": {"version": 3, "names": ["DataTexture", "FloatType", "IntType", "UnsignedIntType", "ByteType", "UnsignedByteType", "ShortType", "UnsignedShortType", "RedFormat", "RGFormat", "RGBAFormat", "RedIntegerFormat", "RGIntegerFormat", "RGBAIntegerFormat", "NearestFilter", "countToStringFormat", "count", "Error", "countToFormat", "countToIntFormat", "VertexAttributeTexture", "constructor", "minFilter", "magFilter", "generateMipmaps", "overrideItemSize", "_forcedType", "updateFrom", "attr", "originalItemSize", "itemSize", "originalCount", "normalized", "originalBufferCons", "array", "byteCount", "BYTES_PER_ELEMENT", "targetType", "finalStride", "Float32Array", "Uint8Array", "Uint16Array", "Uint32Array", "Int8Array", "Int16Array", "Int32Array", "type", "format", "normalizeValue", "targetBufferCons", "internalFormat", "Math", "pow", "dimension", "ceil", "sqrt", "length", "dataArray", "originalNormalized", "i", "ii", "getX", "getY", "getZ", "getW", "image", "width", "height", "data", "needsUpdate", "dispose", "UIntVertexAttributeTexture", "IntVertexAttributeTexture", "FloatVertexAttributeTexture"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/three-mesh-bvh/src/gpu/VertexAttributeTexture.js"], "sourcesContent": ["import {\n\tDataTexture,\n\tFloatType,\n\tIntType,\n\tUnsignedIntType,\n\tByteType,\n\tUnsignedByteType,\n\tShortType,\n\tUnsignedShortType,\n\n\tRedFormat,\n\tRGFormat,\n\tRGBAFormat,\n\n\tRedIntegerFormat,\n\tRGIntegerFormat,\n\tRGBAIntegerFormat,\n\n\tNearestFilter,\n} from 'three';\n\nfunction countToStringFormat( count ) {\n\n\tswitch ( count ) {\n\n\t\tcase 1: return 'R';\n\t\tcase 2: return 'RG';\n\t\tcase 3: return 'RGBA';\n\t\tcase 4: return 'RGBA';\n\n\t}\n\n\tthrow new Error();\n\n}\n\nfunction countToFormat( count ) {\n\n\tswitch ( count ) {\n\n\t\tcase 1: return RedFormat;\n\t\tcase 2: return RGFormat;\n\t\tcase 3: return RGBAFormat;\n\t\tcase 4: return RGBAFormat;\n\n\t}\n\n}\n\nfunction countToIntFormat( count ) {\n\n\tswitch ( count ) {\n\n\t\tcase 1: return RedIntegerFormat;\n\t\tcase 2: return RGIntegerFormat;\n\t\tcase 3: return RGBAIntegerFormat;\n\t\tcase 4: return RGBAIntegerFormat;\n\n\t}\n\n}\n\nexport class VertexAttributeTexture extends DataTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis.minFilter = NearestFilter;\n\t\tthis.magFilter = NearestFilter;\n\t\tthis.generateMipmaps = false;\n\t\tthis.overrideItemSize = null;\n\t\tthis._forcedType = null;\n\n\t}\n\n\tupdateFrom( attr ) {\n\n\t\tconst overrideItemSize = this.overrideItemSize;\n\t\tconst originalItemSize = attr.itemSize;\n\t\tconst originalCount = attr.count;\n\t\tif ( overrideItemSize !== null ) {\n\n\t\t\tif ( ( originalItemSize * originalCount ) % overrideItemSize !== 0.0 ) {\n\n\t\t\t\tthrow new Error( 'VertexAttributeTexture: overrideItemSize must divide evenly into buffer length.' );\n\n\t\t\t}\n\n\t\t\tattr.itemSize = overrideItemSize;\n\t\t\tattr.count = originalCount * originalItemSize / overrideItemSize;\n\n\t\t}\n\n\t\tconst itemSize = attr.itemSize;\n\t\tconst count = attr.count;\n\t\tconst normalized = attr.normalized;\n\t\tconst originalBufferCons = attr.array.constructor;\n\t\tconst byteCount = originalBufferCons.BYTES_PER_ELEMENT;\n\t\tlet targetType = this._forcedType;\n\t\tlet finalStride = itemSize;\n\n\t\t// derive the type of texture this should be in the shader\n\t\tif ( targetType === null ) {\n\n\t\t\tswitch ( originalBufferCons ) {\n\n\t\t\t\tcase Float32Array:\n\t\t\t\t\ttargetType = FloatType;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase Uint8Array:\n\t\t\t\tcase Uint16Array:\n\t\t\t\tcase Uint32Array:\n\t\t\t\t\ttargetType = UnsignedIntType;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase Int8Array:\n\t\t\t\tcase Int16Array:\n\t\t\t\tcase Int32Array:\n\t\t\t\t\ttargetType = IntType;\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// get the target format to store the texture as\n\t\tlet type, format, normalizeValue, targetBufferCons;\n\t\tlet internalFormat = countToStringFormat( itemSize );\n\t\tswitch ( targetType ) {\n\n\t\t\tcase FloatType:\n\t\t\t\tnormalizeValue = 1.0;\n\t\t\t\tformat = countToFormat( itemSize );\n\n\t\t\t\tif ( normalized && byteCount === 1 ) {\n\n\t\t\t\t\ttargetBufferCons = originalBufferCons;\n\t\t\t\t\tinternalFormat += '8';\n\n\t\t\t\t\tif ( originalBufferCons === Uint8Array ) {\n\n\t\t\t\t\t\ttype = UnsignedByteType;\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\ttype = ByteType;\n\t\t\t\t\t\tinternalFormat += '_SNORM';\n\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttargetBufferCons = Float32Array;\n\t\t\t\t\tinternalFormat += '32F';\n\t\t\t\t\ttype = FloatType;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t\tcase IntType:\n\t\t\t\tinternalFormat += byteCount * 8 + 'I';\n\t\t\t\tnormalizeValue = normalized ? Math.pow( 2, originalBufferCons.BYTES_PER_ELEMENT * 8 - 1 ) : 1.0;\n\t\t\t\tformat = countToIntFormat( itemSize );\n\n\t\t\t\tif ( byteCount === 1 ) {\n\n\t\t\t\t\ttargetBufferCons = Int8Array;\n\t\t\t\t\ttype = ByteType;\n\n\t\t\t\t} else if ( byteCount === 2 ) {\n\n\t\t\t\t\ttargetBufferCons = Int16Array;\n\t\t\t\t\ttype = ShortType;\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttargetBufferCons = Int32Array;\n\t\t\t\t\ttype = IntType;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t\tcase UnsignedIntType:\n\t\t\t\tinternalFormat += byteCount * 8 + 'UI';\n\t\t\t\tnormalizeValue = normalized ? Math.pow( 2, originalBufferCons.BYTES_PER_ELEMENT * 8 - 1 ) : 1.0;\n\t\t\t\tformat = countToIntFormat( itemSize );\n\n\t\t\t\tif ( byteCount === 1 ) {\n\n\t\t\t\t\ttargetBufferCons = Uint8Array;\n\t\t\t\t\ttype = UnsignedByteType;\n\n\t\t\t\t} else if ( byteCount === 2 ) {\n\n\t\t\t\t\ttargetBufferCons = Uint16Array;\n\t\t\t\t\ttype = UnsignedShortType;\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttargetBufferCons = Uint32Array;\n\t\t\t\t\ttype = UnsignedIntType;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\t// there will be a mismatch between format length and final length because\n\t\t// RGBFormat and RGBIntegerFormat was removed\n\t\tif ( finalStride === 3 && ( format === RGBAFormat || format === RGBAIntegerFormat ) ) {\n\n\t\t\tfinalStride = 4;\n\n\t\t}\n\n\t\t// copy the data over to the new texture array\n\t\tconst dimension = Math.ceil( Math.sqrt( count ) );\n\t\tconst length = finalStride * dimension * dimension;\n\t\tconst dataArray = new targetBufferCons( length );\n\n\t\t// temporarily set the normalized state to false since we have custom normalization logic\n\t\tconst originalNormalized = attr.normalized;\n\t\tattr.normalized = false;\n\t\tfor ( let i = 0; i < count; i ++ ) {\n\n\t\t\tconst ii = finalStride * i;\n\t\t\tdataArray[ ii ] = attr.getX( i ) / normalizeValue;\n\n\t\t\tif ( itemSize >= 2 ) {\n\n\t\t\t\tdataArray[ ii + 1 ] = attr.getY( i ) / normalizeValue;\n\n\t\t\t}\n\n\t\t\tif ( itemSize >= 3 ) {\n\n\t\t\t\tdataArray[ ii + 2 ] = attr.getZ( i ) / normalizeValue;\n\n\t\t\t\tif ( finalStride === 4 ) {\n\n\t\t\t\t\tdataArray[ ii + 3 ] = 1.0;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( itemSize >= 4 ) {\n\n\t\t\t\tdataArray[ ii + 3 ] = attr.getW( i ) / normalizeValue;\n\n\t\t\t}\n\n\t\t}\n\n\t\tattr.normalized = originalNormalized;\n\n\t\tthis.internalFormat = internalFormat;\n\t\tthis.format = format;\n\t\tthis.type = type;\n\t\tthis.image.width = dimension;\n\t\tthis.image.height = dimension;\n\t\tthis.image.data = dataArray;\n\t\tthis.needsUpdate = true;\n\t\tthis.dispose();\n\n\t\tattr.itemSize = originalItemSize;\n\t\tattr.count = originalCount;\n\n\t}\n\n}\n\nexport class UIntVertexAttributeTexture extends VertexAttributeTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis._forcedType = UnsignedIntType;\n\n\t}\n\n}\n\nexport class IntVertexAttributeTexture extends VertexAttributeTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis._forcedType = IntType;\n\n\t}\n\n\n}\n\nexport class FloatVertexAttributeTexture extends VertexAttributeTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis._forcedType = FloatType;\n\n\t}\n\n}\n"], "mappings": "AAAA,SACCA,WAAW,EACXC,SAAS,EACTC,OAAO,EACPC,eAAe,EACfC,QAAQ,EACRC,gBAAgB,EAChBC,SAAS,EACTC,iBAAiB,EAEjBC,SAAS,EACTC,QAAQ,EACRC,UAAU,EAEVC,gBAAgB,EAChBC,eAAe,EACfC,iBAAiB,EAEjBC,aAAa,QACP,OAAO;AAEd,SAASC,mBAAmBA,CAAEC,KAAK,EAAG;EAErC,QAASA,KAAK;IAEb,KAAK,CAAC;MAAE,OAAO,GAAG;IAClB,KAAK,CAAC;MAAE,OAAO,IAAI;IACnB,KAAK,CAAC;MAAE,OAAO,MAAM;IACrB,KAAK,CAAC;MAAE,OAAO,MAAM;EAEtB;EAEA,MAAM,IAAIC,KAAK,CAAC,CAAC;AAElB;AAEA,SAASC,aAAaA,CAAEF,KAAK,EAAG;EAE/B,QAASA,KAAK;IAEb,KAAK,CAAC;MAAE,OAAOR,SAAS;IACxB,KAAK,CAAC;MAAE,OAAOC,QAAQ;IACvB,KAAK,CAAC;MAAE,OAAOC,UAAU;IACzB,KAAK,CAAC;MAAE,OAAOA,UAAU;EAE1B;AAED;AAEA,SAASS,gBAAgBA,CAAEH,KAAK,EAAG;EAElC,QAASA,KAAK;IAEb,KAAK,CAAC;MAAE,OAAOL,gBAAgB;IAC/B,KAAK,CAAC;MAAE,OAAOC,eAAe;IAC9B,KAAK,CAAC;MAAE,OAAOC,iBAAiB;IAChC,KAAK,CAAC;MAAE,OAAOA,iBAAiB;EAEjC;AAED;AAEA,OAAO,MAAMO,sBAAsB,SAASpB,WAAW,CAAC;EAEvDqB,WAAWA,CAAA,EAAG;IAEb,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,SAAS,GAAGR,aAAa;IAC9B,IAAI,CAACS,SAAS,GAAGT,aAAa;IAC9B,IAAI,CAACU,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,WAAW,GAAG,IAAI;EAExB;EAEAC,UAAUA,CAAEC,IAAI,EAAG;IAElB,MAAMH,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9C,MAAMI,gBAAgB,GAAGD,IAAI,CAACE,QAAQ;IACtC,MAAMC,aAAa,GAAGH,IAAI,CAACZ,KAAK;IAChC,IAAKS,gBAAgB,KAAK,IAAI,EAAG;MAEhC,IAAOI,gBAAgB,GAAGE,aAAa,GAAKN,gBAAgB,KAAK,GAAG,EAAG;QAEtE,MAAM,IAAIR,KAAK,CAAE,iFAAkF,CAAC;MAErG;MAEAW,IAAI,CAACE,QAAQ,GAAGL,gBAAgB;MAChCG,IAAI,CAACZ,KAAK,GAAGe,aAAa,GAAGF,gBAAgB,GAAGJ,gBAAgB;IAEjE;IAEA,MAAMK,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IAC9B,MAAMd,KAAK,GAAGY,IAAI,CAACZ,KAAK;IACxB,MAAMgB,UAAU,GAAGJ,IAAI,CAACI,UAAU;IAClC,MAAMC,kBAAkB,GAAGL,IAAI,CAACM,KAAK,CAACb,WAAW;IACjD,MAAMc,SAAS,GAAGF,kBAAkB,CAACG,iBAAiB;IACtD,IAAIC,UAAU,GAAG,IAAI,CAACX,WAAW;IACjC,IAAIY,WAAW,GAAGR,QAAQ;;IAE1B;IACA,IAAKO,UAAU,KAAK,IAAI,EAAG;MAE1B,QAASJ,kBAAkB;QAE1B,KAAKM,YAAY;UAChBF,UAAU,GAAGpC,SAAS;UACtB;QAED,KAAKuC,UAAU;QACf,KAAKC,WAAW;QAChB,KAAKC,WAAW;UACfL,UAAU,GAAGlC,eAAe;UAC5B;QAED,KAAKwC,SAAS;QACd,KAAKC,UAAU;QACf,KAAKC,UAAU;UACdR,UAAU,GAAGnC,OAAO;UACpB;MAEF;IAED;;IAEA;IACA,IAAI4C,IAAI,EAAEC,MAAM,EAAEC,cAAc,EAAEC,gBAAgB;IAClD,IAAIC,cAAc,GAAGnC,mBAAmB,CAAEe,QAAS,CAAC;IACpD,QAASO,UAAU;MAElB,KAAKpC,SAAS;QACb+C,cAAc,GAAG,GAAG;QACpBD,MAAM,GAAG7B,aAAa,CAAEY,QAAS,CAAC;QAElC,IAAKE,UAAU,IAAIG,SAAS,KAAK,CAAC,EAAG;UAEpCc,gBAAgB,GAAGhB,kBAAkB;UACrCiB,cAAc,IAAI,GAAG;UAErB,IAAKjB,kBAAkB,KAAKO,UAAU,EAAG;YAExCM,IAAI,GAAGzC,gBAAgB;UAExB,CAAC,MAAM;YAENyC,IAAI,GAAG1C,QAAQ;YACf8C,cAAc,IAAI,QAAQ;UAE3B;QAED,CAAC,MAAM;UAEND,gBAAgB,GAAGV,YAAY;UAC/BW,cAAc,IAAI,KAAK;UACvBJ,IAAI,GAAG7C,SAAS;QAEjB;QAEA;MAED,KAAKC,OAAO;QACXgD,cAAc,IAAIf,SAAS,GAAG,CAAC,GAAG,GAAG;QACrCa,cAAc,GAAGhB,UAAU,GAAGmB,IAAI,CAACC,GAAG,CAAE,CAAC,EAAEnB,kBAAkB,CAACG,iBAAiB,GAAG,CAAC,GAAG,CAAE,CAAC,GAAG,GAAG;QAC/FW,MAAM,GAAG5B,gBAAgB,CAAEW,QAAS,CAAC;QAErC,IAAKK,SAAS,KAAK,CAAC,EAAG;UAEtBc,gBAAgB,GAAGN,SAAS;UAC5BG,IAAI,GAAG1C,QAAQ;QAEhB,CAAC,MAAM,IAAK+B,SAAS,KAAK,CAAC,EAAG;UAE7Bc,gBAAgB,GAAGL,UAAU;UAC7BE,IAAI,GAAGxC,SAAS;QAEjB,CAAC,MAAM;UAEN2C,gBAAgB,GAAGJ,UAAU;UAC7BC,IAAI,GAAG5C,OAAO;QAEf;QAEA;MAED,KAAKC,eAAe;QACnB+C,cAAc,IAAIf,SAAS,GAAG,CAAC,GAAG,IAAI;QACtCa,cAAc,GAAGhB,UAAU,GAAGmB,IAAI,CAACC,GAAG,CAAE,CAAC,EAAEnB,kBAAkB,CAACG,iBAAiB,GAAG,CAAC,GAAG,CAAE,CAAC,GAAG,GAAG;QAC/FW,MAAM,GAAG5B,gBAAgB,CAAEW,QAAS,CAAC;QAErC,IAAKK,SAAS,KAAK,CAAC,EAAG;UAEtBc,gBAAgB,GAAGT,UAAU;UAC7BM,IAAI,GAAGzC,gBAAgB;QAExB,CAAC,MAAM,IAAK8B,SAAS,KAAK,CAAC,EAAG;UAE7Bc,gBAAgB,GAAGR,WAAW;UAC9BK,IAAI,GAAGvC,iBAAiB;QAEzB,CAAC,MAAM;UAEN0C,gBAAgB,GAAGP,WAAW;UAC9BI,IAAI,GAAG3C,eAAe;QAEvB;QAEA;IAEF;;IAEA;IACA;IACA,IAAKmC,WAAW,KAAK,CAAC,KAAMS,MAAM,KAAKrC,UAAU,IAAIqC,MAAM,KAAKlC,iBAAiB,CAAE,EAAG;MAErFyB,WAAW,GAAG,CAAC;IAEhB;;IAEA;IACA,MAAMe,SAAS,GAAGF,IAAI,CAACG,IAAI,CAAEH,IAAI,CAACI,IAAI,CAAEvC,KAAM,CAAE,CAAC;IACjD,MAAMwC,MAAM,GAAGlB,WAAW,GAAGe,SAAS,GAAGA,SAAS;IAClD,MAAMI,SAAS,GAAG,IAAIR,gBAAgB,CAAEO,MAAO,CAAC;;IAEhD;IACA,MAAME,kBAAkB,GAAG9B,IAAI,CAACI,UAAU;IAC1CJ,IAAI,CAACI,UAAU,GAAG,KAAK;IACvB,KAAM,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3C,KAAK,EAAE2C,CAAC,EAAG,EAAG;MAElC,MAAMC,EAAE,GAAGtB,WAAW,GAAGqB,CAAC;MAC1BF,SAAS,CAAEG,EAAE,CAAE,GAAGhC,IAAI,CAACiC,IAAI,CAAEF,CAAE,CAAC,GAAGX,cAAc;MAEjD,IAAKlB,QAAQ,IAAI,CAAC,EAAG;QAEpB2B,SAAS,CAAEG,EAAE,GAAG,CAAC,CAAE,GAAGhC,IAAI,CAACkC,IAAI,CAAEH,CAAE,CAAC,GAAGX,cAAc;MAEtD;MAEA,IAAKlB,QAAQ,IAAI,CAAC,EAAG;QAEpB2B,SAAS,CAAEG,EAAE,GAAG,CAAC,CAAE,GAAGhC,IAAI,CAACmC,IAAI,CAAEJ,CAAE,CAAC,GAAGX,cAAc;QAErD,IAAKV,WAAW,KAAK,CAAC,EAAG;UAExBmB,SAAS,CAAEG,EAAE,GAAG,CAAC,CAAE,GAAG,GAAG;QAE1B;MAED;MAEA,IAAK9B,QAAQ,IAAI,CAAC,EAAG;QAEpB2B,SAAS,CAAEG,EAAE,GAAG,CAAC,CAAE,GAAGhC,IAAI,CAACoC,IAAI,CAAEL,CAAE,CAAC,GAAGX,cAAc;MAEtD;IAED;IAEApB,IAAI,CAACI,UAAU,GAAG0B,kBAAkB;IAEpC,IAAI,CAACR,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACH,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACmB,KAAK,CAACC,KAAK,GAAGb,SAAS;IAC5B,IAAI,CAACY,KAAK,CAACE,MAAM,GAAGd,SAAS;IAC7B,IAAI,CAACY,KAAK,CAACG,IAAI,GAAGX,SAAS;IAC3B,IAAI,CAACY,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,OAAO,CAAC,CAAC;IAEd1C,IAAI,CAACE,QAAQ,GAAGD,gBAAgB;IAChCD,IAAI,CAACZ,KAAK,GAAGe,aAAa;EAE3B;AAED;AAEA,OAAO,MAAMwC,0BAA0B,SAASnD,sBAAsB,CAAC;EAEtEC,WAAWA,CAAA,EAAG;IAEb,KAAK,CAAC,CAAC;IACP,IAAI,CAACK,WAAW,GAAGvB,eAAe;EAEnC;AAED;AAEA,OAAO,MAAMqE,yBAAyB,SAASpD,sBAAsB,CAAC;EAErEC,WAAWA,CAAA,EAAG;IAEb,KAAK,CAAC,CAAC;IACP,IAAI,CAACK,WAAW,GAAGxB,OAAO;EAE3B;AAGD;AAEA,OAAO,MAAMuE,2BAA2B,SAASrD,sBAAsB,CAAC;EAEvEC,WAAWA,CAAA,EAAG;IAEb,KAAK,CAAC,CAAC;IACP,IAAI,CAACK,WAAW,GAAGzB,SAAS;EAE7B;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}