{"version": 3, "file": "index.module.js", "sources": ["../src/core/Constants.js", "../src/core/MeshBVHNode.js", "../src/utils/ArrayBoxUtilities.js", "../src/core/buildFunctions.js", "../src/math/SeparatingAxisBounds.js", "../src/math/MathUtilities.js", "../src/math/ExtendedTriangle.js", "../src/math/OrientedBox.js", "../src/utils/ThreeRayIntersectUtilities.js", "../src/utils/GeometryRayIntersectUtilities.js", "../src/utils/TriangleUtilities.js", "../src/utils/PrimitivePool.js", "../src/core/nodeBufferFunctions.js", "../src/core/castFunctions.js", "../src/core/MeshBVH.js", "../src/objects/MeshBVHVisualizer.js", "../src/debug/Debug.js", "../src/utils/ExtensionUtilities.js", "../src/gpu/VertexAttributeTexture.js", "../src/gpu/MeshBVHUniformStruct.js", "../src/gpu/shaderFunctions.js", "../src/utils/StaticGeometryGenerator.js"], "sourcesContent": ["// Split strategy constants\nexport const CENTER = 0;\nexport const AVERAGE = 1;\nexport const SAH = 2;\n\n// Traversal constants\nexport const NOT_INTERSECTED = 0;\nexport const INTERSECTED = 1;\nexport const CONTAINED = 2;\n\n// SAH cost constants\n// TODO: hone these costs more. The relative difference between them should be the\n// difference in measured time to perform a triangle intersection vs traversing\n// bounds.\nexport const TRIANGLE_INTERSECT_COST = 1.25;\nexport const TRAVERSAL_COST = 1;\n\n\n// Build constants\nexport const BYTES_PER_NODE = 6 * 4 + 4 + 4;\nexport const IS_LEAFNODE_FLAG = 0xFFFF;\n\n// EPSILON for computing floating point error during build\n// https://en.wikipedia.org/wiki/Machine_epsilon#Values_for_standard_hardware_floating_point_arithmetics\nexport const FLOAT32_EPSILON = Math.pow( 2, - 24 );\n\n", "export class MeshBVHNode {\n\n\tconstructor() {\n\n\t\t// internal nodes have boundingData, left, right, and splitAxis\n\t\t// leaf nodes have offset and count (referring to primitives in the mesh geometry)\n\n\t}\n\n}\n", "export function arrayToBox( nodeIndex32, array, target ) {\n\n\ttarget.min.x = array[ nodeIndex32 ];\n\ttarget.min.y = array[ nodeIndex32 + 1 ];\n\ttarget.min.z = array[ nodeIndex32 + 2 ];\n\n\ttarget.max.x = array[ nodeIndex32 + 3 ];\n\ttarget.max.y = array[ nodeIndex32 + 4 ];\n\ttarget.max.z = array[ nodeIndex32 + 5 ];\n\n\treturn target;\n\n}\n\nexport function getLongestEdgeIndex( bounds ) {\n\n\tlet splitDimIdx = - 1;\n\tlet splitDist = - Infinity;\n\n\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\tconst dist = bounds[ i + 3 ] - bounds[ i ];\n\t\tif ( dist > splitDist ) {\n\n\t\t\tsplitDist = dist;\n\t\t\tsplitDimIdx = i;\n\n\t\t}\n\n\t}\n\n\treturn splitDimIdx;\n\n}\n\n// copies bounds a into bounds b\nexport function copyBounds( source, target ) {\n\n\ttarget.set( source );\n\n}\n\n// sets bounds target to the union of bounds a and b\nexport function unionBounds( a, b, target ) {\n\n\tlet aVal, bVal;\n\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\tconst d3 = d + 3;\n\n\t\t// set the minimum values\n\t\taVal = a[ d ];\n\t\tbVal = b[ d ];\n\t\ttarget[ d ] = aVal < bVal ? aVal : bVal;\n\n\t\t// set the max values\n\t\taVal = a[ d3 ];\n\t\tbVal = b[ d3 ];\n\t\ttarget[ d3 ] = aVal > bVal ? aVal : bVal;\n\n\t}\n\n}\n\n// expands the given bounds by the provided triangle bounds\nexport function expandByTriangleBounds( startIndex, triangleBounds, bounds ) {\n\n\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\tconst tCenter = triangleBounds[ startIndex + 2 * d ];\n\t\tconst tHalf = triangleBounds[ startIndex + 2 * d + 1 ];\n\n\t\tconst tMin = tCenter - tHalf;\n\t\tconst tMax = tCenter + tHalf;\n\n\t\tif ( tMin < bounds[ d ] ) {\n\n\t\t\tbounds[ d ] = tMin;\n\n\t\t}\n\n\t\tif ( tMax > bounds[ d + 3 ] ) {\n\n\t\t\tbounds[ d + 3 ] = tMax;\n\n\t\t}\n\n\t}\n\n}\n\n// compute bounds surface area\nexport function computeSurfaceArea( bounds ) {\n\n\tconst d0 = bounds[ 3 ] - bounds[ 0 ];\n\tconst d1 = bounds[ 4 ] - bounds[ 1 ];\n\tconst d2 = bounds[ 5 ] - bounds[ 2 ];\n\n\treturn 2 * ( d0 * d1 + d1 * d2 + d2 * d0 );\n\n}\n", "import { BufferAttribute } from 'three';\nimport { MeshBVHNode } from './MeshBVHNode.js';\nimport { getLongestEdgeIndex, computeSurfaceArea, copyBounds, unionBounds, expandByTriangleBounds } from '../utils/ArrayBoxUtilities.js';\nimport {\n\tCENTER, AVERAGE, SAH, TRIANGLE_INTERSECT_COST, TRAVERSAL_COST,\n\tBYTES_PER_NODE, FLOAT32_EPSILON, IS_LEAFNODE_FLAG,\n} from './Constants.js';\n\nfunction ensureIndex( geo, options ) {\n\n\tif ( ! geo.index ) {\n\n\t\tconst vertexCount = geo.attributes.position.count;\n\t\tconst BufferConstructor = options.useSharedArrayBuffer ? SharedArrayBuffer : ArrayBuffer;\n\t\tlet index;\n\t\tif ( vertexCount > 65535 ) {\n\n\t\t\tindex = new Uint32Array( new BufferConstructor( 4 * vertexCount ) );\n\n\t\t} else {\n\n\t\t\tindex = new Uint16Array( new BufferConstructor( 2 * vertexCount ) );\n\n\t\t}\n\n\t\tgeo.setIndex( new BufferAttribute( index, 1 ) );\n\n\t\tfor ( let i = 0; i < vertexCount; i ++ ) {\n\n\t\t\tindex[ i ] = i;\n\n\t\t}\n\n\t}\n\n}\n\n// Computes the set of { offset, count } ranges which need independent BVH roots. Each\n// region in the geometry index that belongs to a different set of material groups requires\n// a separate BVH root, so that triangles indices belonging to one group never get swapped\n// with triangle indices belongs to another group. For example, if the groups were like this:\n//\n// [-------------------------------------------------------------]\n// |__________________|\n//   g0 = [0, 20]  |______________________||_____________________|\n//                      g1 = [16, 40]           g2 = [41, 60]\n//\n// we would need four BVH roots: [0, 15], [16, 20], [21, 40], [41, 60].\nfunction getRootIndexRanges( geo ) {\n\n\tif ( ! geo.groups || ! geo.groups.length ) {\n\n\t\treturn [ { offset: 0, count: geo.index.count / 3 } ];\n\n\t}\n\n\tconst ranges = [];\n\tconst rangeBoundaries = new Set();\n\tfor ( const group of geo.groups ) {\n\n\t\trangeBoundaries.add( group.start );\n\t\trangeBoundaries.add( group.start + group.count );\n\n\t}\n\n\t// note that if you don't pass in a comparator, it sorts them lexicographically as strings :-(\n\tconst sortedBoundaries = Array.from( rangeBoundaries.values() ).sort( ( a, b ) => a - b );\n\tfor ( let i = 0; i < sortedBoundaries.length - 1; i ++ ) {\n\n\t\tconst start = sortedBoundaries[ i ], end = sortedBoundaries[ i + 1 ];\n\t\tranges.push( { offset: ( start / 3 ), count: ( end - start ) / 3 } );\n\n\t}\n\n\treturn ranges;\n\n}\n\n// computes the union of the bounds of all of the given triangles and puts the resulting box in target. If\n// centroidTarget is provided then a bounding box is computed for the centroids of the triangles, as well.\n// These are computed together to avoid redundant accesses to bounds array.\nfunction getBounds( triangleBounds, offset, count, target, centroidTarget = null ) {\n\n\tlet minx = Infinity;\n\tlet miny = Infinity;\n\tlet minz = Infinity;\n\tlet maxx = - Infinity;\n\tlet maxy = - Infinity;\n\tlet maxz = - Infinity;\n\n\tlet cminx = Infinity;\n\tlet cminy = Infinity;\n\tlet cminz = Infinity;\n\tlet cmaxx = - Infinity;\n\tlet cmaxy = - Infinity;\n\tlet cmaxz = - Infinity;\n\n\tconst includeCentroid = centroidTarget !== null;\n\tfor ( let i = offset * 6, end = ( offset + count ) * 6; i < end; i += 6 ) {\n\n\t\tconst cx = triangleBounds[ i + 0 ];\n\t\tconst hx = triangleBounds[ i + 1 ];\n\t\tconst lx = cx - hx;\n\t\tconst rx = cx + hx;\n\t\tif ( lx < minx ) minx = lx;\n\t\tif ( rx > maxx ) maxx = rx;\n\t\tif ( includeCentroid && cx < cminx ) cminx = cx;\n\t\tif ( includeCentroid && cx > cmaxx ) cmaxx = cx;\n\n\t\tconst cy = triangleBounds[ i + 2 ];\n\t\tconst hy = triangleBounds[ i + 3 ];\n\t\tconst ly = cy - hy;\n\t\tconst ry = cy + hy;\n\t\tif ( ly < miny ) miny = ly;\n\t\tif ( ry > maxy ) maxy = ry;\n\t\tif ( includeCentroid && cy < cminy ) cminy = cy;\n\t\tif ( includeCentroid && cy > cmaxy ) cmaxy = cy;\n\n\t\tconst cz = triangleBounds[ i + 4 ];\n\t\tconst hz = triangleBounds[ i + 5 ];\n\t\tconst lz = cz - hz;\n\t\tconst rz = cz + hz;\n\t\tif ( lz < minz ) minz = lz;\n\t\tif ( rz > maxz ) maxz = rz;\n\t\tif ( includeCentroid && cz < cminz ) cminz = cz;\n\t\tif ( includeCentroid && cz > cmaxz ) cmaxz = cz;\n\n\t}\n\n\ttarget[ 0 ] = minx;\n\ttarget[ 1 ] = miny;\n\ttarget[ 2 ] = minz;\n\n\ttarget[ 3 ] = maxx;\n\ttarget[ 4 ] = maxy;\n\ttarget[ 5 ] = maxz;\n\n\tif ( includeCentroid ) {\n\n\t\tcentroidTarget[ 0 ] = cminx;\n\t\tcentroidTarget[ 1 ] = cminy;\n\t\tcentroidTarget[ 2 ] = cminz;\n\n\t\tcentroidTarget[ 3 ] = cmaxx;\n\t\tcentroidTarget[ 4 ] = cmaxy;\n\t\tcentroidTarget[ 5 ] = cmaxz;\n\n\t}\n\n}\n\n// A stand alone function for retrieving the centroid bounds.\nfunction getCentroidBounds( triangleBounds, offset, count, centroidTarget ) {\n\n\tlet cminx = Infinity;\n\tlet cminy = Infinity;\n\tlet cminz = Infinity;\n\tlet cmaxx = - Infinity;\n\tlet cmaxy = - Infinity;\n\tlet cmaxz = - Infinity;\n\n\tfor ( let i = offset * 6, end = ( offset + count ) * 6; i < end; i += 6 ) {\n\n\t\tconst cx = triangleBounds[ i + 0 ];\n\t\tif ( cx < cminx ) cminx = cx;\n\t\tif ( cx > cmaxx ) cmaxx = cx;\n\n\t\tconst cy = triangleBounds[ i + 2 ];\n\t\tif ( cy < cminy ) cminy = cy;\n\t\tif ( cy > cmaxy ) cmaxy = cy;\n\n\t\tconst cz = triangleBounds[ i + 4 ];\n\t\tif ( cz < cminz ) cminz = cz;\n\t\tif ( cz > cmaxz ) cmaxz = cz;\n\n\t}\n\n\tcentroidTarget[ 0 ] = cminx;\n\tcentroidTarget[ 1 ] = cminy;\n\tcentroidTarget[ 2 ] = cminz;\n\n\tcentroidTarget[ 3 ] = cmaxx;\n\tcentroidTarget[ 4 ] = cmaxy;\n\tcentroidTarget[ 5 ] = cmaxz;\n\n}\n\n\n// reorders `tris` such that for `count` elements after `offset`, elements on the left side of the split\n// will be on the left and elements on the right side of the split will be on the right. returns the index\n// of the first element on the right side, or offset + count if there are no elements on the right side.\nfunction partition( index, triangleBounds, offset, count, split ) {\n\n\tlet left = offset;\n\tlet right = offset + count - 1;\n\tconst pos = split.pos;\n\tconst axisOffset = split.axis * 2;\n\n\t// hoare partitioning, see e.g. https://en.wikipedia.org/wiki/Quicksort#Hoare_partition_scheme\n\twhile ( true ) {\n\n\t\twhile ( left <= right && triangleBounds[ left * 6 + axisOffset ] < pos ) {\n\n\t\t\tleft ++;\n\n\t\t}\n\n\n\t\t// if a triangle center lies on the partition plane it is considered to be on the right side\n\t\twhile ( left <= right && triangleBounds[ right * 6 + axisOffset ] >= pos ) {\n\n\t\t\tright --;\n\n\t\t}\n\n\t\tif ( left < right ) {\n\n\t\t\t// we need to swap all of the information associated with the triangles at index\n\t\t\t// left and right; that's the verts in the geometry index, the bounds,\n\t\t\t// and perhaps the SAH planes\n\n\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\tlet t0 = index[ left * 3 + i ];\n\t\t\t\tindex[ left * 3 + i ] = index[ right * 3 + i ];\n\t\t\t\tindex[ right * 3 + i ] = t0;\n\n\t\t\t\tlet t1 = triangleBounds[ left * 6 + i * 2 + 0 ];\n\t\t\t\ttriangleBounds[ left * 6 + i * 2 + 0 ] = triangleBounds[ right * 6 + i * 2 + 0 ];\n\t\t\t\ttriangleBounds[ right * 6 + i * 2 + 0 ] = t1;\n\n\t\t\t\tlet t2 = triangleBounds[ left * 6 + i * 2 + 1 ];\n\t\t\t\ttriangleBounds[ left * 6 + i * 2 + 1 ] = triangleBounds[ right * 6 + i * 2 + 1 ];\n\t\t\t\ttriangleBounds[ right * 6 + i * 2 + 1 ] = t2;\n\n\t\t\t}\n\n\t\t\tleft ++;\n\t\t\tright --;\n\n\t\t} else {\n\n\t\t\treturn left;\n\n\t\t}\n\n\t}\n\n}\n\nconst BIN_COUNT = 32;\nconst binsSort = ( a, b ) => a.candidate - b.candidate;\nconst sahBins = new Array( BIN_COUNT ).fill().map( () => {\n\n\treturn {\n\n\t\tcount: 0,\n\t\tbounds: new Float32Array( 6 ),\n\t\trightCacheBounds: new Float32Array( 6 ),\n\t\tleftCacheBounds: new Float32Array( 6 ),\n\t\tcandidate: 0,\n\n\t};\n\n} );\nconst leftBounds = new Float32Array( 6 );\n\nfunction getOptimalSplit( nodeBoundingData, centroidBoundingData, triangleBounds, offset, count, strategy ) {\n\n\tlet axis = - 1;\n\tlet pos = 0;\n\n\t// Center\n\tif ( strategy === CENTER ) {\n\n\t\taxis = getLongestEdgeIndex( centroidBoundingData );\n\t\tif ( axis !== - 1 ) {\n\n\t\t\tpos = ( centroidBoundingData[ axis ] + centroidBoundingData[ axis + 3 ] ) / 2;\n\n\t\t}\n\n\t} else if ( strategy === AVERAGE ) {\n\n\t\taxis = getLongestEdgeIndex( nodeBoundingData );\n\t\tif ( axis !== - 1 ) {\n\n\t\t\tpos = getAverage( triangleBounds, offset, count, axis );\n\n\t\t}\n\n\t} else if ( strategy === SAH ) {\n\n\t\tconst rootSurfaceArea = computeSurfaceArea( nodeBoundingData );\n\t\tlet bestCost = TRIANGLE_INTERSECT_COST * count;\n\n\t\t// iterate over all axes\n\t\tconst cStart = offset * 6;\n\t\tconst cEnd = ( offset + count ) * 6;\n\t\tfor ( let a = 0; a < 3; a ++ ) {\n\n\t\t\tconst axisLeft = centroidBoundingData[ a ];\n\t\t\tconst axisRight = centroidBoundingData[ a + 3 ];\n\t\t\tconst axisLength = axisRight - axisLeft;\n\t\t\tconst binWidth = axisLength / BIN_COUNT;\n\n\t\t\t// If we have fewer triangles than we're planning to split then just check all\n\t\t\t// the triangle positions because it will be faster.\n\t\t\tif ( count < BIN_COUNT / 4 ) {\n\n\t\t\t\t// initialize the bin candidates\n\t\t\t\tconst truncatedBins = [ ...sahBins ];\n\t\t\t\ttruncatedBins.length = count;\n\n\t\t\t\t// set the candidates\n\t\t\t\tlet b = 0;\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6, b ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ b ];\n\t\t\t\t\tbin.candidate = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tbin.count = 0;\n\n\t\t\t\t\tconst {\n\t\t\t\t\t\tbounds,\n\t\t\t\t\t\tleftCacheBounds,\n\t\t\t\t\t\trightCacheBounds,\n\t\t\t\t\t} = bin;\n\t\t\t\t\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\t\t\t\t\trightCacheBounds[ d ] = Infinity;\n\t\t\t\t\t\trightCacheBounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t\tleftCacheBounds[ d ] = Infinity;\n\t\t\t\t\t\tleftCacheBounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t\tbounds[ d ] = Infinity;\n\t\t\t\t\t\tbounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t}\n\n\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bounds );\n\n\t\t\t\t}\n\n\t\t\t\ttruncatedBins.sort( binsSort );\n\n\t\t\t\t// remove redundant splits\n\t\t\t\tlet splitCount = count;\n\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\twhile ( bi + 1 < splitCount && truncatedBins[ bi + 1 ].candidate === bin.candidate ) {\n\n\t\t\t\t\t\ttruncatedBins.splice( bi + 1, 1 );\n\t\t\t\t\t\tsplitCount --;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// find the appropriate bin for each triangle and expand the bounds.\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6 ) {\n\n\t\t\t\t\tconst center = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\t\tif ( center >= bin.candidate ) {\n\n\t\t\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.rightCacheBounds );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.leftCacheBounds );\n\t\t\t\t\t\t\tbin.count ++;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// expand all the bounds\n\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\tconst leftCount = bin.count;\n\t\t\t\t\tconst rightCount = count - bin.count;\n\n\t\t\t\t\t// check the cost of this split\n\t\t\t\t\tconst leftBounds = bin.leftCacheBounds;\n\t\t\t\t\tconst rightBounds = bin.rightCacheBounds;\n\n\t\t\t\t\tlet leftProb = 0;\n\t\t\t\t\tif ( leftCount !== 0 ) {\n\n\t\t\t\t\t\tleftProb = computeSurfaceArea( leftBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tlet rightProb = 0;\n\t\t\t\t\tif ( rightCount !== 0 ) {\n\n\t\t\t\t\t\trightProb = computeSurfaceArea( rightBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst cost = TRAVERSAL_COST + TRIANGLE_INTERSECT_COST * (\n\t\t\t\t\t\tleftProb * leftCount + rightProb * rightCount\n\t\t\t\t\t);\n\n\t\t\t\t\tif ( cost < bestCost ) {\n\n\t\t\t\t\t\taxis = a;\n\t\t\t\t\t\tbestCost = cost;\n\t\t\t\t\t\tpos = bin.candidate;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\t// reset the bins\n\t\t\t\tfor ( let i = 0; i < BIN_COUNT; i ++ ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tbin.count = 0;\n\t\t\t\t\tbin.candidate = axisLeft + binWidth + i * binWidth;\n\n\t\t\t\t\tconst bounds = bin.bounds;\n\t\t\t\t\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\t\t\t\t\tbounds[ d ] = Infinity;\n\t\t\t\t\t\tbounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// iterate over all center positions\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6 ) {\n\n\t\t\t\t\tconst triCenter = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tconst relativeCenter = triCenter - axisLeft;\n\n\t\t\t\t\t// in the partition function if the centroid lies on the split plane then it is\n\t\t\t\t\t// considered to be on the right side of the split\n\t\t\t\t\tlet binIndex = ~ ~ ( relativeCenter / binWidth );\n\t\t\t\t\tif ( binIndex >= BIN_COUNT ) binIndex = BIN_COUNT - 1;\n\n\t\t\t\t\tconst bin = sahBins[ binIndex ];\n\t\t\t\t\tbin.count ++;\n\n\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.bounds );\n\n\t\t\t\t}\n\n\t\t\t\t// cache the unioned bounds from right to left so we don't have to regenerate them each time\n\t\t\t\tconst lastBin = sahBins[ BIN_COUNT - 1 ];\n\t\t\t\tcopyBounds( lastBin.bounds, lastBin.rightCacheBounds );\n\t\t\t\tfor ( let i = BIN_COUNT - 2; i >= 0; i -- ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tconst nextBin = sahBins[ i + 1 ];\n\t\t\t\t\tunionBounds( bin.bounds, nextBin.rightCacheBounds, bin.rightCacheBounds );\n\n\t\t\t\t}\n\n\t\t\t\tlet leftCount = 0;\n\t\t\t\tfor ( let i = 0; i < BIN_COUNT - 1; i ++ ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tconst binCount = bin.count;\n\t\t\t\t\tconst bounds = bin.bounds;\n\n\t\t\t\t\tconst nextBin = sahBins[ i + 1 ];\n\t\t\t\t\tconst rightBounds = nextBin.rightCacheBounds;\n\n\t\t\t\t\t// don't do anything with the bounds if the new bounds have no triangles\n\t\t\t\t\tif ( binCount !== 0 ) {\n\n\t\t\t\t\t\tif ( leftCount === 0 ) {\n\n\t\t\t\t\t\t\tcopyBounds( bounds, leftBounds );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\tunionBounds( bounds, leftBounds, leftBounds );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\tleftCount += binCount;\n\n\t\t\t\t\t// check the cost of this split\n\t\t\t\t\tlet leftProb = 0;\n\t\t\t\t\tlet rightProb = 0;\n\n\t\t\t\t\tif ( leftCount !== 0 ) {\n\n\t\t\t\t\t\tleftProb = computeSurfaceArea( leftBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst rightCount = count - leftCount;\n\t\t\t\t\tif ( rightCount !== 0 ) {\n\n\t\t\t\t\t\trightProb = computeSurfaceArea( rightBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst cost = TRAVERSAL_COST + TRIANGLE_INTERSECT_COST * (\n\t\t\t\t\t\tleftProb * leftCount + rightProb * rightCount\n\t\t\t\t\t);\n\n\t\t\t\t\tif ( cost < bestCost ) {\n\n\t\t\t\t\t\taxis = a;\n\t\t\t\t\t\tbestCost = cost;\n\t\t\t\t\t\tpos = bin.candidate;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t} else {\n\n\t\tconsole.warn( `MeshBVH: Invalid build strategy value ${ strategy } used.` );\n\n\t}\n\n\treturn { axis, pos };\n\n}\n\n// returns the average coordinate on the specified axis of the all the provided triangles\nfunction getAverage( triangleBounds, offset, count, axis ) {\n\n\tlet avg = 0;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tavg += triangleBounds[ i * 6 + axis * 2 ];\n\n\t}\n\n\treturn avg / count;\n\n}\n\n// precomputes the bounding box for each triangle; required for quickly calculating tree splits.\n// result is an array of size tris.length * 6 where triangle i maps to a\n// [x_center, x_delta, y_center, y_delta, z_center, z_delta] tuple starting at index i * 6,\n// representing the center and half-extent in each dimension of triangle i\nfunction computeTriangleBounds( geo, fullBounds ) {\n\n\tconst posAttr = geo.attributes.position;\n\tconst index = geo.index.array;\n\tconst triCount = index.length / 3;\n\tconst triangleBounds = new Float32Array( triCount * 6 );\n\tconst normalized = posAttr.normalized;\n\n\t// used for non-normalized positions\n\tconst posArr = posAttr.array;\n\n\t// support for an interleaved position buffer\n\tconst bufferOffset = posAttr.offset || 0;\n\tlet stride = 3;\n\tif ( posAttr.isInterleavedBufferAttribute ) {\n\n\t\tstride = posAttr.data.stride;\n\n\t}\n\n\t// used for normalized positions\n\tconst getters = [ 'getX', 'getY', 'getZ' ];\n\n\tfor ( let tri = 0; tri < triCount; tri ++ ) {\n\n\t\tconst tri3 = tri * 3;\n\t\tconst tri6 = tri * 6;\n\n\t\tlet ai, bi, ci;\n\n\t\tif ( normalized ) {\n\n\t\t\tai = index[ tri3 + 0 ];\n\t\t\tbi = index[ tri3 + 1 ];\n\t\t\tci = index[ tri3 + 2 ];\n\n\t\t} else {\n\n\t\t\tai = index[ tri3 + 0 ] * stride + bufferOffset;\n\t\t\tbi = index[ tri3 + 1 ] * stride + bufferOffset;\n\t\t\tci = index[ tri3 + 2 ] * stride + bufferOffset;\n\n\t\t}\n\n\t\tfor ( let el = 0; el < 3; el ++ ) {\n\n\t\t\tlet a, b, c;\n\n\t\t\tif ( normalized ) {\n\n\t\t\t\ta = posAttr[ getters[ el ] ]( ai );\n\t\t\t\tb = posAttr[ getters[ el ] ]( bi );\n\t\t\t\tc = posAttr[ getters[ el ] ]( ci );\n\n\t\t\t} else {\n\n\t\t\t\ta = posArr[ ai + el ];\n\t\t\t\tb = posArr[ bi + el ];\n\t\t\t\tc = posArr[ ci + el ];\n\n\t\t\t}\n\n\t\t\tlet min = a;\n\t\t\tif ( b < min ) min = b;\n\t\t\tif ( c < min ) min = c;\n\n\t\t\tlet max = a;\n\t\t\tif ( b > max ) max = b;\n\t\t\tif ( c > max ) max = c;\n\n\t\t\t// Increase the bounds size by float32 epsilon to avoid precision errors when\n\t\t\t// converting to 32 bit float. Scale the epsilon by the size of the numbers being\n\t\t\t// worked with.\n\t\t\tconst halfExtents = ( max - min ) / 2;\n\t\t\tconst el2 = el * 2;\n\t\t\ttriangleBounds[ tri6 + el2 + 0 ] = min + halfExtents;\n\t\t\ttriangleBounds[ tri6 + el2 + 1 ] = halfExtents + ( Math.abs( min ) + halfExtents ) * FLOAT32_EPSILON;\n\n\t\t\tif ( min < fullBounds[ el ] ) fullBounds[ el ] = min;\n\t\t\tif ( max > fullBounds[ el + 3 ] ) fullBounds[ el + 3 ] = max;\n\n\t\t}\n\n\t}\n\n\treturn triangleBounds;\n\n}\n\nexport function buildTree( geo, options ) {\n\n\tfunction triggerProgress( trianglesProcessed ) {\n\n\t\tif ( onProgress ) {\n\n\t\t\tonProgress( trianglesProcessed / totalTriangles );\n\n\t\t}\n\n\t}\n\n\t// either recursively splits the given node, creating left and right subtrees for it, or makes it a leaf node,\n\t// recording the offset and count of its triangles and writing them into the reordered geometry index.\n\tfunction splitNode( node, offset, count, centroidBoundingData = null, depth = 0 ) {\n\n\t\tif ( ! reachedMaxDepth && depth >= maxDepth ) {\n\n\t\t\treachedMaxDepth = true;\n\t\t\tif ( verbose ) {\n\n\t\t\t\tconsole.warn( `MeshBVH: Max depth of ${ maxDepth } reached when generating BVH. Consider increasing maxDepth.` );\n\t\t\t\tconsole.warn( geo );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// early out if we've met our capacity\n\t\tif ( count <= maxLeafTris || depth >= maxDepth ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\t\t\treturn node;\n\n\t\t}\n\n\t\t// Find where to split the volume\n\t\tconst split = getOptimalSplit( node.boundingData, centroidBoundingData, triangleBounds, offset, count, strategy );\n\t\tif ( split.axis === - 1 ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\t\t\treturn node;\n\n\t\t}\n\n\t\tconst splitOffset = partition( indexArray, triangleBounds, offset, count, split );\n\n\t\t// create the two new child nodes\n\t\tif ( splitOffset === offset || splitOffset === offset + count ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\n\t\t} else {\n\n\t\t\tnode.splitAxis = split.axis;\n\n\t\t\t// create the left child and compute its bounding box\n\t\t\tconst left = new MeshBVHNode();\n\t\t\tconst lstart = offset;\n\t\t\tconst lcount = splitOffset - offset;\n\t\t\tnode.left = left;\n\t\t\tleft.boundingData = new Float32Array( 6 );\n\n\t\t\tgetBounds( triangleBounds, lstart, lcount, left.boundingData, cacheCentroidBoundingData );\n\t\t\tsplitNode( left, lstart, lcount, cacheCentroidBoundingData, depth + 1 );\n\n\t\t\t// repeat for right\n\t\t\tconst right = new MeshBVHNode();\n\t\t\tconst rstart = splitOffset;\n\t\t\tconst rcount = count - lcount;\n\t\t\tnode.right = right;\n\t\t\tright.boundingData = new Float32Array( 6 );\n\n\t\t\tgetBounds( triangleBounds, rstart, rcount, right.boundingData, cacheCentroidBoundingData );\n\t\t\tsplitNode( right, rstart, rcount, cacheCentroidBoundingData, depth + 1 );\n\n\t\t}\n\n\t\treturn node;\n\n\t}\n\n\tensureIndex( geo, options );\n\n\t// Compute the full bounds of the geometry at the same time as triangle bounds because\n\t// we'll need it for the root bounds in the case with no groups and it should be fast here.\n\t// We can't use the geometrying bounding box if it's available because it may be out of date.\n\tconst fullBounds = new Float32Array( 6 );\n\tconst cacheCentroidBoundingData = new Float32Array( 6 );\n\tconst triangleBounds = computeTriangleBounds( geo, fullBounds );\n\tconst indexArray = geo.index.array;\n\tconst maxDepth = options.maxDepth;\n\tconst verbose = options.verbose;\n\tconst maxLeafTris = options.maxLeafTris;\n\tconst strategy = options.strategy;\n\tconst onProgress = options.onProgress;\n\tconst totalTriangles = geo.index.count / 3;\n\tlet reachedMaxDepth = false;\n\n\tconst roots = [];\n\tconst ranges = getRootIndexRanges( geo );\n\n\tif ( ranges.length === 1 ) {\n\n\t\tconst range = ranges[ 0 ];\n\t\tconst root = new MeshBVHNode();\n\t\troot.boundingData = fullBounds;\n\t\tgetCentroidBounds( triangleBounds, range.offset, range.count, cacheCentroidBoundingData );\n\n\t\tsplitNode( root, range.offset, range.count, cacheCentroidBoundingData );\n\t\troots.push( root );\n\n\t} else {\n\n\t\tfor ( let range of ranges ) {\n\n\t\t\tconst root = new MeshBVHNode();\n\t\t\troot.boundingData = new Float32Array( 6 );\n\t\t\tgetBounds( triangleBounds, range.offset, range.count, root.boundingData, cacheCentroidBoundingData );\n\n\t\t\tsplitNode( root, range.offset, range.count, cacheCentroidBoundingData );\n\t\t\troots.push( root );\n\n\t\t}\n\n\t}\n\n\treturn roots;\n\n}\n\nexport function buildPackedTree( geo, options ) {\n\n\t// boundingData  \t\t\t\t: 6 float32\n\t// right / offset \t\t\t\t: 1 uint32\n\t// splitAxis / isLeaf + count \t: 1 uint32 / 2 uint16\n\tconst roots = buildTree( geo, options );\n\n\tlet float32Array;\n\tlet uint32Array;\n\tlet uint16Array;\n\tconst packedRoots = [];\n\tconst BufferConstructor = options.useSharedArrayBuffer ? SharedArrayBuffer : ArrayBuffer;\n\tfor ( let i = 0; i < roots.length; i ++ ) {\n\n\t\tconst root = roots[ i ];\n\t\tlet nodeCount = countNodes( root );\n\n\t\tconst buffer = new BufferConstructor( BYTES_PER_NODE * nodeCount );\n\t\tfloat32Array = new Float32Array( buffer );\n\t\tuint32Array = new Uint32Array( buffer );\n\t\tuint16Array = new Uint16Array( buffer );\n\t\tpopulateBuffer( 0, root );\n\t\tpackedRoots.push( buffer );\n\n\t}\n\n\treturn packedRoots;\n\n\tfunction countNodes( node ) {\n\n\t\tif ( node.count ) {\n\n\t\t\treturn 1;\n\n\t\t} else {\n\n\t\t\treturn 1 + countNodes( node.left ) + countNodes( node.right );\n\n\t\t}\n\n\t}\n\n\tfunction populateBuffer( byteOffset, node ) {\n\n\t\tconst stride4Offset = byteOffset / 4;\n\t\tconst stride2Offset = byteOffset / 2;\n\t\tconst isLeaf = ! ! node.count;\n\t\tconst boundingData = node.boundingData;\n\t\tfor ( let i = 0; i < 6; i ++ ) {\n\n\t\t\tfloat32Array[ stride4Offset + i ] = boundingData[ i ];\n\n\t\t}\n\n\t\tif ( isLeaf ) {\n\n\t\t\tconst offset = node.offset;\n\t\t\tconst count = node.count;\n\t\t\tuint32Array[ stride4Offset + 6 ] = offset;\n\t\t\tuint16Array[ stride2Offset + 14 ] = count;\n\t\t\tuint16Array[ stride2Offset + 15 ] = IS_LEAFNODE_FLAG;\n\t\t\treturn byteOffset + BYTES_PER_NODE;\n\n\t\t} else {\n\n\t\t\tconst left = node.left;\n\t\t\tconst right = node.right;\n\t\t\tconst splitAxis = node.splitAxis;\n\n\t\t\tlet nextUnusedPointer;\n\t\t\tnextUnusedPointer = populateBuffer( byteOffset + BYTES_PER_NODE, left );\n\n\t\t\tif ( ( nextUnusedPointer / 4 ) > Math.pow( 2, 32 ) ) {\n\n\t\t\t\tthrow new Error( 'MeshBVH: Cannot store child pointer greater than 32 bits.' );\n\n\t\t\t}\n\n\t\t\tuint32Array[ stride4Offset + 6 ] = nextUnusedPointer / 4;\n\t\t\tnextUnusedPointer = populateBuffer( nextUnusedPointer, right );\n\n\t\t\tuint32Array[ stride4Offset + 7 ] = splitAxis;\n\t\t\treturn nextUnusedPointer;\n\n\t\t}\n\n\t}\n\n}\n", "import { Vector3 } from 'three';\n\nexport class SeparatingAxisBounds {\n\n\tconstructor() {\n\n\t\tthis.min = Infinity;\n\t\tthis.max = - Infinity;\n\n\t}\n\n\tsetFromPointsField( points, field ) {\n\n\t\tlet min = Infinity;\n\t\tlet max = - Infinity;\n\t\tfor ( let i = 0, l = points.length; i < l; i ++ ) {\n\n\t\t\tconst p = points[ i ];\n\t\t\tconst val = p[ field ];\n\t\t\tmin = val < min ? val : min;\n\t\t\tmax = val > max ? val : max;\n\n\t\t}\n\n\t\tthis.min = min;\n\t\tthis.max = max;\n\n\t}\n\n\tsetFromPoints( axis, points ) {\n\n\t\tlet min = Infinity;\n\t\tlet max = - Infinity;\n\t\tfor ( let i = 0, l = points.length; i < l; i ++ ) {\n\n\t\t\tconst p = points[ i ];\n\t\t\tconst val = axis.dot( p );\n\t\t\tmin = val < min ? val : min;\n\t\t\tmax = val > max ? val : max;\n\n\t\t}\n\n\t\tthis.min = min;\n\t\tthis.max = max;\n\n\t}\n\n\tisSeparated( other ) {\n\n\t\treturn this.min > other.max || other.min > this.max;\n\n\t}\n\n}\n\nSeparatingAxisBounds.prototype.setFromBox = ( function () {\n\n\tconst p = new Vector3();\n\treturn function setFromBox( axis, box ) {\n\n\t\tconst boxMin = box.min;\n\t\tconst boxMax = box.max;\n\t\tlet min = Infinity;\n\t\tlet max = - Infinity;\n\t\tfor ( let x = 0; x <= 1; x ++ ) {\n\n\t\t\tfor ( let y = 0; y <= 1; y ++ ) {\n\n\t\t\t\tfor ( let z = 0; z <= 1; z ++ ) {\n\n\t\t\t\t\tp.x = boxMin.x * x + boxMax.x * ( 1 - x );\n\t\t\t\t\tp.y = boxMin.y * y + boxMax.y * ( 1 - y );\n\t\t\t\t\tp.z = boxMin.z * z + boxMax.z * ( 1 - z );\n\n\t\t\t\t\tconst val = axis.dot( p );\n\t\t\t\t\tmin = Math.min( val, min );\n\t\t\t\t\tmax = Math.max( val, max );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tthis.min = min;\n\t\tthis.max = max;\n\n\t};\n\n} )();\n\nexport const areIntersecting = ( function () {\n\n\tconst cacheSatBounds = new SeparatingAxisBounds();\n\treturn function areIntersecting( shape1, shape2 ) {\n\n\t\tconst points1 = shape1.points;\n\t\tconst satAxes1 = shape1.satAxes;\n\t\tconst satBounds1 = shape1.satBounds;\n\n\t\tconst points2 = shape2.points;\n\t\tconst satAxes2 = shape2.satAxes;\n\t\tconst satBounds2 = shape2.satBounds;\n\n\t\t// check axes of the first shape\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = satBounds1[ i ];\n\t\t\tconst sa = satAxes1[ i ];\n\t\t\tcacheSatBounds.setFromPoints( sa, points2 );\n\t\t\tif ( sb.isSeparated( cacheSatBounds ) ) return false;\n\n\t\t}\n\n\t\t// check axes of the second shape\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = satBounds2[ i ];\n\t\t\tconst sa = satAxes2[ i ];\n\t\t\tcacheSatBounds.setFromPoints( sa, points1 );\n\t\t\tif ( sb.isSeparated( cacheSatBounds ) ) return false;\n\n\t\t}\n\n\t};\n\n} )();\n", "import { Vector3, Vector2, Plane, Line3 } from 'three';\n\nexport const closestPointLineToLine = ( function () {\n\n\t// https://github.com/juj/MathGeoLib/blob/master/src/Geometry/Line.cpp#L56\n\tconst dir1 = new Vector3();\n\tconst dir2 = new Vector3();\n\tconst v02 = new Vector3();\n\treturn function closestPointLineToLine( l1, l2, result ) {\n\n\t\tconst v0 = l1.start;\n\t\tconst v10 = dir1;\n\t\tconst v2 = l2.start;\n\t\tconst v32 = dir2;\n\n\t\tv02.subVectors( v0, v2 );\n\t\tdir1.subVectors( l1.end, l1.start );\n\t\tdir2.subVectors( l2.end, l2.start );\n\n\t\t// float d0232 = v02.Dot(v32);\n\t\tconst d0232 = v02.dot( v32 );\n\n\t\t// float d3210 = v32.Dot(v10);\n\t\tconst d3210 = v32.dot( v10 );\n\n\t\t// float d3232 = v32.Dot(v32);\n\t\tconst d3232 = v32.dot( v32 );\n\n\t\t// float d0210 = v02.Dot(v10);\n\t\tconst d0210 = v02.dot( v10 );\n\n\t\t// float d1010 = v10.Dot(v10);\n\t\tconst d1010 = v10.dot( v10 );\n\n\t\t// float denom = d1010*d3232 - d3210*d3210;\n\t\tconst denom = d1010 * d3232 - d3210 * d3210;\n\n\t\tlet d, d2;\n\t\tif ( denom !== 0 ) {\n\n\t\t\td = ( d0232 * d3210 - d0210 * d3232 ) / denom;\n\n\t\t} else {\n\n\t\t\td = 0;\n\n\t\t}\n\n\t\td2 = ( d0232 + d * d3210 ) / d3232;\n\n\t\tresult.x = d;\n\t\tresult.y = d2;\n\n\t};\n\n} )();\n\nexport const closestPointsSegmentToSegment = ( function () {\n\n\t// https://github.com/juj/MathGeoLib/blob/master/src/Geometry/LineSegment.cpp#L187\n\tconst paramResult = new Vector2();\n\tconst temp1 = new Vector3();\n\tconst temp2 = new Vector3();\n\treturn function closestPointsSegmentToSegment( l1, l2, target1, target2 ) {\n\n\t\tclosestPointLineToLine( l1, l2, paramResult );\n\n\t\tlet d = paramResult.x;\n\t\tlet d2 = paramResult.y;\n\t\tif ( d >= 0 && d <= 1 && d2 >= 0 && d2 <= 1 ) {\n\n\t\t\tl1.at( d, target1 );\n\t\t\tl2.at( d2, target2 );\n\n\t\t\treturn;\n\n\t\t} else if ( d >= 0 && d <= 1 ) {\n\n\t\t\t// Only d2 is out of bounds.\n\t\t\tif ( d2 < 0 ) {\n\n\t\t\t\tl2.at( 0, target2 );\n\n\t\t\t} else {\n\n\t\t\t\tl2.at( 1, target2 );\n\n\t\t\t}\n\n\t\t\tl1.closestPointToPoint( target2, true, target1 );\n\t\t\treturn;\n\n\t\t} else if ( d2 >= 0 && d2 <= 1 ) {\n\n\t\t\t// Only d is out of bounds.\n\t\t\tif ( d < 0 ) {\n\n\t\t\t\tl1.at( 0, target1 );\n\n\t\t\t} else {\n\n\t\t\t\tl1.at( 1, target1 );\n\n\t\t\t}\n\n\t\t\tl2.closestPointToPoint( target1, true, target2 );\n\t\t\treturn;\n\n\t\t} else {\n\n\t\t\t// Both u and u2 are out of bounds.\n\t\t\tlet p;\n\t\t\tif ( d < 0 ) {\n\n\t\t\t\tp = l1.start;\n\n\t\t\t} else {\n\n\t\t\t\tp = l1.end;\n\n\t\t\t}\n\n\t\t\tlet p2;\n\t\t\tif ( d2 < 0 ) {\n\n\t\t\t\tp2 = l2.start;\n\n\t\t\t} else {\n\n\t\t\t\tp2 = l2.end;\n\n\t\t\t}\n\n\t\t\tconst closestPoint = temp1;\n\t\t\tconst closestPoint2 = temp2;\n\t\t\tl1.closestPointToPoint( p2, true, temp1 );\n\t\t\tl2.closestPointToPoint( p, true, temp2 );\n\n\t\t\tif ( closestPoint.distanceToSquared( p2 ) <= closestPoint2.distanceToSquared( p ) ) {\n\n\t\t\t\ttarget1.copy( closestPoint );\n\t\t\t\ttarget2.copy( p2 );\n\t\t\t\treturn;\n\n\t\t\t} else {\n\n\t\t\t\ttarget1.copy( p );\n\t\t\t\ttarget2.copy( closestPoint2 );\n\t\t\t\treturn;\n\n\t\t\t}\n\n\t\t}\n\n\t};\n\n} )();\n\n\nexport const sphereIntersectTriangle = ( function () {\n\n\t// https://stackoverflow.com/questions/34043955/detect-collision-between-sphere-and-triangle-in-three-js\n\tconst closestPointTemp = new Vector3();\n\tconst projectedPointTemp = new Vector3();\n\tconst planeTemp = new Plane();\n\tconst lineTemp = new Line3();\n\treturn function sphereIntersectTriangle( sphere, triangle ) {\n\n\t\tconst { radius, center } = sphere;\n\t\tconst { a, b, c } = triangle;\n\n\t\t// phase 1\n\t\tlineTemp.start = a;\n\t\tlineTemp.end = b;\n\t\tconst closestPoint1 = lineTemp.closestPointToPoint( center, true, closestPointTemp );\n\t\tif ( closestPoint1.distanceTo( center ) <= radius ) return true;\n\n\t\tlineTemp.start = a;\n\t\tlineTemp.end = c;\n\t\tconst closestPoint2 = lineTemp.closestPointToPoint( center, true, closestPointTemp );\n\t\tif ( closestPoint2.distanceTo( center ) <= radius ) return true;\n\n\t\tlineTemp.start = b;\n\t\tlineTemp.end = c;\n\t\tconst closestPoint3 = lineTemp.closestPointToPoint( center, true, closestPointTemp );\n\t\tif ( closestPoint3.distanceTo( center ) <= radius ) return true;\n\n\t\t// phase 2\n\t\tconst plane = triangle.getPlane( planeTemp );\n\t\tconst dp = Math.abs( plane.distanceToPoint( center ) );\n\t\tif ( dp <= radius ) {\n\n\t\t\tconst pp = plane.projectPoint( center, projectedPointTemp );\n\t\t\tconst cp = triangle.containsPoint( pp );\n\t\t\tif ( cp ) return true;\n\n\t\t}\n\n\t\treturn false;\n\n\t};\n\n} )();\n", "import { Triangle, Vector3, Line3, Sphere, Plane } from 'three';\nimport { SeparatingAxisBounds } from './SeparatingAxisBounds.js';\nimport { closestPointsSegmentToSegment, sphereIntersectTriangle } from './MathUtilities.js';\n\nconst DIST_EPSILON = 1e-15;\nfunction isNearZero( value ) {\n\n\treturn Math.abs( value ) < DIST_EPSILON;\n\n}\n\nexport class ExtendedTriangle extends Triangle {\n\n\tconstructor( ...args ) {\n\n\t\tsuper( ...args );\n\n\t\tthis.isExtendedTriangle = true;\n\t\tthis.satAxes = new Array( 4 ).fill().map( () => new Vector3() );\n\t\tthis.satBounds = new Array( 4 ).fill().map( () => new SeparatingAxisBounds() );\n\t\tthis.points = [ this.a, this.b, this.c ];\n\t\tthis.sphere = new Sphere();\n\t\tthis.plane = new Plane();\n\t\tthis.needsUpdate = true;\n\n\t}\n\n\tintersectsSphere( sphere ) {\n\n\t\treturn sphereIntersectTriangle( sphere, this );\n\n\t}\n\n\tupdate() {\n\n\t\tconst a = this.a;\n\t\tconst b = this.b;\n\t\tconst c = this.c;\n\t\tconst points = this.points;\n\n\t\tconst satAxes = this.satAxes;\n\t\tconst satBounds = this.satBounds;\n\n\t\tconst axis0 = satAxes[ 0 ];\n\t\tconst sab0 = satBounds[ 0 ];\n\t\tthis.getNormal( axis0 );\n\t\tsab0.setFromPoints( axis0, points );\n\n\t\tconst axis1 = satAxes[ 1 ];\n\t\tconst sab1 = satBounds[ 1 ];\n\t\taxis1.subVectors( a, b );\n\t\tsab1.setFromPoints( axis1, points );\n\n\t\tconst axis2 = satAxes[ 2 ];\n\t\tconst sab2 = satBounds[ 2 ];\n\t\taxis2.subVectors( b, c );\n\t\tsab2.setFromPoints( axis2, points );\n\n\t\tconst axis3 = satAxes[ 3 ];\n\t\tconst sab3 = satBounds[ 3 ];\n\t\taxis3.subVectors( c, a );\n\t\tsab3.setFromPoints( axis3, points );\n\n\t\tthis.sphere.setFromPoints( this.points );\n\t\tthis.plane.setFromNormalAndCoplanarPoint( axis0, a );\n\t\tthis.needsUpdate = false;\n\n\t}\n\n}\n\nExtendedTriangle.prototype.closestPointToSegment = ( function () {\n\n\tconst point1 = new Vector3();\n\tconst point2 = new Vector3();\n\tconst edge = new Line3();\n\n\treturn function distanceToSegment( segment, target1 = null, target2 = null ) {\n\n\t\tconst { start, end } = segment;\n\t\tconst points = this.points;\n\t\tlet distSq;\n\t\tlet closestDistanceSq = Infinity;\n\n\t\t// check the triangle edges\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst nexti = ( i + 1 ) % 3;\n\t\t\tedge.start.copy( points[ i ] );\n\t\t\tedge.end.copy( points[ nexti ] );\n\n\t\t\tclosestPointsSegmentToSegment( edge, segment, point1, point2 );\n\n\t\t\tdistSq = point1.distanceToSquared( point2 );\n\t\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = distSq;\n\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// check end points\n\t\tthis.closestPointToPoint( start, point1 );\n\t\tdistSq = start.distanceToSquared( point1 );\n\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\tclosestDistanceSq = distSq;\n\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\tif ( target2 ) target2.copy( start );\n\n\t\t}\n\n\t\tthis.closestPointToPoint( end, point1 );\n\t\tdistSq = end.distanceToSquared( point1 );\n\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\tclosestDistanceSq = distSq;\n\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\tif ( target2 ) target2.copy( end );\n\n\t\t}\n\n\t\treturn Math.sqrt( closestDistanceSq );\n\n\t};\n\n} )();\n\nExtendedTriangle.prototype.intersectsTriangle = ( function () {\n\n\tconst saTri2 = new ExtendedTriangle();\n\tconst arr1 = new Array( 3 );\n\tconst arr2 = new Array( 3 );\n\tconst cachedSatBounds = new SeparatingAxisBounds();\n\tconst cachedSatBounds2 = new SeparatingAxisBounds();\n\tconst cachedAxis = new Vector3();\n\tconst dir1 = new Vector3();\n\tconst dir2 = new Vector3();\n\tconst tempDir = new Vector3();\n\tconst edge = new Line3();\n\tconst edge1 = new Line3();\n\tconst edge2 = new Line3();\n\n\t// TODO: If the triangles are coplanar and intersecting the target is nonsensical. It should at least\n\t// be a line contained by both triangles if not a different special case somehow represented in the return result.\n\treturn function intersectsTriangle( other, target = null, suppressLog = false ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tif ( ! other.isExtendedTriangle ) {\n\n\t\t\tsaTri2.copy( other );\n\t\t\tsaTri2.update();\n\t\t\tother = saTri2;\n\n\t\t} else if ( other.needsUpdate ) {\n\n\t\t\tother.update();\n\n\t\t}\n\n\t\tconst plane1 = this.plane;\n\t\tconst plane2 = other.plane;\n\n\t\tif ( Math.abs( plane1.normal.dot( plane2.normal ) ) > 1.0 - 1e-10 ) {\n\n\t\t\t// perform separating axis intersection test only for coplanar triangles\n\t\t\tconst satBounds1 = this.satBounds;\n\t\t\tconst satAxes1 = this.satAxes;\n\t\t\tarr2[ 0 ] = other.a;\n\t\t\tarr2[ 1 ] = other.b;\n\t\t\tarr2[ 2 ] = other.c;\n\t\t\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\t\t\tconst sb = satBounds1[ i ];\n\t\t\t\tconst sa = satAxes1[ i ];\n\t\t\t\tcachedSatBounds.setFromPoints( sa, arr2 );\n\t\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t\t}\n\n\t\t\tconst satBounds2 = other.satBounds;\n\t\t\tconst satAxes2 = other.satAxes;\n\t\t\tarr1[ 0 ] = this.a;\n\t\t\tarr1[ 1 ] = this.b;\n\t\t\tarr1[ 2 ] = this.c;\n\t\t\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\t\t\tconst sb = satBounds2[ i ];\n\t\t\t\tconst sa = satAxes2[ i ];\n\t\t\t\tcachedSatBounds.setFromPoints( sa, arr1 );\n\t\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t\t}\n\n\t\t\t// check crossed axes\n\t\t\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\t\t\tconst sa1 = satAxes1[ i ];\n\t\t\t\tfor ( let i2 = 0; i2 < 4; i2 ++ ) {\n\n\t\t\t\t\tconst sa2 = satAxes2[ i2 ];\n\t\t\t\t\tcachedAxis.crossVectors( sa1, sa2 );\n\t\t\t\t\tcachedSatBounds.setFromPoints( cachedAxis, arr1 );\n\t\t\t\t\tcachedSatBounds2.setFromPoints( cachedAxis, arr2 );\n\t\t\t\t\tif ( cachedSatBounds.isSeparated( cachedSatBounds2 ) ) return false;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( target ) {\n\n\t\t\t\t// TODO find two points that intersect on the edges and make that the result\n\t\t\t\tif ( ! suppressLog ) {\n\n\t\t\t\t\tconsole.warn( 'ExtendedTriangle.intersectsTriangle: Triangles are coplanar which does not support an output edge. Setting edge to 0, 0, 0.' );\n\n\t\t\t\t}\n\n\t\t\t\ttarget.start.set( 0, 0, 0 );\n\t\t\t\ttarget.end.set( 0, 0, 0 );\n\n\t\t\t}\n\n\t\t\treturn true;\n\n\t\t} else {\n\n\t\t\t// find the edge that intersects the other triangle plane\n\t\t\tconst points1 = this.points;\n\t\t\tlet found1 = false;\n\t\t\tlet count1 = 0;\n\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\tconst p = points1[ i ];\n\t\t\t\tconst pNext = points1[ ( i + 1 ) % 3 ];\n\n\t\t\t\tedge.start.copy( p );\n\t\t\t\tedge.end.copy( pNext );\n\t\t\t\tedge.delta( dir1 );\n\n\t\t\t\tconst targetPoint = found1 ? edge1.start : edge1.end;\n\t\t\t\tconst startIntersects = isNearZero( plane2.distanceToPoint( p ) );\n\t\t\t\tif ( isNearZero( plane2.normal.dot( dir1 ) ) && startIntersects ) {\n\n\t\t\t\t\t// if the edge lies on the plane then take the line\n\t\t\t\t\tedge1.copy( edge );\n\t\t\t\t\tcount1 = 2;\n\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t\t// check if the start point is near the plane because \"intersectLine\" is not robust to that case\n\t\t\t\tconst doesIntersect = plane2.intersectLine( edge, targetPoint ) || startIntersects;\n\t\t\t\tif ( doesIntersect && ! isNearZero( targetPoint.distanceTo( pNext ) ) ) {\n\n\t\t\t\t\tcount1 ++;\n\t\t\t\t\tif ( found1 ) {\n\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tfound1 = true;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( count1 === 1 && other.containsPoint( edge1.end ) ) {\n\n\t\t\t\tif ( target ) {\n\n\t\t\t\t\ttarget.start.copy( edge1.end );\n\t\t\t\t\ttarget.end.copy( edge1.end );\n\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\n\t\t\t} else if ( count1 !== 2 ) {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\t// find the other triangles edge that intersects this plane\n\t\t\tconst points2 = other.points;\n\t\t\tlet found2 = false;\n\t\t\tlet count2 = 0;\n\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\tconst p = points2[ i ];\n\t\t\t\tconst pNext = points2[ ( i + 1 ) % 3 ];\n\n\t\t\t\tedge.start.copy( p );\n\t\t\t\tedge.end.copy( pNext );\n\t\t\t\tedge.delta( dir2 );\n\n\t\t\t\tconst targetPoint = found2 ? edge2.start : edge2.end;\n\t\t\t\tconst startIntersects = isNearZero( plane1.distanceToPoint( p ) );\n\t\t\t\tif ( isNearZero( plane1.normal.dot( dir2 ) ) && startIntersects ) {\n\n\t\t\t\t\t// if the edge lies on the plane then take the line\n\t\t\t\t\tedge2.copy( edge );\n\t\t\t\t\tcount2 = 2;\n\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t\t// check if the start point is near the plane because \"intersectLine\" is not robust to that case\n\t\t\t\tconst doesIntersect = plane1.intersectLine( edge, targetPoint ) || startIntersects;\n\t\t\t\tif ( doesIntersect && ! isNearZero( targetPoint.distanceTo( pNext ) ) ) {\n\n\t\t\t\t\tcount2 ++;\n\t\t\t\t\tif ( found2 ) {\n\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tfound2 = true;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( count2 === 1 && this.containsPoint( edge2.end ) ) {\n\n\t\t\t\tif ( target ) {\n\n\t\t\t\t\ttarget.start.copy( edge2.end );\n\t\t\t\t\ttarget.end.copy( edge2.end );\n\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\n\t\t\t} else if ( count2 !== 2 ) {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\t// find swap the second edge so both lines are running the same direction\n\t\t\tedge1.delta( dir1 );\n\t\t\tedge2.delta( dir2 );\n\n\t\t\tif ( dir1.dot( dir2 ) < 0 ) {\n\n\t\t\t\tlet tmp = edge2.start;\n\t\t\t\tedge2.start = edge2.end;\n\t\t\t\tedge2.end = tmp;\n\n\t\t\t}\n\n\t\t\t// check if the edges are overlapping\n\t\t\tconst s1 = edge1.start.dot( dir1 );\n\t\t\tconst e1 = edge1.end.dot( dir1 );\n\t\t\tconst s2 = edge2.start.dot( dir1 );\n\t\t\tconst e2 = edge2.end.dot( dir1 );\n\t\t\tconst separated1 = e1 < s2;\n\t\t\tconst separated2 = s1 < e2;\n\n\t\t\tif ( s1 !== e2 && s2 !== e1 && separated1 === separated2 ) {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\t// assign the target output\n\t\t\tif ( target ) {\n\n\t\t\t\ttempDir.subVectors( edge1.start, edge2.start );\n\t\t\t\tif ( tempDir.dot( dir1 ) > 0 ) {\n\n\t\t\t\t\ttarget.start.copy( edge1.start );\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttarget.start.copy( edge2.start );\n\n\t\t\t\t}\n\n\t\t\t\ttempDir.subVectors( edge1.end, edge2.end );\n\t\t\t\tif ( tempDir.dot( dir1 ) < 0 ) {\n\n\t\t\t\t\ttarget.end.copy( edge1.end );\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttarget.end.copy( edge2.end );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn true;\n\n\t\t}\n\n\t};\n\n} )();\n\n\nExtendedTriangle.prototype.distanceToPoint = ( function () {\n\n\tconst target = new Vector3();\n\treturn function distanceToPoint( point ) {\n\n\t\tthis.closestPointToPoint( point, target );\n\t\treturn point.distanceTo( target );\n\n\t};\n\n} )();\n\n\nExtendedTriangle.prototype.distanceToTriangle = ( function () {\n\n\tconst point = new Vector3();\n\tconst point2 = new Vector3();\n\tconst cornerFields = [ 'a', 'b', 'c' ];\n\tconst line1 = new Line3();\n\tconst line2 = new Line3();\n\n\treturn function distanceToTriangle( other, target1 = null, target2 = null ) {\n\n\t\tconst lineTarget = target1 || target2 ? line1 : null;\n\t\tif ( this.intersectsTriangle( other, lineTarget ) ) {\n\n\t\t\tif ( target1 || target2 ) {\n\n\t\t\t\tif ( target1 ) lineTarget.getCenter( target1 );\n\t\t\t\tif ( target2 ) lineTarget.getCenter( target2 );\n\n\t\t\t}\n\n\t\t\treturn 0;\n\n\t\t}\n\n\t\tlet closestDistanceSq = Infinity;\n\n\t\t// check all point distances\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tlet dist;\n\t\t\tconst field = cornerFields[ i ];\n\t\t\tconst otherVec = other[ field ];\n\t\t\tthis.closestPointToPoint( otherVec, point );\n\n\t\t\tdist = otherVec.distanceToSquared( point );\n\n\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\tif ( target1 ) target1.copy( point );\n\t\t\t\tif ( target2 ) target2.copy( otherVec );\n\n\t\t\t}\n\n\n\t\t\tconst thisVec = this[ field ];\n\t\t\tother.closestPointToPoint( thisVec, point );\n\n\t\t\tdist = thisVec.distanceToSquared( point );\n\n\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\tif ( target1 ) target1.copy( thisVec );\n\t\t\t\tif ( target2 ) target2.copy( point );\n\n\t\t\t}\n\n\t\t}\n\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst f11 = cornerFields[ i ];\n\t\t\tconst f12 = cornerFields[ ( i + 1 ) % 3 ];\n\t\t\tline1.set( this[ f11 ], this[ f12 ] );\n\t\t\tfor ( let i2 = 0; i2 < 3; i2 ++ ) {\n\n\t\t\t\tconst f21 = cornerFields[ i2 ];\n\t\t\t\tconst f22 = cornerFields[ ( i2 + 1 ) % 3 ];\n\t\t\t\tline2.set( other[ f21 ], other[ f22 ] );\n\n\t\t\t\tclosestPointsSegmentToSegment( line1, line2, point, point2 );\n\n\t\t\t\tconst dist = point.distanceToSquared( point2 );\n\t\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\t\tif ( target1 ) target1.copy( point );\n\t\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Math.sqrt( closestDistanceSq );\n\n\t};\n\n} )();\n", "import { Vector3, Matrix4, Line3 } from 'three';\nimport { SeparatingAxisBounds } from './SeparatingAxisBounds.js';\nimport { ExtendedTriangle } from './ExtendedTriangle.js';\nimport { closestPointsSegmentToSegment } from './MathUtilities.js';\n\nexport class OrientedBox {\n\n\tconstructor( min, max, matrix ) {\n\n\t\tthis.isOrientedBox = true;\n\t\tthis.min = new Vector3();\n\t\tthis.max = new Vector3();\n\t\tthis.matrix = new Matrix4();\n\t\tthis.invMatrix = new Matrix4();\n\t\tthis.points = new Array( 8 ).fill().map( () => new Vector3() );\n\t\tthis.satAxes = new Array( 3 ).fill().map( () => new Vector3() );\n\t\tthis.satBounds = new Array( 3 ).fill().map( () => new SeparatingAxisBounds() );\n\t\tthis.alignedSatBounds = new Array( 3 ).fill().map( () => new SeparatingAxisBounds() );\n\t\tthis.needsUpdate = false;\n\n\t\tif ( min ) this.min.copy( min );\n\t\tif ( max ) this.max.copy( max );\n\t\tif ( matrix ) this.matrix.copy( matrix );\n\n\t}\n\n\tset( min, max, matrix ) {\n\n\t\tthis.min.copy( min );\n\t\tthis.max.copy( max );\n\t\tthis.matrix.copy( matrix );\n\t\tthis.needsUpdate = true;\n\n\t}\n\n\tcopy( other ) {\n\n\t\tthis.min.copy( other.min );\n\t\tthis.max.copy( other.max );\n\t\tthis.matrix.copy( other.matrix );\n\t\tthis.needsUpdate = true;\n\n\t}\n\n}\n\nOrientedBox.prototype.update = ( function () {\n\n\treturn function update() {\n\n\t\tconst matrix = this.matrix;\n\t\tconst min = this.min;\n\t\tconst max = this.max;\n\n\t\tconst points = this.points;\n\t\tfor ( let x = 0; x <= 1; x ++ ) {\n\n\t\t\tfor ( let y = 0; y <= 1; y ++ ) {\n\n\t\t\t\tfor ( let z = 0; z <= 1; z ++ ) {\n\n\t\t\t\t\tconst i = ( ( 1 << 0 ) * x ) | ( ( 1 << 1 ) * y ) | ( ( 1 << 2 ) * z );\n\t\t\t\t\tconst v = points[ i ];\n\t\t\t\t\tv.x = x ? max.x : min.x;\n\t\t\t\t\tv.y = y ? max.y : min.y;\n\t\t\t\t\tv.z = z ? max.z : min.z;\n\n\t\t\t\t\tv.applyMatrix4( matrix );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tconst satBounds = this.satBounds;\n\t\tconst satAxes = this.satAxes;\n\t\tconst minVec = points[ 0 ];\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst axis = satAxes[ i ];\n\t\t\tconst sb = satBounds[ i ];\n\t\t\tconst index = 1 << i;\n\t\t\tconst pi = points[ index ];\n\n\t\t\taxis.subVectors( minVec, pi );\n\t\t\tsb.setFromPoints( axis, points );\n\n\t\t}\n\n\t\tconst alignedSatBounds = this.alignedSatBounds;\n\t\talignedSatBounds[ 0 ].setFromPointsField( points, 'x' );\n\t\talignedSatBounds[ 1 ].setFromPointsField( points, 'y' );\n\t\talignedSatBounds[ 2 ].setFromPointsField( points, 'z' );\n\n\t\tthis.invMatrix.copy( this.matrix ).invert();\n\t\tthis.needsUpdate = false;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.intersectsBox = ( function () {\n\n\tconst aabbBounds = new SeparatingAxisBounds();\n\treturn function intersectsBox( box ) {\n\n\t\t// TODO: should this be doing SAT against the AABB?\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tconst min = box.min;\n\t\tconst max = box.max;\n\t\tconst satBounds = this.satBounds;\n\t\tconst satAxes = this.satAxes;\n\t\tconst alignedSatBounds = this.alignedSatBounds;\n\n\t\taabbBounds.min = min.x;\n\t\taabbBounds.max = max.x;\n\t\tif ( alignedSatBounds[ 0 ].isSeparated( aabbBounds ) ) return false;\n\n\t\taabbBounds.min = min.y;\n\t\taabbBounds.max = max.y;\n\t\tif ( alignedSatBounds[ 1 ].isSeparated( aabbBounds ) ) return false;\n\n\t\taabbBounds.min = min.z;\n\t\taabbBounds.max = max.z;\n\t\tif ( alignedSatBounds[ 2 ].isSeparated( aabbBounds ) ) return false;\n\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst axis = satAxes[ i ];\n\t\t\tconst sb = satBounds[ i ];\n\t\t\taabbBounds.setFromBox( axis, box );\n\t\t\tif ( sb.isSeparated( aabbBounds ) ) return false;\n\n\t\t}\n\n\t\treturn true;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.intersectsTriangle = ( function () {\n\n\tconst saTri = new ExtendedTriangle();\n\tconst pointsArr = new Array( 3 );\n\tconst cachedSatBounds = new SeparatingAxisBounds();\n\tconst cachedSatBounds2 = new SeparatingAxisBounds();\n\tconst cachedAxis = new Vector3();\n\treturn function intersectsTriangle( triangle ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tif ( ! triangle.isExtendedTriangle ) {\n\n\t\t\tsaTri.copy( triangle );\n\t\t\tsaTri.update();\n\t\t\ttriangle = saTri;\n\n\t\t} else if ( triangle.needsUpdate ) {\n\n\t\t\ttriangle.update();\n\n\t\t}\n\n\t\tconst satBounds = this.satBounds;\n\t\tconst satAxes = this.satAxes;\n\n\t\tpointsArr[ 0 ] = triangle.a;\n\t\tpointsArr[ 1 ] = triangle.b;\n\t\tpointsArr[ 2 ] = triangle.c;\n\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = satBounds[ i ];\n\t\t\tconst sa = satAxes[ i ];\n\t\t\tcachedSatBounds.setFromPoints( sa, pointsArr );\n\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t}\n\n\t\tconst triSatBounds = triangle.satBounds;\n\t\tconst triSatAxes = triangle.satAxes;\n\t\tconst points = this.points;\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = triSatBounds[ i ];\n\t\t\tconst sa = triSatAxes[ i ];\n\t\t\tcachedSatBounds.setFromPoints( sa, points );\n\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t}\n\n\t\t// check crossed axes\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sa1 = satAxes[ i ];\n\t\t\tfor ( let i2 = 0; i2 < 4; i2 ++ ) {\n\n\t\t\t\tconst sa2 = triSatAxes[ i2 ];\n\t\t\t\tcachedAxis.crossVectors( sa1, sa2 );\n\t\t\t\tcachedSatBounds.setFromPoints( cachedAxis, pointsArr );\n\t\t\t\tcachedSatBounds2.setFromPoints( cachedAxis, points );\n\t\t\t\tif ( cachedSatBounds.isSeparated( cachedSatBounds2 ) ) return false;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn true;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.closestPointToPoint = ( function () {\n\n\treturn function closestPointToPoint( point, target1 ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\ttarget1\n\t\t\t.copy( point )\n\t\t\t.applyMatrix4( this.invMatrix )\n\t\t\t.clamp( this.min, this.max )\n\t\t\t.applyMatrix4( this.matrix );\n\n\t\treturn target1;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.distanceToPoint = ( function () {\n\n\tconst target = new Vector3();\n\treturn function distanceToPoint( point ) {\n\n\t\tthis.closestPointToPoint( point, target );\n\t\treturn point.distanceTo( target );\n\n\t};\n\n} )();\n\nOrientedBox.prototype.distanceToBox = ( function () {\n\n\tconst xyzFields = [ 'x', 'y', 'z' ];\n\tconst segments1 = new Array( 12 ).fill().map( () => new Line3() );\n\tconst segments2 = new Array( 12 ).fill().map( () => new Line3() );\n\n\tconst point1 = new Vector3();\n\tconst point2 = new Vector3();\n\n\t// early out if we find a value below threshold\n\treturn function distanceToBox( box, threshold = 0, target1 = null, target2 = null ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tif ( this.intersectsBox( box ) ) {\n\n\t\t\tif ( target1 || target2 ) {\n\n\t\t\t\tbox.getCenter( point2 );\n\t\t\t\tthis.closestPointToPoint( point2, point1 );\n\t\t\t\tbox.closestPointToPoint( point1, point2 );\n\n\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t}\n\n\t\t\treturn 0;\n\n\t\t}\n\n\t\tconst threshold2 = threshold * threshold;\n\t\tconst min = box.min;\n\t\tconst max = box.max;\n\t\tconst points = this.points;\n\n\n\t\t// iterate over every edge and compare distances\n\t\tlet closestDistanceSq = Infinity;\n\n\t\t// check over all these points\n\t\tfor ( let i = 0; i < 8; i ++ ) {\n\n\t\t\tconst p = points[ i ];\n\t\t\tpoint2.copy( p ).clamp( min, max );\n\n\t\t\tconst dist = p.distanceToSquared( point2 );\n\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\tif ( target1 ) target1.copy( p );\n\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\tif ( dist < threshold2 ) return Math.sqrt( dist );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// generate and check all line segment distances\n\t\tlet count = 0;\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tfor ( let i1 = 0; i1 <= 1; i1 ++ ) {\n\n\t\t\t\tfor ( let i2 = 0; i2 <= 1; i2 ++ ) {\n\n\t\t\t\t\tconst nextIndex = ( i + 1 ) % 3;\n\t\t\t\t\tconst nextIndex2 = ( i + 2 ) % 3;\n\n\t\t\t\t\t// get obb line segments\n\t\t\t\t\tconst index = i1 << nextIndex | i2 << nextIndex2;\n\t\t\t\t\tconst index2 = 1 << i | i1 << nextIndex | i2 << nextIndex2;\n\t\t\t\t\tconst p1 = points[ index ];\n\t\t\t\t\tconst p2 = points[ index2 ];\n\t\t\t\t\tconst line1 = segments1[ count ];\n\t\t\t\t\tline1.set( p1, p2 );\n\n\n\t\t\t\t\t// get aabb line segments\n\t\t\t\t\tconst f1 = xyzFields[ i ];\n\t\t\t\t\tconst f2 = xyzFields[ nextIndex ];\n\t\t\t\t\tconst f3 = xyzFields[ nextIndex2 ];\n\t\t\t\t\tconst line2 = segments2[ count ];\n\t\t\t\t\tconst start = line2.start;\n\t\t\t\t\tconst end = line2.end;\n\n\t\t\t\t\tstart[ f1 ] = min[ f1 ];\n\t\t\t\t\tstart[ f2 ] = i1 ? min[ f2 ] : max[ f2 ];\n\t\t\t\t\tstart[ f3 ] = i2 ? min[ f3 ] : max[ f2 ];\n\n\t\t\t\t\tend[ f1 ] = max[ f1 ];\n\t\t\t\t\tend[ f2 ] = i1 ? min[ f2 ] : max[ f2 ];\n\t\t\t\t\tend[ f3 ] = i2 ? min[ f3 ] : max[ f2 ];\n\n\t\t\t\t\tcount ++;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t// check all the other boxes point\n\t\tfor ( let x = 0; x <= 1; x ++ ) {\n\n\t\t\tfor ( let y = 0; y <= 1; y ++ ) {\n\n\t\t\t\tfor ( let z = 0; z <= 1; z ++ ) {\n\n\t\t\t\t\tpoint2.x = x ? max.x : min.x;\n\t\t\t\t\tpoint2.y = y ? max.y : min.y;\n\t\t\t\t\tpoint2.z = z ? max.z : min.z;\n\n\t\t\t\t\tthis.closestPointToPoint( point2, point1 );\n\t\t\t\t\tconst dist = point2.distanceToSquared( point1 );\n\t\t\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\t\t\tif ( dist < threshold2 ) return Math.sqrt( dist );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tfor ( let i = 0; i < 12; i ++ ) {\n\n\t\t\tconst l1 = segments1[ i ];\n\t\t\tfor ( let i2 = 0; i2 < 12; i2 ++ ) {\n\n\t\t\t\tconst l2 = segments2[ i2 ];\n\t\t\t\tclosestPointsSegmentToSegment( l1, l2, point1, point2 );\n\t\t\t\tconst dist = point1.distanceToSquared( point2 );\n\t\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\t\tif ( dist < threshold2 ) return Math.sqrt( dist );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Math.sqrt( closestDistanceSq );\n\n\t};\n\n} )();\n", "import { Vector3, Vector2, Triangle, DoubleSide, BackSide } from 'three';\n\n// Ripped and modified From THREE.js Mesh raycast\n// https://github.com/mrdoob/three.js/blob/0aa87c999fe61e216c1133fba7a95772b503eddf/src/objects/Mesh.js#L115\nconst vA = /* @__PURE__ */ new Vector3();\nconst vB = /* @__PURE__ */ new Vector3();\nconst vC = /* @__PURE__ */ new Vector3();\n\nconst uvA = /* @__PURE__ */ new Vector2();\nconst uvB = /* @__PURE__ */ new Vector2();\nconst uvC = /* @__PURE__ */ new Vector2();\n\nconst intersectionPoint = /* @__PURE__ */ new Vector3();\nfunction checkIntersection( ray, pA, pB, pC, point, side ) {\n\n\tlet intersect;\n\tif ( side === BackSide ) {\n\n\t\tintersect = ray.intersectTriangle( pC, pB, pA, true, point );\n\n\t} else {\n\n\t\tintersect = ray.intersectTriangle( pA, pB, pC, side !== DoubleSide, point );\n\n\t}\n\n\tif ( intersect === null ) return null;\n\n\tconst distance = ray.origin.distanceTo( point );\n\n\treturn {\n\n\t\tdistance: distance,\n\t\tpoint: point.clone(),\n\n\t};\n\n}\n\nfunction checkBufferGeometryIntersection( ray, position, uv, a, b, c, side ) {\n\n\tvA.fromBufferAttribute( position, a );\n\tvB.fromBufferAttribute( position, b );\n\tvC.fromBufferAttribute( position, c );\n\n\tconst intersection = checkIntersection( ray, vA, vB, vC, intersectionPoint, side );\n\n\tif ( intersection ) {\n\n\t\tif ( uv ) {\n\n\t\t\tuvA.fromBufferAttribute( uv, a );\n\t\t\tuvB.fromBufferAttribute( uv, b );\n\t\t\tuvC.fromBufferAttribute( uv, c );\n\n\t\t\tintersection.uv = Triangle.getUV( intersectionPoint, vA, vB, vC, uvA, uvB, uvC, new Vector2( ) );\n\n\t\t}\n\n\t\tconst face = {\n\t\t\ta: a,\n\t\t\tb: b,\n\t\t\tc: c,\n\t\t\tnormal: new Vector3(),\n\t\t\tmaterialIndex: 0\n\t\t};\n\n\t\tTriangle.getNormal( vA, vB, vC, face.normal );\n\n\t\tintersection.face = face;\n\t\tintersection.faceIndex = a;\n\n\t}\n\n\treturn intersection;\n\n}\n\n// https://github.com/mrdoob/three.js/blob/0aa87c999fe61e216c1133fba7a95772b503eddf/src/objects/Mesh.js#L258\nfunction intersectTri( geo, side, ray, tri, intersections ) {\n\n\tconst triOffset = tri * 3;\n\tconst a = geo.index.getX( triOffset );\n\tconst b = geo.index.getX( triOffset + 1 );\n\tconst c = geo.index.getX( triOffset + 2 );\n\n\tconst intersection = checkBufferGeometryIntersection( ray, geo.attributes.position, geo.attributes.uv, a, b, c, side );\n\n\tif ( intersection ) {\n\n\t\tintersection.faceIndex = tri;\n\t\tif ( intersections ) intersections.push( intersection );\n\t\treturn intersection;\n\n\t}\n\n\treturn null;\n\n}\n\nexport { intersectTri };\n", "import { intersectTri } from './ThreeRayIntersectUtilities.js';\n\nexport function intersectTris( geo, side, ray, offset, count, intersections ) {\n\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tintersectTri( geo, side, ray, i, intersections );\n\n\t}\n\n}\n\nexport function intersectClosestTri( geo, side, ray, offset, count ) {\n\n\tlet dist = Infinity;\n\tlet res = null;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tconst intersection = intersectTri( geo, side, ray, i );\n\t\tif ( intersection && intersection.distance < dist ) {\n\n\t\t\tres = intersection;\n\t\t\tdist = intersection.distance;\n\n\t\t}\n\n\t}\n\n\treturn res;\n\n}\n\n// converts the given BVH raycast intersection to align with the three.js raycast\n// structure (include object, world space distance and point).\nexport function convertRaycastIntersect( hit, object, raycaster ) {\n\n\tif ( hit === null ) {\n\n\t\treturn null;\n\n\t}\n\n\thit.point.applyMatrix4( object.matrixWorld );\n\thit.distance = hit.point.distanceTo( raycaster.ray.origin );\n\thit.object = object;\n\n\tif ( hit.distance < raycaster.near || hit.distance > raycaster.far ) {\n\n\t\treturn null;\n\n\t} else {\n\n\t\treturn hit;\n\n\t}\n\n}\n", "\nimport { Vector2, Vector3, Triangle } from 'three';\n\n// sets the vertices of triangle `tri` with the 3 vertices after i\nexport function setTriangle( tri, i, index, pos ) {\n\n\tconst ta = tri.a;\n\tconst tb = tri.b;\n\tconst tc = tri.c;\n\n\tlet i0 = i;\n\tlet i1 = i + 1;\n\tlet i2 = i + 2;\n\tif ( index ) {\n\n\t\ti0 = index.getX( i );\n\t\ti1 = index.getX( i + 1 );\n\t\ti2 = index.getX( i + 2 );\n\n\t}\n\n\tta.x = pos.getX( i0 );\n\tta.y = pos.getY( i0 );\n\tta.z = pos.getZ( i0 );\n\n\ttb.x = pos.getX( i1 );\n\ttb.y = pos.getY( i1 );\n\ttb.z = pos.getZ( i1 );\n\n\ttc.x = pos.getX( i2 );\n\ttc.y = pos.getY( i2 );\n\ttc.z = pos.getZ( i2 );\n\n}\n\nexport function iterateOverTriangles(\n\toffset,\n\tcount,\n\tgeometry,\n\tintersectsTriangleFunc,\n\tcontained,\n\tdepth,\n\ttriangle\n) {\n\n\tconst index = geometry.index;\n\tconst pos = geometry.attributes.position;\n\tfor ( let i = offset, l = count + offset; i < l; i ++ ) {\n\n\t\tsetTriangle( triangle, i * 3, index, pos );\n\t\ttriangle.needsUpdate = true;\n\n\t\tif ( intersectsTriangleFunc( triangle, i, contained, depth ) ) {\n\n\t\t\treturn true;\n\n\t\t}\n\n\t}\n\n\treturn false;\n\n}\n\nconst tempV1 = /* @__PURE__ */ new Vector3();\nconst tempV2 = /* @__PURE__ */ new Vector3();\nconst tempV3 = /* @__PURE__ */ new Vector3();\nconst tempUV1 = /* @__PURE__ */ new Vector2();\nconst tempUV2 = /* @__PURE__ */ new Vector2();\nconst tempUV3 = /* @__PURE__ */ new Vector2();\n\nexport function getTriangleHitPointInfo( point, geometry, triangleIndex, target ) {\n\n\tconst indices = geometry.getIndex().array;\n\tconst positions = geometry.getAttribute( 'position' );\n\tconst uvs = geometry.getAttribute( 'uv' );\n\n\tconst a = indices[ triangleIndex * 3 ];\n\tconst b = indices[ triangleIndex * 3 + 1 ];\n\tconst c = indices[ triangleIndex * 3 + 2 ];\n\n\ttempV1.fromBufferAttribute( positions, a );\n\ttempV2.fromBufferAttribute( positions, b );\n\ttempV3.fromBufferAttribute( positions, c );\n\n\t// find the associated material index\n\tlet materialIndex = 0;\n\tconst groups = geometry.groups;\n\tconst firstVertexIndex = triangleIndex * 3;\n\tfor ( let i = 0, l = groups.length; i < l; i ++ ) {\n\n\t\tconst group = groups[ i ];\n\t\tconst { start, count } = group;\n\t\tif ( firstVertexIndex >= start && firstVertexIndex < start + count ) {\n\n\t\t\tmaterialIndex = group.materialIndex;\n\t\t\tbreak;\n\n\t\t}\n\n\t}\n\n\t// extract uvs\n\tlet uv = null;\n\tif ( uvs ) {\n\n\t\ttempUV1.fromBufferAttribute( uvs, a );\n\t\ttempUV2.fromBufferAttribute( uvs, b );\n\t\ttempUV3.fromBufferAttribute( uvs, c );\n\n\t\tif ( target && target.uv ) uv = target.uv;\n\t\telse uv = new Vector2();\n\n\t\tTriangle.getUV( point, tempV1, tempV2, tempV3, tempUV1, tempUV2, tempUV3, uv );\n\n\t}\n\n\t// adjust the provided target or create a new one\n\tif ( target ) {\n\n\t\tif ( ! target.face ) target.face = { };\n\t\ttarget.face.a = a;\n\t\ttarget.face.b = b;\n\t\ttarget.face.c = c;\n\t\ttarget.face.materialIndex = materialIndex;\n\t\tif ( ! target.face.normal ) target.face.normal = new Vector3();\n\t\tTriangle.getNormal( tempV1, tempV2, tempV3, target.face.normal );\n\n\t\tif ( uv ) target.uv = uv;\n\n\t\treturn target;\n\n\t} else {\n\n\t\treturn {\n\t\t\tface: {\n\t\t\t\ta: a,\n\t\t\t\tb: b,\n\t\t\t\tc: c,\n\t\t\t\tmaterialIndex: materialIndex,\n\t\t\t\tnormal: Triangle.getNormal( tempV1, tempV2, tempV3, new Vector3() )\n\t\t\t},\n\t\t\tuv: uv\n\t\t};\n\n\t}\n\n}\n", "export class PrimitivePool {\n\n\tconstructor( getNewPrimitive ) {\n\n\t\tthis._getNewPrimitive = getNewPrimitive;\n\t\tthis._primitives = [];\n\n\t}\n\n\tgetPrimitive() {\n\n\t\tconst primitives = this._primitives;\n\t\tif ( primitives.length === 0 ) {\n\n\t\t\treturn this._getNewPrimitive();\n\n\t\t} else {\n\n\t\t\treturn primitives.pop();\n\n\t\t}\n\n\t}\n\n\treleasePrimitive( primitive ) {\n\n\t\tthis._primitives.push( primitive );\n\n\t}\n\n}\n", "export function IS_LEAF( n16, uint16Array ) {\n\n\treturn uint16Array[ n16 + 15 ] === 0xFFFF;\n\n}\n\nexport function OFFSET( n32, uint32Array ) {\n\n\treturn uint32Array[ n32 + 6 ];\n\n}\n\nexport function COUNT( n16, uint16Array ) {\n\n\treturn uint16Array[ n16 + 14 ];\n\n}\n\nexport function LEFT_NODE( n32 ) {\n\n\treturn n32 + 8;\n\n}\n\nexport function RIGHT_NODE( n32, uint32Array ) {\n\n\treturn uint32Array[ n32 + 6 ];\n\n}\n\nexport function SPLIT_AXIS( n32, uint32Array ) {\n\n\treturn uint32Array[ n32 + 7 ];\n\n}\n\nexport function BOUNDING_DATA_INDEX( n32 ) {\n\n\treturn n32;\n\n}\n", "import { Box3, Vector3, Matrix4 } from 'three';\nimport { CONTAINED } from './Constants.js';\n\nimport { OrientedBox } from '../math/OrientedBox.js';\nimport { ExtendedTriangle } from '../math/ExtendedTriangle.js';\nimport { intersectTris, intersectClosestTri } from '../utils/GeometryRayIntersectUtilities.js';\nimport { setTriangle } from '../utils/TriangleUtilities.js';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nimport { PrimitivePool } from '../utils/PrimitivePool.js';\nimport { COUNT, OFFSET, LEFT_NODE, RIGHT_NODE, IS_LEAF, BOUNDING_DATA_INDEX, SPLIT_AXIS } from './nodeBufferFunctions.js';\n\nconst boundingBox = new Box3();\nconst boxIntersection = new Vector3();\nconst xyzFields = [ 'x', 'y', 'z' ];\n\nexport function raycast( nodeIndex32, geometry, side, ray, intersects ) {\n\n\tlet nodeIndex16 = nodeIndex32 * 2, float32Array = _float32Array, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\t\tintersectTris( geometry, side, ray, offset, count, intersects );\n\n\t} else {\n\n\t\tconst leftIndex = LEFT_NODE( nodeIndex32 );\n\t\tif ( intersectRay( leftIndex, float32Array, ray, boxIntersection ) ) {\n\n\t\t\traycast( leftIndex, geometry, side, ray, intersects );\n\n\t\t}\n\n\t\tconst rightIndex = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\tif ( intersectRay( rightIndex, float32Array, ray, boxIntersection ) ) {\n\n\t\t\traycast( rightIndex, geometry, side, ray, intersects );\n\n\t\t}\n\n\t}\n\n}\n\nexport function raycastFirst( nodeIndex32, geometry, side, ray ) {\n\n\tlet nodeIndex16 = nodeIndex32 * 2, float32Array = _float32Array, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\t\treturn intersectClosestTri( geometry, side, ray, offset, count );\n\n\t} else {\n\n\t\t// consider the position of the split plane with respect to the oncoming ray; whichever direction\n\t\t// the ray is coming from, look for an intersection among that side of the tree first\n\t\tconst splitAxis = SPLIT_AXIS( nodeIndex32, uint32Array );\n\t\tconst xyzAxis = xyzFields[ splitAxis ];\n\t\tconst rayDir = ray.direction[ xyzAxis ];\n\t\tconst leftToRight = rayDir >= 0;\n\n\t\t// c1 is the child to check first\n\t\tlet c1, c2;\n\t\tif ( leftToRight ) {\n\n\t\t\tc1 = LEFT_NODE( nodeIndex32 );\n\t\t\tc2 = RIGHT_NODE( nodeIndex32, uint32Array );\n\n\t\t} else {\n\n\t\t\tc1 = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\t\tc2 = LEFT_NODE( nodeIndex32 );\n\n\t\t}\n\n\t\tconst c1Intersection = intersectRay( c1, float32Array, ray, boxIntersection );\n\t\tconst c1Result = c1Intersection ? raycastFirst( c1, geometry, side, ray ) : null;\n\n\t\t// if we got an intersection in the first node and it's closer than the second node's bounding\n\t\t// box, we don't need to consider the second node because it couldn't possibly be a better result\n\t\tif ( c1Result ) {\n\n\t\t\t// check if the point is within the second bounds\n\t\t\t// \"point\" is in the local frame of the bvh\n\t\t\tconst point = c1Result.point[ xyzAxis ];\n\t\t\tconst isOutside = leftToRight ?\n\t\t\t\tpoint <= float32Array[ c2 + splitAxis ] : // min bounding data\n\t\t\t\tpoint >= float32Array[ c2 + splitAxis + 3 ]; // max bounding data\n\n\t\t\tif ( isOutside ) {\n\n\t\t\t\treturn c1Result;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// either there was no intersection in the first node, or there could still be a closer\n\t\t// intersection in the second, so check the second node and then take the better of the two\n\t\tconst c2Intersection = intersectRay( c2, float32Array, ray, boxIntersection );\n\t\tconst c2Result = c2Intersection ? raycastFirst( c2, geometry, side, ray ) : null;\n\n\t\tif ( c1Result && c2Result ) {\n\n\t\t\treturn c1Result.distance <= c2Result.distance ? c1Result : c2Result;\n\n\t\t} else {\n\n\t\t\treturn c1Result || c2Result || null;\n\n\t\t}\n\n\t}\n\n}\n\nexport const shapecast = ( function () {\n\n\tlet _box1, _box2;\n\tconst boxStack = [];\n\tconst boxPool = new PrimitivePool( () => new Box3() );\n\n\treturn function shapecast( ...args ) {\n\n\t\t_box1 = boxPool.getPrimitive();\n\t\t_box2 = boxPool.getPrimitive();\n\t\tboxStack.push( _box1, _box2 );\n\n\t\tconst result = shapecastTraverse( ...args );\n\n\t\tboxPool.releasePrimitive( _box1 );\n\t\tboxPool.releasePrimitive( _box2 );\n\t\tboxStack.pop();\n\t\tboxStack.pop();\n\n\t\tconst length = boxStack.length;\n\t\tif ( length > 0 ) {\n\n\t\t\t_box2 = boxStack[ length - 1 ];\n\t\t\t_box1 = boxStack[ length - 2 ];\n\n\t\t}\n\n\t\treturn result;\n\n\t};\n\n\tfunction shapecastTraverse(\n\t\tnodeIndex32,\n\t\tgeometry,\n\t\tintersectsBoundsFunc,\n\t\tintersectsRangeFunc,\n\t\tnodeScoreFunc = null,\n\t\tnodeIndexByteOffset = 0, // offset for unique node identifier\n\t\tdepth = 0\n\t) {\n\n\t\t// Define these inside the function so it has access to the local variables needed\n\t\t// when converting to the buffer equivalents\n\t\tfunction getLeftOffset( nodeIndex32 ) {\n\n\t\t\tlet nodeIndex16 = nodeIndex32 * 2, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\t\t\t// traverse until we find a leaf\n\t\t\twhile ( ! IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\t\tnodeIndex32 = LEFT_NODE( nodeIndex32 );\n\t\t\t\tnodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t}\n\n\t\t\treturn OFFSET( nodeIndex32, uint32Array );\n\n\t\t}\n\n\t\tfunction getRightEndOffset( nodeIndex32 ) {\n\n\t\t\tlet nodeIndex16 = nodeIndex32 * 2, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\t\t\t// traverse until we find a leaf\n\t\t\twhile ( ! IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\t\t// adjust offset to point to the right node\n\t\t\t\tnodeIndex32 = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\t\t\tnodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t}\n\n\t\t\t// return the end offset of the triangle range\n\t\t\treturn OFFSET( nodeIndex32, uint32Array ) + COUNT( nodeIndex16, uint16Array );\n\n\t\t}\n\n\t\tlet nodeIndex16 = nodeIndex32 * 2, float32Array = _float32Array, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\t\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\t\tif ( isLeaf ) {\n\n\t\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( nodeIndex32 ), float32Array, _box1 );\n\t\t\treturn intersectsRangeFunc( offset, count, false, depth, nodeIndexByteOffset + nodeIndex32, _box1 );\n\n\t\t} else {\n\n\t\t\tconst left = LEFT_NODE( nodeIndex32 );\n\t\t\tconst right = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\t\tlet c1 = left;\n\t\t\tlet c2 = right;\n\n\t\t\tlet score1, score2;\n\t\t\tlet box1, box2;\n\t\t\tif ( nodeScoreFunc ) {\n\n\t\t\t\tbox1 = _box1;\n\t\t\t\tbox2 = _box2;\n\n\t\t\t\t// bounding data is not offset\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c1 ), float32Array, box1 );\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c2 ), float32Array, box2 );\n\n\t\t\t\tscore1 = nodeScoreFunc( box1 );\n\t\t\t\tscore2 = nodeScoreFunc( box2 );\n\n\t\t\t\tif ( score2 < score1 ) {\n\n\t\t\t\t\tc1 = right;\n\t\t\t\t\tc2 = left;\n\n\t\t\t\t\tconst temp = score1;\n\t\t\t\t\tscore1 = score2;\n\t\t\t\t\tscore2 = temp;\n\n\t\t\t\t\tbox1 = box2;\n\t\t\t\t\t// box2 is always set before use below\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// Check box 1 intersection\n\t\t\tif ( ! box1 ) {\n\n\t\t\t\tbox1 = _box1;\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c1 ), float32Array, box1 );\n\n\t\t\t}\n\n\t\t\tconst isC1Leaf = IS_LEAF( c1 * 2, uint16Array );\n\t\t\tconst c1Intersection = intersectsBoundsFunc( box1, isC1Leaf, score1, depth + 1, nodeIndexByteOffset + c1 );\n\n\t\t\tlet c1StopTraversal;\n\t\t\tif ( c1Intersection === CONTAINED ) {\n\n\t\t\t\tconst offset = getLeftOffset( c1 );\n\t\t\t\tconst end = getRightEndOffset( c1 );\n\t\t\t\tconst count = end - offset;\n\n\t\t\t\tc1StopTraversal = intersectsRangeFunc( offset, count, true, depth + 1, nodeIndexByteOffset + c1, box1 );\n\n\t\t\t} else {\n\n\t\t\t\tc1StopTraversal =\n\t\t\t\t\tc1Intersection &&\n\t\t\t\t\tshapecastTraverse(\n\t\t\t\t\t\tc1,\n\t\t\t\t\t\tgeometry,\n\t\t\t\t\t\tintersectsBoundsFunc,\n\t\t\t\t\t\tintersectsRangeFunc,\n\t\t\t\t\t\tnodeScoreFunc,\n\t\t\t\t\t\tnodeIndexByteOffset,\n\t\t\t\t\t\tdepth + 1\n\t\t\t\t\t);\n\n\t\t\t}\n\n\t\t\tif ( c1StopTraversal ) return true;\n\n\t\t\t// Check box 2 intersection\n\t\t\t// cached box2 will have been overwritten by previous traversal\n\t\t\tbox2 = _box2;\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c2 ), float32Array, box2 );\n\n\t\t\tconst isC2Leaf = IS_LEAF( c2 * 2, uint16Array );\n\t\t\tconst c2Intersection = intersectsBoundsFunc( box2, isC2Leaf, score2, depth + 1, nodeIndexByteOffset + c2 );\n\n\t\t\tlet c2StopTraversal;\n\t\t\tif ( c2Intersection === CONTAINED ) {\n\n\t\t\t\tconst offset = getLeftOffset( c2 );\n\t\t\t\tconst end = getRightEndOffset( c2 );\n\t\t\t\tconst count = end - offset;\n\n\t\t\t\tc2StopTraversal = intersectsRangeFunc( offset, count, true, depth + 1, nodeIndexByteOffset + c2, box2 );\n\n\t\t\t} else {\n\n\t\t\t\tc2StopTraversal =\n\t\t\t\t\tc2Intersection &&\n\t\t\t\t\tshapecastTraverse(\n\t\t\t\t\t\tc2,\n\t\t\t\t\t\tgeometry,\n\t\t\t\t\t\tintersectsBoundsFunc,\n\t\t\t\t\t\tintersectsRangeFunc,\n\t\t\t\t\t\tnodeScoreFunc,\n\t\t\t\t\t\tnodeIndexByteOffset,\n\t\t\t\t\t\tdepth + 1\n\t\t\t\t\t);\n\n\t\t\t}\n\n\t\t\tif ( c2StopTraversal ) return true;\n\n\t\t\treturn false;\n\n\t\t}\n\n\t}\n\n} )();\n\nexport const intersectsGeometry = ( function () {\n\n\tconst triangle = new ExtendedTriangle();\n\tconst triangle2 = new ExtendedTriangle();\n\tconst invertedMat = new Matrix4();\n\n\tconst obb = new OrientedBox();\n\tconst obb2 = new OrientedBox();\n\n\treturn function intersectsGeometry( nodeIndex32, geometry, otherGeometry, geometryToBvh, cachedObb = null ) {\n\n\t\tlet nodeIndex16 = nodeIndex32 * 2, float32Array = _float32Array, uint16Array = _uint16Array, uint32Array = _uint32Array;\n\n\t\tif ( cachedObb === null ) {\n\n\t\t\tif ( ! otherGeometry.boundingBox ) {\n\n\t\t\t\totherGeometry.computeBoundingBox();\n\n\t\t\t}\n\n\t\t\tobb.set( otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh );\n\t\t\tcachedObb = obb;\n\n\t\t}\n\n\t\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\t\tif ( isLeaf ) {\n\n\t\t\tconst thisGeometry = geometry;\n\t\t\tconst thisIndex = thisGeometry.index;\n\t\t\tconst thisPos = thisGeometry.attributes.position;\n\n\t\t\tconst index = otherGeometry.index;\n\t\t\tconst pos = otherGeometry.attributes.position;\n\n\t\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\t\t\t// get the inverse of the geometry matrix so we can transform our triangles into the\n\t\t\t// geometry space we're trying to test. We assume there are fewer triangles being checked\n\t\t\t// here.\n\t\t\tinvertedMat.copy( geometryToBvh ).invert();\n\n\t\t\tif ( otherGeometry.boundsTree ) {\n\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( nodeIndex32 ), float32Array, obb2 );\n\t\t\t\tobb2.matrix.copy( invertedMat );\n\t\t\t\tobb2.needsUpdate = true;\n\n\t\t\t\tconst res = otherGeometry.boundsTree.shapecast( {\n\n\t\t\t\t\tintersectsBounds: box => obb2.intersectsBox( box ),\n\n\t\t\t\t\tintersectsTriangle: tri => {\n\n\t\t\t\t\t\ttri.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttri.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttri.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttri.needsUpdate = true;\n\n\t\t\t\t\t\tfor ( let i = offset * 3, l = ( count + offset ) * 3; i < l; i += 3 ) {\n\n\t\t\t\t\t\t\t// this triangle needs to be transformed into the current BVH coordinate frame\n\t\t\t\t\t\t\tsetTriangle( triangle2, i, thisIndex, thisPos );\n\t\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\t\t\t\t\t\t\tif ( tri.intersectsTriangle( triangle2 ) ) {\n\n\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn false;\n\n\t\t\t\t\t}\n\n\t\t\t\t} );\n\n\t\t\t\treturn res;\n\n\t\t\t} else {\n\n\t\t\t\tfor ( let i = offset * 3, l = ( count + offset * 3 ); i < l; i += 3 ) {\n\n\t\t\t\t\t// this triangle needs to be transformed into the current BVH coordinate frame\n\t\t\t\t\tsetTriangle( triangle, i, thisIndex, thisPos );\n\t\t\t\t\ttriangle.a.applyMatrix4( invertedMat );\n\t\t\t\t\ttriangle.b.applyMatrix4( invertedMat );\n\t\t\t\t\ttriangle.c.applyMatrix4( invertedMat );\n\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\tfor ( let i2 = 0, l2 = index.count; i2 < l2; i2 += 3 ) {\n\n\t\t\t\t\t\tsetTriangle( triangle2, i2, index, pos );\n\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\tif ( triangle.intersectsTriangle( triangle2 ) ) {\n\n\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconst left = nodeIndex32 + 8;\n\t\t\tconst right = uint32Array[ nodeIndex32 + 6 ];\n\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( left ), float32Array, boundingBox );\n\t\t\tconst leftIntersection =\n\t\t\t\tcachedObb.intersectsBox( boundingBox ) &&\n\t\t\t\tintersectsGeometry( left, geometry, otherGeometry, geometryToBvh, cachedObb );\n\n\t\t\tif ( leftIntersection ) return true;\n\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( right ), float32Array, boundingBox );\n\t\t\tconst rightIntersection =\n\t\t\t\tcachedObb.intersectsBox( boundingBox ) &&\n\t\t\t\tintersectsGeometry( right, geometry, otherGeometry, geometryToBvh, cachedObb );\n\n\t\t\tif ( rightIntersection ) return true;\n\n\t\t\treturn false;\n\n\t\t}\n\n\t};\n\n} )();\n\nfunction intersectRay( nodeIndex32, array, ray, target ) {\n\n\tarrayToBox( nodeIndex32, array, boundingBox );\n\treturn ray.intersectBox( boundingBox, target );\n\n}\n\nconst bufferStack = [];\nlet _prevBuffer;\nlet _float32Array;\nlet _uint16Array;\nlet _uint32Array;\nexport function setBuffer( buffer ) {\n\n\tif ( _prevBuffer ) {\n\n\t\tbufferStack.push( _prevBuffer );\n\n\t}\n\n\t_prevBuffer = buffer;\n\t_float32Array = new Float32Array( buffer );\n\t_uint16Array = new Uint16Array( buffer );\n\t_uint32Array = new Uint32Array( buffer );\n\n}\n\nexport function clearBuffer() {\n\n\t_prevBuffer = null;\n\t_float32Array = null;\n\t_uint16Array = null;\n\t_uint32Array = null;\n\n\tif ( bufferStack.length ) {\n\n\t\tsetBuffer( bufferStack.pop() );\n\n\t}\n\n}\n", "import { Vector3, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Matrix4 } from 'three';\nimport { CENTER, BYTES_PER_NODE, IS_LEAFNODE_FLAG } from './Constants.js';\nimport { buildPackedTree } from './buildFunctions.js';\nimport {\n\traycast,\n\traycastFirst,\n\tshapecast,\n\tintersectsGeometry,\n\tsetBuffer,\n\tclearBuffer,\n} from './castFunctions.js';\nimport { OrientedBox } from '../math/OrientedBox.js';\nimport { ExtendedTriangle } from '../math/ExtendedTriangle.js';\nimport { PrimitivePool } from '../utils/PrimitivePool.js';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nimport { iterateOverTriangles, setTriangle } from '../utils/TriangleUtilities.js';\n\nconst SKIP_GENERATION = Symbol( 'skip tree generation' );\n\nconst aabb = /* @__PURE__ */ new Box3();\nconst aabb2 = /* @__PURE__ */ new Box3();\nconst tempMatrix = /* @__PURE__ */ new Matrix4();\nconst obb = /* @__PURE__ */ new OrientedBox();\nconst obb2 = /* @__PURE__ */ new OrientedBox();\nconst temp = /* @__PURE__ */ new Vector3();\nconst temp1 = /* @__PURE__ */ new Vector3();\nconst temp2 = /* @__PURE__ */ new Vector3();\nconst temp3 = /* @__PURE__ */ new Vector3();\nconst temp4 = /* @__PURE__ */ new Vector3();\nconst tempBox = /* @__PURE__ */ new Box3();\nconst trianglePool = /* @__PURE__ */ new PrimitivePool( () => new ExtendedTriangle() );\n\nexport class MeshBVH {\n\n\tstatic serialize( bvh, options = {} ) {\n\n\t\tif ( options.isBufferGeometry ) {\n\n\t\t\tconsole.warn( 'MeshBVH.serialize: The arguments for the function have changed. See documentation for new signature.' );\n\n\t\t\treturn MeshBVH.serialize(\n\t\t\t\targuments[ 0 ],\n\t\t\t\t{\n\t\t\t\t\tcloneBuffers: arguments[ 2 ] === undefined ? true : arguments[ 2 ],\n\t\t\t\t}\n\t\t\t);\n\n\t\t}\n\n\t\toptions = {\n\t\t\tcloneBuffers: true,\n\t\t\t...options,\n\t\t};\n\n\t\tconst geometry = bvh.geometry;\n\t\tconst rootData = bvh._roots;\n\t\tconst indexAttribute = geometry.getIndex();\n\t\tlet result;\n\t\tif ( options.cloneBuffers ) {\n\n\t\t\tresult = {\n\t\t\t\troots: rootData.map( root => root.slice() ),\n\t\t\t\tindex: indexAttribute.array.slice(),\n\t\t\t};\n\n\t\t} else {\n\n\t\t\tresult = {\n\t\t\t\troots: rootData,\n\t\t\t\tindex: indexAttribute.array,\n\t\t\t};\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n\tstatic deserialize( data, geometry, options = {} ) {\n\n\t\tif ( typeof options === 'boolean' ) {\n\n\t\t\tconsole.warn( 'MeshBVH.deserialize: The arguments for the function have changed. See documentation for new signature.' );\n\n\t\t\treturn MeshBVH.deserialize(\n\t\t\t\targuments[ 0 ],\n\t\t\t\targuments[ 1 ],\n\t\t\t\t{\n\t\t\t\t\tsetIndex: arguments[ 2 ] === undefined ? true : arguments[ 2 ],\n\t\t\t\t}\n\t\t\t);\n\n\t\t}\n\n\t\toptions = {\n\t\t\tsetIndex: true,\n\t\t\t...options,\n\t\t};\n\n\t\tconst { index, roots } = data;\n\t\tconst bvh = new MeshBVH( geometry, { ...options, [ SKIP_GENERATION ]: true } );\n\t\tbvh._roots = roots;\n\n\t\tif ( options.setIndex ) {\n\n\t\t\tconst indexAttribute = geometry.getIndex();\n\t\t\tif ( indexAttribute === null ) {\n\n\t\t\t\tconst newIndex = new BufferAttribute( data.index, 1, false );\n\t\t\t\tgeometry.setIndex( newIndex );\n\n\t\t\t} else if ( indexAttribute.array !== index ) {\n\n\t\t\t\tindexAttribute.array.set( index );\n\t\t\t\tindexAttribute.needsUpdate = true;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn bvh;\n\n\t}\n\n\tconstructor( geometry, options = {} ) {\n\n\t\tif ( ! geometry.isBufferGeometry ) {\n\n\t\t\tthrow new Error( 'MeshBVH: Only BufferGeometries are supported.' );\n\n\t\t} else if ( geometry.index && geometry.index.isInterleavedBufferAttribute ) {\n\n\t\t\tthrow new Error( 'MeshBVH: InterleavedBufferAttribute is not supported for the index attribute.' );\n\n\t\t}\n\n\t\t// default options\n\t\toptions = Object.assign( {\n\n\t\t\tstrategy: CENTER,\n\t\t\tmaxDepth: 40,\n\t\t\tmaxLeafTris: 10,\n\t\t\tverbose: true,\n\t\t\tuseSharedArrayBuffer: false,\n\t\t\tsetBoundingBox: true,\n\t\t\tonProgress: null,\n\n\t\t\t// undocumented options\n\n\t\t\t// Whether to skip generating the tree. Used for deserialization.\n\t\t\t[ SKIP_GENERATION ]: false,\n\n\t\t}, options );\n\n\t\tif ( options.useSharedArrayBuffer && typeof SharedArrayBuffer === 'undefined' ) {\n\n\t\t\tthrow new Error( 'MeshBVH: SharedArrayBuffer is not available.' );\n\n\t\t}\n\n\t\tthis._roots = null;\n\t\tif ( ! options[ SKIP_GENERATION ] ) {\n\n\t\t\tthis._roots = buildPackedTree( geometry, options );\n\n\t\t\tif ( ! geometry.boundingBox && options.setBoundingBox ) {\n\n\t\t\t\tgeometry.boundingBox = this.getBoundingBox( new Box3() );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// retain references to the geometry so we can use them it without having to\n\t\t// take a geometry reference in every function.\n\t\tthis.geometry = geometry;\n\n\t}\n\n\trefit( nodeIndices = null ) {\n\n\t\tif ( nodeIndices && Array.isArray( nodeIndices ) ) {\n\n\t\t\tnodeIndices = new Set( nodeIndices );\n\n\t\t}\n\n\t\tconst geometry = this.geometry;\n\t\tconst indexArr = geometry.index.array;\n\t\tconst posAttr = geometry.attributes.position;\n\n\t\tlet buffer, uint32Array, uint16Array, float32Array;\n\t\tlet byteOffset = 0;\n\t\tconst roots = this._roots;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tbuffer = roots[ i ];\n\t\t\tuint32Array = new Uint32Array( buffer );\n\t\t\tuint16Array = new Uint16Array( buffer );\n\t\t\tfloat32Array = new Float32Array( buffer );\n\n\t\t\t_traverse( 0, byteOffset );\n\t\t\tbyteOffset += buffer.byteLength;\n\n\t\t}\n\n\t\tfunction _traverse( node32Index, byteOffset, force = false ) {\n\n\t\t\tconst node16Index = node32Index * 2;\n\t\t\tconst isLeaf = uint16Array[ node16Index + 15 ] === IS_LEAFNODE_FLAG;\n\t\t\tif ( isLeaf ) {\n\n\t\t\t\tconst offset = uint32Array[ node32Index + 6 ];\n\t\t\t\tconst count = uint16Array[ node16Index + 14 ];\n\n\t\t\t\tlet minx = Infinity;\n\t\t\t\tlet miny = Infinity;\n\t\t\t\tlet minz = Infinity;\n\t\t\t\tlet maxx = - Infinity;\n\t\t\t\tlet maxy = - Infinity;\n\t\t\t\tlet maxz = - Infinity;\n\n\t\t\t\tfor ( let i = 3 * offset, l = 3 * ( offset + count ); i < l; i ++ ) {\n\n\t\t\t\t\tconst index = indexArr[ i ];\n\t\t\t\t\tconst x = posAttr.getX( index );\n\t\t\t\t\tconst y = posAttr.getY( index );\n\t\t\t\t\tconst z = posAttr.getZ( index );\n\n\t\t\t\t\tif ( x < minx ) minx = x;\n\t\t\t\t\tif ( x > maxx ) maxx = x;\n\n\t\t\t\t\tif ( y < miny ) miny = y;\n\t\t\t\t\tif ( y > maxy ) maxy = y;\n\n\t\t\t\t\tif ( z < minz ) minz = z;\n\t\t\t\t\tif ( z > maxz ) maxz = z;\n\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\tfloat32Array[ node32Index + 0 ] !== minx ||\n\t\t\t\t\tfloat32Array[ node32Index + 1 ] !== miny ||\n\t\t\t\t\tfloat32Array[ node32Index + 2 ] !== minz ||\n\n\t\t\t\t\tfloat32Array[ node32Index + 3 ] !== maxx ||\n\t\t\t\t\tfloat32Array[ node32Index + 4 ] !== maxy ||\n\t\t\t\t\tfloat32Array[ node32Index + 5 ] !== maxz\n\t\t\t\t) {\n\n\t\t\t\t\tfloat32Array[ node32Index + 0 ] = minx;\n\t\t\t\t\tfloat32Array[ node32Index + 1 ] = miny;\n\t\t\t\t\tfloat32Array[ node32Index + 2 ] = minz;\n\n\t\t\t\t\tfloat32Array[ node32Index + 3 ] = maxx;\n\t\t\t\t\tfloat32Array[ node32Index + 4 ] = maxy;\n\t\t\t\t\tfloat32Array[ node32Index + 5 ] = maxz;\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t} else {\n\n\t\t\t\t\treturn false;\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tconst left = node32Index + 8;\n\t\t\t\tconst right = uint32Array[ node32Index + 6 ];\n\n\t\t\t\t// the identifying node indices provided by the shapecast function include offsets of all\n\t\t\t\t// root buffers to guarantee they're unique between roots so offset left and right indices here.\n\t\t\t\tconst offsetLeft = left + byteOffset;\n\t\t\t\tconst offsetRight = right + byteOffset;\n\t\t\t\tlet forceChildren = force;\n\t\t\t\tlet includesLeft = false;\n\t\t\t\tlet includesRight = false;\n\n\t\t\t\tif ( nodeIndices ) {\n\n\t\t\t\t\t// if we see that neither the left or right child are included in the set that need to be updated\n\t\t\t\t\t// then we assume that all children need to be updated.\n\t\t\t\t\tif ( ! forceChildren ) {\n\n\t\t\t\t\t\tincludesLeft = nodeIndices.has( offsetLeft );\n\t\t\t\t\t\tincludesRight = nodeIndices.has( offsetRight );\n\t\t\t\t\t\tforceChildren = ! includesLeft && ! includesRight;\n\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\n\t\t\t\t\tincludesLeft = true;\n\t\t\t\t\tincludesRight = true;\n\n\t\t\t\t}\n\n\t\t\t\tconst traverseLeft = forceChildren || includesLeft;\n\t\t\t\tconst traverseRight = forceChildren || includesRight;\n\n\t\t\t\tlet leftChange = false;\n\t\t\t\tif ( traverseLeft ) {\n\n\t\t\t\t\tleftChange = _traverse( left, byteOffset, forceChildren );\n\n\t\t\t\t}\n\n\t\t\t\tlet rightChange = false;\n\t\t\t\tif ( traverseRight ) {\n\n\t\t\t\t\trightChange = _traverse( right, byteOffset, forceChildren );\n\n\t\t\t\t}\n\n\t\t\t\tconst didChange = leftChange || rightChange;\n\t\t\t\tif ( didChange ) {\n\n\t\t\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\t\t\tconst lefti = left + i;\n\t\t\t\t\t\tconst righti = right + i;\n\t\t\t\t\t\tconst minLeftValue = float32Array[ lefti ];\n\t\t\t\t\t\tconst maxLeftValue = float32Array[ lefti + 3 ];\n\t\t\t\t\t\tconst minRightValue = float32Array[ righti ];\n\t\t\t\t\t\tconst maxRightValue = float32Array[ righti + 3 ];\n\n\t\t\t\t\t\tfloat32Array[ node32Index + i ] = minLeftValue < minRightValue ? minLeftValue : minRightValue;\n\t\t\t\t\t\tfloat32Array[ node32Index + i + 3 ] = maxLeftValue > maxRightValue ? maxLeftValue : maxRightValue;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\treturn didChange;\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\ttraverse( callback, rootIndex = 0 ) {\n\n\t\tconst buffer = this._roots[ rootIndex ];\n\t\tconst uint32Array = new Uint32Array( buffer );\n\t\tconst uint16Array = new Uint16Array( buffer );\n\t\t_traverse( 0 );\n\n\t\tfunction _traverse( node32Index, depth = 0 ) {\n\n\t\t\tconst node16Index = node32Index * 2;\n\t\t\tconst isLeaf = uint16Array[ node16Index + 15 ] === IS_LEAFNODE_FLAG;\n\t\t\tif ( isLeaf ) {\n\n\t\t\t\tconst offset = uint32Array[ node32Index + 6 ];\n\t\t\t\tconst count = uint16Array[ node16Index + 14 ];\n\t\t\t\tcallback( depth, isLeaf, new Float32Array( buffer, node32Index * 4, 6 ), offset, count );\n\n\t\t\t} else {\n\n\t\t\t\t// TODO: use node functions here\n\t\t\t\tconst left = node32Index + BYTES_PER_NODE / 4;\n\t\t\t\tconst right = uint32Array[ node32Index + 6 ];\n\t\t\t\tconst splitAxis = uint32Array[ node32Index + 7 ];\n\t\t\t\tconst stopTraversal = callback( depth, isLeaf, new Float32Array( buffer, node32Index * 4, 6 ), splitAxis );\n\n\t\t\t\tif ( ! stopTraversal ) {\n\n\t\t\t\t\t_traverse( left, depth + 1 );\n\t\t\t\t\t_traverse( right, depth + 1 );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/* Core Cast Functions */\n\traycast( ray, materialOrSide = FrontSide ) {\n\n\t\tconst roots = this._roots;\n\t\tconst geometry = this.geometry;\n\t\tconst intersects = [];\n\t\tconst isMaterial = materialOrSide.isMaterial;\n\t\tconst isArrayMaterial = Array.isArray( materialOrSide );\n\n\t\tconst groups = geometry.groups;\n\t\tconst side = isMaterial ? materialOrSide.side : materialOrSide;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tconst materialSide = isArrayMaterial ? materialOrSide[ groups[ i ].materialIndex ].side : side;\n\t\t\tconst startCount = intersects.length;\n\n\t\t\tsetBuffer( roots[ i ] );\n\t\t\traycast( 0, geometry, materialSide, ray, intersects );\n\t\t\tclearBuffer();\n\n\t\t\tif ( isArrayMaterial ) {\n\n\t\t\t\tconst materialIndex = groups[ i ].materialIndex;\n\t\t\t\tfor ( let j = startCount, jl = intersects.length; j < jl; j ++ ) {\n\n\t\t\t\t\tintersects[ j ].face.materialIndex = materialIndex;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn intersects;\n\n\t}\n\n\traycastFirst( ray, materialOrSide = FrontSide ) {\n\n\t\tconst roots = this._roots;\n\t\tconst geometry = this.geometry;\n\t\tconst isMaterial = materialOrSide.isMaterial;\n\t\tconst isArrayMaterial = Array.isArray( materialOrSide );\n\n\t\tlet closestResult = null;\n\n\t\tconst groups = geometry.groups;\n\t\tconst side = isMaterial ? materialOrSide.side : materialOrSide;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tconst materialSide = isArrayMaterial ? materialOrSide[ groups[ i ].materialIndex ].side : side;\n\n\t\t\tsetBuffer( roots[ i ] );\n\t\t\tconst result = raycastFirst( 0, geometry, materialSide, ray );\n\t\t\tclearBuffer();\n\n\t\t\tif ( result != null && ( closestResult == null || result.distance < closestResult.distance ) ) {\n\n\t\t\t\tclosestResult = result;\n\t\t\t\tif ( isArrayMaterial ) {\n\n\t\t\t\t\tresult.face.materialIndex = groups[ i ].materialIndex;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn closestResult;\n\n\t}\n\n\tintersectsGeometry( otherGeometry, geomToMesh ) {\n\n\t\tconst geometry = this.geometry;\n\t\tlet result = false;\n\t\tfor ( const root of this._roots ) {\n\n\t\t\tsetBuffer( root );\n\t\t\tresult = intersectsGeometry( 0, geometry, otherGeometry, geomToMesh );\n\t\t\tclearBuffer();\n\n\t\t\tif ( result ) {\n\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n\tshapecast( callbacks, _intersectsTriangleFunc, _orderNodesFunc ) {\n\n\t\tconst geometry = this.geometry;\n\t\tif ( callbacks instanceof Function ) {\n\n\t\t\tif ( _intersectsTriangleFunc ) {\n\n\t\t\t\t// Support the previous function signature that provided three sequential index buffer\n\t\t\t\t// indices here.\n\t\t\t\tconst originalTriangleFunc = _intersectsTriangleFunc;\n\t\t\t\t_intersectsTriangleFunc = ( tri, index, contained, depth ) => {\n\n\t\t\t\t\tconst i3 = index * 3;\n\t\t\t\t\treturn originalTriangleFunc( tri, i3, i3 + 1, i3 + 2, contained, depth );\n\n\t\t\t\t};\n\n\n\t\t\t}\n\n\t\t\tcallbacks = {\n\n\t\t\t\tboundsTraverseOrder: _orderNodesFunc,\n\t\t\t\tintersectsBounds: callbacks,\n\t\t\t\tintersectsTriangle: _intersectsTriangleFunc,\n\t\t\t\tintersectsRange: null,\n\n\t\t\t};\n\n\t\t\tconsole.warn( 'MeshBVH: Shapecast function signature has changed and now takes an object of callbacks as a second argument. See docs for new signature.' );\n\n\t\t}\n\n\t\tconst triangle = trianglePool.getPrimitive();\n\t\tlet {\n\t\t\tboundsTraverseOrder,\n\t\t\tintersectsBounds,\n\t\t\tintersectsRange,\n\t\t\tintersectsTriangle,\n\t\t} = callbacks;\n\n\t\tif ( intersectsRange && intersectsTriangle ) {\n\n\t\t\tconst originalIntersectsRange = intersectsRange;\n\t\t\tintersectsRange = ( offset, count, contained, depth, nodeIndex ) => {\n\n\t\t\t\tif ( ! originalIntersectsRange( offset, count, contained, depth, nodeIndex ) ) {\n\n\t\t\t\t\treturn iterateOverTriangles( offset, count, geometry, intersectsTriangle, contained, depth, triangle );\n\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\n\t\t\t};\n\n\t\t} else if ( ! intersectsRange ) {\n\n\t\t\tif ( intersectsTriangle ) {\n\n\t\t\t\tintersectsRange = ( offset, count, contained, depth ) => {\n\n\t\t\t\t\treturn iterateOverTriangles( offset, count, geometry, intersectsTriangle, contained, depth, triangle );\n\n\t\t\t\t};\n\n\t\t\t} else {\n\n\t\t\t\tintersectsRange = ( offset, count, contained ) => {\n\n\t\t\t\t\treturn contained;\n\n\t\t\t\t};\n\n\t\t\t}\n\n\t\t}\n\n\t\tlet result = false;\n\t\tlet byteOffset = 0;\n\t\tfor ( const root of this._roots ) {\n\n\t\t\tsetBuffer( root );\n\t\t\tresult = shapecast( 0, geometry, intersectsBounds, intersectsRange, boundsTraverseOrder, byteOffset );\n\t\t\tclearBuffer();\n\n\t\t\tif ( result ) {\n\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tbyteOffset += root.byteLength;\n\n\t\t}\n\n\t\ttrianglePool.releasePrimitive( triangle );\n\n\t\treturn result;\n\n\t}\n\n\tbvhcast( otherBvh, matrixToLocal, callbacks ) {\n\n\t\t// BVHCast function for intersecting two BVHs against each other. Ultimately just uses two recursive shapecast calls rather\n\t\t// than an approach that walks down the tree (see bvhcast.js file for more info).\n\n\t\tlet {\n\t\t\tintersectsRanges,\n\t\t\tintersectsTriangles,\n\t\t} = callbacks;\n\n\t\tconst indexAttr = this.geometry.index;\n\t\tconst positionAttr = this.geometry.attributes.position;\n\n\t\tconst otherIndexAttr = otherBvh.geometry.index;\n\t\tconst otherPositionAttr = otherBvh.geometry.attributes.position;\n\n\t\ttempMatrix.copy( matrixToLocal ).invert();\n\n\t\tconst triangle = trianglePool.getPrimitive();\n\t\tconst triangle2 = trianglePool.getPrimitive();\n\n\t\tif ( intersectsTriangles ) {\n\n\t\t\tfunction iterateOverDoubleTriangles( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) {\n\n\t\t\t\tfor ( let i2 = offset2, l2 = offset2 + count2; i2 < l2; i2 ++ ) {\n\n\t\t\t\t\tsetTriangle( triangle2, i2 * 3, otherIndexAttr, otherPositionAttr );\n\t\t\t\t\ttriangle2.a.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.b.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.c.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\tfor ( let i1 = offset1, l1 = offset1 + count1; i1 < l1; i1 ++ ) {\n\n\t\t\t\t\t\tsetTriangle( triangle, i1 * 3, indexAttr, positionAttr );\n\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\tif ( intersectsTriangles( triangle, triangle2, i1, i2, depth1, index1, depth2, index2 ) ) {\n\n\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\tif ( intersectsRanges ) {\n\n\t\t\t\tconst originalIntersectsRanges = intersectsRanges;\n\t\t\t\tintersectsRanges = function ( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) {\n\n\t\t\t\t\tif ( ! originalIntersectsRanges( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) ) {\n\n\t\t\t\t\t\treturn iterateOverDoubleTriangles( offset1, count1, offset2, count2, depth1, index1, depth2, index2 );\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t};\n\n\t\t\t} else {\n\n\t\t\t\tintersectsRanges = iterateOverDoubleTriangles;\n\n\t\t\t}\n\n\t\t}\n\n\t\totherBvh.getBoundingBox( aabb2 );\n\t\taabb2.applyMatrix4( matrixToLocal );\n\t\tconst result = this.shapecast( {\n\n\t\t\tintersectsBounds: box => aabb2.intersectsBox( box ),\n\n\t\t\tintersectsRange: ( offset1, count1, contained, depth1, nodeIndex1, box ) => {\n\n\t\t\t\taabb.copy( box );\n\t\t\t\taabb.applyMatrix4( tempMatrix );\n\t\t\t\treturn otherBvh.shapecast( {\n\n\t\t\t\t\tintersectsBounds: box => aabb.intersectsBox( box ),\n\n\t\t\t\t\tintersectsRange: ( offset2, count2, contained, depth2, nodeIndex2 ) => {\n\n\t\t\t\t\t\treturn intersectsRanges( offset1, count1, offset2, count2, depth1, nodeIndex1, depth2, nodeIndex2 );\n\n\t\t\t\t\t},\n\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t} );\n\n\t\ttrianglePool.releasePrimitive( triangle );\n\t\ttrianglePool.releasePrimitive( triangle2 );\n\t\treturn result;\n\n\t}\n\n\t/* Derived Cast Functions */\n\tintersectsBox( box, boxToMesh ) {\n\n\t\tobb.set( box.min, box.max, boxToMesh );\n\t\tobb.needsUpdate = true;\n\n\t\treturn this.shapecast(\n\t\t\t{\n\t\t\t\tintersectsBounds: box => obb.intersectsBox( box ),\n\t\t\t\tintersectsTriangle: tri => obb.intersectsTriangle( tri )\n\t\t\t}\n\t\t);\n\n\t}\n\n\tintersectsSphere( sphere ) {\n\n\t\treturn this.shapecast(\n\t\t\t{\n\t\t\t\tintersectsBounds: box => sphere.intersectsBox( box ),\n\t\t\t\tintersectsTriangle: tri => tri.intersectsSphere( sphere )\n\t\t\t}\n\t\t);\n\n\t}\n\n\tclosestPointToGeometry( otherGeometry, geometryToBvh, target1 = { }, target2 = { }, minThreshold = 0, maxThreshold = Infinity ) {\n\n\t\tif ( ! otherGeometry.boundingBox ) {\n\n\t\t\totherGeometry.computeBoundingBox();\n\n\t\t}\n\n\t\tobb.set( otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh );\n\t\tobb.needsUpdate = true;\n\n\t\tconst geometry = this.geometry;\n\t\tconst pos = geometry.attributes.position;\n\t\tconst index = geometry.index;\n\t\tconst otherPos = otherGeometry.attributes.position;\n\t\tconst otherIndex = otherGeometry.index;\n\t\tconst triangle = trianglePool.getPrimitive();\n\t\tconst triangle2 = trianglePool.getPrimitive();\n\n\t\tlet tempTarget1 = temp1;\n\t\tlet tempTargetDest1 = temp2;\n\t\tlet tempTarget2 = null;\n\t\tlet tempTargetDest2 = null;\n\n\t\tif ( target2 ) {\n\n\t\t\ttempTarget2 = temp3;\n\t\t\ttempTargetDest2 = temp4;\n\n\t\t}\n\n\t\tlet closestDistance = Infinity;\n\t\tlet closestDistanceTriIndex = null;\n\t\tlet closestDistanceOtherTriIndex = null;\n\t\ttempMatrix.copy( geometryToBvh ).invert();\n\t\tobb2.matrix.copy( tempMatrix );\n\t\tthis.shapecast(\n\t\t\t{\n\n\t\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\t\treturn obb.distanceToBox( box );\n\n\t\t\t\t},\n\n\t\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\t\tif ( score < closestDistance && score < maxThreshold ) {\n\n\t\t\t\t\t\t// if we know the triangles of this bounds will be intersected next then\n\t\t\t\t\t\t// save the bounds to use during triangle checks.\n\t\t\t\t\t\tif ( isLeaf ) {\n\n\t\t\t\t\t\t\tobb2.min.copy( box.min );\n\t\t\t\t\t\t\tobb2.max.copy( box.max );\n\t\t\t\t\t\t\tobb2.needsUpdate = true;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn false;\n\n\t\t\t\t},\n\n\t\t\t\tintersectsRange: ( offset, count ) => {\n\n\t\t\t\t\tif ( otherGeometry.boundsTree ) {\n\n\t\t\t\t\t\t// if the other geometry has a bvh then use the accelerated path where we use shapecast to find\n\t\t\t\t\t\t// the closest bounds in the other geometry to check.\n\t\t\t\t\t\treturn otherGeometry.boundsTree.shapecast( {\n\t\t\t\t\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\t\t\t\t\treturn obb2.distanceToBox( box );\n\n\t\t\t\t\t\t\t},\n\n\t\t\t\t\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\t\t\t\t\treturn score < closestDistance && score < maxThreshold;\n\n\t\t\t\t\t\t\t},\n\n\t\t\t\t\t\t\tintersectsRange: ( otherOffset, otherCount ) => {\n\n\t\t\t\t\t\t\t\tfor ( let i2 = otherOffset * 3, l2 = ( otherOffset + otherCount ) * 3; i2 < l2; i2 += 3 ) {\n\n\t\t\t\t\t\t\t\t\tsetTriangle( triangle2, i2, otherIndex, otherPos );\n\t\t\t\t\t\t\t\t\ttriangle2.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\t\ttriangle2.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\t\ttriangle2.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\t\t\t\tfor ( let i = offset * 3, l = ( offset + count ) * 3; i < l; i += 3 ) {\n\n\t\t\t\t\t\t\t\t\t\tsetTriangle( triangle, i, index, pos );\n\t\t\t\t\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\t\t\t\t\tconst dist = triangle.distanceToTriangle( triangle2, tempTarget1, tempTarget2 );\n\t\t\t\t\t\t\t\t\t\tif ( dist < closestDistance ) {\n\n\t\t\t\t\t\t\t\t\t\t\ttempTargetDest1.copy( tempTarget1 );\n\n\t\t\t\t\t\t\t\t\t\t\tif ( tempTargetDest2 ) {\n\n\t\t\t\t\t\t\t\t\t\t\t\ttempTargetDest2.copy( tempTarget2 );\n\n\t\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t\tclosestDistance = dist;\n\t\t\t\t\t\t\t\t\t\t\tclosestDistanceTriIndex = i / 3;\n\t\t\t\t\t\t\t\t\t\t\tclosestDistanceOtherTriIndex = i2 / 3;\n\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t// stop traversal if we find a point that's under the given threshold\n\t\t\t\t\t\t\t\t\t\tif ( dist < minThreshold ) {\n\n\t\t\t\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t} );\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\t// If no bounds tree then we'll just check every triangle.\n\t\t\t\t\t\tconst triCount = otherIndex ? otherIndex.count : otherPos.count;\n\t\t\t\t\t\tfor ( let i2 = 0, l2 = triCount; i2 < l2; i2 += 3 ) {\n\n\t\t\t\t\t\t\tsetTriangle( triangle2, i2, otherIndex, otherPos );\n\t\t\t\t\t\t\ttriangle2.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\ttriangle2.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\ttriangle2.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\t\tfor ( let i = offset * 3, l = ( offset + count ) * 3; i < l; i += 3 ) {\n\n\t\t\t\t\t\t\t\tsetTriangle( triangle, i, index, pos );\n\t\t\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\t\t\tconst dist = triangle.distanceToTriangle( triangle2, tempTarget1, tempTarget2 );\n\t\t\t\t\t\t\t\tif ( dist < closestDistance ) {\n\n\t\t\t\t\t\t\t\t\ttempTargetDest1.copy( tempTarget1 );\n\n\t\t\t\t\t\t\t\t\tif ( tempTargetDest2 ) {\n\n\t\t\t\t\t\t\t\t\t\ttempTargetDest2.copy( tempTarget2 );\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\tclosestDistance = dist;\n\t\t\t\t\t\t\t\t\tclosestDistanceTriIndex = i / 3;\n\t\t\t\t\t\t\t\t\tclosestDistanceOtherTriIndex = i2 / 3;\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t// stop traversal if we find a point that's under the given threshold\n\t\t\t\t\t\t\t\tif ( dist < minThreshold ) {\n\n\t\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t},\n\n\t\t\t}\n\n\t\t);\n\n\t\ttrianglePool.releasePrimitive( triangle );\n\t\ttrianglePool.releasePrimitive( triangle2 );\n\n\t\tif ( closestDistance === Infinity ) return null;\n\n\t\tif ( ! target1.point ) target1.point = tempTargetDest1.clone();\n\t\telse target1.point.copy( tempTargetDest1 );\n\t\ttarget1.distance = closestDistance,\n\t\ttarget1.faceIndex = closestDistanceTriIndex;\n\n\t\tif ( target2 ) {\n\n\t\t\tif ( ! target2.point ) target2.point = tempTargetDest2.clone();\n\t\t\telse target2.point.copy( tempTargetDest2 );\n\t\t\ttarget2.point.applyMatrix4( tempMatrix );\n\t\t\ttempTargetDest1.applyMatrix4( tempMatrix );\n\t\t\ttarget2.distance = tempTargetDest1.sub( target2.point ).length();\n\t\t\ttarget2.faceIndex = closestDistanceOtherTriIndex;\n\n\t\t}\n\n\t\treturn target1;\n\n\t}\n\n\tclosestPointToPoint( point, target = { }, minThreshold = 0, maxThreshold = Infinity ) {\n\n\t\t// early out if under minThreshold\n\t\t// skip checking if over maxThreshold\n\t\t// set minThreshold = maxThreshold to quickly check if a point is within a threshold\n\t\t// returns Infinity if no value found\n\t\tconst minThresholdSq = minThreshold * minThreshold;\n\t\tconst maxThresholdSq = maxThreshold * maxThreshold;\n\t\tlet closestDistanceSq = Infinity;\n\t\tlet closestDistanceTriIndex = null;\n\t\tthis.shapecast(\n\n\t\t\t{\n\n\t\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\t\ttemp.copy( point ).clamp( box.min, box.max );\n\t\t\t\t\treturn temp.distanceToSquared( point );\n\n\t\t\t\t},\n\n\t\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\t\treturn score < closestDistanceSq && score < maxThresholdSq;\n\n\t\t\t\t},\n\n\t\t\t\tintersectsTriangle: ( tri, triIndex ) => {\n\n\t\t\t\t\ttri.closestPointToPoint( point, temp );\n\t\t\t\t\tconst distSq = point.distanceToSquared( temp );\n\t\t\t\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\t\t\t\ttemp1.copy( temp );\n\t\t\t\t\t\tclosestDistanceSq = distSq;\n\t\t\t\t\t\tclosestDistanceTriIndex = triIndex;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( distSq < minThresholdSq ) {\n\n\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\treturn false;\n\n\t\t\t\t\t}\n\n\t\t\t\t},\n\n\t\t\t}\n\n\t\t);\n\n\t\tif ( closestDistanceSq === Infinity ) return null;\n\n\t\tconst closestDistance = Math.sqrt( closestDistanceSq );\n\n\t\tif ( ! target.point ) target.point = temp1.clone();\n\t\telse target.point.copy( temp1 );\n\t\ttarget.distance = closestDistance,\n\t\ttarget.faceIndex = closestDistanceTriIndex;\n\n\t\treturn target;\n\n\t}\n\n\tgetBoundingBox( target ) {\n\n\t\ttarget.makeEmpty();\n\n\t\tconst roots = this._roots;\n\t\troots.forEach( buffer => {\n\n\t\t\tarrayToBox( 0, new Float32Array( buffer ), tempBox );\n\t\t\ttarget.union( tempBox );\n\n\t\t} );\n\n\t\treturn target;\n\n\t}\n\n}\n", "import { LineBasicMaterial, BufferAttribute, Box3, Group, MeshBasicMaterial, Object3D, BufferGeometry } from 'three';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\n\nconst boundingBox = /* @__PURE__ */ new Box3();\nclass MeshBVHRootVisualizer extends Object3D {\n\n\tget isMesh() {\n\n\t\treturn ! this.displayEdges;\n\n\t}\n\n\tget isLineSegments() {\n\n\t\treturn this.displayEdges;\n\n\t}\n\n\tget isLine() {\n\n\t\treturn this.displayEdges;\n\n\t}\n\n\tconstructor( mesh, material, depth = 10, group = 0 ) {\n\n\t\tsuper();\n\n\t\tthis.material = material;\n\t\tthis.geometry = new BufferGeometry();\n\t\tthis.name = 'MeshBVHRootVisualizer';\n\t\tthis.depth = depth;\n\t\tthis.displayParents = false;\n\t\tthis.mesh = mesh;\n\t\tthis.displayEdges = true;\n\t\tthis._group = group;\n\n\t}\n\n\traycast() {}\n\n\tupdate() {\n\n\t\tconst geometry = this.geometry;\n\t\tconst boundsTree = this.mesh.geometry.boundsTree;\n\t\tconst group = this._group;\n\t\tgeometry.dispose();\n\t\tthis.visible = false;\n\t\tif ( boundsTree ) {\n\n\t\t\t// count the number of bounds required\n\t\t\tconst targetDepth = this.depth - 1;\n\t\t\tconst displayParents = this.displayParents;\n\t\t\tlet boundsCount = 0;\n\t\t\tboundsTree.traverse( ( depth, isLeaf ) => {\n\n\t\t\t\tif ( depth === targetDepth || isLeaf ) {\n\n\t\t\t\t\tboundsCount ++;\n\t\t\t\t\treturn true;\n\n\t\t\t\t} else if ( displayParents ) {\n\n\t\t\t\t\tboundsCount ++;\n\n\t\t\t\t}\n\n\t\t\t}, group );\n\n\t\t\t// fill in the position buffer with the bounds corners\n\t\t\tlet posIndex = 0;\n\t\t\tconst positionArray = new Float32Array( 8 * 3 * boundsCount );\n\t\t\tboundsTree.traverse( ( depth, isLeaf, boundingData ) => {\n\n\t\t\t\tconst terminate = depth === targetDepth || isLeaf;\n\t\t\t\tif ( terminate || displayParents ) {\n\n\t\t\t\t\tarrayToBox( 0, boundingData, boundingBox );\n\n\t\t\t\t\tconst { min, max } = boundingBox;\n\t\t\t\t\tfor ( let x = - 1; x <= 1; x += 2 ) {\n\n\t\t\t\t\t\tconst xVal = x < 0 ? min.x : max.x;\n\t\t\t\t\t\tfor ( let y = - 1; y <= 1; y += 2 ) {\n\n\t\t\t\t\t\t\tconst yVal = y < 0 ? min.y : max.y;\n\t\t\t\t\t\t\tfor ( let z = - 1; z <= 1; z += 2 ) {\n\n\t\t\t\t\t\t\t\tconst zVal = z < 0 ? min.z : max.z;\n\t\t\t\t\t\t\t\tpositionArray[ posIndex + 0 ] = xVal;\n\t\t\t\t\t\t\t\tpositionArray[ posIndex + 1 ] = yVal;\n\t\t\t\t\t\t\t\tpositionArray[ posIndex + 2 ] = zVal;\n\n\t\t\t\t\t\t\t\tposIndex += 3;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn terminate;\n\n\t\t\t\t}\n\n\t\t\t}, group );\n\n\t\t\tlet indexArray;\n\t\t\tlet indices;\n\t\t\tif ( this.displayEdges ) {\n\n\t\t\t\t// fill in the index buffer to point to the corner points\n\t\t\t\tindices = new Uint8Array( [\n\t\t\t\t\t// x axis\n\t\t\t\t\t0, 4,\n\t\t\t\t\t1, 5,\n\t\t\t\t\t2, 6,\n\t\t\t\t\t3, 7,\n\n\t\t\t\t\t// y axis\n\t\t\t\t\t0, 2,\n\t\t\t\t\t1, 3,\n\t\t\t\t\t4, 6,\n\t\t\t\t\t5, 7,\n\n\t\t\t\t\t// z axis\n\t\t\t\t\t0, 1,\n\t\t\t\t\t2, 3,\n\t\t\t\t\t4, 5,\n\t\t\t\t\t6, 7,\n\t\t\t\t] );\n\n\t\t\t} else {\n\n\t\t\t\tindices = new Uint8Array( [\n\n\t\t\t\t\t// X-, X+\n\t\t\t\t\t0, 1, 2,\n\t\t\t\t\t2, 1, 3,\n\n\t\t\t\t\t4, 6, 5,\n\t\t\t\t\t6, 7, 5,\n\n\t\t\t\t\t// Y-, Y+\n\t\t\t\t\t1, 4, 5,\n\t\t\t\t\t0, 4, 1,\n\n\t\t\t\t\t2, 3, 6,\n\t\t\t\t\t3, 7, 6,\n\n\t\t\t\t\t// Z-, Z+\n\t\t\t\t\t0, 2, 4,\n\t\t\t\t\t2, 6, 4,\n\n\t\t\t\t\t1, 5, 3,\n\t\t\t\t\t3, 5, 7,\n\n\t\t\t\t] );\n\n\t\t\t}\n\n\t\t\tif ( positionArray.length > 65535 ) {\n\n\t\t\t\tindexArray = new Uint32Array( indices.length * boundsCount );\n\n\t\t\t} else {\n\n\t\t\t\tindexArray = new Uint16Array( indices.length * boundsCount );\n\n\t\t\t}\n\n\t\t\tconst indexLength = indices.length;\n\t\t\tfor ( let i = 0; i < boundsCount; i ++ ) {\n\n\t\t\t\tconst posOffset = i * 8;\n\t\t\t\tconst indexOffset = i * indexLength;\n\t\t\t\tfor ( let j = 0; j < indexLength; j ++ ) {\n\n\t\t\t\t\tindexArray[ indexOffset + j ] = posOffset + indices[ j ];\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// update the geometry\n\t\t\tgeometry.setIndex(\n\t\t\t\tnew BufferAttribute( indexArray, 1, false ),\n\t\t\t);\n\t\t\tgeometry.setAttribute(\n\t\t\t\t'position',\n\t\t\t\tnew BufferAttribute( positionArray, 3, false ),\n\t\t\t);\n\t\t\tthis.visible = true;\n\n\t\t}\n\n\t}\n\n}\n\nclass MeshBVHVisualizer extends Group {\n\n\tget color() {\n\n\t\treturn this.edgeMaterial.color;\n\n\t}\n\n\tget opacity() {\n\n\t\treturn this.edgeMaterial.opacity;\n\n\t}\n\n\tset opacity( v ) {\n\n\t\tthis.edgeMaterial.opacity = v;\n\t\tthis.meshMaterial.opacity = v;\n\n\t}\n\n\tconstructor( mesh, depth = 10 ) {\n\n\t\tsuper();\n\n\t\tthis.name = 'MeshBVHVisualizer';\n\t\tthis.depth = depth;\n\t\tthis.mesh = mesh;\n\t\tthis.displayParents = false;\n\t\tthis.displayEdges = true;\n\t\tthis._roots = [];\n\n\t\tconst edgeMaterial = new LineBasicMaterial( {\n\t\t\tcolor: 0x00FF88,\n\t\t\ttransparent: true,\n\t\t\topacity: 0.3,\n\t\t\tdepthWrite: false,\n\t\t} );\n\n\t\tconst meshMaterial = new MeshBasicMaterial( {\n\t\t\tcolor: 0x00FF88,\n\t\t\ttransparent: true,\n\t\t\topacity: 0.3,\n\t\t\tdepthWrite: false,\n\t\t} );\n\n\t\tmeshMaterial.color = edgeMaterial.color;\n\n\t\tthis.edgeMaterial = edgeMaterial;\n\t\tthis.meshMaterial = meshMaterial;\n\n\t\tthis.update();\n\n\t}\n\n\tupdate() {\n\n\t\tconst bvh = this.mesh.geometry.boundsTree;\n\t\tconst totalRoots = bvh ? bvh._roots.length : 0;\n\t\twhile ( this._roots.length > totalRoots ) {\n\n\t\t\tconst root = this._roots.pop();\n\t\t\troot.geometry.dispose();\n\t\t\tthis.remove( root );\n\n\t\t}\n\n\t\tfor ( let i = 0; i < totalRoots; i ++ ) {\n\n\t\t\tif ( i >= this._roots.length ) {\n\n\t\t\t\tconst root = new MeshBVHRootVisualizer( this.mesh, this.edgeMaterial, this.depth, i );\n\t\t\t\tthis.add( root );\n\t\t\t\tthis._roots.push( root );\n\n\t\t\t}\n\n\t\t\tconst root = this._roots[ i ];\n\t\t\troot.depth = this.depth;\n\t\t\troot.mesh = this.mesh;\n\t\t\troot.displayParents = this.displayParents;\n\t\t\troot.displayEdges = this.displayEdges;\n\t\t\troot.material = this.displayEdges ? this.edgeMaterial : this.meshMaterial;\n\t\t\troot.update();\n\n\t\t}\n\n\t}\n\n\tupdateMatrixWorld( ...args ) {\n\n\t\tthis.position.copy( this.mesh.position );\n\t\tthis.rotation.copy( this.mesh.rotation );\n\t\tthis.scale.copy( this.mesh.scale );\n\n\t\tsuper.updateMatrixWorld( ...args );\n\n\t}\n\n\tcopy( source ) {\n\n\t\tthis.depth = source.depth;\n\t\tthis.mesh = source.mesh;\n\n\t}\n\n\tclone() {\n\n\t\treturn new MeshBVHVisualizer( this.mesh, this.depth );\n\n\t}\n\n\tdispose() {\n\n\t\tthis.edgeMaterial.dispose();\n\t\tthis.meshMaterial.dispose();\n\n\t\tconst children = this.children;\n\t\tfor ( let i = 0, l = children.length; i < l; i ++ ) {\n\n\t\t\tchildren[ i ].geometry.dispose();\n\n\t\t}\n\n\t}\n\n}\n\n\nexport { MeshBVHVisualizer };\n", "import { Box3, Vector3 } from 'three';\nimport { TRAVERSAL_COST, TRIANGLE_INTERSECT_COST } from '../core/Constants.js';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\n\nconst _box1 = /* @__PURE__ */ new Box3();\nconst _box2 = /* @__PURE__ */ new Box3();\nconst _vec = /* @__PURE__ */ new Vector3();\n\n// https://stackoverflow.com/questions/1248302/how-to-get-the-size-of-a-javascript-object\nfunction getPrimitiveSize( el ) {\n\n\tswitch ( typeof el ) {\n\n\t\tcase 'number':\n\t\t\treturn 8;\n\t\tcase 'string':\n\t\t\treturn el.length * 2;\n\t\tcase 'boolean':\n\t\t\treturn 4;\n\t\tdefault:\n\t\t\treturn 0;\n\n\t}\n\n}\n\nfunction isTypedArray( arr ) {\n\n\tconst regex = /(Uint|Int|Float)(8|16|32)Array/;\n\treturn regex.test( arr.constructor.name );\n\n}\n\nfunction getRootExtremes( bvh, group ) {\n\n\tconst result = {\n\t\tnodeCount: 0,\n\t\tleafNodeCount: 0,\n\n\t\tdepth: {\n\t\t\tmin: Infinity, max: - Infinity\n\t\t},\n\t\ttris: {\n\t\t\tmin: Infinity, max: - Infinity\n\t\t},\n\t\tsplits: [ 0, 0, 0 ],\n\t\tsurfaceAreaScore: 0,\n\t};\n\n\tbvh.traverse( ( depth, isLeaf, boundingData, offsetOrSplit, count ) => {\n\n\t\tconst l0 = boundingData[ 0 + 3 ] - boundingData[ 0 ];\n\t\tconst l1 = boundingData[ 1 + 3 ] - boundingData[ 1 ];\n\t\tconst l2 = boundingData[ 2 + 3 ] - boundingData[ 2 ];\n\n\t\tconst surfaceArea = 2 * ( l0 * l1 + l1 * l2 + l2 * l0 );\n\n\t\tresult.nodeCount ++;\n\t\tif ( isLeaf ) {\n\n\t\t\tresult.leafNodeCount ++;\n\n\t\t\tresult.depth.min = Math.min( depth, result.depth.min );\n\t\t\tresult.depth.max = Math.max( depth, result.depth.max );\n\n\t\t\tresult.tris.min = Math.min( count, result.tris.min );\n\t\t\tresult.tris.max = Math.max( count, result.tris.max );\n\n\t\t\tresult.surfaceAreaScore += surfaceArea * TRIANGLE_INTERSECT_COST * count;\n\n\t\t} else {\n\n\t\t\tresult.splits[ offsetOrSplit ] ++;\n\n\t\t\tresult.surfaceAreaScore += surfaceArea * TRAVERSAL_COST;\n\n\t\t}\n\n\t}, group );\n\n\t// If there are no leaf nodes because the tree hasn't finished generating yet.\n\tif ( result.tris.min === Infinity ) {\n\n\t\tresult.tris.min = 0;\n\t\tresult.tris.max = 0;\n\n\t}\n\n\tif ( result.depth.min === Infinity ) {\n\n\t\tresult.depth.min = 0;\n\t\tresult.depth.max = 0;\n\n\t}\n\n\treturn result;\n\n}\n\nfunction getBVHExtremes( bvh ) {\n\n\treturn bvh._roots.map( ( root, i ) => getRootExtremes( bvh, i ) );\n\n}\n\nfunction estimateMemoryInBytes( obj ) {\n\n\tconst traversed = new Set();\n\tconst stack = [ obj ];\n\tlet bytes = 0;\n\n\twhile ( stack.length ) {\n\n\t\tconst curr = stack.pop();\n\t\tif ( traversed.has( curr ) ) {\n\n\t\t\tcontinue;\n\n\t\t}\n\n\t\ttraversed.add( curr );\n\n\t\tfor ( let key in curr ) {\n\n\t\t\tif ( ! curr.hasOwnProperty( key ) ) {\n\n\t\t\t\tcontinue;\n\n\t\t\t}\n\n\t\t\tbytes += getPrimitiveSize( key );\n\n\t\t\tconst value = curr[ key ];\n\t\t\tif ( value && ( typeof value === 'object' || typeof value === 'function' ) ) {\n\n\t\t\t\tif ( isTypedArray( value ) ) {\n\n\t\t\t\t\tbytes += value.byteLength;\n\n\t\t\t\t} else if ( value instanceof ArrayBuffer ) {\n\n\t\t\t\t\tbytes += value.byteLength;\n\n\t\t\t\t} else {\n\n\t\t\t\t\tstack.push( value );\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tbytes += getPrimitiveSize( value );\n\n\t\t\t}\n\n\n\t\t}\n\n\t}\n\n\treturn bytes;\n\n}\n\nfunction validateBounds( bvh ) {\n\n\tconst geometry = bvh.geometry;\n\tconst depthStack = [];\n\tconst index = geometry.index;\n\tconst position = geometry.getAttribute( 'position' );\n\tlet passes = true;\n\n\tbvh.traverse( ( depth, isLeaf, boundingData, offset, count ) => {\n\n\t\tconst info = {\n\t\t\tdepth,\n\t\t\tisLeaf,\n\t\t\tboundingData,\n\t\t\toffset,\n\t\t\tcount,\n\t\t};\n\t\tdepthStack[ depth ] = info;\n\n\t\tarrayToBox( 0, boundingData, _box1 );\n\t\tconst parent = depthStack[ depth - 1 ];\n\n\t\tif ( isLeaf ) {\n\n\t\t\t// check triangles\n\t\t\tfor ( let i = offset * 3, l = ( offset + count ) * 3; i < l; i += 3 ) {\n\n\t\t\t\tconst i0 = index.getX( i );\n\t\t\t\tconst i1 = index.getX( i + 1 );\n\t\t\t\tconst i2 = index.getX( i + 2 );\n\n\t\t\t\tlet isContained;\n\n\t\t\t\t_vec.fromBufferAttribute( position, i0 );\n\t\t\t\tisContained = _box1.containsPoint( _vec );\n\n\t\t\t\t_vec.fromBufferAttribute( position, i1 );\n\t\t\t\tisContained = isContained && _box1.containsPoint( _vec );\n\n\t\t\t\t_vec.fromBufferAttribute( position, i2 );\n\t\t\t\tisContained = isContained && _box1.containsPoint( _vec );\n\n\t\t\t\tconsole.assert( isContained, 'Leaf bounds does not fully contain triangle.' );\n\t\t\t\tpasses = passes && isContained;\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( parent ) {\n\n\t\t\t// check if my bounds fit in my parents\n\t\t\tarrayToBox( 0, boundingData, _box2 );\n\n\t\t\tconst isContained = _box2.containsBox( _box1 );\n\t\t\tconsole.assert( isContained, 'Parent bounds does not fully contain child.' );\n\t\t\tpasses = passes && isContained;\n\n\t\t}\n\n\t} );\n\n\treturn passes;\n\n}\n\n// Returns a simple, human readable object that represents the BVH.\nfunction getJSONStructure( bvh ) {\n\n\tconst depthStack = [];\n\n\tbvh.traverse( ( depth, isLeaf, boundingData, offset, count ) => {\n\n\t\tconst info = {\n\t\t\tbounds: arrayToBox( 0, boundingData, new Box3() ),\n\t\t};\n\n\t\tif ( isLeaf ) {\n\n\t\t\tinfo.count = count;\n\t\t\tinfo.offset = offset;\n\n\t\t} else {\n\n\t\t\tinfo.left = null;\n\t\t\tinfo.right = null;\n\n\t\t}\n\n\t\tdepthStack[ depth ] = info;\n\n\t\t// traversal hits the left then right node\n\t\tconst parent = depthStack[ depth - 1 ];\n\t\tif ( parent ) {\n\n\t\t\tif ( parent.left === null ) {\n\n\t\t\t\tparent.left = info;\n\n\t\t\t} else {\n\n\t\t\t\tparent.right = info;\n\n\t\t\t}\n\n\t\t}\n\n\t} );\n\n\treturn depthStack[ 0 ];\n\n}\n\nexport { estimateMemoryInBytes, getBVHExtremes, validateBounds, getJSONStructure };\n", "import { Ray, Matrix4, <PERSON><PERSON> } from 'three';\nimport { convertRaycastIntersect } from './GeometryRayIntersectUtilities.js';\nimport { MeshBVH } from '../core/MeshBVH.js';\n\nconst ray = /* @__PURE__ */ new Ray();\nconst tmpInverseMatrix = /* @__PURE__ */ new Matrix4();\nconst origMeshRaycastFunc = Mesh.prototype.raycast;\n\nexport function acceleratedRaycast( raycaster, intersects ) {\n\n\tif ( this.geometry.boundsTree ) {\n\n\t\tif ( this.material === undefined ) return;\n\n\t\ttmpInverseMatrix.copy( this.matrixWorld ).invert();\n\t\tray.copy( raycaster.ray ).applyMatrix4( tmpInverseMatrix );\n\n\t\tconst bvh = this.geometry.boundsTree;\n\t\tif ( raycaster.firstHitOnly === true ) {\n\n\t\t\tconst hit = convertRaycastIntersect( bvh.raycastFirst( ray, this.material ), this, raycaster );\n\t\t\tif ( hit ) {\n\n\t\t\t\tintersects.push( hit );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconst hits = bvh.raycast( ray, this.material );\n\t\t\tfor ( let i = 0, l = hits.length; i < l; i ++ ) {\n\n\t\t\t\tconst hit = convertRaycastIntersect( hits[ i ], this, raycaster );\n\t\t\t\tif ( hit ) {\n\n\t\t\t\t\tintersects.push( hit );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t} else {\n\n\t\torigMeshRaycastFunc.call( this, raycaster, intersects );\n\n\t}\n\n}\n\nexport function computeBoundsTree( options ) {\n\n\tthis.boundsTree = new MeshBVH( this, options );\n\treturn this.boundsTree;\n\n}\n\nexport function disposeBoundsTree() {\n\n\tthis.boundsTree = null;\n\n}\n", "import {\n\tDataTexture,\n\tFloatType,\n\tIntType,\n\tUnsignedIntType,\n\tByteType,\n\tUnsignedByteType,\n\tShortType,\n\tUnsignedShortType,\n\n\tRedFormat,\n\tRGFormat,\n\tRGBAFormat,\n\n\tRedIntegerFormat,\n\tRGIntegerFormat,\n\tRGBAIntegerFormat,\n\n\tNearestFilter,\n} from 'three';\n\nfunction countToStringFormat( count ) {\n\n\tswitch ( count ) {\n\n\t\tcase 1: return 'R';\n\t\tcase 2: return 'RG';\n\t\tcase 3: return 'RGBA';\n\t\tcase 4: return 'RGBA';\n\n\t}\n\n\tthrow new Error();\n\n}\n\nfunction countToFormat( count ) {\n\n\tswitch ( count ) {\n\n\t\tcase 1: return RedFormat;\n\t\tcase 2: return RGFormat;\n\t\tcase 3: return RGBAFormat;\n\t\tcase 4: return RGBAFormat;\n\n\t}\n\n}\n\nfunction countToIntFormat( count ) {\n\n\tswitch ( count ) {\n\n\t\tcase 1: return RedIntegerFormat;\n\t\tcase 2: return RGIntegerFormat;\n\t\tcase 3: return RGBAIntegerFormat;\n\t\tcase 4: return RGBAIntegerFormat;\n\n\t}\n\n}\n\nexport class VertexAttributeTexture extends DataTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis.minFilter = NearestFilter;\n\t\tthis.magFilter = NearestFilter;\n\t\tthis.generateMipmaps = false;\n\t\tthis.overrideItemSize = null;\n\t\tthis._forcedType = null;\n\n\t}\n\n\tupdateFrom( attr ) {\n\n\t\tconst overrideItemSize = this.overrideItemSize;\n\t\tconst originalItemSize = attr.itemSize;\n\t\tconst originalCount = attr.count;\n\t\tif ( overrideItemSize !== null ) {\n\n\t\t\tif ( ( originalItemSize * originalCount ) % overrideItemSize !== 0.0 ) {\n\n\t\t\t\tthrow new Error( 'VertexAttributeTexture: overrideItemSize must divide evenly into buffer length.' );\n\n\t\t\t}\n\n\t\t\tattr.itemSize = overrideItemSize;\n\t\t\tattr.count = originalCount * originalItemSize / overrideItemSize;\n\n\t\t}\n\n\t\tconst itemSize = attr.itemSize;\n\t\tconst count = attr.count;\n\t\tconst normalized = attr.normalized;\n\t\tconst originalBufferCons = attr.array.constructor;\n\t\tconst byteCount = originalBufferCons.BYTES_PER_ELEMENT;\n\t\tlet targetType = this._forcedType;\n\t\tlet finalStride = itemSize;\n\n\t\t// derive the type of texture this should be in the shader\n\t\tif ( targetType === null ) {\n\n\t\t\tswitch ( originalBufferCons ) {\n\n\t\t\t\tcase Float32Array:\n\t\t\t\t\ttargetType = FloatType;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase Uint8Array:\n\t\t\t\tcase Uint16Array:\n\t\t\t\tcase Uint32Array:\n\t\t\t\t\ttargetType = UnsignedIntType;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase Int8Array:\n\t\t\t\tcase Int16Array:\n\t\t\t\tcase Int32Array:\n\t\t\t\t\ttargetType = IntType;\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// get the target format to store the texture as\n\t\tlet type, format, normalizeValue, targetBufferCons;\n\t\tlet internalFormat = countToStringFormat( itemSize );\n\t\tswitch ( targetType ) {\n\n\t\t\tcase FloatType:\n\t\t\t\tnormalizeValue = 1.0;\n\t\t\t\tformat = countToFormat( itemSize );\n\n\t\t\t\tif ( normalized && byteCount === 1 ) {\n\n\t\t\t\t\ttargetBufferCons = originalBufferCons;\n\t\t\t\t\tinternalFormat += '8';\n\n\t\t\t\t\tif ( originalBufferCons === Uint8Array ) {\n\n\t\t\t\t\t\ttype = UnsignedByteType;\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\ttype = ByteType;\n\t\t\t\t\t\tinternalFormat += '_SNORM';\n\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttargetBufferCons = Float32Array;\n\t\t\t\t\tinternalFormat += '32F';\n\t\t\t\t\ttype = FloatType;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t\tcase IntType:\n\t\t\t\tinternalFormat += byteCount * 8 + 'I';\n\t\t\t\tnormalizeValue = normalized ? Math.pow( 2, originalBufferCons.BYTES_PER_ELEMENT * 8 - 1 ) : 1.0;\n\t\t\t\tformat = countToIntFormat( itemSize );\n\n\t\t\t\tif ( byteCount === 1 ) {\n\n\t\t\t\t\ttargetBufferCons = Int8Array;\n\t\t\t\t\ttype = ByteType;\n\n\t\t\t\t} else if ( byteCount === 2 ) {\n\n\t\t\t\t\ttargetBufferCons = Int16Array;\n\t\t\t\t\ttype = ShortType;\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttargetBufferCons = Int32Array;\n\t\t\t\t\ttype = IntType;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t\tcase UnsignedIntType:\n\t\t\t\tinternalFormat += byteCount * 8 + 'UI';\n\t\t\t\tnormalizeValue = normalized ? Math.pow( 2, originalBufferCons.BYTES_PER_ELEMENT * 8 - 1 ) : 1.0;\n\t\t\t\tformat = countToIntFormat( itemSize );\n\n\t\t\t\tif ( byteCount === 1 ) {\n\n\t\t\t\t\ttargetBufferCons = Uint8Array;\n\t\t\t\t\ttype = UnsignedByteType;\n\n\t\t\t\t} else if ( byteCount === 2 ) {\n\n\t\t\t\t\ttargetBufferCons = Uint16Array;\n\t\t\t\t\ttype = UnsignedShortType;\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttargetBufferCons = Uint32Array;\n\t\t\t\t\ttype = UnsignedIntType;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\t// there will be a mismatch between format length and final length because\n\t\t// RGBFormat and RGBIntegerFormat was removed\n\t\tif ( finalStride === 3 && ( format === RGBAFormat || format === RGBAIntegerFormat ) ) {\n\n\t\t\tfinalStride = 4;\n\n\t\t}\n\n\t\t// copy the data over to the new texture array\n\t\tconst dimension = Math.ceil( Math.sqrt( count ) );\n\t\tconst length = finalStride * dimension * dimension;\n\t\tconst dataArray = new targetBufferCons( length );\n\n\t\t// temporarily set the normalized state to false since we have custom normalization logic\n\t\tconst originalNormalized = attr.normalized;\n\t\tattr.normalized = false;\n\t\tfor ( let i = 0; i < count; i ++ ) {\n\n\t\t\tconst ii = finalStride * i;\n\t\t\tdataArray[ ii ] = attr.getX( i ) / normalizeValue;\n\n\t\t\tif ( itemSize >= 2 ) {\n\n\t\t\t\tdataArray[ ii + 1 ] = attr.getY( i ) / normalizeValue;\n\n\t\t\t}\n\n\t\t\tif ( itemSize >= 3 ) {\n\n\t\t\t\tdataArray[ ii + 2 ] = attr.getZ( i ) / normalizeValue;\n\n\t\t\t\tif ( finalStride === 4 ) {\n\n\t\t\t\t\tdataArray[ ii + 3 ] = 1.0;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( itemSize >= 4 ) {\n\n\t\t\t\tdataArray[ ii + 3 ] = attr.getW( i ) / normalizeValue;\n\n\t\t\t}\n\n\t\t}\n\n\t\tattr.normalized = originalNormalized;\n\n\t\tthis.internalFormat = internalFormat;\n\t\tthis.format = format;\n\t\tthis.type = type;\n\t\tthis.image.width = dimension;\n\t\tthis.image.height = dimension;\n\t\tthis.image.data = dataArray;\n\t\tthis.needsUpdate = true;\n\t\tthis.dispose();\n\n\t\tattr.itemSize = originalItemSize;\n\t\tattr.count = originalCount;\n\n\t}\n\n}\n\nexport class UIntVertexAttributeTexture extends VertexAttributeTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis._forcedType = UnsignedIntType;\n\n\t}\n\n}\n\nexport class IntVertexAttributeTexture extends VertexAttributeTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis._forcedType = IntType;\n\n\t}\n\n\n}\n\nexport class FloatVertexAttributeTexture extends VertexAttributeTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis._forcedType = FloatType;\n\n\t}\n\n}\n", "import {\n\tDataTexture,\n\tFloatType,\n\tUnsignedIntType,\n\tRGBAFormat,\n\tRGIntegerFormat,\n\tNearestFilter,\n} from 'three';\nimport {\n\tFloatVertexAttributeTexture,\n\tUIntVertexAttributeTexture,\n} from './VertexAttributeTexture.js';\nimport { BYTES_PER_NODE } from '../core/Constants.js';\nimport {\n\tBOUNDING_DATA_INDEX,\n\tCOUNT,\n\tIS_LEAF,\n\tRIGHT_NODE,\n\tOFFSET,\n\tSPLIT_AXIS,\n} from '../core/nodeBufferFunctions.js';\n\nfunction bvhToTextures( bvh, boundsTexture, contentsTexture ) {\n\n\tconst roots = bvh._roots;\n\n\tif ( roots.length !== 1 ) {\n\n\t\tthrow new Error( 'MeshBVHUniformStruct: Multi-root BVHs not supported.' );\n\n\t}\n\n\tconst root = roots[ 0 ];\n\tconst uint16Array = new Uint16Array( root );\n\tconst uint32Array = new Uint32Array( root );\n\tconst float32Array = new Float32Array( root );\n\n\t// Both bounds need two elements per node so compute the height so it's twice as long as\n\t// the width so we can expand the row by two and still have a square texture\n\tconst nodeCount = root.byteLength / BYTES_PER_NODE;\n\tconst boundsDimension = 2 * Math.ceil( Math.sqrt( nodeCount / 2 ) );\n\tconst boundsArray = new Float32Array( 4 * boundsDimension * boundsDimension );\n\n\tconst contentsDimension = Math.ceil( Math.sqrt( nodeCount ) );\n\tconst contentsArray = new Uint32Array( 2 * contentsDimension * contentsDimension );\n\n\tfor ( let i = 0; i < nodeCount; i ++ ) {\n\n\t\tconst nodeIndex32 = i * BYTES_PER_NODE / 4;\n\t\tconst nodeIndex16 = nodeIndex32 * 2;\n\t\tconst boundsIndex = BOUNDING_DATA_INDEX( nodeIndex32 );\n\t\tfor ( let b = 0; b < 3; b ++ ) {\n\n\t\t\tboundsArray[ 8 * i + 0 + b ] = float32Array[ boundsIndex + 0 + b ];\n\t\t\tboundsArray[ 8 * i + 4 + b ] = float32Array[ boundsIndex + 3 + b ];\n\n\t\t}\n\n\t\tif ( IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\t\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\n\t\t\tconst mergedLeafCount = 0xffff0000 | count;\n\t\t\tcontentsArray[ i * 2 + 0 ] = mergedLeafCount;\n\t\t\tcontentsArray[ i * 2 + 1 ] = offset;\n\n\t\t} else {\n\n\t\t\tconst rightIndex = 4 * RIGHT_NODE( nodeIndex32, uint32Array ) / BYTES_PER_NODE;\n\t\t\tconst splitAxis = SPLIT_AXIS( nodeIndex32, uint32Array );\n\n\t\t\tcontentsArray[ i * 2 + 0 ] = splitAxis;\n\t\t\tcontentsArray[ i * 2 + 1 ] = rightIndex;\n\n\t\t}\n\n\t}\n\n\tboundsTexture.image.data = boundsArray;\n\tboundsTexture.image.width = boundsDimension;\n\tboundsTexture.image.height = boundsDimension;\n\tboundsTexture.format = RGBAFormat;\n\tboundsTexture.type = FloatType;\n\tboundsTexture.internalFormat = 'RGBA32F';\n\tboundsTexture.minFilter = NearestFilter;\n\tboundsTexture.magFilter = NearestFilter;\n\tboundsTexture.generateMipmaps = false;\n\tboundsTexture.needsUpdate = true;\n\tboundsTexture.dispose();\n\n\tcontentsTexture.image.data = contentsArray;\n\tcontentsTexture.image.width = contentsDimension;\n\tcontentsTexture.image.height = contentsDimension;\n\tcontentsTexture.format = RGIntegerFormat;\n\tcontentsTexture.type = UnsignedIntType;\n\tcontentsTexture.internalFormat = 'RG32UI';\n\tcontentsTexture.minFilter = NearestFilter;\n\tcontentsTexture.magFilter = NearestFilter;\n\tcontentsTexture.generateMipmaps = false;\n\tcontentsTexture.needsUpdate = true;\n\tcontentsTexture.dispose();\n\n}\n\nexport class MeshBVHUniformStruct {\n\n\tconstructor() {\n\n\t\tthis.autoDispose = true;\n\t\tthis.index = new UIntVertexAttributeTexture();\n\t\tthis.position = new FloatVertexAttributeTexture();\n\t\tthis.bvhBounds = new DataTexture();\n\t\tthis.bvhContents = new DataTexture();\n\n\t\tthis.index.overrideItemSize = 3;\n\n\t}\n\n\tupdateFrom( bvh ) {\n\n\t\tconst { geometry } = bvh;\n\n\t\tbvhToTextures( bvh, this.bvhBounds, this.bvhContents );\n\n\t\tthis.index.updateFrom( geometry.index );\n\t\tthis.position.updateFrom( geometry.attributes.position );\n\n\t}\n\n\tdispose() {\n\n\t\tconst { index, position, bvhBounds, bvhContents } = this;\n\n\t\tif ( index ) index.dispose();\n\t\tif ( position ) position.dispose();\n\t\tif ( bvhBounds ) bvhBounds.dispose();\n\t\tif ( bvhContents ) bvhContents.dispose();\n\n\t}\n\n}\n", "// Note that a struct cannot be used for the hit record including faceIndices, faceNormal, barycoord,\n// side, and dist because on some mobile GPUS (such as Adreno) numbers are afforded less precision specifically\n// when in a struct leading to inaccurate hit results. See KhronosGroup/WebGL#3351 for more details.\nexport const shaderStructs = /* glsl */`\n#ifndef TRI_INTERSECT_EPSILON\n#define TRI_INTERSECT_EPSILON 1e-5\n#endif\n\n#ifndef INFINITY\n#define INFINITY 1e20\n#endif\n\nstruct BVH {\n\n\tusampler2D index;\n\tsampler2D position;\n\n\tsampler2D bvhBounds;\n\tusampler2D bvhContents;\n\n};\n`;\n\nexport const shaderIntersectFunction = /* glsl */`\n\n// Utilities\nuvec4 uTexelFetch1D( usampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nivec4 iTexelFetch1D( isampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nvec4 texelFetch1D( sampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nvec4 textureSampleBarycoord( sampler2D tex, vec3 barycoord, uvec3 faceIndices ) {\n\n\treturn\n\t\tbarycoord.x * texelFetch1D( tex, faceIndices.x ) +\n\t\tbarycoord.y * texelFetch1D( tex, faceIndices.y ) +\n\t\tbarycoord.z * texelFetch1D( tex, faceIndices.z );\n\n}\n\nvoid ndcToCameraRay(\n\tvec2 coord, mat4 cameraWorld, mat4 invProjectionMatrix,\n\tout vec3 rayOrigin, out vec3 rayDirection\n) {\n\n\t// get camera look direction and near plane for camera clipping\n\tvec4 lookDirection = cameraWorld * vec4( 0.0, 0.0, - 1.0, 0.0 );\n\tvec4 nearVector = invProjectionMatrix * vec4( 0.0, 0.0, - 1.0, 1.0 );\n\tfloat near = abs( nearVector.z / nearVector.w );\n\n\t// get the camera direction and position from camera matrices\n\tvec4 origin = cameraWorld * vec4( 0.0, 0.0, 0.0, 1.0 );\n\tvec4 direction = invProjectionMatrix * vec4( coord, 0.5, 1.0 );\n\tdirection /= direction.w;\n\tdirection = cameraWorld * direction - origin;\n\n\t// slide the origin along the ray until it sits at the near clip plane position\n\torigin.xyz += direction.xyz * near / dot( direction, lookDirection );\n\n\trayOrigin = origin.xyz;\n\trayDirection = direction.xyz;\n\n}\n\n// Raycasting\nfloat intersectsBounds( vec3 rayOrigin, vec3 rayDirection, vec3 boundsMin, vec3 boundsMax ) {\n\n\t// https://www.reddit.com/r/opengl/comments/8ntzz5/fast_glsl_ray_box_intersection/\n\t// https://tavianator.com/2011/ray_box.html\n\tvec3 invDir = 1.0 / rayDirection;\n\n\t// find intersection distances for each plane\n\tvec3 tMinPlane = invDir * ( boundsMin - rayOrigin );\n\tvec3 tMaxPlane = invDir * ( boundsMax - rayOrigin );\n\n\t// get the min and max distances from each intersection\n\tvec3 tMinHit = min( tMaxPlane, tMinPlane );\n\tvec3 tMaxHit = max( tMaxPlane, tMinPlane );\n\n\t// get the furthest hit distance\n\tvec2 t = max( tMinHit.xx, tMinHit.yz );\n\tfloat t0 = max( t.x, t.y );\n\n\t// get the minimum hit distance\n\tt = min( tMaxHit.xx, tMaxHit.yz );\n\tfloat t1 = min( t.x, t.y );\n\n\t// set distance to 0.0 if the ray starts inside the box\n\tfloat dist = max( t0, 0.0 );\n\n\treturn t1 >= dist ? dist : INFINITY;\n\n}\n\nbool intersectsTriangle(\n\tvec3 rayOrigin, vec3 rayDirection, vec3 a, vec3 b, vec3 c,\n\tout vec3 barycoord, out vec3 norm, out float dist, out float side\n) {\n\n\t// https://stackoverflow.com/questions/42740765/intersection-between-line-and-triangle-in-3d\n\tvec3 edge1 = b - a;\n\tvec3 edge2 = c - a;\n\tnorm = cross( edge1, edge2 );\n\n\tfloat det = - dot( rayDirection, norm );\n\tfloat invdet = 1.0 / det;\n\n\tvec3 AO = rayOrigin - a;\n\tvec3 DAO = cross( AO, rayDirection );\n\n\tvec4 uvt;\n\tuvt.x = dot( edge2, DAO ) * invdet;\n\tuvt.y = - dot( edge1, DAO ) * invdet;\n\tuvt.z = dot( AO, norm ) * invdet;\n\tuvt.w = 1.0 - uvt.x - uvt.y;\n\n\t// set the hit information\n\tbarycoord = uvt.wxy; // arranged in A, B, C order\n\tdist = uvt.z;\n\tside = sign( det );\n\tnorm = side * normalize( norm );\n\n\t// add an epsilon to avoid misses between triangles\n\tuvt += vec4( TRI_INTERSECT_EPSILON );\n\n\treturn all( greaterThanEqual( uvt, vec4( 0.0 ) ) );\n\n}\n\nbool intersectTriangles(\n\tBVH bvh, vec3 rayOrigin, vec3 rayDirection, uint offset, uint count,\n\tinout float minDistance,\n\n\t// output variables\n\tout uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord,\n\tout float side, out float dist\n) {\n\n\tbool found = false;\n\tvec3 localBarycoord, localNormal;\n\tfloat localDist, localSide;\n\tfor ( uint i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\tuvec3 indices = uTexelFetch1D( bvh.index, i ).xyz;\n\t\tvec3 a = texelFetch1D( bvh.position, indices.x ).rgb;\n\t\tvec3 b = texelFetch1D( bvh.position, indices.y ).rgb;\n\t\tvec3 c = texelFetch1D( bvh.position, indices.z ).rgb;\n\n\t\tif (\n\t\t\tintersectsTriangle( rayOrigin, rayDirection, a, b, c, localBarycoord, localNormal, localDist, localSide )\n\t\t\t&& localDist < minDistance\n\t\t) {\n\n\t\t\tfound = true;\n\t\t\tminDistance = localDist;\n\n\t\t\tfaceIndices = uvec4( indices.xyz, i );\n\t\t\tfaceNormal = localNormal;\n\n\t\t\tside = localSide;\n\t\t\tbarycoord = localBarycoord;\n\t\t\tdist = localDist;\n\n\t\t}\n\n\t}\n\n\treturn found;\n\n}\n\nfloat intersectsBVHNodeBounds( vec3 rayOrigin, vec3 rayDirection, BVH bvh, uint currNodeIndex ) {\n\n\tvec3 boundsMin = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 0u ).xyz;\n\tvec3 boundsMax = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 1u ).xyz;\n\treturn intersectsBounds( rayOrigin, rayDirection, boundsMin, boundsMax );\n\n}\n\nbool bvhIntersectFirstHit(\n\tBVH bvh, vec3 rayOrigin, vec3 rayDirection,\n\n\t// output variables\n\tout uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord,\n\tout float side, out float dist\n) {\n\n\t// stack needs to be twice as long as the deepest tree we expect because\n\t// we push both the left and right child onto the stack every traversal\n\tint ptr = 0;\n\tuint stack[ 60 ];\n\tstack[ 0 ] = 0u;\n\n\tfloat triangleDistance = 1e20;\n\tbool found = false;\n\twhile ( ptr > - 1 && ptr < 60 ) {\n\n\t\tuint currNodeIndex = stack[ ptr ];\n\t\tptr --;\n\n\t\t// check if we intersect the current bounds\n\t\tfloat boundsHitDistance = intersectsBVHNodeBounds( rayOrigin, rayDirection, bvh, currNodeIndex );\n\t\tif ( boundsHitDistance == INFINITY || boundsHitDistance > triangleDistance ) {\n\n\t\t\tcontinue;\n\n\t\t}\n\n\t\tuvec2 boundsInfo = uTexelFetch1D( bvh.bvhContents, currNodeIndex ).xy;\n\t\tbool isLeaf = bool( boundsInfo.x & 0xffff0000u );\n\n\t\tif ( isLeaf ) {\n\n\t\t\tuint count = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint offset = boundsInfo.y;\n\n\t\t\tfound = intersectTriangles(\n\t\t\t\tbvh, rayOrigin, rayDirection, offset, count, triangleDistance,\n\t\t\t\tfaceIndices, faceNormal, barycoord, side, dist\n\t\t\t) || found;\n\n\t\t} else {\n\n\t\t\tuint leftIndex = currNodeIndex + 1u;\n\t\t\tuint splitAxis = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint rightIndex = boundsInfo.y;\n\n\t\t\tbool leftToRight = rayDirection[ splitAxis ] >= 0.0;\n\t\t\tuint c1 = leftToRight ? leftIndex : rightIndex;\n\t\t\tuint c2 = leftToRight ? rightIndex : leftIndex;\n\n\t\t\t// set c2 in the stack so we traverse it later. We need to keep track of a pointer in\n\t\t\t// the stack while we traverse. The second pointer added is the one that will be\n\t\t\t// traversed first\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c2;\n\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c1;\n\n\t\t}\n\n\t}\n\n\treturn found;\n\n}\n`;\n\n// Distance to Point\nexport const shaderDistanceFunction = /* glsl */`\n\nfloat dot2( in vec3 v ) {\n\n\treturn dot( v, v );\n\n}\n\n\n// https://www.shadertoy.com/view/ttfGWl\nvec3 closestPointToTriangle( vec3 p, vec3 v0, vec3 v1, vec3 v2, out vec3 barycoord ) {\n\n    vec3 v10 = v1 - v0;\n    vec3 v21 = v2 - v1;\n    vec3 v02 = v0 - v2;\n\n\tvec3 p0 = p - v0;\n\tvec3 p1 = p - v1;\n\tvec3 p2 = p - v2;\n\n    vec3 nor = cross( v10, v02 );\n\n    // method 2, in barycentric space\n    vec3  q = cross( nor, p0 );\n    float d = 1.0 / dot2( nor );\n    float u = d * dot( q, v02 );\n    float v = d * dot( q, v10 );\n    float w = 1.0 - u - v;\n\n\tif( u < 0.0 ) {\n\n\t\tw = clamp( dot( p2, v02 ) / dot2( v02 ), 0.0, 1.0 );\n\t\tu = 0.0;\n\t\tv = 1.0 - w;\n\n\t} else if( v < 0.0 ) {\n\n\t\tu = clamp( dot( p0, v10 ) / dot2( v10 ), 0.0, 1.0 );\n\t\tv = 0.0;\n\t\tw = 1.0 - u;\n\n\t} else if( w < 0.0 ) {\n\n\t\tv = clamp( dot( p1, v21 ) / dot2( v21 ), 0.0, 1.0 );\n\t\tw = 0.0;\n\t\tu = 1.0-v;\n\n\t}\n\n\tbarycoord = vec3( u, v, w );\n    return u * v1 + v * v2 + w * v0;\n\n}\n\nfloat distanceToTriangles(\n\tBVH bvh, vec3 point, uint offset, uint count, float closestDistanceSquared,\n\n\tout uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord, out float side, out vec3 outPoint\n) {\n\n\tbool found = false;\n\tuvec3 localIndices;\n\tvec3 localBarycoord;\n\tvec3 localNormal;\n\tfor ( uint i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\tuvec3 indices = uTexelFetch1D( bvh.index, i ).xyz;\n\t\tvec3 a = texelFetch1D( bvh.position, indices.x ).rgb;\n\t\tvec3 b = texelFetch1D( bvh.position, indices.y ).rgb;\n\t\tvec3 c = texelFetch1D( bvh.position, indices.z ).rgb;\n\n\t\t// get the closest point and barycoord\n\t\tvec3 closestPoint = closestPointToTriangle( point, a, b, c, localBarycoord );\n\t\tvec3 delta = point - closestPoint;\n\t\tfloat sqDist = dot2( delta );\n\t\tif ( sqDist < closestDistanceSquared ) {\n\n\t\t\t// set the output results\n\t\t\tclosestDistanceSquared = sqDist;\n\t\t\tfaceIndices = uvec4( indices.xyz, i );\n\t\t\tfaceNormal = normalize( cross( a - b, b - c ) );\n\t\t\tbarycoord = localBarycoord;\n\t\t\toutPoint = closestPoint;\n\t\t\tside = sign( dot( faceNormal, delta ) );\n\n\t\t}\n\n\t}\n\n\treturn closestDistanceSquared;\n\n}\n\nfloat distanceSqToBounds( vec3 point, vec3 boundsMin, vec3 boundsMax ) {\n\n\tvec3 clampedPoint = clamp( point, boundsMin, boundsMax );\n\tvec3 delta = point - clampedPoint;\n\treturn dot( delta, delta );\n\n}\n\nfloat distanceSqToBVHNodeBoundsPoint( vec3 point, BVH bvh, uint currNodeIndex ) {\n\n\tvec3 boundsMin = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 0u ).xyz;\n\tvec3 boundsMax = texelFetch1D( bvh.bvhBounds, currNodeIndex * 2u + 1u ).xyz;\n\treturn distanceSqToBounds( point, boundsMin, boundsMax );\n\n}\n\nfloat bvhClosestPointToPoint(\n\tBVH bvh, vec3 point,\n\n\t// output variables\n\tout uvec4 faceIndices, out vec3 faceNormal, out vec3 barycoord,\n\tout float side, out vec3 outPoint\n ) {\n\n\t// stack needs to be twice as long as the deepest tree we expect because\n\t// we push both the left and right child onto the stack every traversal\n\tint ptr = 0;\n\tuint stack[ 60 ];\n\tstack[ 0 ] = 0u;\n\tfloat closestDistanceSquared = pow( 100000.0, 2.0 );\n\tbool found = false;\n\twhile ( ptr > - 1 && ptr < 60 ) {\n\n\t\tuint currNodeIndex = stack[ ptr ];\n\t\tptr --;\n\n\t\t// check if we intersect the current bounds\n\t\tfloat boundsHitDistance = distanceSqToBVHNodeBoundsPoint( point, bvh, currNodeIndex );\n\t\tif ( boundsHitDistance > closestDistanceSquared ) {\n\n\t\t\tcontinue;\n\n\t\t}\n\n\t\tuvec2 boundsInfo = uTexelFetch1D( bvh.bvhContents, currNodeIndex ).xy;\n\t\tbool isLeaf = bool( boundsInfo.x & 0xffff0000u );\n\t\tif ( isLeaf ) {\n\n\t\t\tuint count = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint offset = boundsInfo.y;\n\t\t\tclosestDistanceSquared = distanceToTriangles(\n\t\t\t\tbvh, point, offset, count, closestDistanceSquared,\n\n\t\t\t\t// outputs\n\t\t\t\tfaceIndices, faceNormal, barycoord, side, outPoint\n\t\t\t);\n\n\t\t} else {\n\n\t\t\tuint leftIndex = currNodeIndex + 1u;\n\t\t\tuint splitAxis = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint rightIndex = boundsInfo.y;\n\t\t\tbool leftToRight = distanceSqToBVHNodeBoundsPoint( point, bvh, leftIndex ) < distanceSqToBVHNodeBoundsPoint( point, bvh, rightIndex );//rayDirection[ splitAxis ] >= 0.0;\n\t\t\tuint c1 = leftToRight ? leftIndex : rightIndex;\n\t\t\tuint c2 = leftToRight ? rightIndex : leftIndex;\n\n\t\t\t// set c2 in the stack so we traverse it later. We need to keep track of a pointer in\n\t\t\t// the stack while we traverse. The second pointer added is the one that will be\n\t\t\t// traversed first\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c2;\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c1;\n\n\t\t}\n\n\t}\n\n\treturn sqrt( closestDistanceSquared );\n\n}\n`;\n", "import { Buffer<PERSON>ttribute, BufferGeometry, Vector3, Vector4, Matrix4, Matrix3 } from 'three';\n\nconst _positionVector = /*@__PURE__*/ new Vector3();\nconst _normalVector = /*@__PURE__*/ new Vector3();\nconst _tangentVector = /*@__PURE__*/ new Vector3();\nconst _tangentVector4 = /*@__PURE__*/ new Vector4();\n\nconst _morphVector = /*@__PURE__*/ new Vector3();\nconst _temp = /*@__PURE__*/ new Vector3();\n\nconst _skinIndex = /*@__PURE__*/ new Vector4();\nconst _skinWeight = /*@__PURE__*/ new Vector4();\nconst _matrix = /*@__PURE__*/ new Matrix4();\nconst _boneMatrix = /*@__PURE__*/ new Matrix4();\n\n// Confirms that the two provided attributes are compatible\nfunction validateAttributes( attr1, attr2 ) {\n\n\tif ( ! attr1 && ! attr2 ) {\n\n\t\treturn;\n\n\t}\n\n\tconst sameCount = attr1.count === attr2.count;\n\tconst sameNormalized = attr1.normalized === attr2.normalized;\n\tconst sameType = attr1.array.constructor === attr2.array.constructor;\n\tconst sameItemSize = attr1.itemSize === attr2.itemSize;\n\n\tif ( ! sameCount || ! sameNormalized || ! sameType || ! sameItemSize ) {\n\n\t\tthrow new Error();\n\n\t}\n\n}\n\n// Clones the given attribute with a new compatible buffer attribute but no data\nfunction createAttributeClone( attr, countOverride = null ) {\n\n\tconst cons = attr.array.constructor;\n\tconst normalized = attr.normalized;\n\tconst itemSize = attr.itemSize;\n\tconst count = countOverride === null ? attr.count : countOverride;\n\n\treturn new BufferAttribute( new cons( itemSize * count ), itemSize, normalized );\n\n}\n\n// target offset is the number of elements in the target buffer stride to skip before copying the\n// attributes contents in to.\nfunction copyAttributeContents( attr, target, targetOffset = 0 ) {\n\n\tif ( attr.isInterleavedBufferAttribute ) {\n\n\t\tconst itemSize = attr.itemSize;\n\t\tfor ( let i = 0, l = attr.count; i < l; i ++ ) {\n\n\t\t\tconst io = i + targetOffset;\n\t\t\ttarget.setX( io, attr.getX( i ) );\n\t\t\tif ( itemSize >= 2 ) target.setY( io, attr.getY( i ) );\n\t\t\tif ( itemSize >= 3 ) target.setZ( io, attr.getZ( i ) );\n\t\t\tif ( itemSize >= 4 ) target.setW( io, attr.getW( i ) );\n\n\t\t}\n\n\t} else {\n\n\t\tconst array = target.array;\n\t\tconst cons = array.constructor;\n\t\tconst byteOffset = array.BYTES_PER_ELEMENT * attr.itemSize * targetOffset;\n\t\tconst temp = new cons( array.buffer, byteOffset, attr.array.length );\n\t\ttemp.set( attr.array );\n\n\t}\n\n}\n\n// Adds the \"matrix\" multiplied by \"scale\" to \"target\"\nfunction addScaledMatrix( target, matrix, scale ) {\n\n\tconst targetArray = target.elements;\n\tconst matrixArray = matrix.elements;\n\tfor ( let i = 0, l = matrixArray.length; i < l; i ++ ) {\n\n\t\ttargetArray[ i ] += matrixArray[ i ] * scale;\n\n\t}\n\n}\n\n// A version of \"SkinnedMesh.boneTransform\" for normals\nfunction boneNormalTransform( mesh, index, target ) {\n\n\tconst skeleton = mesh.skeleton;\n\tconst geometry = mesh.geometry;\n\tconst bones = skeleton.bones;\n\tconst boneInverses = skeleton.boneInverses;\n\n\t_skinIndex.fromBufferAttribute( geometry.attributes.skinIndex, index );\n\t_skinWeight.fromBufferAttribute( geometry.attributes.skinWeight, index );\n\n\t_matrix.elements.fill( 0 );\n\n\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\tconst weight = _skinWeight.getComponent( i );\n\n\t\tif ( weight !== 0 ) {\n\n\t\t\tconst boneIndex = _skinIndex.getComponent( i );\n\t\t\t_boneMatrix.multiplyMatrices( bones[ boneIndex ].matrixWorld, boneInverses[ boneIndex ] );\n\n\t\t\taddScaledMatrix( _matrix, _boneMatrix, weight );\n\n\t\t}\n\n\t}\n\n\t_matrix.multiply( mesh.bindMatrix ).premultiply( mesh.bindMatrixInverse );\n\ttarget.transformDirection( _matrix );\n\n\treturn target;\n\n}\n\n// Applies the morph target data to the target vector\nfunction applyMorphTarget( morphData, morphInfluences, morphTargetsRelative, i, target ) {\n\n\t_morphVector.set( 0, 0, 0 );\n\tfor ( let j = 0, jl = morphData.length; j < jl; j ++ ) {\n\n\t\tconst influence = morphInfluences[ j ];\n\t\tconst morphAttribute = morphData[ j ];\n\n\t\tif ( influence === 0 ) continue;\n\n\t\t_temp.fromBufferAttribute( morphAttribute, i );\n\n\t\tif ( morphTargetsRelative ) {\n\n\t\t\t_morphVector.addScaledVector( _temp, influence );\n\n\t\t} else {\n\n\t\t\t_morphVector.addScaledVector( _temp.sub( target ), influence );\n\n\t\t}\n\n\t}\n\n\ttarget.add( _morphVector );\n\n}\n\n// Modified version of BufferGeometryUtils.mergeBufferGeometries that ignores morph targets and updates a attributes in place\nfunction mergeBufferGeometries( geometries, options = { useGroups: false, updateIndex: false, skipAttributes: [] }, targetGeometry = new BufferGeometry() ) {\n\n\tconst isIndexed = geometries[ 0 ].index !== null;\n\tconst { useGroups = false, updateIndex = false, skipAttributes = [] } = options;\n\n\tconst attributesUsed = new Set( Object.keys( geometries[ 0 ].attributes ) );\n\tconst attributes = {};\n\n\tlet offset = 0;\n\n\ttargetGeometry.clearGroups();\n\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\tconst geometry = geometries[ i ];\n\t\tlet attributesCount = 0;\n\n\t\t// ensure that all geometries are indexed, or none\n\t\tif ( isIndexed !== ( geometry.index !== null ) ) {\n\n\t\t\tthrow new Error( 'StaticGeometryGenerator: All geometries must have compatible attributes; make sure index attribute exists among all geometries, or in none of them.' );\n\n\t\t}\n\n\t\t// gather attributes, exit early if they're different\n\t\tfor ( const name in geometry.attributes ) {\n\n\t\t\tif ( ! attributesUsed.has( name ) ) {\n\n\t\t\t\tthrow new Error( 'StaticGeometryGenerator: All geometries must have compatible attributes; make sure \"' + name + '\" attribute exists among all geometries, or in none of them.' );\n\n\t\t\t}\n\n\t\t\tif ( attributes[ name ] === undefined ) {\n\n\t\t\t\tattributes[ name ] = [];\n\n\t\t\t}\n\n\t\t\tattributes[ name ].push( geometry.attributes[ name ] );\n\t\t\tattributesCount ++;\n\n\t\t}\n\n\t\t// ensure geometries have the same number of attributes\n\t\tif ( attributesCount !== attributesUsed.size ) {\n\n\t\t\tthrow new Error( 'StaticGeometryGenerator: Make sure all geometries have the same number of attributes.' );\n\n\t\t}\n\n\t\tif ( useGroups ) {\n\n\t\t\tlet count;\n\t\t\tif ( isIndexed ) {\n\n\t\t\t\tcount = geometry.index.count;\n\n\t\t\t} else if ( geometry.attributes.position !== undefined ) {\n\n\t\t\t\tcount = geometry.attributes.position.count;\n\n\t\t\t} else {\n\n\t\t\t\tthrow new Error( 'StaticGeometryGenerator: The geometry must have either an index or a position attribute' );\n\n\t\t\t}\n\n\t\t\ttargetGeometry.addGroup( offset, count, i );\n\t\t\toffset += count;\n\n\t\t}\n\n\t}\n\n\t// merge indices\n\tif ( isIndexed ) {\n\n\t\tlet forceUpdateIndex = false;\n\t\tif ( ! targetGeometry.index ) {\n\n\t\t\tlet indexCount = 0;\n\t\t\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\t\t\tindexCount += geometries[ i ].index.count;\n\n\t\t\t}\n\n\t\t\ttargetGeometry.setIndex( new BufferAttribute( new Uint32Array( indexCount ), 1, false ) );\n\t\t\tforceUpdateIndex = true;\n\n\t\t}\n\n\t\tif ( updateIndex || forceUpdateIndex ) {\n\n\t\t\tconst targetIndex = targetGeometry.index;\n\t\t\tlet targetOffset = 0;\n\t\t\tlet indexOffset = 0;\n\t\t\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\t\t\tconst geometry = geometries[ i ];\n\t\t\t\tconst index = geometry.index;\n\t\t\t\tif ( skipAttributes[ i ] !== true ) {\n\n\t\t\t\t\tfor ( let j = 0; j < index.count; ++ j ) {\n\n\t\t\t\t\t\ttargetIndex.setX( targetOffset, index.getX( j ) + indexOffset );\n\t\t\t\t\t\ttargetOffset ++;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tindexOffset += geometry.attributes.position.count;\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t// merge attributes\n\tfor ( const name in attributes ) {\n\n\t\tconst attrList = attributes[ name ];\n\t\tif ( ! ( name in targetGeometry.attributes ) ) {\n\n\t\t\tlet count = 0;\n\t\t\tfor ( const key in attrList ) {\n\n\t\t\t\tcount += attrList[ key ].count;\n\n\t\t\t}\n\n\t\t\ttargetGeometry.setAttribute( name, createAttributeClone( attributes[ name ][ 0 ], count ) );\n\n\t\t}\n\n\t\tconst targetAttribute = targetGeometry.attributes[ name ];\n\t\tlet offset = 0;\n\t\tfor ( let i = 0, l = attrList.length; i < l; i ++ ) {\n\n\t\t\tconst attr = attrList[ i ];\n\t\t\tif ( skipAttributes[ i ] !== true ) {\n\n\t\t\t\tcopyAttributeContents( attr, targetAttribute, offset );\n\n\t\t\t}\n\n\t\t\toffset += attr.count;\n\n\t\t}\n\n\t}\n\n\treturn targetGeometry;\n\n}\n\nfunction checkTypedArrayEquality( a, b ) {\n\n\tif ( a === null || b === null ) {\n\n\t\treturn a === b;\n\n\t}\n\n\tif ( a.length !== b.length ) {\n\n\t\treturn false;\n\n\t}\n\n\tfor ( let i = 0, l = a.length; i < l; i ++ ) {\n\n\t\tif ( a[ i ] !== b[ i ] ) {\n\n\t\t\treturn false;\n\n\t\t}\n\n\t}\n\n\treturn true;\n\n}\n\n// Checks whether the geometry changed between this and last evaluation\nclass GeometryDiff {\n\n\tconstructor( mesh ) {\n\n\t\tthis.matrixWorld = new Matrix4();\n\t\tthis.geometryHash = null;\n\t\tthis.boneMatrices = null;\n\t\tthis.primitiveCount = - 1;\n\t\tthis.mesh = mesh;\n\n\t\tthis.update();\n\n\t}\n\n\tupdate() {\n\n\t\tconst mesh = this.mesh;\n\t\tconst geometry = mesh.geometry;\n\t\tconst skeleton = mesh.skeleton;\n\t\tconst primitiveCount = ( geometry.index ? geometry.index.count : geometry.attributes.position.count ) / 3;\n\t\tthis.matrixWorld.copy( mesh.matrixWorld );\n\t\tthis.geometryHash = geometry.attributes.position.version;\n\t\tthis.primitiveCount = primitiveCount;\n\n\t\tif ( skeleton ) {\n\n\t\t\t// ensure the bone matrix array is updated to the appropriate length\n\t\t\tif ( ! skeleton.boneTexture ) {\n\n\t\t\t\tskeleton.computeBoneTexture();\n\n\t\t\t}\n\n\t\t\tskeleton.update();\n\n\t\t\t// copy data if possible otherwise clone it\n\t\t\tconst boneMatrices = skeleton.boneMatrices;\n\t\t\tif ( ! this.boneMatrices || this.boneMatrices.length !== boneMatrices.length ) {\n\n\t\t\t\tthis.boneMatrices = boneMatrices.slice();\n\n\t\t\t} else {\n\n\t\t\t\tthis.boneMatrices.set( boneMatrices );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tthis.boneMatrices = null;\n\n\t\t}\n\n\t}\n\n\tdidChange() {\n\n\t\tconst mesh = this.mesh;\n\t\tconst geometry = mesh.geometry;\n\t\tconst primitiveCount = ( geometry.index ? geometry.index.count : geometry.attributes.position.count ) / 3;\n\t\tconst identical =\n\t\t\tthis.matrixWorld.equals( mesh.matrixWorld ) &&\n\t\t\tthis.geometryHash === geometry.attributes.position.version &&\n\t\t\tcheckTypedArrayEquality( mesh.skeleton && mesh.skeleton.boneMatrices || null, this.boneMatrices ) &&\n\t\t\tthis.primitiveCount === primitiveCount;\n\n\t\treturn ! identical;\n\n\t}\n\n}\n\nexport class StaticGeometryGenerator {\n\n\tconstructor( meshes ) {\n\n\t\tif ( ! Array.isArray( meshes ) ) {\n\n\t\t\tmeshes = [ meshes ];\n\n\t\t}\n\n\t\tconst finalMeshes = [];\n\t\tmeshes.forEach( object => {\n\n\t\t\tobject.traverseVisible( c => {\n\n\t\t\t\tif ( c.isMesh ) {\n\n\t\t\t\t\tfinalMeshes.push( c );\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t} );\n\n\t\tthis.meshes = finalMeshes;\n\t\tthis.useGroups = true;\n\t\tthis.applyWorldTransforms = true;\n\t\tthis.attributes = [ 'position', 'normal', 'color', 'tangent', 'uv', 'uv2' ];\n\t\tthis._intermediateGeometry = new Array( finalMeshes.length ).fill().map( () => new BufferGeometry() );\n\t\tthis._diffMap = new WeakMap();\n\n\t}\n\n\tgetMaterials() {\n\n\t\tconst materials = [];\n\t\tthis.meshes.forEach( mesh => {\n\n\t\t\tif ( Array.isArray( mesh.material ) ) {\n\n\t\t\t\tmaterials.push( ...mesh.material );\n\n\t\t\t} else {\n\n\t\t\t\tmaterials.push( mesh.material );\n\n\t\t\t}\n\n\t\t} );\n\t\treturn materials;\n\n\t}\n\n\tgenerate( targetGeometry = new BufferGeometry() ) {\n\n\t\t// track which attributes have been updated and which to skip to avoid unnecessary attribute copies\n\t\tlet skipAttributes = [];\n\t\tconst { meshes, useGroups, _intermediateGeometry, _diffMap } = this;\n\t\tfor ( let i = 0, l = meshes.length; i < l; i ++ ) {\n\n\t\t\tconst mesh = meshes[ i ];\n\t\t\tconst geom = _intermediateGeometry[ i ];\n\t\t\tconst diff = _diffMap.get( mesh );\n\t\t\tif ( ! diff || diff.didChange( mesh ) ) {\n\n\t\t\t\tthis._convertToStaticGeometry( mesh, geom );\n\t\t\t\tskipAttributes.push( false );\n\n\t\t\t\tif ( ! diff ) {\n\n\t\t\t\t\t_diffMap.set( mesh, new GeometryDiff( mesh ) );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tdiff.update();\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tskipAttributes.push( true );\n\n\t\t\t}\n\n\t\t}\n\n\t\tmergeBufferGeometries( _intermediateGeometry, { useGroups, skipAttributes }, targetGeometry );\n\n\t\tfor ( const key in targetGeometry.attributes ) {\n\n\t\t\ttargetGeometry.attributes[ key ].needsUpdate = true;\n\n\t\t}\n\n\t\treturn targetGeometry;\n\n\t}\n\n\t_convertToStaticGeometry( mesh, targetGeometry = new BufferGeometry() ) {\n\n\t\tconst geometry = mesh.geometry;\n\t\tconst applyWorldTransforms = this.applyWorldTransforms;\n\t\tconst includeNormal = this.attributes.includes( 'normal' );\n\t\tconst includeTangent = this.attributes.includes( 'tangent' );\n\t\tconst attributes = geometry.attributes;\n\t\tconst targetAttributes = targetGeometry.attributes;\n\n\t\t// initialize the attributes if they don't exist\n\t\tif ( ! targetGeometry.index ) {\n\n\t\t\ttargetGeometry.index = geometry.index;\n\n\t\t}\n\n\t\tif ( ! targetAttributes.position ) {\n\n\t\t\ttargetGeometry.setAttribute( 'position', createAttributeClone( attributes.position ) );\n\n\t\t}\n\n\t\tif ( includeNormal && ! targetAttributes.normal && attributes.normal ) {\n\n\t\t\ttargetGeometry.setAttribute( 'normal', createAttributeClone( attributes.normal ) );\n\n\t\t}\n\n\t\tif ( includeTangent && ! targetAttributes.tangent && attributes.tangent ) {\n\n\t\t\ttargetGeometry.setAttribute( 'tangent', createAttributeClone( attributes.tangent ) );\n\n\t\t}\n\n\t\t// ensure the attributes are consistent\n\t\tvalidateAttributes( geometry.index, targetGeometry.index );\n\t\tvalidateAttributes( attributes.position, targetAttributes.position );\n\n\t\tif ( includeNormal ) {\n\n\t\t\tvalidateAttributes( attributes.normal, targetAttributes.normal );\n\n\t\t}\n\n\t\tif ( includeTangent ) {\n\n\t\t\tvalidateAttributes( attributes.tangent, targetAttributes.tangent );\n\n\t\t}\n\n\t\t// generate transformed vertex attribute data\n\t\tconst position = attributes.position;\n\t\tconst normal = includeNormal ? attributes.normal : null;\n\t\tconst tangent = includeTangent ? attributes.tangent : null;\n\t\tconst morphPosition = geometry.morphAttributes.position;\n\t\tconst morphNormal = geometry.morphAttributes.normal;\n\t\tconst morphTangent = geometry.morphAttributes.tangent;\n\t\tconst morphTargetsRelative = geometry.morphTargetsRelative;\n\t\tconst morphInfluences = mesh.morphTargetInfluences;\n\t\tconst normalMatrix = new Matrix3();\n\t\tnormalMatrix.getNormalMatrix( mesh.matrixWorld );\n\n\t\tfor ( let i = 0, l = attributes.position.count; i < l; i ++ ) {\n\n\t\t\t_positionVector.fromBufferAttribute( position, i );\n\t\t\tif ( normal ) {\n\n\t\t\t\t_normalVector.fromBufferAttribute( normal, i );\n\n\t\t\t}\n\n\t\t\tif ( tangent ) {\n\n\t\t\t\t_tangentVector4.fromBufferAttribute( tangent, i );\n\t\t\t\t_tangentVector.fromBufferAttribute( tangent, i );\n\n\t\t\t}\n\n\t\t\t// apply morph target transform\n\t\t\tif ( morphInfluences ) {\n\n\t\t\t\tif ( morphPosition ) {\n\n\t\t\t\t\tapplyMorphTarget( morphPosition, morphInfluences, morphTargetsRelative, i, _positionVector );\n\n\t\t\t\t}\n\n\t\t\t\tif ( morphNormal ) {\n\n\t\t\t\t\tapplyMorphTarget( morphNormal, morphInfluences, morphTargetsRelative, i, _normalVector );\n\n\t\t\t\t}\n\n\t\t\t\tif ( morphTangent ) {\n\n\t\t\t\t\tapplyMorphTarget( morphTangent, morphInfluences, morphTargetsRelative, i, _tangentVector );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// apply bone transform\n\t\t\tif ( mesh.isSkinnedMesh ) {\n\n\t\t\t\tmesh.boneTransform( i, _positionVector );\n\t\t\t\tif ( normal ) {\n\n\t\t\t\t\tboneNormalTransform( mesh, i, _normalVector );\n\n\t\t\t\t}\n\n\t\t\t\tif ( tangent ) {\n\n\t\t\t\t\tboneNormalTransform( mesh, i, _tangentVector );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// update the vectors of the attributes\n\t\t\tif ( applyWorldTransforms ) {\n\n\t\t\t\t_positionVector.applyMatrix4( mesh.matrixWorld );\n\n\t\t\t}\n\n\t\t\ttargetAttributes.position.setXYZ( i, _positionVector.x, _positionVector.y, _positionVector.z );\n\n\t\t\tif ( normal ) {\n\n\t\t\t\tif ( applyWorldTransforms ) {\n\n\t\t\t\t\t_normalVector.applyNormalMatrix( normalMatrix );\n\n\t\t\t\t}\n\n\t\t\t\ttargetAttributes.normal.setXYZ( i, _normalVector.x, _normalVector.y, _normalVector.z );\n\n\t\t\t}\n\n\t\t\tif ( tangent ) {\n\n\t\t\t\tif ( applyWorldTransforms ) {\n\n\t\t\t\t\t_tangentVector.transformDirection( mesh.matrixWorld );\n\n\t\t\t\t}\n\n\t\t\t\ttargetAttributes.tangent.setXYZW( i, _tangentVector.x, _tangentVector.y, _tangentVector.z, _tangentVector4.w );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// copy other attributes over\n\t\tfor ( const i in this.attributes ) {\n\n\t\t\tconst key = this.attributes[ i ];\n\t\t\tif ( key === 'position' || key === 'tangent' || key === 'normal' || ! ( key in attributes ) ) {\n\n\t\t\t\tcontinue;\n\n\t\t\t}\n\n\t\t\tif ( ! targetAttributes[ key ] ) {\n\n\t\t\t\ttargetGeometry.setAttribute( key, createAttributeClone( attributes[ key ] ) );\n\n\t\t\t}\n\n\t\t\tvalidateAttributes( attributes[ key ], targetAttributes[ key ] );\n\t\t\tcopyAttributeContents( attributes[ key ], targetAttributes[ key ] );\n\n\t\t}\n\n\t\treturn targetGeometry;\n\n\t}\n\n}\n"], "names": ["boundingBox"], "mappings": ";;AAAA;AACY,MAAC,MAAM,GAAG,EAAE;AACZ,MAAC,OAAO,GAAG,EAAE;AACb,MAAC,GAAG,GAAG,EAAE;AACrB;AACA;AACY,MAAC,eAAe,GAAG,EAAE;AACrB,MAAC,WAAW,GAAG,EAAE;AACjB,MAAC,SAAS,GAAG,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACO,MAAM,uBAAuB,GAAG,IAAI,CAAC;AACrC,MAAM,cAAc,GAAG,CAAC,CAAC;AAChC;AACA;AACA;AACO,MAAM,cAAc,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACrC,MAAM,gBAAgB,GAAG,MAAM,CAAC;AACvC;AACA;AACA;AACO,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE;;ACxB3C,MAAM,WAAW,CAAC;AACzB;AACA,CAAC,WAAW,GAAG;AACf;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;;ACTO,SAAS,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,GAAG;AACzD;AACA,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,EAAE,CAAC;AACrC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AACzC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AACzC;AACA,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AACzC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AACzC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AACzC;AACA,CAAC,OAAO,MAAM,CAAC;AACf;AACA,CAAC;AACD;AACO,SAAS,mBAAmB,EAAE,MAAM,GAAG;AAC9C;AACA,CAAC,IAAI,WAAW,GAAG,EAAE,CAAC,CAAC;AACvB,CAAC,IAAI,SAAS,GAAG,EAAE,QAAQ,CAAC;AAC5B;AACA,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChC;AACA,EAAE,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;AAC7C,EAAE,KAAK,IAAI,GAAG,SAAS,GAAG;AAC1B;AACA,GAAG,SAAS,GAAG,IAAI,CAAC;AACpB,GAAG,WAAW,GAAG,CAAC,CAAC;AACnB;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC,OAAO,WAAW,CAAC;AACpB;AACA,CAAC;AACD;AACA;AACO,SAAS,UAAU,EAAE,MAAM,EAAE,MAAM,GAAG;AAC7C;AACA,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC;AACtB;AACA,CAAC;AACD;AACA;AACO,SAAS,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG;AAC5C;AACA,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC;AAChB,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChC;AACA,EAAE,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AACnB;AACA;AACA,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;AAChB,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;AAChB,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAC1C;AACA;AACA,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AACjB,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AACjB,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAC3C;AACA,EAAE;AACF;AACA,CAAC;AACD;AACA;AACO,SAAS,sBAAsB,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,GAAG;AAC7E;AACA,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChC;AACA,EAAE,MAAM,OAAO,GAAG,cAAc,EAAE,UAAU,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACvD,EAAE,MAAM,KAAK,GAAG,cAAc,EAAE,UAAU,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACzD;AACA,EAAE,MAAM,IAAI,GAAG,OAAO,GAAG,KAAK,CAAC;AAC/B,EAAE,MAAM,IAAI,GAAG,OAAO,GAAG,KAAK,CAAC;AAC/B;AACA,EAAE,KAAK,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG;AAC5B;AACA,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AACtB;AACA,GAAG;AACH;AACA,EAAE,KAAK,IAAI,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG;AAChC;AACA,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC1B;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC;AACD;AACA;AACO,SAAS,kBAAkB,EAAE,MAAM,GAAG;AAC7C;AACA,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;AACtC,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;AACtC,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;AACtC;AACA,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC5C;AACA;;AC5FA,SAAS,WAAW,EAAE,GAAG,EAAE,OAAO,GAAG;AACrC;AACA,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG;AACpB;AACA,EAAE,MAAM,WAAW,GAAG,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;AACpD,EAAE,MAAM,iBAAiB,GAAG,OAAO,CAAC,oBAAoB,GAAG,iBAAiB,GAAG,WAAW,CAAC;AAC3F,EAAE,IAAI,KAAK,CAAC;AACZ,EAAE,KAAK,WAAW,GAAG,KAAK,GAAG;AAC7B;AACA,GAAG,KAAK,GAAG,IAAI,WAAW,EAAE,IAAI,iBAAiB,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC;AACvE;AACA,GAAG,MAAM;AACT;AACA,GAAG,KAAK,GAAG,IAAI,WAAW,EAAE,IAAI,iBAAiB,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC;AACvE;AACA,GAAG;AACH;AACA,EAAE,GAAG,CAAC,QAAQ,EAAE,IAAI,eAAe,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;AAClD;AACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,GAAG;AAC3C;AACA,GAAG,KAAK,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AAClB;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,EAAE,GAAG,GAAG;AACnC;AACA,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG;AAC5C;AACA,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,EAAE,CAAC;AACvD;AACA,EAAE;AACF;AACA,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC;AACnB,CAAC,MAAM,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;AACnC,CAAC,MAAM,MAAM,KAAK,IAAI,GAAG,CAAC,MAAM,GAAG;AACnC;AACA,EAAE,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;AACrC,EAAE,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AACnD;AACA,EAAE;AACF;AACA;AACA,CAAC,MAAM,gBAAgB,GAAG,KAAK,CAAC,IAAI,EAAE,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;AAC3F,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC1D;AACA,EAAE,MAAM,KAAK,GAAG,gBAAgB,EAAE,CAAC,EAAE,EAAE,GAAG,GAAG,gBAAgB,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AACvE,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,KAAK,GAAG,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,GAAG,GAAG,KAAK,KAAK,CAAC,EAAE,EAAE,CAAC;AACvE;AACA,EAAE;AACF;AACA,CAAC,OAAO,MAAM,CAAC;AACf;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,cAAc,GAAG,IAAI,GAAG;AACnF;AACA,CAAC,IAAI,IAAI,GAAG,QAAQ,CAAC;AACrB,CAAC,IAAI,IAAI,GAAG,QAAQ,CAAC;AACrB,CAAC,IAAI,IAAI,GAAG,QAAQ,CAAC;AACrB,CAAC,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;AACvB,CAAC,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;AACvB,CAAC,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;AACvB;AACA,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC;AACtB,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC;AACtB,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC;AACtB,CAAC,IAAI,KAAK,GAAG,EAAE,QAAQ,CAAC;AACxB,CAAC,IAAI,KAAK,GAAG,EAAE,QAAQ,CAAC;AACxB,CAAC,IAAI,KAAK,GAAG,EAAE,QAAQ,CAAC;AACxB;AACA,CAAC,MAAM,eAAe,GAAG,cAAc,KAAK,IAAI,CAAC;AACjD,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,GAAG,GAAG,EAAE,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG;AAC3E;AACA,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AACrC,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AACrC,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACrB,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACrB,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;AAC7B,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;AAC7B,EAAE,KAAK,eAAe,IAAI,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAClD,EAAE,KAAK,eAAe,IAAI,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAClD;AACA,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AACrC,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AACrC,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACrB,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACrB,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;AAC7B,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;AAC7B,EAAE,KAAK,eAAe,IAAI,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAClD,EAAE,KAAK,eAAe,IAAI,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAClD;AACA,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AACrC,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AACrC,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACrB,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACrB,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;AAC7B,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;AAC7B,EAAE,KAAK,eAAe,IAAI,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAClD,EAAE,KAAK,eAAe,IAAI,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAClD;AACA,EAAE;AACF;AACA,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AACpB,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AACpB,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AACpB;AACA,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AACpB,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AACpB,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AACpB;AACA,CAAC,KAAK,eAAe,GAAG;AACxB;AACA,EAAE,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC9B,EAAE,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC9B,EAAE,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC9B;AACA,EAAE,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC9B,EAAE,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC9B,EAAE,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC9B;AACA,EAAE;AACF;AACA,CAAC;AACD;AACA;AACA,SAAS,iBAAiB,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,cAAc,GAAG;AAC5E;AACA,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC;AACtB,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC;AACtB,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC;AACtB,CAAC,IAAI,KAAK,GAAG,EAAE,QAAQ,CAAC;AACxB,CAAC,IAAI,KAAK,GAAG,EAAE,QAAQ,CAAC;AACxB,CAAC,IAAI,KAAK,GAAG,EAAE,QAAQ,CAAC;AACxB;AACA,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,GAAG,GAAG,EAAE,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG;AAC3E;AACA,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AACrC,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAC/B,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAC/B;AACA,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AACrC,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAC/B,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAC/B;AACA,EAAE,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AACrC,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAC/B,EAAE,KAAK,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;AAC/B;AACA,EAAE;AACF;AACA,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC7B,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC7B,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC7B;AACA,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC7B,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC7B,CAAC,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC7B;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG;AAClE;AACA,CAAC,IAAI,IAAI,GAAG,MAAM,CAAC;AACnB,CAAC,IAAI,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;AAChC,CAAC,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;AACvB,CAAC,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;AACnC;AACA;AACA,CAAC,QAAQ,IAAI,GAAG;AAChB;AACA,EAAE,QAAQ,IAAI,IAAI,KAAK,IAAI,cAAc,EAAE,IAAI,GAAG,CAAC,GAAG,UAAU,EAAE,GAAG,GAAG,GAAG;AAC3E;AACA,GAAG,IAAI,GAAG,CAAC;AACX;AACA,GAAG;AACH;AACA;AACA;AACA,EAAE,QAAQ,IAAI,IAAI,KAAK,IAAI,cAAc,EAAE,KAAK,GAAG,CAAC,GAAG,UAAU,EAAE,IAAI,GAAG,GAAG;AAC7E;AACA,GAAG,KAAK,GAAG,CAAC;AACZ;AACA,GAAG;AACH;AACA,EAAE,KAAK,IAAI,GAAG,KAAK,GAAG;AACtB;AACA;AACA;AACA;AACA;AACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;AACA,IAAI,IAAI,EAAE,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACnC,IAAI,KAAK,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,KAAK,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACnD,IAAI,KAAK,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;AAChC;AACA,IAAI,IAAI,EAAE,GAAG,cAAc,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACpD,IAAI,cAAc,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,cAAc,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACrF,IAAI,cAAc,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;AACjD;AACA,IAAI,IAAI,EAAE,GAAG,cAAc,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACpD,IAAI,cAAc,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,cAAc,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACrF,IAAI,cAAc,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC;AACjD;AACA,IAAI;AACJ;AACA,GAAG,IAAI,GAAG,CAAC;AACX,GAAG,KAAK,GAAG,CAAC;AACZ;AACA,GAAG,MAAM;AACT;AACA,GAAG,OAAO,IAAI,CAAC;AACf;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC;AACD;AACA,MAAM,SAAS,GAAG,EAAE,CAAC;AACrB,MAAM,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;AACvD,MAAM,OAAO,GAAG,IAAI,KAAK,EAAE,SAAS,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM;AACzD;AACA,CAAC,OAAO;AACR;AACA,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,MAAM,EAAE,IAAI,YAAY,EAAE,CAAC,EAAE;AAC/B,EAAE,gBAAgB,EAAE,IAAI,YAAY,EAAE,CAAC,EAAE;AACzC,EAAE,eAAe,EAAE,IAAI,YAAY,EAAE,CAAC,EAAE;AACxC,EAAE,SAAS,EAAE,CAAC;AACd;AACA,EAAE,CAAC;AACH;AACA,CAAC,EAAE,CAAC;AACJ,MAAM,UAAU,GAAG,IAAI,YAAY,EAAE,CAAC,EAAE,CAAC;AACzC;AACA,SAAS,eAAe,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,GAAG;AAC5G;AACA,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;AAChB,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;AACb;AACA;AACA,CAAC,KAAK,QAAQ,KAAK,MAAM,GAAG;AAC5B;AACA,EAAE,IAAI,GAAG,mBAAmB,EAAE,oBAAoB,EAAE,CAAC;AACrD,EAAE,KAAK,IAAI,KAAK,EAAE,CAAC,GAAG;AACtB;AACA,GAAG,GAAG,GAAG,EAAE,oBAAoB,EAAE,IAAI,EAAE,GAAG,oBAAoB,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;AACjF;AACA,GAAG;AACH;AACA,EAAE,MAAM,KAAK,QAAQ,KAAK,OAAO,GAAG;AACpC;AACA,EAAE,IAAI,GAAG,mBAAmB,EAAE,gBAAgB,EAAE,CAAC;AACjD,EAAE,KAAK,IAAI,KAAK,EAAE,CAAC,GAAG;AACtB;AACA,GAAG,GAAG,GAAG,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;AAC3D;AACA,GAAG;AACH;AACA,EAAE,MAAM,KAAK,QAAQ,KAAK,GAAG,GAAG;AAChC;AACA,EAAE,MAAM,eAAe,GAAG,kBAAkB,EAAE,gBAAgB,EAAE,CAAC;AACjE,EAAE,IAAI,QAAQ,GAAG,uBAAuB,GAAG,KAAK,CAAC;AACjD;AACA;AACA,EAAE,MAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC;AAC5B,EAAE,MAAM,IAAI,GAAG,EAAE,MAAM,GAAG,KAAK,KAAK,CAAC,CAAC;AACtC,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;AACA,GAAG,MAAM,QAAQ,GAAG,oBAAoB,EAAE,CAAC,EAAE,CAAC;AAC9C,GAAG,MAAM,SAAS,GAAG,oBAAoB,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AACnD,GAAG,MAAM,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAC;AAC3C,GAAG,MAAM,QAAQ,GAAG,UAAU,GAAG,SAAS,CAAC;AAC3C;AACA;AACA;AACA,GAAG,KAAK,KAAK,GAAG,SAAS,GAAG,CAAC,GAAG;AAChC;AACA;AACA,IAAI,MAAM,aAAa,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;AACzC,IAAI,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC;AACjC;AACA;AACA,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AACd,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;AACA,KAAK,MAAM,GAAG,GAAG,aAAa,EAAE,CAAC,EAAE,CAAC;AACpC,KAAK,GAAG,CAAC,SAAS,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACjD,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;AACnB;AACA,KAAK,MAAM;AACX,MAAM,MAAM;AACZ,MAAM,eAAe;AACrB,MAAM,gBAAgB;AACtB,MAAM,GAAG,GAAG,CAAC;AACb,KAAK,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACpC;AACA,MAAM,gBAAgB,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC;AACvC,MAAM,gBAAgB,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC;AAC7C;AACA,MAAM,eAAe,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC;AACtC,MAAM,eAAe,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC;AAC5C;AACA,MAAM,MAAM,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC7B,MAAM,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC;AACnC;AACA,MAAM;AACN;AACA,KAAK,sBAAsB,EAAE,CAAC,EAAE,cAAc,EAAE,MAAM,EAAE,CAAC;AACzD;AACA,KAAK;AACL;AACA,IAAI,aAAa,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC;AACnC;AACA;AACA,IAAI,IAAI,UAAU,GAAG,KAAK,CAAC;AAC3B,IAAI,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,EAAE,EAAE,GAAG,GAAG;AAC/C;AACA,KAAK,MAAM,GAAG,GAAG,aAAa,EAAE,EAAE,EAAE,CAAC;AACrC,KAAK,QAAQ,EAAE,GAAG,CAAC,GAAG,UAAU,IAAI,aAAa,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,SAAS,KAAK,GAAG,CAAC,SAAS,GAAG;AAC1F;AACA,MAAM,aAAa,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;AACxC,MAAM,UAAU,GAAG,CAAC;AACpB;AACA,MAAM;AACN;AACA,KAAK;AACL;AACA;AACA,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG;AAC7C;AACA,KAAK,MAAM,MAAM,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AAChD,KAAK,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,EAAE,EAAE,GAAG,GAAG;AAChD;AACA,MAAM,MAAM,GAAG,GAAG,aAAa,EAAE,EAAE,EAAE,CAAC;AACtC,MAAM,KAAK,MAAM,IAAI,GAAG,CAAC,SAAS,GAAG;AACrC;AACA,OAAO,sBAAsB,EAAE,CAAC,EAAE,cAAc,EAAE,GAAG,CAAC,gBAAgB,EAAE,CAAC;AACzE;AACA,OAAO,MAAM;AACb;AACA,OAAO,sBAAsB,EAAE,CAAC,EAAE,cAAc,EAAE,GAAG,CAAC,eAAe,EAAE,CAAC;AACxE,OAAO,GAAG,CAAC,KAAK,GAAG,CAAC;AACpB;AACA,OAAO;AACP;AACA,MAAM;AACN;AACA,KAAK;AACL;AACA;AACA,IAAI,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,UAAU,EAAE,EAAE,GAAG,GAAG;AAC/C;AACA,KAAK,MAAM,GAAG,GAAG,aAAa,EAAE,EAAE,EAAE,CAAC;AACrC,KAAK,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC;AACjC,KAAK,MAAM,UAAU,GAAG,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;AAC1C;AACA;AACA,KAAK,MAAM,UAAU,GAAG,GAAG,CAAC,eAAe,CAAC;AAC5C,KAAK,MAAM,WAAW,GAAG,GAAG,CAAC,gBAAgB,CAAC;AAC9C;AACA,KAAK,IAAI,QAAQ,GAAG,CAAC,CAAC;AACtB,KAAK,KAAK,SAAS,KAAK,CAAC,GAAG;AAC5B;AACA,MAAM,QAAQ,GAAG,kBAAkB,EAAE,UAAU,EAAE,GAAG,eAAe,CAAC;AACpE;AACA,MAAM;AACN;AACA,KAAK,IAAI,SAAS,GAAG,CAAC,CAAC;AACvB,KAAK,KAAK,UAAU,KAAK,CAAC,GAAG;AAC7B;AACA,MAAM,SAAS,GAAG,kBAAkB,EAAE,WAAW,EAAE,GAAG,eAAe,CAAC;AACtE;AACA,MAAM;AACN;AACA,KAAK,MAAM,IAAI,GAAG,cAAc,GAAG,uBAAuB;AAC1D,MAAM,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,UAAU;AACnD,MAAM,CAAC;AACP;AACA,KAAK,KAAK,IAAI,GAAG,QAAQ,GAAG;AAC5B;AACA,MAAM,IAAI,GAAG,CAAC,CAAC;AACf,MAAM,QAAQ,GAAG,IAAI,CAAC;AACtB,MAAM,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC;AAC1B;AACA,MAAM;AACN;AACA,KAAK;AACL;AACA,IAAI,MAAM;AACV;AACA;AACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,GAAG,GAAG;AAC3C;AACA,KAAK,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;AAC9B,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;AACnB,KAAK,GAAG,CAAC,SAAS,GAAG,QAAQ,GAAG,QAAQ,GAAG,CAAC,GAAG,QAAQ,CAAC;AACxD;AACA,KAAK,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;AAC/B,KAAK,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACpC;AACA,MAAM,MAAM,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC7B,MAAM,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC;AACnC;AACA,MAAM;AACN;AACA,KAAK;AACL;AACA;AACA,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG;AAC7C;AACA,KAAK,MAAM,SAAS,GAAG,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACnD,KAAK,MAAM,cAAc,GAAG,SAAS,GAAG,QAAQ,CAAC;AACjD;AACA;AACA;AACA,KAAK,IAAI,QAAQ,GAAG,EAAE,IAAI,cAAc,GAAG,QAAQ,EAAE,CAAC;AACtD,KAAK,KAAK,QAAQ,IAAI,SAAS,GAAG,QAAQ,GAAG,SAAS,GAAG,CAAC,CAAC;AAC3D;AACA,KAAK,MAAM,GAAG,GAAG,OAAO,EAAE,QAAQ,EAAE,CAAC;AACrC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC;AAClB;AACA,KAAK,sBAAsB,EAAE,CAAC,EAAE,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC;AAC7D;AACA,KAAK;AACL;AACA;AACA,IAAI,MAAM,OAAO,GAAG,OAAO,EAAE,SAAS,GAAG,CAAC,EAAE,CAAC;AAC7C,IAAI,UAAU,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,gBAAgB,EAAE,CAAC;AAC3D,IAAI,MAAM,IAAI,CAAC,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AAChD;AACA,KAAK,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;AAC9B,KAAK,MAAM,OAAO,GAAG,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AACtC,KAAK,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,gBAAgB,EAAE,GAAG,CAAC,gBAAgB,EAAE,CAAC;AAC/E;AACA,KAAK;AACL;AACA,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC;AACtB,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC/C;AACA,KAAK,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;AAC9B,KAAK,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC;AAChC,KAAK,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;AAC/B;AACA,KAAK,MAAM,OAAO,GAAG,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AACtC,KAAK,MAAM,WAAW,GAAG,OAAO,CAAC,gBAAgB,CAAC;AAClD;AACA;AACA,KAAK,KAAK,QAAQ,KAAK,CAAC,GAAG;AAC3B;AACA,MAAM,KAAK,SAAS,KAAK,CAAC,GAAG;AAC7B;AACA,OAAO,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;AACxC;AACA,OAAO,MAAM;AACb;AACA,OAAO,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;AACrD;AACA,OAAO;AACP;AACA,MAAM;AACN;AACA,KAAK,SAAS,IAAI,QAAQ,CAAC;AAC3B;AACA;AACA,KAAK,IAAI,QAAQ,GAAG,CAAC,CAAC;AACtB,KAAK,IAAI,SAAS,GAAG,CAAC,CAAC;AACvB;AACA,KAAK,KAAK,SAAS,KAAK,CAAC,GAAG;AAC5B;AACA,MAAM,QAAQ,GAAG,kBAAkB,EAAE,UAAU,EAAE,GAAG,eAAe,CAAC;AACpE;AACA,MAAM;AACN;AACA,KAAK,MAAM,UAAU,GAAG,KAAK,GAAG,SAAS,CAAC;AAC1C,KAAK,KAAK,UAAU,KAAK,CAAC,GAAG;AAC7B;AACA,MAAM,SAAS,GAAG,kBAAkB,EAAE,WAAW,EAAE,GAAG,eAAe,CAAC;AACtE;AACA,MAAM;AACN;AACA,KAAK,MAAM,IAAI,GAAG,cAAc,GAAG,uBAAuB;AAC1D,MAAM,QAAQ,GAAG,SAAS,GAAG,SAAS,GAAG,UAAU;AACnD,MAAM,CAAC;AACP;AACA,KAAK,KAAK,IAAI,GAAG,QAAQ,GAAG;AAC5B;AACA,MAAM,IAAI,GAAG,CAAC,CAAC;AACf,MAAM,QAAQ,GAAG,IAAI,CAAC;AACtB,MAAM,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC;AAC1B;AACA,MAAM;AACN;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,MAAM;AACR;AACA,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,sCAAsC,GAAG,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC;AAC9E;AACA,EAAE;AACF;AACA,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AACtB;AACA,CAAC;AACD;AACA;AACA,SAAS,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,GAAG;AAC3D;AACA,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;AACb,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG;AAC7D;AACA,EAAE,GAAG,IAAI,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC;AAC5C;AACA,EAAE;AACF;AACA,CAAC,OAAO,GAAG,GAAG,KAAK,CAAC;AACpB;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS,qBAAqB,EAAE,GAAG,EAAE,UAAU,GAAG;AAClD;AACA,CAAC,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC;AACzC,CAAC,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;AAC/B,CAAC,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AACnC,CAAC,MAAM,cAAc,GAAG,IAAI,YAAY,EAAE,QAAQ,GAAG,CAAC,EAAE,CAAC;AACzD,CAAC,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;AACvC;AACA;AACA,CAAC,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;AAC9B;AACA;AACA,CAAC,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;AAC1C,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;AAChB,CAAC,KAAK,OAAO,CAAC,4BAA4B,GAAG;AAC7C;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/B;AACA,EAAE;AACF;AACA;AACA,CAAC,MAAM,OAAO,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC5C;AACA,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ,EAAE,GAAG,GAAG,GAAG;AAC7C;AACA,EAAE,MAAM,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;AACvB,EAAE,MAAM,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;AACvB;AACA,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACjB;AACA,EAAE,KAAK,UAAU,GAAG;AACpB;AACA,GAAG,EAAE,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC;AAC1B,GAAG,EAAE,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC;AAC1B,GAAG,EAAE,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC;AAC1B;AACA,GAAG,MAAM;AACT;AACA,GAAG,EAAE,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,EAAE,GAAG,MAAM,GAAG,YAAY,CAAC;AAClD,GAAG,EAAE,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,EAAE,GAAG,MAAM,GAAG,YAAY,CAAC;AAClD,GAAG,EAAE,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,EAAE,GAAG,MAAM,GAAG,YAAY,CAAC;AAClD;AACA,GAAG;AACH;AACA,EAAE,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG;AACpC;AACA,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACf;AACA,GAAG,KAAK,UAAU,GAAG;AACrB;AACA,IAAI,CAAC,GAAG,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACvC,IAAI,CAAC,GAAG,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACvC,IAAI,CAAC,GAAG,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACvC;AACA,IAAI,MAAM;AACV;AACA,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC1B,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC1B,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC1B;AACA,IAAI;AACJ;AACA,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;AACf,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAC1B,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAC1B;AACA,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;AACf,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAC1B,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAC1B;AACA;AACA;AACA;AACA,GAAG,MAAM,WAAW,GAAG,EAAE,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;AACzC,GAAG,MAAM,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;AACtB,GAAG,cAAc,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,WAAW,CAAC;AACxD,GAAG,cAAc,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,WAAW,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,WAAW,KAAK,eAAe,CAAC;AACxG;AACA,GAAG,KAAK,GAAG,GAAG,UAAU,EAAE,EAAE,EAAE,GAAG,UAAU,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC;AACxD,GAAG,KAAK,GAAG,GAAG,UAAU,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,UAAU,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC;AAChE;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC,OAAO,cAAc,CAAC;AACvB;AACA,CAAC;AACD;AACO,SAAS,SAAS,EAAE,GAAG,EAAE,OAAO,GAAG;AAC1C;AACA,CAAC,SAAS,eAAe,EAAE,kBAAkB,GAAG;AAChD;AACA,EAAE,KAAK,UAAU,GAAG;AACpB;AACA,GAAG,UAAU,EAAE,kBAAkB,GAAG,cAAc,EAAE,CAAC;AACrD;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA;AACA;AACA,CAAC,SAAS,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,oBAAoB,GAAG,IAAI,EAAE,KAAK,GAAG,CAAC,GAAG;AACnF;AACA,EAAE,KAAK,EAAE,eAAe,IAAI,KAAK,IAAI,QAAQ,GAAG;AAChD;AACA,GAAG,eAAe,GAAG,IAAI,CAAC;AAC1B,GAAG,KAAK,OAAO,GAAG;AAClB;AACA,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,sBAAsB,GAAG,QAAQ,EAAE,2DAA2D,CAAC,EAAE,CAAC;AACrH,IAAI,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AACxB;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA;AACA,EAAE,KAAK,KAAK,IAAI,WAAW,IAAI,KAAK,IAAI,QAAQ,GAAG;AACnD;AACA,GAAG,eAAe,EAAE,MAAM,GAAG,KAAK,EAAE,CAAC;AACrC,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACxB,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACtB,GAAG,OAAO,IAAI,CAAC;AACf;AACA,GAAG;AACH;AACA;AACA,EAAE,MAAM,KAAK,GAAG,eAAe,EAAE,IAAI,CAAC,YAAY,EAAE,oBAAoB,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;AACpH,EAAE,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE,CAAC,GAAG;AAC5B;AACA,GAAG,eAAe,EAAE,MAAM,GAAG,KAAK,EAAE,CAAC;AACrC,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACxB,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACtB,GAAG,OAAO,IAAI,CAAC;AACf;AACA,GAAG;AACH;AACA,EAAE,MAAM,WAAW,GAAG,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AACpF;AACA;AACA,EAAE,KAAK,WAAW,KAAK,MAAM,IAAI,WAAW,KAAK,MAAM,GAAG,KAAK,GAAG;AAClE;AACA,GAAG,eAAe,EAAE,MAAM,GAAG,KAAK,EAAE,CAAC;AACrC,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACxB,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACtB;AACA,GAAG,MAAM;AACT;AACA,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;AAC/B;AACA;AACA,GAAG,MAAM,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC;AAClC,GAAG,MAAM,MAAM,GAAG,MAAM,CAAC;AACzB,GAAG,MAAM,MAAM,GAAG,WAAW,GAAG,MAAM,CAAC;AACvC,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACpB,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC,EAAE,CAAC;AAC7C;AACA,GAAG,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,YAAY,EAAE,yBAAyB,EAAE,CAAC;AAC7F,GAAG,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,yBAAyB,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;AAC3E;AACA;AACA,GAAG,MAAM,KAAK,GAAG,IAAI,WAAW,EAAE,CAAC;AACnC,GAAG,MAAM,MAAM,GAAG,WAAW,CAAC;AAC9B,GAAG,MAAM,MAAM,GAAG,KAAK,GAAG,MAAM,CAAC;AACjC,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACtB,GAAG,KAAK,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC,EAAE,CAAC;AAC9C;AACA,GAAG,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,YAAY,EAAE,yBAAyB,EAAE,CAAC;AAC9F,GAAG,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,yBAAyB,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;AAC5E;AACA,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC;AACd;AACA,EAAE;AACF;AACA,CAAC,WAAW,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;AAC7B;AACA;AACA;AACA;AACA,CAAC,MAAM,UAAU,GAAG,IAAI,YAAY,EAAE,CAAC,EAAE,CAAC;AAC1C,CAAC,MAAM,yBAAyB,GAAG,IAAI,YAAY,EAAE,CAAC,EAAE,CAAC;AACzD,CAAC,MAAM,cAAc,GAAG,qBAAqB,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;AACjE,CAAC,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;AACpC,CAAC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AACnC,CAAC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;AACjC,CAAC,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AACzC,CAAC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AACnC,CAAC,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;AACvC,CAAC,MAAM,cAAc,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAC5C,CAAC,IAAI,eAAe,GAAG,KAAK,CAAC;AAC7B;AACA,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;AAClB,CAAC,MAAM,MAAM,GAAG,kBAAkB,EAAE,GAAG,EAAE,CAAC;AAC1C;AACA,CAAC,KAAK,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG;AAC5B;AACA,EAAE,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;AAC5B,EAAE,MAAM,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC;AACjC,EAAE,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC;AACjC,EAAE,iBAAiB,EAAE,cAAc,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,yBAAyB,EAAE,CAAC;AAC5F;AACA,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,yBAAyB,EAAE,CAAC;AAC1E,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AACrB;AACA,EAAE,MAAM;AACR;AACA,EAAE,MAAM,IAAI,KAAK,IAAI,MAAM,GAAG;AAC9B;AACA,GAAG,MAAM,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC;AAClC,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC,EAAE,CAAC;AAC7C,GAAG,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,yBAAyB,EAAE,CAAC;AACxG;AACA,GAAG,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,yBAAyB,EAAE,CAAC;AAC3E,GAAG,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AACtB;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC,OAAO,KAAK,CAAC;AACd;AACA,CAAC;AACD;AACO,SAAS,eAAe,EAAE,GAAG,EAAE,OAAO,GAAG;AAChD;AACA;AACA;AACA;AACA,CAAC,MAAM,KAAK,GAAG,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;AACzC;AACA,CAAC,IAAI,YAAY,CAAC;AAClB,CAAC,IAAI,WAAW,CAAC;AACjB,CAAC,IAAI,WAAW,CAAC;AACjB,CAAC,MAAM,WAAW,GAAG,EAAE,CAAC;AACxB,CAAC,MAAM,iBAAiB,GAAG,OAAO,CAAC,oBAAoB,GAAG,iBAAiB,GAAG,WAAW,CAAC;AAC1F,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG;AAC3C;AACA,EAAE,MAAM,IAAI,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC;AAC1B,EAAE,IAAI,SAAS,GAAG,UAAU,EAAE,IAAI,EAAE,CAAC;AACrC;AACA,EAAE,MAAM,MAAM,GAAG,IAAI,iBAAiB,EAAE,cAAc,GAAG,SAAS,EAAE,CAAC;AACrE,EAAE,YAAY,GAAG,IAAI,YAAY,EAAE,MAAM,EAAE,CAAC;AAC5C,EAAE,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;AAC1C,EAAE,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;AAC1C,EAAE,cAAc,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC;AAC5B,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC7B;AACA,EAAE;AACF;AACA,CAAC,OAAO,WAAW,CAAC;AACpB;AACA,CAAC,SAAS,UAAU,EAAE,IAAI,GAAG;AAC7B;AACA,EAAE,KAAK,IAAI,CAAC,KAAK,GAAG;AACpB;AACA,GAAG,OAAO,CAAC,CAAC;AACZ;AACA,GAAG,MAAM;AACT;AACA,GAAG,OAAO,CAAC,GAAG,UAAU,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,UAAU,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AACjE;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC,SAAS,cAAc,EAAE,UAAU,EAAE,IAAI,GAAG;AAC7C;AACA,EAAE,MAAM,aAAa,GAAG,UAAU,GAAG,CAAC,CAAC;AACvC,EAAE,MAAM,aAAa,GAAG,UAAU,GAAG,CAAC,CAAC;AACvC,EAAE,MAAM,MAAM,GAAG,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC;AAChC,EAAE,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;AACzC,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;AACA,GAAG,YAAY,EAAE,aAAa,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;AACzD;AACA,GAAG;AACH;AACA,EAAE,KAAK,MAAM,GAAG;AAChB;AACA,GAAG,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC9B,GAAG,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC5B,GAAG,WAAW,EAAE,aAAa,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC;AAC7C,GAAG,WAAW,EAAE,aAAa,GAAG,EAAE,EAAE,GAAG,KAAK,CAAC;AAC7C,GAAG,WAAW,EAAE,aAAa,GAAG,EAAE,EAAE,GAAG,gBAAgB,CAAC;AACxD,GAAG,OAAO,UAAU,GAAG,cAAc,CAAC;AACtC;AACA,GAAG,MAAM;AACT;AACA,GAAG,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAC1B,GAAG,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC5B,GAAG,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC;AACA,GAAG,IAAI,iBAAiB,CAAC;AACzB,GAAG,iBAAiB,GAAG,cAAc,EAAE,UAAU,GAAG,cAAc,EAAE,IAAI,EAAE,CAAC;AAC3E;AACA,GAAG,KAAK,EAAE,iBAAiB,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG;AACxD;AACA,IAAI,MAAM,IAAI,KAAK,EAAE,2DAA2D,EAAE,CAAC;AACnF;AACA,IAAI;AACJ;AACA,GAAG,WAAW,EAAE,aAAa,GAAG,CAAC,EAAE,GAAG,iBAAiB,GAAG,CAAC,CAAC;AAC5D,GAAG,iBAAiB,GAAG,cAAc,EAAE,iBAAiB,EAAE,KAAK,EAAE,CAAC;AAClE;AACA,GAAG,WAAW,EAAE,aAAa,GAAG,CAAC,EAAE,GAAG,SAAS,CAAC;AAChD,GAAG,OAAO,iBAAiB,CAAC;AAC5B;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA;;ACv2BO,MAAM,oBAAoB,CAAC;AAClC;AACA,CAAC,WAAW,GAAG;AACf;AACA,EAAE,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC;AACtB,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,QAAQ,CAAC;AACxB;AACA,EAAE;AACF;AACA,CAAC,kBAAkB,EAAE,MAAM,EAAE,KAAK,GAAG;AACrC;AACA,EAAE,IAAI,GAAG,GAAG,QAAQ,CAAC;AACrB,EAAE,IAAI,GAAG,GAAG,EAAE,QAAQ,CAAC;AACvB,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACpD;AACA,GAAG,MAAM,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;AACzB,GAAG,MAAM,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC;AAC1B,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/B,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/B;AACA,GAAG;AACH;AACA,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB;AACA,EAAE;AACF;AACA,CAAC,aAAa,EAAE,IAAI,EAAE,MAAM,GAAG;AAC/B;AACA,EAAE,IAAI,GAAG,GAAG,QAAQ,CAAC;AACrB,EAAE,IAAI,GAAG,GAAG,EAAE,QAAQ,CAAC;AACvB,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACpD;AACA,GAAG,MAAM,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;AACzB,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;AAC7B,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/B,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/B;AACA,GAAG;AACH;AACA,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB;AACA,EAAE;AACF;AACA,CAAC,WAAW,EAAE,KAAK,GAAG;AACtB;AACA,EAAE,OAAO,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACtD;AACA,EAAE;AACF;AACA,CAAC;AACD;AACA,oBAAoB,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,YAAY;AAC1D;AACA,CAAC,MAAM,CAAC,GAAG,IAAI,OAAO,EAAE,CAAC;AACzB,CAAC,OAAO,SAAS,UAAU,EAAE,IAAI,EAAE,GAAG,GAAG;AACzC;AACA,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC;AACzB,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC;AACzB,EAAE,IAAI,GAAG,GAAG,QAAQ,CAAC;AACrB,EAAE,IAAI,GAAG,GAAG,EAAE,QAAQ,CAAC;AACvB,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;AACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACnC;AACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACpC;AACA,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;AAC/C,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;AAC/C,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;AAC/C;AACA,KAAK,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;AAC/B,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAChC,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAChC;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACjB;AACA,EAAE,CAAC;AACH;AACA,CAAC,IAAI,CAAC;AACN;AACO,MAAM,eAAe,GAAG,EAAE,YAAY;AAC7C;AACA,CAAC,MAAM,cAAc,GAAG,IAAI,oBAAoB,EAAE,CAAC;AACnD,CAAC,OAAO,SAAS,eAAe,EAAE,MAAM,EAAE,MAAM,GAAG;AACnD;AACA,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AAChC,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;AAClC,EAAE,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;AACtC;AACA,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AAChC,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;AAClC,EAAE,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;AACtC;AACA;AACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;AACA,GAAG,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;AAC9B,GAAG,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;AAC5B,GAAG,cAAc,CAAC,aAAa,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC;AAC/C,GAAG,KAAK,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,GAAG,OAAO,KAAK,CAAC;AACxD;AACA,GAAG;AACH;AACA;AACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;AACA,GAAG,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;AAC9B,GAAG,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;AAC5B,GAAG,cAAc,CAAC,aAAa,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC;AAC/C,GAAG,KAAK,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,GAAG,OAAO,KAAK,CAAC;AACxD;AACA,GAAG;AACH;AACA,EAAE,CAAC;AACH;AACA,CAAC,IAAI;;AC5HE,MAAM,sBAAsB,GAAG,EAAE,YAAY;AACpD;AACA;AACA,CAAC,MAAM,IAAI,GAAG,IAAI,OAAO,EAAE,CAAC;AAC5B,CAAC,MAAM,IAAI,GAAG,IAAI,OAAO,EAAE,CAAC;AAC5B,CAAC,MAAM,GAAG,GAAG,IAAI,OAAO,EAAE,CAAC;AAC3B,CAAC,OAAO,SAAS,sBAAsB,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,GAAG;AAC1D;AACA,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC;AACtB,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC;AACnB,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC;AACtB,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC;AACnB;AACA,EAAE,GAAG,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC3B,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC;AACtC,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC;AACtC;AACA;AACA,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC/B;AACA;AACA,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC/B;AACA;AACA,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC/B;AACA;AACA,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC/B;AACA;AACA,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC/B;AACA;AACA,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAC9C;AACA,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;AACZ,EAAE,KAAK,KAAK,KAAK,CAAC,GAAG;AACrB;AACA,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,KAAK,KAAK,CAAC;AACjD;AACA,GAAG,MAAM;AACT;AACA,GAAG,CAAC,GAAG,CAAC,CAAC;AACT;AACA,GAAG;AACH;AACA,EAAE,EAAE,GAAG,EAAE,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,KAAK,CAAC;AACrC;AACA,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;AACf,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;AAChB;AACA,EAAE,CAAC;AACH;AACA,CAAC,IAAI,CAAC;AACN;AACO,MAAM,6BAA6B,GAAG,EAAE,YAAY;AAC3D;AACA;AACA,CAAC,MAAM,WAAW,GAAG,IAAI,OAAO,EAAE,CAAC;AACnC,CAAC,MAAM,KAAK,GAAG,IAAI,OAAO,EAAE,CAAC;AAC7B,CAAC,MAAM,KAAK,GAAG,IAAI,OAAO,EAAE,CAAC;AAC7B,CAAC,OAAO,SAAS,6BAA6B,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO,GAAG;AAC3E;AACA,EAAE,sBAAsB,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,CAAC;AAChD;AACA,EAAE,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;AACxB,EAAE,IAAI,EAAE,GAAG,WAAW,CAAC,CAAC,CAAC;AACzB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG;AAChD;AACA,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;AACvB,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC;AACxB;AACA,GAAG,OAAO;AACV;AACA,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;AACjC;AACA;AACA,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG;AACjB;AACA,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;AACxB;AACA,IAAI,MAAM;AACV;AACA,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;AACxB;AACA,IAAI;AACJ;AACA,GAAG,EAAE,CAAC,mBAAmB,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;AACpD,GAAG,OAAO;AACV;AACA,GAAG,MAAM,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG;AACnC;AACA;AACA,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG;AAChB;AACA,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;AACxB;AACA,IAAI,MAAM;AACV;AACA,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;AACxB;AACA,IAAI;AACJ;AACA,GAAG,EAAE,CAAC,mBAAmB,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;AACpD,GAAG,OAAO;AACV;AACA,GAAG,MAAM;AACT;AACA;AACA,GAAG,IAAI,CAAC,CAAC;AACT,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG;AAChB;AACA,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;AACjB;AACA,IAAI,MAAM;AACV;AACA,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;AACf;AACA,IAAI;AACJ;AACA,GAAG,IAAI,EAAE,CAAC;AACV,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG;AACjB;AACA,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC;AAClB;AACA,IAAI,MAAM;AACV;AACA,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC;AAChB;AACA,IAAI;AACJ;AACA,GAAG,MAAM,YAAY,GAAG,KAAK,CAAC;AAC9B,GAAG,MAAM,aAAa,GAAG,KAAK,CAAC;AAC/B,GAAG,EAAE,CAAC,mBAAmB,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AAC7C,GAAG,EAAE,CAAC,mBAAmB,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AAC5C;AACA,GAAG,KAAK,YAAY,CAAC,iBAAiB,EAAE,EAAE,EAAE,IAAI,aAAa,CAAC,iBAAiB,EAAE,CAAC,EAAE,GAAG;AACvF;AACA,IAAI,OAAO,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC;AACjC,IAAI,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB,IAAI,OAAO;AACX;AACA,IAAI,MAAM;AACV;AACA,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AACtB,IAAI,OAAO,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC;AAClC,IAAI,OAAO;AACX;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,CAAC;AACH;AACA,CAAC,IAAI,CAAC;AACN;AACA;AACO,MAAM,uBAAuB,GAAG,EAAE,YAAY;AACrD;AACA;AACA,CAAC,MAAM,gBAAgB,GAAG,IAAI,OAAO,EAAE,CAAC;AACxC,CAAC,MAAM,kBAAkB,GAAG,IAAI,OAAO,EAAE,CAAC;AAC1C,CAAC,MAAM,SAAS,GAAG,IAAI,KAAK,EAAE,CAAC;AAC/B,CAAC,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAE,CAAC;AAC9B,CAAC,OAAO,SAAS,uBAAuB,EAAE,MAAM,EAAE,QAAQ,GAAG;AAC7D;AACA,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;AACpC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC/B;AACA;AACA,EAAE,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;AACrB,EAAE,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC;AACnB,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,mBAAmB,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC;AACvF,EAAE,KAAK,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,MAAM,GAAG,OAAO,IAAI,CAAC;AAClE;AACA,EAAE,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;AACrB,EAAE,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC;AACnB,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,mBAAmB,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC;AACvF,EAAE,KAAK,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,MAAM,GAAG,OAAO,IAAI,CAAC;AAClE;AACA,EAAE,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;AACrB,EAAE,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC;AACnB,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,mBAAmB,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC;AACvF,EAAE,KAAK,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,MAAM,GAAG,OAAO,IAAI,CAAC;AAClE;AACA;AACA,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC;AAC/C,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,eAAe,EAAE,MAAM,EAAE,EAAE,CAAC;AACzD,EAAE,KAAK,EAAE,IAAI,MAAM,GAAG;AACtB;AACA,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,YAAY,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC;AAC/D,GAAG,MAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,EAAE,EAAE,EAAE,CAAC;AAC3C,GAAG,KAAK,EAAE,GAAG,OAAO,IAAI,CAAC;AACzB;AACA,GAAG;AACH;AACA,EAAE,OAAO,KAAK,CAAC;AACf;AACA,EAAE,CAAC;AACH;AACA,CAAC,IAAI;;ACtML,MAAM,YAAY,GAAG,KAAK,CAAC;AAC3B,SAAS,UAAU,EAAE,KAAK,GAAG;AAC7B;AACA,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,YAAY,CAAC;AACzC;AACA,CAAC;AACD;AACO,MAAM,gBAAgB,SAAS,QAAQ,CAAC;AAC/C;AACA,CAAC,WAAW,EAAE,GAAG,IAAI,GAAG;AACxB;AACA,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,CAAC;AACnB;AACA,EAAE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AACjC,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAI,OAAO,EAAE,EAAE,CAAC;AAClE,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAI,oBAAoB,EAAE,EAAE,CAAC;AACjF,EAAE,IAAI,CAAC,MAAM,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;AAC3C,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;AAC7B,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;AAC3B,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B;AACA,EAAE;AACF;AACA,CAAC,gBAAgB,EAAE,MAAM,GAAG;AAC5B;AACA,EAAE,OAAO,uBAAuB,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACjD;AACA,EAAE;AACF;AACA,CAAC,MAAM,GAAG;AACV;AACA,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACnB,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACnB,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACnB,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B;AACA,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACnC;AACA,EAAE,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;AAC7B,EAAE,MAAM,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;AAC9B,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC;AAC1B,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACtC;AACA,EAAE,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;AAC7B,EAAE,MAAM,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;AAC9B,EAAE,KAAK,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAC3B,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACtC;AACA,EAAE,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;AAC7B,EAAE,MAAM,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;AAC9B,EAAE,KAAK,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAC3B,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACtC;AACA,EAAE,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;AAC7B,EAAE,MAAM,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;AAC9B,EAAE,KAAK,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAC3B,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACtC;AACA,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;AAC3C,EAAE,IAAI,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;AACvD,EAAE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC3B;AACA,EAAE;AACF;AACA,CAAC;AACD;AACA,gBAAgB,CAAC,SAAS,CAAC,qBAAqB,GAAG,EAAE,YAAY;AACjE;AACA,CAAC,MAAM,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;AAC9B,CAAC,MAAM,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;AAC9B,CAAC,MAAM,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;AAC1B;AACA,CAAC,OAAO,SAAS,iBAAiB,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,GAAG;AAC9E;AACA,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,IAAI,iBAAiB,GAAG,QAAQ,CAAC;AACnC;AACA;AACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;AACA,GAAG,MAAM,KAAK,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/B,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;AAClC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;AACpC;AACA,GAAG,6BAA6B,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAClE;AACA,GAAG,MAAM,GAAG,MAAM,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;AAC/C,GAAG,KAAK,MAAM,GAAG,iBAAiB,GAAG;AACrC;AACA,IAAI,iBAAiB,GAAG,MAAM,CAAC;AAC/B,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC1C,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC1C;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA;AACA,EAAE,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AAC5C,EAAE,MAAM,GAAG,KAAK,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;AAC7C,EAAE,KAAK,MAAM,GAAG,iBAAiB,GAAG;AACpC;AACA,GAAG,iBAAiB,GAAG,MAAM,CAAC;AAC9B,GAAG,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AACzC,GAAG,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACxC;AACA,GAAG;AACH;AACA,EAAE,IAAI,CAAC,mBAAmB,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC;AAC1C,EAAE,MAAM,GAAG,GAAG,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;AAC3C,EAAE,KAAK,MAAM,GAAG,iBAAiB,GAAG;AACpC;AACA,GAAG,iBAAiB,GAAG,MAAM,CAAC;AAC9B,GAAG,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AACzC,GAAG,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AACtC;AACA,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC;AACxC;AACA,EAAE,CAAC;AACH;AACA,CAAC,IAAI,CAAC;AACN;AACA,gBAAgB,CAAC,SAAS,CAAC,kBAAkB,GAAG,EAAE,YAAY;AAC9D;AACA,CAAC,MAAM,MAAM,GAAG,IAAI,gBAAgB,EAAE,CAAC;AACvC,CAAC,MAAM,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC;AAC7B,CAAC,MAAM,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC;AAC7B,CAAC,MAAM,eAAe,GAAG,IAAI,oBAAoB,EAAE,CAAC;AACpD,CAAC,MAAM,gBAAgB,GAAG,IAAI,oBAAoB,EAAE,CAAC;AACrD,CAAC,MAAM,UAAU,GAAG,IAAI,OAAO,EAAE,CAAC;AAClC,CAAC,MAAM,IAAI,GAAG,IAAI,OAAO,EAAE,CAAC;AAC5B,CAAC,MAAM,IAAI,GAAG,IAAI,OAAO,EAAE,CAAC;AAC5B,CAAC,MAAM,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC;AAC/B,CAAC,MAAM,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;AAC1B,CAAC,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;AAC3B,CAAC,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;AAC3B;AACA;AACA;AACA,CAAC,OAAO,SAAS,kBAAkB,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI,EAAE,WAAW,GAAG,KAAK,GAAG;AACjF;AACA,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG;AAC1B;AACA,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;AACA,GAAG;AACH;AACA,EAAE,KAAK,EAAE,KAAK,CAAC,kBAAkB,GAAG;AACpC;AACA,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACxB,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;AACnB,GAAG,KAAK,GAAG,MAAM,CAAC;AAClB;AACA,GAAG,MAAM,KAAK,KAAK,CAAC,WAAW,GAAG;AAClC;AACA,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;AAClB;AACA,GAAG;AACH;AACA,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;AAC5B,EAAE,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;AAC7B;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,EAAE,EAAE,GAAG,GAAG,GAAG,KAAK,GAAG;AACtE;AACA;AACA,GAAG,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;AACrC,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;AACvB,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;AACvB,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;AACvB,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;AACA,IAAI,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;AAC/B,IAAI,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;AAC7B,IAAI,eAAe,CAAC,aAAa,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC;AAC9C,IAAI,KAAK,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,GAAG,OAAO,KAAK,CAAC;AAC1D;AACA,IAAI;AACJ;AACA,GAAG,MAAM,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC;AACtC,GAAG,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;AAClC,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AACtB,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AACtB,GAAG,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AACtB,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;AACA,IAAI,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;AAC/B,IAAI,MAAM,EAAE,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;AAC7B,IAAI,eAAe,CAAC,aAAa,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC;AAC9C,IAAI,KAAK,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,GAAG,OAAO,KAAK,CAAC;AAC1D;AACA,IAAI;AACJ;AACA;AACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;AACA,IAAI,MAAM,GAAG,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;AAC9B,IAAI,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG;AACtC;AACA,KAAK,MAAM,GAAG,GAAG,QAAQ,EAAE,EAAE,EAAE,CAAC;AAChC,KAAK,UAAU,CAAC,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACzC,KAAK,eAAe,CAAC,aAAa,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;AACvD,KAAK,gBAAgB,CAAC,aAAa,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;AACxD,KAAK,KAAK,eAAe,CAAC,WAAW,EAAE,gBAAgB,EAAE,GAAG,OAAO,KAAK,CAAC;AACzE;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG,KAAK,MAAM,GAAG;AACjB;AACA;AACA,IAAI,KAAK,EAAE,WAAW,GAAG;AACzB;AACA,KAAK,OAAO,CAAC,IAAI,EAAE,6HAA6H,EAAE,CAAC;AACnJ;AACA,KAAK;AACL;AACA,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAChC,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAC9B;AACA,IAAI;AACJ;AACA,GAAG,OAAO,IAAI,CAAC;AACf;AACA,GAAG,MAAM;AACT;AACA;AACA,GAAG,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;AAC/B,GAAG,IAAI,MAAM,GAAG,KAAK,CAAC;AACtB,GAAG,IAAI,MAAM,GAAG,CAAC,CAAC;AAClB,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;AACA,IAAI,MAAM,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;AAC3B,IAAI,MAAM,KAAK,GAAG,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;AAC3C;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AACzB,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AAC3B,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;AACvB;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;AACzD,IAAI,MAAM,eAAe,GAAG,UAAU,EAAE,MAAM,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC;AACtE,IAAI,KAAK,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,IAAI,eAAe,GAAG;AACtE;AACA;AACA,KAAK,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AACxB,KAAK,MAAM,GAAG,CAAC,CAAC;AAChB,KAAK,MAAM;AACX;AACA,KAAK;AACL;AACA;AACA,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,eAAe,CAAC;AACvF,IAAI,KAAK,aAAa,IAAI,EAAE,UAAU,EAAE,WAAW,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE,GAAG;AAC5E;AACA,KAAK,MAAM,GAAG,CAAC;AACf,KAAK,KAAK,MAAM,GAAG;AACnB;AACA,MAAM,MAAM;AACZ;AACA,MAAM;AACN;AACA,KAAK,MAAM,GAAG,IAAI,CAAC;AACnB;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG,KAAK,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG;AAC3D;AACA,IAAI,KAAK,MAAM,GAAG;AAClB;AACA,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AACpC,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AAClC;AACA,KAAK;AACL;AACA,IAAI,OAAO,IAAI,CAAC;AAChB;AACA,IAAI,MAAM,KAAK,MAAM,KAAK,CAAC,GAAG;AAC9B;AACA,IAAI,OAAO,KAAK,CAAC;AACjB;AACA,IAAI;AACJ;AACA;AACA,GAAG,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;AAChC,GAAG,IAAI,MAAM,GAAG,KAAK,CAAC;AACtB,GAAG,IAAI,MAAM,GAAG,CAAC,CAAC;AAClB,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;AACA,IAAI,MAAM,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;AAC3B,IAAI,MAAM,KAAK,GAAG,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;AAC3C;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AACzB,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AAC3B,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;AACvB;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;AACzD,IAAI,MAAM,eAAe,GAAG,UAAU,EAAE,MAAM,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC;AACtE,IAAI,KAAK,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,IAAI,eAAe,GAAG;AACtE;AACA;AACA,KAAK,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AACxB,KAAK,MAAM,GAAG,CAAC,CAAC;AAChB,KAAK,MAAM;AACX;AACA,KAAK;AACL;AACA;AACA,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,eAAe,CAAC;AACvF,IAAI,KAAK,aAAa,IAAI,EAAE,UAAU,EAAE,WAAW,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE,GAAG;AAC5E;AACA,KAAK,MAAM,GAAG,CAAC;AACf,KAAK,KAAK,MAAM,GAAG;AACnB;AACA,MAAM,MAAM;AACZ;AACA,MAAM;AACN;AACA,KAAK,MAAM,GAAG,IAAI,CAAC;AACnB;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG,KAAK,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG;AAC1D;AACA,IAAI,KAAK,MAAM,GAAG;AAClB;AACA,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AACpC,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AAClC;AACA,KAAK;AACL;AACA,IAAI,OAAO,IAAI,CAAC;AAChB;AACA,IAAI,MAAM,KAAK,MAAM,KAAK,CAAC,GAAG;AAC9B;AACA,IAAI,OAAO,KAAK,CAAC;AACjB;AACA,IAAI;AACJ;AACA;AACA,GAAG,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;AACvB,GAAG,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;AACvB;AACA,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG;AAC/B;AACA,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC;AAC1B,IAAI,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;AAC5B,IAAI,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;AACpB;AACA,IAAI;AACJ;AACA;AACA,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;AACtC,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;AACpC,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;AACtC,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;AACpC,GAAG,MAAM,UAAU,GAAG,EAAE,GAAG,EAAE,CAAC;AAC9B,GAAG,MAAM,UAAU,GAAG,EAAE,GAAG,EAAE,CAAC;AAC9B;AACA,GAAG,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,UAAU,KAAK,UAAU,GAAG;AAC9D;AACA,IAAI,OAAO,KAAK,CAAC;AACjB;AACA,IAAI;AACJ;AACA;AACA,GAAG,KAAK,MAAM,GAAG;AACjB;AACA,IAAI,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;AACnD,IAAI,KAAK,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG;AACnC;AACA,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;AACtC;AACA,KAAK,MAAM;AACX;AACA,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;AACtC;AACA,KAAK;AACL;AACA,IAAI,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AAC/C,IAAI,KAAK,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG;AACnC;AACA,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AAClC;AACA,KAAK,MAAM;AACX;AACA,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AAClC;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG,OAAO,IAAI,CAAC;AACf;AACA,GAAG;AACH;AACA,EAAE,CAAC;AACH;AACA,CAAC,IAAI,CAAC;AACN;AACA;AACA,gBAAgB,CAAC,SAAS,CAAC,eAAe,GAAG,EAAE,YAAY;AAC3D;AACA,CAAC,MAAM,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;AAC9B,CAAC,OAAO,SAAS,eAAe,EAAE,KAAK,GAAG;AAC1C;AACA,EAAE,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AAC5C,EAAE,OAAO,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;AACpC;AACA,EAAE,CAAC;AACH;AACA,CAAC,IAAI,CAAC;AACN;AACA;AACA,gBAAgB,CAAC,SAAS,CAAC,kBAAkB,GAAG,EAAE,YAAY;AAC9D;AACA,CAAC,MAAM,KAAK,GAAG,IAAI,OAAO,EAAE,CAAC;AAC7B,CAAC,MAAM,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;AAC9B,CAAC,MAAM,YAAY,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACxC,CAAC,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;AAC3B,CAAC,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;AAC3B;AACA,CAAC,OAAO,SAAS,kBAAkB,EAAE,KAAK,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,GAAG;AAC7E;AACA,EAAE,MAAM,UAAU,GAAG,OAAO,IAAI,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC;AACvD,EAAE,KAAK,IAAI,CAAC,kBAAkB,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;AACtD;AACA,GAAG,KAAK,OAAO,IAAI,OAAO,GAAG;AAC7B;AACA,IAAI,KAAK,OAAO,GAAG,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC;AACnD,IAAI,KAAK,OAAO,GAAG,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC;AACnD;AACA,IAAI;AACJ;AACA,GAAG,OAAO,CAAC,CAAC;AACZ;AACA,GAAG;AACH;AACA,EAAE,IAAI,iBAAiB,GAAG,QAAQ,CAAC;AACnC;AACA;AACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;AACA,GAAG,IAAI,IAAI,CAAC;AACZ,GAAG,MAAM,KAAK,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;AACnC,GAAG,MAAM,QAAQ,GAAG,KAAK,EAAE,KAAK,EAAE,CAAC;AACnC,GAAG,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;AAC/C;AACA,GAAG,IAAI,GAAG,QAAQ,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC;AAC9C;AACA,GAAG,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACnC;AACA,IAAI,iBAAiB,GAAG,IAAI,CAAC;AAC7B,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACzC,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC;AAC5C;AACA,IAAI;AACJ;AACA;AACA,GAAG,MAAM,OAAO,GAAG,IAAI,EAAE,KAAK,EAAE,CAAC;AACjC,GAAG,KAAK,CAAC,mBAAmB,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;AAC/C;AACA,GAAG,IAAI,GAAG,OAAO,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC;AAC7C;AACA,GAAG,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACnC;AACA,IAAI,iBAAiB,GAAG,IAAI,CAAC;AAC7B,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC;AAC3C,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACzC;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;AACA,GAAG,MAAM,GAAG,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;AACjC,GAAG,MAAM,GAAG,GAAG,YAAY,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;AAC7C,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;AACzC,GAAG,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG;AACrC;AACA,IAAI,MAAM,GAAG,GAAG,YAAY,EAAE,EAAE,EAAE,CAAC;AACnC,IAAI,MAAM,GAAG,GAAG,YAAY,EAAE,EAAE,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;AAC/C,IAAI,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC;AAC5C;AACA,IAAI,6BAA6B,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACjE;AACA,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;AACnD,IAAI,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACpC;AACA,KAAK,iBAAiB,GAAG,IAAI,CAAC;AAC9B,KAAK,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AAC1C,KAAK,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC3C;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC;AACxC;AACA,EAAE,CAAC;AACH;AACA,CAAC,IAAI;;AChgBE,MAAM,WAAW,CAAC;AACzB;AACA,CAAC,WAAW,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,GAAG;AACjC;AACA,EAAE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC5B,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,OAAO,EAAE,CAAC;AAC3B,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,OAAO,EAAE,CAAC;AAC3B,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;AAC9B,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;AACjC,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAI,OAAO,EAAE,EAAE,CAAC;AACjE,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAI,OAAO,EAAE,EAAE,CAAC;AAClE,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAI,oBAAoB,EAAE,EAAE,CAAC;AACjF,EAAE,IAAI,CAAC,gBAAgB,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAI,oBAAoB,EAAE,EAAE,CAAC;AACxF,EAAE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC3B;AACA,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AAClC,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AAClC,EAAE,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC3C;AACA,EAAE;AACF;AACA,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,GAAG;AACzB;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AACvB,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AACvB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC7B,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B;AACA,EAAE;AACF;AACA,CAAC,IAAI,EAAE,KAAK,GAAG;AACf;AACA,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AAC7B,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AAC7B,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC;AACnC,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B;AACA,EAAE;AACF;AACA,CAAC;AACD;AACA,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE,YAAY;AAC7C;AACA,CAAC,OAAO,SAAS,MAAM,GAAG;AAC1B;AACA,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACvB,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACvB;AACA,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;AACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACnC;AACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACpC;AACA,KAAK,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;AAC5E,KAAK,MAAM,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;AAC3B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAC7B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAC7B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAC7B;AACA,KAAK,CAAC,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC;AAC9B;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACnC,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,EAAE,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;AAC7B,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;AACA,GAAG,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;AAC7B,GAAG,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;AAC7B,GAAG,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;AACxB,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,KAAK,EAAE,CAAC;AAC9B;AACA,GAAG,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;AACjC,GAAG,EAAE,CAAC,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;AACpC;AACA,GAAG;AACH;AACA,EAAE,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACjD,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,kBAAkB,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1D,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,kBAAkB,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1D,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,kBAAkB,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1D;AACA,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC;AAC9C,EAAE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC3B;AACA,EAAE,CAAC;AACH;AACA,CAAC,IAAI,CAAC;AACN;AACA,WAAW,CAAC,SAAS,CAAC,aAAa,GAAG,EAAE,YAAY;AACpD;AACA,CAAC,MAAM,UAAU,GAAG,IAAI,oBAAoB,EAAE,CAAC;AAC/C,CAAC,OAAO,SAAS,aAAa,EAAE,GAAG,GAAG;AACtC;AACA;AACA,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG;AAC1B;AACA,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;AACA,GAAG;AACH;AACA,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;AACtB,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;AACtB,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACnC,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,EAAE,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACjD;AACA,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AACzB,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AACzB,EAAE,KAAK,gBAAgB,EAAE,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,GAAG,OAAO,KAAK,CAAC;AACtE;AACA,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AACzB,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AACzB,EAAE,KAAK,gBAAgB,EAAE,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,GAAG,OAAO,KAAK,CAAC;AACtE;AACA,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AACzB,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AACzB,EAAE,KAAK,gBAAgB,EAAE,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,GAAG,OAAO,KAAK,CAAC;AACtE;AACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;AACA,GAAG,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;AAC7B,GAAG,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;AAC7B,GAAG,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AACtC,GAAG,KAAK,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,GAAG,OAAO,KAAK,CAAC;AACpD;AACA,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC;AACd;AACA,EAAE,CAAC;AACH;AACA,CAAC,IAAI,CAAC;AACN;AACA,WAAW,CAAC,SAAS,CAAC,kBAAkB,GAAG,EAAE,YAAY;AACzD;AACA,CAAC,MAAM,KAAK,GAAG,IAAI,gBAAgB,EAAE,CAAC;AACtC,CAAC,MAAM,SAAS,GAAG,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC;AAClC,CAAC,MAAM,eAAe,GAAG,IAAI,oBAAoB,EAAE,CAAC;AACpD,CAAC,MAAM,gBAAgB,GAAG,IAAI,oBAAoB,EAAE,CAAC;AACrD,CAAC,MAAM,UAAU,GAAG,IAAI,OAAO,EAAE,CAAC;AAClC,CAAC,OAAO,SAAS,kBAAkB,EAAE,QAAQ,GAAG;AAChD;AACA,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG;AAC1B;AACA,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;AACA,GAAG;AACH;AACA,EAAE,KAAK,EAAE,QAAQ,CAAC,kBAAkB,GAAG;AACvC;AACA,GAAG,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC;AAC1B,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;AAClB,GAAG,QAAQ,GAAG,KAAK,CAAC;AACpB;AACA,GAAG,MAAM,KAAK,QAAQ,CAAC,WAAW,GAAG;AACrC;AACA,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;AACrB;AACA,GAAG;AACH;AACA,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACnC,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;AAC9B,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;AAC9B,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;AAC9B;AACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;AACA,GAAG,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;AAC7B,GAAG,MAAM,EAAE,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;AAC3B,GAAG,eAAe,CAAC,aAAa,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC;AAClD,GAAG,KAAK,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,GAAG,OAAO,KAAK,CAAC;AACzD;AACA,GAAG;AACH;AACA,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC;AAC1C,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC;AACtC,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;AACA,GAAG,MAAM,EAAE,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;AAChC,GAAG,MAAM,EAAE,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;AAC9B,GAAG,eAAe,CAAC,aAAa,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC;AAC/C,GAAG,KAAK,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,GAAG,OAAO,KAAK,CAAC;AACzD;AACA,GAAG;AACH;AACA;AACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;AACA,GAAG,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;AAC5B,GAAG,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG;AACrC;AACA,IAAI,MAAM,GAAG,GAAG,UAAU,EAAE,EAAE,EAAE,CAAC;AACjC,IAAI,UAAU,CAAC,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACxC,IAAI,eAAe,CAAC,aAAa,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;AAC3D,IAAI,gBAAgB,CAAC,aAAa,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC;AACzD,IAAI,KAAK,eAAe,CAAC,WAAW,EAAE,gBAAgB,EAAE,GAAG,OAAO,KAAK,CAAC;AACxE;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC;AACd;AACA,EAAE,CAAC;AACH;AACA,CAAC,IAAI,CAAC;AACN;AACA,WAAW,CAAC,SAAS,CAAC,mBAAmB,GAAG,EAAE,YAAY;AAC1D;AACA,CAAC,OAAO,SAAS,mBAAmB,EAAE,KAAK,EAAE,OAAO,GAAG;AACvD;AACA,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG;AAC1B;AACA,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;AACA,GAAG;AACH;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,KAAK,EAAE;AACjB,IAAI,YAAY,EAAE,IAAI,CAAC,SAAS,EAAE;AAClC,IAAI,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE;AAC/B,IAAI,YAAY,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;AAChC;AACA,EAAE,OAAO,OAAO,CAAC;AACjB;AACA,EAAE,CAAC;AACH;AACA,CAAC,IAAI,CAAC;AACN;AACA,WAAW,CAAC,SAAS,CAAC,eAAe,GAAG,EAAE,YAAY;AACtD;AACA,CAAC,MAAM,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;AAC9B,CAAC,OAAO,SAAS,eAAe,EAAE,KAAK,GAAG;AAC1C;AACA,EAAE,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AAC5C,EAAE,OAAO,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;AACpC;AACA,EAAE,CAAC;AACH;AACA,CAAC,IAAI,CAAC;AACN;AACA,WAAW,CAAC,SAAS,CAAC,aAAa,GAAG,EAAE,YAAY;AACpD;AACA,CAAC,MAAM,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACrC,CAAC,MAAM,SAAS,GAAG,IAAI,KAAK,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAI,KAAK,EAAE,EAAE,CAAC;AACnE,CAAC,MAAM,SAAS,GAAG,IAAI,KAAK,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAI,KAAK,EAAE,EAAE,CAAC;AACnE;AACA,CAAC,MAAM,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;AAC9B,CAAC,MAAM,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;AAC9B;AACA;AACA,CAAC,OAAO,SAAS,aAAa,EAAE,GAAG,EAAE,SAAS,GAAG,CAAC,EAAE,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,GAAG;AACrF;AACA,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG;AAC1B;AACA,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;AACA,GAAG;AACH;AACA,EAAE,KAAK,IAAI,CAAC,aAAa,EAAE,GAAG,EAAE,GAAG;AACnC;AACA,GAAG,KAAK,OAAO,IAAI,OAAO,GAAG;AAC7B;AACA,IAAI,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC;AAC5B,IAAI,IAAI,CAAC,mBAAmB,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC/C,IAAI,GAAG,CAAC,mBAAmB,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC9C;AACA,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC1C,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC1C;AACA,IAAI;AACJ;AACA,GAAG,OAAO,CAAC,CAAC;AACZ;AACA,GAAG;AACH;AACA,EAAE,MAAM,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;AAC3C,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;AACtB,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;AACtB,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B;AACA;AACA;AACA,EAAE,IAAI,iBAAiB,GAAG,QAAQ,CAAC;AACnC;AACA;AACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;AACA,GAAG,MAAM,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;AACzB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACtC;AACA,GAAG,MAAM,IAAI,GAAG,CAAC,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;AAC9C,GAAG,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACnC;AACA,IAAI,iBAAiB,GAAG,IAAI,CAAC;AAC7B,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AACrC,IAAI,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC1C;AACA,IAAI,KAAK,IAAI,GAAG,UAAU,GAAG,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AACtD;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA;AACA,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC;AAChB,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;AACA,GAAG,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,GAAG;AACtC;AACA,IAAI,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,GAAG;AACvC;AACA,KAAK,MAAM,SAAS,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACrC,KAAK,MAAM,UAAU,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACtC;AACA;AACA,KAAK,MAAM,KAAK,GAAG,EAAE,IAAI,SAAS,GAAG,EAAE,IAAI,UAAU,CAAC;AACtD,KAAK,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,SAAS,GAAG,EAAE,IAAI,UAAU,CAAC;AAChE,KAAK,MAAM,EAAE,GAAG,MAAM,EAAE,KAAK,EAAE,CAAC;AAChC,KAAK,MAAM,EAAE,GAAG,MAAM,EAAE,MAAM,EAAE,CAAC;AACjC,KAAK,MAAM,KAAK,GAAG,SAAS,EAAE,KAAK,EAAE,CAAC;AACtC,KAAK,KAAK,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACzB;AACA;AACA;AACA,KAAK,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;AAC/B,KAAK,MAAM,EAAE,GAAG,SAAS,EAAE,SAAS,EAAE,CAAC;AACvC,KAAK,MAAM,EAAE,GAAG,SAAS,EAAE,UAAU,EAAE,CAAC;AACxC,KAAK,MAAM,KAAK,GAAG,SAAS,EAAE,KAAK,EAAE,CAAC;AACtC,KAAK,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AAC/B,KAAK,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;AAC3B;AACA,KAAK,KAAK,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;AAC7B,KAAK,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;AAC9C,KAAK,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;AAC9C;AACA,KAAK,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;AAC3B,KAAK,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;AAC5C,KAAK,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC;AAC5C;AACA,KAAK,KAAK,GAAG,CAAC;AACd;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA;AACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AAClC;AACA,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACnC;AACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;AACpC;AACA,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAClC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAClC,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAClC;AACA,KAAK,IAAI,CAAC,mBAAmB,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAChD,KAAK,MAAM,IAAI,GAAG,MAAM,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;AACrD,KAAK,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACrC;AACA,MAAM,iBAAiB,GAAG,IAAI,CAAC;AAC/B,MAAM,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC5C,MAAM,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC5C;AACA,MAAM,KAAK,IAAI,GAAG,UAAU,GAAG,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AACxD;AACA,MAAM;AACN;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG;AAClC;AACA,GAAG,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;AAC7B,GAAG,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG;AACtC;AACA,IAAI,MAAM,EAAE,GAAG,SAAS,EAAE,EAAE,EAAE,CAAC;AAC/B,IAAI,6BAA6B,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC5D,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC;AACpD,IAAI,KAAK,IAAI,GAAG,iBAAiB,GAAG;AACpC;AACA,KAAK,iBAAiB,GAAG,IAAI,CAAC;AAC9B,KAAK,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC3C,KAAK,KAAK,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;AAC3C;AACA,KAAK,KAAK,IAAI,GAAG,UAAU,GAAG,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AACvD;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,OAAO,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC;AACxC;AACA,EAAE,CAAC;AACH;AACA,CAAC,IAAI;;AClaL;AACA;AACA,MAAM,EAAE,mBAAmB,IAAI,OAAO,EAAE,CAAC;AACzC,MAAM,EAAE,mBAAmB,IAAI,OAAO,EAAE,CAAC;AACzC,MAAM,EAAE,mBAAmB,IAAI,OAAO,EAAE,CAAC;AACzC;AACA,MAAM,GAAG,mBAAmB,IAAI,OAAO,EAAE,CAAC;AAC1C,MAAM,GAAG,mBAAmB,IAAI,OAAO,EAAE,CAAC;AAC1C,MAAM,GAAG,mBAAmB,IAAI,OAAO,EAAE,CAAC;AAC1C;AACA,MAAM,iBAAiB,mBAAmB,IAAI,OAAO,EAAE,CAAC;AACxD,SAAS,iBAAiB,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,GAAG;AAC3D;AACA,CAAC,IAAI,SAAS,CAAC;AACf,CAAC,KAAK,IAAI,KAAK,QAAQ,GAAG;AAC1B;AACA,EAAE,SAAS,GAAG,GAAG,CAAC,iBAAiB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AAC/D;AACA,EAAE,MAAM;AACR;AACA,EAAE,SAAS,GAAG,GAAG,CAAC,iBAAiB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,KAAK,UAAU,EAAE,KAAK,EAAE,CAAC;AAC9E;AACA,EAAE;AACF;AACA,CAAC,KAAK,SAAS,KAAK,IAAI,GAAG,OAAO,IAAI,CAAC;AACvC;AACA,CAAC,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC;AACjD;AACA,CAAC,OAAO;AACR;AACA,EAAE,QAAQ,EAAE,QAAQ;AACpB,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE;AACtB;AACA,EAAE,CAAC;AACH;AACA,CAAC;AACD;AACA,SAAS,+BAA+B,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG;AAC7E;AACA,CAAC,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;AACvC,CAAC,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;AACvC,CAAC,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;AACvC;AACA,CAAC,MAAM,YAAY,GAAG,iBAAiB,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,iBAAiB,EAAE,IAAI,EAAE,CAAC;AACpF;AACA,CAAC,KAAK,YAAY,GAAG;AACrB;AACA,EAAE,KAAK,EAAE,GAAG;AACZ;AACA,GAAG,GAAG,CAAC,mBAAmB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;AACpC,GAAG,GAAG,CAAC,mBAAmB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;AACpC,GAAG,GAAG,CAAC,mBAAmB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;AACpC;AACA,GAAG,YAAY,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,EAAE,iBAAiB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,OAAO,GAAG,EAAE,CAAC;AACpG;AACA,GAAG;AACH;AACA,EAAE,MAAM,IAAI,GAAG;AACf,GAAG,CAAC,EAAE,CAAC;AACP,GAAG,CAAC,EAAE,CAAC;AACP,GAAG,CAAC,EAAE,CAAC;AACP,GAAG,MAAM,EAAE,IAAI,OAAO,EAAE;AACxB,GAAG,aAAa,EAAE,CAAC;AACnB,GAAG,CAAC;AACJ;AACA,EAAE,QAAQ,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;AAChD;AACA,EAAE,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC;AAC3B,EAAE,YAAY,CAAC,SAAS,GAAG,CAAC,CAAC;AAC7B;AACA,EAAE;AACF;AACA,CAAC,OAAO,YAAY,CAAC;AACrB;AACA,CAAC;AACD;AACA;AACA,SAAS,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,aAAa,GAAG;AAC5D;AACA,CAAC,MAAM,SAAS,GAAG,GAAG,GAAG,CAAC,CAAC;AAC3B,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC;AACvC,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,GAAG,CAAC,EAAE,CAAC;AAC3C,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,GAAG,CAAC,EAAE,CAAC;AAC3C;AACA,CAAC,MAAM,YAAY,GAAG,+BAA+B,EAAE,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC;AACxH;AACA,CAAC,KAAK,YAAY,GAAG;AACrB;AACA,EAAE,YAAY,CAAC,SAAS,GAAG,GAAG,CAAC;AAC/B,EAAE,KAAK,aAAa,GAAG,aAAa,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC;AAC1D,EAAE,OAAO,YAAY,CAAC;AACtB;AACA,EAAE;AACF;AACA,CAAC,OAAO,IAAI,CAAC;AACb;AACA;;AChGO,SAAS,aAAa,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,aAAa,GAAG;AAC9E;AACA,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG;AAC7D;AACA,EAAE,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC;AACnD;AACA,EAAE;AACF;AACA,CAAC;AACD;AACO,SAAS,mBAAmB,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,GAAG;AACrE;AACA,CAAC,IAAI,IAAI,GAAG,QAAQ,CAAC;AACrB,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC;AAChB,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG;AAC7D;AACA,EAAE,MAAM,YAAY,GAAG,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACzD,EAAE,KAAK,YAAY,IAAI,YAAY,CAAC,QAAQ,GAAG,IAAI,GAAG;AACtD;AACA,GAAG,GAAG,GAAG,YAAY,CAAC;AACtB,GAAG,IAAI,GAAG,YAAY,CAAC,QAAQ,CAAC;AAChC;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC,OAAO,GAAG,CAAC;AACZ;AACA,CAAC;AACD;AACA;AACA;AACO,SAAS,uBAAuB,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,GAAG;AAClE;AACA,CAAC,KAAK,GAAG,KAAK,IAAI,GAAG;AACrB;AACA,EAAE,OAAO,IAAI,CAAC;AACd;AACA,EAAE;AACF;AACA,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC;AAC9C,CAAC,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;AAC7D,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB;AACA,CAAC,KAAK,GAAG,CAAC,QAAQ,GAAG,SAAS,CAAC,IAAI,IAAI,GAAG,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,GAAG;AACtE;AACA,EAAE,OAAO,IAAI,CAAC;AACd;AACA,EAAE,MAAM;AACR;AACA,EAAE,OAAO,GAAG,CAAC;AACb;AACA,EAAE;AACF;AACA;;ACrDA;AACO,SAAS,WAAW,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,GAAG;AAClD;AACA,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAClB,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAClB,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAClB;AACA,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACZ,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAChB,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAChB,CAAC,KAAK,KAAK,GAAG;AACd;AACA,EAAE,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AACvB,EAAE,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AAC3B,EAAE,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AAC3B;AACA,EAAE;AACF;AACA,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB;AACA,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB;AACA,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB;AACA,CAAC;AACD;AACO,SAAS,oBAAoB;AACpC,CAAC,MAAM;AACP,CAAC,KAAK;AACN,CAAC,QAAQ;AACT,CAAC,sBAAsB;AACvB,CAAC,SAAS;AACV,CAAC,KAAK;AACN,CAAC,QAAQ;AACT,EAAE;AACF;AACA,CAAC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AAC9B,CAAC,MAAM,GAAG,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;AAC1C,CAAC,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,KAAK,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACzD;AACA,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;AAC7C,EAAE,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AAC9B;AACA,EAAE,KAAK,sBAAsB,EAAE,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;AACjE;AACA,GAAG,OAAO,IAAI,CAAC;AACf;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC,OAAO,KAAK,CAAC;AACd;AACA,CAAC;AACD;AACA,MAAM,MAAM,mBAAmB,IAAI,OAAO,EAAE,CAAC;AAC7C,MAAM,MAAM,mBAAmB,IAAI,OAAO,EAAE,CAAC;AAC7C,MAAM,MAAM,mBAAmB,IAAI,OAAO,EAAE,CAAC;AAC7C,MAAM,OAAO,mBAAmB,IAAI,OAAO,EAAE,CAAC;AAC9C,MAAM,OAAO,mBAAmB,IAAI,OAAO,EAAE,CAAC;AAC9C,MAAM,OAAO,mBAAmB,IAAI,OAAO,EAAE,CAAC;AAC9C;AACO,SAAS,uBAAuB,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,GAAG;AAClF;AACA,CAAC,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC;AAC3C,CAAC,MAAM,SAAS,GAAG,QAAQ,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;AACvD,CAAC,MAAM,GAAG,GAAG,QAAQ,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC;AAC3C;AACA,CAAC,MAAM,CAAC,GAAG,OAAO,EAAE,aAAa,GAAG,CAAC,EAAE,CAAC;AACxC,CAAC,MAAM,CAAC,GAAG,OAAO,EAAE,aAAa,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AAC5C,CAAC,MAAM,CAAC,GAAG,OAAO,EAAE,aAAa,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AAC5C;AACA,CAAC,MAAM,CAAC,mBAAmB,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;AAC5C,CAAC,MAAM,CAAC,mBAAmB,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;AAC5C,CAAC,MAAM,CAAC,mBAAmB,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;AAC5C;AACA;AACA,CAAC,IAAI,aAAa,GAAG,CAAC,CAAC;AACvB,CAAC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AAChC,CAAC,MAAM,gBAAgB,GAAG,aAAa,GAAG,CAAC,CAAC;AAC5C,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;AACA,EAAE,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;AAC5B,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;AACjC,EAAE,KAAK,gBAAgB,IAAI,KAAK,IAAI,gBAAgB,GAAG,KAAK,GAAG,KAAK,GAAG;AACvE;AACA,GAAG,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;AACvC,GAAG,MAAM;AACT;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA;AACA,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC;AACf,CAAC,KAAK,GAAG,GAAG;AACZ;AACA,EAAE,OAAO,CAAC,mBAAmB,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACxC,EAAE,OAAO,CAAC,mBAAmB,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACxC,EAAE,OAAO,CAAC,mBAAmB,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACxC;AACA,EAAE,KAAK,MAAM,IAAI,MAAM,CAAC,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAC5C,OAAO,EAAE,GAAG,IAAI,OAAO,EAAE,CAAC;AAC1B;AACA,EAAE,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;AACjF;AACA,EAAE;AACF;AACA;AACA,CAAC,KAAK,MAAM,GAAG;AACf;AACA,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC;AACzC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACpB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACpB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACpB,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AAC5C,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;AACjE,EAAE,QAAQ,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AACnE;AACA,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC;AAC3B;AACA,EAAE,OAAO,MAAM,CAAC;AAChB;AACA,EAAE,MAAM;AACR;AACA,EAAE,OAAO;AACT,GAAG,IAAI,EAAE;AACT,IAAI,CAAC,EAAE,CAAC;AACR,IAAI,CAAC,EAAE,CAAC;AACR,IAAI,CAAC,EAAE,CAAC;AACR,IAAI,aAAa,EAAE,aAAa;AAChC,IAAI,MAAM,EAAE,QAAQ,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,OAAO,EAAE,EAAE;AACvE,IAAI;AACJ,GAAG,EAAE,EAAE,EAAE;AACT,GAAG,CAAC;AACJ;AACA,EAAE;AACF;AACA;;ACnJO,MAAM,aAAa,CAAC;AAC3B;AACA,CAAC,WAAW,EAAE,eAAe,GAAG;AAChC;AACA,EAAE,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;AAC1C,EAAE,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACxB;AACA,EAAE;AACF;AACA,CAAC,YAAY,GAAG;AAChB;AACA,EAAE,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;AACtC,EAAE,KAAK,UAAU,CAAC,MAAM,KAAK,CAAC,GAAG;AACjC;AACA,GAAG,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAClC;AACA,GAAG,MAAM;AACT;AACA,GAAG,OAAO,UAAU,CAAC,GAAG,EAAE,CAAC;AAC3B;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC,gBAAgB,EAAE,SAAS,GAAG;AAC/B;AACA,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC;AACrC;AACA,EAAE;AACF;AACA;;AC9BO,SAAS,OAAO,EAAE,GAAG,EAAE,WAAW,GAAG;AAC5C;AACA,CAAC,OAAO,WAAW,EAAE,GAAG,GAAG,EAAE,EAAE,KAAK,MAAM,CAAC;AAC3C;AACA,CAAC;AACD;AACO,SAAS,MAAM,EAAE,GAAG,EAAE,WAAW,GAAG;AAC3C;AACA,CAAC,OAAO,WAAW,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;AAC/B;AACA,CAAC;AACD;AACO,SAAS,KAAK,EAAE,GAAG,EAAE,WAAW,GAAG;AAC1C;AACA,CAAC,OAAO,WAAW,EAAE,GAAG,GAAG,EAAE,EAAE,CAAC;AAChC;AACA,CAAC;AACD;AACO,SAAS,SAAS,EAAE,GAAG,GAAG;AACjC;AACA,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC;AAChB;AACA,CAAC;AACD;AACO,SAAS,UAAU,EAAE,GAAG,EAAE,WAAW,GAAG;AAC/C;AACA,CAAC,OAAO,WAAW,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;AAC/B;AACA,CAAC;AACD;AACO,SAAS,UAAU,EAAE,GAAG,EAAE,WAAW,GAAG;AAC/C;AACA,CAAC,OAAO,WAAW,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;AAC/B;AACA,CAAC;AACD;AACO,SAAS,mBAAmB,EAAE,GAAG,GAAG;AAC3C;AACA,CAAC,OAAO,GAAG,CAAC;AACZ;AACA;;AC7BA,MAAMA,aAAW,GAAG,IAAI,IAAI,EAAE,CAAC;AAC/B,MAAM,eAAe,GAAG,IAAI,OAAO,EAAE,CAAC;AACtC,MAAM,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACpC;AACO,SAAS,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,GAAG;AACxE;AACA,CAAC,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,EAAE,YAAY,GAAG,aAAa,EAAE,WAAW,GAAG,YAAY,EAAE,WAAW,GAAG,YAAY,CAAC;AACzH;AACA,CAAC,MAAM,MAAM,GAAG,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACpD,CAAC,KAAK,MAAM,GAAG;AACf;AACA,EAAE,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACpD,EAAE,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAClD;AACA,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;AAClE;AACA,EAAE,MAAM;AACR;AACA,EAAE,MAAM,SAAS,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;AAC7C,EAAE,KAAK,YAAY,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,EAAE,eAAe,EAAE,GAAG;AACvE;AACA,GAAG,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;AACzD;AACA,GAAG;AACH;AACA,EAAE,MAAM,UAAU,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAC5D,EAAE,KAAK,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,EAAE,eAAe,EAAE,GAAG;AACxE;AACA,GAAG,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;AAC1D;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC;AACD;AACO,SAAS,YAAY,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,GAAG;AACjE;AACA,CAAC,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,EAAE,YAAY,GAAG,aAAa,EAAE,WAAW,GAAG,YAAY,EAAE,WAAW,GAAG,YAAY,CAAC;AACzH;AACA,CAAC,MAAM,MAAM,GAAG,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACpD,CAAC,KAAK,MAAM,GAAG;AACf;AACA,EAAE,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACpD,EAAE,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAClD,EAAE,OAAO,mBAAmB,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AACnE;AACA,EAAE,MAAM;AACR;AACA;AACA;AACA,EAAE,MAAM,SAAS,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAC3D,EAAE,MAAM,OAAO,GAAG,SAAS,EAAE,SAAS,EAAE,CAAC;AACzC,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC;AAC1C,EAAE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,CAAC;AAClC;AACA;AACA,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;AACb,EAAE,KAAK,WAAW,GAAG;AACrB;AACA,GAAG,EAAE,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;AACjC,GAAG,EAAE,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAC/C;AACA,GAAG,MAAM;AACT;AACA,GAAG,EAAE,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAC/C,GAAG,EAAE,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;AACjC;AACA,GAAG;AACH;AACA,EAAE,MAAM,cAAc,GAAG,YAAY,EAAE,EAAE,EAAE,YAAY,EAAE,GAAG,EAAE,eAAe,EAAE,CAAC;AAChF,EAAE,MAAM,QAAQ,GAAG,cAAc,GAAG,YAAY,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AACnF;AACA;AACA;AACA,EAAE,KAAK,QAAQ,GAAG;AAClB;AACA;AACA;AACA,GAAG,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC;AAC3C,GAAG,MAAM,SAAS,GAAG,WAAW;AAChC,IAAI,KAAK,IAAI,YAAY,EAAE,EAAE,GAAG,SAAS,EAAE;AAC3C,IAAI,KAAK,IAAI,YAAY,EAAE,EAAE,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC;AAChD;AACA,GAAG,KAAK,SAAS,GAAG;AACpB;AACA,IAAI,OAAO,QAAQ,CAAC;AACpB;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA;AACA;AACA,EAAE,MAAM,cAAc,GAAG,YAAY,EAAE,EAAE,EAAE,YAAY,EAAE,GAAG,EAAE,eAAe,EAAE,CAAC;AAChF,EAAE,MAAM,QAAQ,GAAG,cAAc,GAAG,YAAY,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AACnF;AACA,EAAE,KAAK,QAAQ,IAAI,QAAQ,GAAG;AAC9B;AACA,GAAG,OAAO,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;AACvE;AACA,GAAG,MAAM;AACT;AACA,GAAG,OAAO,QAAQ,IAAI,QAAQ,IAAI,IAAI,CAAC;AACvC;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC;AACD;AACO,MAAM,SAAS,GAAG,EAAE,YAAY;AACvC;AACA,CAAC,IAAI,KAAK,EAAE,KAAK,CAAC;AAClB,CAAC,MAAM,QAAQ,GAAG,EAAE,CAAC;AACrB,CAAC,MAAM,OAAO,GAAG,IAAI,aAAa,EAAE,MAAM,IAAI,IAAI,EAAE,EAAE,CAAC;AACvD;AACA,CAAC,OAAO,SAAS,SAAS,EAAE,GAAG,IAAI,GAAG;AACtC;AACA,EAAE,KAAK,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;AACjC,EAAE,KAAK,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;AACjC,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AAChC;AACA,EAAE,MAAM,MAAM,GAAG,iBAAiB,EAAE,GAAG,IAAI,EAAE,CAAC;AAC9C;AACA,EAAE,OAAO,CAAC,gBAAgB,EAAE,KAAK,EAAE,CAAC;AACpC,EAAE,OAAO,CAAC,gBAAgB,EAAE,KAAK,EAAE,CAAC;AACpC,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC;AACjB,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC;AACjB;AACA,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AACjC,EAAE,KAAK,MAAM,GAAG,CAAC,GAAG;AACpB;AACA,GAAG,KAAK,GAAG,QAAQ,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;AAClC,GAAG,KAAK,GAAG,QAAQ,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;AAClC;AACA,GAAG;AACH;AACA,EAAE,OAAO,MAAM,CAAC;AAChB;AACA,EAAE,CAAC;AACH;AACA,CAAC,SAAS,iBAAiB;AAC3B,EAAE,WAAW;AACb,EAAE,QAAQ;AACV,EAAE,oBAAoB;AACtB,EAAE,mBAAmB;AACrB,EAAE,aAAa,GAAG,IAAI;AACtB,EAAE,mBAAmB,GAAG,CAAC;AACzB,EAAE,KAAK,GAAG,CAAC;AACX,GAAG;AACH;AACA;AACA;AACA,EAAE,SAAS,aAAa,EAAE,WAAW,GAAG;AACxC;AACA,GAAG,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,YAAY,EAAE,WAAW,GAAG,YAAY,CAAC;AAC7F;AACA;AACA,GAAG,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;AACnD;AACA,IAAI,WAAW,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;AAC3C,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;AAClC;AACA,IAAI;AACJ;AACA,GAAG,OAAO,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAC7C;AACA,GAAG;AACH;AACA,EAAE,SAAS,iBAAiB,EAAE,WAAW,GAAG;AAC5C;AACA,GAAG,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,YAAY,EAAE,WAAW,GAAG,YAAY,CAAC;AAC7F;AACA;AACA,GAAG,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;AACnD;AACA;AACA,IAAI,WAAW,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACzD,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;AAClC;AACA,IAAI;AACJ;AACA;AACA,GAAG,OAAO,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACjF;AACA,GAAG;AACH;AACA,EAAE,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,EAAE,YAAY,GAAG,aAAa,EAAE,WAAW,GAAG,YAAY,EAAE,WAAW,GAAG,YAAY,CAAC;AAC1H;AACA,EAAE,MAAM,MAAM,GAAG,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACrD,EAAE,KAAK,MAAM,GAAG;AAChB;AACA,GAAG,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACrD,GAAG,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACnD,GAAG,UAAU,EAAE,mBAAmB,EAAE,WAAW,EAAE,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;AACzE,GAAG,OAAO,mBAAmB,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,GAAG,WAAW,EAAE,KAAK,EAAE,CAAC;AACvG;AACA,GAAG,MAAM;AACT;AACA,GAAG,MAAM,IAAI,GAAG,SAAS,EAAE,WAAW,EAAE,CAAC;AACzC,GAAG,MAAM,KAAK,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACxD,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC;AACjB,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC;AAClB;AACA,GAAG,IAAI,MAAM,EAAE,MAAM,CAAC;AACtB,GAAG,IAAI,IAAI,EAAE,IAAI,CAAC;AAClB,GAAG,KAAK,aAAa,GAAG;AACxB;AACA,IAAI,IAAI,GAAG,KAAK,CAAC;AACjB,IAAI,IAAI,GAAG,KAAK,CAAC;AACjB;AACA;AACA,IAAI,UAAU,EAAE,mBAAmB,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;AAChE,IAAI,UAAU,EAAE,mBAAmB,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;AAChE;AACA,IAAI,MAAM,GAAG,aAAa,EAAE,IAAI,EAAE,CAAC;AACnC,IAAI,MAAM,GAAG,aAAa,EAAE,IAAI,EAAE,CAAC;AACnC;AACA,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG;AAC3B;AACA,KAAK,EAAE,GAAG,KAAK,CAAC;AAChB,KAAK,EAAE,GAAG,IAAI,CAAC;AACf;AACA,KAAK,MAAM,IAAI,GAAG,MAAM,CAAC;AACzB,KAAK,MAAM,GAAG,MAAM,CAAC;AACrB,KAAK,MAAM,GAAG,IAAI,CAAC;AACnB;AACA,KAAK,IAAI,GAAG,IAAI,CAAC;AACjB;AACA;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA;AACA,GAAG,KAAK,EAAE,IAAI,GAAG;AACjB;AACA,IAAI,IAAI,GAAG,KAAK,CAAC;AACjB,IAAI,UAAU,EAAE,mBAAmB,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;AAChE;AACA,IAAI;AACJ;AACA,GAAG,MAAM,QAAQ,GAAG,OAAO,EAAE,EAAE,GAAG,CAAC,EAAE,WAAW,EAAE,CAAC;AACnD,GAAG,MAAM,cAAc,GAAG,oBAAoB,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,mBAAmB,GAAG,EAAE,EAAE,CAAC;AAC9G;AACA,GAAG,IAAI,eAAe,CAAC;AACvB,GAAG,KAAK,cAAc,KAAK,SAAS,GAAG;AACvC;AACA,IAAI,MAAM,MAAM,GAAG,aAAa,EAAE,EAAE,EAAE,CAAC;AACvC,IAAI,MAAM,GAAG,GAAG,iBAAiB,EAAE,EAAE,EAAE,CAAC;AACxC,IAAI,MAAM,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC;AAC/B;AACA,IAAI,eAAe,GAAG,mBAAmB,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,mBAAmB,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC;AAC5G;AACA,IAAI,MAAM;AACV;AACA,IAAI,eAAe;AACnB,KAAK,cAAc;AACnB,KAAK,iBAAiB;AACtB,MAAM,EAAE;AACR,MAAM,QAAQ;AACd,MAAM,oBAAoB;AAC1B,MAAM,mBAAmB;AACzB,MAAM,aAAa;AACnB,MAAM,mBAAmB;AACzB,MAAM,KAAK,GAAG,CAAC;AACf,MAAM,CAAC;AACP;AACA,IAAI;AACJ;AACA,GAAG,KAAK,eAAe,GAAG,OAAO,IAAI,CAAC;AACtC;AACA;AACA;AACA,GAAG,IAAI,GAAG,KAAK,CAAC;AAChB,GAAG,UAAU,EAAE,mBAAmB,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;AAC/D;AACA,GAAG,MAAM,QAAQ,GAAG,OAAO,EAAE,EAAE,GAAG,CAAC,EAAE,WAAW,EAAE,CAAC;AACnD,GAAG,MAAM,cAAc,GAAG,oBAAoB,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,mBAAmB,GAAG,EAAE,EAAE,CAAC;AAC9G;AACA,GAAG,IAAI,eAAe,CAAC;AACvB,GAAG,KAAK,cAAc,KAAK,SAAS,GAAG;AACvC;AACA,IAAI,MAAM,MAAM,GAAG,aAAa,EAAE,EAAE,EAAE,CAAC;AACvC,IAAI,MAAM,GAAG,GAAG,iBAAiB,EAAE,EAAE,EAAE,CAAC;AACxC,IAAI,MAAM,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC;AAC/B;AACA,IAAI,eAAe,GAAG,mBAAmB,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,mBAAmB,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC;AAC5G;AACA,IAAI,MAAM;AACV;AACA,IAAI,eAAe;AACnB,KAAK,cAAc;AACnB,KAAK,iBAAiB;AACtB,MAAM,EAAE;AACR,MAAM,QAAQ;AACd,MAAM,oBAAoB;AAC1B,MAAM,mBAAmB;AACzB,MAAM,aAAa;AACnB,MAAM,mBAAmB;AACzB,MAAM,KAAK,GAAG,CAAC;AACf,MAAM,CAAC;AACP;AACA,IAAI;AACJ;AACA,GAAG,KAAK,eAAe,GAAG,OAAO,IAAI,CAAC;AACtC;AACA,GAAG,OAAO,KAAK,CAAC;AAChB;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC,IAAI,CAAC;AACN;AACO,MAAM,kBAAkB,GAAG,EAAE,YAAY;AAChD;AACA,CAAC,MAAM,QAAQ,GAAG,IAAI,gBAAgB,EAAE,CAAC;AACzC,CAAC,MAAM,SAAS,GAAG,IAAI,gBAAgB,EAAE,CAAC;AAC1C,CAAC,MAAM,WAAW,GAAG,IAAI,OAAO,EAAE,CAAC;AACnC;AACA,CAAC,MAAM,GAAG,GAAG,IAAI,WAAW,EAAE,CAAC;AAC/B,CAAC,MAAM,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC;AAChC;AACA,CAAC,OAAO,SAAS,kBAAkB,EAAE,WAAW,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,GAAG,IAAI,GAAG;AAC7G;AACA,EAAE,IAAI,WAAW,GAAG,WAAW,GAAG,CAAC,EAAE,YAAY,GAAG,aAAa,EAAE,WAAW,GAAG,YAAY,EAAE,WAAW,GAAG,YAAY,CAAC;AAC1H;AACA,EAAE,KAAK,SAAS,KAAK,IAAI,GAAG;AAC5B;AACA,GAAG,KAAK,EAAE,aAAa,CAAC,WAAW,GAAG;AACtC;AACA,IAAI,aAAa,CAAC,kBAAkB,EAAE,CAAC;AACvC;AACA,IAAI;AACJ;AACA,GAAG,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,EAAE,CAAC;AAC1F,GAAG,SAAS,GAAG,GAAG,CAAC;AACnB;AACA,GAAG;AACH;AACA,EAAE,MAAM,MAAM,GAAG,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACrD,EAAE,KAAK,MAAM,GAAG;AAChB;AACA,GAAG,MAAM,YAAY,GAAG,QAAQ,CAAC;AACjC,GAAG,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC;AACxC,GAAG,MAAM,OAAO,GAAG,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC;AACpD;AACA,GAAG,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;AACrC,GAAG,MAAM,GAAG,GAAG,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;AACjD;AACA,GAAG,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACrD,GAAG,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACnD;AACA;AACA;AACA;AACA,GAAG,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC;AAC9C;AACA,GAAG,KAAK,aAAa,CAAC,UAAU,GAAG;AACnC;AACA,IAAI,UAAU,EAAE,mBAAmB,EAAE,WAAW,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;AACzE,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AACpC,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B;AACA,IAAI,MAAM,GAAG,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,EAAE;AACpD;AACA,KAAK,gBAAgB,EAAE,GAAG,IAAI,IAAI,CAAC,aAAa,EAAE,GAAG,EAAE;AACvD;AACA,KAAK,kBAAkB,EAAE,GAAG,IAAI;AAChC;AACA,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;AAC1C,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;AAC1C,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;AAC1C,MAAM,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC;AAC7B;AACA,MAAM,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AAC5E;AACA;AACA,OAAO,WAAW,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;AACvD,OAAO,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;AACpC,OAAO,KAAK,GAAG,CAAC,kBAAkB,EAAE,SAAS,EAAE,GAAG;AAClD;AACA,QAAQ,OAAO,IAAI,CAAC;AACpB;AACA,QAAQ;AACR;AACA,OAAO;AACP;AACA,MAAM,OAAO,KAAK,CAAC;AACnB;AACA,MAAM;AACN;AACA,KAAK,EAAE,CAAC;AACR;AACA,IAAI,OAAO,GAAG,CAAC;AACf;AACA,IAAI,MAAM;AACV;AACA,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,KAAK,KAAK,GAAG,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AAC1E;AACA;AACA,KAAK,WAAW,EAAE,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;AACpD,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,EAAE,WAAW,EAAE,CAAC;AAC5C,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,EAAE,WAAW,EAAE,CAAC;AAC5C,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,EAAE,WAAW,EAAE,CAAC;AAC5C,KAAK,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AACjC;AACA,KAAK,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG;AAC5D;AACA,MAAM,WAAW,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;AAC/C,MAAM,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;AACnC;AACA,MAAM,KAAK,QAAQ,CAAC,kBAAkB,EAAE,SAAS,EAAE,GAAG;AACtD;AACA,OAAO,OAAO,IAAI,CAAC;AACnB;AACA,OAAO;AACP;AACA,MAAM;AACN;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG,MAAM;AACT;AACA,GAAG,MAAM,IAAI,GAAG,WAAW,GAAG,CAAC,CAAC;AAChC,GAAG,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AAChD;AACA,GAAG,UAAU,EAAE,mBAAmB,EAAE,IAAI,EAAE,EAAE,YAAY,EAAEA,aAAW,EAAE,CAAC;AACxE,GAAG,MAAM,gBAAgB;AACzB,IAAI,SAAS,CAAC,aAAa,EAAEA,aAAW,EAAE;AAC1C,IAAI,kBAAkB,EAAE,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;AAClF;AACA,GAAG,KAAK,gBAAgB,GAAG,OAAO,IAAI,CAAC;AACvC;AACA,GAAG,UAAU,EAAE,mBAAmB,EAAE,KAAK,EAAE,EAAE,YAAY,EAAEA,aAAW,EAAE,CAAC;AACzE,GAAG,MAAM,iBAAiB;AAC1B,IAAI,SAAS,CAAC,aAAa,EAAEA,aAAW,EAAE;AAC1C,IAAI,kBAAkB,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;AACnF;AACA,GAAG,KAAK,iBAAiB,GAAG,OAAO,IAAI,CAAC;AACxC;AACA,GAAG,OAAO,KAAK,CAAC;AAChB;AACA,GAAG;AACH;AACA,EAAE,CAAC;AACH;AACA,CAAC,IAAI,CAAC;AACN;AACA,SAAS,YAAY,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,GAAG;AACzD;AACA,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,EAAEA,aAAW,EAAE,CAAC;AAC/C,CAAC,OAAO,GAAG,CAAC,YAAY,EAAEA,aAAW,EAAE,MAAM,EAAE,CAAC;AAChD;AACA,CAAC;AACD;AACA,MAAM,WAAW,GAAG,EAAE,CAAC;AACvB,IAAI,WAAW,CAAC;AAChB,IAAI,aAAa,CAAC;AAClB,IAAI,YAAY,CAAC;AACjB,IAAI,YAAY,CAAC;AACV,SAAS,SAAS,EAAE,MAAM,GAAG;AACpC;AACA,CAAC,KAAK,WAAW,GAAG;AACpB;AACA,EAAE,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAClC;AACA,EAAE;AACF;AACA,CAAC,WAAW,GAAG,MAAM,CAAC;AACtB,CAAC,aAAa,GAAG,IAAI,YAAY,EAAE,MAAM,EAAE,CAAC;AAC5C,CAAC,YAAY,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;AAC1C,CAAC,YAAY,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;AAC1C;AACA,CAAC;AACD;AACO,SAAS,WAAW,GAAG;AAC9B;AACA,CAAC,WAAW,GAAG,IAAI,CAAC;AACpB,CAAC,aAAa,GAAG,IAAI,CAAC;AACtB,CAAC,YAAY,GAAG,IAAI,CAAC;AACrB,CAAC,YAAY,GAAG,IAAI,CAAC;AACrB;AACA,CAAC,KAAK,WAAW,CAAC,MAAM,GAAG;AAC3B;AACA,EAAE,SAAS,EAAE,WAAW,CAAC,GAAG,EAAE,EAAE,CAAC;AACjC;AACA,EAAE;AACF;AACA;;ACveA,MAAM,eAAe,GAAG,MAAM,EAAE,sBAAsB,EAAE,CAAC;AACzD;AACA,MAAM,IAAI,mBAAmB,IAAI,IAAI,EAAE,CAAC;AACxC,MAAM,KAAK,mBAAmB,IAAI,IAAI,EAAE,CAAC;AACzC,MAAM,UAAU,mBAAmB,IAAI,OAAO,EAAE,CAAC;AACjD,MAAM,GAAG,mBAAmB,IAAI,WAAW,EAAE,CAAC;AAC9C,MAAM,IAAI,mBAAmB,IAAI,WAAW,EAAE,CAAC;AAC/C,MAAM,IAAI,mBAAmB,IAAI,OAAO,EAAE,CAAC;AAC3C,MAAM,KAAK,mBAAmB,IAAI,OAAO,EAAE,CAAC;AAC5C,MAAM,KAAK,mBAAmB,IAAI,OAAO,EAAE,CAAC;AAC5C,MAAM,KAAK,mBAAmB,IAAI,OAAO,EAAE,CAAC;AAC5C,MAAM,KAAK,mBAAmB,IAAI,OAAO,EAAE,CAAC;AAC5C,MAAM,OAAO,mBAAmB,IAAI,IAAI,EAAE,CAAC;AAC3C,MAAM,YAAY,mBAAmB,IAAI,aAAa,EAAE,MAAM,IAAI,gBAAgB,EAAE,EAAE,CAAC;AACvF;AACO,MAAM,OAAO,CAAC;AACrB;AACA,CAAC,OAAO,SAAS,EAAE,GAAG,EAAE,OAAO,GAAG,EAAE,GAAG;AACvC;AACA,EAAE,KAAK,OAAO,CAAC,gBAAgB,GAAG;AAClC;AACA,GAAG,OAAO,CAAC,IAAI,EAAE,sGAAsG,EAAE,CAAC;AAC1H;AACA,GAAG,OAAO,OAAO,CAAC,SAAS;AAC3B,IAAI,SAAS,EAAE,CAAC,EAAE;AAClB,IAAI;AACJ,KAAK,YAAY,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,SAAS,GAAG,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE;AACvE,KAAK;AACL,IAAI,CAAC;AACL;AACA,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,GAAG,YAAY,EAAE,IAAI;AACrB,GAAG,GAAG,OAAO;AACb,GAAG,CAAC;AACJ;AACA,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;AAChC,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC;AAC9B,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;AAC7C,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,KAAK,OAAO,CAAC,YAAY,GAAG;AAC9B;AACA,GAAG,MAAM,GAAG;AACZ,IAAI,KAAK,EAAE,QAAQ,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE;AAC/C,IAAI,KAAK,EAAE,cAAc,CAAC,KAAK,CAAC,KAAK,EAAE;AACvC,IAAI,CAAC;AACL;AACA,GAAG,MAAM;AACT;AACA,GAAG,MAAM,GAAG;AACZ,IAAI,KAAK,EAAE,QAAQ;AACnB,IAAI,KAAK,EAAE,cAAc,CAAC,KAAK;AAC/B,IAAI,CAAC;AACL;AACA,GAAG;AACH;AACA,EAAE,OAAO,MAAM,CAAC;AAChB;AACA,EAAE;AACF;AACA,CAAC,OAAO,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,GAAG,EAAE,GAAG;AACpD;AACA,EAAE,KAAK,OAAO,OAAO,KAAK,SAAS,GAAG;AACtC;AACA,GAAG,OAAO,CAAC,IAAI,EAAE,wGAAwG,EAAE,CAAC;AAC5H;AACA,GAAG,OAAO,OAAO,CAAC,WAAW;AAC7B,IAAI,SAAS,EAAE,CAAC,EAAE;AAClB,IAAI,SAAS,EAAE,CAAC,EAAE;AAClB,IAAI;AACJ,KAAK,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,SAAS,GAAG,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE;AACnE,KAAK;AACL,IAAI,CAAC;AACL;AACA,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,GAAG,QAAQ,EAAE,IAAI;AACjB,GAAG,GAAG,OAAO;AACb,GAAG,CAAC;AACJ;AACA,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;AAChC,EAAE,MAAM,GAAG,GAAG,IAAI,OAAO,EAAE,QAAQ,EAAE,EAAE,GAAG,OAAO,EAAE,EAAE,eAAe,IAAI,IAAI,EAAE,EAAE,CAAC;AACjF,EAAE,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC;AACrB;AACA,EAAE,KAAK,OAAO,CAAC,QAAQ,GAAG;AAC1B;AACA,GAAG,MAAM,cAAc,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;AAC9C,GAAG,KAAK,cAAc,KAAK,IAAI,GAAG;AAClC;AACA,IAAI,MAAM,QAAQ,GAAG,IAAI,eAAe,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC;AACjE,IAAI,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC;AAClC;AACA,IAAI,MAAM,KAAK,cAAc,CAAC,KAAK,KAAK,KAAK,GAAG;AAChD;AACA,IAAI,cAAc,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC;AACtC,IAAI,cAAc,CAAC,WAAW,GAAG,IAAI,CAAC;AACtC;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,OAAO,GAAG,CAAC;AACb;AACA,EAAE;AACF;AACA,CAAC,WAAW,EAAE,QAAQ,EAAE,OAAO,GAAG,EAAE,GAAG;AACvC;AACA,EAAE,KAAK,EAAE,QAAQ,CAAC,gBAAgB,GAAG;AACrC;AACA,GAAG,MAAM,IAAI,KAAK,EAAE,+CAA+C,EAAE,CAAC;AACtE;AACA,GAAG,MAAM,KAAK,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,4BAA4B,GAAG;AAC9E;AACA,GAAG,MAAM,IAAI,KAAK,EAAE,+EAA+E,EAAE,CAAC;AACtG;AACA,GAAG;AACH;AACA;AACA,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE;AAC3B;AACA,GAAG,QAAQ,EAAE,MAAM;AACnB,GAAG,QAAQ,EAAE,EAAE;AACf,GAAG,WAAW,EAAE,EAAE;AAClB,GAAG,OAAO,EAAE,IAAI;AAChB,GAAG,oBAAoB,EAAE,KAAK;AAC9B,GAAG,cAAc,EAAE,IAAI;AACvB,GAAG,UAAU,EAAE,IAAI;AACnB;AACA;AACA;AACA;AACA,GAAG,EAAE,eAAe,IAAI,KAAK;AAC7B;AACA,GAAG,EAAE,OAAO,EAAE,CAAC;AACf;AACA,EAAE,KAAK,OAAO,CAAC,oBAAoB,IAAI,OAAO,iBAAiB,KAAK,WAAW,GAAG;AAClF;AACA,GAAG,MAAM,IAAI,KAAK,EAAE,8CAA8C,EAAE,CAAC;AACrE;AACA,GAAG;AACH;AACA,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACrB,EAAE,KAAK,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG;AACtC;AACA,GAAG,IAAI,CAAC,MAAM,GAAG,eAAe,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;AACtD;AACA,GAAG,KAAK,EAAE,QAAQ,CAAC,WAAW,IAAI,OAAO,CAAC,cAAc,GAAG;AAC3D;AACA,IAAI,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC;AAC7D;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA;AACA;AACA,EAAE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC3B;AACA,EAAE;AACF;AACA,CAAC,KAAK,EAAE,WAAW,GAAG,IAAI,GAAG;AAC7B;AACA,EAAE,KAAK,WAAW,IAAI,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,GAAG;AACrD;AACA,GAAG,WAAW,GAAG,IAAI,GAAG,EAAE,WAAW,EAAE,CAAC;AACxC;AACA,GAAG;AACH;AACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjC,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC;AACxC,EAAE,MAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;AAC/C;AACA,EAAE,IAAI,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;AACrD,EAAE,IAAI,UAAU,GAAG,CAAC,CAAC;AACrB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AAC5B,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;AACA,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC;AACvB,GAAG,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;AAC3C,GAAG,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;AAC3C,GAAG,YAAY,GAAG,IAAI,YAAY,EAAE,MAAM,EAAE,CAAC;AAC7C;AACA,GAAG,SAAS,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC;AAC9B,GAAG,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC;AACnC;AACA,GAAG;AACH;AACA,EAAE,SAAS,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,GAAG,KAAK,GAAG;AAC/D;AACA,GAAG,MAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;AACvC,GAAG,MAAM,MAAM,GAAG,WAAW,EAAE,WAAW,GAAG,EAAE,EAAE,KAAK,gBAAgB,CAAC;AACvE,GAAG,KAAK,MAAM,GAAG;AACjB;AACA,IAAI,MAAM,MAAM,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AAClD,IAAI,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,EAAE,EAAE,CAAC;AAClD;AACA,IAAI,IAAI,IAAI,GAAG,QAAQ,CAAC;AACxB,IAAI,IAAI,IAAI,GAAG,QAAQ,CAAC;AACxB,IAAI,IAAI,IAAI,GAAG,QAAQ,CAAC;AACxB,IAAI,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;AAC1B,IAAI,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;AAC1B,IAAI,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;AAC1B;AACA,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,KAAK,MAAM,GAAG,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACxE;AACA,KAAK,MAAM,KAAK,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;AACjC,KAAK,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACrC,KAAK,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACrC,KAAK,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACrC;AACA,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AAC9B,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AAC9B;AACA,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AAC9B,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AAC9B;AACA,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AAC9B,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;AAC9B;AACA,KAAK;AACL;AACA,IAAI;AACJ,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;AAC7C,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;AAC7C,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;AAC7C;AACA,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;AAC7C,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;AAC7C,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,KAAK,IAAI;AAC7C,MAAM;AACN;AACA,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC5C,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC5C,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC5C;AACA,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC5C,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC5C,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC5C;AACA,KAAK,OAAO,IAAI,CAAC;AACjB;AACA,KAAK,MAAM;AACX;AACA,KAAK,OAAO,KAAK,CAAC;AAClB;AACA,KAAK;AACL;AACA,IAAI,MAAM;AACV;AACA,IAAI,MAAM,IAAI,GAAG,WAAW,GAAG,CAAC,CAAC;AACjC,IAAI,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AACjD;AACA;AACA;AACA,IAAI,MAAM,UAAU,GAAG,IAAI,GAAG,UAAU,CAAC;AACzC,IAAI,MAAM,WAAW,GAAG,KAAK,GAAG,UAAU,CAAC;AAC3C,IAAI,IAAI,aAAa,GAAG,KAAK,CAAC;AAC9B,IAAI,IAAI,YAAY,GAAG,KAAK,CAAC;AAC7B,IAAI,IAAI,aAAa,GAAG,KAAK,CAAC;AAC9B;AACA,IAAI,KAAK,WAAW,GAAG;AACvB;AACA;AACA;AACA,KAAK,KAAK,EAAE,aAAa,GAAG;AAC5B;AACA,MAAM,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE,UAAU,EAAE,CAAC;AACnD,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE,WAAW,EAAE,CAAC;AACrD,MAAM,aAAa,GAAG,EAAE,YAAY,IAAI,EAAE,aAAa,CAAC;AACxD;AACA,MAAM;AACN;AACA,KAAK,MAAM;AACX;AACA,KAAK,YAAY,GAAG,IAAI,CAAC;AACzB,KAAK,aAAa,GAAG,IAAI,CAAC;AAC1B;AACA,KAAK;AACL;AACA,IAAI,MAAM,YAAY,GAAG,aAAa,IAAI,YAAY,CAAC;AACvD,IAAI,MAAM,aAAa,GAAG,aAAa,IAAI,aAAa,CAAC;AACzD;AACA,IAAI,IAAI,UAAU,GAAG,KAAK,CAAC;AAC3B,IAAI,KAAK,YAAY,GAAG;AACxB;AACA,KAAK,UAAU,GAAG,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;AAC/D;AACA,KAAK;AACL;AACA,IAAI,IAAI,WAAW,GAAG,KAAK,CAAC;AAC5B,IAAI,KAAK,aAAa,GAAG;AACzB;AACA,KAAK,WAAW,GAAG,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;AACjE;AACA,KAAK;AACL;AACA,IAAI,MAAM,SAAS,GAAG,UAAU,IAAI,WAAW,CAAC;AAChD,IAAI,KAAK,SAAS,GAAG;AACrB;AACA,KAAK,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACpC;AACA,MAAM,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;AAC7B,MAAM,MAAM,MAAM,GAAG,KAAK,GAAG,CAAC,CAAC;AAC/B,MAAM,MAAM,YAAY,GAAG,YAAY,EAAE,KAAK,EAAE,CAAC;AACjD,MAAM,MAAM,YAAY,GAAG,YAAY,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;AACrD,MAAM,MAAM,aAAa,GAAG,YAAY,EAAE,MAAM,EAAE,CAAC;AACnD,MAAM,MAAM,aAAa,GAAG,YAAY,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;AACvD;AACA,MAAM,YAAY,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,YAAY,GAAG,aAAa,GAAG,YAAY,GAAG,aAAa,CAAC;AACpG,MAAM,YAAY,EAAE,WAAW,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,GAAG,aAAa,GAAG,YAAY,GAAG,aAAa,CAAC;AACxG;AACA,MAAM;AACN;AACA,KAAK;AACL;AACA,IAAI,OAAO,SAAS,CAAC;AACrB;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,GAAG,CAAC,GAAG;AACrC;AACA,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC;AAC1C,EAAE,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;AAChD,EAAE,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;AAChD,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;AACjB;AACA,EAAE,SAAS,SAAS,EAAE,WAAW,EAAE,KAAK,GAAG,CAAC,GAAG;AAC/C;AACA,GAAG,MAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;AACvC,GAAG,MAAM,MAAM,GAAG,WAAW,EAAE,WAAW,GAAG,EAAE,EAAE,KAAK,gBAAgB,CAAC;AACvE,GAAG,KAAK,MAAM,GAAG;AACjB;AACA,IAAI,MAAM,MAAM,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AAClD,IAAI,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,EAAE,EAAE,CAAC;AAClD,IAAI,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,YAAY,EAAE,MAAM,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AAC7F;AACA,IAAI,MAAM;AACV;AACA;AACA,IAAI,MAAM,IAAI,GAAG,WAAW,GAAG,cAAc,GAAG,CAAC,CAAC;AAClD,IAAI,MAAM,KAAK,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AACjD,IAAI,MAAM,SAAS,GAAG,WAAW,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;AACrD,IAAI,MAAM,aAAa,GAAG,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,YAAY,EAAE,MAAM,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC;AAC/G;AACA,IAAI,KAAK,EAAE,aAAa,GAAG;AAC3B;AACA,KAAK,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;AAClC,KAAK,SAAS,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;AACnC;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA;AACA,CAAC,OAAO,EAAE,GAAG,EAAE,cAAc,GAAG,SAAS,GAAG;AAC5C;AACA,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AAC5B,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjC,EAAE,MAAM,UAAU,GAAG,EAAE,CAAC;AACxB,EAAE,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;AAC/C,EAAE,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,EAAE,cAAc,EAAE,CAAC;AAC1D;AACA,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AACjC,EAAE,MAAM,IAAI,GAAG,UAAU,GAAG,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC;AACjE,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;AACA,GAAG,MAAM,YAAY,GAAG,eAAe,GAAG,cAAc,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;AAClG,GAAG,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;AACxC;AACA,GAAG,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;AAC3B,GAAG,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;AACzD,GAAG,WAAW,EAAE,CAAC;AACjB;AACA,GAAG,KAAK,eAAe,GAAG;AAC1B;AACA,IAAI,MAAM,aAAa,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,aAAa,CAAC;AACpD,IAAI,MAAM,IAAI,CAAC,GAAG,UAAU,EAAE,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG;AACrE;AACA,KAAK,UAAU,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACxD;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,OAAO,UAAU,CAAC;AACpB;AACA,EAAE;AACF;AACA,CAAC,YAAY,EAAE,GAAG,EAAE,cAAc,GAAG,SAAS,GAAG;AACjD;AACA,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AAC5B,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjC,EAAE,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;AAC/C,EAAE,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,EAAE,cAAc,EAAE,CAAC;AAC1D;AACA,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC;AAC3B;AACA,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AACjC,EAAE,MAAM,IAAI,GAAG,UAAU,GAAG,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC;AACjE,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;AACA,GAAG,MAAM,YAAY,GAAG,eAAe,GAAG,cAAc,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;AAClG;AACA,GAAG,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;AAC3B,GAAG,MAAM,MAAM,GAAG,YAAY,EAAE,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,EAAE,CAAC;AACjE,GAAG,WAAW,EAAE,CAAC;AACjB;AACA,GAAG,KAAK,MAAM,IAAI,IAAI,MAAM,aAAa,IAAI,IAAI,IAAI,MAAM,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,EAAE,GAAG;AAClG;AACA,IAAI,aAAa,GAAG,MAAM,CAAC;AAC3B,IAAI,KAAK,eAAe,GAAG;AAC3B;AACA,KAAK,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,aAAa,CAAC;AAC3D;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,OAAO,aAAa,CAAC;AACvB;AACA,EAAE;AACF;AACA,CAAC,kBAAkB,EAAE,aAAa,EAAE,UAAU,GAAG;AACjD;AACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjC,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC;AACrB,EAAE,MAAM,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG;AACpC;AACA,GAAG,SAAS,EAAE,IAAI,EAAE,CAAC;AACrB,GAAG,MAAM,GAAG,kBAAkB,EAAE,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;AACzE,GAAG,WAAW,EAAE,CAAC;AACjB;AACA,GAAG,KAAK,MAAM,GAAG;AACjB;AACA,IAAI,MAAM;AACV;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,OAAO,MAAM,CAAC;AAChB;AACA,EAAE;AACF;AACA,CAAC,SAAS,EAAE,SAAS,EAAE,uBAAuB,EAAE,eAAe,GAAG;AAClE;AACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjC,EAAE,KAAK,SAAS,YAAY,QAAQ,GAAG;AACvC;AACA,GAAG,KAAK,uBAAuB,GAAG;AAClC;AACA;AACA;AACA,IAAI,MAAM,oBAAoB,GAAG,uBAAuB,CAAC;AACzD,IAAI,uBAAuB,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,MAAM;AAClE;AACA,KAAK,MAAM,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC;AAC1B,KAAK,OAAO,oBAAoB,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;AAC9E;AACA,KAAK,CAAC;AACN;AACA;AACA,IAAI;AACJ;AACA,GAAG,SAAS,GAAG;AACf;AACA,IAAI,mBAAmB,EAAE,eAAe;AACxC,IAAI,gBAAgB,EAAE,SAAS;AAC/B,IAAI,kBAAkB,EAAE,uBAAuB;AAC/C,IAAI,eAAe,EAAE,IAAI;AACzB;AACA,IAAI,CAAC;AACL;AACA,GAAG,OAAO,CAAC,IAAI,EAAE,0IAA0I,EAAE,CAAC;AAC9J;AACA,GAAG;AACH;AACA,EAAE,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;AAC/C,EAAE,IAAI;AACN,GAAG,mBAAmB;AACtB,GAAG,gBAAgB;AACnB,GAAG,eAAe;AAClB,GAAG,kBAAkB;AACrB,GAAG,GAAG,SAAS,CAAC;AAChB;AACA,EAAE,KAAK,eAAe,IAAI,kBAAkB,GAAG;AAC/C;AACA,GAAG,MAAM,uBAAuB,GAAG,eAAe,CAAC;AACnD,GAAG,eAAe,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,MAAM;AACvE;AACA,IAAI,KAAK,EAAE,uBAAuB,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG;AACnF;AACA,KAAK,OAAO,oBAAoB,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,kBAAkB,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;AAC5G;AACA,KAAK;AACL;AACA,IAAI,OAAO,IAAI,CAAC;AAChB;AACA,IAAI,CAAC;AACL;AACA,GAAG,MAAM,KAAK,EAAE,eAAe,GAAG;AAClC;AACA,GAAG,KAAK,kBAAkB,GAAG;AAC7B;AACA,IAAI,eAAe,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,MAAM;AAC7D;AACA,KAAK,OAAO,oBAAoB,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,kBAAkB,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;AAC5G;AACA,KAAK,CAAC;AACN;AACA,IAAI,MAAM;AACV;AACA,IAAI,eAAe,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,MAAM;AACtD;AACA,KAAK,OAAO,SAAS,CAAC;AACtB;AACA,KAAK,CAAC;AACN;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC;AACrB,EAAE,IAAI,UAAU,GAAG,CAAC,CAAC;AACrB,EAAE,MAAM,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG;AACpC;AACA,GAAG,SAAS,EAAE,IAAI,EAAE,CAAC;AACrB,GAAG,MAAM,GAAG,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,EAAE,UAAU,EAAE,CAAC;AACzG,GAAG,WAAW,EAAE,CAAC;AACjB;AACA,GAAG,KAAK,MAAM,GAAG;AACjB;AACA,IAAI,MAAM;AACV;AACA,IAAI;AACJ;AACA,GAAG,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC;AACjC;AACA,GAAG;AACH;AACA,EAAE,YAAY,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC;AAC5C;AACA,EAAE,OAAO,MAAM,CAAC;AAChB;AACA,EAAE;AACF;AACA,CAAC,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,GAAG;AAC/C;AACA;AACA;AACA;AACA,EAAE,IAAI;AACN,GAAG,gBAAgB;AACnB,GAAG,mBAAmB;AACtB,GAAG,GAAG,SAAS,CAAC;AAChB;AACA,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;AACxC,EAAE,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;AACzD;AACA,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;AACjD,EAAE,MAAM,iBAAiB,GAAG,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;AAClE;AACA,EAAE,UAAU,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC;AAC5C;AACA,EAAE,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;AAC/C,EAAE,MAAM,SAAS,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;AAChD;AACA,EAAE,KAAK,mBAAmB,GAAG;AAC7B;AACA,GAAG,SAAS,0BAA0B,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG;AAC3G;AACA,IAAI,MAAM,IAAI,EAAE,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,GAAG,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG;AACpE;AACA,KAAK,WAAW,EAAE,SAAS,EAAE,EAAE,GAAG,CAAC,EAAE,cAAc,EAAE,iBAAiB,EAAE,CAAC;AACzE,KAAK,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;AAC/C,KAAK,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;AAC/C,KAAK,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;AAC/C,KAAK,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;AAClC;AACA,KAAK,MAAM,IAAI,EAAE,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,GAAG,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG;AACrE;AACA,MAAM,WAAW,EAAE,QAAQ,EAAE,EAAE,GAAG,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC;AAC/D,MAAM,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AAClC;AACA,MAAM,KAAK,mBAAmB,EAAE,QAAQ,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG;AAChG;AACA,OAAO,OAAO,IAAI,CAAC;AACnB;AACA,OAAO;AACP;AACA,MAAM;AACN;AACA,KAAK;AACL;AACA,IAAI,OAAO,KAAK,CAAC;AACjB;AACA,IAAI;AACJ;AACA,GAAG,KAAK,gBAAgB,GAAG;AAC3B;AACA,IAAI,MAAM,wBAAwB,GAAG,gBAAgB,CAAC;AACtD,IAAI,gBAAgB,GAAG,WAAW,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG;AACrG;AACA,KAAK,KAAK,EAAE,wBAAwB,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG;AAC3G;AACA,MAAM,OAAO,0BAA0B,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC5G;AACA,MAAM;AACN;AACA,KAAK,OAAO,IAAI,CAAC;AACjB;AACA,KAAK,CAAC;AACN;AACA,IAAI,MAAM;AACV;AACA,IAAI,gBAAgB,GAAG,0BAA0B,CAAC;AAClD;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,QAAQ,CAAC,cAAc,EAAE,KAAK,EAAE,CAAC;AACnC,EAAE,KAAK,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;AACtC,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE;AACjC;AACA,GAAG,gBAAgB,EAAE,GAAG,IAAI,KAAK,CAAC,aAAa,EAAE,GAAG,EAAE;AACtD;AACA,GAAG,eAAe,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM;AAC/E;AACA,IAAI,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AACrB,IAAI,IAAI,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;AACpC,IAAI,OAAO,QAAQ,CAAC,SAAS,EAAE;AAC/B;AACA,KAAK,gBAAgB,EAAE,GAAG,IAAI,IAAI,CAAC,aAAa,EAAE,GAAG,EAAE;AACvD;AACA,KAAK,eAAe,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,MAAM;AAC5E;AACA,MAAM,OAAO,gBAAgB,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;AAC1G;AACA,MAAM;AACN;AACA,KAAK,EAAE,CAAC;AACR;AACA,IAAI;AACJ;AACA,GAAG,EAAE,CAAC;AACN;AACA,EAAE,YAAY,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC;AAC5C,EAAE,YAAY,CAAC,gBAAgB,EAAE,SAAS,EAAE,CAAC;AAC7C,EAAE,OAAO,MAAM,CAAC;AAChB;AACA,EAAE;AACF;AACA;AACA,CAAC,aAAa,EAAE,GAAG,EAAE,SAAS,GAAG;AACjC;AACA,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,SAAS,EAAE,CAAC;AACzC,EAAE,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC;AACzB;AACA,EAAE,OAAO,IAAI,CAAC,SAAS;AACvB,GAAG;AACH,IAAI,gBAAgB,EAAE,GAAG,IAAI,GAAG,CAAC,aAAa,EAAE,GAAG,EAAE;AACrD,IAAI,kBAAkB,EAAE,GAAG,IAAI,GAAG,CAAC,kBAAkB,EAAE,GAAG,EAAE;AAC5D,IAAI;AACJ,GAAG,CAAC;AACJ;AACA,EAAE;AACF;AACA,CAAC,gBAAgB,EAAE,MAAM,GAAG;AAC5B;AACA,EAAE,OAAO,IAAI,CAAC,SAAS;AACvB,GAAG;AACH,IAAI,gBAAgB,EAAE,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,GAAG,EAAE;AACxD,IAAI,kBAAkB,EAAE,GAAG,IAAI,GAAG,CAAC,gBAAgB,EAAE,MAAM,EAAE;AAC7D,IAAI;AACJ,GAAG,CAAC;AACJ;AACA,EAAE;AACF;AACA,CAAC,sBAAsB,EAAE,aAAa,EAAE,aAAa,EAAE,OAAO,GAAG,GAAG,EAAE,OAAO,GAAG,GAAG,EAAE,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,QAAQ,GAAG;AACjI;AACA,EAAE,KAAK,EAAE,aAAa,CAAC,WAAW,GAAG;AACrC;AACA,GAAG,aAAa,CAAC,kBAAkB,EAAE,CAAC;AACtC;AACA,GAAG;AACH;AACA,EAAE,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,aAAa,EAAE,CAAC;AACzF,EAAE,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC;AACzB;AACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjC,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;AAC3C,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AAC/B,EAAE,MAAM,QAAQ,GAAG,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC;AACrD,EAAE,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC;AACzC,EAAE,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;AAC/C,EAAE,MAAM,SAAS,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;AAChD;AACA,EAAE,IAAI,WAAW,GAAG,KAAK,CAAC;AAC1B,EAAE,IAAI,eAAe,GAAG,KAAK,CAAC;AAC9B,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC;AACzB,EAAE,IAAI,eAAe,GAAG,IAAI,CAAC;AAC7B;AACA,EAAE,KAAK,OAAO,GAAG;AACjB;AACA,GAAG,WAAW,GAAG,KAAK,CAAC;AACvB,GAAG,eAAe,GAAG,KAAK,CAAC;AAC3B;AACA,GAAG;AACH;AACA,EAAE,IAAI,eAAe,GAAG,QAAQ,CAAC;AACjC,EAAE,IAAI,uBAAuB,GAAG,IAAI,CAAC;AACrC,EAAE,IAAI,4BAA4B,GAAG,IAAI,CAAC;AAC1C,EAAE,UAAU,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC;AAC5C,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC;AACjC,EAAE,IAAI,CAAC,SAAS;AAChB,GAAG;AACH;AACA,IAAI,mBAAmB,EAAE,GAAG,IAAI;AAChC;AACA,KAAK,OAAO,GAAG,CAAC,aAAa,EAAE,GAAG,EAAE,CAAC;AACrC;AACA,KAAK;AACL;AACA,IAAI,gBAAgB,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,MAAM;AAChD;AACA,KAAK,KAAK,KAAK,GAAG,eAAe,IAAI,KAAK,GAAG,YAAY,GAAG;AAC5D;AACA;AACA;AACA,MAAM,KAAK,MAAM,GAAG;AACpB;AACA,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAChC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAChC,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC/B;AACA,OAAO;AACP;AACA,MAAM,OAAO,IAAI,CAAC;AAClB;AACA,MAAM;AACN;AACA,KAAK,OAAO,KAAK,CAAC;AAClB;AACA,KAAK;AACL;AACA,IAAI,eAAe,EAAE,EAAE,MAAM,EAAE,KAAK,MAAM;AAC1C;AACA,KAAK,KAAK,aAAa,CAAC,UAAU,GAAG;AACrC;AACA;AACA;AACA,MAAM,OAAO,aAAa,CAAC,UAAU,CAAC,SAAS,EAAE;AACjD,OAAO,mBAAmB,EAAE,GAAG,IAAI;AACnC;AACA,QAAQ,OAAO,IAAI,CAAC,aAAa,EAAE,GAAG,EAAE,CAAC;AACzC;AACA,QAAQ;AACR;AACA,OAAO,gBAAgB,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,MAAM;AACnD;AACA,QAAQ,OAAO,KAAK,GAAG,eAAe,IAAI,KAAK,GAAG,YAAY,CAAC;AAC/D;AACA,QAAQ;AACR;AACA,OAAO,eAAe,EAAE,EAAE,WAAW,EAAE,UAAU,MAAM;AACvD;AACA,QAAQ,MAAM,IAAI,EAAE,GAAG,WAAW,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,WAAW,GAAG,UAAU,KAAK,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG;AAClG;AACA,SAAS,WAAW,EAAE,SAAS,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC;AAC5D,SAAS,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;AACnD,SAAS,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;AACnD,SAAS,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;AACnD,SAAS,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;AACtC;AACA,SAAS,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AAC/E;AACA,UAAU,WAAW,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;AACjD,UAAU,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AACtC;AACA,UAAU,MAAM,IAAI,GAAG,QAAQ,CAAC,kBAAkB,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAC1F,UAAU,KAAK,IAAI,GAAG,eAAe,GAAG;AACxC;AACA,WAAW,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAC/C;AACA,WAAW,KAAK,eAAe,GAAG;AAClC;AACA,YAAY,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAChD;AACA,YAAY;AACZ;AACA,WAAW,eAAe,GAAG,IAAI,CAAC;AAClC,WAAW,uBAAuB,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3C,WAAW,4BAA4B,GAAG,EAAE,GAAG,CAAC,CAAC;AACjD;AACA,WAAW;AACX;AACA;AACA,UAAU,KAAK,IAAI,GAAG,YAAY,GAAG;AACrC;AACA,WAAW,OAAO,IAAI,CAAC;AACvB;AACA,WAAW;AACX;AACA,UAAU;AACV;AACA,SAAS;AACT;AACA,QAAQ;AACR,OAAO,EAAE,CAAC;AACV;AACA,MAAM,MAAM;AACZ;AACA;AACA,MAAM,MAAM,QAAQ,GAAG,UAAU,GAAG,UAAU,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AACtE,MAAM,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,QAAQ,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG;AAC1D;AACA,OAAO,WAAW,EAAE,SAAS,EAAE,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC;AAC1D,OAAO,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;AACjD,OAAO,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;AACjD,OAAO,SAAS,CAAC,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,CAAC;AACjD,OAAO,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;AACpC;AACA,OAAO,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AAC7E;AACA,QAAQ,WAAW,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;AAC/C,QAAQ,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;AACpC;AACA,QAAQ,MAAM,IAAI,GAAG,QAAQ,CAAC,kBAAkB,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACxF,QAAQ,KAAK,IAAI,GAAG,eAAe,GAAG;AACtC;AACA,SAAS,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAC7C;AACA,SAAS,KAAK,eAAe,GAAG;AAChC;AACA,UAAU,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;AAC9C;AACA,UAAU;AACV;AACA,SAAS,eAAe,GAAG,IAAI,CAAC;AAChC,SAAS,uBAAuB,GAAG,CAAC,GAAG,CAAC,CAAC;AACzC,SAAS,4BAA4B,GAAG,EAAE,GAAG,CAAC,CAAC;AAC/C;AACA,SAAS;AACT;AACA;AACA,QAAQ,KAAK,IAAI,GAAG,YAAY,GAAG;AACnC;AACA,SAAS,OAAO,IAAI,CAAC;AACrB;AACA,SAAS;AACT;AACA,QAAQ;AACR;AACA,OAAO;AACP;AACA,MAAM;AACN;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG,CAAC;AACJ;AACA,EAAE,YAAY,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC;AAC5C,EAAE,YAAY,CAAC,gBAAgB,EAAE,SAAS,EAAE,CAAC;AAC7C;AACA,EAAE,KAAK,eAAe,KAAK,QAAQ,GAAG,OAAO,IAAI,CAAC;AAClD;AACA,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC;AACjE,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC;AAC7C,EAAE,OAAO,CAAC,QAAQ,GAAG,eAAe;AACpC,EAAE,OAAO,CAAC,SAAS,GAAG,uBAAuB,CAAC;AAC9C;AACA,EAAE,KAAK,OAAO,GAAG;AACjB;AACA,GAAG,KAAK,EAAE,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC;AAClE,QAAQ,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC;AAC9C,GAAG,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;AAC5C,GAAG,eAAe,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;AAC9C,GAAG,OAAO,CAAC,QAAQ,GAAG,eAAe,CAAC,GAAG,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;AACpE,GAAG,OAAO,CAAC,SAAS,GAAG,4BAA4B,CAAC;AACpD;AACA,GAAG;AACH;AACA,EAAE,OAAO,OAAO,CAAC;AACjB;AACA,EAAE;AACF;AACA,CAAC,mBAAmB,EAAE,KAAK,EAAE,MAAM,GAAG,GAAG,EAAE,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,QAAQ,GAAG;AACvF;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,cAAc,GAAG,YAAY,GAAG,YAAY,CAAC;AACrD,EAAE,MAAM,cAAc,GAAG,YAAY,GAAG,YAAY,CAAC;AACrD,EAAE,IAAI,iBAAiB,GAAG,QAAQ,CAAC;AACnC,EAAE,IAAI,uBAAuB,GAAG,IAAI,CAAC;AACrC,EAAE,IAAI,CAAC,SAAS;AAChB;AACA,GAAG;AACH;AACA,IAAI,mBAAmB,EAAE,GAAG,IAAI;AAChC;AACA,KAAK,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAClD,KAAK,OAAO,IAAI,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC;AAC5C;AACA,KAAK;AACL;AACA,IAAI,gBAAgB,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,MAAM;AAChD;AACA,KAAK,OAAO,KAAK,GAAG,iBAAiB,IAAI,KAAK,GAAG,cAAc,CAAC;AAChE;AACA,KAAK;AACL;AACA,IAAI,kBAAkB,EAAE,EAAE,GAAG,EAAE,QAAQ,MAAM;AAC7C;AACA,KAAK,GAAG,CAAC,mBAAmB,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;AAC5C,KAAK,MAAM,MAAM,GAAG,KAAK,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC;AACpD,KAAK,KAAK,MAAM,GAAG,iBAAiB,GAAG;AACvC;AACA,MAAM,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AACzB,MAAM,iBAAiB,GAAG,MAAM,CAAC;AACjC,MAAM,uBAAuB,GAAG,QAAQ,CAAC;AACzC;AACA,MAAM;AACN;AACA,KAAK,KAAK,MAAM,GAAG,cAAc,GAAG;AACpC;AACA,MAAM,OAAO,IAAI,CAAC;AAClB;AACA,MAAM,MAAM;AACZ;AACA,MAAM,OAAO,KAAK,CAAC;AACnB;AACA,MAAM;AACN;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG,CAAC;AACJ;AACA,EAAE,KAAK,iBAAiB,KAAK,QAAQ,GAAG,OAAO,IAAI,CAAC;AACpD;AACA,EAAE,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,CAAC;AACzD;AACA,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AACrD,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AAClC,EAAE,MAAM,CAAC,QAAQ,GAAG,eAAe;AACnC,EAAE,MAAM,CAAC,SAAS,GAAG,uBAAuB,CAAC;AAC7C;AACA,EAAE,OAAO,MAAM,CAAC;AAChB;AACA,EAAE;AACF;AACA,CAAC,cAAc,EAAE,MAAM,GAAG;AAC1B;AACA,EAAE,MAAM,CAAC,SAAS,EAAE,CAAC;AACrB;AACA,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AAC5B,EAAE,KAAK,CAAC,OAAO,EAAE,MAAM,IAAI;AAC3B;AACA,GAAG,UAAU,EAAE,CAAC,EAAE,IAAI,YAAY,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,CAAC;AACxD,GAAG,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC;AAC3B;AACA,GAAG,EAAE,CAAC;AACN;AACA,EAAE,OAAO,MAAM,CAAC;AAChB;AACA,EAAE;AACF;AACA;;ACz+BA,MAAM,WAAW,mBAAmB,IAAI,IAAI,EAAE,CAAC;AAC/C,MAAM,qBAAqB,SAAS,QAAQ,CAAC;AAC7C;AACA,CAAC,IAAI,MAAM,GAAG;AACd;AACA,EAAE,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC;AAC7B;AACA,EAAE;AACF;AACA,CAAC,IAAI,cAAc,GAAG;AACtB;AACA,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC;AAC3B;AACA,EAAE;AACF;AACA,CAAC,IAAI,MAAM,GAAG;AACd;AACA,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC;AAC3B;AACA,EAAE;AACF;AACA,CAAC,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,GAAG,EAAE,EAAE,KAAK,GAAG,CAAC,GAAG;AACtD;AACA,EAAE,KAAK,EAAE,CAAC;AACV;AACA,EAAE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC3B,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAC;AACvC,EAAE,IAAI,CAAC,IAAI,GAAG,uBAAuB,CAAC;AACtC,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACrB,EAAE,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC9B,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACnB,EAAE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC3B,EAAE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACtB;AACA,EAAE;AACF;AACA,CAAC,OAAO,GAAG,EAAE;AACb;AACA,CAAC,MAAM,GAAG;AACV;AACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjC,EAAE,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;AACnD,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AAC5B,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC;AACrB,EAAE,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACvB,EAAE,KAAK,UAAU,GAAG;AACpB;AACA;AACA,GAAG,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACtC,GAAG,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;AAC9C,GAAG,IAAI,WAAW,GAAG,CAAC,CAAC;AACvB,GAAG,UAAU,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,MAAM;AAC7C;AACA,IAAI,KAAK,KAAK,KAAK,WAAW,IAAI,MAAM,GAAG;AAC3C;AACA,KAAK,WAAW,GAAG,CAAC;AACpB,KAAK,OAAO,IAAI,CAAC;AACjB;AACA,KAAK,MAAM,KAAK,cAAc,GAAG;AACjC;AACA,KAAK,WAAW,GAAG,CAAC;AACpB;AACA,KAAK;AACL;AACA,IAAI,EAAE,KAAK,EAAE,CAAC;AACd;AACA;AACA,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAC;AACpB,GAAG,MAAM,aAAa,GAAG,IAAI,YAAY,EAAE,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC;AACjE,GAAG,UAAU,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,MAAM;AAC3D;AACA,IAAI,MAAM,SAAS,GAAG,KAAK,KAAK,WAAW,IAAI,MAAM,CAAC;AACtD,IAAI,KAAK,SAAS,IAAI,cAAc,GAAG;AACvC;AACA,KAAK,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,WAAW,EAAE,CAAC;AAChD;AACA,KAAK,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,WAAW,CAAC;AACtC,KAAK,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AACzC;AACA,MAAM,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AACzC,MAAM,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AAC1C;AACA,OAAO,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAC1C,OAAO,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AAC3C;AACA,QAAQ,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAC3C,QAAQ,aAAa,EAAE,QAAQ,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC7C,QAAQ,aAAa,EAAE,QAAQ,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC7C,QAAQ,aAAa,EAAE,QAAQ,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;AAC7C;AACA,QAAQ,QAAQ,IAAI,CAAC,CAAC;AACtB;AACA,QAAQ;AACR;AACA,OAAO;AACP;AACA,MAAM;AACN;AACA,KAAK,OAAO,SAAS,CAAC;AACtB;AACA,KAAK;AACL;AACA,IAAI,EAAE,KAAK,EAAE,CAAC;AACd;AACA,GAAG,IAAI,UAAU,CAAC;AAClB,GAAG,IAAI,OAAO,CAAC;AACf,GAAG,KAAK,IAAI,CAAC,YAAY,GAAG;AAC5B;AACA;AACA,IAAI,OAAO,GAAG,IAAI,UAAU,EAAE;AAC9B;AACA,KAAK,CAAC,EAAE,CAAC;AACT,KAAK,CAAC,EAAE,CAAC;AACT,KAAK,CAAC,EAAE,CAAC;AACT,KAAK,CAAC,EAAE,CAAC;AACT;AACA;AACA,KAAK,CAAC,EAAE,CAAC;AACT,KAAK,CAAC,EAAE,CAAC;AACT,KAAK,CAAC,EAAE,CAAC;AACT,KAAK,CAAC,EAAE,CAAC;AACT;AACA;AACA,KAAK,CAAC,EAAE,CAAC;AACT,KAAK,CAAC,EAAE,CAAC;AACT,KAAK,CAAC,EAAE,CAAC;AACT,KAAK,CAAC,EAAE,CAAC;AACT,KAAK,EAAE,CAAC;AACR;AACA,IAAI,MAAM;AACV;AACA,IAAI,OAAO,GAAG,IAAI,UAAU,EAAE;AAC9B;AACA;AACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;AACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;AACA;AACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;AACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;AACA;AACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;AACA,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACZ;AACA,KAAK,EAAE,CAAC;AACR;AACA,IAAI;AACJ;AACA,GAAG,KAAK,aAAa,CAAC,MAAM,GAAG,KAAK,GAAG;AACvC;AACA,IAAI,UAAU,GAAG,IAAI,WAAW,EAAE,OAAO,CAAC,MAAM,GAAG,WAAW,EAAE,CAAC;AACjE;AACA,IAAI,MAAM;AACV;AACA,IAAI,UAAU,GAAG,IAAI,WAAW,EAAE,OAAO,CAAC,MAAM,GAAG,WAAW,EAAE,CAAC;AACjE;AACA,IAAI;AACJ;AACA,GAAG,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC;AACtC,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,GAAG;AAC5C;AACA,IAAI,MAAM,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5B,IAAI,MAAM,WAAW,GAAG,CAAC,GAAG,WAAW,CAAC;AACxC,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,GAAG;AAC7C;AACA,KAAK,UAAU,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,SAAS,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC;AAC9D;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA;AACA,GAAG,QAAQ,CAAC,QAAQ;AACpB,IAAI,IAAI,eAAe,EAAE,UAAU,EAAE,CAAC,EAAE,KAAK,EAAE;AAC/C,IAAI,CAAC;AACL,GAAG,QAAQ,CAAC,YAAY;AACxB,IAAI,UAAU;AACd,IAAI,IAAI,eAAe,EAAE,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE;AAClD,IAAI,CAAC;AACL,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACvB;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC;AACD;AACA,MAAM,iBAAiB,SAAS,KAAK,CAAC;AACtC;AACA,CAAC,IAAI,KAAK,GAAG;AACb;AACA,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;AACjC;AACA,EAAE;AACF;AACA,CAAC,IAAI,OAAO,GAAG;AACf;AACA,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;AACnC;AACA,EAAE;AACF;AACA,CAAC,IAAI,OAAO,EAAE,CAAC,GAAG;AAClB;AACA,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,CAAC;AAChC,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,CAAC;AAChC;AACA,EAAE;AACF;AACA,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE,GAAG;AACjC;AACA,EAAE,KAAK,EAAE,CAAC;AACV;AACA,EAAE,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC;AAClC,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACrB,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACnB,EAAE,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC9B,EAAE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC3B,EAAE,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AACnB;AACA,EAAE,MAAM,YAAY,GAAG,IAAI,iBAAiB,EAAE;AAC9C,GAAG,KAAK,EAAE,QAAQ;AAClB,GAAG,WAAW,EAAE,IAAI;AACpB,GAAG,OAAO,EAAE,GAAG;AACf,GAAG,UAAU,EAAE,KAAK;AACpB,GAAG,EAAE,CAAC;AACN;AACA,EAAE,MAAM,YAAY,GAAG,IAAI,iBAAiB,EAAE;AAC9C,GAAG,KAAK,EAAE,QAAQ;AAClB,GAAG,WAAW,EAAE,IAAI;AACpB,GAAG,OAAO,EAAE,GAAG;AACf,GAAG,UAAU,EAAE,KAAK;AACpB,GAAG,EAAE,CAAC;AACN;AACA,EAAE,YAAY,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;AAC1C;AACA,EAAE,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACnC,EAAE,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACnC;AACA,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;AAChB;AACA,EAAE;AACF;AACA,CAAC,MAAM,GAAG;AACV;AACA,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;AAC5C,EAAE,MAAM,UAAU,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AACjD,EAAE,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,GAAG;AAC5C;AACA,GAAG,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;AAClC,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;AAC3B,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC;AACvB;AACA,GAAG;AACH;AACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,GAAG;AAC1C;AACA,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;AAClC;AACA,IAAI,MAAM,IAAI,GAAG,IAAI,qBAAqB,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;AAC1F,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AAC7B;AACA,IAAI;AACJ;AACA,GAAG,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC;AACjC,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC3B,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACzB,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;AAC7C,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;AACzC,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;AAC7E,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACjB;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC,iBAAiB,EAAE,GAAG,IAAI,GAAG;AAC9B;AACA,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC3C,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC3C,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;AACrC;AACA,EAAE,KAAK,CAAC,iBAAiB,EAAE,GAAG,IAAI,EAAE,CAAC;AACrC;AACA,EAAE;AACF;AACA,CAAC,IAAI,EAAE,MAAM,GAAG;AAChB;AACA,EAAE,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AAC5B,EAAE,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;AAC1B;AACA,EAAE;AACF;AACA,CAAC,KAAK,GAAG;AACT;AACA,EAAE,OAAO,IAAI,iBAAiB,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AACxD;AACA,EAAE;AACF;AACA,CAAC,OAAO,GAAG;AACX;AACA,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;AAC9B,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;AAC9B;AACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjC,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACtD;AACA,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;AACpC;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA;;AClUA,MAAM,KAAK,mBAAmB,IAAI,IAAI,EAAE,CAAC;AACzC,MAAM,KAAK,mBAAmB,IAAI,IAAI,EAAE,CAAC;AACzC,MAAM,IAAI,mBAAmB,IAAI,OAAO,EAAE,CAAC;AAC3C;AACA;AACA,SAAS,gBAAgB,EAAE,EAAE,GAAG;AAChC;AACA,CAAC,SAAS,OAAO,EAAE;AACnB;AACA,EAAE,KAAK,QAAQ;AACf,GAAG,OAAO,CAAC,CAAC;AACZ,EAAE,KAAK,QAAQ;AACf,GAAG,OAAO,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;AACxB,EAAE,KAAK,SAAS;AAChB,GAAG,OAAO,CAAC,CAAC;AACZ,EAAE;AACF,GAAG,OAAO,CAAC,CAAC;AACZ;AACA,EAAE;AACF;AACA,CAAC;AACD;AACA,SAAS,YAAY,EAAE,GAAG,GAAG;AAC7B;AACA,CAAC,MAAM,KAAK,GAAG,gCAAgC,CAAC;AAChD,CAAC,OAAO,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;AAC3C;AACA,CAAC;AACD;AACA,SAAS,eAAe,EAAE,GAAG,EAAE,KAAK,GAAG;AACvC;AACA,CAAC,MAAM,MAAM,GAAG;AAChB,EAAE,SAAS,EAAE,CAAC;AACd,EAAE,aAAa,EAAE,CAAC;AAClB;AACA,EAAE,KAAK,EAAE;AACT,GAAG,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE,QAAQ;AACjC,GAAG;AACH,EAAE,IAAI,EAAE;AACR,GAAG,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE,QAAQ;AACjC,GAAG;AACH,EAAE,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACrB,EAAE,gBAAgB,EAAE,CAAC;AACrB,EAAE,CAAC;AACH;AACA,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,KAAK,MAAM;AACxE;AACA,EAAE,MAAM,EAAE,GAAG,YAAY,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;AACvD,EAAE,MAAM,EAAE,GAAG,YAAY,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;AACvD,EAAE,MAAM,EAAE,GAAG,YAAY,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,CAAC,EAAE,CAAC;AACvD;AACA,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AAC1D;AACA,EAAE,MAAM,CAAC,SAAS,GAAG,CAAC;AACtB,EAAE,KAAK,MAAM,GAAG;AAChB;AACA,GAAG,MAAM,CAAC,aAAa,GAAG,CAAC;AAC3B;AACA,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AAC1D,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AAC1D;AACA,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AACxD,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AACxD;AACA,GAAG,MAAM,CAAC,gBAAgB,IAAI,WAAW,GAAG,uBAAuB,GAAG,KAAK,CAAC;AAC5E;AACA,GAAG,MAAM;AACT;AACA,GAAG,MAAM,CAAC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAC;AACrC;AACA,GAAG,MAAM,CAAC,gBAAgB,IAAI,WAAW,GAAG,cAAc,CAAC;AAC3D;AACA,GAAG;AACH;AACA,EAAE,EAAE,KAAK,EAAE,CAAC;AACZ;AACA;AACA,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,QAAQ,GAAG;AACrC;AACA,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACtB,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACtB;AACA,EAAE;AACF;AACA,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,QAAQ,GAAG;AACtC;AACA,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AACvB,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AACvB;AACA,EAAE;AACF;AACA,CAAC,OAAO,MAAM,CAAC;AACf;AACA,CAAC;AACD;AACA,SAAS,cAAc,EAAE,GAAG,GAAG;AAC/B;AACA,CAAC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC,MAAM,eAAe,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;AACnE;AACA,CAAC;AACD;AACA,SAAS,qBAAqB,EAAE,GAAG,GAAG;AACtC;AACA,CAAC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;AAC7B,CAAC,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;AACvB,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC;AACf;AACA,CAAC,QAAQ,KAAK,CAAC,MAAM,GAAG;AACxB;AACA,EAAE,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;AAC3B,EAAE,KAAK,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG;AAC/B;AACA,GAAG,SAAS;AACZ;AACA,GAAG;AACH;AACA,EAAE,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;AACxB;AACA,EAAE,MAAM,IAAI,GAAG,IAAI,IAAI,GAAG;AAC1B;AACA,GAAG,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,GAAG,EAAE,GAAG;AACvC;AACA,IAAI,SAAS;AACb;AACA,IAAI;AACJ;AACA,GAAG,KAAK,IAAI,gBAAgB,EAAE,GAAG,EAAE,CAAC;AACpC;AACA,GAAG,MAAM,KAAK,GAAG,IAAI,EAAE,GAAG,EAAE,CAAC;AAC7B,GAAG,KAAK,KAAK,MAAM,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,GAAG;AAChF;AACA,IAAI,KAAK,YAAY,EAAE,KAAK,EAAE,GAAG;AACjC;AACA,KAAK,KAAK,IAAI,KAAK,CAAC,UAAU,CAAC;AAC/B;AACA,KAAK,MAAM,KAAK,KAAK,YAAY,WAAW,GAAG;AAC/C;AACA,KAAK,KAAK,IAAI,KAAK,CAAC,UAAU,CAAC;AAC/B;AACA,KAAK,MAAM;AACX;AACA,KAAK,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACzB;AACA,KAAK;AACL;AACA,IAAI,MAAM;AACV;AACA,IAAI,KAAK,IAAI,gBAAgB,EAAE,KAAK,EAAE,CAAC;AACvC;AACA,IAAI;AACJ;AACA;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC,OAAO,KAAK,CAAC;AACd;AACA,CAAC;AACD;AACA,SAAS,cAAc,EAAE,GAAG,GAAG;AAC/B;AACA,CAAC,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;AAC/B,CAAC,MAAM,UAAU,GAAG,EAAE,CAAC;AACvB,CAAC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AAC9B,CAAC,MAAM,QAAQ,GAAG,QAAQ,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;AACtD,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC;AACnB;AACA,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,MAAM;AACjE;AACA,EAAE,MAAM,IAAI,GAAG;AACf,GAAG,KAAK;AACR,GAAG,MAAM;AACT,GAAG,YAAY;AACf,GAAG,MAAM;AACT,GAAG,KAAK;AACR,GAAG,CAAC;AACJ,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;AAC7B;AACA,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;AACvC,EAAE,MAAM,MAAM,GAAG,UAAU,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;AACzC;AACA,EAAE,KAAK,MAAM,GAAG;AAChB;AACA;AACA,GAAG,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;AACzE;AACA,IAAI,MAAM,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAC/B,IAAI,MAAM,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AACnC,IAAI,MAAM,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AACnC;AACA,IAAI,IAAI,WAAW,CAAC;AACpB;AACA,IAAI,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;AAC7C,IAAI,WAAW,GAAG,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC;AAC9C;AACA,IAAI,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;AAC7C,IAAI,WAAW,GAAG,WAAW,IAAI,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC;AAC7D;AACA,IAAI,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;AAC7C,IAAI,WAAW,GAAG,WAAW,IAAI,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC;AAC7D;AACA,IAAI,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,8CAA8C,EAAE,CAAC;AAClF,IAAI,MAAM,GAAG,MAAM,IAAI,WAAW,CAAC;AACnC;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,KAAK,MAAM,GAAG;AAChB;AACA;AACA,GAAG,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;AACxC;AACA,GAAG,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC;AAClD,GAAG,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,6CAA6C,EAAE,CAAC;AAChF,GAAG,MAAM,GAAG,MAAM,IAAI,WAAW,CAAC;AAClC;AACA,GAAG;AACH;AACA,EAAE,EAAE,CAAC;AACL;AACA,CAAC,OAAO,MAAM,CAAC;AACf;AACA,CAAC;AACD;AACA;AACA,SAAS,gBAAgB,EAAE,GAAG,GAAG;AACjC;AACA,CAAC,MAAM,UAAU,GAAG,EAAE,CAAC;AACvB;AACA,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,MAAM;AACjE;AACA,EAAE,MAAM,IAAI,GAAG;AACf,GAAG,MAAM,EAAE,UAAU,EAAE,CAAC,EAAE,YAAY,EAAE,IAAI,IAAI,EAAE,EAAE;AACpD,GAAG,CAAC;AACJ;AACA,EAAE,KAAK,MAAM,GAAG;AAChB;AACA,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACtB,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACxB;AACA,GAAG,MAAM;AACT;AACA,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACpB,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACrB;AACA,GAAG;AACH;AACA,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;AAC7B;AACA;AACA,EAAE,MAAM,MAAM,GAAG,UAAU,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC;AACzC,EAAE,KAAK,MAAM,GAAG;AAChB;AACA,GAAG,KAAK,MAAM,CAAC,IAAI,KAAK,IAAI,GAAG;AAC/B;AACA,IAAI,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;AACvB;AACA,IAAI,MAAM;AACV;AACA,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;AACxB;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,EAAE,CAAC;AACL;AACA,CAAC,OAAO,UAAU,EAAE,CAAC,EAAE,CAAC;AACxB;AACA;;AC/QA,MAAM,GAAG,mBAAmB,IAAI,GAAG,EAAE,CAAC;AACtC,MAAM,gBAAgB,mBAAmB,IAAI,OAAO,EAAE,CAAC;AACvD,MAAM,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AACnD;AACO,SAAS,kBAAkB,EAAE,SAAS,EAAE,UAAU,GAAG;AAC5D;AACA,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG;AACjC;AACA,EAAE,KAAK,IAAI,CAAC,QAAQ,KAAK,SAAS,GAAG,OAAO;AAC5C;AACA,EAAE,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,CAAC;AACrD,EAAE,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,gBAAgB,EAAE,CAAC;AAC7D;AACA,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;AACvC,EAAE,KAAK,SAAS,CAAC,YAAY,KAAK,IAAI,GAAG;AACzC;AACA,GAAG,MAAM,GAAG,GAAG,uBAAuB,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;AAClG,GAAG,KAAK,GAAG,GAAG;AACd;AACA,IAAI,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AAC3B;AACA,IAAI;AACJ;AACA,GAAG,MAAM;AACT;AACA,GAAG,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;AAClD,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACnD;AACA,IAAI,MAAM,GAAG,GAAG,uBAAuB,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;AACtE,IAAI,KAAK,GAAG,GAAG;AACf;AACA,KAAK,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AAC5B;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,MAAM;AACR;AACA,EAAE,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;AAC1D;AACA,EAAE;AACF;AACA,CAAC;AACD;AACO,SAAS,iBAAiB,EAAE,OAAO,GAAG;AAC7C;AACA,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;AAChD,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC;AACxB;AACA,CAAC;AACD;AACO,SAAS,iBAAiB,GAAG;AACpC;AACA,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACxB;AACA;;ACzCA,SAAS,mBAAmB,EAAE,KAAK,GAAG;AACtC;AACA,CAAC,SAAS,KAAK;AACf;AACA,EAAE,KAAK,CAAC,EAAE,OAAO,GAAG,CAAC;AACrB,EAAE,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC;AACtB,EAAE,KAAK,CAAC,EAAE,OAAO,MAAM,CAAC;AACxB,EAAE,KAAK,CAAC,EAAE,OAAO,MAAM,CAAC;AACxB;AACA,EAAE;AACF;AACA,CAAC,MAAM,IAAI,KAAK,EAAE,CAAC;AACnB;AACA,CAAC;AACD;AACA,SAAS,aAAa,EAAE,KAAK,GAAG;AAChC;AACA,CAAC,SAAS,KAAK;AACf;AACA,EAAE,KAAK,CAAC,EAAE,OAAO,SAAS,CAAC;AAC3B,EAAE,KAAK,CAAC,EAAE,OAAO,QAAQ,CAAC;AAC1B,EAAE,KAAK,CAAC,EAAE,OAAO,UAAU,CAAC;AAC5B,EAAE,KAAK,CAAC,EAAE,OAAO,UAAU,CAAC;AAC5B;AACA,EAAE;AACF;AACA,CAAC;AACD;AACA,SAAS,gBAAgB,EAAE,KAAK,GAAG;AACnC;AACA,CAAC,SAAS,KAAK;AACf;AACA,EAAE,KAAK,CAAC,EAAE,OAAO,gBAAgB,CAAC;AAClC,EAAE,KAAK,CAAC,EAAE,OAAO,eAAe,CAAC;AACjC,EAAE,KAAK,CAAC,EAAE,OAAO,iBAAiB,CAAC;AACnC,EAAE,KAAK,CAAC,EAAE,OAAO,iBAAiB,CAAC;AACnC;AACA,EAAE;AACF;AACA,CAAC;AACD;AACO,MAAM,sBAAsB,SAAS,WAAW,CAAC;AACxD;AACA,CAAC,WAAW,GAAG;AACf;AACA,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC;AACjC,EAAE,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC;AACjC,EAAE,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;AAC/B,EAAE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAC/B,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B;AACA,EAAE;AACF;AACA,CAAC,UAAU,EAAE,IAAI,GAAG;AACpB;AACA,EAAE,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACjD,EAAE,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC;AACzC,EAAE,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC;AACnC,EAAE,KAAK,gBAAgB,KAAK,IAAI,GAAG;AACnC;AACA,GAAG,KAAK,EAAE,gBAAgB,GAAG,aAAa,KAAK,gBAAgB,KAAK,GAAG,GAAG;AAC1E;AACA,IAAI,MAAM,IAAI,KAAK,EAAE,iFAAiF,EAAE,CAAC;AACzG;AACA,IAAI;AACJ;AACA,GAAG,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC;AACpC,GAAG,IAAI,CAAC,KAAK,GAAG,aAAa,GAAG,gBAAgB,GAAG,gBAAgB,CAAC;AACpE;AACA,GAAG;AACH;AACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjC,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC3B,EAAE,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AACrC,EAAE,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;AACpD,EAAE,MAAM,SAAS,GAAG,kBAAkB,CAAC,iBAAiB,CAAC;AACzD,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;AACpC,EAAE,IAAI,WAAW,GAAG,QAAQ,CAAC;AAC7B;AACA;AACA,EAAE,KAAK,UAAU,KAAK,IAAI,GAAG;AAC7B;AACA,GAAG,SAAS,kBAAkB;AAC9B;AACA,IAAI,KAAK,YAAY;AACrB,KAAK,UAAU,GAAG,SAAS,CAAC;AAC5B,KAAK,MAAM;AACX;AACA,IAAI,KAAK,UAAU,CAAC;AACpB,IAAI,KAAK,WAAW,CAAC;AACrB,IAAI,KAAK,WAAW;AACpB,KAAK,UAAU,GAAG,eAAe,CAAC;AAClC,KAAK,MAAM;AACX;AACA,IAAI,KAAK,SAAS,CAAC;AACnB,IAAI,KAAK,UAAU,CAAC;AACpB,IAAI,KAAK,UAAU;AACnB,KAAK,UAAU,GAAG,OAAO,CAAC;AAC1B,KAAK,MAAM;AACX;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA;AACA,EAAE,IAAI,IAAI,EAAE,MAAM,EAAE,cAAc,EAAE,gBAAgB,CAAC;AACrD,EAAE,IAAI,cAAc,GAAG,mBAAmB,EAAE,QAAQ,EAAE,CAAC;AACvD,EAAE,SAAS,UAAU;AACrB;AACA,GAAG,KAAK,SAAS;AACjB,IAAI,cAAc,GAAG,GAAG,CAAC;AACzB,IAAI,MAAM,GAAG,aAAa,EAAE,QAAQ,EAAE,CAAC;AACvC;AACA,IAAI,KAAK,UAAU,IAAI,SAAS,KAAK,CAAC,GAAG;AACzC;AACA,KAAK,gBAAgB,GAAG,kBAAkB,CAAC;AAC3C,KAAK,cAAc,IAAI,GAAG,CAAC;AAC3B;AACA,KAAK,KAAK,kBAAkB,KAAK,UAAU,GAAG;AAC9C;AACA,MAAM,IAAI,GAAG,gBAAgB,CAAC;AAC9B;AACA,MAAM,MAAM;AACZ;AACA,MAAM,IAAI,GAAG,QAAQ,CAAC;AACtB,MAAM,cAAc,IAAI,QAAQ,CAAC;AACjC;AACA,MAAM;AACN;AACA,KAAK,MAAM;AACX;AACA,KAAK,gBAAgB,GAAG,YAAY,CAAC;AACrC,KAAK,cAAc,IAAI,KAAK,CAAC;AAC7B,KAAK,IAAI,GAAG,SAAS,CAAC;AACtB;AACA,KAAK;AACL;AACA,IAAI,MAAM;AACV;AACA,GAAG,KAAK,OAAO;AACf,IAAI,cAAc,IAAI,SAAS,GAAG,CAAC,GAAG,GAAG,CAAC;AAC1C,IAAI,cAAc,GAAG,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC;AACpG,IAAI,MAAM,GAAG,gBAAgB,EAAE,QAAQ,EAAE,CAAC;AAC1C;AACA,IAAI,KAAK,SAAS,KAAK,CAAC,GAAG;AAC3B;AACA,KAAK,gBAAgB,GAAG,SAAS,CAAC;AAClC,KAAK,IAAI,GAAG,QAAQ,CAAC;AACrB;AACA,KAAK,MAAM,KAAK,SAAS,KAAK,CAAC,GAAG;AAClC;AACA,KAAK,gBAAgB,GAAG,UAAU,CAAC;AACnC,KAAK,IAAI,GAAG,SAAS,CAAC;AACtB;AACA,KAAK,MAAM;AACX;AACA,KAAK,gBAAgB,GAAG,UAAU,CAAC;AACnC,KAAK,IAAI,GAAG,OAAO,CAAC;AACpB;AACA,KAAK;AACL;AACA,IAAI,MAAM;AACV;AACA,GAAG,KAAK,eAAe;AACvB,IAAI,cAAc,IAAI,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3C,IAAI,cAAc,GAAG,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC;AACpG,IAAI,MAAM,GAAG,gBAAgB,EAAE,QAAQ,EAAE,CAAC;AAC1C;AACA,IAAI,KAAK,SAAS,KAAK,CAAC,GAAG;AAC3B;AACA,KAAK,gBAAgB,GAAG,UAAU,CAAC;AACnC,KAAK,IAAI,GAAG,gBAAgB,CAAC;AAC7B;AACA,KAAK,MAAM,KAAK,SAAS,KAAK,CAAC,GAAG;AAClC;AACA,KAAK,gBAAgB,GAAG,WAAW,CAAC;AACpC,KAAK,IAAI,GAAG,iBAAiB,CAAC;AAC9B;AACA,KAAK,MAAM;AACX;AACA,KAAK,gBAAgB,GAAG,WAAW,CAAC;AACpC,KAAK,IAAI,GAAG,eAAe,CAAC;AAC5B;AACA,KAAK;AACL;AACA,IAAI,MAAM;AACV;AACA,GAAG;AACH;AACA;AACA;AACA,EAAE,KAAK,WAAW,KAAK,CAAC,MAAM,MAAM,KAAK,UAAU,IAAI,MAAM,KAAK,iBAAiB,EAAE,GAAG;AACxF;AACA,GAAG,WAAW,GAAG,CAAC,CAAC;AACnB;AACA,GAAG;AACH;AACA;AACA,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;AACpD,EAAE,MAAM,MAAM,GAAG,WAAW,GAAG,SAAS,GAAG,SAAS,CAAC;AACrD,EAAE,MAAM,SAAS,GAAG,IAAI,gBAAgB,EAAE,MAAM,EAAE,CAAC;AACnD;AACA;AACA,EAAE,MAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC;AAC7C,EAAE,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAC1B,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG;AACrC;AACA,GAAG,MAAM,EAAE,GAAG,WAAW,GAAG,CAAC,CAAC;AAC9B,GAAG,SAAS,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,cAAc,CAAC;AACrD;AACA,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG;AACxB;AACA,IAAI,SAAS,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,cAAc,CAAC;AAC1D;AACA,IAAI;AACJ;AACA,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG;AACxB;AACA,IAAI,SAAS,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,cAAc,CAAC;AAC1D;AACA,IAAI,KAAK,WAAW,KAAK,CAAC,GAAG;AAC7B;AACA,KAAK,SAAS,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC;AAC/B;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG;AACxB;AACA,IAAI,SAAS,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,cAAc,CAAC;AAC1D;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,IAAI,CAAC,UAAU,GAAG,kBAAkB,CAAC;AACvC;AACA,EAAE,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;AACvC,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACvB,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACnB,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;AAC/B,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;AAChC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC;AAC9B,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;AACjB;AACA,EAAE,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC;AACnC,EAAE,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC;AAC7B;AACA,EAAE;AACF;AACA,CAAC;AACD;AACO,MAAM,0BAA0B,SAAS,sBAAsB,CAAC;AACvE;AACA,CAAC,WAAW,GAAG;AACf;AACA,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,IAAI,CAAC,WAAW,GAAG,eAAe,CAAC;AACrC;AACA,EAAE;AACF;AACA,CAAC;AACD;AACO,MAAM,yBAAyB,SAAS,sBAAsB,CAAC;AACtE;AACA,CAAC,WAAW,GAAG;AACf;AACA,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;AAC7B;AACA,EAAE;AACF;AACA;AACA,CAAC;AACD;AACO,MAAM,2BAA2B,SAAS,sBAAsB,CAAC;AACxE;AACA,CAAC,WAAW,GAAG;AACf;AACA,EAAE,KAAK,EAAE,CAAC;AACV,EAAE,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;AAC/B;AACA,EAAE;AACF;AACA;;AC9RA,SAAS,aAAa,EAAE,GAAG,EAAE,aAAa,EAAE,eAAe,GAAG;AAC9D;AACA,CAAC,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC;AAC1B;AACA,CAAC,KAAK,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG;AAC3B;AACA,EAAE,MAAM,IAAI,KAAK,EAAE,sDAAsD,EAAE,CAAC;AAC5E;AACA,EAAE;AACF;AACA,CAAC,MAAM,IAAI,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC;AACzB,CAAC,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,IAAI,EAAE,CAAC;AAC7C,CAAC,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,IAAI,EAAE,CAAC;AAC7C,CAAC,MAAM,YAAY,GAAG,IAAI,YAAY,EAAE,IAAI,EAAE,CAAC;AAC/C;AACA;AACA;AACA,CAAC,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC;AACpD,CAAC,MAAM,eAAe,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,SAAS,GAAG,CAAC,EAAE,EAAE,CAAC;AACrE,CAAC,MAAM,WAAW,GAAG,IAAI,YAAY,EAAE,CAAC,GAAG,eAAe,GAAG,eAAe,EAAE,CAAC;AAC/E;AACA,CAAC,MAAM,iBAAiB,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,CAAC;AAC/D,CAAC,MAAM,aAAa,GAAG,IAAI,WAAW,EAAE,CAAC,GAAG,iBAAiB,GAAG,iBAAiB,EAAE,CAAC;AACpF;AACA,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,GAAG,GAAG;AACxC;AACA,EAAE,MAAM,WAAW,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,CAAC;AAC7C,EAAE,MAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;AACtC,EAAE,MAAM,WAAW,GAAG,mBAAmB,EAAE,WAAW,EAAE,CAAC;AACzD,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjC;AACA,GAAG,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,WAAW,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACtE,GAAG,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,YAAY,EAAE,WAAW,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;AACtE;AACA,GAAG;AACH;AACA,EAAE,KAAK,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;AAC7C;AACA,GAAG,MAAM,KAAK,GAAG,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACnD,GAAG,MAAM,MAAM,GAAG,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACrD;AACA,GAAG,MAAM,eAAe,GAAG,UAAU,GAAG,KAAK,CAAC;AAC9C,GAAG,aAAa,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,eAAe,CAAC;AAChD,GAAG,aAAa,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC;AACvC;AACA,GAAG,MAAM;AACT;AACA,GAAG,MAAM,UAAU,GAAG,CAAC,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,cAAc,CAAC;AAClF,GAAG,MAAM,SAAS,GAAG,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AAC5D;AACA,GAAG,aAAa,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,SAAS,CAAC;AAC1C,GAAG,aAAa,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC;AAC3C;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,GAAG,WAAW,CAAC;AACxC,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG,eAAe,CAAC;AAC7C,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,eAAe,CAAC;AAC9C,CAAC,aAAa,CAAC,MAAM,GAAG,UAAU,CAAC;AACnC,CAAC,aAAa,CAAC,IAAI,GAAG,SAAS,CAAC;AAChC,CAAC,aAAa,CAAC,cAAc,GAAG,SAAS,CAAC;AAC1C,CAAC,aAAa,CAAC,SAAS,GAAG,aAAa,CAAC;AACzC,CAAC,aAAa,CAAC,SAAS,GAAG,aAAa,CAAC;AACzC,CAAC,aAAa,CAAC,eAAe,GAAG,KAAK,CAAC;AACvC,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC;AAClC,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;AACzB;AACA,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC;AAC5C,CAAC,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,iBAAiB,CAAC;AACjD,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG,iBAAiB,CAAC;AAClD,CAAC,eAAe,CAAC,MAAM,GAAG,eAAe,CAAC;AAC1C,CAAC,eAAe,CAAC,IAAI,GAAG,eAAe,CAAC;AACxC,CAAC,eAAe,CAAC,cAAc,GAAG,QAAQ,CAAC;AAC3C,CAAC,eAAe,CAAC,SAAS,GAAG,aAAa,CAAC;AAC3C,CAAC,eAAe,CAAC,SAAS,GAAG,aAAa,CAAC;AAC3C,CAAC,eAAe,CAAC,eAAe,GAAG,KAAK,CAAC;AACzC,CAAC,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC;AACpC,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;AAC3B;AACA,CAAC;AACD;AACO,MAAM,oBAAoB,CAAC;AAClC;AACA,CAAC,WAAW,GAAG;AACf;AACA,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1B,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,0BAA0B,EAAE,CAAC;AAChD,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,2BAA2B,EAAE,CAAC;AACpD,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,WAAW,EAAE,CAAC;AACrC,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;AACvC;AACA,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,CAAC;AAClC;AACA,EAAE;AACF;AACA,CAAC,UAAU,EAAE,GAAG,GAAG;AACnB;AACA,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC;AAC3B;AACA,EAAE,aAAa,EAAE,GAAG,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;AACzD;AACA,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,KAAK,EAAE,CAAC;AAC1C,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAC3D;AACA,EAAE;AACF;AACA,CAAC,OAAO,GAAG;AACX;AACA,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;AAC3D;AACA,EAAE,KAAK,KAAK,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;AAC/B,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;AACrC,EAAE,KAAK,SAAS,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;AACvC,EAAE,KAAK,WAAW,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;AAC3C;AACA,EAAE;AACF;AACA;;AC7IA;AACA;AACA;AACY,MAAC,aAAa,aAAa,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACY,MAAC,uBAAuB,aAAa,CAAC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACY,MAAC,sBAAsB,aAAa,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AClcA,MAAM,eAAe,iBAAiB,IAAI,OAAO,EAAE,CAAC;AACpD,MAAM,aAAa,iBAAiB,IAAI,OAAO,EAAE,CAAC;AAClD,MAAM,cAAc,iBAAiB,IAAI,OAAO,EAAE,CAAC;AACnD,MAAM,eAAe,iBAAiB,IAAI,OAAO,EAAE,CAAC;AACpD;AACA,MAAM,YAAY,iBAAiB,IAAI,OAAO,EAAE,CAAC;AACjD,MAAM,KAAK,iBAAiB,IAAI,OAAO,EAAE,CAAC;AAC1C;AACA,MAAM,UAAU,iBAAiB,IAAI,OAAO,EAAE,CAAC;AAC/C,MAAM,WAAW,iBAAiB,IAAI,OAAO,EAAE,CAAC;AAChD,MAAM,OAAO,iBAAiB,IAAI,OAAO,EAAE,CAAC;AAC5C,MAAM,WAAW,iBAAiB,IAAI,OAAO,EAAE,CAAC;AAChD;AACA;AACA,SAAS,kBAAkB,EAAE,KAAK,EAAE,KAAK,GAAG;AAC5C;AACA,CAAC,KAAK,EAAE,KAAK,IAAI,EAAE,KAAK,GAAG;AAC3B;AACA,EAAE,OAAO;AACT;AACA,EAAE;AACF;AACA,CAAC,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,CAAC;AAC/C,CAAC,MAAM,cAAc,GAAG,KAAK,CAAC,UAAU,KAAK,KAAK,CAAC,UAAU,CAAC;AAC9D,CAAC,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,KAAK,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC;AACtE,CAAC,MAAM,YAAY,GAAG,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC;AACxD;AACA,CAAC,KAAK,EAAE,SAAS,IAAI,EAAE,cAAc,IAAI,EAAE,QAAQ,IAAI,EAAE,YAAY,GAAG;AACxE;AACA,EAAE,MAAM,IAAI,KAAK,EAAE,CAAC;AACpB;AACA,EAAE;AACF;AACA,CAAC;AACD;AACA;AACA,SAAS,oBAAoB,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,GAAG;AAC5D;AACA,CAAC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;AACrC,CAAC,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AACpC,CAAC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,CAAC,MAAM,KAAK,GAAG,aAAa,KAAK,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC;AACnE;AACA,CAAC,OAAO,IAAI,eAAe,EAAE,IAAI,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;AAClF;AACA,CAAC;AACD;AACA;AACA;AACA,SAAS,qBAAqB,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,GAAG,CAAC,GAAG;AACjE;AACA,CAAC,KAAK,IAAI,CAAC,4BAA4B,GAAG;AAC1C;AACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjC,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACjD;AACA,GAAG,MAAM,EAAE,GAAG,CAAC,GAAG,YAAY,CAAC;AAC/B,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;AACrC,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;AAC1D,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;AAC1D,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;AAC1D;AACA,GAAG;AACH;AACA,EAAE,MAAM;AACR;AACA,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AAC7B,EAAE,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC;AACjC,EAAE,MAAM,UAAU,GAAG,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC;AAC5E,EAAE,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;AACvE,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AACzB;AACA,EAAE;AACF;AACA,CAAC;AACD;AACA;AACA,SAAS,eAAe,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,GAAG;AAClD;AACA,CAAC,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC;AACrC,CAAC,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC;AACrC,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACxD;AACA,EAAE,WAAW,EAAE,CAAC,EAAE,IAAI,WAAW,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC/C;AACA,EAAE;AACF;AACA,CAAC;AACD;AACA;AACA,SAAS,mBAAmB,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,GAAG;AACpD;AACA,CAAC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,CAAC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,CAAC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AAC9B,CAAC,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;AAC5C;AACA,CAAC,UAAU,CAAC,mBAAmB,EAAE,QAAQ,CAAC,UAAU,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC;AACxE,CAAC,WAAW,CAAC,mBAAmB,EAAE,QAAQ,CAAC,UAAU,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC;AAC1E;AACA,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAC5B;AACA,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChC;AACA,EAAE,MAAM,MAAM,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC;AAC/C;AACA,EAAE,KAAK,MAAM,KAAK,CAAC,GAAG;AACtB;AACA,GAAG,MAAM,SAAS,GAAG,UAAU,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC;AAClD,GAAG,WAAW,CAAC,gBAAgB,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,EAAE,CAAC;AAC7F;AACA,GAAG,eAAe,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;AACnD;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC3E,CAAC,MAAM,CAAC,kBAAkB,EAAE,OAAO,EAAE,CAAC;AACtC;AACA,CAAC,OAAO,MAAM,CAAC;AACf;AACA,CAAC;AACD;AACA;AACA,SAAS,gBAAgB,EAAE,SAAS,EAAE,eAAe,EAAE,oBAAoB,EAAE,CAAC,EAAE,MAAM,GAAG;AACzF;AACA,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAC7B,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG;AACxD;AACA,EAAE,MAAM,SAAS,GAAG,eAAe,EAAE,CAAC,EAAE,CAAC;AACzC,EAAE,MAAM,cAAc,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;AACxC;AACA,EAAE,KAAK,SAAS,KAAK,CAAC,GAAG,SAAS;AAClC;AACA,EAAE,KAAK,CAAC,mBAAmB,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC;AACjD;AACA,EAAE,KAAK,oBAAoB,GAAG;AAC9B;AACA,GAAG,YAAY,CAAC,eAAe,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACpD;AACA,GAAG,MAAM;AACT;AACA,GAAG,YAAY,CAAC,eAAe,EAAE,KAAK,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,SAAS,EAAE,CAAC;AAClE;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC,MAAM,CAAC,GAAG,EAAE,YAAY,EAAE,CAAC;AAC5B;AACA,CAAC;AACD;AACA;AACA,SAAS,qBAAqB,EAAE,UAAU,EAAE,OAAO,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,EAAE,cAAc,GAAG,IAAI,cAAc,EAAE,GAAG;AAC5J;AACA,CAAC,MAAM,SAAS,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC;AAClD,CAAC,MAAM,EAAE,SAAS,GAAG,KAAK,EAAE,WAAW,GAAG,KAAK,EAAE,cAAc,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjF;AACA,CAAC,MAAM,cAAc,GAAG,IAAI,GAAG,EAAE,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC;AAC7E,CAAC,MAAM,UAAU,GAAG,EAAE,CAAC;AACvB;AACA,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;AAChB;AACA,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;AAC9B,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG;AAChD;AACA,EAAE,MAAM,QAAQ,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;AACnC,EAAE,IAAI,eAAe,GAAG,CAAC,CAAC;AAC1B;AACA;AACA,EAAE,KAAK,SAAS,OAAO,QAAQ,CAAC,KAAK,KAAK,IAAI,EAAE,GAAG;AACnD;AACA,GAAG,MAAM,IAAI,KAAK,EAAE,qJAAqJ,EAAE,CAAC;AAC5K;AACA,GAAG;AACH;AACA;AACA,EAAE,MAAM,MAAM,IAAI,IAAI,QAAQ,CAAC,UAAU,GAAG;AAC5C;AACA,GAAG,KAAK,EAAE,cAAc,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG;AACvC;AACA,IAAI,MAAM,IAAI,KAAK,EAAE,sFAAsF,GAAG,IAAI,GAAG,8DAA8D,EAAE,CAAC;AACtL;AACA,IAAI;AACJ;AACA,GAAG,KAAK,UAAU,EAAE,IAAI,EAAE,KAAK,SAAS,GAAG;AAC3C;AACA,IAAI,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AAC5B;AACA,IAAI;AACJ;AACA,GAAG,UAAU,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,CAAC;AAC1D,GAAG,eAAe,GAAG,CAAC;AACtB;AACA,GAAG;AACH;AACA;AACA,EAAE,KAAK,eAAe,KAAK,cAAc,CAAC,IAAI,GAAG;AACjD;AACA,GAAG,MAAM,IAAI,KAAK,EAAE,uFAAuF,EAAE,CAAC;AAC9G;AACA,GAAG;AACH;AACA,EAAE,KAAK,SAAS,GAAG;AACnB;AACA,GAAG,IAAI,KAAK,CAAC;AACb,GAAG,KAAK,SAAS,GAAG;AACpB;AACA,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC;AACjC;AACA,IAAI,MAAM,KAAK,QAAQ,CAAC,UAAU,CAAC,QAAQ,KAAK,SAAS,GAAG;AAC5D;AACA,IAAI,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;AAC/C;AACA,IAAI,MAAM;AACV;AACA,IAAI,MAAM,IAAI,KAAK,EAAE,yFAAyF,EAAE,CAAC;AACjH;AACA,IAAI;AACJ;AACA,GAAG,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;AAC/C,GAAG,MAAM,IAAI,KAAK,CAAC;AACnB;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA;AACA,CAAC,KAAK,SAAS,GAAG;AAClB;AACA,EAAE,IAAI,gBAAgB,GAAG,KAAK,CAAC;AAC/B,EAAE,KAAK,EAAE,cAAc,CAAC,KAAK,GAAG;AAChC;AACA,GAAG,IAAI,UAAU,GAAG,CAAC,CAAC;AACtB,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG;AAClD;AACA,IAAI,UAAU,IAAI,UAAU,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;AAC9C;AACA,IAAI;AACJ;AACA,GAAG,cAAc,CAAC,QAAQ,EAAE,IAAI,eAAe,EAAE,IAAI,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;AAC7F,GAAG,gBAAgB,GAAG,IAAI,CAAC;AAC3B;AACA,GAAG;AACH;AACA,EAAE,KAAK,WAAW,IAAI,gBAAgB,GAAG;AACzC;AACA,GAAG,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC;AAC5C,GAAG,IAAI,YAAY,GAAG,CAAC,CAAC;AACxB,GAAG,IAAI,WAAW,GAAG,CAAC,CAAC;AACvB,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG;AAClD;AACA,IAAI,MAAM,QAAQ,GAAG,UAAU,EAAE,CAAC,EAAE,CAAC;AACrC,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AACjC,IAAI,KAAK,cAAc,EAAE,CAAC,EAAE,KAAK,IAAI,GAAG;AACxC;AACA,KAAK,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG;AAC9C;AACA,MAAM,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,WAAW,EAAE,CAAC;AACtE,MAAM,YAAY,GAAG,CAAC;AACtB;AACA,MAAM;AACN;AACA,KAAK;AACL;AACA,IAAI,WAAW,IAAI,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;AACtD;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA;AACA,CAAC,MAAM,MAAM,IAAI,IAAI,UAAU,GAAG;AAClC;AACA,EAAE,MAAM,QAAQ,GAAG,UAAU,EAAE,IAAI,EAAE,CAAC;AACtC,EAAE,KAAK,IAAI,IAAI,IAAI,cAAc,CAAC,UAAU,EAAE,GAAG;AACjD;AACA,GAAG,IAAI,KAAK,GAAG,CAAC,CAAC;AACjB,GAAG,MAAM,MAAM,GAAG,IAAI,QAAQ,GAAG;AACjC;AACA,IAAI,KAAK,IAAI,QAAQ,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC;AACnC;AACA,IAAI;AACJ;AACA,GAAG,cAAc,CAAC,YAAY,EAAE,IAAI,EAAE,oBAAoB,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC;AAC/F;AACA,GAAG;AACH;AACA,EAAE,MAAM,eAAe,GAAG,cAAc,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC;AAC5D,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACtD;AACA,GAAG,MAAM,IAAI,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAC;AAC9B,GAAG,KAAK,cAAc,EAAE,CAAC,EAAE,KAAK,IAAI,GAAG;AACvC;AACA,IAAI,qBAAqB,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,CAAC;AAC3D;AACA,IAAI;AACJ;AACA,GAAG,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC;AACxB;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC,OAAO,cAAc,CAAC;AACvB;AACA,CAAC;AACD;AACA,SAAS,uBAAuB,EAAE,CAAC,EAAE,CAAC,GAAG;AACzC;AACA,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,GAAG;AACjC;AACA,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;AACjB;AACA,EAAE;AACF;AACA,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,GAAG;AAC9B;AACA,EAAE,OAAO,KAAK,CAAC;AACf;AACA,EAAE;AACF;AACA,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAC9C;AACA,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,GAAG;AAC3B;AACA,GAAG,OAAO,KAAK,CAAC;AAChB;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC,OAAO,IAAI,CAAC;AACb;AACA,CAAC;AACD;AACA;AACA,MAAM,YAAY,CAAC;AACnB;AACA,CAAC,WAAW,EAAE,IAAI,GAAG;AACrB;AACA,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,OAAO,EAAE,CAAC;AACnC,EAAE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC3B,EAAE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC3B,EAAE,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,CAAC;AAC5B,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACnB;AACA,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;AAChB;AACA,EAAE;AACF;AACA,CAAC,MAAM,GAAG;AACV;AACA,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACzB,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjC,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjC,EAAE,MAAM,cAAc,GAAG,EAAE,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC,CAAC;AAC5G,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;AAC5C,EAAE,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;AAC3D,EAAE,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;AACvC;AACA,EAAE,KAAK,QAAQ,GAAG;AAClB;AACA;AACA,GAAG,KAAK,EAAE,QAAQ,CAAC,WAAW,GAAG;AACjC;AACA,IAAI,QAAQ,CAAC,kBAAkB,EAAE,CAAC;AAClC;AACA,IAAI;AACJ;AACA,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;AACrB;AACA;AACA,GAAG,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;AAC9C,GAAG,KAAK,EAAE,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM,GAAG;AAClF;AACA,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,KAAK,EAAE,CAAC;AAC7C;AACA,IAAI,MAAM;AACV;AACA,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,EAAE,CAAC;AAC1C;AACA,IAAI;AACJ;AACA,GAAG,MAAM;AACT;AACA,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC5B;AACA,GAAG;AACH;AACA,EAAE;AACF;AACA,CAAC,SAAS,GAAG;AACb;AACA,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACzB,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjC,EAAE,MAAM,cAAc,GAAG,EAAE,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC,CAAC;AAC5G,EAAE,MAAM,SAAS;AACjB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE;AAC9C,GAAG,IAAI,CAAC,YAAY,KAAK,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO;AAC7D,GAAG,uBAAuB,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE;AACpG,GAAG,IAAI,CAAC,cAAc,KAAK,cAAc,CAAC;AAC1C;AACA,EAAE,OAAO,EAAE,SAAS,CAAC;AACrB;AACA,EAAE;AACF;AACA,CAAC;AACD;AACO,MAAM,uBAAuB,CAAC;AACrC;AACA,CAAC,WAAW,EAAE,MAAM,GAAG;AACvB;AACA,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG;AACnC;AACA,GAAG,MAAM,GAAG,EAAE,MAAM,EAAE,CAAC;AACvB;AACA,GAAG;AACH;AACA,EAAE,MAAM,WAAW,GAAG,EAAE,CAAC;AACzB,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,IAAI;AAC5B;AACA,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC,IAAI;AAChC;AACA,IAAI,KAAK,CAAC,CAAC,MAAM,GAAG;AACpB;AACA,KAAK,WAAW,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAC3B;AACA,KAAK;AACL;AACA,IAAI,EAAE,CAAC;AACP;AACA,GAAG,EAAE,CAAC;AACN;AACA,EAAE,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;AAC5B,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACxB,EAAE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACnC,EAAE,IAAI,CAAC,UAAU,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AAC9E,EAAE,IAAI,CAAC,qBAAqB,GAAG,IAAI,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,IAAI,cAAc,EAAE,EAAE,CAAC;AACxG,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,EAAE,CAAC;AAChC;AACA,EAAE;AACF;AACA,CAAC,YAAY,GAAG;AAChB;AACA,EAAE,MAAM,SAAS,GAAG,EAAE,CAAC;AACvB,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,IAAI;AAC/B;AACA,GAAG,KAAK,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,GAAG;AACzC;AACA,IAAI,SAAS,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AACvC;AACA,IAAI,MAAM;AACV;AACA,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;AACpC;AACA,IAAI;AACJ;AACA,GAAG,EAAE,CAAC;AACN,EAAE,OAAO,SAAS,CAAC;AACnB;AACA,EAAE;AACF;AACA,CAAC,QAAQ,EAAE,cAAc,GAAG,IAAI,cAAc,EAAE,GAAG;AACnD;AACA;AACA,EAAE,IAAI,cAAc,GAAG,EAAE,CAAC;AAC1B,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,qBAAqB,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;AACtE,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AACpD;AACA,GAAG,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC;AAC5B,GAAG,MAAM,IAAI,GAAG,qBAAqB,EAAE,CAAC,EAAE,CAAC;AAC3C,GAAG,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;AACrC,GAAG,KAAK,EAAE,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,GAAG;AAC3C;AACA,IAAI,IAAI,CAAC,wBAAwB,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AAChD,IAAI,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;AACjC;AACA,IAAI,KAAK,EAAE,IAAI,GAAG;AAClB;AACA,KAAK,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,YAAY,EAAE,IAAI,EAAE,EAAE,CAAC;AACpD;AACA,KAAK,MAAM;AACX;AACA,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;AACnB;AACA,KAAK;AACL;AACA,IAAI,MAAM;AACV;AACA,IAAI,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AAChC;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,EAAE,SAAS,EAAE,cAAc,EAAE,EAAE,cAAc,EAAE,CAAC;AAChG;AACA,EAAE,MAAM,MAAM,GAAG,IAAI,cAAc,CAAC,UAAU,GAAG;AACjD;AACA,GAAG,cAAc,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC;AACvD;AACA,GAAG;AACH;AACA,EAAE,OAAO,cAAc,CAAC;AACxB;AACA,EAAE;AACF;AACA,CAAC,wBAAwB,EAAE,IAAI,EAAE,cAAc,GAAG,IAAI,cAAc,EAAE,GAAG;AACzE;AACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjC,EAAE,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;AACzD,EAAE,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC;AAC7D,EAAE,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC;AAC/D,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;AACzC,EAAE,MAAM,gBAAgB,GAAG,cAAc,CAAC,UAAU,CAAC;AACrD;AACA;AACA,EAAE,KAAK,EAAE,cAAc,CAAC,KAAK,GAAG;AAChC;AACA,GAAG,cAAc,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AACzC;AACA,GAAG;AACH;AACA,EAAE,KAAK,EAAE,gBAAgB,CAAC,QAAQ,GAAG;AACrC;AACA,GAAG,cAAc,CAAC,YAAY,EAAE,UAAU,EAAE,oBAAoB,EAAE,UAAU,CAAC,QAAQ,EAAE,EAAE,CAAC;AAC1F;AACA,GAAG;AACH;AACA,EAAE,KAAK,aAAa,IAAI,EAAE,gBAAgB,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,GAAG;AACzE;AACA,GAAG,cAAc,CAAC,YAAY,EAAE,QAAQ,EAAE,oBAAoB,EAAE,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;AACtF;AACA,GAAG;AACH;AACA,EAAE,KAAK,cAAc,IAAI,EAAE,gBAAgB,CAAC,OAAO,IAAI,UAAU,CAAC,OAAO,GAAG;AAC5E;AACA,GAAG,cAAc,CAAC,YAAY,EAAE,SAAS,EAAE,oBAAoB,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC;AACxF;AACA,GAAG;AACH;AACA;AACA,EAAE,kBAAkB,EAAE,QAAQ,CAAC,KAAK,EAAE,cAAc,CAAC,KAAK,EAAE,CAAC;AAC7D,EAAE,kBAAkB,EAAE,UAAU,CAAC,QAAQ,EAAE,gBAAgB,CAAC,QAAQ,EAAE,CAAC;AACvE;AACA,EAAE,KAAK,aAAa,GAAG;AACvB;AACA,GAAG,kBAAkB,EAAE,UAAU,CAAC,MAAM,EAAE,gBAAgB,CAAC,MAAM,EAAE,CAAC;AACpE;AACA,GAAG;AACH;AACA,EAAE,KAAK,cAAc,GAAG;AACxB;AACA,GAAG,kBAAkB,EAAE,UAAU,CAAC,OAAO,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC;AACtE;AACA,GAAG;AACH;AACA;AACA,EAAE,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;AACvC,EAAE,MAAM,MAAM,GAAG,aAAa,GAAG,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;AAC1D,EAAE,MAAM,OAAO,GAAG,cAAc,GAAG,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;AAC7D,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC;AAC1D,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,eAAe,CAAC,MAAM,CAAC;AACtD,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC;AACxD,EAAE,MAAM,oBAAoB,GAAG,QAAQ,CAAC,oBAAoB,CAAC;AAC7D,EAAE,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC;AACrD,EAAE,MAAM,YAAY,GAAG,IAAI,OAAO,EAAE,CAAC;AACrC,EAAE,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;AACnD;AACA,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG;AAChE;AACA,GAAG,eAAe,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;AACtD,GAAG,KAAK,MAAM,GAAG;AACjB;AACA,IAAI,aAAa,CAAC,mBAAmB,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;AACnD;AACA,IAAI;AACJ;AACA,GAAG,KAAK,OAAO,GAAG;AAClB;AACA,IAAI,eAAe,CAAC,mBAAmB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;AACtD,IAAI,cAAc,CAAC,mBAAmB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;AACrD;AACA,IAAI;AACJ;AACA;AACA,GAAG,KAAK,eAAe,GAAG;AAC1B;AACA,IAAI,KAAK,aAAa,GAAG;AACzB;AACA,KAAK,gBAAgB,EAAE,aAAa,EAAE,eAAe,EAAE,oBAAoB,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC;AAClG;AACA,KAAK;AACL;AACA,IAAI,KAAK,WAAW,GAAG;AACvB;AACA,KAAK,gBAAgB,EAAE,WAAW,EAAE,eAAe,EAAE,oBAAoB,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC;AAC9F;AACA,KAAK;AACL;AACA,IAAI,KAAK,YAAY,GAAG;AACxB;AACA,KAAK,gBAAgB,EAAE,YAAY,EAAE,eAAe,EAAE,oBAAoB,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC;AAChG;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA;AACA,GAAG,KAAK,IAAI,CAAC,aAAa,GAAG;AAC7B;AACA,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC;AAC7C,IAAI,KAAK,MAAM,GAAG;AAClB;AACA,KAAK,mBAAmB,EAAE,IAAI,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC;AACnD;AACA,KAAK;AACL;AACA,IAAI,KAAK,OAAO,GAAG;AACnB;AACA,KAAK,mBAAmB,EAAE,IAAI,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC;AACpD;AACA,KAAK;AACL;AACA,IAAI;AACJ;AACA;AACA,GAAG,KAAK,oBAAoB,GAAG;AAC/B;AACA,IAAI,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;AACrD;AACA,IAAI;AACJ;AACA,GAAG,gBAAgB,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC;AAClG;AACA,GAAG,KAAK,MAAM,GAAG;AACjB;AACA,IAAI,KAAK,oBAAoB,GAAG;AAChC;AACA,KAAK,aAAa,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;AACrD;AACA,KAAK;AACL;AACA,IAAI,gBAAgB,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,CAAC;AAC3F;AACA,IAAI;AACJ;AACA,GAAG,KAAK,OAAO,GAAG;AAClB;AACA,IAAI,KAAK,oBAAoB,GAAG;AAChC;AACA,KAAK,cAAc,CAAC,kBAAkB,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;AAC3D;AACA,KAAK;AACL;AACA,IAAI,gBAAgB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC;AACnH;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA;AACA,EAAE,MAAM,MAAM,CAAC,IAAI,IAAI,CAAC,UAAU,GAAG;AACrC;AACA,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC;AACpC,GAAG,KAAK,GAAG,KAAK,UAAU,IAAI,GAAG,KAAK,SAAS,IAAI,GAAG,KAAK,QAAQ,IAAI,IAAI,GAAG,IAAI,UAAU,EAAE,GAAG;AACjG;AACA,IAAI,SAAS;AACb;AACA,IAAI;AACJ;AACA,GAAG,KAAK,EAAE,gBAAgB,EAAE,GAAG,EAAE,GAAG;AACpC;AACA,IAAI,cAAc,CAAC,YAAY,EAAE,GAAG,EAAE,oBAAoB,EAAE,UAAU,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;AAClF;AACA,IAAI;AACJ;AACA,GAAG,kBAAkB,EAAE,UAAU,EAAE,GAAG,EAAE,EAAE,gBAAgB,EAAE,GAAG,EAAE,EAAE,CAAC;AACpE,GAAG,qBAAqB,EAAE,UAAU,EAAE,GAAG,EAAE,EAAE,gBAAgB,EAAE,GAAG,EAAE,EAAE,CAAC;AACvE;AACA,GAAG;AACH;AACA,EAAE,OAAO,cAAc,CAAC;AACxB;AACA,EAAE;AACF;AACA;;;;"}