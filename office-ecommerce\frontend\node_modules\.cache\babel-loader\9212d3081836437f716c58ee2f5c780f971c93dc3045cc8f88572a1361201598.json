{"ast": null, "code": "/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0,\n  MAX_SAFE_INTEGER = 9007199254740991;\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]',\n  funcTag = '[object Function]',\n  genTag = '[object GeneratorFunction]',\n  symbolTag = '[object Symbol]';\n\n/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\n/**\n * A faster alternative to `Function#apply`, this function invokes `func`\n * with the `this` binding of `thisArg` and the arguments of `args`.\n *\n * @private\n * @param {Function} func The function to invoke.\n * @param {*} thisArg The `this` binding of `func`.\n * @param {Array} args The arguments to invoke `func` with.\n * @returns {*} Returns the result of `func`.\n */\nfunction apply(func, thisArg, args) {\n  switch (args.length) {\n    case 0:\n      return func.call(thisArg);\n    case 1:\n      return func.call(thisArg, args[0]);\n    case 2:\n      return func.call(thisArg, args[0], args[1]);\n    case 3:\n      return func.call(thisArg, args[0], args[1], args[2]);\n  }\n  return func.apply(thisArg, args);\n}\n\n/**\n * A specialized version of `_.map` for arrays without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the new mapped array.\n */\nfunction arrayMap(array, iteratee) {\n  var index = -1,\n    length = array ? array.length : 0,\n    result = Array(length);\n  while (++index < length) {\n    result[index] = iteratee(array[index], index, array);\n  }\n  return result;\n}\n\n/**\n * Appends the elements of `values` to `array`.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {Array} values The values to append.\n * @returns {Array} Returns `array`.\n */\nfunction arrayPush(array, values) {\n  var index = -1,\n    length = values.length,\n    offset = array.length;\n  while (++index < length) {\n    array[offset + index] = values[index];\n  }\n  return array;\n}\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar Symbol = root.Symbol,\n  propertyIsEnumerable = objectProto.propertyIsEnumerable,\n  spreadableSymbol = Symbol ? Symbol.isConcatSpreadable : undefined;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * The base implementation of `_.flatten` with support for restricting flattening.\n *\n * @private\n * @param {Array} array The array to flatten.\n * @param {number} depth The maximum recursion depth.\n * @param {boolean} [predicate=isFlattenable] The function invoked per iteration.\n * @param {boolean} [isStrict] Restrict to values that pass `predicate` checks.\n * @param {Array} [result=[]] The initial result value.\n * @returns {Array} Returns the new flattened array.\n */\nfunction baseFlatten(array, depth, predicate, isStrict, result) {\n  var index = -1,\n    length = array.length;\n  predicate || (predicate = isFlattenable);\n  result || (result = []);\n  while (++index < length) {\n    var value = array[index];\n    if (depth > 0 && predicate(value)) {\n      if (depth > 1) {\n        // Recursively flatten arrays (susceptible to call stack limits).\n        baseFlatten(value, depth - 1, predicate, isStrict, result);\n      } else {\n        arrayPush(result, value);\n      }\n    } else if (!isStrict) {\n      result[result.length] = value;\n    }\n  }\n  return result;\n}\n\n/**\n * The base implementation of `_.pick` without support for individual\n * property identifiers.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} props The property identifiers to pick.\n * @returns {Object} Returns the new object.\n */\nfunction basePick(object, props) {\n  object = Object(object);\n  return basePickBy(object, props, function (value, key) {\n    return key in object;\n  });\n}\n\n/**\n * The base implementation of  `_.pickBy` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} props The property identifiers to pick from.\n * @param {Function} predicate The function invoked per property.\n * @returns {Object} Returns the new object.\n */\nfunction basePickBy(object, props, predicate) {\n  var index = -1,\n    length = props.length,\n    result = {};\n  while (++index < length) {\n    var key = props[index],\n      value = object[key];\n    if (predicate(value, key)) {\n      result[key] = value;\n    }\n  }\n  return result;\n}\n\n/**\n * The base implementation of `_.rest` which doesn't validate or coerce arguments.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @returns {Function} Returns the new function.\n */\nfunction baseRest(func, start) {\n  start = nativeMax(start === undefined ? func.length - 1 : start, 0);\n  return function () {\n    var args = arguments,\n      index = -1,\n      length = nativeMax(args.length - start, 0),\n      array = Array(length);\n    while (++index < length) {\n      array[index] = args[start + index];\n    }\n    index = -1;\n    var otherArgs = Array(start + 1);\n    while (++index < start) {\n      otherArgs[index] = args[index];\n    }\n    otherArgs[start] = array;\n    return apply(func, this, otherArgs);\n  };\n}\n\n/**\n * Checks if `value` is a flattenable `arguments` object or array.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is flattenable, else `false`.\n */\nfunction isFlattenable(value) {\n  return isArray(value) || isArguments(value) || !!(spreadableSymbol && value && value[spreadableSymbol]);\n}\n\n/**\n * Converts `value` to a string key if it's not a string or symbol.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {string|symbol} Returns the key.\n */\nfunction toKey(value) {\n  if (typeof value == 'string' || isSymbol(value)) {\n    return value;\n  }\n  var result = value + '';\n  return result == '0' && 1 / value == -INFINITY ? '-0' : result;\n}\n\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */\nfunction isArguments(value) {\n  // Safari 8.1 makes `arguments.callee` enumerable in strict mode.\n  return isArrayLikeObject(value) && hasOwnProperty.call(value, 'callee') && (!propertyIsEnumerable.call(value, 'callee') || objectToString.call(value) == argsTag);\n}\n\n/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */\nvar isArray = Array.isArray;\n\n/**\n * Checks if `value` is array-like. A value is considered array-like if it's\n * not a function and has a `value.length` that's an integer greater than or\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\n * @example\n *\n * _.isArrayLike([1, 2, 3]);\n * // => true\n *\n * _.isArrayLike(document.body.children);\n * // => true\n *\n * _.isArrayLike('abc');\n * // => true\n *\n * _.isArrayLike(_.noop);\n * // => false\n */\nfunction isArrayLike(value) {\n  return value != null && isLength(value.length) && !isFunction(value);\n}\n\n/**\n * This method is like `_.isArrayLike` except that it also checks if `value`\n * is an object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array-like object,\n *  else `false`.\n * @example\n *\n * _.isArrayLikeObject([1, 2, 3]);\n * // => true\n *\n * _.isArrayLikeObject(document.body.children);\n * // => true\n *\n * _.isArrayLikeObject('abc');\n * // => false\n *\n * _.isArrayLikeObject(_.noop);\n * // => false\n */\nfunction isArrayLikeObject(value) {\n  return isObjectLike(value) && isArrayLike(value);\n}\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 8-9 which returns 'object' for typed array and other constructors.\n  var tag = isObject(value) ? objectToString.call(value) : '';\n  return tag == funcTag || tag == genTag;\n}\n\n/**\n * Checks if `value` is a valid array-like length.\n *\n * **Note:** This method is loosely based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.\n * @example\n *\n * _.isLength(3);\n * // => true\n *\n * _.isLength(Number.MIN_VALUE);\n * // => false\n *\n * _.isLength(Infinity);\n * // => false\n *\n * _.isLength('3');\n * // => false\n */\nfunction isLength(value) {\n  return typeof value == 'number' && value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' || isObjectLike(value) && objectToString.call(value) == symbolTag;\n}\n\n/**\n * Creates an object composed of the picked `object` properties.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [props] The property identifiers to pick.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.pick(object, ['a', 'c']);\n * // => { 'a': 1, 'c': 3 }\n */\nvar pick = baseRest(function (object, props) {\n  return object == null ? {} : basePick(object, arrayMap(baseFlatten(props, 1), toKey));\n});\nmodule.exports = pick;", "map": {"version": 3, "names": ["INFINITY", "MAX_SAFE_INTEGER", "argsTag", "funcTag", "genTag", "symbolTag", "freeGlobal", "global", "Object", "freeSelf", "self", "root", "Function", "apply", "func", "thisArg", "args", "length", "call", "arrayMap", "array", "iteratee", "index", "result", "Array", "arrayPush", "values", "offset", "objectProto", "prototype", "hasOwnProperty", "objectToString", "toString", "Symbol", "propertyIsEnumerable", "spreadableSymbol", "isConcatSpreadable", "undefined", "nativeMax", "Math", "max", "baseFlatten", "depth", "predicate", "isStrict", "isFlattenable", "value", "base<PERSON>ick", "object", "props", "basePickBy", "key", "baseRest", "start", "arguments", "otherArgs", "isArray", "isArguments", "to<PERSON><PERSON>", "isSymbol", "isArrayLikeObject", "isArrayLike", "<PERSON><PERSON><PERSON><PERSON>", "isFunction", "isObjectLike", "tag", "isObject", "type", "pick", "module", "exports"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/lodash.pick/index.js"], "sourcesContent": ["/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0,\n    MAX_SAFE_INTEGER = 9007199254740991;\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    symbolTag = '[object Symbol]';\n\n/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\n/**\n * A faster alternative to `Function#apply`, this function invokes `func`\n * with the `this` binding of `thisArg` and the arguments of `args`.\n *\n * @private\n * @param {Function} func The function to invoke.\n * @param {*} thisArg The `this` binding of `func`.\n * @param {Array} args The arguments to invoke `func` with.\n * @returns {*} Returns the result of `func`.\n */\nfunction apply(func, thisArg, args) {\n  switch (args.length) {\n    case 0: return func.call(thisArg);\n    case 1: return func.call(thisArg, args[0]);\n    case 2: return func.call(thisArg, args[0], args[1]);\n    case 3: return func.call(thisArg, args[0], args[1], args[2]);\n  }\n  return func.apply(thisArg, args);\n}\n\n/**\n * A specialized version of `_.map` for arrays without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the new mapped array.\n */\nfunction arrayMap(array, iteratee) {\n  var index = -1,\n      length = array ? array.length : 0,\n      result = Array(length);\n\n  while (++index < length) {\n    result[index] = iteratee(array[index], index, array);\n  }\n  return result;\n}\n\n/**\n * Appends the elements of `values` to `array`.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {Array} values The values to append.\n * @returns {Array} Returns `array`.\n */\nfunction arrayPush(array, values) {\n  var index = -1,\n      length = values.length,\n      offset = array.length;\n\n  while (++index < length) {\n    array[offset + index] = values[index];\n  }\n  return array;\n}\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar Symbol = root.Symbol,\n    propertyIsEnumerable = objectProto.propertyIsEnumerable,\n    spreadableSymbol = Symbol ? Symbol.isConcatSpreadable : undefined;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * The base implementation of `_.flatten` with support for restricting flattening.\n *\n * @private\n * @param {Array} array The array to flatten.\n * @param {number} depth The maximum recursion depth.\n * @param {boolean} [predicate=isFlattenable] The function invoked per iteration.\n * @param {boolean} [isStrict] Restrict to values that pass `predicate` checks.\n * @param {Array} [result=[]] The initial result value.\n * @returns {Array} Returns the new flattened array.\n */\nfunction baseFlatten(array, depth, predicate, isStrict, result) {\n  var index = -1,\n      length = array.length;\n\n  predicate || (predicate = isFlattenable);\n  result || (result = []);\n\n  while (++index < length) {\n    var value = array[index];\n    if (depth > 0 && predicate(value)) {\n      if (depth > 1) {\n        // Recursively flatten arrays (susceptible to call stack limits).\n        baseFlatten(value, depth - 1, predicate, isStrict, result);\n      } else {\n        arrayPush(result, value);\n      }\n    } else if (!isStrict) {\n      result[result.length] = value;\n    }\n  }\n  return result;\n}\n\n/**\n * The base implementation of `_.pick` without support for individual\n * property identifiers.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} props The property identifiers to pick.\n * @returns {Object} Returns the new object.\n */\nfunction basePick(object, props) {\n  object = Object(object);\n  return basePickBy(object, props, function(value, key) {\n    return key in object;\n  });\n}\n\n/**\n * The base implementation of  `_.pickBy` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} props The property identifiers to pick from.\n * @param {Function} predicate The function invoked per property.\n * @returns {Object} Returns the new object.\n */\nfunction basePickBy(object, props, predicate) {\n  var index = -1,\n      length = props.length,\n      result = {};\n\n  while (++index < length) {\n    var key = props[index],\n        value = object[key];\n\n    if (predicate(value, key)) {\n      result[key] = value;\n    }\n  }\n  return result;\n}\n\n/**\n * The base implementation of `_.rest` which doesn't validate or coerce arguments.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @returns {Function} Returns the new function.\n */\nfunction baseRest(func, start) {\n  start = nativeMax(start === undefined ? (func.length - 1) : start, 0);\n  return function() {\n    var args = arguments,\n        index = -1,\n        length = nativeMax(args.length - start, 0),\n        array = Array(length);\n\n    while (++index < length) {\n      array[index] = args[start + index];\n    }\n    index = -1;\n    var otherArgs = Array(start + 1);\n    while (++index < start) {\n      otherArgs[index] = args[index];\n    }\n    otherArgs[start] = array;\n    return apply(func, this, otherArgs);\n  };\n}\n\n/**\n * Checks if `value` is a flattenable `arguments` object or array.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is flattenable, else `false`.\n */\nfunction isFlattenable(value) {\n  return isArray(value) || isArguments(value) ||\n    !!(spreadableSymbol && value && value[spreadableSymbol]);\n}\n\n/**\n * Converts `value` to a string key if it's not a string or symbol.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {string|symbol} Returns the key.\n */\nfunction toKey(value) {\n  if (typeof value == 'string' || isSymbol(value)) {\n    return value;\n  }\n  var result = (value + '');\n  return (result == '0' && (1 / value) == -INFINITY) ? '-0' : result;\n}\n\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */\nfunction isArguments(value) {\n  // Safari 8.1 makes `arguments.callee` enumerable in strict mode.\n  return isArrayLikeObject(value) && hasOwnProperty.call(value, 'callee') &&\n    (!propertyIsEnumerable.call(value, 'callee') || objectToString.call(value) == argsTag);\n}\n\n/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */\nvar isArray = Array.isArray;\n\n/**\n * Checks if `value` is array-like. A value is considered array-like if it's\n * not a function and has a `value.length` that's an integer greater than or\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\n * @example\n *\n * _.isArrayLike([1, 2, 3]);\n * // => true\n *\n * _.isArrayLike(document.body.children);\n * // => true\n *\n * _.isArrayLike('abc');\n * // => true\n *\n * _.isArrayLike(_.noop);\n * // => false\n */\nfunction isArrayLike(value) {\n  return value != null && isLength(value.length) && !isFunction(value);\n}\n\n/**\n * This method is like `_.isArrayLike` except that it also checks if `value`\n * is an object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array-like object,\n *  else `false`.\n * @example\n *\n * _.isArrayLikeObject([1, 2, 3]);\n * // => true\n *\n * _.isArrayLikeObject(document.body.children);\n * // => true\n *\n * _.isArrayLikeObject('abc');\n * // => false\n *\n * _.isArrayLikeObject(_.noop);\n * // => false\n */\nfunction isArrayLikeObject(value) {\n  return isObjectLike(value) && isArrayLike(value);\n}\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 8-9 which returns 'object' for typed array and other constructors.\n  var tag = isObject(value) ? objectToString.call(value) : '';\n  return tag == funcTag || tag == genTag;\n}\n\n/**\n * Checks if `value` is a valid array-like length.\n *\n * **Note:** This method is loosely based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.\n * @example\n *\n * _.isLength(3);\n * // => true\n *\n * _.isLength(Number.MIN_VALUE);\n * // => false\n *\n * _.isLength(Infinity);\n * // => false\n *\n * _.isLength('3');\n * // => false\n */\nfunction isLength(value) {\n  return typeof value == 'number' &&\n    value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\n}\n\n/**\n * Creates an object composed of the picked `object` properties.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [props] The property identifiers to pick.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.pick(object, ['a', 'c']);\n * // => { 'a': 1, 'c': 3 }\n */\nvar pick = baseRest(function(object, props) {\n  return object == null ? {} : basePick(object, arrayMap(baseFlatten(props, 1), toKey));\n});\n\nmodule.exports = pick;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAIA,QAAQ,GAAG,CAAC,GAAG,CAAC;EAChBC,gBAAgB,GAAG,gBAAgB;;AAEvC;AACA,IAAIC,OAAO,GAAG,oBAAoB;EAC9BC,OAAO,GAAG,mBAAmB;EAC7BC,MAAM,GAAG,4BAA4B;EACrCC,SAAS,GAAG,iBAAiB;;AAEjC;AACA,IAAIC,UAAU,GAAG,OAAOC,MAAM,IAAI,QAAQ,IAAIA,MAAM,IAAIA,MAAM,CAACC,MAAM,KAAKA,MAAM,IAAID,MAAM;;AAE1F;AACA,IAAIE,QAAQ,GAAG,OAAOC,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAIA,IAAI,CAACF,MAAM,KAAKA,MAAM,IAAIE,IAAI;;AAEhF;AACA,IAAIC,IAAI,GAAGL,UAAU,IAAIG,QAAQ,IAAIG,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;;AAE9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,IAAI,EAAEC,OAAO,EAAEC,IAAI,EAAE;EAClC,QAAQA,IAAI,CAACC,MAAM;IACjB,KAAK,CAAC;MAAE,OAAOH,IAAI,CAACI,IAAI,CAACH,OAAO,CAAC;IACjC,KAAK,CAAC;MAAE,OAAOD,IAAI,CAACI,IAAI,CAACH,OAAO,EAAEC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC1C,KAAK,CAAC;MAAE,OAAOF,IAAI,CAACI,IAAI,CAACH,OAAO,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;IACnD,KAAK,CAAC;MAAE,OAAOF,IAAI,CAACI,IAAI,CAACH,OAAO,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;EAC9D;EACA,OAAOF,IAAI,CAACD,KAAK,CAACE,OAAO,EAAEC,IAAI,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACjC,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVL,MAAM,GAAGG,KAAK,GAAGA,KAAK,CAACH,MAAM,GAAG,CAAC;IACjCM,MAAM,GAAGC,KAAK,CAACP,MAAM,CAAC;EAE1B,OAAO,EAAEK,KAAK,GAAGL,MAAM,EAAE;IACvBM,MAAM,CAACD,KAAK,CAAC,GAAGD,QAAQ,CAACD,KAAK,CAACE,KAAK,CAAC,EAAEA,KAAK,EAAEF,KAAK,CAAC;EACtD;EACA,OAAOG,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAACL,KAAK,EAAEM,MAAM,EAAE;EAChC,IAAIJ,KAAK,GAAG,CAAC,CAAC;IACVL,MAAM,GAAGS,MAAM,CAACT,MAAM;IACtBU,MAAM,GAAGP,KAAK,CAACH,MAAM;EAEzB,OAAO,EAAEK,KAAK,GAAGL,MAAM,EAAE;IACvBG,KAAK,CAACO,MAAM,GAAGL,KAAK,CAAC,GAAGI,MAAM,CAACJ,KAAK,CAAC;EACvC;EACA,OAAOF,KAAK;AACd;;AAEA;AACA,IAAIQ,WAAW,GAAGpB,MAAM,CAACqB,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGF,WAAW,CAACE,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACI,QAAQ;;AAEzC;AACA,IAAIC,MAAM,GAAGtB,IAAI,CAACsB,MAAM;EACpBC,oBAAoB,GAAGN,WAAW,CAACM,oBAAoB;EACvDC,gBAAgB,GAAGF,MAAM,GAAGA,MAAM,CAACG,kBAAkB,GAAGC,SAAS;;AAErE;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACrB,KAAK,EAAEsB,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAErB,MAAM,EAAE;EAC9D,IAAID,KAAK,GAAG,CAAC,CAAC;IACVL,MAAM,GAAGG,KAAK,CAACH,MAAM;EAEzB0B,SAAS,KAAKA,SAAS,GAAGE,aAAa,CAAC;EACxCtB,MAAM,KAAKA,MAAM,GAAG,EAAE,CAAC;EAEvB,OAAO,EAAED,KAAK,GAAGL,MAAM,EAAE;IACvB,IAAI6B,KAAK,GAAG1B,KAAK,CAACE,KAAK,CAAC;IACxB,IAAIoB,KAAK,GAAG,CAAC,IAAIC,SAAS,CAACG,KAAK,CAAC,EAAE;MACjC,IAAIJ,KAAK,GAAG,CAAC,EAAE;QACb;QACAD,WAAW,CAACK,KAAK,EAAEJ,KAAK,GAAG,CAAC,EAAEC,SAAS,EAAEC,QAAQ,EAAErB,MAAM,CAAC;MAC5D,CAAC,MAAM;QACLE,SAAS,CAACF,MAAM,EAAEuB,KAAK,CAAC;MAC1B;IACF,CAAC,MAAM,IAAI,CAACF,QAAQ,EAAE;MACpBrB,MAAM,CAACA,MAAM,CAACN,MAAM,CAAC,GAAG6B,KAAK;IAC/B;EACF;EACA,OAAOvB,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwB,QAAQA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC/BD,MAAM,GAAGxC,MAAM,CAACwC,MAAM,CAAC;EACvB,OAAOE,UAAU,CAACF,MAAM,EAAEC,KAAK,EAAE,UAASH,KAAK,EAAEK,GAAG,EAAE;IACpD,OAAOA,GAAG,IAAIH,MAAM;EACtB,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,UAAUA,CAACF,MAAM,EAAEC,KAAK,EAAEN,SAAS,EAAE;EAC5C,IAAIrB,KAAK,GAAG,CAAC,CAAC;IACVL,MAAM,GAAGgC,KAAK,CAAChC,MAAM;IACrBM,MAAM,GAAG,CAAC,CAAC;EAEf,OAAO,EAAED,KAAK,GAAGL,MAAM,EAAE;IACvB,IAAIkC,GAAG,GAAGF,KAAK,CAAC3B,KAAK,CAAC;MAClBwB,KAAK,GAAGE,MAAM,CAACG,GAAG,CAAC;IAEvB,IAAIR,SAAS,CAACG,KAAK,EAAEK,GAAG,CAAC,EAAE;MACzB5B,MAAM,CAAC4B,GAAG,CAAC,GAAGL,KAAK;IACrB;EACF;EACA,OAAOvB,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6B,QAAQA,CAACtC,IAAI,EAAEuC,KAAK,EAAE;EAC7BA,KAAK,GAAGf,SAAS,CAACe,KAAK,KAAKhB,SAAS,GAAIvB,IAAI,CAACG,MAAM,GAAG,CAAC,GAAIoC,KAAK,EAAE,CAAC,CAAC;EACrE,OAAO,YAAW;IAChB,IAAIrC,IAAI,GAAGsC,SAAS;MAChBhC,KAAK,GAAG,CAAC,CAAC;MACVL,MAAM,GAAGqB,SAAS,CAACtB,IAAI,CAACC,MAAM,GAAGoC,KAAK,EAAE,CAAC,CAAC;MAC1CjC,KAAK,GAAGI,KAAK,CAACP,MAAM,CAAC;IAEzB,OAAO,EAAEK,KAAK,GAAGL,MAAM,EAAE;MACvBG,KAAK,CAACE,KAAK,CAAC,GAAGN,IAAI,CAACqC,KAAK,GAAG/B,KAAK,CAAC;IACpC;IACAA,KAAK,GAAG,CAAC,CAAC;IACV,IAAIiC,SAAS,GAAG/B,KAAK,CAAC6B,KAAK,GAAG,CAAC,CAAC;IAChC,OAAO,EAAE/B,KAAK,GAAG+B,KAAK,EAAE;MACtBE,SAAS,CAACjC,KAAK,CAAC,GAAGN,IAAI,CAACM,KAAK,CAAC;IAChC;IACAiC,SAAS,CAACF,KAAK,CAAC,GAAGjC,KAAK;IACxB,OAAOP,KAAK,CAACC,IAAI,EAAE,IAAI,EAAEyC,SAAS,CAAC;EACrC,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASV,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAOU,OAAO,CAACV,KAAK,CAAC,IAAIW,WAAW,CAACX,KAAK,CAAC,IACzC,CAAC,EAAEX,gBAAgB,IAAIW,KAAK,IAAIA,KAAK,CAACX,gBAAgB,CAAC,CAAC;AAC5D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuB,KAAKA,CAACZ,KAAK,EAAE;EACpB,IAAI,OAAOA,KAAK,IAAI,QAAQ,IAAIa,QAAQ,CAACb,KAAK,CAAC,EAAE;IAC/C,OAAOA,KAAK;EACd;EACA,IAAIvB,MAAM,GAAIuB,KAAK,GAAG,EAAG;EACzB,OAAQvB,MAAM,IAAI,GAAG,IAAK,CAAC,GAAGuB,KAAK,IAAK,CAAC9C,QAAQ,GAAI,IAAI,GAAGuB,MAAM;AACpE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkC,WAAWA,CAACX,KAAK,EAAE;EAC1B;EACA,OAAOc,iBAAiB,CAACd,KAAK,CAAC,IAAIhB,cAAc,CAACZ,IAAI,CAAC4B,KAAK,EAAE,QAAQ,CAAC,KACpE,CAACZ,oBAAoB,CAAChB,IAAI,CAAC4B,KAAK,EAAE,QAAQ,CAAC,IAAIf,cAAc,CAACb,IAAI,CAAC4B,KAAK,CAAC,IAAI5C,OAAO,CAAC;AAC1F;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIsD,OAAO,GAAGhC,KAAK,CAACgC,OAAO;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,WAAWA,CAACf,KAAK,EAAE;EAC1B,OAAOA,KAAK,IAAI,IAAI,IAAIgB,QAAQ,CAAChB,KAAK,CAAC7B,MAAM,CAAC,IAAI,CAAC8C,UAAU,CAACjB,KAAK,CAAC;AACtE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASc,iBAAiBA,CAACd,KAAK,EAAE;EAChC,OAAOkB,YAAY,CAAClB,KAAK,CAAC,IAAIe,WAAW,CAACf,KAAK,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiB,UAAUA,CAACjB,KAAK,EAAE;EACzB;EACA;EACA,IAAImB,GAAG,GAAGC,QAAQ,CAACpB,KAAK,CAAC,GAAGf,cAAc,CAACb,IAAI,CAAC4B,KAAK,CAAC,GAAG,EAAE;EAC3D,OAAOmB,GAAG,IAAI9D,OAAO,IAAI8D,GAAG,IAAI7D,MAAM;AACxC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0D,QAAQA,CAAChB,KAAK,EAAE;EACvB,OAAO,OAAOA,KAAK,IAAI,QAAQ,IAC7BA,KAAK,GAAG,CAAC,CAAC,IAAIA,KAAK,GAAG,CAAC,IAAI,CAAC,IAAIA,KAAK,IAAI7C,gBAAgB;AAC7D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiE,QAAQA,CAACpB,KAAK,EAAE;EACvB,IAAIqB,IAAI,GAAG,OAAOrB,KAAK;EACvB,OAAO,CAAC,CAACA,KAAK,KAAKqB,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,UAAU,CAAC;AAC5D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASH,YAAYA,CAAClB,KAAK,EAAE;EAC3B,OAAO,CAAC,CAACA,KAAK,IAAI,OAAOA,KAAK,IAAI,QAAQ;AAC5C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,QAAQA,CAACb,KAAK,EAAE;EACvB,OAAO,OAAOA,KAAK,IAAI,QAAQ,IAC5BkB,YAAY,CAAClB,KAAK,CAAC,IAAIf,cAAc,CAACb,IAAI,CAAC4B,KAAK,CAAC,IAAIzC,SAAU;AACpE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI+D,IAAI,GAAGhB,QAAQ,CAAC,UAASJ,MAAM,EAAEC,KAAK,EAAE;EAC1C,OAAOD,MAAM,IAAI,IAAI,GAAG,CAAC,CAAC,GAAGD,QAAQ,CAACC,MAAM,EAAE7B,QAAQ,CAACsB,WAAW,CAACQ,KAAK,EAAE,CAAC,CAAC,EAAES,KAAK,CAAC,CAAC;AACvF,CAAC,CAAC;AAEFW,MAAM,CAACC,OAAO,GAAGF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}