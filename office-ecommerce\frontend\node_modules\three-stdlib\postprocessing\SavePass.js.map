{"version": 3, "file": "SavePass.js", "sources": ["../../src/postprocessing/SavePass.js"], "sourcesContent": ["import { NoBlending, ShaderMaterial, UniformsUtils, WebGLRenderTarget } from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { CopyShader } from '../shaders/CopyShader'\n\nclass SavePass extends Pass {\n  constructor(renderTarget) {\n    super()\n\n    if (CopyShader === undefined) console.error('THREE.SavePass relies on CopyShader')\n\n    const shader = CopyShader\n\n    this.textureID = 'tDiffuse'\n\n    this.uniforms = UniformsUtils.clone(shader.uniforms)\n\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: shader.vertexShader,\n      fragmentShader: shader.fragmentShader,\n      blending: NoBlending,\n    })\n\n    this.renderTarget = renderTarget\n\n    if (this.renderTarget === undefined) {\n      this.renderTarget = new WebGLRenderTarget(window.innerWidth, window.innerHeight)\n      this.renderTarget.texture.name = 'SavePass.rt'\n    }\n\n    this.needsSwap = false\n\n    this.fsQuad = new FullScreenQuad(this.material)\n  }\n\n  render(renderer, writeBuffer, readBuffer /*, deltaTime, maskActive */) {\n    if (this.uniforms[this.textureID]) {\n      this.uniforms[this.textureID].value = readBuffer.texture\n    }\n\n    renderer.setRenderTarget(this.renderTarget)\n    if (this.clear) renderer.clear()\n    this.fsQuad.render(renderer)\n  }\n}\n\nexport { SavePass }\n"], "names": [], "mappings": ";;;AAIA,MAAM,iBAAiB,KAAK;AAAA,EAC1B,YAAY,cAAc;AACxB,UAAO;AAEP,QAAI,eAAe;AAAW,cAAQ,MAAM,qCAAqC;AAEjF,UAAM,SAAS;AAEf,SAAK,YAAY;AAEjB,SAAK,WAAW,cAAc,MAAM,OAAO,QAAQ;AAEnD,SAAK,WAAW,IAAI,eAAe;AAAA,MACjC,UAAU,KAAK;AAAA,MACf,cAAc,OAAO;AAAA,MACrB,gBAAgB,OAAO;AAAA,MACvB,UAAU;AAAA,IAChB,CAAK;AAED,SAAK,eAAe;AAEpB,QAAI,KAAK,iBAAiB,QAAW;AACnC,WAAK,eAAe,IAAI,kBAAkB,OAAO,YAAY,OAAO,WAAW;AAC/E,WAAK,aAAa,QAAQ,OAAO;AAAA,IAClC;AAED,SAAK,YAAY;AAEjB,SAAK,SAAS,IAAI,eAAe,KAAK,QAAQ;AAAA,EAC/C;AAAA,EAED,OAAO,UAAU,aAAa,YAAyC;AACrE,QAAI,KAAK,SAAS,KAAK,SAAS,GAAG;AACjC,WAAK,SAAS,KAAK,SAAS,EAAE,QAAQ,WAAW;AAAA,IAClD;AAED,aAAS,gBAAgB,KAAK,YAAY;AAC1C,QAAI,KAAK;AAAO,eAAS,MAAO;AAChC,SAAK,OAAO,OAAO,QAAQ;AAAA,EAC5B;AACH;"}