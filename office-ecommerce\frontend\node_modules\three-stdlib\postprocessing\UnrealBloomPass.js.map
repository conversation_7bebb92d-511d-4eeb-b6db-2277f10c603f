{"version": 3, "file": "UnrealBloomPass.js", "sources": ["../../src/postprocessing/UnrealBloomPass.js"], "sourcesContent": ["import {\n  AdditiveBlending,\n  Color,\n  HalfFloatType,\n  MeshBasicMaterial,\n  ShaderMaterial,\n  UniformsUtils,\n  Vector2,\n  Vector3,\n  WebGLRenderTarget,\n} from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { CopyShader } from '../shaders/CopyShader'\nimport { LuminosityHighPassShader } from '../shaders/LuminosityHighPassShader'\n\n/**\n * UnrealBloomPass is inspired by the bloom pass of Unreal Engine. It creates a\n * mip map chain of bloom textures and blurs them with different radii. Because\n * of the weighted combination of mips, and because larger blurs are done on\n * higher mips, this effect provides good quality and performance.\n *\n * Reference:\n * - https://docs.unrealengine.com/latest/INT/Engine/Rendering/PostProcessEffects/Bloom/\n */\nconst UnrealBloomPass = /* @__PURE__ */ (() => {\n  class UnrealBloomPass extends Pass {\n    static BlurDirectionX = new Vector2(1.0, 0.0)\n    static BlurDirectionY = new Vector2(0.0, 1.0)\n\n    constructor(resolution, strength, radius, threshold) {\n      super()\n\n      this.strength = strength !== undefined ? strength : 1\n      this.radius = radius\n      this.threshold = threshold\n      this.resolution = resolution !== undefined ? new Vector2(resolution.x, resolution.y) : new Vector2(256, 256)\n\n      // create color only once here, reuse it later inside the render function\n      this.clearColor = new Color(0, 0, 0)\n\n      // render targets\n      this.renderTargetsHorizontal = []\n      this.renderTargetsVertical = []\n      this.nMips = 5\n      let resx = Math.round(this.resolution.x / 2)\n      let resy = Math.round(this.resolution.y / 2)\n\n      this.renderTargetBright = new WebGLRenderTarget(resx, resy, { type: HalfFloatType })\n      this.renderTargetBright.texture.name = 'UnrealBloomPass.bright'\n      this.renderTargetBright.texture.generateMipmaps = false\n\n      for (let i = 0; i < this.nMips; i++) {\n        const renderTargetHorizonal = new WebGLRenderTarget(resx, resy, { type: HalfFloatType })\n\n        renderTargetHorizonal.texture.name = 'UnrealBloomPass.h' + i\n        renderTargetHorizonal.texture.generateMipmaps = false\n\n        this.renderTargetsHorizontal.push(renderTargetHorizonal)\n\n        const renderTargetVertical = new WebGLRenderTarget(resx, resy, { type: HalfFloatType })\n\n        renderTargetVertical.texture.name = 'UnrealBloomPass.v' + i\n        renderTargetVertical.texture.generateMipmaps = false\n\n        this.renderTargetsVertical.push(renderTargetVertical)\n\n        resx = Math.round(resx / 2)\n\n        resy = Math.round(resy / 2)\n      }\n\n      // luminosity high pass material\n\n      const highPassShader = LuminosityHighPassShader\n      this.highPassUniforms = UniformsUtils.clone(highPassShader.uniforms)\n\n      this.highPassUniforms['luminosityThreshold'].value = threshold\n      this.highPassUniforms['smoothWidth'].value = 0.01\n\n      this.materialHighPassFilter = new ShaderMaterial({\n        uniforms: this.highPassUniforms,\n        vertexShader: highPassShader.vertexShader,\n        fragmentShader: highPassShader.fragmentShader,\n        defines: {},\n      })\n\n      // Gaussian Blur Materials\n      this.separableBlurMaterials = []\n      const kernelSizeArray = [3, 5, 7, 9, 11]\n      resx = Math.round(this.resolution.x / 2)\n      resy = Math.round(this.resolution.y / 2)\n\n      for (let i = 0; i < this.nMips; i++) {\n        this.separableBlurMaterials.push(this.getSeperableBlurMaterial(kernelSizeArray[i]))\n\n        this.separableBlurMaterials[i].uniforms['texSize'].value = new Vector2(resx, resy)\n\n        resx = Math.round(resx / 2)\n\n        resy = Math.round(resy / 2)\n      }\n\n      // Composite material\n      this.compositeMaterial = this.getCompositeMaterial(this.nMips)\n      this.compositeMaterial.uniforms['blurTexture1'].value = this.renderTargetsVertical[0].texture\n      this.compositeMaterial.uniforms['blurTexture2'].value = this.renderTargetsVertical[1].texture\n      this.compositeMaterial.uniforms['blurTexture3'].value = this.renderTargetsVertical[2].texture\n      this.compositeMaterial.uniforms['blurTexture4'].value = this.renderTargetsVertical[3].texture\n      this.compositeMaterial.uniforms['blurTexture5'].value = this.renderTargetsVertical[4].texture\n      this.compositeMaterial.uniforms['bloomStrength'].value = strength\n      this.compositeMaterial.uniforms['bloomRadius'].value = 0.1\n      this.compositeMaterial.needsUpdate = true\n\n      const bloomFactors = [1.0, 0.8, 0.6, 0.4, 0.2]\n      this.compositeMaterial.uniforms['bloomFactors'].value = bloomFactors\n      this.bloomTintColors = [\n        new Vector3(1, 1, 1),\n        new Vector3(1, 1, 1),\n        new Vector3(1, 1, 1),\n        new Vector3(1, 1, 1),\n        new Vector3(1, 1, 1),\n      ]\n      this.compositeMaterial.uniforms['bloomTintColors'].value = this.bloomTintColors\n\n      // copy material\n\n      const copyShader = CopyShader\n\n      this.copyUniforms = UniformsUtils.clone(copyShader.uniforms)\n      this.copyUniforms['opacity'].value = 1.0\n\n      this.materialCopy = new ShaderMaterial({\n        uniforms: this.copyUniforms,\n        vertexShader: copyShader.vertexShader,\n        fragmentShader: copyShader.fragmentShader,\n        blending: AdditiveBlending,\n        depthTest: false,\n        depthWrite: false,\n        transparent: true,\n      })\n\n      this.enabled = true\n      this.needsSwap = false\n\n      this._oldClearColor = new Color()\n      this.oldClearAlpha = 1\n\n      this.basic = new MeshBasicMaterial()\n\n      this.fsQuad = new FullScreenQuad(null)\n    }\n\n    dispose() {\n      for (let i = 0; i < this.renderTargetsHorizontal.length; i++) {\n        this.renderTargetsHorizontal[i].dispose()\n      }\n\n      for (let i = 0; i < this.renderTargetsVertical.length; i++) {\n        this.renderTargetsVertical[i].dispose()\n      }\n\n      this.renderTargetBright.dispose()\n\n      //\n\n      for (let i = 0; i < this.separableBlurMaterials.length; i++) {\n        this.separableBlurMaterials[i].dispose()\n      }\n\n      this.compositeMaterial.dispose()\n      this.materialCopy.dispose()\n      this.basic.dispose()\n\n      //\n\n      this.fsQuad.dispose()\n    }\n\n    setSize(width, height) {\n      let resx = Math.round(width / 2)\n      let resy = Math.round(height / 2)\n\n      this.renderTargetBright.setSize(resx, resy)\n\n      for (let i = 0; i < this.nMips; i++) {\n        this.renderTargetsHorizontal[i].setSize(resx, resy)\n        this.renderTargetsVertical[i].setSize(resx, resy)\n\n        this.separableBlurMaterials[i].uniforms['texSize'].value = new Vector2(resx, resy)\n\n        resx = Math.round(resx / 2)\n        resy = Math.round(resy / 2)\n      }\n    }\n\n    render(renderer, writeBuffer, readBuffer, deltaTime, maskActive) {\n      renderer.getClearColor(this._oldClearColor)\n      this.oldClearAlpha = renderer.getClearAlpha()\n      const oldAutoClear = renderer.autoClear\n      renderer.autoClear = false\n\n      renderer.setClearColor(this.clearColor, 0)\n\n      if (maskActive) renderer.state.buffers.stencil.setTest(false)\n\n      // Render input to screen\n\n      if (this.renderToScreen) {\n        this.fsQuad.material = this.basic\n        this.basic.map = readBuffer.texture\n\n        renderer.setRenderTarget(null)\n        renderer.clear()\n        this.fsQuad.render(renderer)\n      }\n\n      // 1. Extract Bright Areas\n\n      this.highPassUniforms['tDiffuse'].value = readBuffer.texture\n      this.highPassUniforms['luminosityThreshold'].value = this.threshold\n      this.fsQuad.material = this.materialHighPassFilter\n\n      renderer.setRenderTarget(this.renderTargetBright)\n      renderer.clear()\n      this.fsQuad.render(renderer)\n\n      // 2. Blur All the mips progressively\n\n      let inputRenderTarget = this.renderTargetBright\n\n      for (let i = 0; i < this.nMips; i++) {\n        this.fsQuad.material = this.separableBlurMaterials[i]\n\n        this.separableBlurMaterials[i].uniforms['colorTexture'].value = inputRenderTarget.texture\n        this.separableBlurMaterials[i].uniforms['direction'].value = UnrealBloomPass.BlurDirectionX\n        renderer.setRenderTarget(this.renderTargetsHorizontal[i])\n        renderer.clear()\n        this.fsQuad.render(renderer)\n\n        this.separableBlurMaterials[i].uniforms['colorTexture'].value = this.renderTargetsHorizontal[i].texture\n        this.separableBlurMaterials[i].uniforms['direction'].value = UnrealBloomPass.BlurDirectionY\n        renderer.setRenderTarget(this.renderTargetsVertical[i])\n        renderer.clear()\n        this.fsQuad.render(renderer)\n\n        inputRenderTarget = this.renderTargetsVertical[i]\n      }\n\n      // Composite All the mips\n\n      this.fsQuad.material = this.compositeMaterial\n      this.compositeMaterial.uniforms['bloomStrength'].value = this.strength\n      this.compositeMaterial.uniforms['bloomRadius'].value = this.radius\n      this.compositeMaterial.uniforms['bloomTintColors'].value = this.bloomTintColors\n\n      renderer.setRenderTarget(this.renderTargetsHorizontal[0])\n      renderer.clear()\n      this.fsQuad.render(renderer)\n\n      // Blend it additively over the input texture\n\n      this.fsQuad.material = this.materialCopy\n      this.copyUniforms['tDiffuse'].value = this.renderTargetsHorizontal[0].texture\n\n      if (maskActive) renderer.state.buffers.stencil.setTest(true)\n\n      if (this.renderToScreen) {\n        renderer.setRenderTarget(null)\n        this.fsQuad.render(renderer)\n      } else {\n        renderer.setRenderTarget(readBuffer)\n        this.fsQuad.render(renderer)\n      }\n\n      // Restore renderer settings\n\n      renderer.setClearColor(this._oldClearColor, this.oldClearAlpha)\n      renderer.autoClear = oldAutoClear\n    }\n\n    getSeperableBlurMaterial(kernelRadius) {\n      return new ShaderMaterial({\n        defines: {\n          KERNEL_RADIUS: kernelRadius,\n          SIGMA: kernelRadius,\n        },\n\n        uniforms: {\n          colorTexture: { value: null },\n          texSize: { value: new Vector2(0.5, 0.5) },\n          direction: { value: new Vector2(0.5, 0.5) },\n        },\n\n        vertexShader: `varying vec2 vUv;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t}`,\n\n        fragmentShader: `#include <common>\n\t\t\t\tvarying vec2 vUv;\n\t\t\t\tuniform sampler2D colorTexture;\n\t\t\t\tuniform vec2 texSize;\n\t\t\t\tuniform vec2 direction;\n\n\t\t\t\tfloat gaussianPdf(in float x, in float sigma) {\n\t\t\t\t\treturn 0.39894 * exp( -0.5 * x * x/( sigma * sigma))/sigma;\n\t\t\t\t}\n\t\t\t\tvoid main() {\n\t\t\t\t\tvec2 invSize = 1.0 / texSize;\n\t\t\t\t\tfloat fSigma = float(SIGMA);\n\t\t\t\t\tfloat weightSum = gaussianPdf(0.0, fSigma);\n\t\t\t\t\tvec3 diffuseSum = texture2D( colorTexture, vUv).rgb * weightSum;\n\t\t\t\t\tfor( int i = 1; i < KERNEL_RADIUS; i ++ ) {\n\t\t\t\t\t\tfloat x = float(i);\n\t\t\t\t\t\tfloat w = gaussianPdf(x, fSigma);\n\t\t\t\t\t\tvec2 uvOffset = direction * invSize * x;\n\t\t\t\t\t\tvec3 sample1 = texture2D( colorTexture, vUv + uvOffset).rgb;\n\t\t\t\t\t\tvec3 sample2 = texture2D( colorTexture, vUv - uvOffset).rgb;\n\t\t\t\t\t\tdiffuseSum += (sample1 + sample2) * w;\n\t\t\t\t\t\tweightSum += 2.0 * w;\n\t\t\t\t\t}\n\t\t\t\t\tgl_FragColor = vec4(diffuseSum/weightSum, 1.0);\n\t\t\t\t}`,\n      })\n    }\n\n    getCompositeMaterial(nMips) {\n      return new ShaderMaterial({\n        defines: {\n          NUM_MIPS: nMips,\n        },\n\n        uniforms: {\n          blurTexture1: { value: null },\n          blurTexture2: { value: null },\n          blurTexture3: { value: null },\n          blurTexture4: { value: null },\n          blurTexture5: { value: null },\n          bloomStrength: { value: 1.0 },\n          bloomFactors: { value: null },\n          bloomTintColors: { value: null },\n          bloomRadius: { value: 0.0 },\n        },\n\n        vertexShader: `varying vec2 vUv;\n\t\t\t\tvoid main() {\n\t\t\t\t\tvUv = uv;\n\t\t\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t}`,\n\n        fragmentShader: `varying vec2 vUv;\n\t\t\t\tuniform sampler2D blurTexture1;\n\t\t\t\tuniform sampler2D blurTexture2;\n\t\t\t\tuniform sampler2D blurTexture3;\n\t\t\t\tuniform sampler2D blurTexture4;\n\t\t\t\tuniform sampler2D blurTexture5;\n\t\t\t\tuniform float bloomStrength;\n\t\t\t\tuniform float bloomRadius;\n\t\t\t\tuniform float bloomFactors[NUM_MIPS];\n\t\t\t\tuniform vec3 bloomTintColors[NUM_MIPS];\n\n\t\t\t\tfloat lerpBloomFactor(const in float factor) {\n\t\t\t\t\tfloat mirrorFactor = 1.2 - factor;\n\t\t\t\t\treturn mix(factor, mirrorFactor, bloomRadius);\n\t\t\t\t}\n\n\t\t\t\tvoid main() {\n\t\t\t\t\tgl_FragColor = bloomStrength * ( lerpBloomFactor(bloomFactors[0]) * vec4(bloomTintColors[0], 1.0) * texture2D(blurTexture1, vUv) +\n\t\t\t\t\t\tlerpBloomFactor(bloomFactors[1]) * vec4(bloomTintColors[1], 1.0) * texture2D(blurTexture2, vUv) +\n\t\t\t\t\t\tlerpBloomFactor(bloomFactors[2]) * vec4(bloomTintColors[2], 1.0) * texture2D(blurTexture3, vUv) +\n\t\t\t\t\t\tlerpBloomFactor(bloomFactors[3]) * vec4(bloomTintColors[3], 1.0) * texture2D(blurTexture4, vUv) +\n\t\t\t\t\t\tlerpBloomFactor(bloomFactors[4]) * vec4(bloomTintColors[4], 1.0) * texture2D(blurTexture5, vUv) );\n\t\t\t\t}`,\n      })\n    }\n  }\n\n  return UnrealBloomPass\n})()\n\nexport { UnrealBloomPass }\n"], "names": ["UnrealBloomPass"], "mappings": ";;;;;;;;;;AAwBK,MAAC,kBAAmC,uBAAM;AAC7C,QAAM,mBAAN,cAA8B,KAAK;AAAA,IAIjC,YAAY,YAAY,UAAU,QAAQ,WAAW;AACnD,YAAO;AAEP,WAAK,WAAW,aAAa,SAAY,WAAW;AACpD,WAAK,SAAS;AACd,WAAK,YAAY;AACjB,WAAK,aAAa,eAAe,SAAY,IAAI,QAAQ,WAAW,GAAG,WAAW,CAAC,IAAI,IAAI,QAAQ,KAAK,GAAG;AAG3G,WAAK,aAAa,IAAI,MAAM,GAAG,GAAG,CAAC;AAGnC,WAAK,0BAA0B,CAAE;AACjC,WAAK,wBAAwB,CAAE;AAC/B,WAAK,QAAQ;AACb,UAAI,OAAO,KAAK,MAAM,KAAK,WAAW,IAAI,CAAC;AAC3C,UAAI,OAAO,KAAK,MAAM,KAAK,WAAW,IAAI,CAAC;AAE3C,WAAK,qBAAqB,IAAI,kBAAkB,MAAM,MAAM,EAAE,MAAM,eAAe;AACnF,WAAK,mBAAmB,QAAQ,OAAO;AACvC,WAAK,mBAAmB,QAAQ,kBAAkB;AAElD,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,KAAK;AACnC,cAAM,wBAAwB,IAAI,kBAAkB,MAAM,MAAM,EAAE,MAAM,eAAe;AAEvF,8BAAsB,QAAQ,OAAO,sBAAsB;AAC3D,8BAAsB,QAAQ,kBAAkB;AAEhD,aAAK,wBAAwB,KAAK,qBAAqB;AAEvD,cAAM,uBAAuB,IAAI,kBAAkB,MAAM,MAAM,EAAE,MAAM,eAAe;AAEtF,6BAAqB,QAAQ,OAAO,sBAAsB;AAC1D,6BAAqB,QAAQ,kBAAkB;AAE/C,aAAK,sBAAsB,KAAK,oBAAoB;AAEpD,eAAO,KAAK,MAAM,OAAO,CAAC;AAE1B,eAAO,KAAK,MAAM,OAAO,CAAC;AAAA,MAC3B;AAID,YAAM,iBAAiB;AACvB,WAAK,mBAAmB,cAAc,MAAM,eAAe,QAAQ;AAEnE,WAAK,iBAAiB,qBAAqB,EAAE,QAAQ;AACrD,WAAK,iBAAiB,aAAa,EAAE,QAAQ;AAE7C,WAAK,yBAAyB,IAAI,eAAe;AAAA,QAC/C,UAAU,KAAK;AAAA,QACf,cAAc,eAAe;AAAA,QAC7B,gBAAgB,eAAe;AAAA,QAC/B,SAAS,CAAE;AAAA,MACnB,CAAO;AAGD,WAAK,yBAAyB,CAAE;AAChC,YAAM,kBAAkB,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE;AACvC,aAAO,KAAK,MAAM,KAAK,WAAW,IAAI,CAAC;AACvC,aAAO,KAAK,MAAM,KAAK,WAAW,IAAI,CAAC;AAEvC,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,KAAK;AACnC,aAAK,uBAAuB,KAAK,KAAK,yBAAyB,gBAAgB,CAAC,CAAC,CAAC;AAElF,aAAK,uBAAuB,CAAC,EAAE,SAAS,SAAS,EAAE,QAAQ,IAAI,QAAQ,MAAM,IAAI;AAEjF,eAAO,KAAK,MAAM,OAAO,CAAC;AAE1B,eAAO,KAAK,MAAM,OAAO,CAAC;AAAA,MAC3B;AAGD,WAAK,oBAAoB,KAAK,qBAAqB,KAAK,KAAK;AAC7D,WAAK,kBAAkB,SAAS,cAAc,EAAE,QAAQ,KAAK,sBAAsB,CAAC,EAAE;AACtF,WAAK,kBAAkB,SAAS,cAAc,EAAE,QAAQ,KAAK,sBAAsB,CAAC,EAAE;AACtF,WAAK,kBAAkB,SAAS,cAAc,EAAE,QAAQ,KAAK,sBAAsB,CAAC,EAAE;AACtF,WAAK,kBAAkB,SAAS,cAAc,EAAE,QAAQ,KAAK,sBAAsB,CAAC,EAAE;AACtF,WAAK,kBAAkB,SAAS,cAAc,EAAE,QAAQ,KAAK,sBAAsB,CAAC,EAAE;AACtF,WAAK,kBAAkB,SAAS,eAAe,EAAE,QAAQ;AACzD,WAAK,kBAAkB,SAAS,aAAa,EAAE,QAAQ;AACvD,WAAK,kBAAkB,cAAc;AAErC,YAAM,eAAe,CAAC,GAAK,KAAK,KAAK,KAAK,GAAG;AAC7C,WAAK,kBAAkB,SAAS,cAAc,EAAE,QAAQ;AACxD,WAAK,kBAAkB;AAAA,QACrB,IAAI,QAAQ,GAAG,GAAG,CAAC;AAAA,QACnB,IAAI,QAAQ,GAAG,GAAG,CAAC;AAAA,QACnB,IAAI,QAAQ,GAAG,GAAG,CAAC;AAAA,QACnB,IAAI,QAAQ,GAAG,GAAG,CAAC;AAAA,QACnB,IAAI,QAAQ,GAAG,GAAG,CAAC;AAAA,MACpB;AACD,WAAK,kBAAkB,SAAS,iBAAiB,EAAE,QAAQ,KAAK;AAIhE,YAAM,aAAa;AAEnB,WAAK,eAAe,cAAc,MAAM,WAAW,QAAQ;AAC3D,WAAK,aAAa,SAAS,EAAE,QAAQ;AAErC,WAAK,eAAe,IAAI,eAAe;AAAA,QACrC,UAAU,KAAK;AAAA,QACf,cAAc,WAAW;AAAA,QACzB,gBAAgB,WAAW;AAAA,QAC3B,UAAU;AAAA,QACV,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,aAAa;AAAA,MACrB,CAAO;AAED,WAAK,UAAU;AACf,WAAK,YAAY;AAEjB,WAAK,iBAAiB,IAAI,MAAO;AACjC,WAAK,gBAAgB;AAErB,WAAK,QAAQ,IAAI,kBAAmB;AAEpC,WAAK,SAAS,IAAI,eAAe,IAAI;AAAA,IACtC;AAAA,IAED,UAAU;AACR,eAAS,IAAI,GAAG,IAAI,KAAK,wBAAwB,QAAQ,KAAK;AAC5D,aAAK,wBAAwB,CAAC,EAAE,QAAS;AAAA,MAC1C;AAED,eAAS,IAAI,GAAG,IAAI,KAAK,sBAAsB,QAAQ,KAAK;AAC1D,aAAK,sBAAsB,CAAC,EAAE,QAAS;AAAA,MACxC;AAED,WAAK,mBAAmB,QAAS;AAIjC,eAAS,IAAI,GAAG,IAAI,KAAK,uBAAuB,QAAQ,KAAK;AAC3D,aAAK,uBAAuB,CAAC,EAAE,QAAS;AAAA,MACzC;AAED,WAAK,kBAAkB,QAAS;AAChC,WAAK,aAAa,QAAS;AAC3B,WAAK,MAAM,QAAS;AAIpB,WAAK,OAAO,QAAS;AAAA,IACtB;AAAA,IAED,QAAQ,OAAO,QAAQ;AACrB,UAAI,OAAO,KAAK,MAAM,QAAQ,CAAC;AAC/B,UAAI,OAAO,KAAK,MAAM,SAAS,CAAC;AAEhC,WAAK,mBAAmB,QAAQ,MAAM,IAAI;AAE1C,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,KAAK;AACnC,aAAK,wBAAwB,CAAC,EAAE,QAAQ,MAAM,IAAI;AAClD,aAAK,sBAAsB,CAAC,EAAE,QAAQ,MAAM,IAAI;AAEhD,aAAK,uBAAuB,CAAC,EAAE,SAAS,SAAS,EAAE,QAAQ,IAAI,QAAQ,MAAM,IAAI;AAEjF,eAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,eAAO,KAAK,MAAM,OAAO,CAAC;AAAA,MAC3B;AAAA,IACF;AAAA,IAED,OAAO,UAAU,aAAa,YAAY,WAAW,YAAY;AAC/D,eAAS,cAAc,KAAK,cAAc;AAC1C,WAAK,gBAAgB,SAAS,cAAe;AAC7C,YAAM,eAAe,SAAS;AAC9B,eAAS,YAAY;AAErB,eAAS,cAAc,KAAK,YAAY,CAAC;AAEzC,UAAI;AAAY,iBAAS,MAAM,QAAQ,QAAQ,QAAQ,KAAK;AAI5D,UAAI,KAAK,gBAAgB;AACvB,aAAK,OAAO,WAAW,KAAK;AAC5B,aAAK,MAAM,MAAM,WAAW;AAE5B,iBAAS,gBAAgB,IAAI;AAC7B,iBAAS,MAAO;AAChB,aAAK,OAAO,OAAO,QAAQ;AAAA,MAC5B;AAID,WAAK,iBAAiB,UAAU,EAAE,QAAQ,WAAW;AACrD,WAAK,iBAAiB,qBAAqB,EAAE,QAAQ,KAAK;AAC1D,WAAK,OAAO,WAAW,KAAK;AAE5B,eAAS,gBAAgB,KAAK,kBAAkB;AAChD,eAAS,MAAO;AAChB,WAAK,OAAO,OAAO,QAAQ;AAI3B,UAAI,oBAAoB,KAAK;AAE7B,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,KAAK;AACnC,aAAK,OAAO,WAAW,KAAK,uBAAuB,CAAC;AAEpD,aAAK,uBAAuB,CAAC,EAAE,SAAS,cAAc,EAAE,QAAQ,kBAAkB;AAClF,aAAK,uBAAuB,CAAC,EAAE,SAAS,WAAW,EAAE,QAAQ,iBAAgB;AAC7E,iBAAS,gBAAgB,KAAK,wBAAwB,CAAC,CAAC;AACxD,iBAAS,MAAO;AAChB,aAAK,OAAO,OAAO,QAAQ;AAE3B,aAAK,uBAAuB,CAAC,EAAE,SAAS,cAAc,EAAE,QAAQ,KAAK,wBAAwB,CAAC,EAAE;AAChG,aAAK,uBAAuB,CAAC,EAAE,SAAS,WAAW,EAAE,QAAQ,iBAAgB;AAC7E,iBAAS,gBAAgB,KAAK,sBAAsB,CAAC,CAAC;AACtD,iBAAS,MAAO;AAChB,aAAK,OAAO,OAAO,QAAQ;AAE3B,4BAAoB,KAAK,sBAAsB,CAAC;AAAA,MACjD;AAID,WAAK,OAAO,WAAW,KAAK;AAC5B,WAAK,kBAAkB,SAAS,eAAe,EAAE,QAAQ,KAAK;AAC9D,WAAK,kBAAkB,SAAS,aAAa,EAAE,QAAQ,KAAK;AAC5D,WAAK,kBAAkB,SAAS,iBAAiB,EAAE,QAAQ,KAAK;AAEhE,eAAS,gBAAgB,KAAK,wBAAwB,CAAC,CAAC;AACxD,eAAS,MAAO;AAChB,WAAK,OAAO,OAAO,QAAQ;AAI3B,WAAK,OAAO,WAAW,KAAK;AAC5B,WAAK,aAAa,UAAU,EAAE,QAAQ,KAAK,wBAAwB,CAAC,EAAE;AAEtE,UAAI;AAAY,iBAAS,MAAM,QAAQ,QAAQ,QAAQ,IAAI;AAE3D,UAAI,KAAK,gBAAgB;AACvB,iBAAS,gBAAgB,IAAI;AAC7B,aAAK,OAAO,OAAO,QAAQ;AAAA,MACnC,OAAa;AACL,iBAAS,gBAAgB,UAAU;AACnC,aAAK,OAAO,OAAO,QAAQ;AAAA,MAC5B;AAID,eAAS,cAAc,KAAK,gBAAgB,KAAK,aAAa;AAC9D,eAAS,YAAY;AAAA,IACtB;AAAA,IAED,yBAAyB,cAAc;AACrC,aAAO,IAAI,eAAe;AAAA,QACxB,SAAS;AAAA,UACP,eAAe;AAAA,UACf,OAAO;AAAA,QACR;AAAA,QAED,UAAU;AAAA,UACR,cAAc,EAAE,OAAO,KAAM;AAAA,UAC7B,SAAS,EAAE,OAAO,IAAI,QAAQ,KAAK,GAAG,EAAG;AAAA,UACzC,WAAW,EAAE,OAAO,IAAI,QAAQ,KAAK,GAAG,EAAG;AAAA,QAC5C;AAAA,QAED,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,QAMd,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAyBxB,CAAO;AAAA,IACF;AAAA,IAED,qBAAqB,OAAO;AAC1B,aAAO,IAAI,eAAe;AAAA,QACxB,SAAS;AAAA,UACP,UAAU;AAAA,QACX;AAAA,QAED,UAAU;AAAA,UACR,cAAc,EAAE,OAAO,KAAM;AAAA,UAC7B,cAAc,EAAE,OAAO,KAAM;AAAA,UAC7B,cAAc,EAAE,OAAO,KAAM;AAAA,UAC7B,cAAc,EAAE,OAAO,KAAM;AAAA,UAC7B,cAAc,EAAE,OAAO,KAAM;AAAA,UAC7B,eAAe,EAAE,OAAO,EAAK;AAAA,UAC7B,cAAc,EAAE,OAAO,KAAM;AAAA,UAC7B,iBAAiB,EAAE,OAAO,KAAM;AAAA,UAChC,aAAa,EAAE,OAAO,EAAK;AAAA,QAC5B;AAAA,QAED,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,QAMd,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuBxB,CAAO;AAAA,IACF;AAAA,EACF;AA/VD,MAAMA,mBAAN;AACE,gBADIA,kBACG,kBAAiB,IAAI,QAAQ,GAAK,CAAG;AAC5C,gBAFIA,kBAEG,kBAAiB,IAAI,QAAQ,GAAK,CAAG;AA+V9C,SAAOA;AACT,GAAC;"}