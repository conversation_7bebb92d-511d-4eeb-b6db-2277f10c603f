import React, { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';
import ProductFormModal from './modals/ProductFormModal';
import ProductDetailsModal from './modals/ProductDetailsModal';
import ConfirmationModal from '../modals/ConfirmationModal';
import { productsApi } from '../../services/api';
import websocketService from '../../services/websocketService';
import './ProductManagement.css';

const ProductManagement = () => {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [sortBy, setSortBy] = useState('ProductName');
  const [sortDirection, setSortDirection] = useState('ASC');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Modal states
  const [showProductForm, setShowProductForm] = useState(false);
  const [showProductDetails, setShowProductDetails] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [editingProduct, setEditingProduct] = useState(null);

  // Fetch products with filters and pagination
  const fetchProducts = useCallback(async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        limit: pageSize,
        search: searchTerm || undefined,
        category: selectedCategory || undefined,
        status: selectedStatus || undefined,
        sortBy,
        sortDirection
      };

      const response = await productsApi.getProducts(params);

      if (response.success) {
        setProducts(response.data.products || []);
        setTotalCount(response.data.pagination?.totalItems || 0);
        setTotalPages(response.data.pagination?.totalPages || 0);
      } else {
        toast.error('Failed to fetch products');
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      toast.error('Error loading products');
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, searchTerm, selectedCategory, selectedStatus, sortBy, sortDirection]);

  // Fetch categories
  const fetchCategories = useCallback(async () => {
    try {
      const response = await productsApi.getCategories();
      if (response.success) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  }, []);

  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // WebSocket integration for real-time updates
  useEffect(() => {
    // Subscribe to product updates
    websocketService.subscribeToProducts();

    // Set up event listeners
    const handleProductCreated = (data) => {
      toast.success(`New product "${data.ProductName}" has been created`);
      fetchProducts(); // Refresh the product list
    };

    const handleProductUpdated = (data) => {
      toast.info(`Product "${data.ProductName}" has been updated`);
      fetchProducts(); // Refresh the product list
    };

    const handleProductDeleted = (data) => {
      toast.warning(`Product has been deleted`);
      fetchProducts(); // Refresh the product list
    };

    const handleProductFileUploaded = (data) => {
      toast.success(`File uploaded for product ID: ${data.productId}`);
      // Optionally refresh specific product or entire list
      fetchProducts();
    };

    // Register event listeners
    websocketService.on('productCreated', handleProductCreated);
    websocketService.on('productUpdated', handleProductUpdated);
    websocketService.on('productDeleted', handleProductDeleted);
    websocketService.on('productFileUploaded', handleProductFileUploaded);

    // Cleanup function
    return () => {
      websocketService.off('productCreated', handleProductCreated);
      websocketService.off('productUpdated', handleProductUpdated);
      websocketService.off('productDeleted', handleProductDeleted);
      websocketService.off('productFileUploaded', handleProductFileUploaded);
    };
  }, [fetchProducts]);

  // Handle search with debouncing
  const handleSearch = useCallback((value) => {
    setSearchTerm(value);
    setCurrentPage(1);
  }, []);

  // Handle sorting
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');
    } else {
      setSortBy(field);
      setSortDirection('ASC');
    }
    setCurrentPage(1);
  };

  // Handle product actions
  const handleAddProduct = () => {
    setEditingProduct(null);
    setShowProductForm(true);
  };

  const handleEditProduct = (product) => {
    setEditingProduct(product);
    setShowProductForm(true);
  };

  const handleViewProduct = async (productId) => {
    try {
      const response = await productsApi.getProductById(productId);
      if (response.success) {
        setSelectedProduct(response.data);
        setShowProductDetails(true);
      } else {
        toast.error('Failed to load product details');
      }
    } catch (error) {
      console.error('Error fetching product details:', error);
      toast.error('Error loading product details');
    }
  };

  const handleDeleteProduct = (product) => {
    setSelectedProduct(product);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteProduct = async () => {
    try {
      const response = await productsApi.deleteProduct(selectedProduct.ProductID);
      if (response.success) {
        // Emit WebSocket event for real-time updates
        websocketService.notifyProductDeleted(selectedProduct.ProductID);
        toast.success('Product deleted successfully');
        fetchProducts();
      } else {
        toast.error('Failed to delete product');
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Error deleting product');
    } finally {
      setShowDeleteConfirm(false);
      setSelectedProduct(null);
    }
  };

  const handleProductSaved = (productData) => {
    setShowProductForm(false);
    setEditingProduct(null);
    fetchProducts();

    // Emit WebSocket event for real-time updates
    if (editingProduct) {
      websocketService.notifyProductUpdated(productData);
      toast.success('Product updated successfully');
    } else {
      websocketService.notifyProductCreated(productData);
      toast.success('Product created successfully');
    }
  };

  // Utility functions
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'Active': return '#27ae60';
      case 'Draft': return '#f39c12';
      case 'Inactive': return '#95a5a6';
      case 'Discontinued': return '#e74c3c';
      case 'Pending Review': return '#3498db';
      default: return '#95a5a6';
    }
  };

  const getSortIcon = (field) => {
    if (sortBy !== field) return '↕️';
    return sortDirection === 'ASC' ? '↑' : '↓';
  };

  if (loading) {
    return (
      <div className="admin-loading">
        <div className="loading-spinner"></div>
        <p>Loading products...</p>
      </div>
    );
  }

  return (
    <div className="product-management">
      {/* Header */}
      <div className="admin-card-header">
        <h1 className="admin-card-title">Product Management</h1>
        <button
          className="admin-btn admin-btn-primary"
          onClick={handleAddProduct}
        >
          <span className="btn-icon">+</span>
          Add New Product
        </button>
      </div>

      {/* Filters and Search */}
      <div className="admin-card">
        <div className="product-filters">
          <div className="filter-row">
            <div className="filter-group">
              <label htmlFor="search">Search Products</label>
              <input
                id="search"
                type="text"
                placeholder="Search by name, code, or description..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="admin-input"
              />
            </div>

            <div className="filter-group">
              <label htmlFor="category">Category</label>
              <select
                id="category"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="admin-select"
              >
                <option value="">All Categories</option>
                {categories.map(category => (
                  <option key={category.CategoryID} value={category.CategoryID}>
                    {category.CategoryName}
                  </option>
                ))}
              </select>
            </div>

            <div className="filter-group">
              <label htmlFor="status">Status</label>
              <select
                id="status"
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="admin-select"
              >
                <option value="">All Statuses</option>
                <option value="Active">Active</option>
                <option value="Draft">Draft</option>
                <option value="Inactive">Inactive</option>
                <option value="Discontinued">Discontinued</option>
                <option value="Pending Review">Pending Review</option>
              </select>
            </div>

            <div className="filter-group">
              <label htmlFor="pageSize">Items per page</label>
              <select
                id="pageSize"
                value={pageSize}
                onChange={(e) => {
                  setPageSize(parseInt(e.target.value));
                  setCurrentPage(1);
                }}
                className="admin-select"
              >
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Products Table */}
      <div className="admin-card">
        <div className="table-container">
          <table className="admin-table">
            <thead>
              <tr>
                <th
                  className="sortable"
                  onClick={() => handleSort('ProductName')}
                >
                  Product Name {getSortIcon('ProductName')}
                </th>
                <th>Category</th>
                <th
                  className="sortable"
                  onClick={() => handleSort('BasePrice')}
                >
                  Price {getSortIcon('BasePrice')}
                </th>
                <th
                  className="sortable"
                  onClick={() => handleSort('Status')}
                >
                  Status {getSortIcon('Status')}
                </th>
                <th>Files</th>
                <th
                  className="sortable"
                  onClick={() => handleSort('UpdatedAt')}
                >
                  Last Updated {getSortIcon('UpdatedAt')}
                </th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {products.length === 0 ? (
                <tr>
                  <td colSpan="7" className="no-data">
                    No products found
                  </td>
                </tr>
              ) : (
                products.map(product => (
                  <tr key={product.ProductID}>
                    <td>
                      <div className="product-info">
                        <strong>{product.ProductName}</strong>
                        <small>{product.ProductCode}</small>
                      </div>
                    </td>
                    <td>{product.CategoryName}</td>
                    <td>{formatCurrency(product.BasePrice)}</td>
                    <td>
                      <span
                        className="status-badge"
                        style={{ backgroundColor: getStatusBadgeColor(product.Status) }}
                      >
                        {product.Status}
                      </span>
                    </td>
                    <td>
                      <div className="file-counts">
                        <span className="file-count">
                          📷 {product.ImageCount || 0}
                        </span>
                        <span className="file-count">
                          🎯 {product.ModelCount || 0}
                        </span>
                      </div>
                    </td>
                    <td>
                      {product.UpdatedAt ? new Date(product.UpdatedAt).toLocaleDateString() : '-'}
                    </td>
                    <td>
                      <div className="action-buttons">
                        <button
                          className="admin-btn admin-btn-secondary btn-small"
                          onClick={() => handleViewProduct(product.ProductID)}
                          title="View Details"
                        >
                          👁️
                        </button>
                        <button
                          className="admin-btn admin-btn-secondary btn-small"
                          onClick={() => handleEditProduct(product)}
                          title="Edit Product"
                        >
                          ✏️
                        </button>
                        <button
                          className="admin-btn admin-btn-danger btn-small"
                          onClick={() => handleDeleteProduct(product)}
                          title="Delete Product"
                        >
                          🗑️
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="pagination">
            <div className="pagination-info">
              Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} products
            </div>
            <div className="pagination-controls">
              <button
                className="admin-btn admin-btn-secondary btn-small"
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
              >
                First
              </button>
              <button
                className="admin-btn admin-btn-secondary btn-small"
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Previous
              </button>
              <span className="page-info">
                Page {currentPage} of {totalPages}
              </span>
              <button
                className="admin-btn admin-btn-secondary btn-small"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
              </button>
              <button
                className="admin-btn admin-btn-secondary btn-small"
                onClick={() => setCurrentPage(totalPages)}
                disabled={currentPage === totalPages}
              >
                Last
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      {showProductForm && (
        <ProductFormModal
          product={editingProduct}
          categories={categories}
          onSave={handleProductSaved}
          onClose={() => {
            setShowProductForm(false);
            setEditingProduct(null);
          }}
        />
      )}

      {showProductDetails && selectedProduct && (
        <ProductDetailsModal
          product={selectedProduct}
          onClose={() => {
            setShowProductDetails(false);
            setSelectedProduct(null);
          }}
          onEdit={() => {
            setEditingProduct(selectedProduct.product);
            setShowProductDetails(false);
            setShowProductForm(true);
          }}
        />
      )}

      {showDeleteConfirm && selectedProduct && (
        <ConfirmationModal
          title="Delete Product"
          message={`Are you sure you want to delete "${selectedProduct.ProductName}"? This action cannot be undone.`}
          confirmText="Delete"
          cancelText="Cancel"
          onConfirm={confirmDeleteProduct}
          onCancel={() => {
            setShowDeleteConfirm(false);
            setSelectedProduct(null);
          }}
          type="danger"
        />
      )}
    </div>
  );
};

export default ProductManagement;
