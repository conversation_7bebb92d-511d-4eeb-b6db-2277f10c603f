import * as React from 'react';
import * as THREE from 'three';
export declare type MediaPipeFaceMesh = typeof FacemeshDatas.SAMPLE_FACE;
export declare type FacemeshProps = {
    face?: MediaPipeFaceMesh;
    width?: number;
    height?: number;
    depth?: number;
    verticalTri?: [number, number, number];
    origin?: number;
    debug?: boolean;
} & JSX.IntrinsicElements['group'];
export declare type FacemeshApi = {
    meshRef: React.RefObject<THREE.Mesh>;
    outerRef: React.RefObject<THREE.Group>;
};
export declare const Facemesh: React.ForwardRefExoticComponent<Pick<FacemeshProps, "visible" | "attach" | "args" | "children" | "key" | "onUpdate" | "position" | "up" | "scale" | "rotation" | "matrix" | "quaternion" | "layers" | "dispose" | "type" | "isGroup" | "id" | "uuid" | "name" | "parent" | "modelViewMatrix" | "normalMatrix" | "matrixWorld" | "matrixAutoUpdate" | "matrixWorldAutoUpdate" | "matrixWorldNeedsUpdate" | "castShadow" | "receiveShadow" | "frustumCulled" | "renderOrder" | "animations" | "userData" | "customDepthMaterial" | "customDistanceMaterial" | "isObject3D" | "onBeforeRender" | "onAfterRender" | "applyMatrix4" | "applyQuaternion" | "setRotationFromAxisAngle" | "setRotationFromEuler" | "setRotationFromMatrix" | "setRotationFromQuaternion" | "rotateOnAxis" | "rotateOnWorldAxis" | "rotateX" | "rotateY" | "rotateZ" | "translateOnAxis" | "translateX" | "translateY" | "translateZ" | "localToWorld" | "worldToLocal" | "lookAt" | "add" | "remove" | "removeFromParent" | "clear" | "getObjectById" | "getObjectByName" | "getObjectByProperty" | "getObjectsByProperty" | "getWorldPosition" | "getWorldQuaternion" | "getWorldScale" | "getWorldDirection" | "raycast" | "traverse" | "traverseVisible" | "traverseAncestors" | "updateMatrix" | "updateMatrixWorld" | "updateWorldMatrix" | "toJSON" | "clone" | "copy" | "addEventListener" | "hasEventListener" | "removeEventListener" | "dispatchEvent" | keyof import("@react-three/fiber/dist/declarations/src/core/events").EventHandlers | "width" | "height" | "depth" | "debug" | "face" | "verticalTri" | "origin"> & React.RefAttributes<FacemeshApi>>;
export declare const FacemeshDatas: {
    SAMPLE_FACE: {
        keypoints: ({
            x: number;
            y: number;
            z: number;
            name: string;
        } | {
            x: number;
            y: number;
            z: number;
            name?: undefined;
        })[];
        box: {
            xMin: number;
            yMin: number;
            xMax: number;
            yMax: number;
            width: number;
            height: number;
        };
    };
    TRIANGULATION: number[];
};
