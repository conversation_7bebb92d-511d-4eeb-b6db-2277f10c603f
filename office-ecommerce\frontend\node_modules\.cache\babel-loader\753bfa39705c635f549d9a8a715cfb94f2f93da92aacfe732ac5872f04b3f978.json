{"ast": null, "code": "import { addEffect, applyProps } from '@react-three/fiber';\nimport { Globals } from '@react-spring/core';\nexport * from '@react-spring/core';\nimport { createStringInterpolator, colors, raf } from '@react-spring/shared';\nimport { createHost } from '@react-spring/animated';\nimport * as THREE from 'three';\nconst primitives = ['primitive'].concat(Object.keys(THREE).filter(key => /^[A-Z]/.test(key)).map(key => key[0].toLowerCase() + key.slice(1)));\nGlobals.assign({\n  createStringInterpolator,\n  colors,\n  frameLoop: 'demand'\n});\naddEffect(() => {\n  raf.advance();\n});\nconst host = createHost(primitives, {\n  applyAnimatedValues: applyProps\n});\nconst animated = host.animated;\nexport { animated as a, animated };", "map": {"version": 3, "names": ["addEffect", "applyProps", "Globals", "createStringInterpolator", "colors", "raf", "createHost", "THREE", "primitives", "concat", "Object", "keys", "filter", "key", "test", "map", "toLowerCase", "slice", "assign", "frameLoop", "advance", "host", "applyAnimatedValues", "animated", "a"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-spring/three/dist/react-spring-three.esm.js"], "sourcesContent": ["import { addEffect, applyProps } from '@react-three/fiber';\nimport { Globals } from '@react-spring/core';\nexport * from '@react-spring/core';\nimport { createStringInterpolator, colors, raf } from '@react-spring/shared';\nimport { createHost } from '@react-spring/animated';\nimport * as THREE from 'three';\n\nconst primitives = ['primitive'].concat(Object.keys(THREE).filter(key => /^[A-Z]/.test(key)).map(key => key[0].toLowerCase() + key.slice(1)));\n\nGlobals.assign({\n  createStringInterpolator,\n  colors,\n  frameLoop: 'demand'\n});\naddEffect(() => {\n  raf.advance();\n});\nconst host = createHost(primitives, {\n  applyAnimatedValues: applyProps\n});\nconst animated = host.animated;\n\nexport { animated as a, animated };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,UAAU,QAAQ,oBAAoB;AAC1D,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,cAAc,oBAAoB;AAClC,SAASC,wBAAwB,EAAEC,MAAM,EAAEC,GAAG,QAAQ,sBAAsB;AAC5E,SAASC,UAAU,QAAQ,wBAAwB;AACnD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,MAAMC,UAAU,GAAG,CAAC,WAAW,CAAC,CAACC,MAAM,CAACC,MAAM,CAACC,IAAI,CAACJ,KAAK,CAAC,CAACK,MAAM,CAACC,GAAG,IAAI,QAAQ,CAACC,IAAI,CAACD,GAAG,CAAC,CAAC,CAACE,GAAG,CAACF,GAAG,IAAIA,GAAG,CAAC,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,GAAGH,GAAG,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAE7If,OAAO,CAACgB,MAAM,CAAC;EACbf,wBAAwB;EACxBC,MAAM;EACNe,SAAS,EAAE;AACb,CAAC,CAAC;AACFnB,SAAS,CAAC,MAAM;EACdK,GAAG,CAACe,OAAO,CAAC,CAAC;AACf,CAAC,CAAC;AACF,MAAMC,IAAI,GAAGf,UAAU,CAACE,UAAU,EAAE;EAClCc,mBAAmB,EAAErB;AACvB,CAAC,CAAC;AACF,MAAMsB,QAAQ,GAAGF,IAAI,CAACE,QAAQ;AAE9B,SAASA,QAAQ,IAAIC,CAAC,EAAED,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}