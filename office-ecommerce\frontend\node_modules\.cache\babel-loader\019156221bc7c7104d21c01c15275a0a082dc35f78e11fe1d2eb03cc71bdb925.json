{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { FirstPersonControls as FirstPersonControls$1 } from 'three-stdlib';\nconst FirstPersonControls = /*#__PURE__*/React.forwardRef(({\n  domElement,\n  makeDefault,\n  ...props\n}, ref) => {\n  const camera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const get = useThree(state => state.get);\n  const set = useThree(state => state.set);\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const [controls] = React.useState(() => new FirstPersonControls$1(camera, explDomElement));\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  useFrame((_, delta) => {\n    controls.update(delta);\n  }, -1);\n  return controls ? /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls\n  }, props)) : null;\n});\nexport { FirstPersonControls };", "map": {"version": 3, "names": ["_extends", "React", "useThree", "useFrame", "FirstPersonControls", "FirstPersonControls$1", "forwardRef", "dom<PERSON>lement", "makeDefault", "props", "ref", "camera", "state", "gl", "events", "get", "set", "explDomElement", "connected", "controls", "useState", "useEffect", "old", "_", "delta", "update", "createElement", "object"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/FirstPersonControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { FirstPersonControls as FirstPersonControls$1 } from 'three-stdlib';\n\nconst FirstPersonControls = /*#__PURE__*/React.forwardRef(({\n  domElement,\n  makeDefault,\n  ...props\n}, ref) => {\n  const camera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const get = useThree(state => state.get);\n  const set = useThree(state => state.set);\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const [controls] = React.useState(() => new FirstPersonControls$1(camera, explDomElement));\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  useFrame((_, delta) => {\n    controls.update(delta);\n  }, -1);\n  return controls ? /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls\n  }, props)) : null;\n});\n\nexport { FirstPersonControls };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,SAASC,mBAAmB,IAAIC,qBAAqB,QAAQ,cAAc;AAE3E,MAAMD,mBAAmB,GAAG,aAAaH,KAAK,CAACK,UAAU,CAAC,CAAC;EACzDC,UAAU;EACVC,WAAW;EACX,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,MAAM,GAAGT,QAAQ,CAACU,KAAK,IAAIA,KAAK,CAACD,MAAM,CAAC;EAC9C,MAAME,EAAE,GAAGX,QAAQ,CAACU,KAAK,IAAIA,KAAK,CAACC,EAAE,CAAC;EACtC,MAAMC,MAAM,GAAGZ,QAAQ,CAACU,KAAK,IAAIA,KAAK,CAACE,MAAM,CAAC;EAC9C,MAAMC,GAAG,GAAGb,QAAQ,CAACU,KAAK,IAAIA,KAAK,CAACG,GAAG,CAAC;EACxC,MAAMC,GAAG,GAAGd,QAAQ,CAACU,KAAK,IAAIA,KAAK,CAACI,GAAG,CAAC;EACxC,MAAMC,cAAc,GAAGV,UAAU,IAAIO,MAAM,CAACI,SAAS,IAAIL,EAAE,CAACN,UAAU;EACtE,MAAM,CAACY,QAAQ,CAAC,GAAGlB,KAAK,CAACmB,QAAQ,CAAC,MAAM,IAAIf,qBAAqB,CAACM,MAAM,EAAEM,cAAc,CAAC,CAAC;EAC1FhB,KAAK,CAACoB,SAAS,CAAC,MAAM;IACpB,IAAIb,WAAW,EAAE;MACf,MAAMc,GAAG,GAAGP,GAAG,CAAC,CAAC,CAACI,QAAQ;MAC1BH,GAAG,CAAC;QACFG;MACF,CAAC,CAAC;MACF,OAAO,MAAMH,GAAG,CAAC;QACfG,QAAQ,EAAEG;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACd,WAAW,EAAEW,QAAQ,CAAC,CAAC;EAC3BhB,QAAQ,CAAC,CAACoB,CAAC,EAAEC,KAAK,KAAK;IACrBL,QAAQ,CAACM,MAAM,CAACD,KAAK,CAAC;EACxB,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,OAAOL,QAAQ,GAAG,aAAalB,KAAK,CAACyB,aAAa,CAAC,WAAW,EAAE1B,QAAQ,CAAC;IACvEU,GAAG,EAAEA,GAAG;IACRiB,MAAM,EAAER;EACV,CAAC,EAAEV,KAAK,CAAC,CAAC,GAAG,IAAI;AACnB,CAAC,CAAC;AAEF,SAASL,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}