{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useRef, useMemo, useLayoutEffect } from 'react';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { MeshBVHUniformStruct, MeshBVH, SAH } from 'three-mesh-bvh';\nimport { MeshRefractionMaterial as MeshRefractionMaterial$1 } from '../materials/MeshRefractionMaterial.js';\nconst isCubeTexture = def => def && def.isCubeTexture;\nfunction MeshRefractionMaterial({\n  aberrationStrength = 0,\n  fastChroma = true,\n  envMap,\n  ...props\n}) {\n  extend({\n    MeshRefractionMaterial: MeshRefractionMaterial$1\n  });\n  const material = useRef();\n  const {\n    size\n  } = useThree();\n  const defines = useMemo(() => {\n    var _ref, _envMap$image$;\n    const temp = {}; // Sampler2D and SamplerCube need different defines\n\n    const isCubeMap = isCubeTexture(envMap);\n    const w = (_ref = isCubeMap ? (_envMap$image$ = envMap.image[0]) == null ? void 0 : _envMap$image$.width : envMap.image.width) !== null && _ref !== void 0 ? _ref : 1024;\n    const cubeSize = w / 4;\n    const _lodMax = Math.floor(Math.log2(cubeSize));\n    const _cubeSize = Math.pow(2, _lodMax);\n    const width = 3 * Math.max(_cubeSize, 16 * 7);\n    const height = 4 * _cubeSize;\n    if (isCubeMap) temp.ENVMAP_TYPE_CUBEM = '';\n    temp.CUBEUV_TEXEL_WIDTH = `${1.0 / width}`;\n    temp.CUBEUV_TEXEL_HEIGHT = `${1.0 / height}`;\n    temp.CUBEUV_MAX_MIP = `${_lodMax}.0`; // Add defines from chromatic aberration\n\n    if (aberrationStrength > 0) temp.CHROMATIC_ABERRATIONS = '';\n    if (fastChroma) temp.FAST_CHROMA = '';\n    return temp;\n  }, [aberrationStrength, fastChroma]);\n  useLayoutEffect(() => {\n    var _material$current, _material$current$__r, _material$current$__r2;\n\n    // Get the geometry of this materials parent\n    const geometry = (_material$current = material.current) == null ? void 0 : (_material$current$__r = _material$current.__r3f) == null ? void 0 : (_material$current$__r2 = _material$current$__r.parent) == null ? void 0 : _material$current$__r2.geometry; // Update the BVH\n\n    if (geometry) {\n      material.current.bvh = new MeshBVHUniformStruct();\n      material.current.bvh.updateFrom(new MeshBVH(geometry.clone().toNonIndexed(), {\n        lazyGeneration: false,\n        strategy: SAH\n      }));\n    }\n  }, []);\n  useFrame(({\n    camera\n  }) => {\n    material.current.viewMatrixInverse = camera.matrixWorld;\n    material.current.projectionMatrixInverse = camera.projectionMatrixInverse;\n  });\n  return /*#__PURE__*/React.createElement(\"meshRefractionMaterial\", _extends({\n    // @ts-ignore\n    key: JSON.stringify(defines) // @ts-ignore\n    ,\n\n    defines: defines,\n    ref: material,\n    resolution: [size.width, size.height],\n    aberrationStrength: aberrationStrength,\n    envMap: envMap\n  }, props));\n}\nexport { MeshRefractionMaterial };", "map": {"version": 3, "names": ["_extends", "React", "useRef", "useMemo", "useLayoutEffect", "extend", "useThree", "useFrame", "MeshBVHUniformStruct", "MeshBVH", "SAH", "MeshRefractionMaterial", "MeshRefractionMaterial$1", "isCubeTexture", "def", "aberrationStrength", "fastChroma", "envMap", "props", "material", "size", "defines", "_ref", "_envMap$image$", "temp", "isCubeMap", "w", "image", "width", "cubeSize", "_lodMax", "Math", "floor", "log2", "_cubeSize", "pow", "max", "height", "ENVMAP_TYPE_CUBEM", "CUBEUV_TEXEL_WIDTH", "CUBEUV_TEXEL_HEIGHT", "CUBEUV_MAX_MIP", "CHROMATIC_ABERRATIONS", "FAST_CHROMA", "_material$current", "_material$current$__r", "_material$current$__r2", "geometry", "current", "__r3f", "parent", "bvh", "updateFrom", "clone", "toNonIndexed", "lazyGeneration", "strategy", "camera", "viewMatrixInverse", "matrixWorld", "projectionMatrixInverse", "createElement", "key", "JSON", "stringify", "ref", "resolution"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/MeshRefractionMaterial.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useRef, useMemo, useLayoutEffect } from 'react';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { MeshBVHUniformStruct, MeshBVH, SAH } from 'three-mesh-bvh';\nimport { MeshRefractionMaterial as MeshRefractionMaterial$1 } from '../materials/MeshRefractionMaterial.js';\n\nconst isCubeTexture = def => def && def.isCubeTexture;\n\nfunction MeshRefractionMaterial({\n  aberrationStrength = 0,\n  fastChroma = true,\n  envMap,\n  ...props\n}) {\n  extend({\n    MeshRefractionMaterial: MeshRefractionMaterial$1\n  });\n  const material = useRef();\n  const {\n    size\n  } = useThree();\n  const defines = useMemo(() => {\n    var _ref, _envMap$image$;\n\n    const temp = {}; // Sampler2D and SamplerCube need different defines\n\n    const isCubeMap = isCubeTexture(envMap);\n    const w = (_ref = isCubeMap ? (_envMap$image$ = envMap.image[0]) == null ? void 0 : _envMap$image$.width : envMap.image.width) !== null && _ref !== void 0 ? _ref : 1024;\n    const cubeSize = w / 4;\n\n    const _lodMax = Math.floor(Math.log2(cubeSize));\n\n    const _cubeSize = Math.pow(2, _lodMax);\n\n    const width = 3 * Math.max(_cubeSize, 16 * 7);\n    const height = 4 * _cubeSize;\n    if (isCubeMap) temp.ENVMAP_TYPE_CUBEM = '';\n    temp.CUBEUV_TEXEL_WIDTH = `${1.0 / width}`;\n    temp.CUBEUV_TEXEL_HEIGHT = `${1.0 / height}`;\n    temp.CUBEUV_MAX_MIP = `${_lodMax}.0`; // Add defines from chromatic aberration\n\n    if (aberrationStrength > 0) temp.CHROMATIC_ABERRATIONS = '';\n    if (fastChroma) temp.FAST_CHROMA = '';\n    return temp;\n  }, [aberrationStrength, fastChroma]);\n  useLayoutEffect(() => {\n    var _material$current, _material$current$__r, _material$current$__r2;\n\n    // Get the geometry of this materials parent\n    const geometry = (_material$current = material.current) == null ? void 0 : (_material$current$__r = _material$current.__r3f) == null ? void 0 : (_material$current$__r2 = _material$current$__r.parent) == null ? void 0 : _material$current$__r2.geometry; // Update the BVH\n\n    if (geometry) {\n      material.current.bvh = new MeshBVHUniformStruct();\n      material.current.bvh.updateFrom(new MeshBVH(geometry.clone().toNonIndexed(), {\n        lazyGeneration: false,\n        strategy: SAH\n      }));\n    }\n  }, []);\n  useFrame(({\n    camera\n  }) => {\n    material.current.viewMatrixInverse = camera.matrixWorld;\n    material.current.projectionMatrixInverse = camera.projectionMatrixInverse;\n  });\n  return /*#__PURE__*/React.createElement(\"meshRefractionMaterial\", _extends({\n    // @ts-ignore\n    key: JSON.stringify(defines) // @ts-ignore\n    ,\n    defines: defines,\n    ref: material,\n    resolution: [size.width, size.height],\n    aberrationStrength: aberrationStrength,\n    envMap: envMap\n  }, props));\n}\n\nexport { MeshRefractionMaterial };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,OAAO,EAAEC,eAAe,QAAQ,OAAO;AACxD,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAC/D,SAASC,oBAAoB,EAAEC,OAAO,EAAEC,GAAG,QAAQ,gBAAgB;AACnE,SAASC,sBAAsB,IAAIC,wBAAwB,QAAQ,wCAAwC;AAE3G,MAAMC,aAAa,GAAGC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACD,aAAa;AAErD,SAASF,sBAAsBA,CAAC;EAC9BI,kBAAkB,GAAG,CAAC;EACtBC,UAAU,GAAG,IAAI;EACjBC,MAAM;EACN,GAAGC;AACL,CAAC,EAAE;EACDb,MAAM,CAAC;IACLM,sBAAsB,EAAEC;EAC1B,CAAC,CAAC;EACF,MAAMO,QAAQ,GAAGjB,MAAM,CAAC,CAAC;EACzB,MAAM;IACJkB;EACF,CAAC,GAAGd,QAAQ,CAAC,CAAC;EACd,MAAMe,OAAO,GAAGlB,OAAO,CAAC,MAAM;IAC5B,IAAImB,IAAI,EAAEC,cAAc;IAExB,MAAMC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEjB,MAAMC,SAAS,GAAGZ,aAAa,CAACI,MAAM,CAAC;IACvC,MAAMS,CAAC,GAAG,CAACJ,IAAI,GAAGG,SAAS,GAAG,CAACF,cAAc,GAAGN,MAAM,CAACU,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,cAAc,CAACK,KAAK,GAAGX,MAAM,CAACU,KAAK,CAACC,KAAK,MAAM,IAAI,IAAIN,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,IAAI;IACxK,MAAMO,QAAQ,GAAGH,CAAC,GAAG,CAAC;IAEtB,MAAMI,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,IAAI,CAACJ,QAAQ,CAAC,CAAC;IAE/C,MAAMK,SAAS,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEL,OAAO,CAAC;IAEtC,MAAMF,KAAK,GAAG,CAAC,GAAGG,IAAI,CAACK,GAAG,CAACF,SAAS,EAAE,EAAE,GAAG,CAAC,CAAC;IAC7C,MAAMG,MAAM,GAAG,CAAC,GAAGH,SAAS;IAC5B,IAAIT,SAAS,EAAED,IAAI,CAACc,iBAAiB,GAAG,EAAE;IAC1Cd,IAAI,CAACe,kBAAkB,GAAG,GAAG,GAAG,GAAGX,KAAK,EAAE;IAC1CJ,IAAI,CAACgB,mBAAmB,GAAG,GAAG,GAAG,GAAGH,MAAM,EAAE;IAC5Cb,IAAI,CAACiB,cAAc,GAAG,GAAGX,OAAO,IAAI,CAAC,CAAC;;IAEtC,IAAIf,kBAAkB,GAAG,CAAC,EAAES,IAAI,CAACkB,qBAAqB,GAAG,EAAE;IAC3D,IAAI1B,UAAU,EAAEQ,IAAI,CAACmB,WAAW,GAAG,EAAE;IACrC,OAAOnB,IAAI;EACb,CAAC,EAAE,CAACT,kBAAkB,EAAEC,UAAU,CAAC,CAAC;EACpCZ,eAAe,CAAC,MAAM;IACpB,IAAIwC,iBAAiB,EAAEC,qBAAqB,EAAEC,sBAAsB;;IAEpE;IACA,MAAMC,QAAQ,GAAG,CAACH,iBAAiB,GAAGzB,QAAQ,CAAC6B,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAACH,qBAAqB,GAAGD,iBAAiB,CAACK,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAACH,sBAAsB,GAAGD,qBAAqB,CAACK,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,sBAAsB,CAACC,QAAQ,CAAC,CAAC;;IAE5P,IAAIA,QAAQ,EAAE;MACZ5B,QAAQ,CAAC6B,OAAO,CAACG,GAAG,GAAG,IAAI3C,oBAAoB,CAAC,CAAC;MACjDW,QAAQ,CAAC6B,OAAO,CAACG,GAAG,CAACC,UAAU,CAAC,IAAI3C,OAAO,CAACsC,QAAQ,CAACM,KAAK,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,EAAE;QAC3EC,cAAc,EAAE,KAAK;QACrBC,QAAQ,EAAE9C;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,EAAE,CAAC;EACNH,QAAQ,CAAC,CAAC;IACRkD;EACF,CAAC,KAAK;IACJtC,QAAQ,CAAC6B,OAAO,CAACU,iBAAiB,GAAGD,MAAM,CAACE,WAAW;IACvDxC,QAAQ,CAAC6B,OAAO,CAACY,uBAAuB,GAAGH,MAAM,CAACG,uBAAuB;EAC3E,CAAC,CAAC;EACF,OAAO,aAAa3D,KAAK,CAAC4D,aAAa,CAAC,wBAAwB,EAAE7D,QAAQ,CAAC;IACzE;IACA8D,GAAG,EAAEC,IAAI,CAACC,SAAS,CAAC3C,OAAO,CAAC,CAAC;IAAA;;IAE7BA,OAAO,EAAEA,OAAO;IAChB4C,GAAG,EAAE9C,QAAQ;IACb+C,UAAU,EAAE,CAAC9C,IAAI,CAACQ,KAAK,EAAER,IAAI,CAACiB,MAAM,CAAC;IACrCtB,kBAAkB,EAAEA,kBAAkB;IACtCE,MAAM,EAAEA;EACV,CAAC,EAAEC,KAAK,CAAC,CAAC;AACZ;AAEA,SAASP,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}