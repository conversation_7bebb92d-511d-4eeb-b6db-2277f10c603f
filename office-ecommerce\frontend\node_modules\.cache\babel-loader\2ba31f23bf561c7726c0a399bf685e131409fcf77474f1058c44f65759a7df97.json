{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useGLTF } from './useGLTF.js';\nimport { Clone } from './Clone.js';\nconst Gltf = /*#__PURE__*/React.forwardRef(({\n  src,\n  ...props\n}, ref) => {\n  const {\n    scene\n  } = useGLTF(src);\n  return /*#__PURE__*/React.createElement(Clone, _extends({\n    ref: ref\n  }, props, {\n    object: scene\n  }));\n});\nexport { Gltf };", "map": {"version": 3, "names": ["_extends", "React", "useGLTF", "<PERSON><PERSON>", "Gltf", "forwardRef", "src", "props", "ref", "scene", "createElement", "object"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Gltf.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useGLTF } from './useGLTF.js';\nimport { Clone } from './Clone.js';\n\nconst Gltf = /*#__PURE__*/React.forwardRef(({\n  src,\n  ...props\n}, ref) => {\n  const {\n    scene\n  } = useGLTF(src);\n  return /*#__PURE__*/React.createElement(Clone, _extends({\n    ref: ref\n  }, props, {\n    object: scene\n  }));\n});\n\nexport { Gltf };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,KAAK,QAAQ,YAAY;AAElC,MAAMC,IAAI,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,CAAC;EAC1CC,GAAG;EACH,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM;IACJC;EACF,CAAC,GAAGP,OAAO,CAACI,GAAG,CAAC;EAChB,OAAO,aAAaL,KAAK,CAACS,aAAa,CAACP,KAAK,EAAEH,QAAQ,CAAC;IACtDQ,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,EAAE;IACRI,MAAM,EAAEF;EACV,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASL,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}