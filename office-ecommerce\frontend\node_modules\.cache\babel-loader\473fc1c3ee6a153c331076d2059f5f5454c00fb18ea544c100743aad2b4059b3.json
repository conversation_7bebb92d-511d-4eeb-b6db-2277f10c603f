{"ast": null, "code": "import * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { Billboard } from './Billboard.js';\nimport { Plane } from './shapes.js';\nimport { useTexture } from './useTexture.js';\nconst CLOUD_URL = 'https://rawcdn.githack.com/pmndrs/drei-assets/9225a9f1fbd449d9411125c2f419b843d0308c9f/cloud.png';\nfunction Cloud({\n  opacity = 0.5,\n  speed = 0.4,\n  width = 10,\n  depth = 1.5,\n  segments = 20,\n  texture = CLOUD_URL,\n  color = '#ffffff',\n  depthTest = true,\n  ...props\n}) {\n  const group = React.useRef();\n  const cloudTexture = useTexture(texture);\n  const clouds = React.useMemo(() => [...new Array(segments)].map((_, index) => ({\n    x: width / 2 - Math.random() * width,\n    y: width / 2 - Math.random() * width,\n    scale: 0.4 + Math.sin((index + 1) / segments * Math.PI) * ((0.2 + Math.random()) * 10),\n    density: Math.max(0.2, Math.random()),\n    rotation: Math.max(0.002, 0.005 * Math.random()) * speed\n  })), [width, segments, speed]);\n  useFrame(state => {\n    var _group$current;\n    return (_group$current = group.current) == null ? void 0 : _group$current.children.forEach((cloud, index) => {\n      cloud.children[0].rotation.z += clouds[index].rotation;\n      cloud.children[0].scale.setScalar(clouds[index].scale + (1 + Math.sin(state.clock.getElapsedTime() / 10)) / 2 * index / 10);\n    });\n  });\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"group\", {\n    position: [0, 0, segments / 2 * depth],\n    ref: group\n  }, clouds.map(({\n    x,\n    y,\n    scale,\n    density\n  }, index) => /*#__PURE__*/React.createElement(Billboard, {\n    key: index,\n    position: [x, y, -index * depth]\n  }, /*#__PURE__*/React.createElement(Plane, {\n    scale: scale,\n    rotation: [0, 0, 0]\n  }, /*#__PURE__*/React.createElement(\"meshStandardMaterial\", {\n    map: cloudTexture,\n    transparent: true,\n    opacity: scale / 6 * density * opacity,\n    depthTest: depthTest,\n    color: color\n  }))))));\n}\nexport { Cloud };", "map": {"version": 3, "names": ["React", "useFrame", "Billboard", "Plane", "useTexture", "CLOUD_URL", "Cloud", "opacity", "speed", "width", "depth", "segments", "texture", "color", "depthTest", "props", "group", "useRef", "cloudTexture", "clouds", "useMemo", "Array", "map", "_", "index", "x", "Math", "random", "y", "scale", "sin", "PI", "density", "max", "rotation", "state", "_group$current", "current", "children", "for<PERSON>ach", "cloud", "z", "setScalar", "clock", "getElapsedTime", "createElement", "position", "ref", "key", "transparent"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Cloud.js"], "sourcesContent": ["import * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { Billboard } from './Billboard.js';\nimport { Plane } from './shapes.js';\nimport { useTexture } from './useTexture.js';\n\nconst CLOUD_URL = 'https://rawcdn.githack.com/pmndrs/drei-assets/9225a9f1fbd449d9411125c2f419b843d0308c9f/cloud.png';\nfunction Cloud({\n  opacity = 0.5,\n  speed = 0.4,\n  width = 10,\n  depth = 1.5,\n  segments = 20,\n  texture = CLOUD_URL,\n  color = '#ffffff',\n  depthTest = true,\n  ...props\n}) {\n  const group = React.useRef();\n  const cloudTexture = useTexture(texture);\n  const clouds = React.useMemo(() => [...new Array(segments)].map((_, index) => ({\n    x: width / 2 - Math.random() * width,\n    y: width / 2 - Math.random() * width,\n    scale: 0.4 + Math.sin((index + 1) / segments * Math.PI) * ((0.2 + Math.random()) * 10),\n    density: Math.max(0.2, Math.random()),\n    rotation: Math.max(0.002, 0.005 * Math.random()) * speed\n  })), [width, segments, speed]);\n  useFrame(state => {\n    var _group$current;\n\n    return (_group$current = group.current) == null ? void 0 : _group$current.children.forEach((cloud, index) => {\n      cloud.children[0].rotation.z += clouds[index].rotation;\n      cloud.children[0].scale.setScalar(clouds[index].scale + (1 + Math.sin(state.clock.getElapsedTime() / 10)) / 2 * index / 10);\n    });\n  });\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"group\", {\n    position: [0, 0, segments / 2 * depth],\n    ref: group\n  }, clouds.map(({\n    x,\n    y,\n    scale,\n    density\n  }, index) => /*#__PURE__*/React.createElement(Billboard, {\n    key: index,\n    position: [x, y, -index * depth]\n  }, /*#__PURE__*/React.createElement(Plane, {\n    scale: scale,\n    rotation: [0, 0, 0]\n  }, /*#__PURE__*/React.createElement(\"meshStandardMaterial\", {\n    map: cloudTexture,\n    transparent: true,\n    opacity: scale / 6 * density * opacity,\n    depthTest: depthTest,\n    color: color\n  }))))));\n}\n\nexport { Cloud };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,UAAU,QAAQ,iBAAiB;AAE5C,MAAMC,SAAS,GAAG,kGAAkG;AACpH,SAASC,KAAKA,CAAC;EACbC,OAAO,GAAG,GAAG;EACbC,KAAK,GAAG,GAAG;EACXC,KAAK,GAAG,EAAE;EACVC,KAAK,GAAG,GAAG;EACXC,QAAQ,GAAG,EAAE;EACbC,OAAO,GAAGP,SAAS;EACnBQ,KAAK,GAAG,SAAS;EACjBC,SAAS,GAAG,IAAI;EAChB,GAAGC;AACL,CAAC,EAAE;EACD,MAAMC,KAAK,GAAGhB,KAAK,CAACiB,MAAM,CAAC,CAAC;EAC5B,MAAMC,YAAY,GAAGd,UAAU,CAACQ,OAAO,CAAC;EACxC,MAAMO,MAAM,GAAGnB,KAAK,CAACoB,OAAO,CAAC,MAAM,CAAC,GAAG,IAAIC,KAAK,CAACV,QAAQ,CAAC,CAAC,CAACW,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,MAAM;IAC7EC,CAAC,EAAEhB,KAAK,GAAG,CAAC,GAAGiB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGlB,KAAK;IACpCmB,CAAC,EAAEnB,KAAK,GAAG,CAAC,GAAGiB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGlB,KAAK;IACpCoB,KAAK,EAAE,GAAG,GAAGH,IAAI,CAACI,GAAG,CAAC,CAACN,KAAK,GAAG,CAAC,IAAIb,QAAQ,GAAGe,IAAI,CAACK,EAAE,CAAC,IAAI,CAAC,GAAG,GAAGL,IAAI,CAACC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;IACtFK,OAAO,EAAEN,IAAI,CAACO,GAAG,CAAC,GAAG,EAAEP,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC;IACrCO,QAAQ,EAAER,IAAI,CAACO,GAAG,CAAC,KAAK,EAAE,KAAK,GAAGP,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,GAAGnB;EACrD,CAAC,CAAC,CAAC,EAAE,CAACC,KAAK,EAAEE,QAAQ,EAAEH,KAAK,CAAC,CAAC;EAC9BP,QAAQ,CAACkC,KAAK,IAAI;IAChB,IAAIC,cAAc;IAElB,OAAO,CAACA,cAAc,GAAGpB,KAAK,CAACqB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,cAAc,CAACE,QAAQ,CAACC,OAAO,CAAC,CAACC,KAAK,EAAEhB,KAAK,KAAK;MAC3GgB,KAAK,CAACF,QAAQ,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAACO,CAAC,IAAItB,MAAM,CAACK,KAAK,CAAC,CAACU,QAAQ;MACtDM,KAAK,CAACF,QAAQ,CAAC,CAAC,CAAC,CAACT,KAAK,CAACa,SAAS,CAACvB,MAAM,CAACK,KAAK,CAAC,CAACK,KAAK,GAAG,CAAC,CAAC,GAAGH,IAAI,CAACI,GAAG,CAACK,KAAK,CAACQ,KAAK,CAACC,cAAc,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAGpB,KAAK,GAAG,EAAE,CAAC;IAC7H,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO,aAAaxB,KAAK,CAAC6C,aAAa,CAAC,OAAO,EAAE9B,KAAK,EAAE,aAAaf,KAAK,CAAC6C,aAAa,CAAC,OAAO,EAAE;IAChGC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAEnC,QAAQ,GAAG,CAAC,GAAGD,KAAK,CAAC;IACtCqC,GAAG,EAAE/B;EACP,CAAC,EAAEG,MAAM,CAACG,GAAG,CAAC,CAAC;IACbG,CAAC;IACDG,CAAC;IACDC,KAAK;IACLG;EACF,CAAC,EAAER,KAAK,KAAK,aAAaxB,KAAK,CAAC6C,aAAa,CAAC3C,SAAS,EAAE;IACvD8C,GAAG,EAAExB,KAAK;IACVsB,QAAQ,EAAE,CAACrB,CAAC,EAAEG,CAAC,EAAE,CAACJ,KAAK,GAAGd,KAAK;EACjC,CAAC,EAAE,aAAaV,KAAK,CAAC6C,aAAa,CAAC1C,KAAK,EAAE;IACzC0B,KAAK,EAAEA,KAAK;IACZK,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EACpB,CAAC,EAAE,aAAalC,KAAK,CAAC6C,aAAa,CAAC,sBAAsB,EAAE;IAC1DvB,GAAG,EAAEJ,YAAY;IACjB+B,WAAW,EAAE,IAAI;IACjB1C,OAAO,EAAEsB,KAAK,GAAG,CAAC,GAAGG,OAAO,GAAGzB,OAAO;IACtCO,SAAS,EAAEA,SAAS;IACpBD,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACT;AAEA,SAASP,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}