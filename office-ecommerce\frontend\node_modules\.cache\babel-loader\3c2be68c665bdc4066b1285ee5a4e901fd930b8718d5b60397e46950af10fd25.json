{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { forwardRef, useMemo, useEffect } from 'react';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport CameraControlsImpl from 'camera-controls';\nconst CameraControls = /*#__PURE__*/forwardRef((props, ref) => {\n  useMemo(() => {\n    CameraControlsImpl.install({\n      THREE\n    });\n    extend({\n      CameraControlsImpl\n    });\n  }, []);\n  const {\n    camera,\n    domElement,\n    makeDefault,\n    onStart,\n    onEnd,\n    onChange,\n    regress,\n    ...restProps\n  } = props;\n  const defaultCamera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const invalidate = useThree(state => state.invalidate);\n  const events = useThree(state => state.events);\n  const setEvents = useThree(state => state.setEvents);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const performance = useThree(state => state.performance);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = useMemo(() => new CameraControlsImpl(explCamera), [explCamera]);\n  useFrame((state, delta) => {\n    if (controls.enabled) controls.update(delta);\n  }, -1);\n  useEffect(() => {\n    controls.connect(explDomElement);\n    return () => void controls.disconnect();\n  }, [explDomElement, controls]);\n  React.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (regress) performance.regress();\n      if (onChange) onChange(e);\n    };\n    const onStartCb = e => {\n      if (onStart) onStart(e);\n    };\n    const onEndCb = e => {\n      if (onEnd) onEnd(e);\n    };\n    controls.addEventListener('update', callback);\n    controls.addEventListener('controlstart', onStartCb);\n    controls.addEventListener('controlend', onEndCb);\n    controls.addEventListener('control', callback);\n    controls.addEventListener('transitionstart', callback);\n    controls.addEventListener('wake', callback);\n    return () => {\n      controls.removeEventListener('update', callback);\n      controls.removeEventListener('controlstart', onStartCb);\n      controls.removeEventListener('controlend', onEndCb);\n      controls.removeEventListener('control', callback);\n      controls.removeEventListener('transitionstart', callback);\n      controls.removeEventListener('wake', callback);\n    };\n  }, [controls, onStart, onEnd, invalidate, setEvents, regress, onChange]);\n  useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls: controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls\n  }, restProps));\n});\nexport { CameraControls };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "forwardRef", "useMemo", "useEffect", "extend", "useThree", "useFrame", "CameraControlsImpl", "CameraControls", "props", "ref", "install", "camera", "dom<PERSON>lement", "makeDefault", "onStart", "onEnd", "onChange", "regress", "restProps", "defaultCamera", "state", "gl", "invalidate", "events", "setEvents", "set", "get", "performance", "explCamera", "explDomElement", "connected", "controls", "delta", "enabled", "update", "connect", "disconnect", "callback", "e", "onStartCb", "onEndCb", "addEventListener", "removeEventListener", "old", "createElement", "object"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/CameraControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { forwardRef, useMemo, useEffect } from 'react';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport CameraControlsImpl from 'camera-controls';\n\nconst CameraControls = /*#__PURE__*/forwardRef((props, ref) => {\n  useMemo(() => {\n    CameraControlsImpl.install({\n      THREE\n    });\n    extend({\n      CameraControlsImpl\n    });\n  }, []);\n  const {\n    camera,\n    domElement,\n    makeDefault,\n    onStart,\n    onEnd,\n    onChange,\n    regress,\n    ...restProps\n  } = props;\n  const defaultCamera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const invalidate = useThree(state => state.invalidate);\n  const events = useThree(state => state.events);\n  const setEvents = useThree(state => state.setEvents);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const performance = useThree(state => state.performance);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = useMemo(() => new CameraControlsImpl(explCamera), [explCamera]);\n  useFrame((state, delta) => {\n    if (controls.enabled) controls.update(delta);\n  }, -1);\n  useEffect(() => {\n    controls.connect(explDomElement);\n    return () => void controls.disconnect();\n  }, [explDomElement, controls]);\n  React.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (regress) performance.regress();\n      if (onChange) onChange(e);\n    };\n\n    const onStartCb = e => {\n      if (onStart) onStart(e);\n    };\n\n    const onEndCb = e => {\n      if (onEnd) onEnd(e);\n    };\n\n    controls.addEventListener('update', callback);\n    controls.addEventListener('controlstart', onStartCb);\n    controls.addEventListener('controlend', onEndCb);\n    controls.addEventListener('control', callback);\n    controls.addEventListener('transitionstart', callback);\n    controls.addEventListener('wake', callback);\n    return () => {\n      controls.removeEventListener('update', callback);\n      controls.removeEventListener('controlstart', onStartCb);\n      controls.removeEventListener('controlend', onEndCb);\n      controls.removeEventListener('control', callback);\n      controls.removeEventListener('transitionstart', callback);\n      controls.removeEventListener('wake', callback);\n    };\n  }, [controls, onStart, onEnd, invalidate, setEvents, regress, onChange]);\n  useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls: controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls\n  }, restProps));\n});\n\nexport { CameraControls };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,EAAEC,SAAS,QAAQ,OAAO;AACtD,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAC/D,OAAOC,kBAAkB,MAAM,iBAAiB;AAEhD,MAAMC,cAAc,GAAG,aAAaP,UAAU,CAAC,CAACQ,KAAK,EAAEC,GAAG,KAAK;EAC7DR,OAAO,CAAC,MAAM;IACZK,kBAAkB,CAACI,OAAO,CAAC;MACzBZ;IACF,CAAC,CAAC;IACFK,MAAM,CAAC;MACLG;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,MAAM;IACJK,MAAM;IACNC,UAAU;IACVC,WAAW;IACXC,OAAO;IACPC,KAAK;IACLC,QAAQ;IACRC,OAAO;IACP,GAAGC;EACL,CAAC,GAAGV,KAAK;EACT,MAAMW,aAAa,GAAGf,QAAQ,CAACgB,KAAK,IAAIA,KAAK,CAACT,MAAM,CAAC;EACrD,MAAMU,EAAE,GAAGjB,QAAQ,CAACgB,KAAK,IAAIA,KAAK,CAACC,EAAE,CAAC;EACtC,MAAMC,UAAU,GAAGlB,QAAQ,CAACgB,KAAK,IAAIA,KAAK,CAACE,UAAU,CAAC;EACtD,MAAMC,MAAM,GAAGnB,QAAQ,CAACgB,KAAK,IAAIA,KAAK,CAACG,MAAM,CAAC;EAC9C,MAAMC,SAAS,GAAGpB,QAAQ,CAACgB,KAAK,IAAIA,KAAK,CAACI,SAAS,CAAC;EACpD,MAAMC,GAAG,GAAGrB,QAAQ,CAACgB,KAAK,IAAIA,KAAK,CAACK,GAAG,CAAC;EACxC,MAAMC,GAAG,GAAGtB,QAAQ,CAACgB,KAAK,IAAIA,KAAK,CAACM,GAAG,CAAC;EACxC,MAAMC,WAAW,GAAGvB,QAAQ,CAACgB,KAAK,IAAIA,KAAK,CAACO,WAAW,CAAC;EACxD,MAAMC,UAAU,GAAGjB,MAAM,IAAIQ,aAAa;EAC1C,MAAMU,cAAc,GAAGjB,UAAU,IAAIW,MAAM,CAACO,SAAS,IAAIT,EAAE,CAACT,UAAU;EACtE,MAAMmB,QAAQ,GAAG9B,OAAO,CAAC,MAAM,IAAIK,kBAAkB,CAACsB,UAAU,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAChFvB,QAAQ,CAAC,CAACe,KAAK,EAAEY,KAAK,KAAK;IACzB,IAAID,QAAQ,CAACE,OAAO,EAAEF,QAAQ,CAACG,MAAM,CAACF,KAAK,CAAC;EAC9C,CAAC,EAAE,CAAC,CAAC,CAAC;EACN9B,SAAS,CAAC,MAAM;IACd6B,QAAQ,CAACI,OAAO,CAACN,cAAc,CAAC;IAChC,OAAO,MAAM,KAAKE,QAAQ,CAACK,UAAU,CAAC,CAAC;EACzC,CAAC,EAAE,CAACP,cAAc,EAAEE,QAAQ,CAAC,CAAC;EAC9BhC,KAAK,CAACG,SAAS,CAAC,MAAM;IACpB,MAAMmC,QAAQ,GAAGC,CAAC,IAAI;MACpBhB,UAAU,CAAC,CAAC;MACZ,IAAIL,OAAO,EAAEU,WAAW,CAACV,OAAO,CAAC,CAAC;MAClC,IAAID,QAAQ,EAAEA,QAAQ,CAACsB,CAAC,CAAC;IAC3B,CAAC;IAED,MAAMC,SAAS,GAAGD,CAAC,IAAI;MACrB,IAAIxB,OAAO,EAAEA,OAAO,CAACwB,CAAC,CAAC;IACzB,CAAC;IAED,MAAME,OAAO,GAAGF,CAAC,IAAI;MACnB,IAAIvB,KAAK,EAAEA,KAAK,CAACuB,CAAC,CAAC;IACrB,CAAC;IAEDP,QAAQ,CAACU,gBAAgB,CAAC,QAAQ,EAAEJ,QAAQ,CAAC;IAC7CN,QAAQ,CAACU,gBAAgB,CAAC,cAAc,EAAEF,SAAS,CAAC;IACpDR,QAAQ,CAACU,gBAAgB,CAAC,YAAY,EAAED,OAAO,CAAC;IAChDT,QAAQ,CAACU,gBAAgB,CAAC,SAAS,EAAEJ,QAAQ,CAAC;IAC9CN,QAAQ,CAACU,gBAAgB,CAAC,iBAAiB,EAAEJ,QAAQ,CAAC;IACtDN,QAAQ,CAACU,gBAAgB,CAAC,MAAM,EAAEJ,QAAQ,CAAC;IAC3C,OAAO,MAAM;MACXN,QAAQ,CAACW,mBAAmB,CAAC,QAAQ,EAAEL,QAAQ,CAAC;MAChDN,QAAQ,CAACW,mBAAmB,CAAC,cAAc,EAAEH,SAAS,CAAC;MACvDR,QAAQ,CAACW,mBAAmB,CAAC,YAAY,EAAEF,OAAO,CAAC;MACnDT,QAAQ,CAACW,mBAAmB,CAAC,SAAS,EAAEL,QAAQ,CAAC;MACjDN,QAAQ,CAACW,mBAAmB,CAAC,iBAAiB,EAAEL,QAAQ,CAAC;MACzDN,QAAQ,CAACW,mBAAmB,CAAC,MAAM,EAAEL,QAAQ,CAAC;IAChD,CAAC;EACH,CAAC,EAAE,CAACN,QAAQ,EAAEjB,OAAO,EAAEC,KAAK,EAAEO,UAAU,EAAEE,SAAS,EAAEP,OAAO,EAAED,QAAQ,CAAC,CAAC;EACxEd,SAAS,CAAC,MAAM;IACd,IAAIW,WAAW,EAAE;MACf,MAAM8B,GAAG,GAAGjB,GAAG,CAAC,CAAC,CAACK,QAAQ;MAC1BN,GAAG,CAAC;QACFM,QAAQ,EAAEA;MACZ,CAAC,CAAC;MACF,OAAO,MAAMN,GAAG,CAAC;QACfM,QAAQ,EAAEY;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC9B,WAAW,EAAEkB,QAAQ,CAAC,CAAC;EAC3B,OAAO,aAAahC,KAAK,CAAC6C,aAAa,CAAC,WAAW,EAAE/C,QAAQ,CAAC;IAC5DY,GAAG,EAAEA,GAAG;IACRoC,MAAM,EAAEd;EACV,CAAC,EAAEb,SAAS,CAAC,CAAC;AAChB,CAAC,CAAC;AAEF,SAASX,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}