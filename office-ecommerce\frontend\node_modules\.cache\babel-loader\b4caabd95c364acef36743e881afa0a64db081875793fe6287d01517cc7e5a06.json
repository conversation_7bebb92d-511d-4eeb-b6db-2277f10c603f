{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\admin\\\\ProductManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { toast } from 'react-toastify';\nimport ProductFormModal from './modals/ProductFormModal';\nimport ProductDetailsModal from './modals/ProductDetailsModal';\nimport ConfirmationModal from '../modals/ConfirmationModal';\nimport { productsApi } from '../../services/api';\nimport websocketService from '../../services/websocketService';\nimport './ProductManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductManagement = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('');\n  const [sortBy, setSortBy] = useState('ProductName');\n  const [sortDirection, setSortDirection] = useState('ASC');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(20);\n  const [totalCount, setTotalCount] = useState(0);\n  const [totalPages, setTotalPages] = useState(0);\n\n  // Modal states\n  const [showProductForm, setShowProductForm] = useState(false);\n  const [showProductDetails, setShowProductDetails] = useState(false);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [editingProduct, setEditingProduct] = useState(null);\n\n  // Fetch products with filters and pagination\n  const fetchProducts = useCallback(async () => {\n    try {\n      setLoading(true);\n      const params = {\n        page: currentPage,\n        limit: pageSize,\n        search: searchTerm || undefined,\n        category: selectedCategory || undefined,\n        status: selectedStatus || undefined,\n        sortBy,\n        sortDirection\n      };\n      const response = await productsApi.getProducts(params);\n      if (response.success) {\n        setProducts(response.data);\n        setTotalCount(response.pagination.totalCount);\n        setTotalPages(response.pagination.totalPages);\n      } else {\n        toast.error('Failed to fetch products');\n      }\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      toast.error('Error loading products');\n    } finally {\n      setLoading(false);\n    }\n  }, [currentPage, pageSize, searchTerm, selectedCategory, selectedStatus, sortBy, sortDirection]);\n\n  // Fetch categories\n  const fetchCategories = useCallback(async () => {\n    try {\n      const response = await productsApi.getCategories();\n      if (response.success) {\n        setCategories(response.data);\n      }\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n    }\n  }, []);\n  useEffect(() => {\n    fetchProducts();\n  }, [fetchProducts]);\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n\n  // Handle search with debouncing\n  const handleSearch = useCallback(value => {\n    setSearchTerm(value);\n    setCurrentPage(1);\n  }, []);\n\n  // Handle sorting\n  const handleSort = field => {\n    if (sortBy === field) {\n      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');\n    } else {\n      setSortBy(field);\n      setSortDirection('ASC');\n    }\n    setCurrentPage(1);\n  };\n\n  // Handle product actions\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setShowProductForm(true);\n  };\n  const handleEditProduct = product => {\n    setEditingProduct(product);\n    setShowProductForm(true);\n  };\n  const handleViewProduct = async productId => {\n    try {\n      const response = await productsApi.getProductById(productId);\n      if (response.success) {\n        setSelectedProduct(response.data);\n        setShowProductDetails(true);\n      } else {\n        toast.error('Failed to load product details');\n      }\n    } catch (error) {\n      console.error('Error fetching product details:', error);\n      toast.error('Error loading product details');\n    }\n  };\n  const handleDeleteProduct = product => {\n    setSelectedProduct(product);\n    setShowDeleteConfirm(true);\n  };\n  const confirmDeleteProduct = async () => {\n    try {\n      const response = await productsApi.deleteProduct(selectedProduct.ProductID);\n      if (response.success) {\n        toast.success('Product deleted successfully');\n        fetchProducts();\n      } else {\n        toast.error('Failed to delete product');\n      }\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      toast.error('Error deleting product');\n    } finally {\n      setShowDeleteConfirm(false);\n      setSelectedProduct(null);\n    }\n  };\n  const handleProductSaved = () => {\n    setShowProductForm(false);\n    setEditingProduct(null);\n    fetchProducts();\n    toast.success(editingProduct ? 'Product updated successfully' : 'Product created successfully');\n  };\n\n  // Utility functions\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n  const getStatusBadgeColor = status => {\n    switch (status) {\n      case 'Active':\n        return '#27ae60';\n      case 'Draft':\n        return '#f39c12';\n      case 'Inactive':\n        return '#95a5a6';\n      case 'Discontinued':\n        return '#e74c3c';\n      case 'Pending Review':\n        return '#3498db';\n      default:\n        return '#95a5a6';\n    }\n  };\n  const getSortIcon = field => {\n    if (sortBy !== field) return '↕️';\n    return sortDirection === 'ASC' ? '↑' : '↓';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading products...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-card-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"admin-card-title\",\n        children: \"Product Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"admin-btn admin-btn-primary\",\n        onClick: handleAddProduct,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"btn-icon\",\n          children: \"+\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), \"Add New Product\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-filters\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"search\",\n              children: \"Search Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"search\",\n              type: \"text\",\n              placeholder: \"Search by name, code, or description...\",\n              value: searchTerm,\n              onChange: e => handleSearch(e.target.value),\n              className: \"admin-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"category\",\n              children: \"Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"category\",\n              value: selectedCategory,\n              onChange: e => setSelectedCategory(e.target.value),\n              className: \"admin-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.CategoryID,\n                children: category.CategoryName\n              }, category.CategoryID, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"status\",\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"status\",\n              value: selectedStatus,\n              onChange: e => setSelectedStatus(e.target.value),\n              className: \"admin-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All Statuses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Active\",\n                children: \"Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Draft\",\n                children: \"Draft\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Inactive\",\n                children: \"Inactive\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Discontinued\",\n                children: \"Discontinued\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Pending Review\",\n                children: \"Pending Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"pageSize\",\n              children: \"Items per page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"pageSize\",\n              value: pageSize,\n              onChange: e => {\n                setPageSize(parseInt(e.target.value));\n                setCurrentPage(1);\n              },\n              className: \"admin-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: 10,\n                children: \"10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 20,\n                children: \"20\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 50,\n                children: \"50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 100,\n                children: \"100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"admin-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"sortable\",\n                onClick: () => handleSort('ProductName'),\n                children: [\"Product Name \", getSortIcon('ProductName')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"sortable\",\n                onClick: () => handleSort('BasePrice'),\n                children: [\"Price \", getSortIcon('BasePrice')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"sortable\",\n                onClick: () => handleSort('Status'),\n                children: [\"Status \", getSortIcon('Status')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Files\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"sortable\",\n                onClick: () => handleSort('UpdatedAt'),\n                children: [\"Last Updated \", getSortIcon('UpdatedAt')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: products.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"7\",\n                className: \"no-data\",\n                children: \"No products found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this) : products.map(product => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: product.ProductName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: product.ProductCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.CategoryName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatCurrency(product.BasePrice)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"status-badge\",\n                  style: {\n                    backgroundColor: getStatusBadgeColor(product.Status)\n                  },\n                  children: product.Status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-counts\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-count\",\n                    children: [\"\\uD83D\\uDCF7 \", product.ImageCount || 0]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-count\",\n                    children: [\"\\uD83C\\uDFAF \", product.ModelCount || 0]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.UpdatedAt ? new Date(product.UpdatedAt).toLocaleDateString() : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"action-buttons\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"admin-btn admin-btn-secondary btn-small\",\n                    onClick: () => handleViewProduct(product.ProductID),\n                    title: \"View Details\",\n                    children: \"\\uD83D\\uDC41\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"admin-btn admin-btn-secondary btn-small\",\n                    onClick: () => handleEditProduct(product),\n                    title: \"Edit Product\",\n                    children: \"\\u270F\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"admin-btn admin-btn-danger btn-small\",\n                    onClick: () => handleDeleteProduct(product),\n                    title: \"Delete Product\",\n                    children: \"\\uD83D\\uDDD1\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 21\n              }, this)]\n            }, product.ProductID, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pagination\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-info\",\n          children: [\"Showing \", (currentPage - 1) * pageSize + 1, \" to \", Math.min(currentPage * pageSize, totalCount), \" of \", totalCount, \" products\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn admin-btn-secondary btn-small\",\n            onClick: () => setCurrentPage(1),\n            disabled: currentPage === 1,\n            children: \"First\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn admin-btn-secondary btn-small\",\n            onClick: () => setCurrentPage(currentPage - 1),\n            disabled: currentPage === 1,\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"page-info\",\n            children: [\"Page \", currentPage, \" of \", totalPages]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn admin-btn-secondary btn-small\",\n            onClick: () => setCurrentPage(currentPage + 1),\n            disabled: currentPage === totalPages,\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn admin-btn-secondary btn-small\",\n            onClick: () => setCurrentPage(totalPages),\n            disabled: currentPage === totalPages,\n            children: \"Last\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this), showProductForm && /*#__PURE__*/_jsxDEV(ProductFormModal, {\n      product: editingProduct,\n      categories: categories,\n      onSave: handleProductSaved,\n      onClose: () => {\n        setShowProductForm(false);\n        setEditingProduct(null);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 424,\n      columnNumber: 9\n    }, this), showProductDetails && selectedProduct && /*#__PURE__*/_jsxDEV(ProductDetailsModal, {\n      product: selectedProduct,\n      onClose: () => {\n        setShowProductDetails(false);\n        setSelectedProduct(null);\n      },\n      onEdit: () => {\n        setEditingProduct(selectedProduct.product);\n        setShowProductDetails(false);\n        setShowProductForm(true);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 9\n    }, this), showDeleteConfirm && selectedProduct && /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n      title: \"Delete Product\",\n      message: `Are you sure you want to delete \"${selectedProduct.ProductName}\"? This action cannot be undone.`,\n      confirmText: \"Delete\",\n      cancelText: \"Cancel\",\n      onConfirm: confirmDeleteProduct,\n      onCancel: () => {\n        setShowDeleteConfirm(false);\n        setSelectedProduct(null);\n      },\n      type: \"danger\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductManagement, \"maW/skQ5u9JmUw/YIVyRemjBHnc=\");\n_c = ProductManagement;\nexport default ProductManagement;\nvar _c;\n$RefreshReg$(_c, \"ProductManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "toast", "ProductFormModal", "ProductDetailsModal", "ConfirmationModal", "productsApi", "websocketService", "jsxDEV", "_jsxDEV", "ProductManagement", "_s", "products", "setProducts", "categories", "setCategories", "loading", "setLoading", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedStatus", "setSelectedStatus", "sortBy", "setSortBy", "sortDirection", "setSortDirection", "currentPage", "setCurrentPage", "pageSize", "setPageSize", "totalCount", "setTotalCount", "totalPages", "setTotalPages", "showProductForm", "setShowProductForm", "showProductDetails", "setShowProductDetails", "showDeleteConfirm", "setShowDeleteConfirm", "selectedProduct", "setSelectedProduct", "editingProduct", "setEditingProduct", "fetchProducts", "params", "page", "limit", "search", "undefined", "category", "status", "response", "getProducts", "success", "data", "pagination", "error", "console", "fetchCategories", "getCategories", "handleSearch", "value", "handleSort", "field", "handleAddProduct", "handleEditProduct", "product", "handleViewProduct", "productId", "getProductById", "handleDeleteProduct", "confirmDeleteProduct", "deleteProduct", "ProductID", "handleProductSaved", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "getStatusBadgeColor", "getSortIcon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "htmlFor", "id", "type", "placeholder", "onChange", "e", "target", "map", "CategoryID", "CategoryName", "parseInt", "length", "colSpan", "ProductName", "ProductCode", "BasePrice", "backgroundColor", "Status", "ImageCount", "ModelCount", "UpdatedAt", "Date", "toLocaleDateString", "title", "Math", "min", "disabled", "onSave", "onClose", "onEdit", "message", "confirmText", "cancelText", "onConfirm", "onCancel", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/admin/ProductManagement.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { toast } from 'react-toastify';\nimport ProductFormModal from './modals/ProductFormModal';\nimport ProductDetailsModal from './modals/ProductDetailsModal';\nimport ConfirmationModal from '../modals/ConfirmationModal';\nimport { productsApi } from '../../services/api';\nimport websocketService from '../../services/websocketService';\nimport './ProductManagement.css';\n\nconst ProductManagement = () => {\n  const [products, setProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('');\n  const [sortBy, setSortBy] = useState('ProductName');\n  const [sortDirection, setSortDirection] = useState('ASC');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(20);\n  const [totalCount, setTotalCount] = useState(0);\n  const [totalPages, setTotalPages] = useState(0);\n\n  // Modal states\n  const [showProductForm, setShowProductForm] = useState(false);\n  const [showProductDetails, setShowProductDetails] = useState(false);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [editingProduct, setEditingProduct] = useState(null);\n\n  // Fetch products with filters and pagination\n  const fetchProducts = useCallback(async () => {\n    try {\n      setLoading(true);\n      const params = {\n        page: currentPage,\n        limit: pageSize,\n        search: searchTerm || undefined,\n        category: selectedCategory || undefined,\n        status: selectedStatus || undefined,\n        sortBy,\n        sortDirection\n      };\n\n      const response = await productsApi.getProducts(params);\n\n      if (response.success) {\n        setProducts(response.data);\n        setTotalCount(response.pagination.totalCount);\n        setTotalPages(response.pagination.totalPages);\n      } else {\n        toast.error('Failed to fetch products');\n      }\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      toast.error('Error loading products');\n    } finally {\n      setLoading(false);\n    }\n  }, [currentPage, pageSize, searchTerm, selectedCategory, selectedStatus, sortBy, sortDirection]);\n\n  // Fetch categories\n  const fetchCategories = useCallback(async () => {\n    try {\n      const response = await productsApi.getCategories();\n      if (response.success) {\n        setCategories(response.data);\n      }\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n    }\n  }, []);\n\n  useEffect(() => {\n    fetchProducts();\n  }, [fetchProducts]);\n\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n\n  // Handle search with debouncing\n  const handleSearch = useCallback((value) => {\n    setSearchTerm(value);\n    setCurrentPage(1);\n  }, []);\n\n  // Handle sorting\n  const handleSort = (field) => {\n    if (sortBy === field) {\n      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');\n    } else {\n      setSortBy(field);\n      setSortDirection('ASC');\n    }\n    setCurrentPage(1);\n  };\n\n  // Handle product actions\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setShowProductForm(true);\n  };\n\n  const handleEditProduct = (product) => {\n    setEditingProduct(product);\n    setShowProductForm(true);\n  };\n\n  const handleViewProduct = async (productId) => {\n    try {\n      const response = await productsApi.getProductById(productId);\n      if (response.success) {\n        setSelectedProduct(response.data);\n        setShowProductDetails(true);\n      } else {\n        toast.error('Failed to load product details');\n      }\n    } catch (error) {\n      console.error('Error fetching product details:', error);\n      toast.error('Error loading product details');\n    }\n  };\n\n  const handleDeleteProduct = (product) => {\n    setSelectedProduct(product);\n    setShowDeleteConfirm(true);\n  };\n\n  const confirmDeleteProduct = async () => {\n    try {\n      const response = await productsApi.deleteProduct(selectedProduct.ProductID);\n      if (response.success) {\n        toast.success('Product deleted successfully');\n        fetchProducts();\n      } else {\n        toast.error('Failed to delete product');\n      }\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      toast.error('Error deleting product');\n    } finally {\n      setShowDeleteConfirm(false);\n      setSelectedProduct(null);\n    }\n  };\n\n  const handleProductSaved = () => {\n    setShowProductForm(false);\n    setEditingProduct(null);\n    fetchProducts();\n    toast.success(editingProduct ? 'Product updated successfully' : 'Product created successfully');\n  };\n\n  // Utility functions\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n\n  const getStatusBadgeColor = (status) => {\n    switch (status) {\n      case 'Active': return '#27ae60';\n      case 'Draft': return '#f39c12';\n      case 'Inactive': return '#95a5a6';\n      case 'Discontinued': return '#e74c3c';\n      case 'Pending Review': return '#3498db';\n      default: return '#95a5a6';\n    }\n  };\n\n  const getSortIcon = (field) => {\n    if (sortBy !== field) return '↕️';\n    return sortDirection === 'ASC' ? '↑' : '↓';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"admin-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading products...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"product-management\">\n      {/* Header */}\n      <div className=\"admin-card-header\">\n        <h1 className=\"admin-card-title\">Product Management</h1>\n        <button\n          className=\"admin-btn admin-btn-primary\"\n          onClick={handleAddProduct}\n        >\n          <span className=\"btn-icon\">+</span>\n          Add New Product\n        </button>\n      </div>\n\n      {/* Filters and Search */}\n      <div className=\"admin-card\">\n        <div className=\"product-filters\">\n          <div className=\"filter-row\">\n            <div className=\"filter-group\">\n              <label htmlFor=\"search\">Search Products</label>\n              <input\n                id=\"search\"\n                type=\"text\"\n                placeholder=\"Search by name, code, or description...\"\n                value={searchTerm}\n                onChange={(e) => handleSearch(e.target.value)}\n                className=\"admin-input\"\n              />\n            </div>\n\n            <div className=\"filter-group\">\n              <label htmlFor=\"category\">Category</label>\n              <select\n                id=\"category\"\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n                className=\"admin-select\"\n              >\n                <option value=\"\">All Categories</option>\n                {categories.map(category => (\n                  <option key={category.CategoryID} value={category.CategoryID}>\n                    {category.CategoryName}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div className=\"filter-group\">\n              <label htmlFor=\"status\">Status</label>\n              <select\n                id=\"status\"\n                value={selectedStatus}\n                onChange={(e) => setSelectedStatus(e.target.value)}\n                className=\"admin-select\"\n              >\n                <option value=\"\">All Statuses</option>\n                <option value=\"Active\">Active</option>\n                <option value=\"Draft\">Draft</option>\n                <option value=\"Inactive\">Inactive</option>\n                <option value=\"Discontinued\">Discontinued</option>\n                <option value=\"Pending Review\">Pending Review</option>\n              </select>\n            </div>\n\n            <div className=\"filter-group\">\n              <label htmlFor=\"pageSize\">Items per page</label>\n              <select\n                id=\"pageSize\"\n                value={pageSize}\n                onChange={(e) => {\n                  setPageSize(parseInt(e.target.value));\n                  setCurrentPage(1);\n                }}\n                className=\"admin-select\"\n              >\n                <option value={10}>10</option>\n                <option value={20}>20</option>\n                <option value={50}>50</option>\n                <option value={100}>100</option>\n              </select>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Products Table */}\n      <div className=\"admin-card\">\n        <div className=\"table-container\">\n          <table className=\"admin-table\">\n            <thead>\n              <tr>\n                <th\n                  className=\"sortable\"\n                  onClick={() => handleSort('ProductName')}\n                >\n                  Product Name {getSortIcon('ProductName')}\n                </th>\n                <th>Category</th>\n                <th\n                  className=\"sortable\"\n                  onClick={() => handleSort('BasePrice')}\n                >\n                  Price {getSortIcon('BasePrice')}\n                </th>\n                <th\n                  className=\"sortable\"\n                  onClick={() => handleSort('Status')}\n                >\n                  Status {getSortIcon('Status')}\n                </th>\n                <th>Files</th>\n                <th\n                  className=\"sortable\"\n                  onClick={() => handleSort('UpdatedAt')}\n                >\n                  Last Updated {getSortIcon('UpdatedAt')}\n                </th>\n                <th>Actions</th>\n              </tr>\n            </thead>\n            <tbody>\n              {products.length === 0 ? (\n                <tr>\n                  <td colSpan=\"7\" className=\"no-data\">\n                    No products found\n                  </td>\n                </tr>\n              ) : (\n                products.map(product => (\n                  <tr key={product.ProductID}>\n                    <td>\n                      <div className=\"product-info\">\n                        <strong>{product.ProductName}</strong>\n                        <small>{product.ProductCode}</small>\n                      </div>\n                    </td>\n                    <td>{product.CategoryName}</td>\n                    <td>{formatCurrency(product.BasePrice)}</td>\n                    <td>\n                      <span\n                        className=\"status-badge\"\n                        style={{ backgroundColor: getStatusBadgeColor(product.Status) }}\n                      >\n                        {product.Status}\n                      </span>\n                    </td>\n                    <td>\n                      <div className=\"file-counts\">\n                        <span className=\"file-count\">\n                          📷 {product.ImageCount || 0}\n                        </span>\n                        <span className=\"file-count\">\n                          🎯 {product.ModelCount || 0}\n                        </span>\n                      </div>\n                    </td>\n                    <td>\n                      {product.UpdatedAt ? new Date(product.UpdatedAt).toLocaleDateString() : '-'}\n                    </td>\n                    <td>\n                      <div className=\"action-buttons\">\n                        <button\n                          className=\"admin-btn admin-btn-secondary btn-small\"\n                          onClick={() => handleViewProduct(product.ProductID)}\n                          title=\"View Details\"\n                        >\n                          👁️\n                        </button>\n                        <button\n                          className=\"admin-btn admin-btn-secondary btn-small\"\n                          onClick={() => handleEditProduct(product)}\n                          title=\"Edit Product\"\n                        >\n                          ✏️\n                        </button>\n                        <button\n                          className=\"admin-btn admin-btn-danger btn-small\"\n                          onClick={() => handleDeleteProduct(product)}\n                          title=\"Delete Product\"\n                        >\n                          🗑️\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))\n              )}\n            </tbody>\n          </table>\n        </div>\n\n        {/* Pagination */}\n        {totalPages > 1 && (\n          <div className=\"pagination\">\n            <div className=\"pagination-info\">\n              Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} products\n            </div>\n            <div className=\"pagination-controls\">\n              <button\n                className=\"admin-btn admin-btn-secondary btn-small\"\n                onClick={() => setCurrentPage(1)}\n                disabled={currentPage === 1}\n              >\n                First\n              </button>\n              <button\n                className=\"admin-btn admin-btn-secondary btn-small\"\n                onClick={() => setCurrentPage(currentPage - 1)}\n                disabled={currentPage === 1}\n              >\n                Previous\n              </button>\n              <span className=\"page-info\">\n                Page {currentPage} of {totalPages}\n              </span>\n              <button\n                className=\"admin-btn admin-btn-secondary btn-small\"\n                onClick={() => setCurrentPage(currentPage + 1)}\n                disabled={currentPage === totalPages}\n              >\n                Next\n              </button>\n              <button\n                className=\"admin-btn admin-btn-secondary btn-small\"\n                onClick={() => setCurrentPage(totalPages)}\n                disabled={currentPage === totalPages}\n              >\n                Last\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Modals */}\n      {showProductForm && (\n        <ProductFormModal\n          product={editingProduct}\n          categories={categories}\n          onSave={handleProductSaved}\n          onClose={() => {\n            setShowProductForm(false);\n            setEditingProduct(null);\n          }}\n        />\n      )}\n\n      {showProductDetails && selectedProduct && (\n        <ProductDetailsModal\n          product={selectedProduct}\n          onClose={() => {\n            setShowProductDetails(false);\n            setSelectedProduct(null);\n          }}\n          onEdit={() => {\n            setEditingProduct(selectedProduct.product);\n            setShowProductDetails(false);\n            setShowProductForm(true);\n          }}\n        />\n      )}\n\n      {showDeleteConfirm && selectedProduct && (\n        <ConfirmationModal\n          title=\"Delete Product\"\n          message={`Are you sure you want to delete \"${selectedProduct.ProductName}\"? This action cannot be undone.`}\n          confirmText=\"Delete\"\n          cancelText=\"Cancel\"\n          onConfirm={confirmDeleteProduct}\n          onCancel={() => {\n            setShowDeleteConfirm(false);\n            setSelectedProduct(null);\n          }}\n          type=\"danger\"\n        />\n      )}\n    </div>\n  );\n};\n\nexport default ProductManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,OAAOC,iBAAiB,MAAM,6BAA6B;AAC3D,SAASC,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyB,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,aAAa,CAAC;EACnD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC6C,cAAc,EAAEC,iBAAiB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAM+C,aAAa,GAAG7C,WAAW,CAAC,YAAY;IAC5C,IAAI;MACFgB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM8B,MAAM,GAAG;QACbC,IAAI,EAAEpB,WAAW;QACjBqB,KAAK,EAAEnB,QAAQ;QACfoB,MAAM,EAAEhC,UAAU,IAAIiC,SAAS;QAC/BC,QAAQ,EAAEhC,gBAAgB,IAAI+B,SAAS;QACvCE,MAAM,EAAE/B,cAAc,IAAI6B,SAAS;QACnC3B,MAAM;QACNE;MACF,CAAC;MAED,MAAM4B,QAAQ,GAAG,MAAMhD,WAAW,CAACiD,WAAW,CAACR,MAAM,CAAC;MAEtD,IAAIO,QAAQ,CAACE,OAAO,EAAE;QACpB3C,WAAW,CAACyC,QAAQ,CAACG,IAAI,CAAC;QAC1BxB,aAAa,CAACqB,QAAQ,CAACI,UAAU,CAAC1B,UAAU,CAAC;QAC7CG,aAAa,CAACmB,QAAQ,CAACI,UAAU,CAACxB,UAAU,CAAC;MAC/C,CAAC,MAAM;QACLhC,KAAK,CAACyD,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDzD,KAAK,CAACyD,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACR1C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACW,WAAW,EAAEE,QAAQ,EAAEZ,UAAU,EAAEE,gBAAgB,EAAEE,cAAc,EAAEE,MAAM,EAAEE,aAAa,CAAC,CAAC;;EAEhG;EACA,MAAMmC,eAAe,GAAG5D,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF,MAAMqD,QAAQ,GAAG,MAAMhD,WAAW,CAACwD,aAAa,CAAC,CAAC;MAClD,IAAIR,QAAQ,CAACE,OAAO,EAAE;QACpBzC,aAAa,CAACuC,QAAQ,CAACG,IAAI,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN3D,SAAS,CAAC,MAAM;IACd8C,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB9C,SAAS,CAAC,MAAM;IACd6D,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;;EAErB;EACA,MAAME,YAAY,GAAG9D,WAAW,CAAE+D,KAAK,IAAK;IAC1C7C,aAAa,CAAC6C,KAAK,CAAC;IACpBnC,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMoC,UAAU,GAAIC,KAAK,IAAK;IAC5B,IAAI1C,MAAM,KAAK0C,KAAK,EAAE;MACpBvC,gBAAgB,CAACD,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;IAC5D,CAAC,MAAM;MACLD,SAAS,CAACyC,KAAK,CAAC;MAChBvC,gBAAgB,CAAC,KAAK,CAAC;IACzB;IACAE,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMsC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BtB,iBAAiB,CAAC,IAAI,CAAC;IACvBR,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM+B,iBAAiB,GAAIC,OAAO,IAAK;IACrCxB,iBAAiB,CAACwB,OAAO,CAAC;IAC1BhC,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMiC,iBAAiB,GAAG,MAAOC,SAAS,IAAK;IAC7C,IAAI;MACF,MAAMjB,QAAQ,GAAG,MAAMhD,WAAW,CAACkE,cAAc,CAACD,SAAS,CAAC;MAC5D,IAAIjB,QAAQ,CAACE,OAAO,EAAE;QACpBb,kBAAkB,CAACW,QAAQ,CAACG,IAAI,CAAC;QACjClB,qBAAqB,CAAC,IAAI,CAAC;MAC7B,CAAC,MAAM;QACLrC,KAAK,CAACyD,KAAK,CAAC,gCAAgC,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDzD,KAAK,CAACyD,KAAK,CAAC,+BAA+B,CAAC;IAC9C;EACF,CAAC;EAED,MAAMc,mBAAmB,GAAIJ,OAAO,IAAK;IACvC1B,kBAAkB,CAAC0B,OAAO,CAAC;IAC3B5B,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMiC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMhD,WAAW,CAACqE,aAAa,CAACjC,eAAe,CAACkC,SAAS,CAAC;MAC3E,IAAItB,QAAQ,CAACE,OAAO,EAAE;QACpBtD,KAAK,CAACsD,OAAO,CAAC,8BAA8B,CAAC;QAC7CV,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACL5C,KAAK,CAACyD,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CzD,KAAK,CAACyD,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACRlB,oBAAoB,CAAC,KAAK,CAAC;MAC3BE,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC;EAED,MAAMkC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BxC,kBAAkB,CAAC,KAAK,CAAC;IACzBQ,iBAAiB,CAAC,IAAI,CAAC;IACvBC,aAAa,CAAC,CAAC;IACf5C,KAAK,CAACsD,OAAO,CAACZ,cAAc,GAAG,8BAA8B,GAAG,8BAA8B,CAAC;EACjG,CAAC;;EAED;EACA,MAAMkC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,mBAAmB,GAAIhC,MAAM,IAAK;IACtC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,cAAc;QAAE,OAAO,SAAS;MACrC,KAAK,gBAAgB;QAAE,OAAO,SAAS;MACvC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMiC,WAAW,GAAIpB,KAAK,IAAK;IAC7B,IAAI1C,MAAM,KAAK0C,KAAK,EAAE,OAAO,IAAI;IACjC,OAAOxC,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG;EAC5C,CAAC;EAED,IAAIV,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK8E,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B/E,OAAA;QAAK8E,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCnF,OAAA;QAAA+E,QAAA,EAAG;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAEV;EAEA,oBACEnF,OAAA;IAAK8E,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBAEjC/E,OAAA;MAAK8E,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC/E,OAAA;QAAI8E,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxDnF,OAAA;QACE8E,SAAS,EAAC,6BAA6B;QACvCM,OAAO,EAAE1B,gBAAiB;QAAAqB,QAAA,gBAE1B/E,OAAA;UAAM8E,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,mBAErC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNnF,OAAA;MAAK8E,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzB/E,OAAA;QAAK8E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B/E,OAAA;UAAK8E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB/E,OAAA;YAAK8E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B/E,OAAA;cAAOqF,OAAO,EAAC,QAAQ;cAAAN,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/CnF,OAAA;cACEsF,EAAE,EAAC,QAAQ;cACXC,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,yCAAyC;cACrDjC,KAAK,EAAE9C,UAAW;cAClBgF,QAAQ,EAAGC,CAAC,IAAKpC,YAAY,CAACoC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;cAC9CuB,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENnF,OAAA;YAAK8E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B/E,OAAA;cAAOqF,OAAO,EAAC,UAAU;cAAAN,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CnF,OAAA;cACEsF,EAAE,EAAC,UAAU;cACb/B,KAAK,EAAE5C,gBAAiB;cACxB8E,QAAQ,EAAGC,CAAC,IAAK9E,mBAAmB,CAAC8E,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;cACrDuB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAExB/E,OAAA;gBAAQuD,KAAK,EAAC,EAAE;gBAAAwB,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACvC9E,UAAU,CAACuF,GAAG,CAACjD,QAAQ,iBACtB3C,OAAA;gBAAkCuD,KAAK,EAAEZ,QAAQ,CAACkD,UAAW;gBAAAd,QAAA,EAC1DpC,QAAQ,CAACmD;cAAY,GADXnD,QAAQ,CAACkD,UAAU;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAExB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENnF,OAAA;YAAK8E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B/E,OAAA;cAAOqF,OAAO,EAAC,QAAQ;cAAAN,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCnF,OAAA;cACEsF,EAAE,EAAC,QAAQ;cACX/B,KAAK,EAAE1C,cAAe;cACtB4E,QAAQ,EAAGC,CAAC,IAAK5E,iBAAiB,CAAC4E,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;cACnDuB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAExB/E,OAAA;gBAAQuD,KAAK,EAAC,EAAE;gBAAAwB,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCnF,OAAA;gBAAQuD,KAAK,EAAC,QAAQ;gBAAAwB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCnF,OAAA;gBAAQuD,KAAK,EAAC,OAAO;gBAAAwB,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCnF,OAAA;gBAAQuD,KAAK,EAAC,UAAU;gBAAAwB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1CnF,OAAA;gBAAQuD,KAAK,EAAC,cAAc;gBAAAwB,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClDnF,OAAA;gBAAQuD,KAAK,EAAC,gBAAgB;gBAAAwB,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENnF,OAAA;YAAK8E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B/E,OAAA;cAAOqF,OAAO,EAAC,UAAU;cAAAN,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChDnF,OAAA;cACEsF,EAAE,EAAC,UAAU;cACb/B,KAAK,EAAElC,QAAS;cAChBoE,QAAQ,EAAGC,CAAC,IAAK;gBACfpE,WAAW,CAACyE,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC,CAAC;gBACrCnC,cAAc,CAAC,CAAC,CAAC;cACnB,CAAE;cACF0D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAExB/E,OAAA;gBAAQuD,KAAK,EAAE,EAAG;gBAAAwB,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BnF,OAAA;gBAAQuD,KAAK,EAAE,EAAG;gBAAAwB,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BnF,OAAA;gBAAQuD,KAAK,EAAE,EAAG;gBAAAwB,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BnF,OAAA;gBAAQuD,KAAK,EAAE,GAAI;gBAAAwB,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnF,OAAA;MAAK8E,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB/E,OAAA;QAAK8E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B/E,OAAA;UAAO8E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC5B/E,OAAA;YAAA+E,QAAA,eACE/E,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBACE8E,SAAS,EAAC,UAAU;gBACpBM,OAAO,EAAEA,CAAA,KAAM5B,UAAU,CAAC,aAAa,CAAE;gBAAAuB,QAAA,GAC1C,eACc,EAACF,WAAW,CAAC,aAAa,CAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACLnF,OAAA;gBAAA+E,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBnF,OAAA;gBACE8E,SAAS,EAAC,UAAU;gBACpBM,OAAO,EAAEA,CAAA,KAAM5B,UAAU,CAAC,WAAW,CAAE;gBAAAuB,QAAA,GACxC,QACO,EAACF,WAAW,CAAC,WAAW,CAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACLnF,OAAA;gBACE8E,SAAS,EAAC,UAAU;gBACpBM,OAAO,EAAEA,CAAA,KAAM5B,UAAU,CAAC,QAAQ,CAAE;gBAAAuB,QAAA,GACrC,SACQ,EAACF,WAAW,CAAC,QAAQ,CAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACLnF,OAAA;gBAAA+E,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdnF,OAAA;gBACE8E,SAAS,EAAC,UAAU;gBACpBM,OAAO,EAAEA,CAAA,KAAM5B,UAAU,CAAC,WAAW,CAAE;gBAAAuB,QAAA,GACxC,eACc,EAACF,WAAW,CAAC,WAAW,CAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACLnF,OAAA;gBAAA+E,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRnF,OAAA;YAAA+E,QAAA,EACG5E,QAAQ,CAAC6F,MAAM,KAAK,CAAC,gBACpBhG,OAAA;cAAA+E,QAAA,eACE/E,OAAA;gBAAIiG,OAAO,EAAC,GAAG;gBAACnB,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAEpC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GAELhF,QAAQ,CAACyF,GAAG,CAAChC,OAAO,iBAClB5D,OAAA;cAAA+E,QAAA,gBACE/E,OAAA;gBAAA+E,QAAA,eACE/E,OAAA;kBAAK8E,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B/E,OAAA;oBAAA+E,QAAA,EAASnB,OAAO,CAACsC;kBAAW;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eACtCnF,OAAA;oBAAA+E,QAAA,EAAQnB,OAAO,CAACuC;kBAAW;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLnF,OAAA;gBAAA+E,QAAA,EAAKnB,OAAO,CAACkC;cAAY;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/BnF,OAAA;gBAAA+E,QAAA,EAAKV,cAAc,CAACT,OAAO,CAACwC,SAAS;cAAC;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5CnF,OAAA;gBAAA+E,QAAA,eACE/E,OAAA;kBACE8E,SAAS,EAAC,cAAc;kBACxBL,KAAK,EAAE;oBAAE4B,eAAe,EAAEzB,mBAAmB,CAAChB,OAAO,CAAC0C,MAAM;kBAAE,CAAE;kBAAAvB,QAAA,EAE/DnB,OAAO,CAAC0C;gBAAM;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLnF,OAAA;gBAAA+E,QAAA,eACE/E,OAAA;kBAAK8E,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1B/E,OAAA;oBAAM8E,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAC,eACxB,EAACnB,OAAO,CAAC2C,UAAU,IAAI,CAAC;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACPnF,OAAA;oBAAM8E,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAC,eACxB,EAACnB,OAAO,CAAC4C,UAAU,IAAI,CAAC;kBAAA;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLnF,OAAA;gBAAA+E,QAAA,EACGnB,OAAO,CAAC6C,SAAS,GAAG,IAAIC,IAAI,CAAC9C,OAAO,CAAC6C,SAAS,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;cAAG;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACLnF,OAAA;gBAAA+E,QAAA,eACE/E,OAAA;kBAAK8E,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B/E,OAAA;oBACE8E,SAAS,EAAC,yCAAyC;oBACnDM,OAAO,EAAEA,CAAA,KAAMvB,iBAAiB,CAACD,OAAO,CAACO,SAAS,CAAE;oBACpDyC,KAAK,EAAC,cAAc;oBAAA7B,QAAA,EACrB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTnF,OAAA;oBACE8E,SAAS,EAAC,yCAAyC;oBACnDM,OAAO,EAAEA,CAAA,KAAMzB,iBAAiB,CAACC,OAAO,CAAE;oBAC1CgD,KAAK,EAAC,cAAc;oBAAA7B,QAAA,EACrB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTnF,OAAA;oBACE8E,SAAS,EAAC,sCAAsC;oBAChDM,OAAO,EAAEA,CAAA,KAAMpB,mBAAmB,CAACJ,OAAO,CAAE;oBAC5CgD,KAAK,EAAC,gBAAgB;oBAAA7B,QAAA,EACvB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAtDEvB,OAAO,CAACO,SAAS;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuDtB,CACL;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGL1D,UAAU,GAAG,CAAC,iBACbzB,OAAA;QAAK8E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/E,OAAA;UAAK8E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,UACvB,EAAE,CAAC5D,WAAW,GAAG,CAAC,IAAIE,QAAQ,GAAI,CAAC,EAAC,MAAI,EAACwF,IAAI,CAACC,GAAG,CAAC3F,WAAW,GAAGE,QAAQ,EAAEE,UAAU,CAAC,EAAC,MAAI,EAACA,UAAU,EAAC,WAChH;QAAA;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNnF,OAAA;UAAK8E,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClC/E,OAAA;YACE8E,SAAS,EAAC,yCAAyC;YACnDM,OAAO,EAAEA,CAAA,KAAMhE,cAAc,CAAC,CAAC,CAAE;YACjC2F,QAAQ,EAAE5F,WAAW,KAAK,CAAE;YAAA4D,QAAA,EAC7B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnF,OAAA;YACE8E,SAAS,EAAC,yCAAyC;YACnDM,OAAO,EAAEA,CAAA,KAAMhE,cAAc,CAACD,WAAW,GAAG,CAAC,CAAE;YAC/C4F,QAAQ,EAAE5F,WAAW,KAAK,CAAE;YAAA4D,QAAA,EAC7B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnF,OAAA;YAAM8E,SAAS,EAAC,WAAW;YAAAC,QAAA,GAAC,OACrB,EAAC5D,WAAW,EAAC,MAAI,EAACM,UAAU;UAAA;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACPnF,OAAA;YACE8E,SAAS,EAAC,yCAAyC;YACnDM,OAAO,EAAEA,CAAA,KAAMhE,cAAc,CAACD,WAAW,GAAG,CAAC,CAAE;YAC/C4F,QAAQ,EAAE5F,WAAW,KAAKM,UAAW;YAAAsD,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnF,OAAA;YACE8E,SAAS,EAAC,yCAAyC;YACnDM,OAAO,EAAEA,CAAA,KAAMhE,cAAc,CAACK,UAAU,CAAE;YAC1CsF,QAAQ,EAAE5F,WAAW,KAAKM,UAAW;YAAAsD,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLxD,eAAe,iBACd3B,OAAA,CAACN,gBAAgB;MACfkE,OAAO,EAAEzB,cAAe;MACxB9B,UAAU,EAAEA,UAAW;MACvB2G,MAAM,EAAE5C,kBAAmB;MAC3B6C,OAAO,EAAEA,CAAA,KAAM;QACbrF,kBAAkB,CAAC,KAAK,CAAC;QACzBQ,iBAAiB,CAAC,IAAI,CAAC;MACzB;IAAE;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EAEAtD,kBAAkB,IAAII,eAAe,iBACpCjC,OAAA,CAACL,mBAAmB;MAClBiE,OAAO,EAAE3B,eAAgB;MACzBgF,OAAO,EAAEA,CAAA,KAAM;QACbnF,qBAAqB,CAAC,KAAK,CAAC;QAC5BI,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAE;MACFgF,MAAM,EAAEA,CAAA,KAAM;QACZ9E,iBAAiB,CAACH,eAAe,CAAC2B,OAAO,CAAC;QAC1C9B,qBAAqB,CAAC,KAAK,CAAC;QAC5BF,kBAAkB,CAAC,IAAI,CAAC;MAC1B;IAAE;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EAEApD,iBAAiB,IAAIE,eAAe,iBACnCjC,OAAA,CAACJ,iBAAiB;MAChBgH,KAAK,EAAC,gBAAgB;MACtBO,OAAO,EAAE,oCAAoClF,eAAe,CAACiE,WAAW,kCAAmC;MAC3GkB,WAAW,EAAC,QAAQ;MACpBC,UAAU,EAAC,QAAQ;MACnBC,SAAS,EAAErD,oBAAqB;MAChCsD,QAAQ,EAAEA,CAAA,KAAM;QACdvF,oBAAoB,CAAC,KAAK,CAAC;QAC3BE,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAE;MACFqD,IAAI,EAAC;IAAQ;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjF,EAAA,CAxcID,iBAAiB;AAAAuH,EAAA,GAAjBvH,iBAAiB;AA0cvB,eAAeA,iBAAiB;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}