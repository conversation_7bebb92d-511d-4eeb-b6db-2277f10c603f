{"ast": null, "code": "import { AnimationClip, Bone, Box3, BufferAttribute, BufferGeometry, ClampToEdgeWrapping, Color, ColorManagement, DirectionalLight, DoubleSide, FileLoader, FrontSide, Group, ImageBitmapLoader, InstancedMesh, InterleavedBuffer, InterleavedBufferAttribute, Interpolant, InterpolateDiscrete, InterpolateLinear, Line, LineBasicMaterial, LineLoop, LineSegments, LinearFilter, LinearMipmapLinearFilter, LinearMipmapNearestFilter, LinearSRGBColorSpace, Loader, LoaderUtils, Material, MathUtils, Matrix4, Mesh, MeshBasicMaterial, MeshPhysicalMaterial, MeshStandardMaterial, MirroredRepeatWrapping, NearestFilter, NearestMipmapLinearFilter, NearestMipmapNearestFilter, NumberKeyframeTrack, Object3D, OrthographicCamera, PerspectiveCamera, PointLight, Points, PointsMaterial, PropertyBinding, Quaternion, QuaternionKeyframeTrack, RepeatWrapping, Skeleton, SkinnedMesh, Sphere, SpotLight, Texture, TextureLoader, TriangleFanDrawMode, TriangleStripDrawMode, Vector2, Vector3, VectorKeyframeTrack, SRGBColorSpace, InstancedBufferAttribute } from 'three';\nimport { toTrianglesDrawMode } from '../utils/BufferGeometryUtils.js';\n\n/**\n * A loader for the glTF 2.0 format.\n *\n * [glTF]{@link https://www.khronos.org/gltf/} (GL Transmission Format) is an [open format specification]{@link https://github.com/KhronosGroup/glTF/tree/main/specification/2.0}\n * for efficient delivery and loading of 3D content. Assets may be provided either in JSON (.gltf) or binary (.glb)\n * format. External files store textures (.jpg, .png) and additional binary data (.bin). A glTF asset may deliver\n * one or more scenes, including meshes, materials, textures, skins, skeletons, morph targets, animations, lights,\n * and/or cameras.\n *\n * `GLTFLoader` uses {@link ImageBitmapLoader} whenever possible. Be advised that image bitmaps are not\n * automatically GC-collected when they are no longer referenced, and they require special handling during\n * the disposal process.\n *\n * `GLTFLoader` supports the following glTF 2.0 extensions:\n * - KHR_draco_mesh_compression\n * - KHR_materials_clearcoat\n * - KHR_materials_dispersion\n * - KHR_materials_ior\n * - KHR_materials_specular\n * - KHR_materials_transmission\n * - KHR_materials_iridescence\n * - KHR_materials_unlit\n * - KHR_materials_volume\n * - KHR_mesh_quantization\n * - KHR_lights_punctual\n * - KHR_texture_basisu\n * - KHR_texture_transform\n * - EXT_texture_webp\n * - EXT_meshopt_compression\n * - EXT_mesh_gpu_instancing\n *\n * The following glTF 2.0 extension is supported by an external user plugin:\n * - [KHR_materials_variants]{@link https://github.com/takahirox/three-gltf-extensions}\n * - [MSFT_texture_dds]{@link https://github.com/takahirox/three-gltf-extensions}\n *\n * ```js\n * const loader = new GLTFLoader();\n *\n * // Optional: Provide a DRACOLoader instance to decode compressed mesh data\n * const dracoLoader = new DRACOLoader();\n * dracoLoader.setDecoderPath( '/examples/jsm/libs/draco/' );\n * loader.setDRACOLoader( dracoLoader );\n *\n * const gltf = await loader.loadAsync( 'models/gltf/duck/duck.gltf' );\n * scene.add( gltf.scene );\n * ```\n *\n * @augments Loader\n * @three_import import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';\n */\nclass GLTFLoader extends Loader {\n  /**\n   * Constructs a new glTF loader.\n   *\n   * @param {LoadingManager} [manager] - The loading manager.\n   */\n  constructor(manager) {\n    super(manager);\n    this.dracoLoader = null;\n    this.ktx2Loader = null;\n    this.meshoptDecoder = null;\n    this.pluginCallbacks = [];\n    this.register(function (parser) {\n      return new GLTFMaterialsClearcoatExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsDispersionExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFTextureBasisUExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFTextureWebPExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFTextureAVIFExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsSheenExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsTransmissionExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsVolumeExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsIorExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsEmissiveStrengthExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsSpecularExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsIridescenceExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsAnisotropyExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMaterialsBumpExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFLightsExtension(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMeshoptCompression(parser);\n    });\n    this.register(function (parser) {\n      return new GLTFMeshGpuInstancing(parser);\n    });\n  }\n\n  /**\n   * Starts loading from the given URL and passes the loaded glTF asset\n   * to the `onLoad()` callback.\n   *\n   * @param {string} url - The path/URL of the file to be loaded. This can also be a data URI.\n   * @param {function(GLTFLoader~LoadObject)} onLoad - Executed when the loading process has been finished.\n   * @param {onProgressCallback} onProgress - Executed while the loading is in progress.\n   * @param {onErrorCallback} onError - Executed when errors occur.\n   */\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    let resourcePath;\n    if (this.resourcePath !== '') {\n      resourcePath = this.resourcePath;\n    } else if (this.path !== '') {\n      // If a base path is set, resources will be relative paths from that plus the relative path of the gltf file\n      // Example  path = 'https://my-cnd-server.com/', url = 'assets/models/model.gltf'\n      // resourcePath = 'https://my-cnd-server.com/assets/models/'\n      // referenced resource 'model.bin' will be loaded from 'https://my-cnd-server.com/assets/models/model.bin'\n      // referenced resource '../textures/texture.png' will be loaded from 'https://my-cnd-server.com/assets/textures/texture.png'\n      const relativeUrl = LoaderUtils.extractUrlBase(url);\n      resourcePath = LoaderUtils.resolveURL(relativeUrl, this.path);\n    } else {\n      resourcePath = LoaderUtils.extractUrlBase(url);\n    }\n\n    // Tells the LoadingManager to track an extra item, which resolves after\n    // the model is fully loaded. This means the count of items loaded will\n    // be incorrect, but ensures manager.onLoad() does not fire early.\n    this.manager.itemStart(url);\n    const _onError = function (e) {\n      if (onError) {\n        onError(e);\n      } else {\n        console.error(e);\n      }\n      scope.manager.itemError(url);\n      scope.manager.itemEnd(url);\n    };\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setResponseType('arraybuffer');\n    loader.setRequestHeader(this.requestHeader);\n    loader.setWithCredentials(this.withCredentials);\n    loader.load(url, function (data) {\n      try {\n        scope.parse(data, resourcePath, function (gltf) {\n          onLoad(gltf);\n          scope.manager.itemEnd(url);\n        }, _onError);\n      } catch (e) {\n        _onError(e);\n      }\n    }, onProgress, _onError);\n  }\n\n  /**\n   * Sets the given Draco loader to this loader. Required for decoding assets\n   * compressed with the `KHR_draco_mesh_compression` extension.\n   *\n   * @param {DRACOLoader} dracoLoader - The Draco loader to set.\n   * @return {GLTFLoader} A reference to this loader.\n   */\n  setDRACOLoader(dracoLoader) {\n    this.dracoLoader = dracoLoader;\n    return this;\n  }\n\n  /**\n   * Sets the given KTX2 loader to this loader. Required for loading KTX2\n   * compressed textures.\n   *\n   * @param {KTX2Loader} ktx2Loader - The KTX2 loader to set.\n   * @return {GLTFLoader} A reference to this loader.\n   */\n  setKTX2Loader(ktx2Loader) {\n    this.ktx2Loader = ktx2Loader;\n    return this;\n  }\n\n  /**\n   * Sets the given meshopt decoder. Required for decoding assets\n   * compressed with the `EXT_meshopt_compression` extension.\n   *\n   * @param {Object} meshoptDecoder - The meshopt decoder to set.\n   * @return {GLTFLoader} A reference to this loader.\n   */\n  setMeshoptDecoder(meshoptDecoder) {\n    this.meshoptDecoder = meshoptDecoder;\n    return this;\n  }\n\n  /**\n   * Registers a plugin callback. This API is internally used to implement the various\n   * glTF extensions but can also used by third-party code to add additional logic\n   * to the loader.\n   *\n   * @param {function(parser:GLTFParser)} callback - The callback function to register.\n   * @return {GLTFLoader} A reference to this loader.\n   */\n  register(callback) {\n    if (this.pluginCallbacks.indexOf(callback) === -1) {\n      this.pluginCallbacks.push(callback);\n    }\n    return this;\n  }\n\n  /**\n   * Unregisters a plugin callback.\n   *\n   * @param {Function} callback - The callback function to unregister.\n   * @return {GLTFLoader} A reference to this loader.\n   */\n  unregister(callback) {\n    if (this.pluginCallbacks.indexOf(callback) !== -1) {\n      this.pluginCallbacks.splice(this.pluginCallbacks.indexOf(callback), 1);\n    }\n    return this;\n  }\n\n  /**\n   * Parses the given FBX data and returns the resulting group.\n   *\n   * @param {string|ArrayBuffer} data - The raw glTF data.\n   * @param {string} path - The URL base path.\n   * @param {function(GLTFLoader~LoadObject)} onLoad - Executed when the loading process has been finished.\n   * @param {onErrorCallback} onError - Executed when errors occur.\n   */\n  parse(data, path, onLoad, onError) {\n    let json;\n    const extensions = {};\n    const plugins = {};\n    const textDecoder = new TextDecoder();\n    if (typeof data === 'string') {\n      json = JSON.parse(data);\n    } else if (data instanceof ArrayBuffer) {\n      const magic = textDecoder.decode(new Uint8Array(data, 0, 4));\n      if (magic === BINARY_EXTENSION_HEADER_MAGIC) {\n        try {\n          extensions[EXTENSIONS.KHR_BINARY_GLTF] = new GLTFBinaryExtension(data);\n        } catch (error) {\n          if (onError) onError(error);\n          return;\n        }\n        json = JSON.parse(extensions[EXTENSIONS.KHR_BINARY_GLTF].content);\n      } else {\n        json = JSON.parse(textDecoder.decode(data));\n      }\n    } else {\n      json = data;\n    }\n    if (json.asset === undefined || json.asset.version[0] < 2) {\n      if (onError) onError(new Error('THREE.GLTFLoader: Unsupported asset. glTF versions >=2.0 are supported.'));\n      return;\n    }\n    const parser = new GLTFParser(json, {\n      path: path || this.resourcePath || '',\n      crossOrigin: this.crossOrigin,\n      requestHeader: this.requestHeader,\n      manager: this.manager,\n      ktx2Loader: this.ktx2Loader,\n      meshoptDecoder: this.meshoptDecoder\n    });\n    parser.fileLoader.setRequestHeader(this.requestHeader);\n    for (let i = 0; i < this.pluginCallbacks.length; i++) {\n      const plugin = this.pluginCallbacks[i](parser);\n      if (!plugin.name) console.error('THREE.GLTFLoader: Invalid plugin found: missing name');\n      plugins[plugin.name] = plugin;\n\n      // Workaround to avoid determining as unknown extension\n      // in addUnknownExtensionsToUserData().\n      // Remove this workaround if we move all the existing\n      // extension handlers to plugin system\n      extensions[plugin.name] = true;\n    }\n    if (json.extensionsUsed) {\n      for (let i = 0; i < json.extensionsUsed.length; ++i) {\n        const extensionName = json.extensionsUsed[i];\n        const extensionsRequired = json.extensionsRequired || [];\n        switch (extensionName) {\n          case EXTENSIONS.KHR_MATERIALS_UNLIT:\n            extensions[extensionName] = new GLTFMaterialsUnlitExtension();\n            break;\n          case EXTENSIONS.KHR_DRACO_MESH_COMPRESSION:\n            extensions[extensionName] = new GLTFDracoMeshCompressionExtension(json, this.dracoLoader);\n            break;\n          case EXTENSIONS.KHR_TEXTURE_TRANSFORM:\n            extensions[extensionName] = new GLTFTextureTransformExtension();\n            break;\n          case EXTENSIONS.KHR_MESH_QUANTIZATION:\n            extensions[extensionName] = new GLTFMeshQuantizationExtension();\n            break;\n          default:\n            if (extensionsRequired.indexOf(extensionName) >= 0 && plugins[extensionName] === undefined) {\n              console.warn('THREE.GLTFLoader: Unknown extension \"' + extensionName + '\".');\n            }\n        }\n      }\n    }\n    parser.setExtensions(extensions);\n    parser.setPlugins(plugins);\n    parser.parse(onLoad, onError);\n  }\n\n  /**\n   * Async version of {@link GLTFLoader#parse}.\n   *\n   * @async\n   * @param {string|ArrayBuffer} data - The raw glTF data.\n   * @param {string} path - The URL base path.\n   * @return {Promise<GLTFLoader~LoadObject>} A Promise that resolves with the loaded glTF when the parsing has been finished.\n   */\n  parseAsync(data, path) {\n    const scope = this;\n    return new Promise(function (resolve, reject) {\n      scope.parse(data, path, resolve, reject);\n    });\n  }\n}\n\n/* GLTFREGISTRY */\n\nfunction GLTFRegistry() {\n  let objects = {};\n  return {\n    get: function (key) {\n      return objects[key];\n    },\n    add: function (key, object) {\n      objects[key] = object;\n    },\n    remove: function (key) {\n      delete objects[key];\n    },\n    removeAll: function () {\n      objects = {};\n    }\n  };\n}\n\n/*********************************/\n/********** EXTENSIONS ***********/\n/*********************************/\n\nconst EXTENSIONS = {\n  KHR_BINARY_GLTF: 'KHR_binary_glTF',\n  KHR_DRACO_MESH_COMPRESSION: 'KHR_draco_mesh_compression',\n  KHR_LIGHTS_PUNCTUAL: 'KHR_lights_punctual',\n  KHR_MATERIALS_CLEARCOAT: 'KHR_materials_clearcoat',\n  KHR_MATERIALS_DISPERSION: 'KHR_materials_dispersion',\n  KHR_MATERIALS_IOR: 'KHR_materials_ior',\n  KHR_MATERIALS_SHEEN: 'KHR_materials_sheen',\n  KHR_MATERIALS_SPECULAR: 'KHR_materials_specular',\n  KHR_MATERIALS_TRANSMISSION: 'KHR_materials_transmission',\n  KHR_MATERIALS_IRIDESCENCE: 'KHR_materials_iridescence',\n  KHR_MATERIALS_ANISOTROPY: 'KHR_materials_anisotropy',\n  KHR_MATERIALS_UNLIT: 'KHR_materials_unlit',\n  KHR_MATERIALS_VOLUME: 'KHR_materials_volume',\n  KHR_TEXTURE_BASISU: 'KHR_texture_basisu',\n  KHR_TEXTURE_TRANSFORM: 'KHR_texture_transform',\n  KHR_MESH_QUANTIZATION: 'KHR_mesh_quantization',\n  KHR_MATERIALS_EMISSIVE_STRENGTH: 'KHR_materials_emissive_strength',\n  EXT_MATERIALS_BUMP: 'EXT_materials_bump',\n  EXT_TEXTURE_WEBP: 'EXT_texture_webp',\n  EXT_TEXTURE_AVIF: 'EXT_texture_avif',\n  EXT_MESHOPT_COMPRESSION: 'EXT_meshopt_compression',\n  EXT_MESH_GPU_INSTANCING: 'EXT_mesh_gpu_instancing'\n};\n\n/**\n * Punctual Lights Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_lights_punctual\n *\n * @private\n */\nclass GLTFLightsExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_LIGHTS_PUNCTUAL;\n\n    // Object3D instance caches\n    this.cache = {\n      refs: {},\n      uses: {}\n    };\n  }\n  _markDefs() {\n    const parser = this.parser;\n    const nodeDefs = this.parser.json.nodes || [];\n    for (let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex++) {\n      const nodeDef = nodeDefs[nodeIndex];\n      if (nodeDef.extensions && nodeDef.extensions[this.name] && nodeDef.extensions[this.name].light !== undefined) {\n        parser._addNodeRef(this.cache, nodeDef.extensions[this.name].light);\n      }\n    }\n  }\n  _loadLight(lightIndex) {\n    const parser = this.parser;\n    const cacheKey = 'light:' + lightIndex;\n    let dependency = parser.cache.get(cacheKey);\n    if (dependency) return dependency;\n    const json = parser.json;\n    const extensions = json.extensions && json.extensions[this.name] || {};\n    const lightDefs = extensions.lights || [];\n    const lightDef = lightDefs[lightIndex];\n    let lightNode;\n    const color = new Color(0xffffff);\n    if (lightDef.color !== undefined) color.setRGB(lightDef.color[0], lightDef.color[1], lightDef.color[2], LinearSRGBColorSpace);\n    const range = lightDef.range !== undefined ? lightDef.range : 0;\n    switch (lightDef.type) {\n      case 'directional':\n        lightNode = new DirectionalLight(color);\n        lightNode.target.position.set(0, 0, -1);\n        lightNode.add(lightNode.target);\n        break;\n      case 'point':\n        lightNode = new PointLight(color);\n        lightNode.distance = range;\n        break;\n      case 'spot':\n        lightNode = new SpotLight(color);\n        lightNode.distance = range;\n        // Handle spotlight properties.\n        lightDef.spot = lightDef.spot || {};\n        lightDef.spot.innerConeAngle = lightDef.spot.innerConeAngle !== undefined ? lightDef.spot.innerConeAngle : 0;\n        lightDef.spot.outerConeAngle = lightDef.spot.outerConeAngle !== undefined ? lightDef.spot.outerConeAngle : Math.PI / 4.0;\n        lightNode.angle = lightDef.spot.outerConeAngle;\n        lightNode.penumbra = 1.0 - lightDef.spot.innerConeAngle / lightDef.spot.outerConeAngle;\n        lightNode.target.position.set(0, 0, -1);\n        lightNode.add(lightNode.target);\n        break;\n      default:\n        throw new Error('THREE.GLTFLoader: Unexpected light type: ' + lightDef.type);\n    }\n\n    // Some lights (e.g. spot) default to a position other than the origin. Reset the position\n    // here, because node-level parsing will only override position if explicitly specified.\n    lightNode.position.set(0, 0, 0);\n    assignExtrasToUserData(lightNode, lightDef);\n    if (lightDef.intensity !== undefined) lightNode.intensity = lightDef.intensity;\n    lightNode.name = parser.createUniqueName(lightDef.name || 'light_' + lightIndex);\n    dependency = Promise.resolve(lightNode);\n    parser.cache.add(cacheKey, dependency);\n    return dependency;\n  }\n  getDependency(type, index) {\n    if (type !== 'light') return;\n    return this._loadLight(index);\n  }\n  createNodeAttachment(nodeIndex) {\n    const self = this;\n    const parser = this.parser;\n    const json = parser.json;\n    const nodeDef = json.nodes[nodeIndex];\n    const lightDef = nodeDef.extensions && nodeDef.extensions[this.name] || {};\n    const lightIndex = lightDef.light;\n    if (lightIndex === undefined) return null;\n    return this._loadLight(lightIndex).then(function (light) {\n      return parser._getNodeRef(self.cache, lightIndex, light);\n    });\n  }\n}\n\n/**\n * Unlit Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_unlit\n *\n * @private\n */\nclass GLTFMaterialsUnlitExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_MATERIALS_UNLIT;\n  }\n  getMaterialType() {\n    return MeshBasicMaterial;\n  }\n  extendParams(materialParams, materialDef, parser) {\n    const pending = [];\n    materialParams.color = new Color(1.0, 1.0, 1.0);\n    materialParams.opacity = 1.0;\n    const metallicRoughness = materialDef.pbrMetallicRoughness;\n    if (metallicRoughness) {\n      if (Array.isArray(metallicRoughness.baseColorFactor)) {\n        const array = metallicRoughness.baseColorFactor;\n        materialParams.color.setRGB(array[0], array[1], array[2], LinearSRGBColorSpace);\n        materialParams.opacity = array[3];\n      }\n      if (metallicRoughness.baseColorTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace));\n      }\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Materials Emissive Strength Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/blob/5768b3ce0ef32bc39cdf1bef10b948586635ead3/extensions/2.0/Khronos/KHR_materials_emissive_strength/README.md\n *\n * @private\n */\nclass GLTFMaterialsEmissiveStrengthExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_EMISSIVE_STRENGTH;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const emissiveStrength = materialDef.extensions[this.name].emissiveStrength;\n    if (emissiveStrength !== undefined) {\n      materialParams.emissiveIntensity = emissiveStrength;\n    }\n    return Promise.resolve();\n  }\n}\n\n/**\n * Clearcoat Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_clearcoat\n *\n * @private\n */\nclass GLTFMaterialsClearcoatExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_CLEARCOAT;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    if (extension.clearcoatFactor !== undefined) {\n      materialParams.clearcoat = extension.clearcoatFactor;\n    }\n    if (extension.clearcoatTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatMap', extension.clearcoatTexture));\n    }\n    if (extension.clearcoatRoughnessFactor !== undefined) {\n      materialParams.clearcoatRoughness = extension.clearcoatRoughnessFactor;\n    }\n    if (extension.clearcoatRoughnessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatRoughnessMap', extension.clearcoatRoughnessTexture));\n    }\n    if (extension.clearcoatNormalTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'clearcoatNormalMap', extension.clearcoatNormalTexture));\n      if (extension.clearcoatNormalTexture.scale !== undefined) {\n        const scale = extension.clearcoatNormalTexture.scale;\n        materialParams.clearcoatNormalScale = new Vector2(scale, scale);\n      }\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Materials dispersion Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_dispersion\n *\n * @private\n */\nclass GLTFMaterialsDispersionExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_DISPERSION;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const extension = materialDef.extensions[this.name];\n    materialParams.dispersion = extension.dispersion !== undefined ? extension.dispersion : 0;\n    return Promise.resolve();\n  }\n}\n\n/**\n * Iridescence Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_iridescence\n *\n * @private\n */\nclass GLTFMaterialsIridescenceExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_IRIDESCENCE;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    if (extension.iridescenceFactor !== undefined) {\n      materialParams.iridescence = extension.iridescenceFactor;\n    }\n    if (extension.iridescenceTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'iridescenceMap', extension.iridescenceTexture));\n    }\n    if (extension.iridescenceIor !== undefined) {\n      materialParams.iridescenceIOR = extension.iridescenceIor;\n    }\n    if (materialParams.iridescenceThicknessRange === undefined) {\n      materialParams.iridescenceThicknessRange = [100, 400];\n    }\n    if (extension.iridescenceThicknessMinimum !== undefined) {\n      materialParams.iridescenceThicknessRange[0] = extension.iridescenceThicknessMinimum;\n    }\n    if (extension.iridescenceThicknessMaximum !== undefined) {\n      materialParams.iridescenceThicknessRange[1] = extension.iridescenceThicknessMaximum;\n    }\n    if (extension.iridescenceThicknessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'iridescenceThicknessMap', extension.iridescenceThicknessTexture));\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Sheen Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_sheen\n *\n * @private\n */\nclass GLTFMaterialsSheenExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_SHEEN;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    materialParams.sheenColor = new Color(0, 0, 0);\n    materialParams.sheenRoughness = 0;\n    materialParams.sheen = 1;\n    const extension = materialDef.extensions[this.name];\n    if (extension.sheenColorFactor !== undefined) {\n      const colorFactor = extension.sheenColorFactor;\n      materialParams.sheenColor.setRGB(colorFactor[0], colorFactor[1], colorFactor[2], LinearSRGBColorSpace);\n    }\n    if (extension.sheenRoughnessFactor !== undefined) {\n      materialParams.sheenRoughness = extension.sheenRoughnessFactor;\n    }\n    if (extension.sheenColorTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'sheenColorMap', extension.sheenColorTexture, SRGBColorSpace));\n    }\n    if (extension.sheenRoughnessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'sheenRoughnessMap', extension.sheenRoughnessTexture));\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Transmission Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_transmission\n * Draft: https://github.com/KhronosGroup/glTF/pull/1698\n *\n * @private\n */\nclass GLTFMaterialsTransmissionExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_TRANSMISSION;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    if (extension.transmissionFactor !== undefined) {\n      materialParams.transmission = extension.transmissionFactor;\n    }\n    if (extension.transmissionTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'transmissionMap', extension.transmissionTexture));\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Materials Volume Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_volume\n *\n * @private\n */\nclass GLTFMaterialsVolumeExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_VOLUME;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    materialParams.thickness = extension.thicknessFactor !== undefined ? extension.thicknessFactor : 0;\n    if (extension.thicknessTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'thicknessMap', extension.thicknessTexture));\n    }\n    materialParams.attenuationDistance = extension.attenuationDistance || Infinity;\n    const colorArray = extension.attenuationColor || [1, 1, 1];\n    materialParams.attenuationColor = new Color().setRGB(colorArray[0], colorArray[1], colorArray[2], LinearSRGBColorSpace);\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Materials ior Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_ior\n *\n * @private\n */\nclass GLTFMaterialsIorExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_IOR;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const extension = materialDef.extensions[this.name];\n    materialParams.ior = extension.ior !== undefined ? extension.ior : 1.5;\n    return Promise.resolve();\n  }\n}\n\n/**\n * Materials specular Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_specular\n *\n * @private\n */\nclass GLTFMaterialsSpecularExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_SPECULAR;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    materialParams.specularIntensity = extension.specularFactor !== undefined ? extension.specularFactor : 1.0;\n    if (extension.specularTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'specularIntensityMap', extension.specularTexture));\n    }\n    const colorArray = extension.specularColorFactor || [1, 1, 1];\n    materialParams.specularColor = new Color().setRGB(colorArray[0], colorArray[1], colorArray[2], LinearSRGBColorSpace);\n    if (extension.specularColorTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'specularColorMap', extension.specularColorTexture, SRGBColorSpace));\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Materials bump Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/EXT_materials_bump\n *\n * @private\n */\nclass GLTFMaterialsBumpExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.EXT_MATERIALS_BUMP;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    materialParams.bumpScale = extension.bumpFactor !== undefined ? extension.bumpFactor : 1.0;\n    if (extension.bumpTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'bumpMap', extension.bumpTexture));\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * Materials anisotropy Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_anisotropy\n *\n * @private\n */\nclass GLTFMaterialsAnisotropyExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_MATERIALS_ANISOTROPY;\n  }\n  getMaterialType(materialIndex) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) return null;\n    return MeshPhysicalMaterial;\n  }\n  extendMaterialParams(materialIndex, materialParams) {\n    const parser = this.parser;\n    const materialDef = parser.json.materials[materialIndex];\n    if (!materialDef.extensions || !materialDef.extensions[this.name]) {\n      return Promise.resolve();\n    }\n    const pending = [];\n    const extension = materialDef.extensions[this.name];\n    if (extension.anisotropyStrength !== undefined) {\n      materialParams.anisotropy = extension.anisotropyStrength;\n    }\n    if (extension.anisotropyRotation !== undefined) {\n      materialParams.anisotropyRotation = extension.anisotropyRotation;\n    }\n    if (extension.anisotropyTexture !== undefined) {\n      pending.push(parser.assignTexture(materialParams, 'anisotropyMap', extension.anisotropyTexture));\n    }\n    return Promise.all(pending);\n  }\n}\n\n/**\n * BasisU Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_basisu\n *\n * @private\n */\nclass GLTFTextureBasisUExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.KHR_TEXTURE_BASISU;\n  }\n  loadTexture(textureIndex) {\n    const parser = this.parser;\n    const json = parser.json;\n    const textureDef = json.textures[textureIndex];\n    if (!textureDef.extensions || !textureDef.extensions[this.name]) {\n      return null;\n    }\n    const extension = textureDef.extensions[this.name];\n    const loader = parser.options.ktx2Loader;\n    if (!loader) {\n      if (json.extensionsRequired && json.extensionsRequired.indexOf(this.name) >= 0) {\n        throw new Error('THREE.GLTFLoader: setKTX2Loader must be called before loading KTX2 textures');\n      } else {\n        // Assumes that the extension is optional and that a fallback texture is present\n        return null;\n      }\n    }\n    return parser.loadTextureImage(textureIndex, extension.source, loader);\n  }\n}\n\n/**\n * WebP Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_webp\n *\n * @private\n */\nclass GLTFTextureWebPExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.EXT_TEXTURE_WEBP;\n  }\n  loadTexture(textureIndex) {\n    const name = this.name;\n    const parser = this.parser;\n    const json = parser.json;\n    const textureDef = json.textures[textureIndex];\n    if (!textureDef.extensions || !textureDef.extensions[name]) {\n      return null;\n    }\n    const extension = textureDef.extensions[name];\n    const source = json.images[extension.source];\n    let loader = parser.textureLoader;\n    if (source.uri) {\n      const handler = parser.options.manager.getHandler(source.uri);\n      if (handler !== null) loader = handler;\n    }\n    return parser.loadTextureImage(textureIndex, extension.source, loader);\n  }\n}\n\n/**\n * AVIF Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_avif\n *\n * @private\n */\nclass GLTFTextureAVIFExtension {\n  constructor(parser) {\n    this.parser = parser;\n    this.name = EXTENSIONS.EXT_TEXTURE_AVIF;\n  }\n  loadTexture(textureIndex) {\n    const name = this.name;\n    const parser = this.parser;\n    const json = parser.json;\n    const textureDef = json.textures[textureIndex];\n    if (!textureDef.extensions || !textureDef.extensions[name]) {\n      return null;\n    }\n    const extension = textureDef.extensions[name];\n    const source = json.images[extension.source];\n    let loader = parser.textureLoader;\n    if (source.uri) {\n      const handler = parser.options.manager.getHandler(source.uri);\n      if (handler !== null) loader = handler;\n    }\n    return parser.loadTextureImage(textureIndex, extension.source, loader);\n  }\n}\n\n/**\n * meshopt BufferView Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_meshopt_compression\n *\n * @private\n */\nclass GLTFMeshoptCompression {\n  constructor(parser) {\n    this.name = EXTENSIONS.EXT_MESHOPT_COMPRESSION;\n    this.parser = parser;\n  }\n  loadBufferView(index) {\n    const json = this.parser.json;\n    const bufferView = json.bufferViews[index];\n    if (bufferView.extensions && bufferView.extensions[this.name]) {\n      const extensionDef = bufferView.extensions[this.name];\n      const buffer = this.parser.getDependency('buffer', extensionDef.buffer);\n      const decoder = this.parser.options.meshoptDecoder;\n      if (!decoder || !decoder.supported) {\n        if (json.extensionsRequired && json.extensionsRequired.indexOf(this.name) >= 0) {\n          throw new Error('THREE.GLTFLoader: setMeshoptDecoder must be called before loading compressed files');\n        } else {\n          // Assumes that the extension is optional and that fallback buffer data is present\n          return null;\n        }\n      }\n      return buffer.then(function (res) {\n        const byteOffset = extensionDef.byteOffset || 0;\n        const byteLength = extensionDef.byteLength || 0;\n        const count = extensionDef.count;\n        const stride = extensionDef.byteStride;\n        const source = new Uint8Array(res, byteOffset, byteLength);\n        if (decoder.decodeGltfBufferAsync) {\n          return decoder.decodeGltfBufferAsync(count, stride, source, extensionDef.mode, extensionDef.filter).then(function (res) {\n            return res.buffer;\n          });\n        } else {\n          // Support for MeshoptDecoder 0.18 or earlier, without decodeGltfBufferAsync\n          return decoder.ready.then(function () {\n            const result = new ArrayBuffer(count * stride);\n            decoder.decodeGltfBuffer(new Uint8Array(result), count, stride, source, extensionDef.mode, extensionDef.filter);\n            return result;\n          });\n        }\n      });\n    } else {\n      return null;\n    }\n  }\n}\n\n/**\n * GPU Instancing Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_mesh_gpu_instancing\n *\n * @private\n */\nclass GLTFMeshGpuInstancing {\n  constructor(parser) {\n    this.name = EXTENSIONS.EXT_MESH_GPU_INSTANCING;\n    this.parser = parser;\n  }\n  createNodeMesh(nodeIndex) {\n    const json = this.parser.json;\n    const nodeDef = json.nodes[nodeIndex];\n    if (!nodeDef.extensions || !nodeDef.extensions[this.name] || nodeDef.mesh === undefined) {\n      return null;\n    }\n    const meshDef = json.meshes[nodeDef.mesh];\n\n    // No Points or Lines + Instancing support yet\n\n    for (const primitive of meshDef.primitives) {\n      if (primitive.mode !== WEBGL_CONSTANTS.TRIANGLES && primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_STRIP && primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_FAN && primitive.mode !== undefined) {\n        return null;\n      }\n    }\n    const extensionDef = nodeDef.extensions[this.name];\n    const attributesDef = extensionDef.attributes;\n\n    // @TODO: Can we support InstancedMesh + SkinnedMesh?\n\n    const pending = [];\n    const attributes = {};\n    for (const key in attributesDef) {\n      pending.push(this.parser.getDependency('accessor', attributesDef[key]).then(accessor => {\n        attributes[key] = accessor;\n        return attributes[key];\n      }));\n    }\n    if (pending.length < 1) {\n      return null;\n    }\n    pending.push(this.parser.createNodeMesh(nodeIndex));\n    return Promise.all(pending).then(results => {\n      const nodeObject = results.pop();\n      const meshes = nodeObject.isGroup ? nodeObject.children : [nodeObject];\n      const count = results[0].count; // All attribute counts should be same\n      const instancedMeshes = [];\n      for (const mesh of meshes) {\n        // Temporal variables\n        const m = new Matrix4();\n        const p = new Vector3();\n        const q = new Quaternion();\n        const s = new Vector3(1, 1, 1);\n        const instancedMesh = new InstancedMesh(mesh.geometry, mesh.material, count);\n        for (let i = 0; i < count; i++) {\n          if (attributes.TRANSLATION) {\n            p.fromBufferAttribute(attributes.TRANSLATION, i);\n          }\n          if (attributes.ROTATION) {\n            q.fromBufferAttribute(attributes.ROTATION, i);\n          }\n          if (attributes.SCALE) {\n            s.fromBufferAttribute(attributes.SCALE, i);\n          }\n          instancedMesh.setMatrixAt(i, m.compose(p, q, s));\n        }\n\n        // Add instance attributes to the geometry, excluding TRS.\n        for (const attributeName in attributes) {\n          if (attributeName === '_COLOR_0') {\n            const attr = attributes[attributeName];\n            instancedMesh.instanceColor = new InstancedBufferAttribute(attr.array, attr.itemSize, attr.normalized);\n          } else if (attributeName !== 'TRANSLATION' && attributeName !== 'ROTATION' && attributeName !== 'SCALE') {\n            mesh.geometry.setAttribute(attributeName, attributes[attributeName]);\n          }\n        }\n\n        // Just in case\n        Object3D.prototype.copy.call(instancedMesh, mesh);\n        this.parser.assignFinalMaterial(instancedMesh);\n        instancedMeshes.push(instancedMesh);\n      }\n      if (nodeObject.isGroup) {\n        nodeObject.clear();\n        nodeObject.add(...instancedMeshes);\n        return nodeObject;\n      }\n      return instancedMeshes[0];\n    });\n  }\n}\n\n/* BINARY EXTENSION */\nconst BINARY_EXTENSION_HEADER_MAGIC = 'glTF';\nconst BINARY_EXTENSION_HEADER_LENGTH = 12;\nconst BINARY_EXTENSION_CHUNK_TYPES = {\n  JSON: 0x4E4F534A,\n  BIN: 0x004E4942\n};\nclass GLTFBinaryExtension {\n  constructor(data) {\n    this.name = EXTENSIONS.KHR_BINARY_GLTF;\n    this.content = null;\n    this.body = null;\n    const headerView = new DataView(data, 0, BINARY_EXTENSION_HEADER_LENGTH);\n    const textDecoder = new TextDecoder();\n    this.header = {\n      magic: textDecoder.decode(new Uint8Array(data.slice(0, 4))),\n      version: headerView.getUint32(4, true),\n      length: headerView.getUint32(8, true)\n    };\n    if (this.header.magic !== BINARY_EXTENSION_HEADER_MAGIC) {\n      throw new Error('THREE.GLTFLoader: Unsupported glTF-Binary header.');\n    } else if (this.header.version < 2.0) {\n      throw new Error('THREE.GLTFLoader: Legacy binary file detected.');\n    }\n    const chunkContentsLength = this.header.length - BINARY_EXTENSION_HEADER_LENGTH;\n    const chunkView = new DataView(data, BINARY_EXTENSION_HEADER_LENGTH);\n    let chunkIndex = 0;\n    while (chunkIndex < chunkContentsLength) {\n      const chunkLength = chunkView.getUint32(chunkIndex, true);\n      chunkIndex += 4;\n      const chunkType = chunkView.getUint32(chunkIndex, true);\n      chunkIndex += 4;\n      if (chunkType === BINARY_EXTENSION_CHUNK_TYPES.JSON) {\n        const contentArray = new Uint8Array(data, BINARY_EXTENSION_HEADER_LENGTH + chunkIndex, chunkLength);\n        this.content = textDecoder.decode(contentArray);\n      } else if (chunkType === BINARY_EXTENSION_CHUNK_TYPES.BIN) {\n        const byteOffset = BINARY_EXTENSION_HEADER_LENGTH + chunkIndex;\n        this.body = data.slice(byteOffset, byteOffset + chunkLength);\n      }\n\n      // Clients must ignore chunks with unknown types.\n\n      chunkIndex += chunkLength;\n    }\n    if (this.content === null) {\n      throw new Error('THREE.GLTFLoader: JSON content not found.');\n    }\n  }\n}\n\n/**\n * DRACO Mesh Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_draco_mesh_compression\n *\n * @private\n */\nclass GLTFDracoMeshCompressionExtension {\n  constructor(json, dracoLoader) {\n    if (!dracoLoader) {\n      throw new Error('THREE.GLTFLoader: No DRACOLoader instance provided.');\n    }\n    this.name = EXTENSIONS.KHR_DRACO_MESH_COMPRESSION;\n    this.json = json;\n    this.dracoLoader = dracoLoader;\n    this.dracoLoader.preload();\n  }\n  decodePrimitive(primitive, parser) {\n    const json = this.json;\n    const dracoLoader = this.dracoLoader;\n    const bufferViewIndex = primitive.extensions[this.name].bufferView;\n    const gltfAttributeMap = primitive.extensions[this.name].attributes;\n    const threeAttributeMap = {};\n    const attributeNormalizedMap = {};\n    const attributeTypeMap = {};\n    for (const attributeName in gltfAttributeMap) {\n      const threeAttributeName = ATTRIBUTES[attributeName] || attributeName.toLowerCase();\n      threeAttributeMap[threeAttributeName] = gltfAttributeMap[attributeName];\n    }\n    for (const attributeName in primitive.attributes) {\n      const threeAttributeName = ATTRIBUTES[attributeName] || attributeName.toLowerCase();\n      if (gltfAttributeMap[attributeName] !== undefined) {\n        const accessorDef = json.accessors[primitive.attributes[attributeName]];\n        const componentType = WEBGL_COMPONENT_TYPES[accessorDef.componentType];\n        attributeTypeMap[threeAttributeName] = componentType.name;\n        attributeNormalizedMap[threeAttributeName] = accessorDef.normalized === true;\n      }\n    }\n    return parser.getDependency('bufferView', bufferViewIndex).then(function (bufferView) {\n      return new Promise(function (resolve, reject) {\n        dracoLoader.decodeDracoFile(bufferView, function (geometry) {\n          for (const attributeName in geometry.attributes) {\n            const attribute = geometry.attributes[attributeName];\n            const normalized = attributeNormalizedMap[attributeName];\n            if (normalized !== undefined) attribute.normalized = normalized;\n          }\n          resolve(geometry);\n        }, threeAttributeMap, attributeTypeMap, LinearSRGBColorSpace, reject);\n      });\n    });\n  }\n}\n\n/**\n * Texture Transform Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_transform\n *\n * @private\n */\nclass GLTFTextureTransformExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_TEXTURE_TRANSFORM;\n  }\n  extendTexture(texture, transform) {\n    if ((transform.texCoord === undefined || transform.texCoord === texture.channel) && transform.offset === undefined && transform.rotation === undefined && transform.scale === undefined) {\n      // See https://github.com/mrdoob/three.js/issues/21819.\n      return texture;\n    }\n    texture = texture.clone();\n    if (transform.texCoord !== undefined) {\n      texture.channel = transform.texCoord;\n    }\n    if (transform.offset !== undefined) {\n      texture.offset.fromArray(transform.offset);\n    }\n    if (transform.rotation !== undefined) {\n      texture.rotation = transform.rotation;\n    }\n    if (transform.scale !== undefined) {\n      texture.repeat.fromArray(transform.scale);\n    }\n    texture.needsUpdate = true;\n    return texture;\n  }\n}\n\n/**\n * Mesh Quantization Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization\n *\n * @private\n */\nclass GLTFMeshQuantizationExtension {\n  constructor() {\n    this.name = EXTENSIONS.KHR_MESH_QUANTIZATION;\n  }\n}\n\n/*********************************/\n/********** INTERPOLATION ********/\n/*********************************/\n\n// Spline Interpolation\n// Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#appendix-c-spline-interpolation\nclass GLTFCubicSplineInterpolant extends Interpolant {\n  constructor(parameterPositions, sampleValues, sampleSize, resultBuffer) {\n    super(parameterPositions, sampleValues, sampleSize, resultBuffer);\n  }\n  copySampleValue_(index) {\n    // Copies a sample value to the result buffer. See description of glTF\n    // CUBICSPLINE values layout in interpolate_() function below.\n\n    const result = this.resultBuffer,\n      values = this.sampleValues,\n      valueSize = this.valueSize,\n      offset = index * valueSize * 3 + valueSize;\n    for (let i = 0; i !== valueSize; i++) {\n      result[i] = values[offset + i];\n    }\n    return result;\n  }\n  interpolate_(i1, t0, t, t1) {\n    const result = this.resultBuffer;\n    const values = this.sampleValues;\n    const stride = this.valueSize;\n    const stride2 = stride * 2;\n    const stride3 = stride * 3;\n    const td = t1 - t0;\n    const p = (t - t0) / td;\n    const pp = p * p;\n    const ppp = pp * p;\n    const offset1 = i1 * stride3;\n    const offset0 = offset1 - stride3;\n    const s2 = -2 * ppp + 3 * pp;\n    const s3 = ppp - pp;\n    const s0 = 1 - s2;\n    const s1 = s3 - pp + p;\n\n    // Layout of keyframe output values for CUBICSPLINE animations:\n    //   [ inTangent_1, splineVertex_1, outTangent_1, inTangent_2, splineVertex_2, ... ]\n    for (let i = 0; i !== stride; i++) {\n      const p0 = values[offset0 + i + stride]; // splineVertex_k\n      const m0 = values[offset0 + i + stride2] * td; // outTangent_k * (t_k+1 - t_k)\n      const p1 = values[offset1 + i + stride]; // splineVertex_k+1\n      const m1 = values[offset1 + i] * td; // inTangent_k+1 * (t_k+1 - t_k)\n\n      result[i] = s0 * p0 + s1 * m0 + s2 * p1 + s3 * m1;\n    }\n    return result;\n  }\n}\nconst _quaternion = new Quaternion();\nclass GLTFCubicSplineQuaternionInterpolant extends GLTFCubicSplineInterpolant {\n  interpolate_(i1, t0, t, t1) {\n    const result = super.interpolate_(i1, t0, t, t1);\n    _quaternion.fromArray(result).normalize().toArray(result);\n    return result;\n  }\n}\n\n/*********************************/\n/********** INTERNALS ************/\n/*********************************/\n\n/* CONSTANTS */\n\nconst WEBGL_CONSTANTS = {\n  FLOAT: 5126,\n  //FLOAT_MAT2: 35674,\n  FLOAT_MAT3: 35675,\n  FLOAT_MAT4: 35676,\n  FLOAT_VEC2: 35664,\n  FLOAT_VEC3: 35665,\n  FLOAT_VEC4: 35666,\n  LINEAR: 9729,\n  REPEAT: 10497,\n  SAMPLER_2D: 35678,\n  POINTS: 0,\n  LINES: 1,\n  LINE_LOOP: 2,\n  LINE_STRIP: 3,\n  TRIANGLES: 4,\n  TRIANGLE_STRIP: 5,\n  TRIANGLE_FAN: 6,\n  UNSIGNED_BYTE: 5121,\n  UNSIGNED_SHORT: 5123\n};\nconst WEBGL_COMPONENT_TYPES = {\n  5120: Int8Array,\n  5121: Uint8Array,\n  5122: Int16Array,\n  5123: Uint16Array,\n  5125: Uint32Array,\n  5126: Float32Array\n};\nconst WEBGL_FILTERS = {\n  9728: NearestFilter,\n  9729: LinearFilter,\n  9984: NearestMipmapNearestFilter,\n  9985: LinearMipmapNearestFilter,\n  9986: NearestMipmapLinearFilter,\n  9987: LinearMipmapLinearFilter\n};\nconst WEBGL_WRAPPINGS = {\n  33071: ClampToEdgeWrapping,\n  33648: MirroredRepeatWrapping,\n  10497: RepeatWrapping\n};\nconst WEBGL_TYPE_SIZES = {\n  'SCALAR': 1,\n  'VEC2': 2,\n  'VEC3': 3,\n  'VEC4': 4,\n  'MAT2': 4,\n  'MAT3': 9,\n  'MAT4': 16\n};\nconst ATTRIBUTES = {\n  POSITION: 'position',\n  NORMAL: 'normal',\n  TANGENT: 'tangent',\n  TEXCOORD_0: 'uv',\n  TEXCOORD_1: 'uv1',\n  TEXCOORD_2: 'uv2',\n  TEXCOORD_3: 'uv3',\n  COLOR_0: 'color',\n  WEIGHTS_0: 'skinWeight',\n  JOINTS_0: 'skinIndex'\n};\nconst PATH_PROPERTIES = {\n  scale: 'scale',\n  translation: 'position',\n  rotation: 'quaternion',\n  weights: 'morphTargetInfluences'\n};\nconst INTERPOLATION = {\n  CUBICSPLINE: undefined,\n  // We use a custom interpolant (GLTFCubicSplineInterpolation) for CUBICSPLINE tracks. Each\n  // keyframe track will be initialized with a default interpolation type, then modified.\n  LINEAR: InterpolateLinear,\n  STEP: InterpolateDiscrete\n};\nconst ALPHA_MODES = {\n  OPAQUE: 'OPAQUE',\n  MASK: 'MASK',\n  BLEND: 'BLEND'\n};\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#default-material\n *\n * @private\n * @param {Object<string, Material>} cache\n * @return {Material}\n */\nfunction createDefaultMaterial(cache) {\n  if (cache['DefaultMaterial'] === undefined) {\n    cache['DefaultMaterial'] = new MeshStandardMaterial({\n      color: 0xFFFFFF,\n      emissive: 0x000000,\n      metalness: 1,\n      roughness: 1,\n      transparent: false,\n      depthTest: true,\n      side: FrontSide\n    });\n  }\n  return cache['DefaultMaterial'];\n}\nfunction addUnknownExtensionsToUserData(knownExtensions, object, objectDef) {\n  // Add unknown glTF extensions to an object's userData.\n\n  for (const name in objectDef.extensions) {\n    if (knownExtensions[name] === undefined) {\n      object.userData.gltfExtensions = object.userData.gltfExtensions || {};\n      object.userData.gltfExtensions[name] = objectDef.extensions[name];\n    }\n  }\n}\n\n/**\n *\n * @private\n * @param {Object3D|Material|BufferGeometry|Object} object\n * @param {GLTF.definition} gltfDef\n */\nfunction assignExtrasToUserData(object, gltfDef) {\n  if (gltfDef.extras !== undefined) {\n    if (typeof gltfDef.extras === 'object') {\n      Object.assign(object.userData, gltfDef.extras);\n    } else {\n      console.warn('THREE.GLTFLoader: Ignoring primitive type .extras, ' + gltfDef.extras);\n    }\n  }\n}\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#morph-targets\n *\n * @private\n * @param {BufferGeometry} geometry\n * @param {Array<GLTF.Target>} targets\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addMorphTargets(geometry, targets, parser) {\n  let hasMorphPosition = false;\n  let hasMorphNormal = false;\n  let hasMorphColor = false;\n  for (let i = 0, il = targets.length; i < il; i++) {\n    const target = targets[i];\n    if (target.POSITION !== undefined) hasMorphPosition = true;\n    if (target.NORMAL !== undefined) hasMorphNormal = true;\n    if (target.COLOR_0 !== undefined) hasMorphColor = true;\n    if (hasMorphPosition && hasMorphNormal && hasMorphColor) break;\n  }\n  if (!hasMorphPosition && !hasMorphNormal && !hasMorphColor) return Promise.resolve(geometry);\n  const pendingPositionAccessors = [];\n  const pendingNormalAccessors = [];\n  const pendingColorAccessors = [];\n  for (let i = 0, il = targets.length; i < il; i++) {\n    const target = targets[i];\n    if (hasMorphPosition) {\n      const pendingAccessor = target.POSITION !== undefined ? parser.getDependency('accessor', target.POSITION) : geometry.attributes.position;\n      pendingPositionAccessors.push(pendingAccessor);\n    }\n    if (hasMorphNormal) {\n      const pendingAccessor = target.NORMAL !== undefined ? parser.getDependency('accessor', target.NORMAL) : geometry.attributes.normal;\n      pendingNormalAccessors.push(pendingAccessor);\n    }\n    if (hasMorphColor) {\n      const pendingAccessor = target.COLOR_0 !== undefined ? parser.getDependency('accessor', target.COLOR_0) : geometry.attributes.color;\n      pendingColorAccessors.push(pendingAccessor);\n    }\n  }\n  return Promise.all([Promise.all(pendingPositionAccessors), Promise.all(pendingNormalAccessors), Promise.all(pendingColorAccessors)]).then(function (accessors) {\n    const morphPositions = accessors[0];\n    const morphNormals = accessors[1];\n    const morphColors = accessors[2];\n    if (hasMorphPosition) geometry.morphAttributes.position = morphPositions;\n    if (hasMorphNormal) geometry.morphAttributes.normal = morphNormals;\n    if (hasMorphColor) geometry.morphAttributes.color = morphColors;\n    geometry.morphTargetsRelative = true;\n    return geometry;\n  });\n}\n\n/**\n *\n * @private\n * @param {Mesh} mesh\n * @param {GLTF.Mesh} meshDef\n */\nfunction updateMorphTargets(mesh, meshDef) {\n  mesh.updateMorphTargets();\n  if (meshDef.weights !== undefined) {\n    for (let i = 0, il = meshDef.weights.length; i < il; i++) {\n      mesh.morphTargetInfluences[i] = meshDef.weights[i];\n    }\n  }\n\n  // .extras has user-defined data, so check that .extras.targetNames is an array.\n  if (meshDef.extras && Array.isArray(meshDef.extras.targetNames)) {\n    const targetNames = meshDef.extras.targetNames;\n    if (mesh.morphTargetInfluences.length === targetNames.length) {\n      mesh.morphTargetDictionary = {};\n      for (let i = 0, il = targetNames.length; i < il; i++) {\n        mesh.morphTargetDictionary[targetNames[i]] = i;\n      }\n    } else {\n      console.warn('THREE.GLTFLoader: Invalid extras.targetNames length. Ignoring names.');\n    }\n  }\n}\nfunction createPrimitiveKey(primitiveDef) {\n  let geometryKey;\n  const dracoExtension = primitiveDef.extensions && primitiveDef.extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION];\n  if (dracoExtension) {\n    geometryKey = 'draco:' + dracoExtension.bufferView + ':' + dracoExtension.indices + ':' + createAttributesKey(dracoExtension.attributes);\n  } else {\n    geometryKey = primitiveDef.indices + ':' + createAttributesKey(primitiveDef.attributes) + ':' + primitiveDef.mode;\n  }\n  if (primitiveDef.targets !== undefined) {\n    for (let i = 0, il = primitiveDef.targets.length; i < il; i++) {\n      geometryKey += ':' + createAttributesKey(primitiveDef.targets[i]);\n    }\n  }\n  return geometryKey;\n}\nfunction createAttributesKey(attributes) {\n  let attributesKey = '';\n  const keys = Object.keys(attributes).sort();\n  for (let i = 0, il = keys.length; i < il; i++) {\n    attributesKey += keys[i] + ':' + attributes[keys[i]] + ';';\n  }\n  return attributesKey;\n}\nfunction getNormalizedComponentScale(constructor) {\n  // Reference:\n  // https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization#encoding-quantized-data\n\n  switch (constructor) {\n    case Int8Array:\n      return 1 / 127;\n    case Uint8Array:\n      return 1 / 255;\n    case Int16Array:\n      return 1 / 32767;\n    case Uint16Array:\n      return 1 / 65535;\n    default:\n      throw new Error('THREE.GLTFLoader: Unsupported normalized accessor component type.');\n  }\n}\nfunction getImageURIMimeType(uri) {\n  if (uri.search(/\\.jpe?g($|\\?)/i) > 0 || uri.search(/^data\\:image\\/jpeg/) === 0) return 'image/jpeg';\n  if (uri.search(/\\.webp($|\\?)/i) > 0 || uri.search(/^data\\:image\\/webp/) === 0) return 'image/webp';\n  if (uri.search(/\\.ktx2($|\\?)/i) > 0 || uri.search(/^data\\:image\\/ktx2/) === 0) return 'image/ktx2';\n  return 'image/png';\n}\nconst _identityMatrix = new Matrix4();\n\n/* GLTF PARSER */\n\nclass GLTFParser {\n  constructor(json = {}, options = {}) {\n    this.json = json;\n    this.extensions = {};\n    this.plugins = {};\n    this.options = options;\n\n    // loader object cache\n    this.cache = new GLTFRegistry();\n\n    // associations between Three.js objects and glTF elements\n    this.associations = new Map();\n\n    // BufferGeometry caching\n    this.primitiveCache = {};\n\n    // Node cache\n    this.nodeCache = {};\n\n    // Object3D instance caches\n    this.meshCache = {\n      refs: {},\n      uses: {}\n    };\n    this.cameraCache = {\n      refs: {},\n      uses: {}\n    };\n    this.lightCache = {\n      refs: {},\n      uses: {}\n    };\n    this.sourceCache = {};\n    this.textureCache = {};\n\n    // Track node names, to ensure no duplicates\n    this.nodeNamesUsed = {};\n\n    // Use an ImageBitmapLoader if imageBitmaps are supported. Moves much of the\n    // expensive work of uploading a texture to the GPU off the main thread.\n\n    let isSafari = false;\n    let safariVersion = -1;\n    let isFirefox = false;\n    let firefoxVersion = -1;\n    if (typeof navigator !== 'undefined') {\n      const userAgent = navigator.userAgent;\n      isSafari = /^((?!chrome|android).)*safari/i.test(userAgent) === true;\n      const safariMatch = userAgent.match(/Version\\/(\\d+)/);\n      safariVersion = isSafari && safariMatch ? parseInt(safariMatch[1], 10) : -1;\n      isFirefox = userAgent.indexOf('Firefox') > -1;\n      firefoxVersion = isFirefox ? userAgent.match(/Firefox\\/([0-9]+)\\./)[1] : -1;\n    }\n    if (typeof createImageBitmap === 'undefined' || isSafari && safariVersion < 17 || isFirefox && firefoxVersion < 98) {\n      this.textureLoader = new TextureLoader(this.options.manager);\n    } else {\n      this.textureLoader = new ImageBitmapLoader(this.options.manager);\n    }\n    this.textureLoader.setCrossOrigin(this.options.crossOrigin);\n    this.textureLoader.setRequestHeader(this.options.requestHeader);\n    this.fileLoader = new FileLoader(this.options.manager);\n    this.fileLoader.setResponseType('arraybuffer');\n    if (this.options.crossOrigin === 'use-credentials') {\n      this.fileLoader.setWithCredentials(true);\n    }\n  }\n  setExtensions(extensions) {\n    this.extensions = extensions;\n  }\n  setPlugins(plugins) {\n    this.plugins = plugins;\n  }\n  parse(onLoad, onError) {\n    const parser = this;\n    const json = this.json;\n    const extensions = this.extensions;\n\n    // Clear the loader cache\n    this.cache.removeAll();\n    this.nodeCache = {};\n\n    // Mark the special nodes/meshes in json for efficient parse\n    this._invokeAll(function (ext) {\n      return ext._markDefs && ext._markDefs();\n    });\n    Promise.all(this._invokeAll(function (ext) {\n      return ext.beforeRoot && ext.beforeRoot();\n    })).then(function () {\n      return Promise.all([parser.getDependencies('scene'), parser.getDependencies('animation'), parser.getDependencies('camera')]);\n    }).then(function (dependencies) {\n      const result = {\n        scene: dependencies[0][json.scene || 0],\n        scenes: dependencies[0],\n        animations: dependencies[1],\n        cameras: dependencies[2],\n        asset: json.asset,\n        parser: parser,\n        userData: {}\n      };\n      addUnknownExtensionsToUserData(extensions, result, json);\n      assignExtrasToUserData(result, json);\n      return Promise.all(parser._invokeAll(function (ext) {\n        return ext.afterRoot && ext.afterRoot(result);\n      })).then(function () {\n        for (const scene of result.scenes) {\n          scene.updateMatrixWorld();\n        }\n        onLoad(result);\n      });\n    }).catch(onError);\n  }\n\n  /**\n   * Marks the special nodes/meshes in json for efficient parse.\n   *\n   * @private\n   */\n  _markDefs() {\n    const nodeDefs = this.json.nodes || [];\n    const skinDefs = this.json.skins || [];\n    const meshDefs = this.json.meshes || [];\n\n    // Nothing in the node definition indicates whether it is a Bone or an\n    // Object3D. Use the skins' joint references to mark bones.\n    for (let skinIndex = 0, skinLength = skinDefs.length; skinIndex < skinLength; skinIndex++) {\n      const joints = skinDefs[skinIndex].joints;\n      for (let i = 0, il = joints.length; i < il; i++) {\n        nodeDefs[joints[i]].isBone = true;\n      }\n    }\n\n    // Iterate over all nodes, marking references to shared resources,\n    // as well as skeleton joints.\n    for (let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex++) {\n      const nodeDef = nodeDefs[nodeIndex];\n      if (nodeDef.mesh !== undefined) {\n        this._addNodeRef(this.meshCache, nodeDef.mesh);\n\n        // Nothing in the mesh definition indicates whether it is\n        // a SkinnedMesh or Mesh. Use the node's mesh reference\n        // to mark SkinnedMesh if node has skin.\n        if (nodeDef.skin !== undefined) {\n          meshDefs[nodeDef.mesh].isSkinnedMesh = true;\n        }\n      }\n      if (nodeDef.camera !== undefined) {\n        this._addNodeRef(this.cameraCache, nodeDef.camera);\n      }\n    }\n  }\n\n  /**\n   * Counts references to shared node / Object3D resources. These resources\n   * can be reused, or \"instantiated\", at multiple nodes in the scene\n   * hierarchy. Mesh, Camera, and Light instances are instantiated and must\n   * be marked. Non-scenegraph resources (like Materials, Geometries, and\n   * Textures) can be reused directly and are not marked here.\n   *\n   * Example: CesiumMilkTruck sample model reuses \"Wheel\" meshes.\n   *\n   * @private\n   * @param {Object} cache\n   * @param {Object3D} index\n   */\n  _addNodeRef(cache, index) {\n    if (index === undefined) return;\n    if (cache.refs[index] === undefined) {\n      cache.refs[index] = cache.uses[index] = 0;\n    }\n    cache.refs[index]++;\n  }\n\n  /**\n   * Returns a reference to a shared resource, cloning it if necessary.\n   *\n   * @private\n   * @param {Object} cache\n   * @param {number} index\n   * @param {Object} object\n   * @return {Object}\n   */\n  _getNodeRef(cache, index, object) {\n    if (cache.refs[index] <= 1) return object;\n    const ref = object.clone();\n\n    // Propagates mappings to the cloned object, prevents mappings on the\n    // original object from being lost.\n    const updateMappings = (original, clone) => {\n      const mappings = this.associations.get(original);\n      if (mappings != null) {\n        this.associations.set(clone, mappings);\n      }\n      for (const [i, child] of original.children.entries()) {\n        updateMappings(child, clone.children[i]);\n      }\n    };\n    updateMappings(object, ref);\n    ref.name += '_instance_' + cache.uses[index]++;\n    return ref;\n  }\n  _invokeOne(func) {\n    const extensions = Object.values(this.plugins);\n    extensions.push(this);\n    for (let i = 0; i < extensions.length; i++) {\n      const result = func(extensions[i]);\n      if (result) return result;\n    }\n    return null;\n  }\n  _invokeAll(func) {\n    const extensions = Object.values(this.plugins);\n    extensions.unshift(this);\n    const pending = [];\n    for (let i = 0; i < extensions.length; i++) {\n      const result = func(extensions[i]);\n      if (result) pending.push(result);\n    }\n    return pending;\n  }\n\n  /**\n   * Requests the specified dependency asynchronously, with caching.\n   *\n   * @private\n   * @param {string} type\n   * @param {number} index\n   * @return {Promise<Object3D|Material|THREE.Texture|AnimationClip|ArrayBuffer|Object>}\n   */\n  getDependency(type, index) {\n    const cacheKey = type + ':' + index;\n    let dependency = this.cache.get(cacheKey);\n    if (!dependency) {\n      switch (type) {\n        case 'scene':\n          dependency = this.loadScene(index);\n          break;\n        case 'node':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadNode && ext.loadNode(index);\n          });\n          break;\n        case 'mesh':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadMesh && ext.loadMesh(index);\n          });\n          break;\n        case 'accessor':\n          dependency = this.loadAccessor(index);\n          break;\n        case 'bufferView':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadBufferView && ext.loadBufferView(index);\n          });\n          break;\n        case 'buffer':\n          dependency = this.loadBuffer(index);\n          break;\n        case 'material':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadMaterial && ext.loadMaterial(index);\n          });\n          break;\n        case 'texture':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadTexture && ext.loadTexture(index);\n          });\n          break;\n        case 'skin':\n          dependency = this.loadSkin(index);\n          break;\n        case 'animation':\n          dependency = this._invokeOne(function (ext) {\n            return ext.loadAnimation && ext.loadAnimation(index);\n          });\n          break;\n        case 'camera':\n          dependency = this.loadCamera(index);\n          break;\n        default:\n          dependency = this._invokeOne(function (ext) {\n            return ext != this && ext.getDependency && ext.getDependency(type, index);\n          });\n          if (!dependency) {\n            throw new Error('Unknown type: ' + type);\n          }\n          break;\n      }\n      this.cache.add(cacheKey, dependency);\n    }\n    return dependency;\n  }\n\n  /**\n   * Requests all dependencies of the specified type asynchronously, with caching.\n   *\n   * @private\n   * @param {string} type\n   * @return {Promise<Array<Object>>}\n   */\n  getDependencies(type) {\n    let dependencies = this.cache.get(type);\n    if (!dependencies) {\n      const parser = this;\n      const defs = this.json[type + (type === 'mesh' ? 'es' : 's')] || [];\n      dependencies = Promise.all(defs.map(function (def, index) {\n        return parser.getDependency(type, index);\n      }));\n      this.cache.add(type, dependencies);\n    }\n    return dependencies;\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n   *\n   * @private\n   * @param {number} bufferIndex\n   * @return {Promise<ArrayBuffer>}\n   */\n  loadBuffer(bufferIndex) {\n    const bufferDef = this.json.buffers[bufferIndex];\n    const loader = this.fileLoader;\n    if (bufferDef.type && bufferDef.type !== 'arraybuffer') {\n      throw new Error('THREE.GLTFLoader: ' + bufferDef.type + ' buffer type is not supported.');\n    }\n\n    // If present, GLB container is required to be the first buffer.\n    if (bufferDef.uri === undefined && bufferIndex === 0) {\n      return Promise.resolve(this.extensions[EXTENSIONS.KHR_BINARY_GLTF].body);\n    }\n    const options = this.options;\n    return new Promise(function (resolve, reject) {\n      loader.load(LoaderUtils.resolveURL(bufferDef.uri, options.path), resolve, undefined, function () {\n        reject(new Error('THREE.GLTFLoader: Failed to load buffer \"' + bufferDef.uri + '\".'));\n      });\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n   *\n   * @private\n   * @param {number} bufferViewIndex\n   * @return {Promise<ArrayBuffer>}\n   */\n  loadBufferView(bufferViewIndex) {\n    const bufferViewDef = this.json.bufferViews[bufferViewIndex];\n    return this.getDependency('buffer', bufferViewDef.buffer).then(function (buffer) {\n      const byteLength = bufferViewDef.byteLength || 0;\n      const byteOffset = bufferViewDef.byteOffset || 0;\n      return buffer.slice(byteOffset, byteOffset + byteLength);\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#accessors\n   *\n   * @private\n   * @param {number} accessorIndex\n   * @return {Promise<BufferAttribute|InterleavedBufferAttribute>}\n   */\n  loadAccessor(accessorIndex) {\n    const parser = this;\n    const json = this.json;\n    const accessorDef = this.json.accessors[accessorIndex];\n    if (accessorDef.bufferView === undefined && accessorDef.sparse === undefined) {\n      const itemSize = WEBGL_TYPE_SIZES[accessorDef.type];\n      const TypedArray = WEBGL_COMPONENT_TYPES[accessorDef.componentType];\n      const normalized = accessorDef.normalized === true;\n      const array = new TypedArray(accessorDef.count * itemSize);\n      return Promise.resolve(new BufferAttribute(array, itemSize, normalized));\n    }\n    const pendingBufferViews = [];\n    if (accessorDef.bufferView !== undefined) {\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.bufferView));\n    } else {\n      pendingBufferViews.push(null);\n    }\n    if (accessorDef.sparse !== undefined) {\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.sparse.indices.bufferView));\n      pendingBufferViews.push(this.getDependency('bufferView', accessorDef.sparse.values.bufferView));\n    }\n    return Promise.all(pendingBufferViews).then(function (bufferViews) {\n      const bufferView = bufferViews[0];\n      const itemSize = WEBGL_TYPE_SIZES[accessorDef.type];\n      const TypedArray = WEBGL_COMPONENT_TYPES[accessorDef.componentType];\n\n      // For VEC3: itemSize is 3, elementBytes is 4, itemBytes is 12.\n      const elementBytes = TypedArray.BYTES_PER_ELEMENT;\n      const itemBytes = elementBytes * itemSize;\n      const byteOffset = accessorDef.byteOffset || 0;\n      const byteStride = accessorDef.bufferView !== undefined ? json.bufferViews[accessorDef.bufferView].byteStride : undefined;\n      const normalized = accessorDef.normalized === true;\n      let array, bufferAttribute;\n\n      // The buffer is not interleaved if the stride is the item size in bytes.\n      if (byteStride && byteStride !== itemBytes) {\n        // Each \"slice\" of the buffer, as defined by 'count' elements of 'byteStride' bytes, gets its own InterleavedBuffer\n        // This makes sure that IBA.count reflects accessor.count properly\n        const ibSlice = Math.floor(byteOffset / byteStride);\n        const ibCacheKey = 'InterleavedBuffer:' + accessorDef.bufferView + ':' + accessorDef.componentType + ':' + ibSlice + ':' + accessorDef.count;\n        let ib = parser.cache.get(ibCacheKey);\n        if (!ib) {\n          array = new TypedArray(bufferView, ibSlice * byteStride, accessorDef.count * byteStride / elementBytes);\n\n          // Integer parameters to IB/IBA are in array elements, not bytes.\n          ib = new InterleavedBuffer(array, byteStride / elementBytes);\n          parser.cache.add(ibCacheKey, ib);\n        }\n        bufferAttribute = new InterleavedBufferAttribute(ib, itemSize, byteOffset % byteStride / elementBytes, normalized);\n      } else {\n        if (bufferView === null) {\n          array = new TypedArray(accessorDef.count * itemSize);\n        } else {\n          array = new TypedArray(bufferView, byteOffset, accessorDef.count * itemSize);\n        }\n        bufferAttribute = new BufferAttribute(array, itemSize, normalized);\n      }\n\n      // https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#sparse-accessors\n      if (accessorDef.sparse !== undefined) {\n        const itemSizeIndices = WEBGL_TYPE_SIZES.SCALAR;\n        const TypedArrayIndices = WEBGL_COMPONENT_TYPES[accessorDef.sparse.indices.componentType];\n        const byteOffsetIndices = accessorDef.sparse.indices.byteOffset || 0;\n        const byteOffsetValues = accessorDef.sparse.values.byteOffset || 0;\n        const sparseIndices = new TypedArrayIndices(bufferViews[1], byteOffsetIndices, accessorDef.sparse.count * itemSizeIndices);\n        const sparseValues = new TypedArray(bufferViews[2], byteOffsetValues, accessorDef.sparse.count * itemSize);\n        if (bufferView !== null) {\n          // Avoid modifying the original ArrayBuffer, if the bufferView wasn't initialized with zeroes.\n          bufferAttribute = new BufferAttribute(bufferAttribute.array.slice(), bufferAttribute.itemSize, bufferAttribute.normalized);\n        }\n\n        // Ignore normalized since we copy from sparse\n        bufferAttribute.normalized = false;\n        for (let i = 0, il = sparseIndices.length; i < il; i++) {\n          const index = sparseIndices[i];\n          bufferAttribute.setX(index, sparseValues[i * itemSize]);\n          if (itemSize >= 2) bufferAttribute.setY(index, sparseValues[i * itemSize + 1]);\n          if (itemSize >= 3) bufferAttribute.setZ(index, sparseValues[i * itemSize + 2]);\n          if (itemSize >= 4) bufferAttribute.setW(index, sparseValues[i * itemSize + 3]);\n          if (itemSize >= 5) throw new Error('THREE.GLTFLoader: Unsupported itemSize in sparse BufferAttribute.');\n        }\n        bufferAttribute.normalized = normalized;\n      }\n      return bufferAttribute;\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#textures\n   *\n   * @private\n   * @param {number} textureIndex\n   * @return {Promise<THREE.Texture|null>}\n   */\n  loadTexture(textureIndex) {\n    const json = this.json;\n    const options = this.options;\n    const textureDef = json.textures[textureIndex];\n    const sourceIndex = textureDef.source;\n    const sourceDef = json.images[sourceIndex];\n    let loader = this.textureLoader;\n    if (sourceDef.uri) {\n      const handler = options.manager.getHandler(sourceDef.uri);\n      if (handler !== null) loader = handler;\n    }\n    return this.loadTextureImage(textureIndex, sourceIndex, loader);\n  }\n  loadTextureImage(textureIndex, sourceIndex, loader) {\n    const parser = this;\n    const json = this.json;\n    const textureDef = json.textures[textureIndex];\n    const sourceDef = json.images[sourceIndex];\n    const cacheKey = (sourceDef.uri || sourceDef.bufferView) + ':' + textureDef.sampler;\n    if (this.textureCache[cacheKey]) {\n      // See https://github.com/mrdoob/three.js/issues/21559.\n      return this.textureCache[cacheKey];\n    }\n    const promise = this.loadImageSource(sourceIndex, loader).then(function (texture) {\n      texture.flipY = false;\n      texture.name = textureDef.name || sourceDef.name || '';\n      if (texture.name === '' && typeof sourceDef.uri === 'string' && sourceDef.uri.startsWith('data:image/') === false) {\n        texture.name = sourceDef.uri;\n      }\n      const samplers = json.samplers || {};\n      const sampler = samplers[textureDef.sampler] || {};\n      texture.magFilter = WEBGL_FILTERS[sampler.magFilter] || LinearFilter;\n      texture.minFilter = WEBGL_FILTERS[sampler.minFilter] || LinearMipmapLinearFilter;\n      texture.wrapS = WEBGL_WRAPPINGS[sampler.wrapS] || RepeatWrapping;\n      texture.wrapT = WEBGL_WRAPPINGS[sampler.wrapT] || RepeatWrapping;\n      texture.generateMipmaps = !texture.isCompressedTexture && texture.minFilter !== NearestFilter && texture.minFilter !== LinearFilter;\n      parser.associations.set(texture, {\n        textures: textureIndex\n      });\n      return texture;\n    }).catch(function () {\n      return null;\n    });\n    this.textureCache[cacheKey] = promise;\n    return promise;\n  }\n  loadImageSource(sourceIndex, loader) {\n    const parser = this;\n    const json = this.json;\n    const options = this.options;\n    if (this.sourceCache[sourceIndex] !== undefined) {\n      return this.sourceCache[sourceIndex].then(texture => texture.clone());\n    }\n    const sourceDef = json.images[sourceIndex];\n    const URL = self.URL || self.webkitURL;\n    let sourceURI = sourceDef.uri || '';\n    let isObjectURL = false;\n    if (sourceDef.bufferView !== undefined) {\n      // Load binary image data from bufferView, if provided.\n\n      sourceURI = parser.getDependency('bufferView', sourceDef.bufferView).then(function (bufferView) {\n        isObjectURL = true;\n        const blob = new Blob([bufferView], {\n          type: sourceDef.mimeType\n        });\n        sourceURI = URL.createObjectURL(blob);\n        return sourceURI;\n      });\n    } else if (sourceDef.uri === undefined) {\n      throw new Error('THREE.GLTFLoader: Image ' + sourceIndex + ' is missing URI and bufferView');\n    }\n    const promise = Promise.resolve(sourceURI).then(function (sourceURI) {\n      return new Promise(function (resolve, reject) {\n        let onLoad = resolve;\n        if (loader.isImageBitmapLoader === true) {\n          onLoad = function (imageBitmap) {\n            const texture = new Texture(imageBitmap);\n            texture.needsUpdate = true;\n            resolve(texture);\n          };\n        }\n        loader.load(LoaderUtils.resolveURL(sourceURI, options.path), onLoad, undefined, reject);\n      });\n    }).then(function (texture) {\n      // Clean up resources and configure Texture.\n\n      if (isObjectURL === true) {\n        URL.revokeObjectURL(sourceURI);\n      }\n      assignExtrasToUserData(texture, sourceDef);\n      texture.userData.mimeType = sourceDef.mimeType || getImageURIMimeType(sourceDef.uri);\n      return texture;\n    }).catch(function (error) {\n      console.error('THREE.GLTFLoader: Couldn\\'t load texture', sourceURI);\n      throw error;\n    });\n    this.sourceCache[sourceIndex] = promise;\n    return promise;\n  }\n\n  /**\n   * Asynchronously assigns a texture to the given material parameters.\n   *\n   * @private\n   * @param {Object} materialParams\n   * @param {string} mapName\n   * @param {Object} mapDef\n   * @param {string} [colorSpace]\n   * @return {Promise<Texture>}\n   */\n  assignTexture(materialParams, mapName, mapDef, colorSpace) {\n    const parser = this;\n    return this.getDependency('texture', mapDef.index).then(function (texture) {\n      if (!texture) return null;\n      if (mapDef.texCoord !== undefined && mapDef.texCoord > 0) {\n        texture = texture.clone();\n        texture.channel = mapDef.texCoord;\n      }\n      if (parser.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM]) {\n        const transform = mapDef.extensions !== undefined ? mapDef.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM] : undefined;\n        if (transform) {\n          const gltfReference = parser.associations.get(texture);\n          texture = parser.extensions[EXTENSIONS.KHR_TEXTURE_TRANSFORM].extendTexture(texture, transform);\n          parser.associations.set(texture, gltfReference);\n        }\n      }\n      if (colorSpace !== undefined) {\n        texture.colorSpace = colorSpace;\n      }\n      materialParams[mapName] = texture;\n      return texture;\n    });\n  }\n\n  /**\n   * Assigns final material to a Mesh, Line, or Points instance. The instance\n   * already has a material (generated from the glTF material options alone)\n   * but reuse of the same glTF material may require multiple threejs materials\n   * to accommodate different primitive types, defines, etc. New materials will\n   * be created if necessary, and reused from a cache.\n   *\n   * @private\n   * @param {Object3D} mesh Mesh, Line, or Points instance.\n   */\n  assignFinalMaterial(mesh) {\n    const geometry = mesh.geometry;\n    let material = mesh.material;\n    const useDerivativeTangents = geometry.attributes.tangent === undefined;\n    const useVertexColors = geometry.attributes.color !== undefined;\n    const useFlatShading = geometry.attributes.normal === undefined;\n    if (mesh.isPoints) {\n      const cacheKey = 'PointsMaterial:' + material.uuid;\n      let pointsMaterial = this.cache.get(cacheKey);\n      if (!pointsMaterial) {\n        pointsMaterial = new PointsMaterial();\n        Material.prototype.copy.call(pointsMaterial, material);\n        pointsMaterial.color.copy(material.color);\n        pointsMaterial.map = material.map;\n        pointsMaterial.sizeAttenuation = false; // glTF spec says points should be 1px\n\n        this.cache.add(cacheKey, pointsMaterial);\n      }\n      material = pointsMaterial;\n    } else if (mesh.isLine) {\n      const cacheKey = 'LineBasicMaterial:' + material.uuid;\n      let lineMaterial = this.cache.get(cacheKey);\n      if (!lineMaterial) {\n        lineMaterial = new LineBasicMaterial();\n        Material.prototype.copy.call(lineMaterial, material);\n        lineMaterial.color.copy(material.color);\n        lineMaterial.map = material.map;\n        this.cache.add(cacheKey, lineMaterial);\n      }\n      material = lineMaterial;\n    }\n\n    // Clone the material if it will be modified\n    if (useDerivativeTangents || useVertexColors || useFlatShading) {\n      let cacheKey = 'ClonedMaterial:' + material.uuid + ':';\n      if (useDerivativeTangents) cacheKey += 'derivative-tangents:';\n      if (useVertexColors) cacheKey += 'vertex-colors:';\n      if (useFlatShading) cacheKey += 'flat-shading:';\n      let cachedMaterial = this.cache.get(cacheKey);\n      if (!cachedMaterial) {\n        cachedMaterial = material.clone();\n        if (useVertexColors) cachedMaterial.vertexColors = true;\n        if (useFlatShading) cachedMaterial.flatShading = true;\n        if (useDerivativeTangents) {\n          // https://github.com/mrdoob/three.js/issues/11438#issuecomment-507003995\n          if (cachedMaterial.normalScale) cachedMaterial.normalScale.y *= -1;\n          if (cachedMaterial.clearcoatNormalScale) cachedMaterial.clearcoatNormalScale.y *= -1;\n        }\n        this.cache.add(cacheKey, cachedMaterial);\n        this.associations.set(cachedMaterial, this.associations.get(material));\n      }\n      material = cachedMaterial;\n    }\n    mesh.material = material;\n  }\n  getMaterialType(/* materialIndex */\n  ) {\n    return MeshStandardMaterial;\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#materials\n   *\n   * @private\n   * @param {number} materialIndex\n   * @return {Promise<Material>}\n   */\n  loadMaterial(materialIndex) {\n    const parser = this;\n    const json = this.json;\n    const extensions = this.extensions;\n    const materialDef = json.materials[materialIndex];\n    let materialType;\n    const materialParams = {};\n    const materialExtensions = materialDef.extensions || {};\n    const pending = [];\n    if (materialExtensions[EXTENSIONS.KHR_MATERIALS_UNLIT]) {\n      const kmuExtension = extensions[EXTENSIONS.KHR_MATERIALS_UNLIT];\n      materialType = kmuExtension.getMaterialType();\n      pending.push(kmuExtension.extendParams(materialParams, materialDef, parser));\n    } else {\n      // Specification:\n      // https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#metallic-roughness-material\n\n      const metallicRoughness = materialDef.pbrMetallicRoughness || {};\n      materialParams.color = new Color(1.0, 1.0, 1.0);\n      materialParams.opacity = 1.0;\n      if (Array.isArray(metallicRoughness.baseColorFactor)) {\n        const array = metallicRoughness.baseColorFactor;\n        materialParams.color.setRGB(array[0], array[1], array[2], LinearSRGBColorSpace);\n        materialParams.opacity = array[3];\n      }\n      if (metallicRoughness.baseColorTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace));\n      }\n      materialParams.metalness = metallicRoughness.metallicFactor !== undefined ? metallicRoughness.metallicFactor : 1.0;\n      materialParams.roughness = metallicRoughness.roughnessFactor !== undefined ? metallicRoughness.roughnessFactor : 1.0;\n      if (metallicRoughness.metallicRoughnessTexture !== undefined) {\n        pending.push(parser.assignTexture(materialParams, 'metalnessMap', metallicRoughness.metallicRoughnessTexture));\n        pending.push(parser.assignTexture(materialParams, 'roughnessMap', metallicRoughness.metallicRoughnessTexture));\n      }\n      materialType = this._invokeOne(function (ext) {\n        return ext.getMaterialType && ext.getMaterialType(materialIndex);\n      });\n      pending.push(Promise.all(this._invokeAll(function (ext) {\n        return ext.extendMaterialParams && ext.extendMaterialParams(materialIndex, materialParams);\n      })));\n    }\n    if (materialDef.doubleSided === true) {\n      materialParams.side = DoubleSide;\n    }\n    const alphaMode = materialDef.alphaMode || ALPHA_MODES.OPAQUE;\n    if (alphaMode === ALPHA_MODES.BLEND) {\n      materialParams.transparent = true;\n\n      // See: https://github.com/mrdoob/three.js/issues/17706\n      materialParams.depthWrite = false;\n    } else {\n      materialParams.transparent = false;\n      if (alphaMode === ALPHA_MODES.MASK) {\n        materialParams.alphaTest = materialDef.alphaCutoff !== undefined ? materialDef.alphaCutoff : 0.5;\n      }\n    }\n    if (materialDef.normalTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'normalMap', materialDef.normalTexture));\n      materialParams.normalScale = new Vector2(1, 1);\n      if (materialDef.normalTexture.scale !== undefined) {\n        const scale = materialDef.normalTexture.scale;\n        materialParams.normalScale.set(scale, scale);\n      }\n    }\n    if (materialDef.occlusionTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'aoMap', materialDef.occlusionTexture));\n      if (materialDef.occlusionTexture.strength !== undefined) {\n        materialParams.aoMapIntensity = materialDef.occlusionTexture.strength;\n      }\n    }\n    if (materialDef.emissiveFactor !== undefined && materialType !== MeshBasicMaterial) {\n      const emissiveFactor = materialDef.emissiveFactor;\n      materialParams.emissive = new Color().setRGB(emissiveFactor[0], emissiveFactor[1], emissiveFactor[2], LinearSRGBColorSpace);\n    }\n    if (materialDef.emissiveTexture !== undefined && materialType !== MeshBasicMaterial) {\n      pending.push(parser.assignTexture(materialParams, 'emissiveMap', materialDef.emissiveTexture, SRGBColorSpace));\n    }\n    return Promise.all(pending).then(function () {\n      const material = new materialType(materialParams);\n      if (materialDef.name) material.name = materialDef.name;\n      assignExtrasToUserData(material, materialDef);\n      parser.associations.set(material, {\n        materials: materialIndex\n      });\n      if (materialDef.extensions) addUnknownExtensionsToUserData(extensions, material, materialDef);\n      return material;\n    });\n  }\n\n  /**\n   * When Object3D instances are targeted by animation, they need unique names.\n   *\n   * @private\n   * @param {string} originalName\n   * @return {string}\n   */\n  createUniqueName(originalName) {\n    const sanitizedName = PropertyBinding.sanitizeNodeName(originalName || '');\n    if (sanitizedName in this.nodeNamesUsed) {\n      return sanitizedName + '_' + ++this.nodeNamesUsed[sanitizedName];\n    } else {\n      this.nodeNamesUsed[sanitizedName] = 0;\n      return sanitizedName;\n    }\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#geometry\n   *\n   * Creates BufferGeometries from primitives.\n   *\n   * @private\n   * @param {Array<GLTF.Primitive>} primitives\n   * @return {Promise<Array<BufferGeometry>>}\n   */\n  loadGeometries(primitives) {\n    const parser = this;\n    const extensions = this.extensions;\n    const cache = this.primitiveCache;\n    function createDracoPrimitive(primitive) {\n      return extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION].decodePrimitive(primitive, parser).then(function (geometry) {\n        return addPrimitiveAttributes(geometry, primitive, parser);\n      });\n    }\n    const pending = [];\n    for (let i = 0, il = primitives.length; i < il; i++) {\n      const primitive = primitives[i];\n      const cacheKey = createPrimitiveKey(primitive);\n\n      // See if we've already created this geometry\n      const cached = cache[cacheKey];\n      if (cached) {\n        // Use the cached geometry if it exists\n        pending.push(cached.promise);\n      } else {\n        let geometryPromise;\n        if (primitive.extensions && primitive.extensions[EXTENSIONS.KHR_DRACO_MESH_COMPRESSION]) {\n          // Use DRACO geometry if available\n          geometryPromise = createDracoPrimitive(primitive);\n        } else {\n          // Otherwise create a new geometry\n          geometryPromise = addPrimitiveAttributes(new BufferGeometry(), primitive, parser);\n        }\n\n        // Cache this geometry\n        cache[cacheKey] = {\n          primitive: primitive,\n          promise: geometryPromise\n        };\n        pending.push(geometryPromise);\n      }\n    }\n    return Promise.all(pending);\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#meshes\n   *\n   * @private\n   * @param {number} meshIndex\n   * @return {Promise<Group|Mesh|SkinnedMesh|Line|Points>}\n   */\n  loadMesh(meshIndex) {\n    const parser = this;\n    const json = this.json;\n    const extensions = this.extensions;\n    const meshDef = json.meshes[meshIndex];\n    const primitives = meshDef.primitives;\n    const pending = [];\n    for (let i = 0, il = primitives.length; i < il; i++) {\n      const material = primitives[i].material === undefined ? createDefaultMaterial(this.cache) : this.getDependency('material', primitives[i].material);\n      pending.push(material);\n    }\n    pending.push(parser.loadGeometries(primitives));\n    return Promise.all(pending).then(function (results) {\n      const materials = results.slice(0, results.length - 1);\n      const geometries = results[results.length - 1];\n      const meshes = [];\n      for (let i = 0, il = geometries.length; i < il; i++) {\n        const geometry = geometries[i];\n        const primitive = primitives[i];\n\n        // 1. create Mesh\n\n        let mesh;\n        const material = materials[i];\n        if (primitive.mode === WEBGL_CONSTANTS.TRIANGLES || primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP || primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN || primitive.mode === undefined) {\n          // .isSkinnedMesh isn't in glTF spec. See ._markDefs()\n          mesh = meshDef.isSkinnedMesh === true ? new SkinnedMesh(geometry, material) : new Mesh(geometry, material);\n          if (mesh.isSkinnedMesh === true) {\n            // normalize skin weights to fix malformed assets (see #15319)\n            mesh.normalizeSkinWeights();\n          }\n          if (primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP) {\n            mesh.geometry = toTrianglesDrawMode(mesh.geometry, TriangleStripDrawMode);\n          } else if (primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN) {\n            mesh.geometry = toTrianglesDrawMode(mesh.geometry, TriangleFanDrawMode);\n          }\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINES) {\n          mesh = new LineSegments(geometry, material);\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINE_STRIP) {\n          mesh = new Line(geometry, material);\n        } else if (primitive.mode === WEBGL_CONSTANTS.LINE_LOOP) {\n          mesh = new LineLoop(geometry, material);\n        } else if (primitive.mode === WEBGL_CONSTANTS.POINTS) {\n          mesh = new Points(geometry, material);\n        } else {\n          throw new Error('THREE.GLTFLoader: Primitive mode unsupported: ' + primitive.mode);\n        }\n        if (Object.keys(mesh.geometry.morphAttributes).length > 0) {\n          updateMorphTargets(mesh, meshDef);\n        }\n        mesh.name = parser.createUniqueName(meshDef.name || 'mesh_' + meshIndex);\n        assignExtrasToUserData(mesh, meshDef);\n        if (primitive.extensions) addUnknownExtensionsToUserData(extensions, mesh, primitive);\n        parser.assignFinalMaterial(mesh);\n        meshes.push(mesh);\n      }\n      for (let i = 0, il = meshes.length; i < il; i++) {\n        parser.associations.set(meshes[i], {\n          meshes: meshIndex,\n          primitives: i\n        });\n      }\n      if (meshes.length === 1) {\n        if (meshDef.extensions) addUnknownExtensionsToUserData(extensions, meshes[0], meshDef);\n        return meshes[0];\n      }\n      const group = new Group();\n      if (meshDef.extensions) addUnknownExtensionsToUserData(extensions, group, meshDef);\n      parser.associations.set(group, {\n        meshes: meshIndex\n      });\n      for (let i = 0, il = meshes.length; i < il; i++) {\n        group.add(meshes[i]);\n      }\n      return group;\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#cameras\n   *\n   * @private\n   * @param {number} cameraIndex\n   * @return {Promise<THREE.Camera>}\n   */\n  loadCamera(cameraIndex) {\n    let camera;\n    const cameraDef = this.json.cameras[cameraIndex];\n    const params = cameraDef[cameraDef.type];\n    if (!params) {\n      console.warn('THREE.GLTFLoader: Missing camera parameters.');\n      return;\n    }\n    if (cameraDef.type === 'perspective') {\n      camera = new PerspectiveCamera(MathUtils.radToDeg(params.yfov), params.aspectRatio || 1, params.znear || 1, params.zfar || 2e6);\n    } else if (cameraDef.type === 'orthographic') {\n      camera = new OrthographicCamera(-params.xmag, params.xmag, params.ymag, -params.ymag, params.znear, params.zfar);\n    }\n    if (cameraDef.name) camera.name = this.createUniqueName(cameraDef.name);\n    assignExtrasToUserData(camera, cameraDef);\n    return Promise.resolve(camera);\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#skins\n   *\n   * @private\n   * @param {number} skinIndex\n   * @return {Promise<Skeleton>}\n   */\n  loadSkin(skinIndex) {\n    const skinDef = this.json.skins[skinIndex];\n    const pending = [];\n    for (let i = 0, il = skinDef.joints.length; i < il; i++) {\n      pending.push(this._loadNodeShallow(skinDef.joints[i]));\n    }\n    if (skinDef.inverseBindMatrices !== undefined) {\n      pending.push(this.getDependency('accessor', skinDef.inverseBindMatrices));\n    } else {\n      pending.push(null);\n    }\n    return Promise.all(pending).then(function (results) {\n      const inverseBindMatrices = results.pop();\n      const jointNodes = results;\n\n      // Note that bones (joint nodes) may or may not be in the\n      // scene graph at this time.\n\n      const bones = [];\n      const boneInverses = [];\n      for (let i = 0, il = jointNodes.length; i < il; i++) {\n        const jointNode = jointNodes[i];\n        if (jointNode) {\n          bones.push(jointNode);\n          const mat = new Matrix4();\n          if (inverseBindMatrices !== null) {\n            mat.fromArray(inverseBindMatrices.array, i * 16);\n          }\n          boneInverses.push(mat);\n        } else {\n          console.warn('THREE.GLTFLoader: Joint \"%s\" could not be found.', skinDef.joints[i]);\n        }\n      }\n      return new Skeleton(bones, boneInverses);\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#animations\n   *\n   * @private\n   * @param {number} animationIndex\n   * @return {Promise<AnimationClip>}\n   */\n  loadAnimation(animationIndex) {\n    const json = this.json;\n    const parser = this;\n    const animationDef = json.animations[animationIndex];\n    const animationName = animationDef.name ? animationDef.name : 'animation_' + animationIndex;\n    const pendingNodes = [];\n    const pendingInputAccessors = [];\n    const pendingOutputAccessors = [];\n    const pendingSamplers = [];\n    const pendingTargets = [];\n    for (let i = 0, il = animationDef.channels.length; i < il; i++) {\n      const channel = animationDef.channels[i];\n      const sampler = animationDef.samplers[channel.sampler];\n      const target = channel.target;\n      const name = target.node;\n      const input = animationDef.parameters !== undefined ? animationDef.parameters[sampler.input] : sampler.input;\n      const output = animationDef.parameters !== undefined ? animationDef.parameters[sampler.output] : sampler.output;\n      if (target.node === undefined) continue;\n      pendingNodes.push(this.getDependency('node', name));\n      pendingInputAccessors.push(this.getDependency('accessor', input));\n      pendingOutputAccessors.push(this.getDependency('accessor', output));\n      pendingSamplers.push(sampler);\n      pendingTargets.push(target);\n    }\n    return Promise.all([Promise.all(pendingNodes), Promise.all(pendingInputAccessors), Promise.all(pendingOutputAccessors), Promise.all(pendingSamplers), Promise.all(pendingTargets)]).then(function (dependencies) {\n      const nodes = dependencies[0];\n      const inputAccessors = dependencies[1];\n      const outputAccessors = dependencies[2];\n      const samplers = dependencies[3];\n      const targets = dependencies[4];\n      const tracks = [];\n      for (let i = 0, il = nodes.length; i < il; i++) {\n        const node = nodes[i];\n        const inputAccessor = inputAccessors[i];\n        const outputAccessor = outputAccessors[i];\n        const sampler = samplers[i];\n        const target = targets[i];\n        if (node === undefined) continue;\n        if (node.updateMatrix) {\n          node.updateMatrix();\n        }\n        const createdTracks = parser._createAnimationTracks(node, inputAccessor, outputAccessor, sampler, target);\n        if (createdTracks) {\n          for (let k = 0; k < createdTracks.length; k++) {\n            tracks.push(createdTracks[k]);\n          }\n        }\n      }\n      return new AnimationClip(animationName, undefined, tracks);\n    });\n  }\n  createNodeMesh(nodeIndex) {\n    const json = this.json;\n    const parser = this;\n    const nodeDef = json.nodes[nodeIndex];\n    if (nodeDef.mesh === undefined) return null;\n    return parser.getDependency('mesh', nodeDef.mesh).then(function (mesh) {\n      const node = parser._getNodeRef(parser.meshCache, nodeDef.mesh, mesh);\n\n      // if weights are provided on the node, override weights on the mesh.\n      if (nodeDef.weights !== undefined) {\n        node.traverse(function (o) {\n          if (!o.isMesh) return;\n          for (let i = 0, il = nodeDef.weights.length; i < il; i++) {\n            o.morphTargetInfluences[i] = nodeDef.weights[i];\n          }\n        });\n      }\n      return node;\n    });\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#nodes-and-hierarchy\n   *\n   * @private\n   * @param {number} nodeIndex\n   * @return {Promise<Object3D>}\n   */\n  loadNode(nodeIndex) {\n    const json = this.json;\n    const parser = this;\n    const nodeDef = json.nodes[nodeIndex];\n    const nodePending = parser._loadNodeShallow(nodeIndex);\n    const childPending = [];\n    const childrenDef = nodeDef.children || [];\n    for (let i = 0, il = childrenDef.length; i < il; i++) {\n      childPending.push(parser.getDependency('node', childrenDef[i]));\n    }\n    const skeletonPending = nodeDef.skin === undefined ? Promise.resolve(null) : parser.getDependency('skin', nodeDef.skin);\n    return Promise.all([nodePending, Promise.all(childPending), skeletonPending]).then(function (results) {\n      const node = results[0];\n      const children = results[1];\n      const skeleton = results[2];\n      if (skeleton !== null) {\n        // This full traverse should be fine because\n        // child glTF nodes have not been added to this node yet.\n        node.traverse(function (mesh) {\n          if (!mesh.isSkinnedMesh) return;\n          mesh.bind(skeleton, _identityMatrix);\n        });\n      }\n      for (let i = 0, il = children.length; i < il; i++) {\n        node.add(children[i]);\n      }\n      return node;\n    });\n  }\n\n  // ._loadNodeShallow() parses a single node.\n  // skin and child nodes are created and added in .loadNode() (no '_' prefix).\n  _loadNodeShallow(nodeIndex) {\n    const json = this.json;\n    const extensions = this.extensions;\n    const parser = this;\n\n    // This method is called from .loadNode() and .loadSkin().\n    // Cache a node to avoid duplication.\n\n    if (this.nodeCache[nodeIndex] !== undefined) {\n      return this.nodeCache[nodeIndex];\n    }\n    const nodeDef = json.nodes[nodeIndex];\n\n    // reserve node's name before its dependencies, so the root has the intended name.\n    const nodeName = nodeDef.name ? parser.createUniqueName(nodeDef.name) : '';\n    const pending = [];\n    const meshPromise = parser._invokeOne(function (ext) {\n      return ext.createNodeMesh && ext.createNodeMesh(nodeIndex);\n    });\n    if (meshPromise) {\n      pending.push(meshPromise);\n    }\n    if (nodeDef.camera !== undefined) {\n      pending.push(parser.getDependency('camera', nodeDef.camera).then(function (camera) {\n        return parser._getNodeRef(parser.cameraCache, nodeDef.camera, camera);\n      }));\n    }\n    parser._invokeAll(function (ext) {\n      return ext.createNodeAttachment && ext.createNodeAttachment(nodeIndex);\n    }).forEach(function (promise) {\n      pending.push(promise);\n    });\n    this.nodeCache[nodeIndex] = Promise.all(pending).then(function (objects) {\n      let node;\n\n      // .isBone isn't in glTF spec. See ._markDefs\n      if (nodeDef.isBone === true) {\n        node = new Bone();\n      } else if (objects.length > 1) {\n        node = new Group();\n      } else if (objects.length === 1) {\n        node = objects[0];\n      } else {\n        node = new Object3D();\n      }\n      if (node !== objects[0]) {\n        for (let i = 0, il = objects.length; i < il; i++) {\n          node.add(objects[i]);\n        }\n      }\n      if (nodeDef.name) {\n        node.userData.name = nodeDef.name;\n        node.name = nodeName;\n      }\n      assignExtrasToUserData(node, nodeDef);\n      if (nodeDef.extensions) addUnknownExtensionsToUserData(extensions, node, nodeDef);\n      if (nodeDef.matrix !== undefined) {\n        const matrix = new Matrix4();\n        matrix.fromArray(nodeDef.matrix);\n        node.applyMatrix4(matrix);\n      } else {\n        if (nodeDef.translation !== undefined) {\n          node.position.fromArray(nodeDef.translation);\n        }\n        if (nodeDef.rotation !== undefined) {\n          node.quaternion.fromArray(nodeDef.rotation);\n        }\n        if (nodeDef.scale !== undefined) {\n          node.scale.fromArray(nodeDef.scale);\n        }\n      }\n      if (!parser.associations.has(node)) {\n        parser.associations.set(node, {});\n      } else if (nodeDef.mesh !== undefined && parser.meshCache.refs[nodeDef.mesh] > 1) {\n        const mapping = parser.associations.get(node);\n        parser.associations.set(node, {\n          ...mapping\n        });\n      }\n      parser.associations.get(node).nodes = nodeIndex;\n      return node;\n    });\n    return this.nodeCache[nodeIndex];\n  }\n\n  /**\n   * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#scenes\n   *\n   * @private\n   * @param {number} sceneIndex\n   * @return {Promise<Group>}\n   */\n  loadScene(sceneIndex) {\n    const extensions = this.extensions;\n    const sceneDef = this.json.scenes[sceneIndex];\n    const parser = this;\n\n    // Loader returns Group, not Scene.\n    // See: https://github.com/mrdoob/three.js/issues/18342#issuecomment-578981172\n    const scene = new Group();\n    if (sceneDef.name) scene.name = parser.createUniqueName(sceneDef.name);\n    assignExtrasToUserData(scene, sceneDef);\n    if (sceneDef.extensions) addUnknownExtensionsToUserData(extensions, scene, sceneDef);\n    const nodeIds = sceneDef.nodes || [];\n    const pending = [];\n    for (let i = 0, il = nodeIds.length; i < il; i++) {\n      pending.push(parser.getDependency('node', nodeIds[i]));\n    }\n    return Promise.all(pending).then(function (nodes) {\n      for (let i = 0, il = nodes.length; i < il; i++) {\n        scene.add(nodes[i]);\n      }\n\n      // Removes dangling associations, associations that reference a node that\n      // didn't make it into the scene.\n      const reduceAssociations = node => {\n        const reducedAssociations = new Map();\n        for (const [key, value] of parser.associations) {\n          if (key instanceof Material || key instanceof Texture) {\n            reducedAssociations.set(key, value);\n          }\n        }\n        node.traverse(node => {\n          const mappings = parser.associations.get(node);\n          if (mappings != null) {\n            reducedAssociations.set(node, mappings);\n          }\n        });\n        return reducedAssociations;\n      };\n      parser.associations = reduceAssociations(scene);\n      return scene;\n    });\n  }\n  _createAnimationTracks(node, inputAccessor, outputAccessor, sampler, target) {\n    const tracks = [];\n    const targetName = node.name ? node.name : node.uuid;\n    const targetNames = [];\n    if (PATH_PROPERTIES[target.path] === PATH_PROPERTIES.weights) {\n      node.traverse(function (object) {\n        if (object.morphTargetInfluences) {\n          targetNames.push(object.name ? object.name : object.uuid);\n        }\n      });\n    } else {\n      targetNames.push(targetName);\n    }\n    let TypedKeyframeTrack;\n    switch (PATH_PROPERTIES[target.path]) {\n      case PATH_PROPERTIES.weights:\n        TypedKeyframeTrack = NumberKeyframeTrack;\n        break;\n      case PATH_PROPERTIES.rotation:\n        TypedKeyframeTrack = QuaternionKeyframeTrack;\n        break;\n      case PATH_PROPERTIES.translation:\n      case PATH_PROPERTIES.scale:\n        TypedKeyframeTrack = VectorKeyframeTrack;\n        break;\n      default:\n        switch (outputAccessor.itemSize) {\n          case 1:\n            TypedKeyframeTrack = NumberKeyframeTrack;\n            break;\n          case 2:\n          case 3:\n          default:\n            TypedKeyframeTrack = VectorKeyframeTrack;\n            break;\n        }\n        break;\n    }\n    const interpolation = sampler.interpolation !== undefined ? INTERPOLATION[sampler.interpolation] : InterpolateLinear;\n    const outputArray = this._getArrayFromAccessor(outputAccessor);\n    for (let j = 0, jl = targetNames.length; j < jl; j++) {\n      const track = new TypedKeyframeTrack(targetNames[j] + '.' + PATH_PROPERTIES[target.path], inputAccessor.array, outputArray, interpolation);\n\n      // Override interpolation with custom factory method.\n      if (sampler.interpolation === 'CUBICSPLINE') {\n        this._createCubicSplineTrackInterpolant(track);\n      }\n      tracks.push(track);\n    }\n    return tracks;\n  }\n  _getArrayFromAccessor(accessor) {\n    let outputArray = accessor.array;\n    if (accessor.normalized) {\n      const scale = getNormalizedComponentScale(outputArray.constructor);\n      const scaled = new Float32Array(outputArray.length);\n      for (let j = 0, jl = outputArray.length; j < jl; j++) {\n        scaled[j] = outputArray[j] * scale;\n      }\n      outputArray = scaled;\n    }\n    return outputArray;\n  }\n  _createCubicSplineTrackInterpolant(track) {\n    track.createInterpolant = function InterpolantFactoryMethodGLTFCubicSpline(result) {\n      // A CUBICSPLINE keyframe in glTF has three output values for each input value,\n      // representing inTangent, splineVertex, and outTangent. As a result, track.getValueSize()\n      // must be divided by three to get the interpolant's sampleSize argument.\n\n      const interpolantType = this instanceof QuaternionKeyframeTrack ? GLTFCubicSplineQuaternionInterpolant : GLTFCubicSplineInterpolant;\n      return new interpolantType(this.times, this.values, this.getValueSize() / 3, result);\n    };\n\n    // Mark as CUBICSPLINE. `track.getInterpolation()` doesn't support custom interpolants.\n    track.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline = true;\n  }\n}\n\n/**\n *\n * @private\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n */\nfunction computeBounds(geometry, primitiveDef, parser) {\n  const attributes = primitiveDef.attributes;\n  const box = new Box3();\n  if (attributes.POSITION !== undefined) {\n    const accessor = parser.json.accessors[attributes.POSITION];\n    const min = accessor.min;\n    const max = accessor.max;\n\n    // glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n    if (min !== undefined && max !== undefined) {\n      box.set(new Vector3(min[0], min[1], min[2]), new Vector3(max[0], max[1], max[2]));\n      if (accessor.normalized) {\n        const boxScale = getNormalizedComponentScale(WEBGL_COMPONENT_TYPES[accessor.componentType]);\n        box.min.multiplyScalar(boxScale);\n        box.max.multiplyScalar(boxScale);\n      }\n    } else {\n      console.warn('THREE.GLTFLoader: Missing min/max properties for accessor POSITION.');\n      return;\n    }\n  } else {\n    return;\n  }\n  const targets = primitiveDef.targets;\n  if (targets !== undefined) {\n    const maxDisplacement = new Vector3();\n    const vector = new Vector3();\n    for (let i = 0, il = targets.length; i < il; i++) {\n      const target = targets[i];\n      if (target.POSITION !== undefined) {\n        const accessor = parser.json.accessors[target.POSITION];\n        const min = accessor.min;\n        const max = accessor.max;\n\n        // glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n        if (min !== undefined && max !== undefined) {\n          // we need to get max of absolute components because target weight is [-1,1]\n          vector.setX(Math.max(Math.abs(min[0]), Math.abs(max[0])));\n          vector.setY(Math.max(Math.abs(min[1]), Math.abs(max[1])));\n          vector.setZ(Math.max(Math.abs(min[2]), Math.abs(max[2])));\n          if (accessor.normalized) {\n            const boxScale = getNormalizedComponentScale(WEBGL_COMPONENT_TYPES[accessor.componentType]);\n            vector.multiplyScalar(boxScale);\n          }\n\n          // Note: this assumes that the sum of all weights is at most 1. This isn't quite correct - it's more conservative\n          // to assume that each target can have a max weight of 1. However, for some use cases - notably, when morph targets\n          // are used to implement key-frame animations and as such only two are active at a time - this results in very large\n          // boxes. So for now we make a box that's sometimes a touch too small but is hopefully mostly of reasonable size.\n          maxDisplacement.max(vector);\n        } else {\n          console.warn('THREE.GLTFLoader: Missing min/max properties for accessor POSITION.');\n        }\n      }\n    }\n\n    // As per comment above this box isn't conservative, but has a reasonable size for a very large number of morph targets.\n    box.expandByVector(maxDisplacement);\n  }\n  geometry.boundingBox = box;\n  const sphere = new Sphere();\n  box.getCenter(sphere.center);\n  sphere.radius = box.min.distanceTo(box.max) / 2;\n  geometry.boundingSphere = sphere;\n}\n\n/**\n *\n * @private\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addPrimitiveAttributes(geometry, primitiveDef, parser) {\n  const attributes = primitiveDef.attributes;\n  const pending = [];\n  function assignAttributeAccessor(accessorIndex, attributeName) {\n    return parser.getDependency('accessor', accessorIndex).then(function (accessor) {\n      geometry.setAttribute(attributeName, accessor);\n    });\n  }\n  for (const gltfAttributeName in attributes) {\n    const threeAttributeName = ATTRIBUTES[gltfAttributeName] || gltfAttributeName.toLowerCase();\n\n    // Skip attributes already provided by e.g. Draco extension.\n    if (threeAttributeName in geometry.attributes) continue;\n    pending.push(assignAttributeAccessor(attributes[gltfAttributeName], threeAttributeName));\n  }\n  if (primitiveDef.indices !== undefined && !geometry.index) {\n    const accessor = parser.getDependency('accessor', primitiveDef.indices).then(function (accessor) {\n      geometry.setIndex(accessor);\n    });\n    pending.push(accessor);\n  }\n  if (ColorManagement.workingColorSpace !== LinearSRGBColorSpace && 'COLOR_0' in attributes) {\n    console.warn(`THREE.GLTFLoader: Converting vertex colors from \"srgb-linear\" to \"${ColorManagement.workingColorSpace}\" not supported.`);\n  }\n  assignExtrasToUserData(geometry, primitiveDef);\n  computeBounds(geometry, primitiveDef, parser);\n  return Promise.all(pending).then(function () {\n    return primitiveDef.targets !== undefined ? addMorphTargets(geometry, primitiveDef.targets, parser) : geometry;\n  });\n}\n\n/**\n * Loader result of `GLTFLoader`.\n *\n * @typedef {Object} GLTFLoader~LoadObject\n * @property {Array<AnimationClip>} animations - An array of animation clips.\n * @property {Object} asset - Meta data about the loaded asset.\n * @property {Array<Camera>} cameras - An array of cameras.\n * @property {GLTFParser} parser - A reference to the internal parser.\n * @property {Group} scene - The default scene.\n * @property {Array<Group>} scenes - glTF assets might define multiple scenes.\n * @property {Object} userData - Additional data.\n **/\n\nexport { GLTFLoader };", "map": {"version": 3, "names": ["AnimationClip", "Bone", "Box3", "BufferAttribute", "BufferGeometry", "ClampToEdgeWrapping", "Color", "ColorManagement", "DirectionalLight", "DoubleSide", "<PERSON><PERSON><PERSON><PERSON>", "FrontSide", "Group", "ImageBitmapLoader", "In<PERSON>d<PERSON>esh", "InterleavedBuffer", "InterleavedBufferAttribute", "Interpolant", "InterpolateDiscrete", "InterpolateLinear", "Line", "LineBasicMaterial", "LineLoop", "LineSegments", "LinearFilter", "LinearMipmapLinearFilter", "LinearMipmapNearestFilter", "LinearSRGBColorSpace", "Loader", "LoaderUtils", "Material", "MathUtils", "Matrix4", "<PERSON><PERSON>", "MeshBasicMaterial", "MeshPhysicalMaterial", "MeshStandardMaterial", "MirroredRepeatWrapping", "NearestFilter", "NearestMipmapLinearFilter", "NearestMipmapNearestFilter", "NumberKeyframeTrack", "Object3D", "OrthographicCamera", "PerspectiveCamera", "PointLight", "Points", "PointsMaterial", "PropertyBinding", "Quaternion", "QuaternionKeyframeTrack", "RepeatWrapping", "Skeleton", "<PERSON><PERSON><PERSON><PERSON>", "Sphere", "SpotLight", "Texture", "TextureLoader", "TriangleFanDrawMode", "TriangleStripDrawMode", "Vector2", "Vector3", "VectorKeyframeTrack", "SRGBColorSpace", "InstancedBufferAttribute", "toTrianglesDrawMode", "GLTFLoader", "constructor", "manager", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ktx2Loader", "meshoptDecoder", "pluginCallbacks", "register", "parser", "GLTFMaterialsClearcoatExtension", "GLTFMaterialsDispersionExtension", "GLTFTextureBasisUExtension", "GLTFTextureWebPExtension", "GLTFTextureAVIFExtension", "GLTFMaterialsSheenExtension", "GLTFMaterialsTransmissionExtension", "GLTFMaterialsVolumeExtension", "GLTFMaterialsIorExtension", "GLTFMaterialsEmissiveStrengthExtension", "GLTFMaterialsSpecularExtension", "GLTFMaterialsIridescenceExtension", "GLTFMaterialsAnisotropyExtension", "GLTFMaterialsBumpExtension", "GLTFLightsExtension", "GLTFMeshoptCompression", "GLTFMeshGpuInstancing", "load", "url", "onLoad", "onProgress", "onError", "scope", "resourcePath", "path", "relativeUrl", "extractUrlBase", "resolveURL", "itemStart", "_onError", "e", "console", "error", "itemError", "itemEnd", "loader", "set<PERSON>ath", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "data", "parse", "gltf", "setDRACOLoader", "setKTX2Loader", "setMeshoptDecoder", "callback", "indexOf", "push", "unregister", "splice", "json", "extensions", "plugins", "textDecoder", "TextDecoder", "JSON", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magic", "decode", "Uint8Array", "BINARY_EXTENSION_HEADER_MAGIC", "EXTENSIONS", "KHR_BINARY_GLTF", "GLTFBinaryExtension", "content", "asset", "undefined", "version", "Error", "GLTFParser", "crossOrigin", "fileLoader", "i", "length", "plugin", "name", "extensionsUsed", "extensionName", "extensionsRequired", "KHR_MATERIALS_UNLIT", "GLTFMaterialsUnlitExtension", "KHR_DRACO_MESH_COMPRESSION", "GLTFDracoMeshCompressionExtension", "KHR_TEXTURE_TRANSFORM", "GLTFTextureTransformExtension", "KHR_MESH_QUANTIZATION", "GLTFMeshQuantizationExtension", "warn", "setExtensions", "setPlugins", "parseAsync", "Promise", "resolve", "reject", "GLTFRegistry", "objects", "get", "key", "add", "object", "remove", "removeAll", "KHR_LIGHTS_PUNCTUAL", "KHR_MATERIALS_CLEARCOAT", "KHR_MATERIALS_DISPERSION", "KHR_MATERIALS_IOR", "KHR_MATERIALS_SHEEN", "KHR_MATERIALS_SPECULAR", "KHR_MATERIALS_TRANSMISSION", "KHR_MATERIALS_IRIDESCENCE", "KHR_MATERIALS_ANISOTROPY", "KHR_MATERIALS_VOLUME", "KHR_TEXTURE_BASISU", "KHR_MATERIALS_EMISSIVE_STRENGTH", "EXT_MATERIALS_BUMP", "EXT_TEXTURE_WEBP", "EXT_TEXTURE_AVIF", "EXT_MESHOPT_COMPRESSION", "EXT_MESH_GPU_INSTANCING", "cache", "refs", "uses", "_markDefs", "nodeDefs", "nodes", "nodeIndex", "node<PERSON><PERSON><PERSON>", "nodeDef", "light", "_addNodeRef", "_loadLight", "lightIndex", "cache<PERSON>ey", "dependency", "lightDefs", "lights", "lightDef", "lightNode", "color", "setRGB", "range", "type", "target", "position", "set", "distance", "spot", "innerConeAngle", "outerConeAngle", "Math", "PI", "angle", "penumbra", "assignExtrasToUserData", "intensity", "createUniqueName", "getDependency", "index", "createNodeAttachment", "self", "then", "_getNodeRef", "getMaterialType", "extendParams", "materialParams", "materialDef", "pending", "opacity", "metallicRoughness", "pbrMetallicRoughness", "Array", "isArray", "baseColorFactor", "array", "baseColorTexture", "assignTexture", "all", "extendMaterialParams", "materialIndex", "materials", "emissiveStrength", "emissiveIntensity", "extension", "clearcoatFactor", "clearcoat", "clearcoatTexture", "clearcoatRoughnessFactor", "clearcoatRoughness", "clearcoatRoughnessTexture", "clearcoatNormalTexture", "scale", "clearcoatNormalScale", "dispersion", "iridescenceFactor", "iridescence", "iridescenceTexture", "iridescenceIor", "iridescenceIOR", "iridescenceThicknessRange", "iridescenceThicknessMinimum", "iridescenceThicknessMaximum", "iridescenceThicknessTexture", "sheenColor", "sheenRoughness", "sheen", "sheenColorFactor", "colorFactor", "sheenRoughnessFactor", "sheenColorTexture", "sheenRoughnessTexture", "transmissionFactor", "transmission", "transmissionTexture", "thickness", "thicknessFactor", "thicknessTexture", "attenuationDistance", "Infinity", "colorArray", "attenuationColor", "ior", "specularIntensity", "specularFactor", "specularTexture", "specularColorFactor", "specularColor", "specularColorTexture", "bumpScale", "bumpFactor", "bumpTexture", "anisotropyStrength", "anisotropy", "anisotropyRotation", "anisotropyTexture", "loadTexture", "textureIndex", "textureDef", "textures", "options", "loadTextureImage", "source", "images", "textureLoader", "uri", "handler", "<PERSON><PERSON><PERSON><PERSON>", "loadBufferView", "bufferView", "bufferViews", "extensionDef", "buffer", "decoder", "supported", "res", "byteOffset", "byteLength", "count", "stride", "byteStride", "decodeGltfBufferAsync", "mode", "filter", "ready", "result", "decodeGltfBuffer", "createNodeMesh", "mesh", "meshDef", "meshes", "primitive", "primitives", "WEBGL_CONSTANTS", "TRIANGLES", "TRIANGLE_STRIP", "TRIANGLE_FAN", "attributesDef", "attributes", "accessor", "results", "nodeObject", "pop", "isGroup", "children", "instanced<PERSON><PERSON><PERSON>", "m", "p", "q", "s", "instanced<PERSON><PERSON>", "geometry", "material", "TRANSLATION", "fromBufferAttribute", "ROTATION", "SCALE", "setMatrixAt", "compose", "attributeName", "attr", "instanceColor", "itemSize", "normalized", "setAttribute", "prototype", "copy", "call", "assignFinalMaterial", "clear", "BINARY_EXTENSION_HEADER_LENGTH", "BINARY_EXTENSION_CHUNK_TYPES", "BIN", "body", "headerView", "DataView", "header", "slice", "getUint32", "chunkContentsLength", "chunkView", "chunkIndex", "chunkLength", "chunkType", "contentArray", "preload", "decodePrimitive", "bufferViewIndex", "gltfAttributeMap", "threeAttributeMap", "attributeNormalizedMap", "attributeTypeMap", "threeAttributeName", "ATTRIBUTES", "toLowerCase", "accessorDef", "accessors", "componentType", "WEBGL_COMPONENT_TYPES", "decodeDracoFile", "attribute", "extendTexture", "texture", "transform", "texCoord", "channel", "offset", "rotation", "clone", "fromArray", "repeat", "needsUpdate", "GLTFCubicSplineInterpolant", "parameterPositions", "sampleValues", "sampleSize", "result<PERSON><PERSON><PERSON>", "copySampleValue_", "values", "valueSize", "interpolate_", "i1", "t0", "t", "t1", "stride2", "stride3", "td", "pp", "ppp", "offset1", "offset0", "s2", "s3", "s0", "s1", "p0", "m0", "p1", "m1", "_quaternion", "GLTFCubicSplineQuaternionInterpolant", "normalize", "toArray", "FLOAT", "FLOAT_MAT3", "FLOAT_MAT4", "FLOAT_VEC2", "FLOAT_VEC3", "FLOAT_VEC4", "LINEAR", "REPEAT", "SAMPLER_2D", "POINTS", "LINES", "LINE_LOOP", "LINE_STRIP", "UNSIGNED_BYTE", "UNSIGNED_SHORT", "Int8Array", "Int16Array", "Uint16Array", "Uint32Array", "Float32Array", "WEBGL_FILTERS", "WEBGL_WRAPPINGS", "WEBGL_TYPE_SIZES", "POSITION", "NORMAL", "TANGENT", "TEXCOORD_0", "TEXCOORD_1", "TEXCOORD_2", "TEXCOORD_3", "COLOR_0", "WEIGHTS_0", "JOINTS_0", "PATH_PROPERTIES", "translation", "weights", "INTERPOLATION", "CUBICSPLINE", "STEP", "ALPHA_MODES", "OPAQUE", "MASK", "BLEND", "createDefaultMaterial", "emissive", "metalness", "roughness", "transparent", "depthTest", "side", "addUnknownExtensionsToUserData", "knownExtensions", "objectDef", "userData", "gltfExtensions", "gltfDef", "extras", "Object", "assign", "addMorphTargets", "targets", "hasMorphPosition", "hasMorphNormal", "hasMorphColor", "il", "pendingPositionAccessors", "pendingNormalAccessors", "pendingColorAccessors", "pendingAccessor", "normal", "morphPositions", "morphNormals", "morphColors", "morphAttributes", "morphTargetsRelative", "updateMorphTargets", "morphTargetInfluences", "targetNames", "morphTargetDictionary", "createPrimitiveKey", "primitiveDef", "geometry<PERSON>ey", "dracoExtension", "indices", "createAttributesKey", "<PERSON><PERSON><PERSON>", "keys", "sort", "getNormalizedComponentScale", "getImageURIMimeType", "search", "_identityMatrix", "associations", "Map", "primitiveCache", "nodeCache", "meshCache", "cameraCache", "lightCache", "sourceCache", "textureCache", "nodeNamesUsed", "<PERSON><PERSON><PERSON><PERSON>", "safariVersion", "isFirefox", "firefoxVersion", "navigator", "userAgent", "test", "safariMatch", "match", "parseInt", "createImageBitmap", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_invokeAll", "ext", "beforeRoot", "getDependencies", "dependencies", "scene", "scenes", "animations", "cameras", "afterRoot", "updateMatrixWorld", "catch", "skinDefs", "skins", "meshDefs", "skinIndex", "<PERSON><PERSON><PERSON><PERSON>", "joints", "isBone", "skin", "isSkinnedMesh", "camera", "ref", "updateMappings", "original", "mappings", "child", "entries", "_invokeOne", "func", "unshift", "loadScene", "loadNode", "loadMesh", "loadAccessor", "loadBuffer", "loadMaterial", "loadSkin", "loadAnimation", "loadCamera", "defs", "map", "def", "bufferIndex", "bufferDef", "buffers", "bufferViewDef", "accessorIndex", "sparse", "TypedArray", "pendingBufferViews", "elementBytes", "BYTES_PER_ELEMENT", "itemBytes", "bufferAttribute", "ibSlice", "floor", "ib<PERSON><PERSON><PERSON><PERSON>", "ib", "itemSizeIndices", "SCALAR", "TypedArrayIndices", "byteOffsetIndices", "byteOffsetValues", "sparseIndices", "sparseValues", "setX", "setY", "setZ", "setW", "sourceIndex", "sourceDef", "sampler", "promise", "loadImageSource", "flipY", "startsWith", "samplers", "magFilter", "minFilter", "wrapS", "wrapT", "generateMipmaps", "isCompressedTexture", "URL", "webkitURL", "sourceURI", "isObjectURL", "blob", "Blob", "mimeType", "createObjectURL", "isImageBitmapLoader", "imageBitmap", "revokeObjectURL", "mapName", "mapDef", "colorSpace", "gltfReference", "useDerivativeTangents", "tangent", "useVertexColors", "useFlatShading", "isPoints", "uuid", "pointsMaterial", "sizeAttenuation", "isLine", "lineMaterial", "cachedMaterial", "vertexColors", "flatShading", "normalScale", "y", "materialType", "materialExtensions", "kmuExtension", "metallicFactor", "roughnessFactor", "metallicRoughnessTexture", "doubleSided", "alphaMode", "depthWrite", "alphaTest", "<PERSON><PERSON><PERSON><PERSON>", "normalTexture", "occlusionTexture", "strength", "aoMapIntensity", "emissiveFactor", "emissiveTexture", "originalName", "sanitizedName", "sanitizeNodeName", "loadGeometries", "createDracoPrimitive", "addPrimitiveAttributes", "cached", "geometryPromise", "meshIndex", "geometries", "normalizeSkinWeights", "group", "cameraIndex", "cameraDef", "params", "radToDeg", "yfov", "aspectRatio", "znear", "zfar", "xmag", "ymag", "skinDef", "_loadNodeShallow", "inverseBindMatrices", "jointNodes", "bones", "boneInverses", "jointNode", "mat", "animationIndex", "animationDef", "animationName", "pendingNodes", "pendingInputAccessors", "pendingOutputAccessors", "pendingSamplers", "pendingTargets", "channels", "node", "input", "parameters", "output", "inputAccessors", "outputAccessors", "tracks", "inputAccessor", "outputAccessor", "updateMatrix", "createdTracks", "_createAnimationTracks", "k", "traverse", "o", "<PERSON><PERSON><PERSON>", "nodePending", "childPending", "childrenDef", "skeletonPending", "skeleton", "bind", "nodeName", "meshPromise", "for<PERSON>ach", "matrix", "applyMatrix4", "quaternion", "has", "mapping", "sceneIndex", "sceneDef", "nodeIds", "reduceAssociations", "reducedAssociations", "value", "targetName", "TypedKeyframeTrack", "interpolation", "outputArray", "_getArrayFromAccessor", "j", "jl", "track", "_createCubicSplineTrackInterpolant", "scaled", "createInterpolant", "InterpolantFactoryMethodGLTFCubicSpline", "interpolantType", "times", "getValueSize", "isInterpolantFactoryMethodGLTFCubicSpline", "computeBounds", "box", "min", "max", "boxScale", "multiplyScalar", "maxDisplacement", "vector", "abs", "expandByVector", "boundingBox", "sphere", "getCenter", "center", "radius", "distanceTo", "boundingSphere", "assignAttributeAccessor", "gltfAttributeName", "setIndex", "workingColorSpace"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/three/examples/jsm/loaders/GLTFLoader.js"], "sourcesContent": ["import {\n\tAnimationClip,\n\tBone,\n\tBox3,\n\tBufferAttribute,\n\tBufferGeometry,\n\tClampToEdgeWrapping,\n\tColor,\n\tColorManagement,\n\tDirectionalLight,\n\tDoubleSide,\n\tFileLoader,\n\tFrontSide,\n\tGroup,\n\tImageBitmapLoader,\n\tInstancedMesh,\n\tInterleavedBuffer,\n\tInterleavedBufferAttribute,\n\tInterpolant,\n\tInterpolateDiscrete,\n\tInterpolateLinear,\n\tLine,\n\tLineBasicMaterial,\n\tLineLoop,\n\tLineSegments,\n\tLinearFilter,\n\tLinearMipmapLinearFilter,\n\tLinearMipmapNearestFilter,\n\tLinearSRGBColorSpace,\n\tLoader,\n\tLoaderUtils,\n\tMaterial,\n\tMathUtils,\n\tMatrix4,\n\tMesh,\n\tMeshBasicMaterial,\n\tMeshPhysicalMaterial,\n\tMeshStandardMaterial,\n\tMirroredRepeatWrapping,\n\tNearestFilter,\n\tNearestMipmapLinearFilter,\n\tNearestMipmapNearestFilter,\n\tNumberKeyframeTrack,\n\tObject3D,\n\tOrthographicCamera,\n\tPerspectiveCamera,\n\tPointLight,\n\tPoints,\n\tPointsMaterial,\n\tPropertyBinding,\n\tQuaternion,\n\tQuaternionKeyframeTrack,\n\tRepeatWrapping,\n\tSkeleton,\n\tSkinnedMesh,\n\tSphere,\n\tSpotLight,\n\tTexture,\n\tTextureLoader,\n\tTriangleFanDrawMode,\n\tTriangleStripDrawMode,\n\tVector2,\n\tVector3,\n\tVectorKeyframeTrack,\n\tSRGBColorSpace,\n\tInstancedBufferAttribute\n} from 'three';\nimport { toTrianglesDrawMode } from '../utils/BufferGeometryUtils.js';\n\n/**\n * A loader for the glTF 2.0 format.\n *\n * [glTF]{@link https://www.khronos.org/gltf/} (GL Transmission Format) is an [open format specification]{@link https://github.com/KhronosGroup/glTF/tree/main/specification/2.0}\n * for efficient delivery and loading of 3D content. Assets may be provided either in JSON (.gltf) or binary (.glb)\n * format. External files store textures (.jpg, .png) and additional binary data (.bin). A glTF asset may deliver\n * one or more scenes, including meshes, materials, textures, skins, skeletons, morph targets, animations, lights,\n * and/or cameras.\n *\n * `GLTFLoader` uses {@link ImageBitmapLoader} whenever possible. Be advised that image bitmaps are not\n * automatically GC-collected when they are no longer referenced, and they require special handling during\n * the disposal process.\n *\n * `GLTFLoader` supports the following glTF 2.0 extensions:\n * - KHR_draco_mesh_compression\n * - KHR_materials_clearcoat\n * - KHR_materials_dispersion\n * - KHR_materials_ior\n * - KHR_materials_specular\n * - KHR_materials_transmission\n * - KHR_materials_iridescence\n * - KHR_materials_unlit\n * - KHR_materials_volume\n * - KHR_mesh_quantization\n * - KHR_lights_punctual\n * - KHR_texture_basisu\n * - KHR_texture_transform\n * - EXT_texture_webp\n * - EXT_meshopt_compression\n * - EXT_mesh_gpu_instancing\n *\n * The following glTF 2.0 extension is supported by an external user plugin:\n * - [KHR_materials_variants]{@link https://github.com/takahirox/three-gltf-extensions}\n * - [MSFT_texture_dds]{@link https://github.com/takahirox/three-gltf-extensions}\n *\n * ```js\n * const loader = new GLTFLoader();\n *\n * // Optional: Provide a DRACOLoader instance to decode compressed mesh data\n * const dracoLoader = new DRACOLoader();\n * dracoLoader.setDecoderPath( '/examples/jsm/libs/draco/' );\n * loader.setDRACOLoader( dracoLoader );\n *\n * const gltf = await loader.loadAsync( 'models/gltf/duck/duck.gltf' );\n * scene.add( gltf.scene );\n * ```\n *\n * @augments Loader\n * @three_import import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';\n */\nclass GLTFLoader extends Loader {\n\n\t/**\n\t * Constructs a new glTF loader.\n\t *\n\t * @param {LoadingManager} [manager] - The loading manager.\n\t */\n\tconstructor( manager ) {\n\n\t\tsuper( manager );\n\n\t\tthis.dracoLoader = null;\n\t\tthis.ktx2Loader = null;\n\t\tthis.meshoptDecoder = null;\n\n\t\tthis.pluginCallbacks = [];\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsClearcoatExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsDispersionExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFTextureBasisUExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFTextureWebPExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFTextureAVIFExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsSheenExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsTransmissionExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsVolumeExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsIorExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsEmissiveStrengthExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsSpecularExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsIridescenceExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsAnisotropyExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMaterialsBumpExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFLightsExtension( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMeshoptCompression( parser );\n\n\t\t} );\n\n\t\tthis.register( function ( parser ) {\n\n\t\t\treturn new GLTFMeshGpuInstancing( parser );\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Starts loading from the given URL and passes the loaded glTF asset\n\t * to the `onLoad()` callback.\n\t *\n\t * @param {string} url - The path/URL of the file to be loaded. This can also be a data URI.\n\t * @param {function(GLTFLoader~LoadObject)} onLoad - Executed when the loading process has been finished.\n\t * @param {onProgressCallback} onProgress - Executed while the loading is in progress.\n\t * @param {onErrorCallback} onError - Executed when errors occur.\n\t */\n\tload( url, onLoad, onProgress, onError ) {\n\n\t\tconst scope = this;\n\n\t\tlet resourcePath;\n\n\t\tif ( this.resourcePath !== '' ) {\n\n\t\t\tresourcePath = this.resourcePath;\n\n\t\t} else if ( this.path !== '' ) {\n\n\t\t\t// If a base path is set, resources will be relative paths from that plus the relative path of the gltf file\n\t\t\t// Example  path = 'https://my-cnd-server.com/', url = 'assets/models/model.gltf'\n\t\t\t// resourcePath = 'https://my-cnd-server.com/assets/models/'\n\t\t\t// referenced resource 'model.bin' will be loaded from 'https://my-cnd-server.com/assets/models/model.bin'\n\t\t\t// referenced resource '../textures/texture.png' will be loaded from 'https://my-cnd-server.com/assets/textures/texture.png'\n\t\t\tconst relativeUrl = LoaderUtils.extractUrlBase( url );\n\t\t\tresourcePath = LoaderUtils.resolveURL( relativeUrl, this.path );\n\n\t\t} else {\n\n\t\t\tresourcePath = LoaderUtils.extractUrlBase( url );\n\n\t\t}\n\n\t\t// Tells the LoadingManager to track an extra item, which resolves after\n\t\t// the model is fully loaded. This means the count of items loaded will\n\t\t// be incorrect, but ensures manager.onLoad() does not fire early.\n\t\tthis.manager.itemStart( url );\n\n\t\tconst _onError = function ( e ) {\n\n\t\t\tif ( onError ) {\n\n\t\t\t\tonError( e );\n\n\t\t\t} else {\n\n\t\t\t\tconsole.error( e );\n\n\t\t\t}\n\n\t\t\tscope.manager.itemError( url );\n\t\t\tscope.manager.itemEnd( url );\n\n\t\t};\n\n\t\tconst loader = new FileLoader( this.manager );\n\n\t\tloader.setPath( this.path );\n\t\tloader.setResponseType( 'arraybuffer' );\n\t\tloader.setRequestHeader( this.requestHeader );\n\t\tloader.setWithCredentials( this.withCredentials );\n\n\t\tloader.load( url, function ( data ) {\n\n\t\t\ttry {\n\n\t\t\t\tscope.parse( data, resourcePath, function ( gltf ) {\n\n\t\t\t\t\tonLoad( gltf );\n\n\t\t\t\t\tscope.manager.itemEnd( url );\n\n\t\t\t\t}, _onError );\n\n\t\t\t} catch ( e ) {\n\n\t\t\t\t_onError( e );\n\n\t\t\t}\n\n\t\t}, onProgress, _onError );\n\n\t}\n\n\t/**\n\t * Sets the given Draco loader to this loader. Required for decoding assets\n\t * compressed with the `KHR_draco_mesh_compression` extension.\n\t *\n\t * @param {DRACOLoader} dracoLoader - The Draco loader to set.\n\t * @return {GLTFLoader} A reference to this loader.\n\t */\n\tsetDRACOLoader( dracoLoader ) {\n\n\t\tthis.dracoLoader = dracoLoader;\n\t\treturn this;\n\n\t}\n\n\t/**\n\t * Sets the given KTX2 loader to this loader. Required for loading KTX2\n\t * compressed textures.\n\t *\n\t * @param {KTX2Loader} ktx2Loader - The KTX2 loader to set.\n\t * @return {GLTFLoader} A reference to this loader.\n\t */\n\tsetKTX2Loader( ktx2Loader ) {\n\n\t\tthis.ktx2Loader = ktx2Loader;\n\t\treturn this;\n\n\t}\n\n\t/**\n\t * Sets the given meshopt decoder. Required for decoding assets\n\t * compressed with the `EXT_meshopt_compression` extension.\n\t *\n\t * @param {Object} meshoptDecoder - The meshopt decoder to set.\n\t * @return {GLTFLoader} A reference to this loader.\n\t */\n\tsetMeshoptDecoder( meshoptDecoder ) {\n\n\t\tthis.meshoptDecoder = meshoptDecoder;\n\t\treturn this;\n\n\t}\n\n\t/**\n\t * Registers a plugin callback. This API is internally used to implement the various\n\t * glTF extensions but can also used by third-party code to add additional logic\n\t * to the loader.\n\t *\n\t * @param {function(parser:GLTFParser)} callback - The callback function to register.\n\t * @return {GLTFLoader} A reference to this loader.\n\t */\n\tregister( callback ) {\n\n\t\tif ( this.pluginCallbacks.indexOf( callback ) === - 1 ) {\n\n\t\t\tthis.pluginCallbacks.push( callback );\n\n\t\t}\n\n\t\treturn this;\n\n\t}\n\n\t/**\n\t * Unregisters a plugin callback.\n\t *\n\t * @param {Function} callback - The callback function to unregister.\n\t * @return {GLTFLoader} A reference to this loader.\n\t */\n\tunregister( callback ) {\n\n\t\tif ( this.pluginCallbacks.indexOf( callback ) !== - 1 ) {\n\n\t\t\tthis.pluginCallbacks.splice( this.pluginCallbacks.indexOf( callback ), 1 );\n\n\t\t}\n\n\t\treturn this;\n\n\t}\n\n\t/**\n\t * Parses the given FBX data and returns the resulting group.\n\t *\n\t * @param {string|ArrayBuffer} data - The raw glTF data.\n\t * @param {string} path - The URL base path.\n\t * @param {function(GLTFLoader~LoadObject)} onLoad - Executed when the loading process has been finished.\n\t * @param {onErrorCallback} onError - Executed when errors occur.\n\t */\n\tparse( data, path, onLoad, onError ) {\n\n\t\tlet json;\n\t\tconst extensions = {};\n\t\tconst plugins = {};\n\t\tconst textDecoder = new TextDecoder();\n\n\t\tif ( typeof data === 'string' ) {\n\n\t\t\tjson = JSON.parse( data );\n\n\t\t} else if ( data instanceof ArrayBuffer ) {\n\n\t\t\tconst magic = textDecoder.decode( new Uint8Array( data, 0, 4 ) );\n\n\t\t\tif ( magic === BINARY_EXTENSION_HEADER_MAGIC ) {\n\n\t\t\t\ttry {\n\n\t\t\t\t\textensions[ EXTENSIONS.KHR_BINARY_GLTF ] = new GLTFBinaryExtension( data );\n\n\t\t\t\t} catch ( error ) {\n\n\t\t\t\t\tif ( onError ) onError( error );\n\t\t\t\t\treturn;\n\n\t\t\t\t}\n\n\t\t\t\tjson = JSON.parse( extensions[ EXTENSIONS.KHR_BINARY_GLTF ].content );\n\n\t\t\t} else {\n\n\t\t\t\tjson = JSON.parse( textDecoder.decode( data ) );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tjson = data;\n\n\t\t}\n\n\t\tif ( json.asset === undefined || json.asset.version[ 0 ] < 2 ) {\n\n\t\t\tif ( onError ) onError( new Error( 'THREE.GLTFLoader: Unsupported asset. glTF versions >=2.0 are supported.' ) );\n\t\t\treturn;\n\n\t\t}\n\n\t\tconst parser = new GLTFParser( json, {\n\n\t\t\tpath: path || this.resourcePath || '',\n\t\t\tcrossOrigin: this.crossOrigin,\n\t\t\trequestHeader: this.requestHeader,\n\t\t\tmanager: this.manager,\n\t\t\tktx2Loader: this.ktx2Loader,\n\t\t\tmeshoptDecoder: this.meshoptDecoder\n\n\t\t} );\n\n\t\tparser.fileLoader.setRequestHeader( this.requestHeader );\n\n\t\tfor ( let i = 0; i < this.pluginCallbacks.length; i ++ ) {\n\n\t\t\tconst plugin = this.pluginCallbacks[ i ]( parser );\n\n\t\t\tif ( ! plugin.name ) console.error( 'THREE.GLTFLoader: Invalid plugin found: missing name' );\n\n\t\t\tplugins[ plugin.name ] = plugin;\n\n\t\t\t// Workaround to avoid determining as unknown extension\n\t\t\t// in addUnknownExtensionsToUserData().\n\t\t\t// Remove this workaround if we move all the existing\n\t\t\t// extension handlers to plugin system\n\t\t\textensions[ plugin.name ] = true;\n\n\t\t}\n\n\t\tif ( json.extensionsUsed ) {\n\n\t\t\tfor ( let i = 0; i < json.extensionsUsed.length; ++ i ) {\n\n\t\t\t\tconst extensionName = json.extensionsUsed[ i ];\n\t\t\t\tconst extensionsRequired = json.extensionsRequired || [];\n\n\t\t\t\tswitch ( extensionName ) {\n\n\t\t\t\t\tcase EXTENSIONS.KHR_MATERIALS_UNLIT:\n\t\t\t\t\t\textensions[ extensionName ] = new GLTFMaterialsUnlitExtension();\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase EXTENSIONS.KHR_DRACO_MESH_COMPRESSION:\n\t\t\t\t\t\textensions[ extensionName ] = new GLTFDracoMeshCompressionExtension( json, this.dracoLoader );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase EXTENSIONS.KHR_TEXTURE_TRANSFORM:\n\t\t\t\t\t\textensions[ extensionName ] = new GLTFTextureTransformExtension();\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase EXTENSIONS.KHR_MESH_QUANTIZATION:\n\t\t\t\t\t\textensions[ extensionName ] = new GLTFMeshQuantizationExtension();\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tdefault:\n\n\t\t\t\t\t\tif ( extensionsRequired.indexOf( extensionName ) >= 0 && plugins[ extensionName ] === undefined ) {\n\n\t\t\t\t\t\t\tconsole.warn( 'THREE.GLTFLoader: Unknown extension \"' + extensionName + '\".' );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tparser.setExtensions( extensions );\n\t\tparser.setPlugins( plugins );\n\t\tparser.parse( onLoad, onError );\n\n\t}\n\n\t/**\n\t * Async version of {@link GLTFLoader#parse}.\n\t *\n\t * @async\n\t * @param {string|ArrayBuffer} data - The raw glTF data.\n\t * @param {string} path - The URL base path.\n\t * @return {Promise<GLTFLoader~LoadObject>} A Promise that resolves with the loaded glTF when the parsing has been finished.\n\t */\n\tparseAsync( data, path ) {\n\n\t\tconst scope = this;\n\n\t\treturn new Promise( function ( resolve, reject ) {\n\n\t\t\tscope.parse( data, path, resolve, reject );\n\n\t\t} );\n\n\t}\n\n}\n\n/* GLTFREGISTRY */\n\nfunction GLTFRegistry() {\n\n\tlet objects = {};\n\n\treturn\t{\n\n\t\tget: function ( key ) {\n\n\t\t\treturn objects[ key ];\n\n\t\t},\n\n\t\tadd: function ( key, object ) {\n\n\t\t\tobjects[ key ] = object;\n\n\t\t},\n\n\t\tremove: function ( key ) {\n\n\t\t\tdelete objects[ key ];\n\n\t\t},\n\n\t\tremoveAll: function () {\n\n\t\t\tobjects = {};\n\n\t\t}\n\n\t};\n\n}\n\n/*********************************/\n/********** EXTENSIONS ***********/\n/*********************************/\n\nconst EXTENSIONS = {\n\tKHR_BINARY_GLTF: 'KHR_binary_glTF',\n\tKHR_DRACO_MESH_COMPRESSION: 'KHR_draco_mesh_compression',\n\tKHR_LIGHTS_PUNCTUAL: 'KHR_lights_punctual',\n\tKHR_MATERIALS_CLEARCOAT: 'KHR_materials_clearcoat',\n\tKHR_MATERIALS_DISPERSION: 'KHR_materials_dispersion',\n\tKHR_MATERIALS_IOR: 'KHR_materials_ior',\n\tKHR_MATERIALS_SHEEN: 'KHR_materials_sheen',\n\tKHR_MATERIALS_SPECULAR: 'KHR_materials_specular',\n\tKHR_MATERIALS_TRANSMISSION: 'KHR_materials_transmission',\n\tKHR_MATERIALS_IRIDESCENCE: 'KHR_materials_iridescence',\n\tKHR_MATERIALS_ANISOTROPY: 'KHR_materials_anisotropy',\n\tKHR_MATERIALS_UNLIT: 'KHR_materials_unlit',\n\tKHR_MATERIALS_VOLUME: 'KHR_materials_volume',\n\tKHR_TEXTURE_BASISU: 'KHR_texture_basisu',\n\tKHR_TEXTURE_TRANSFORM: 'KHR_texture_transform',\n\tKHR_MESH_QUANTIZATION: 'KHR_mesh_quantization',\n\tKHR_MATERIALS_EMISSIVE_STRENGTH: 'KHR_materials_emissive_strength',\n\tEXT_MATERIALS_BUMP: 'EXT_materials_bump',\n\tEXT_TEXTURE_WEBP: 'EXT_texture_webp',\n\tEXT_TEXTURE_AVIF: 'EXT_texture_avif',\n\tEXT_MESHOPT_COMPRESSION: 'EXT_meshopt_compression',\n\tEXT_MESH_GPU_INSTANCING: 'EXT_mesh_gpu_instancing'\n};\n\n/**\n * Punctual Lights Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_lights_punctual\n *\n * @private\n */\nclass GLTFLightsExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_LIGHTS_PUNCTUAL;\n\n\t\t// Object3D instance caches\n\t\tthis.cache = { refs: {}, uses: {} };\n\n\t}\n\n\t_markDefs() {\n\n\t\tconst parser = this.parser;\n\t\tconst nodeDefs = this.parser.json.nodes || [];\n\n\t\tfor ( let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex ++ ) {\n\n\t\t\tconst nodeDef = nodeDefs[ nodeIndex ];\n\n\t\t\tif ( nodeDef.extensions\n\t\t\t\t\t&& nodeDef.extensions[ this.name ]\n\t\t\t\t\t&& nodeDef.extensions[ this.name ].light !== undefined ) {\n\n\t\t\t\tparser._addNodeRef( this.cache, nodeDef.extensions[ this.name ].light );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t_loadLight( lightIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst cacheKey = 'light:' + lightIndex;\n\t\tlet dependency = parser.cache.get( cacheKey );\n\n\t\tif ( dependency ) return dependency;\n\n\t\tconst json = parser.json;\n\t\tconst extensions = ( json.extensions && json.extensions[ this.name ] ) || {};\n\t\tconst lightDefs = extensions.lights || [];\n\t\tconst lightDef = lightDefs[ lightIndex ];\n\t\tlet lightNode;\n\n\t\tconst color = new Color( 0xffffff );\n\n\t\tif ( lightDef.color !== undefined ) color.setRGB( lightDef.color[ 0 ], lightDef.color[ 1 ], lightDef.color[ 2 ], LinearSRGBColorSpace );\n\n\t\tconst range = lightDef.range !== undefined ? lightDef.range : 0;\n\n\t\tswitch ( lightDef.type ) {\n\n\t\t\tcase 'directional':\n\t\t\t\tlightNode = new DirectionalLight( color );\n\t\t\t\tlightNode.target.position.set( 0, 0, - 1 );\n\t\t\t\tlightNode.add( lightNode.target );\n\t\t\t\tbreak;\n\n\t\t\tcase 'point':\n\t\t\t\tlightNode = new PointLight( color );\n\t\t\t\tlightNode.distance = range;\n\t\t\t\tbreak;\n\n\t\t\tcase 'spot':\n\t\t\t\tlightNode = new SpotLight( color );\n\t\t\t\tlightNode.distance = range;\n\t\t\t\t// Handle spotlight properties.\n\t\t\t\tlightDef.spot = lightDef.spot || {};\n\t\t\t\tlightDef.spot.innerConeAngle = lightDef.spot.innerConeAngle !== undefined ? lightDef.spot.innerConeAngle : 0;\n\t\t\t\tlightDef.spot.outerConeAngle = lightDef.spot.outerConeAngle !== undefined ? lightDef.spot.outerConeAngle : Math.PI / 4.0;\n\t\t\t\tlightNode.angle = lightDef.spot.outerConeAngle;\n\t\t\t\tlightNode.penumbra = 1.0 - lightDef.spot.innerConeAngle / lightDef.spot.outerConeAngle;\n\t\t\t\tlightNode.target.position.set( 0, 0, - 1 );\n\t\t\t\tlightNode.add( lightNode.target );\n\t\t\t\tbreak;\n\n\t\t\tdefault:\n\t\t\t\tthrow new Error( 'THREE.GLTFLoader: Unexpected light type: ' + lightDef.type );\n\n\t\t}\n\n\t\t// Some lights (e.g. spot) default to a position other than the origin. Reset the position\n\t\t// here, because node-level parsing will only override position if explicitly specified.\n\t\tlightNode.position.set( 0, 0, 0 );\n\n\t\tassignExtrasToUserData( lightNode, lightDef );\n\n\t\tif ( lightDef.intensity !== undefined ) lightNode.intensity = lightDef.intensity;\n\n\t\tlightNode.name = parser.createUniqueName( lightDef.name || ( 'light_' + lightIndex ) );\n\n\t\tdependency = Promise.resolve( lightNode );\n\n\t\tparser.cache.add( cacheKey, dependency );\n\n\t\treturn dependency;\n\n\t}\n\n\tgetDependency( type, index ) {\n\n\t\tif ( type !== 'light' ) return;\n\n\t\treturn this._loadLight( index );\n\n\t}\n\n\tcreateNodeAttachment( nodeIndex ) {\n\n\t\tconst self = this;\n\t\tconst parser = this.parser;\n\t\tconst json = parser.json;\n\t\tconst nodeDef = json.nodes[ nodeIndex ];\n\t\tconst lightDef = ( nodeDef.extensions && nodeDef.extensions[ this.name ] ) || {};\n\t\tconst lightIndex = lightDef.light;\n\n\t\tif ( lightIndex === undefined ) return null;\n\n\t\treturn this._loadLight( lightIndex ).then( function ( light ) {\n\n\t\t\treturn parser._getNodeRef( self.cache, lightIndex, light );\n\n\t\t} );\n\n\t}\n\n}\n\n/**\n * Unlit Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_unlit\n *\n * @private\n */\nclass GLTFMaterialsUnlitExtension {\n\n\tconstructor() {\n\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_UNLIT;\n\n\t}\n\n\tgetMaterialType() {\n\n\t\treturn MeshBasicMaterial;\n\n\t}\n\n\textendParams( materialParams, materialDef, parser ) {\n\n\t\tconst pending = [];\n\n\t\tmaterialParams.color = new Color( 1.0, 1.0, 1.0 );\n\t\tmaterialParams.opacity = 1.0;\n\n\t\tconst metallicRoughness = materialDef.pbrMetallicRoughness;\n\n\t\tif ( metallicRoughness ) {\n\n\t\t\tif ( Array.isArray( metallicRoughness.baseColorFactor ) ) {\n\n\t\t\t\tconst array = metallicRoughness.baseColorFactor;\n\n\t\t\t\tmaterialParams.color.setRGB( array[ 0 ], array[ 1 ], array[ 2 ], LinearSRGBColorSpace );\n\t\t\t\tmaterialParams.opacity = array[ 3 ];\n\n\t\t\t}\n\n\t\t\tif ( metallicRoughness.baseColorTexture !== undefined ) {\n\n\t\t\t\tpending.push( parser.assignTexture( materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace ) );\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Materials Emissive Strength Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/blob/5768b3ce0ef32bc39cdf1bef10b948586635ead3/extensions/2.0/Khronos/KHR_materials_emissive_strength/README.md\n *\n * @private\n */\nclass GLTFMaterialsEmissiveStrengthExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_EMISSIVE_STRENGTH;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst emissiveStrength = materialDef.extensions[ this.name ].emissiveStrength;\n\n\t\tif ( emissiveStrength !== undefined ) {\n\n\t\t\tmaterialParams.emissiveIntensity = emissiveStrength;\n\n\t\t}\n\n\t\treturn Promise.resolve();\n\n\t}\n\n}\n\n/**\n * Clearcoat Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_clearcoat\n *\n * @private\n */\nclass GLTFMaterialsClearcoatExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_CLEARCOAT;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tif ( extension.clearcoatFactor !== undefined ) {\n\n\t\t\tmaterialParams.clearcoat = extension.clearcoatFactor;\n\n\t\t}\n\n\t\tif ( extension.clearcoatTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'clearcoatMap', extension.clearcoatTexture ) );\n\n\t\t}\n\n\t\tif ( extension.clearcoatRoughnessFactor !== undefined ) {\n\n\t\t\tmaterialParams.clearcoatRoughness = extension.clearcoatRoughnessFactor;\n\n\t\t}\n\n\t\tif ( extension.clearcoatRoughnessTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'clearcoatRoughnessMap', extension.clearcoatRoughnessTexture ) );\n\n\t\t}\n\n\t\tif ( extension.clearcoatNormalTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'clearcoatNormalMap', extension.clearcoatNormalTexture ) );\n\n\t\t\tif ( extension.clearcoatNormalTexture.scale !== undefined ) {\n\n\t\t\t\tconst scale = extension.clearcoatNormalTexture.scale;\n\n\t\t\t\tmaterialParams.clearcoatNormalScale = new Vector2( scale, scale );\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Materials dispersion Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_dispersion\n *\n * @private\n */\nclass GLTFMaterialsDispersionExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_DISPERSION;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tmaterialParams.dispersion = extension.dispersion !== undefined ? extension.dispersion : 0;\n\n\t\treturn Promise.resolve();\n\n\t}\n\n}\n\n/**\n * Iridescence Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_iridescence\n *\n * @private\n */\nclass GLTFMaterialsIridescenceExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_IRIDESCENCE;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tif ( extension.iridescenceFactor !== undefined ) {\n\n\t\t\tmaterialParams.iridescence = extension.iridescenceFactor;\n\n\t\t}\n\n\t\tif ( extension.iridescenceTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'iridescenceMap', extension.iridescenceTexture ) );\n\n\t\t}\n\n\t\tif ( extension.iridescenceIor !== undefined ) {\n\n\t\t\tmaterialParams.iridescenceIOR = extension.iridescenceIor;\n\n\t\t}\n\n\t\tif ( materialParams.iridescenceThicknessRange === undefined ) {\n\n\t\t\tmaterialParams.iridescenceThicknessRange = [ 100, 400 ];\n\n\t\t}\n\n\t\tif ( extension.iridescenceThicknessMinimum !== undefined ) {\n\n\t\t\tmaterialParams.iridescenceThicknessRange[ 0 ] = extension.iridescenceThicknessMinimum;\n\n\t\t}\n\n\t\tif ( extension.iridescenceThicknessMaximum !== undefined ) {\n\n\t\t\tmaterialParams.iridescenceThicknessRange[ 1 ] = extension.iridescenceThicknessMaximum;\n\n\t\t}\n\n\t\tif ( extension.iridescenceThicknessTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'iridescenceThicknessMap', extension.iridescenceThicknessTexture ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Sheen Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/main/extensions/2.0/Khronos/KHR_materials_sheen\n *\n * @private\n */\nclass GLTFMaterialsSheenExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_SHEEN;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tmaterialParams.sheenColor = new Color( 0, 0, 0 );\n\t\tmaterialParams.sheenRoughness = 0;\n\t\tmaterialParams.sheen = 1;\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tif ( extension.sheenColorFactor !== undefined ) {\n\n\t\t\tconst colorFactor = extension.sheenColorFactor;\n\t\t\tmaterialParams.sheenColor.setRGB( colorFactor[ 0 ], colorFactor[ 1 ], colorFactor[ 2 ], LinearSRGBColorSpace );\n\n\t\t}\n\n\t\tif ( extension.sheenRoughnessFactor !== undefined ) {\n\n\t\t\tmaterialParams.sheenRoughness = extension.sheenRoughnessFactor;\n\n\t\t}\n\n\t\tif ( extension.sheenColorTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'sheenColorMap', extension.sheenColorTexture, SRGBColorSpace ) );\n\n\t\t}\n\n\t\tif ( extension.sheenRoughnessTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'sheenRoughnessMap', extension.sheenRoughnessTexture ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Transmission Materials Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_transmission\n * Draft: https://github.com/KhronosGroup/glTF/pull/1698\n *\n * @private\n */\nclass GLTFMaterialsTransmissionExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_TRANSMISSION;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tif ( extension.transmissionFactor !== undefined ) {\n\n\t\t\tmaterialParams.transmission = extension.transmissionFactor;\n\n\t\t}\n\n\t\tif ( extension.transmissionTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'transmissionMap', extension.transmissionTexture ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Materials Volume Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_volume\n *\n * @private\n */\nclass GLTFMaterialsVolumeExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_VOLUME;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tmaterialParams.thickness = extension.thicknessFactor !== undefined ? extension.thicknessFactor : 0;\n\n\t\tif ( extension.thicknessTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'thicknessMap', extension.thicknessTexture ) );\n\n\t\t}\n\n\t\tmaterialParams.attenuationDistance = extension.attenuationDistance || Infinity;\n\n\t\tconst colorArray = extension.attenuationColor || [ 1, 1, 1 ];\n\t\tmaterialParams.attenuationColor = new Color().setRGB( colorArray[ 0 ], colorArray[ 1 ], colorArray[ 2 ], LinearSRGBColorSpace );\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Materials ior Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_ior\n *\n * @private\n */\nclass GLTFMaterialsIorExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_IOR;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tmaterialParams.ior = extension.ior !== undefined ? extension.ior : 1.5;\n\n\t\treturn Promise.resolve();\n\n\t}\n\n}\n\n/**\n * Materials specular Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_specular\n *\n * @private\n */\nclass GLTFMaterialsSpecularExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_SPECULAR;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tmaterialParams.specularIntensity = extension.specularFactor !== undefined ? extension.specularFactor : 1.0;\n\n\t\tif ( extension.specularTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'specularIntensityMap', extension.specularTexture ) );\n\n\t\t}\n\n\t\tconst colorArray = extension.specularColorFactor || [ 1, 1, 1 ];\n\t\tmaterialParams.specularColor = new Color().setRGB( colorArray[ 0 ], colorArray[ 1 ], colorArray[ 2 ], LinearSRGBColorSpace );\n\n\t\tif ( extension.specularColorTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'specularColorMap', extension.specularColorTexture, SRGBColorSpace ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n\n/**\n * Materials bump Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/EXT_materials_bump\n *\n * @private\n */\nclass GLTFMaterialsBumpExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.EXT_MATERIALS_BUMP;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tmaterialParams.bumpScale = extension.bumpFactor !== undefined ? extension.bumpFactor : 1.0;\n\n\t\tif ( extension.bumpTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'bumpMap', extension.bumpTexture ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * Materials anisotropy Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_materials_anisotropy\n *\n * @private\n */\nclass GLTFMaterialsAnisotropyExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_MATERIALS_ANISOTROPY;\n\n\t}\n\n\tgetMaterialType( materialIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) return null;\n\n\t\treturn MeshPhysicalMaterial;\n\n\t}\n\n\textendMaterialParams( materialIndex, materialParams ) {\n\n\t\tconst parser = this.parser;\n\t\tconst materialDef = parser.json.materials[ materialIndex ];\n\n\t\tif ( ! materialDef.extensions || ! materialDef.extensions[ this.name ] ) {\n\n\t\t\treturn Promise.resolve();\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tconst extension = materialDef.extensions[ this.name ];\n\n\t\tif ( extension.anisotropyStrength !== undefined ) {\n\n\t\t\tmaterialParams.anisotropy = extension.anisotropyStrength;\n\n\t\t}\n\n\t\tif ( extension.anisotropyRotation !== undefined ) {\n\n\t\t\tmaterialParams.anisotropyRotation = extension.anisotropyRotation;\n\n\t\t}\n\n\t\tif ( extension.anisotropyTexture !== undefined ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'anisotropyMap', extension.anisotropyTexture ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n}\n\n/**\n * BasisU Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_basisu\n *\n * @private\n */\nclass GLTFTextureBasisUExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.KHR_TEXTURE_BASISU;\n\n\t}\n\n\tloadTexture( textureIndex ) {\n\n\t\tconst parser = this.parser;\n\t\tconst json = parser.json;\n\n\t\tconst textureDef = json.textures[ textureIndex ];\n\n\t\tif ( ! textureDef.extensions || ! textureDef.extensions[ this.name ] ) {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\tconst extension = textureDef.extensions[ this.name ];\n\t\tconst loader = parser.options.ktx2Loader;\n\n\t\tif ( ! loader ) {\n\n\t\t\tif ( json.extensionsRequired && json.extensionsRequired.indexOf( this.name ) >= 0 ) {\n\n\t\t\t\tthrow new Error( 'THREE.GLTFLoader: setKTX2Loader must be called before loading KTX2 textures' );\n\n\t\t\t} else {\n\n\t\t\t\t// Assumes that the extension is optional and that a fallback texture is present\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn parser.loadTextureImage( textureIndex, extension.source, loader );\n\n\t}\n\n}\n\n/**\n * WebP Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_webp\n *\n * @private\n */\nclass GLTFTextureWebPExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.EXT_TEXTURE_WEBP;\n\n\t}\n\n\tloadTexture( textureIndex ) {\n\n\t\tconst name = this.name;\n\t\tconst parser = this.parser;\n\t\tconst json = parser.json;\n\n\t\tconst textureDef = json.textures[ textureIndex ];\n\n\t\tif ( ! textureDef.extensions || ! textureDef.extensions[ name ] ) {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\tconst extension = textureDef.extensions[ name ];\n\t\tconst source = json.images[ extension.source ];\n\n\t\tlet loader = parser.textureLoader;\n\t\tif ( source.uri ) {\n\n\t\t\tconst handler = parser.options.manager.getHandler( source.uri );\n\t\t\tif ( handler !== null ) loader = handler;\n\n\t\t}\n\n\t\treturn parser.loadTextureImage( textureIndex, extension.source, loader );\n\n\t}\n\n}\n\n/**\n * AVIF Texture Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_texture_avif\n *\n * @private\n */\nclass GLTFTextureAVIFExtension {\n\n\tconstructor( parser ) {\n\n\t\tthis.parser = parser;\n\t\tthis.name = EXTENSIONS.EXT_TEXTURE_AVIF;\n\n\t}\n\n\tloadTexture( textureIndex ) {\n\n\t\tconst name = this.name;\n\t\tconst parser = this.parser;\n\t\tconst json = parser.json;\n\n\t\tconst textureDef = json.textures[ textureIndex ];\n\n\t\tif ( ! textureDef.extensions || ! textureDef.extensions[ name ] ) {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\tconst extension = textureDef.extensions[ name ];\n\t\tconst source = json.images[ extension.source ];\n\n\t\tlet loader = parser.textureLoader;\n\t\tif ( source.uri ) {\n\n\t\t\tconst handler = parser.options.manager.getHandler( source.uri );\n\t\t\tif ( handler !== null ) loader = handler;\n\n\t\t}\n\n\t\treturn parser.loadTextureImage( textureIndex, extension.source, loader );\n\n\t}\n\n}\n\n/**\n * meshopt BufferView Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_meshopt_compression\n *\n * @private\n */\nclass GLTFMeshoptCompression {\n\n\tconstructor( parser ) {\n\n\t\tthis.name = EXTENSIONS.EXT_MESHOPT_COMPRESSION;\n\t\tthis.parser = parser;\n\n\t}\n\n\tloadBufferView( index ) {\n\n\t\tconst json = this.parser.json;\n\t\tconst bufferView = json.bufferViews[ index ];\n\n\t\tif ( bufferView.extensions && bufferView.extensions[ this.name ] ) {\n\n\t\t\tconst extensionDef = bufferView.extensions[ this.name ];\n\n\t\t\tconst buffer = this.parser.getDependency( 'buffer', extensionDef.buffer );\n\t\t\tconst decoder = this.parser.options.meshoptDecoder;\n\n\t\t\tif ( ! decoder || ! decoder.supported ) {\n\n\t\t\t\tif ( json.extensionsRequired && json.extensionsRequired.indexOf( this.name ) >= 0 ) {\n\n\t\t\t\t\tthrow new Error( 'THREE.GLTFLoader: setMeshoptDecoder must be called before loading compressed files' );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t// Assumes that the extension is optional and that fallback buffer data is present\n\t\t\t\t\treturn null;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn buffer.then( function ( res ) {\n\n\t\t\t\tconst byteOffset = extensionDef.byteOffset || 0;\n\t\t\t\tconst byteLength = extensionDef.byteLength || 0;\n\n\t\t\t\tconst count = extensionDef.count;\n\t\t\t\tconst stride = extensionDef.byteStride;\n\n\t\t\t\tconst source = new Uint8Array( res, byteOffset, byteLength );\n\n\t\t\t\tif ( decoder.decodeGltfBufferAsync ) {\n\n\t\t\t\t\treturn decoder.decodeGltfBufferAsync( count, stride, source, extensionDef.mode, extensionDef.filter ).then( function ( res ) {\n\n\t\t\t\t\t\treturn res.buffer;\n\n\t\t\t\t\t} );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t// Support for MeshoptDecoder 0.18 or earlier, without decodeGltfBufferAsync\n\t\t\t\t\treturn decoder.ready.then( function () {\n\n\t\t\t\t\t\tconst result = new ArrayBuffer( count * stride );\n\t\t\t\t\t\tdecoder.decodeGltfBuffer( new Uint8Array( result ), count, stride, source, extensionDef.mode, extensionDef.filter );\n\t\t\t\t\t\treturn result;\n\n\t\t\t\t\t} );\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t} else {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t}\n\n}\n\n/**\n * GPU Instancing Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/EXT_mesh_gpu_instancing\n *\n * @private\n */\nclass GLTFMeshGpuInstancing {\n\n\tconstructor( parser ) {\n\n\t\tthis.name = EXTENSIONS.EXT_MESH_GPU_INSTANCING;\n\t\tthis.parser = parser;\n\n\t}\n\n\tcreateNodeMesh( nodeIndex ) {\n\n\t\tconst json = this.parser.json;\n\t\tconst nodeDef = json.nodes[ nodeIndex ];\n\n\t\tif ( ! nodeDef.extensions || ! nodeDef.extensions[ this.name ] ||\n\t\t\tnodeDef.mesh === undefined ) {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\tconst meshDef = json.meshes[ nodeDef.mesh ];\n\n\t\t// No Points or Lines + Instancing support yet\n\n\t\tfor ( const primitive of meshDef.primitives ) {\n\n\t\t\tif ( primitive.mode !== WEBGL_CONSTANTS.TRIANGLES &&\n\t\t\t\t primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_STRIP &&\n\t\t\t\t primitive.mode !== WEBGL_CONSTANTS.TRIANGLE_FAN &&\n\t\t\t\t primitive.mode !== undefined ) {\n\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t}\n\n\t\tconst extensionDef = nodeDef.extensions[ this.name ];\n\t\tconst attributesDef = extensionDef.attributes;\n\n\t\t// @TODO: Can we support InstancedMesh + SkinnedMesh?\n\n\t\tconst pending = [];\n\t\tconst attributes = {};\n\n\t\tfor ( const key in attributesDef ) {\n\n\t\t\tpending.push( this.parser.getDependency( 'accessor', attributesDef[ key ] ).then( accessor => {\n\n\t\t\t\tattributes[ key ] = accessor;\n\t\t\t\treturn attributes[ key ];\n\n\t\t\t} ) );\n\n\t\t}\n\n\t\tif ( pending.length < 1 ) {\n\n\t\t\treturn null;\n\n\t\t}\n\n\t\tpending.push( this.parser.createNodeMesh( nodeIndex ) );\n\n\t\treturn Promise.all( pending ).then( results => {\n\n\t\t\tconst nodeObject = results.pop();\n\t\t\tconst meshes = nodeObject.isGroup ? nodeObject.children : [ nodeObject ];\n\t\t\tconst count = results[ 0 ].count; // All attribute counts should be same\n\t\t\tconst instancedMeshes = [];\n\n\t\t\tfor ( const mesh of meshes ) {\n\n\t\t\t\t// Temporal variables\n\t\t\t\tconst m = new Matrix4();\n\t\t\t\tconst p = new Vector3();\n\t\t\t\tconst q = new Quaternion();\n\t\t\t\tconst s = new Vector3( 1, 1, 1 );\n\n\t\t\t\tconst instancedMesh = new InstancedMesh( mesh.geometry, mesh.material, count );\n\n\t\t\t\tfor ( let i = 0; i < count; i ++ ) {\n\n\t\t\t\t\tif ( attributes.TRANSLATION ) {\n\n\t\t\t\t\t\tp.fromBufferAttribute( attributes.TRANSLATION, i );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( attributes.ROTATION ) {\n\n\t\t\t\t\t\tq.fromBufferAttribute( attributes.ROTATION, i );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( attributes.SCALE ) {\n\n\t\t\t\t\t\ts.fromBufferAttribute( attributes.SCALE, i );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tinstancedMesh.setMatrixAt( i, m.compose( p, q, s ) );\n\n\t\t\t\t}\n\n\t\t\t\t// Add instance attributes to the geometry, excluding TRS.\n\t\t\t\tfor ( const attributeName in attributes ) {\n\n\t\t\t\t\tif ( attributeName === '_COLOR_0' ) {\n\n\t\t\t\t\t\tconst attr = attributes[ attributeName ];\n\t\t\t\t\t\tinstancedMesh.instanceColor = new InstancedBufferAttribute( attr.array, attr.itemSize, attr.normalized );\n\n\t\t\t\t\t} else if ( attributeName !== 'TRANSLATION' &&\n\t\t\t\t\t\t attributeName !== 'ROTATION' &&\n\t\t\t\t\t\t attributeName !== 'SCALE' ) {\n\n\t\t\t\t\t\tmesh.geometry.setAttribute( attributeName, attributes[ attributeName ] );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// Just in case\n\t\t\t\tObject3D.prototype.copy.call( instancedMesh, mesh );\n\n\t\t\t\tthis.parser.assignFinalMaterial( instancedMesh );\n\n\t\t\t\tinstancedMeshes.push( instancedMesh );\n\n\t\t\t}\n\n\t\t\tif ( nodeObject.isGroup ) {\n\n\t\t\t\tnodeObject.clear();\n\n\t\t\t\tnodeObject.add( ... instancedMeshes );\n\n\t\t\t\treturn nodeObject;\n\n\t\t\t}\n\n\t\t\treturn instancedMeshes[ 0 ];\n\n\t\t} );\n\n\t}\n\n}\n\n/* BINARY EXTENSION */\nconst BINARY_EXTENSION_HEADER_MAGIC = 'glTF';\nconst BINARY_EXTENSION_HEADER_LENGTH = 12;\nconst BINARY_EXTENSION_CHUNK_TYPES = { JSON: 0x4E4F534A, BIN: 0x004E4942 };\n\nclass GLTFBinaryExtension {\n\n\tconstructor( data ) {\n\n\t\tthis.name = EXTENSIONS.KHR_BINARY_GLTF;\n\t\tthis.content = null;\n\t\tthis.body = null;\n\n\t\tconst headerView = new DataView( data, 0, BINARY_EXTENSION_HEADER_LENGTH );\n\t\tconst textDecoder = new TextDecoder();\n\n\t\tthis.header = {\n\t\t\tmagic: textDecoder.decode( new Uint8Array( data.slice( 0, 4 ) ) ),\n\t\t\tversion: headerView.getUint32( 4, true ),\n\t\t\tlength: headerView.getUint32( 8, true )\n\t\t};\n\n\t\tif ( this.header.magic !== BINARY_EXTENSION_HEADER_MAGIC ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: Unsupported glTF-Binary header.' );\n\n\t\t} else if ( this.header.version < 2.0 ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: Legacy binary file detected.' );\n\n\t\t}\n\n\t\tconst chunkContentsLength = this.header.length - BINARY_EXTENSION_HEADER_LENGTH;\n\t\tconst chunkView = new DataView( data, BINARY_EXTENSION_HEADER_LENGTH );\n\t\tlet chunkIndex = 0;\n\n\t\twhile ( chunkIndex < chunkContentsLength ) {\n\n\t\t\tconst chunkLength = chunkView.getUint32( chunkIndex, true );\n\t\t\tchunkIndex += 4;\n\n\t\t\tconst chunkType = chunkView.getUint32( chunkIndex, true );\n\t\t\tchunkIndex += 4;\n\n\t\t\tif ( chunkType === BINARY_EXTENSION_CHUNK_TYPES.JSON ) {\n\n\t\t\t\tconst contentArray = new Uint8Array( data, BINARY_EXTENSION_HEADER_LENGTH + chunkIndex, chunkLength );\n\t\t\t\tthis.content = textDecoder.decode( contentArray );\n\n\t\t\t} else if ( chunkType === BINARY_EXTENSION_CHUNK_TYPES.BIN ) {\n\n\t\t\t\tconst byteOffset = BINARY_EXTENSION_HEADER_LENGTH + chunkIndex;\n\t\t\t\tthis.body = data.slice( byteOffset, byteOffset + chunkLength );\n\n\t\t\t}\n\n\t\t\t// Clients must ignore chunks with unknown types.\n\n\t\t\tchunkIndex += chunkLength;\n\n\t\t}\n\n\t\tif ( this.content === null ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: JSON content not found.' );\n\n\t\t}\n\n\t}\n\n}\n\n/**\n * DRACO Mesh Compression Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_draco_mesh_compression\n *\n * @private\n */\nclass GLTFDracoMeshCompressionExtension {\n\n\tconstructor( json, dracoLoader ) {\n\n\t\tif ( ! dracoLoader ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: No DRACOLoader instance provided.' );\n\n\t\t}\n\n\t\tthis.name = EXTENSIONS.KHR_DRACO_MESH_COMPRESSION;\n\t\tthis.json = json;\n\t\tthis.dracoLoader = dracoLoader;\n\t\tthis.dracoLoader.preload();\n\n\t}\n\n\tdecodePrimitive( primitive, parser ) {\n\n\t\tconst json = this.json;\n\t\tconst dracoLoader = this.dracoLoader;\n\t\tconst bufferViewIndex = primitive.extensions[ this.name ].bufferView;\n\t\tconst gltfAttributeMap = primitive.extensions[ this.name ].attributes;\n\t\tconst threeAttributeMap = {};\n\t\tconst attributeNormalizedMap = {};\n\t\tconst attributeTypeMap = {};\n\n\t\tfor ( const attributeName in gltfAttributeMap ) {\n\n\t\t\tconst threeAttributeName = ATTRIBUTES[ attributeName ] || attributeName.toLowerCase();\n\n\t\t\tthreeAttributeMap[ threeAttributeName ] = gltfAttributeMap[ attributeName ];\n\n\t\t}\n\n\t\tfor ( const attributeName in primitive.attributes ) {\n\n\t\t\tconst threeAttributeName = ATTRIBUTES[ attributeName ] || attributeName.toLowerCase();\n\n\t\t\tif ( gltfAttributeMap[ attributeName ] !== undefined ) {\n\n\t\t\t\tconst accessorDef = json.accessors[ primitive.attributes[ attributeName ] ];\n\t\t\t\tconst componentType = WEBGL_COMPONENT_TYPES[ accessorDef.componentType ];\n\n\t\t\t\tattributeTypeMap[ threeAttributeName ] = componentType.name;\n\t\t\t\tattributeNormalizedMap[ threeAttributeName ] = accessorDef.normalized === true;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn parser.getDependency( 'bufferView', bufferViewIndex ).then( function ( bufferView ) {\n\n\t\t\treturn new Promise( function ( resolve, reject ) {\n\n\t\t\t\tdracoLoader.decodeDracoFile( bufferView, function ( geometry ) {\n\n\t\t\t\t\tfor ( const attributeName in geometry.attributes ) {\n\n\t\t\t\t\t\tconst attribute = geometry.attributes[ attributeName ];\n\t\t\t\t\t\tconst normalized = attributeNormalizedMap[ attributeName ];\n\n\t\t\t\t\t\tif ( normalized !== undefined ) attribute.normalized = normalized;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tresolve( geometry );\n\n\t\t\t\t}, threeAttributeMap, attributeTypeMap, LinearSRGBColorSpace, reject );\n\n\t\t\t} );\n\n\t\t} );\n\n\t}\n\n}\n\n/**\n * Texture Transform Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_texture_transform\n *\n * @private\n */\nclass GLTFTextureTransformExtension {\n\n\tconstructor() {\n\n\t\tthis.name = EXTENSIONS.KHR_TEXTURE_TRANSFORM;\n\n\t}\n\n\textendTexture( texture, transform ) {\n\n\t\tif ( ( transform.texCoord === undefined || transform.texCoord === texture.channel )\n\t\t\t&& transform.offset === undefined\n\t\t\t&& transform.rotation === undefined\n\t\t\t&& transform.scale === undefined ) {\n\n\t\t\t// See https://github.com/mrdoob/three.js/issues/21819.\n\t\t\treturn texture;\n\n\t\t}\n\n\t\ttexture = texture.clone();\n\n\t\tif ( transform.texCoord !== undefined ) {\n\n\t\t\ttexture.channel = transform.texCoord;\n\n\t\t}\n\n\t\tif ( transform.offset !== undefined ) {\n\n\t\t\ttexture.offset.fromArray( transform.offset );\n\n\t\t}\n\n\t\tif ( transform.rotation !== undefined ) {\n\n\t\t\ttexture.rotation = transform.rotation;\n\n\t\t}\n\n\t\tif ( transform.scale !== undefined ) {\n\n\t\t\ttexture.repeat.fromArray( transform.scale );\n\n\t\t}\n\n\t\ttexture.needsUpdate = true;\n\n\t\treturn texture;\n\n\t}\n\n}\n\n/**\n * Mesh Quantization Extension\n *\n * Specification: https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization\n *\n * @private\n */\nclass GLTFMeshQuantizationExtension {\n\n\tconstructor() {\n\n\t\tthis.name = EXTENSIONS.KHR_MESH_QUANTIZATION;\n\n\t}\n\n}\n\n/*********************************/\n/********** INTERPOLATION ********/\n/*********************************/\n\n// Spline Interpolation\n// Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#appendix-c-spline-interpolation\nclass GLTFCubicSplineInterpolant extends Interpolant {\n\n\tconstructor( parameterPositions, sampleValues, sampleSize, resultBuffer ) {\n\n\t\tsuper( parameterPositions, sampleValues, sampleSize, resultBuffer );\n\n\t}\n\n\tcopySampleValue_( index ) {\n\n\t\t// Copies a sample value to the result buffer. See description of glTF\n\t\t// CUBICSPLINE values layout in interpolate_() function below.\n\n\t\tconst result = this.resultBuffer,\n\t\t\tvalues = this.sampleValues,\n\t\t\tvalueSize = this.valueSize,\n\t\t\toffset = index * valueSize * 3 + valueSize;\n\n\t\tfor ( let i = 0; i !== valueSize; i ++ ) {\n\n\t\t\tresult[ i ] = values[ offset + i ];\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n\tinterpolate_( i1, t0, t, t1 ) {\n\n\t\tconst result = this.resultBuffer;\n\t\tconst values = this.sampleValues;\n\t\tconst stride = this.valueSize;\n\n\t\tconst stride2 = stride * 2;\n\t\tconst stride3 = stride * 3;\n\n\t\tconst td = t1 - t0;\n\n\t\tconst p = ( t - t0 ) / td;\n\t\tconst pp = p * p;\n\t\tconst ppp = pp * p;\n\n\t\tconst offset1 = i1 * stride3;\n\t\tconst offset0 = offset1 - stride3;\n\n\t\tconst s2 = - 2 * ppp + 3 * pp;\n\t\tconst s3 = ppp - pp;\n\t\tconst s0 = 1 - s2;\n\t\tconst s1 = s3 - pp + p;\n\n\t\t// Layout of keyframe output values for CUBICSPLINE animations:\n\t\t//   [ inTangent_1, splineVertex_1, outTangent_1, inTangent_2, splineVertex_2, ... ]\n\t\tfor ( let i = 0; i !== stride; i ++ ) {\n\n\t\t\tconst p0 = values[ offset0 + i + stride ]; // splineVertex_k\n\t\t\tconst m0 = values[ offset0 + i + stride2 ] * td; // outTangent_k * (t_k+1 - t_k)\n\t\t\tconst p1 = values[ offset1 + i + stride ]; // splineVertex_k+1\n\t\t\tconst m1 = values[ offset1 + i ] * td; // inTangent_k+1 * (t_k+1 - t_k)\n\n\t\t\tresult[ i ] = s0 * p0 + s1 * m0 + s2 * p1 + s3 * m1;\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n}\n\nconst _quaternion = new Quaternion();\n\nclass GLTFCubicSplineQuaternionInterpolant extends GLTFCubicSplineInterpolant {\n\n\tinterpolate_( i1, t0, t, t1 ) {\n\n\t\tconst result = super.interpolate_( i1, t0, t, t1 );\n\n\t\t_quaternion.fromArray( result ).normalize().toArray( result );\n\n\t\treturn result;\n\n\t}\n\n}\n\n\n/*********************************/\n/********** INTERNALS ************/\n/*********************************/\n\n/* CONSTANTS */\n\nconst WEBGL_CONSTANTS = {\n\tFLOAT: 5126,\n\t//FLOAT_MAT2: 35674,\n\tFLOAT_MAT3: 35675,\n\tFLOAT_MAT4: 35676,\n\tFLOAT_VEC2: 35664,\n\tFLOAT_VEC3: 35665,\n\tFLOAT_VEC4: 35666,\n\tLINEAR: 9729,\n\tREPEAT: 10497,\n\tSAMPLER_2D: 35678,\n\tPOINTS: 0,\n\tLINES: 1,\n\tLINE_LOOP: 2,\n\tLINE_STRIP: 3,\n\tTRIANGLES: 4,\n\tTRIANGLE_STRIP: 5,\n\tTRIANGLE_FAN: 6,\n\tUNSIGNED_BYTE: 5121,\n\tUNSIGNED_SHORT: 5123\n};\n\nconst WEBGL_COMPONENT_TYPES = {\n\t5120: Int8Array,\n\t5121: Uint8Array,\n\t5122: Int16Array,\n\t5123: Uint16Array,\n\t5125: Uint32Array,\n\t5126: Float32Array\n};\n\nconst WEBGL_FILTERS = {\n\t9728: NearestFilter,\n\t9729: LinearFilter,\n\t9984: NearestMipmapNearestFilter,\n\t9985: LinearMipmapNearestFilter,\n\t9986: NearestMipmapLinearFilter,\n\t9987: LinearMipmapLinearFilter\n};\n\nconst WEBGL_WRAPPINGS = {\n\t33071: ClampToEdgeWrapping,\n\t33648: MirroredRepeatWrapping,\n\t10497: RepeatWrapping\n};\n\nconst WEBGL_TYPE_SIZES = {\n\t'SCALAR': 1,\n\t'VEC2': 2,\n\t'VEC3': 3,\n\t'VEC4': 4,\n\t'MAT2': 4,\n\t'MAT3': 9,\n\t'MAT4': 16\n};\n\nconst ATTRIBUTES = {\n\tPOSITION: 'position',\n\tNORMAL: 'normal',\n\tTANGENT: 'tangent',\n\tTEXCOORD_0: 'uv',\n\tTEXCOORD_1: 'uv1',\n\tTEXCOORD_2: 'uv2',\n\tTEXCOORD_3: 'uv3',\n\tCOLOR_0: 'color',\n\tWEIGHTS_0: 'skinWeight',\n\tJOINTS_0: 'skinIndex',\n};\n\nconst PATH_PROPERTIES = {\n\tscale: 'scale',\n\ttranslation: 'position',\n\trotation: 'quaternion',\n\tweights: 'morphTargetInfluences'\n};\n\nconst INTERPOLATION = {\n\tCUBICSPLINE: undefined, // We use a custom interpolant (GLTFCubicSplineInterpolation) for CUBICSPLINE tracks. Each\n\t\t                        // keyframe track will be initialized with a default interpolation type, then modified.\n\tLINEAR: InterpolateLinear,\n\tSTEP: InterpolateDiscrete\n};\n\nconst ALPHA_MODES = {\n\tOPAQUE: 'OPAQUE',\n\tMASK: 'MASK',\n\tBLEND: 'BLEND'\n};\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#default-material\n *\n * @private\n * @param {Object<string, Material>} cache\n * @return {Material}\n */\nfunction createDefaultMaterial( cache ) {\n\n\tif ( cache[ 'DefaultMaterial' ] === undefined ) {\n\n\t\tcache[ 'DefaultMaterial' ] = new MeshStandardMaterial( {\n\t\t\tcolor: 0xFFFFFF,\n\t\t\temissive: 0x000000,\n\t\t\tmetalness: 1,\n\t\t\troughness: 1,\n\t\t\ttransparent: false,\n\t\t\tdepthTest: true,\n\t\t\tside: FrontSide\n\t\t} );\n\n\t}\n\n\treturn cache[ 'DefaultMaterial' ];\n\n}\n\nfunction addUnknownExtensionsToUserData( knownExtensions, object, objectDef ) {\n\n\t// Add unknown glTF extensions to an object's userData.\n\n\tfor ( const name in objectDef.extensions ) {\n\n\t\tif ( knownExtensions[ name ] === undefined ) {\n\n\t\t\tobject.userData.gltfExtensions = object.userData.gltfExtensions || {};\n\t\t\tobject.userData.gltfExtensions[ name ] = objectDef.extensions[ name ];\n\n\t\t}\n\n\t}\n\n}\n\n/**\n *\n * @private\n * @param {Object3D|Material|BufferGeometry|Object} object\n * @param {GLTF.definition} gltfDef\n */\nfunction assignExtrasToUserData( object, gltfDef ) {\n\n\tif ( gltfDef.extras !== undefined ) {\n\n\t\tif ( typeof gltfDef.extras === 'object' ) {\n\n\t\t\tObject.assign( object.userData, gltfDef.extras );\n\n\t\t} else {\n\n\t\t\tconsole.warn( 'THREE.GLTFLoader: Ignoring primitive type .extras, ' + gltfDef.extras );\n\n\t\t}\n\n\t}\n\n}\n\n/**\n * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#morph-targets\n *\n * @private\n * @param {BufferGeometry} geometry\n * @param {Array<GLTF.Target>} targets\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addMorphTargets( geometry, targets, parser ) {\n\n\tlet hasMorphPosition = false;\n\tlet hasMorphNormal = false;\n\tlet hasMorphColor = false;\n\n\tfor ( let i = 0, il = targets.length; i < il; i ++ ) {\n\n\t\tconst target = targets[ i ];\n\n\t\tif ( target.POSITION !== undefined ) hasMorphPosition = true;\n\t\tif ( target.NORMAL !== undefined ) hasMorphNormal = true;\n\t\tif ( target.COLOR_0 !== undefined ) hasMorphColor = true;\n\n\t\tif ( hasMorphPosition && hasMorphNormal && hasMorphColor ) break;\n\n\t}\n\n\tif ( ! hasMorphPosition && ! hasMorphNormal && ! hasMorphColor ) return Promise.resolve( geometry );\n\n\tconst pendingPositionAccessors = [];\n\tconst pendingNormalAccessors = [];\n\tconst pendingColorAccessors = [];\n\n\tfor ( let i = 0, il = targets.length; i < il; i ++ ) {\n\n\t\tconst target = targets[ i ];\n\n\t\tif ( hasMorphPosition ) {\n\n\t\t\tconst pendingAccessor = target.POSITION !== undefined\n\t\t\t\t? parser.getDependency( 'accessor', target.POSITION )\n\t\t\t\t: geometry.attributes.position;\n\n\t\t\tpendingPositionAccessors.push( pendingAccessor );\n\n\t\t}\n\n\t\tif ( hasMorphNormal ) {\n\n\t\t\tconst pendingAccessor = target.NORMAL !== undefined\n\t\t\t\t? parser.getDependency( 'accessor', target.NORMAL )\n\t\t\t\t: geometry.attributes.normal;\n\n\t\t\tpendingNormalAccessors.push( pendingAccessor );\n\n\t\t}\n\n\t\tif ( hasMorphColor ) {\n\n\t\t\tconst pendingAccessor = target.COLOR_0 !== undefined\n\t\t\t\t? parser.getDependency( 'accessor', target.COLOR_0 )\n\t\t\t\t: geometry.attributes.color;\n\n\t\t\tpendingColorAccessors.push( pendingAccessor );\n\n\t\t}\n\n\t}\n\n\treturn Promise.all( [\n\t\tPromise.all( pendingPositionAccessors ),\n\t\tPromise.all( pendingNormalAccessors ),\n\t\tPromise.all( pendingColorAccessors )\n\t] ).then( function ( accessors ) {\n\n\t\tconst morphPositions = accessors[ 0 ];\n\t\tconst morphNormals = accessors[ 1 ];\n\t\tconst morphColors = accessors[ 2 ];\n\n\t\tif ( hasMorphPosition ) geometry.morphAttributes.position = morphPositions;\n\t\tif ( hasMorphNormal ) geometry.morphAttributes.normal = morphNormals;\n\t\tif ( hasMorphColor ) geometry.morphAttributes.color = morphColors;\n\t\tgeometry.morphTargetsRelative = true;\n\n\t\treturn geometry;\n\n\t} );\n\n}\n\n/**\n *\n * @private\n * @param {Mesh} mesh\n * @param {GLTF.Mesh} meshDef\n */\nfunction updateMorphTargets( mesh, meshDef ) {\n\n\tmesh.updateMorphTargets();\n\n\tif ( meshDef.weights !== undefined ) {\n\n\t\tfor ( let i = 0, il = meshDef.weights.length; i < il; i ++ ) {\n\n\t\t\tmesh.morphTargetInfluences[ i ] = meshDef.weights[ i ];\n\n\t\t}\n\n\t}\n\n\t// .extras has user-defined data, so check that .extras.targetNames is an array.\n\tif ( meshDef.extras && Array.isArray( meshDef.extras.targetNames ) ) {\n\n\t\tconst targetNames = meshDef.extras.targetNames;\n\n\t\tif ( mesh.morphTargetInfluences.length === targetNames.length ) {\n\n\t\t\tmesh.morphTargetDictionary = {};\n\n\t\t\tfor ( let i = 0, il = targetNames.length; i < il; i ++ ) {\n\n\t\t\t\tmesh.morphTargetDictionary[ targetNames[ i ] ] = i;\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconsole.warn( 'THREE.GLTFLoader: Invalid extras.targetNames length. Ignoring names.' );\n\n\t\t}\n\n\t}\n\n}\n\nfunction createPrimitiveKey( primitiveDef ) {\n\n\tlet geometryKey;\n\n\tconst dracoExtension = primitiveDef.extensions && primitiveDef.extensions[ EXTENSIONS.KHR_DRACO_MESH_COMPRESSION ];\n\n\tif ( dracoExtension ) {\n\n\t\tgeometryKey = 'draco:' + dracoExtension.bufferView\n\t\t\t\t+ ':' + dracoExtension.indices\n\t\t\t\t+ ':' + createAttributesKey( dracoExtension.attributes );\n\n\t} else {\n\n\t\tgeometryKey = primitiveDef.indices + ':' + createAttributesKey( primitiveDef.attributes ) + ':' + primitiveDef.mode;\n\n\t}\n\n\tif ( primitiveDef.targets !== undefined ) {\n\n\t\tfor ( let i = 0, il = primitiveDef.targets.length; i < il; i ++ ) {\n\n\t\t\tgeometryKey += ':' + createAttributesKey( primitiveDef.targets[ i ] );\n\n\t\t}\n\n\t}\n\n\treturn geometryKey;\n\n}\n\nfunction createAttributesKey( attributes ) {\n\n\tlet attributesKey = '';\n\n\tconst keys = Object.keys( attributes ).sort();\n\n\tfor ( let i = 0, il = keys.length; i < il; i ++ ) {\n\n\t\tattributesKey += keys[ i ] + ':' + attributes[ keys[ i ] ] + ';';\n\n\t}\n\n\treturn attributesKey;\n\n}\n\nfunction getNormalizedComponentScale( constructor ) {\n\n\t// Reference:\n\t// https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Khronos/KHR_mesh_quantization#encoding-quantized-data\n\n\tswitch ( constructor ) {\n\n\t\tcase Int8Array:\n\t\t\treturn 1 / 127;\n\n\t\tcase Uint8Array:\n\t\t\treturn 1 / 255;\n\n\t\tcase Int16Array:\n\t\t\treturn 1 / 32767;\n\n\t\tcase Uint16Array:\n\t\t\treturn 1 / 65535;\n\n\t\tdefault:\n\t\t\tthrow new Error( 'THREE.GLTFLoader: Unsupported normalized accessor component type.' );\n\n\t}\n\n}\n\nfunction getImageURIMimeType( uri ) {\n\n\tif ( uri.search( /\\.jpe?g($|\\?)/i ) > 0 || uri.search( /^data\\:image\\/jpeg/ ) === 0 ) return 'image/jpeg';\n\tif ( uri.search( /\\.webp($|\\?)/i ) > 0 || uri.search( /^data\\:image\\/webp/ ) === 0 ) return 'image/webp';\n\tif ( uri.search( /\\.ktx2($|\\?)/i ) > 0 || uri.search( /^data\\:image\\/ktx2/ ) === 0 ) return 'image/ktx2';\n\n\treturn 'image/png';\n\n}\n\nconst _identityMatrix = new Matrix4();\n\n/* GLTF PARSER */\n\nclass GLTFParser {\n\n\tconstructor( json = {}, options = {} ) {\n\n\t\tthis.json = json;\n\t\tthis.extensions = {};\n\t\tthis.plugins = {};\n\t\tthis.options = options;\n\n\t\t// loader object cache\n\t\tthis.cache = new GLTFRegistry();\n\n\t\t// associations between Three.js objects and glTF elements\n\t\tthis.associations = new Map();\n\n\t\t// BufferGeometry caching\n\t\tthis.primitiveCache = {};\n\n\t\t// Node cache\n\t\tthis.nodeCache = {};\n\n\t\t// Object3D instance caches\n\t\tthis.meshCache = { refs: {}, uses: {} };\n\t\tthis.cameraCache = { refs: {}, uses: {} };\n\t\tthis.lightCache = { refs: {}, uses: {} };\n\n\t\tthis.sourceCache = {};\n\t\tthis.textureCache = {};\n\n\t\t// Track node names, to ensure no duplicates\n\t\tthis.nodeNamesUsed = {};\n\n\t\t// Use an ImageBitmapLoader if imageBitmaps are supported. Moves much of the\n\t\t// expensive work of uploading a texture to the GPU off the main thread.\n\n\t\tlet isSafari = false;\n\t\tlet safariVersion = - 1;\n\t\tlet isFirefox = false;\n\t\tlet firefoxVersion = - 1;\n\n\t\tif ( typeof navigator !== 'undefined' ) {\n\n\t\t\tconst userAgent = navigator.userAgent;\n\n\t\t\tisSafari = /^((?!chrome|android).)*safari/i.test( userAgent ) === true;\n\t\t\tconst safariMatch = userAgent.match( /Version\\/(\\d+)/ );\n\t\t\tsafariVersion = isSafari && safariMatch ? parseInt( safariMatch[ 1 ], 10 ) : - 1;\n\n\t\t\tisFirefox = userAgent.indexOf( 'Firefox' ) > - 1;\n\t\t\tfirefoxVersion = isFirefox ? userAgent.match( /Firefox\\/([0-9]+)\\./ )[ 1 ] : - 1;\n\n\t\t}\n\n\t\tif ( typeof createImageBitmap === 'undefined' || ( isSafari && safariVersion < 17 ) || ( isFirefox && firefoxVersion < 98 ) ) {\n\n\t\t\tthis.textureLoader = new TextureLoader( this.options.manager );\n\n\t\t} else {\n\n\t\t\tthis.textureLoader = new ImageBitmapLoader( this.options.manager );\n\n\t\t}\n\n\t\tthis.textureLoader.setCrossOrigin( this.options.crossOrigin );\n\t\tthis.textureLoader.setRequestHeader( this.options.requestHeader );\n\n\t\tthis.fileLoader = new FileLoader( this.options.manager );\n\t\tthis.fileLoader.setResponseType( 'arraybuffer' );\n\n\t\tif ( this.options.crossOrigin === 'use-credentials' ) {\n\n\t\t\tthis.fileLoader.setWithCredentials( true );\n\n\t\t}\n\n\t}\n\n\tsetExtensions( extensions ) {\n\n\t\tthis.extensions = extensions;\n\n\t}\n\n\tsetPlugins( plugins ) {\n\n\t\tthis.plugins = plugins;\n\n\t}\n\n\tparse( onLoad, onError ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\t\tconst extensions = this.extensions;\n\n\t\t// Clear the loader cache\n\t\tthis.cache.removeAll();\n\t\tthis.nodeCache = {};\n\n\t\t// Mark the special nodes/meshes in json for efficient parse\n\t\tthis._invokeAll( function ( ext ) {\n\n\t\t\treturn ext._markDefs && ext._markDefs();\n\n\t\t} );\n\n\t\tPromise.all( this._invokeAll( function ( ext ) {\n\n\t\t\treturn ext.beforeRoot && ext.beforeRoot();\n\n\t\t} ) ).then( function () {\n\n\t\t\treturn Promise.all( [\n\n\t\t\t\tparser.getDependencies( 'scene' ),\n\t\t\t\tparser.getDependencies( 'animation' ),\n\t\t\t\tparser.getDependencies( 'camera' ),\n\n\t\t\t] );\n\n\t\t} ).then( function ( dependencies ) {\n\n\t\t\tconst result = {\n\t\t\t\tscene: dependencies[ 0 ][ json.scene || 0 ],\n\t\t\t\tscenes: dependencies[ 0 ],\n\t\t\t\tanimations: dependencies[ 1 ],\n\t\t\t\tcameras: dependencies[ 2 ],\n\t\t\t\tasset: json.asset,\n\t\t\t\tparser: parser,\n\t\t\t\tuserData: {}\n\t\t\t};\n\n\t\t\taddUnknownExtensionsToUserData( extensions, result, json );\n\n\t\t\tassignExtrasToUserData( result, json );\n\n\t\t\treturn Promise.all( parser._invokeAll( function ( ext ) {\n\n\t\t\t\treturn ext.afterRoot && ext.afterRoot( result );\n\n\t\t\t} ) ).then( function () {\n\n\t\t\t\tfor ( const scene of result.scenes ) {\n\n\t\t\t\t\tscene.updateMatrixWorld();\n\n\t\t\t\t}\n\n\t\t\t\tonLoad( result );\n\n\t\t\t} );\n\n\t\t} ).catch( onError );\n\n\t}\n\n\t/**\n\t * Marks the special nodes/meshes in json for efficient parse.\n\t *\n\t * @private\n\t */\n\t_markDefs() {\n\n\t\tconst nodeDefs = this.json.nodes || [];\n\t\tconst skinDefs = this.json.skins || [];\n\t\tconst meshDefs = this.json.meshes || [];\n\n\t\t// Nothing in the node definition indicates whether it is a Bone or an\n\t\t// Object3D. Use the skins' joint references to mark bones.\n\t\tfor ( let skinIndex = 0, skinLength = skinDefs.length; skinIndex < skinLength; skinIndex ++ ) {\n\n\t\t\tconst joints = skinDefs[ skinIndex ].joints;\n\n\t\t\tfor ( let i = 0, il = joints.length; i < il; i ++ ) {\n\n\t\t\t\tnodeDefs[ joints[ i ] ].isBone = true;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// Iterate over all nodes, marking references to shared resources,\n\t\t// as well as skeleton joints.\n\t\tfor ( let nodeIndex = 0, nodeLength = nodeDefs.length; nodeIndex < nodeLength; nodeIndex ++ ) {\n\n\t\t\tconst nodeDef = nodeDefs[ nodeIndex ];\n\n\t\t\tif ( nodeDef.mesh !== undefined ) {\n\n\t\t\t\tthis._addNodeRef( this.meshCache, nodeDef.mesh );\n\n\t\t\t\t// Nothing in the mesh definition indicates whether it is\n\t\t\t\t// a SkinnedMesh or Mesh. Use the node's mesh reference\n\t\t\t\t// to mark SkinnedMesh if node has skin.\n\t\t\t\tif ( nodeDef.skin !== undefined ) {\n\n\t\t\t\t\tmeshDefs[ nodeDef.mesh ].isSkinnedMesh = true;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( nodeDef.camera !== undefined ) {\n\n\t\t\t\tthis._addNodeRef( this.cameraCache, nodeDef.camera );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Counts references to shared node / Object3D resources. These resources\n\t * can be reused, or \"instantiated\", at multiple nodes in the scene\n\t * hierarchy. Mesh, Camera, and Light instances are instantiated and must\n\t * be marked. Non-scenegraph resources (like Materials, Geometries, and\n\t * Textures) can be reused directly and are not marked here.\n\t *\n\t * Example: CesiumMilkTruck sample model reuses \"Wheel\" meshes.\n\t *\n\t * @private\n\t * @param {Object} cache\n\t * @param {Object3D} index\n\t */\n\t_addNodeRef( cache, index ) {\n\n\t\tif ( index === undefined ) return;\n\n\t\tif ( cache.refs[ index ] === undefined ) {\n\n\t\t\tcache.refs[ index ] = cache.uses[ index ] = 0;\n\n\t\t}\n\n\t\tcache.refs[ index ] ++;\n\n\t}\n\n\t/**\n\t * Returns a reference to a shared resource, cloning it if necessary.\n\t *\n\t * @private\n\t * @param {Object} cache\n\t * @param {number} index\n\t * @param {Object} object\n\t * @return {Object}\n\t */\n\t_getNodeRef( cache, index, object ) {\n\n\t\tif ( cache.refs[ index ] <= 1 ) return object;\n\n\t\tconst ref = object.clone();\n\n\t\t// Propagates mappings to the cloned object, prevents mappings on the\n\t\t// original object from being lost.\n\t\tconst updateMappings = ( original, clone ) => {\n\n\t\t\tconst mappings = this.associations.get( original );\n\t\t\tif ( mappings != null ) {\n\n\t\t\t\tthis.associations.set( clone, mappings );\n\n\t\t\t}\n\n\t\t\tfor ( const [ i, child ] of original.children.entries() ) {\n\n\t\t\t\tupdateMappings( child, clone.children[ i ] );\n\n\t\t\t}\n\n\t\t};\n\n\t\tupdateMappings( object, ref );\n\n\t\tref.name += '_instance_' + ( cache.uses[ index ] ++ );\n\n\t\treturn ref;\n\n\t}\n\n\t_invokeOne( func ) {\n\n\t\tconst extensions = Object.values( this.plugins );\n\t\textensions.push( this );\n\n\t\tfor ( let i = 0; i < extensions.length; i ++ ) {\n\n\t\t\tconst result = func( extensions[ i ] );\n\n\t\t\tif ( result ) return result;\n\n\t\t}\n\n\t\treturn null;\n\n\t}\n\n\t_invokeAll( func ) {\n\n\t\tconst extensions = Object.values( this.plugins );\n\t\textensions.unshift( this );\n\n\t\tconst pending = [];\n\n\t\tfor ( let i = 0; i < extensions.length; i ++ ) {\n\n\t\t\tconst result = func( extensions[ i ] );\n\n\t\t\tif ( result ) pending.push( result );\n\n\t\t}\n\n\t\treturn pending;\n\n\t}\n\n\t/**\n\t * Requests the specified dependency asynchronously, with caching.\n\t *\n\t * @private\n\t * @param {string} type\n\t * @param {number} index\n\t * @return {Promise<Object3D|Material|THREE.Texture|AnimationClip|ArrayBuffer|Object>}\n\t */\n\tgetDependency( type, index ) {\n\n\t\tconst cacheKey = type + ':' + index;\n\t\tlet dependency = this.cache.get( cacheKey );\n\n\t\tif ( ! dependency ) {\n\n\t\t\tswitch ( type ) {\n\n\t\t\t\tcase 'scene':\n\t\t\t\t\tdependency = this.loadScene( index );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'node':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadNode && ext.loadNode( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'mesh':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadMesh && ext.loadMesh( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'accessor':\n\t\t\t\t\tdependency = this.loadAccessor( index );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'bufferView':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadBufferView && ext.loadBufferView( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'buffer':\n\t\t\t\t\tdependency = this.loadBuffer( index );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'material':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadMaterial && ext.loadMaterial( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'texture':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadTexture && ext.loadTexture( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'skin':\n\t\t\t\t\tdependency = this.loadSkin( index );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'animation':\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext.loadAnimation && ext.loadAnimation( index );\n\n\t\t\t\t\t} );\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 'camera':\n\t\t\t\t\tdependency = this.loadCamera( index );\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\t\t\t\t\tdependency = this._invokeOne( function ( ext ) {\n\n\t\t\t\t\t\treturn ext != this && ext.getDependency && ext.getDependency( type, index );\n\n\t\t\t\t\t} );\n\n\t\t\t\t\tif ( ! dependency ) {\n\n\t\t\t\t\t\tthrow new Error( 'Unknown type: ' + type );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tthis.cache.add( cacheKey, dependency );\n\n\t\t}\n\n\t\treturn dependency;\n\n\t}\n\n\t/**\n\t * Requests all dependencies of the specified type asynchronously, with caching.\n\t *\n\t * @private\n\t * @param {string} type\n\t * @return {Promise<Array<Object>>}\n\t */\n\tgetDependencies( type ) {\n\n\t\tlet dependencies = this.cache.get( type );\n\n\t\tif ( ! dependencies ) {\n\n\t\t\tconst parser = this;\n\t\t\tconst defs = this.json[ type + ( type === 'mesh' ? 'es' : 's' ) ] || [];\n\n\t\t\tdependencies = Promise.all( defs.map( function ( def, index ) {\n\n\t\t\t\treturn parser.getDependency( type, index );\n\n\t\t\t} ) );\n\n\t\t\tthis.cache.add( type, dependencies );\n\n\t\t}\n\n\t\treturn dependencies;\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n\t *\n\t * @private\n\t * @param {number} bufferIndex\n\t * @return {Promise<ArrayBuffer>}\n\t */\n\tloadBuffer( bufferIndex ) {\n\n\t\tconst bufferDef = this.json.buffers[ bufferIndex ];\n\t\tconst loader = this.fileLoader;\n\n\t\tif ( bufferDef.type && bufferDef.type !== 'arraybuffer' ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: ' + bufferDef.type + ' buffer type is not supported.' );\n\n\t\t}\n\n\t\t// If present, GLB container is required to be the first buffer.\n\t\tif ( bufferDef.uri === undefined && bufferIndex === 0 ) {\n\n\t\t\treturn Promise.resolve( this.extensions[ EXTENSIONS.KHR_BINARY_GLTF ].body );\n\n\t\t}\n\n\t\tconst options = this.options;\n\n\t\treturn new Promise( function ( resolve, reject ) {\n\n\t\t\tloader.load( LoaderUtils.resolveURL( bufferDef.uri, options.path ), resolve, undefined, function () {\n\n\t\t\t\treject( new Error( 'THREE.GLTFLoader: Failed to load buffer \"' + bufferDef.uri + '\".' ) );\n\n\t\t\t} );\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#buffers-and-buffer-views\n\t *\n\t * @private\n\t * @param {number} bufferViewIndex\n\t * @return {Promise<ArrayBuffer>}\n\t */\n\tloadBufferView( bufferViewIndex ) {\n\n\t\tconst bufferViewDef = this.json.bufferViews[ bufferViewIndex ];\n\n\t\treturn this.getDependency( 'buffer', bufferViewDef.buffer ).then( function ( buffer ) {\n\n\t\t\tconst byteLength = bufferViewDef.byteLength || 0;\n\t\t\tconst byteOffset = bufferViewDef.byteOffset || 0;\n\t\t\treturn buffer.slice( byteOffset, byteOffset + byteLength );\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#accessors\n\t *\n\t * @private\n\t * @param {number} accessorIndex\n\t * @return {Promise<BufferAttribute|InterleavedBufferAttribute>}\n\t */\n\tloadAccessor( accessorIndex ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\n\t\tconst accessorDef = this.json.accessors[ accessorIndex ];\n\n\t\tif ( accessorDef.bufferView === undefined && accessorDef.sparse === undefined ) {\n\n\t\t\tconst itemSize = WEBGL_TYPE_SIZES[ accessorDef.type ];\n\t\t\tconst TypedArray = WEBGL_COMPONENT_TYPES[ accessorDef.componentType ];\n\t\t\tconst normalized = accessorDef.normalized === true;\n\n\t\t\tconst array = new TypedArray( accessorDef.count * itemSize );\n\t\t\treturn Promise.resolve( new BufferAttribute( array, itemSize, normalized ) );\n\n\t\t}\n\n\t\tconst pendingBufferViews = [];\n\n\t\tif ( accessorDef.bufferView !== undefined ) {\n\n\t\t\tpendingBufferViews.push( this.getDependency( 'bufferView', accessorDef.bufferView ) );\n\n\t\t} else {\n\n\t\t\tpendingBufferViews.push( null );\n\n\t\t}\n\n\t\tif ( accessorDef.sparse !== undefined ) {\n\n\t\t\tpendingBufferViews.push( this.getDependency( 'bufferView', accessorDef.sparse.indices.bufferView ) );\n\t\t\tpendingBufferViews.push( this.getDependency( 'bufferView', accessorDef.sparse.values.bufferView ) );\n\n\t\t}\n\n\t\treturn Promise.all( pendingBufferViews ).then( function ( bufferViews ) {\n\n\t\t\tconst bufferView = bufferViews[ 0 ];\n\n\t\t\tconst itemSize = WEBGL_TYPE_SIZES[ accessorDef.type ];\n\t\t\tconst TypedArray = WEBGL_COMPONENT_TYPES[ accessorDef.componentType ];\n\n\t\t\t// For VEC3: itemSize is 3, elementBytes is 4, itemBytes is 12.\n\t\t\tconst elementBytes = TypedArray.BYTES_PER_ELEMENT;\n\t\t\tconst itemBytes = elementBytes * itemSize;\n\t\t\tconst byteOffset = accessorDef.byteOffset || 0;\n\t\t\tconst byteStride = accessorDef.bufferView !== undefined ? json.bufferViews[ accessorDef.bufferView ].byteStride : undefined;\n\t\t\tconst normalized = accessorDef.normalized === true;\n\t\t\tlet array, bufferAttribute;\n\n\t\t\t// The buffer is not interleaved if the stride is the item size in bytes.\n\t\t\tif ( byteStride && byteStride !== itemBytes ) {\n\n\t\t\t\t// Each \"slice\" of the buffer, as defined by 'count' elements of 'byteStride' bytes, gets its own InterleavedBuffer\n\t\t\t\t// This makes sure that IBA.count reflects accessor.count properly\n\t\t\t\tconst ibSlice = Math.floor( byteOffset / byteStride );\n\t\t\t\tconst ibCacheKey = 'InterleavedBuffer:' + accessorDef.bufferView + ':' + accessorDef.componentType + ':' + ibSlice + ':' + accessorDef.count;\n\t\t\t\tlet ib = parser.cache.get( ibCacheKey );\n\n\t\t\t\tif ( ! ib ) {\n\n\t\t\t\t\tarray = new TypedArray( bufferView, ibSlice * byteStride, accessorDef.count * byteStride / elementBytes );\n\n\t\t\t\t\t// Integer parameters to IB/IBA are in array elements, not bytes.\n\t\t\t\t\tib = new InterleavedBuffer( array, byteStride / elementBytes );\n\n\t\t\t\t\tparser.cache.add( ibCacheKey, ib );\n\n\t\t\t\t}\n\n\t\t\t\tbufferAttribute = new InterleavedBufferAttribute( ib, itemSize, ( byteOffset % byteStride ) / elementBytes, normalized );\n\n\t\t\t} else {\n\n\t\t\t\tif ( bufferView === null ) {\n\n\t\t\t\t\tarray = new TypedArray( accessorDef.count * itemSize );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tarray = new TypedArray( bufferView, byteOffset, accessorDef.count * itemSize );\n\n\t\t\t\t}\n\n\t\t\t\tbufferAttribute = new BufferAttribute( array, itemSize, normalized );\n\n\t\t\t}\n\n\t\t\t// https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#sparse-accessors\n\t\t\tif ( accessorDef.sparse !== undefined ) {\n\n\t\t\t\tconst itemSizeIndices = WEBGL_TYPE_SIZES.SCALAR;\n\t\t\t\tconst TypedArrayIndices = WEBGL_COMPONENT_TYPES[ accessorDef.sparse.indices.componentType ];\n\n\t\t\t\tconst byteOffsetIndices = accessorDef.sparse.indices.byteOffset || 0;\n\t\t\t\tconst byteOffsetValues = accessorDef.sparse.values.byteOffset || 0;\n\n\t\t\t\tconst sparseIndices = new TypedArrayIndices( bufferViews[ 1 ], byteOffsetIndices, accessorDef.sparse.count * itemSizeIndices );\n\t\t\t\tconst sparseValues = new TypedArray( bufferViews[ 2 ], byteOffsetValues, accessorDef.sparse.count * itemSize );\n\n\t\t\t\tif ( bufferView !== null ) {\n\n\t\t\t\t\t// Avoid modifying the original ArrayBuffer, if the bufferView wasn't initialized with zeroes.\n\t\t\t\t\tbufferAttribute = new BufferAttribute( bufferAttribute.array.slice(), bufferAttribute.itemSize, bufferAttribute.normalized );\n\n\t\t\t\t}\n\n\t\t\t\t// Ignore normalized since we copy from sparse\n\t\t\t\tbufferAttribute.normalized = false;\n\n\t\t\t\tfor ( let i = 0, il = sparseIndices.length; i < il; i ++ ) {\n\n\t\t\t\t\tconst index = sparseIndices[ i ];\n\n\t\t\t\t\tbufferAttribute.setX( index, sparseValues[ i * itemSize ] );\n\t\t\t\t\tif ( itemSize >= 2 ) bufferAttribute.setY( index, sparseValues[ i * itemSize + 1 ] );\n\t\t\t\t\tif ( itemSize >= 3 ) bufferAttribute.setZ( index, sparseValues[ i * itemSize + 2 ] );\n\t\t\t\t\tif ( itemSize >= 4 ) bufferAttribute.setW( index, sparseValues[ i * itemSize + 3 ] );\n\t\t\t\t\tif ( itemSize >= 5 ) throw new Error( 'THREE.GLTFLoader: Unsupported itemSize in sparse BufferAttribute.' );\n\n\t\t\t\t}\n\n\t\t\t\tbufferAttribute.normalized = normalized;\n\n\t\t\t}\n\n\t\t\treturn bufferAttribute;\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#textures\n\t *\n\t * @private\n\t * @param {number} textureIndex\n\t * @return {Promise<THREE.Texture|null>}\n\t */\n\tloadTexture( textureIndex ) {\n\n\t\tconst json = this.json;\n\t\tconst options = this.options;\n\t\tconst textureDef = json.textures[ textureIndex ];\n\t\tconst sourceIndex = textureDef.source;\n\t\tconst sourceDef = json.images[ sourceIndex ];\n\n\t\tlet loader = this.textureLoader;\n\n\t\tif ( sourceDef.uri ) {\n\n\t\t\tconst handler = options.manager.getHandler( sourceDef.uri );\n\t\t\tif ( handler !== null ) loader = handler;\n\n\t\t}\n\n\t\treturn this.loadTextureImage( textureIndex, sourceIndex, loader );\n\n\t}\n\n\tloadTextureImage( textureIndex, sourceIndex, loader ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\n\t\tconst textureDef = json.textures[ textureIndex ];\n\t\tconst sourceDef = json.images[ sourceIndex ];\n\n\t\tconst cacheKey = ( sourceDef.uri || sourceDef.bufferView ) + ':' + textureDef.sampler;\n\n\t\tif ( this.textureCache[ cacheKey ] ) {\n\n\t\t\t// See https://github.com/mrdoob/three.js/issues/21559.\n\t\t\treturn this.textureCache[ cacheKey ];\n\n\t\t}\n\n\t\tconst promise = this.loadImageSource( sourceIndex, loader ).then( function ( texture ) {\n\n\t\t\ttexture.flipY = false;\n\n\t\t\ttexture.name = textureDef.name || sourceDef.name || '';\n\n\t\t\tif ( texture.name === '' && typeof sourceDef.uri === 'string' && sourceDef.uri.startsWith( 'data:image/' ) === false ) {\n\n\t\t\t\ttexture.name = sourceDef.uri;\n\n\t\t\t}\n\n\t\t\tconst samplers = json.samplers || {};\n\t\t\tconst sampler = samplers[ textureDef.sampler ] || {};\n\n\t\t\ttexture.magFilter = WEBGL_FILTERS[ sampler.magFilter ] || LinearFilter;\n\t\t\ttexture.minFilter = WEBGL_FILTERS[ sampler.minFilter ] || LinearMipmapLinearFilter;\n\t\t\ttexture.wrapS = WEBGL_WRAPPINGS[ sampler.wrapS ] || RepeatWrapping;\n\t\t\ttexture.wrapT = WEBGL_WRAPPINGS[ sampler.wrapT ] || RepeatWrapping;\n\t\t\ttexture.generateMipmaps = ! texture.isCompressedTexture && texture.minFilter !== NearestFilter && texture.minFilter !== LinearFilter;\n\n\t\t\tparser.associations.set( texture, { textures: textureIndex } );\n\n\t\t\treturn texture;\n\n\t\t} ).catch( function () {\n\n\t\t\treturn null;\n\n\t\t} );\n\n\t\tthis.textureCache[ cacheKey ] = promise;\n\n\t\treturn promise;\n\n\t}\n\n\tloadImageSource( sourceIndex, loader ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\t\tconst options = this.options;\n\n\t\tif ( this.sourceCache[ sourceIndex ] !== undefined ) {\n\n\t\t\treturn this.sourceCache[ sourceIndex ].then( ( texture ) => texture.clone() );\n\n\t\t}\n\n\t\tconst sourceDef = json.images[ sourceIndex ];\n\n\t\tconst URL = self.URL || self.webkitURL;\n\n\t\tlet sourceURI = sourceDef.uri || '';\n\t\tlet isObjectURL = false;\n\n\t\tif ( sourceDef.bufferView !== undefined ) {\n\n\t\t\t// Load binary image data from bufferView, if provided.\n\n\t\t\tsourceURI = parser.getDependency( 'bufferView', sourceDef.bufferView ).then( function ( bufferView ) {\n\n\t\t\t\tisObjectURL = true;\n\t\t\t\tconst blob = new Blob( [ bufferView ], { type: sourceDef.mimeType } );\n\t\t\t\tsourceURI = URL.createObjectURL( blob );\n\t\t\t\treturn sourceURI;\n\n\t\t\t} );\n\n\t\t} else if ( sourceDef.uri === undefined ) {\n\n\t\t\tthrow new Error( 'THREE.GLTFLoader: Image ' + sourceIndex + ' is missing URI and bufferView' );\n\n\t\t}\n\n\t\tconst promise = Promise.resolve( sourceURI ).then( function ( sourceURI ) {\n\n\t\t\treturn new Promise( function ( resolve, reject ) {\n\n\t\t\t\tlet onLoad = resolve;\n\n\t\t\t\tif ( loader.isImageBitmapLoader === true ) {\n\n\t\t\t\t\tonLoad = function ( imageBitmap ) {\n\n\t\t\t\t\t\tconst texture = new Texture( imageBitmap );\n\t\t\t\t\t\ttexture.needsUpdate = true;\n\n\t\t\t\t\t\tresolve( texture );\n\n\t\t\t\t\t};\n\n\t\t\t\t}\n\n\t\t\t\tloader.load( LoaderUtils.resolveURL( sourceURI, options.path ), onLoad, undefined, reject );\n\n\t\t\t} );\n\n\t\t} ).then( function ( texture ) {\n\n\t\t\t// Clean up resources and configure Texture.\n\n\t\t\tif ( isObjectURL === true ) {\n\n\t\t\t\tURL.revokeObjectURL( sourceURI );\n\n\t\t\t}\n\n\t\t\tassignExtrasToUserData( texture, sourceDef );\n\n\t\t\ttexture.userData.mimeType = sourceDef.mimeType || getImageURIMimeType( sourceDef.uri );\n\n\t\t\treturn texture;\n\n\t\t} ).catch( function ( error ) {\n\n\t\t\tconsole.error( 'THREE.GLTFLoader: Couldn\\'t load texture', sourceURI );\n\t\t\tthrow error;\n\n\t\t} );\n\n\t\tthis.sourceCache[ sourceIndex ] = promise;\n\t\treturn promise;\n\n\t}\n\n\t/**\n\t * Asynchronously assigns a texture to the given material parameters.\n\t *\n\t * @private\n\t * @param {Object} materialParams\n\t * @param {string} mapName\n\t * @param {Object} mapDef\n\t * @param {string} [colorSpace]\n\t * @return {Promise<Texture>}\n\t */\n\tassignTexture( materialParams, mapName, mapDef, colorSpace ) {\n\n\t\tconst parser = this;\n\n\t\treturn this.getDependency( 'texture', mapDef.index ).then( function ( texture ) {\n\n\t\t\tif ( ! texture ) return null;\n\n\t\t\tif ( mapDef.texCoord !== undefined && mapDef.texCoord > 0 ) {\n\n\t\t\t\ttexture = texture.clone();\n\t\t\t\ttexture.channel = mapDef.texCoord;\n\n\t\t\t}\n\n\t\t\tif ( parser.extensions[ EXTENSIONS.KHR_TEXTURE_TRANSFORM ] ) {\n\n\t\t\t\tconst transform = mapDef.extensions !== undefined ? mapDef.extensions[ EXTENSIONS.KHR_TEXTURE_TRANSFORM ] : undefined;\n\n\t\t\t\tif ( transform ) {\n\n\t\t\t\t\tconst gltfReference = parser.associations.get( texture );\n\t\t\t\t\ttexture = parser.extensions[ EXTENSIONS.KHR_TEXTURE_TRANSFORM ].extendTexture( texture, transform );\n\t\t\t\t\tparser.associations.set( texture, gltfReference );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( colorSpace !== undefined ) {\n\n\t\t\t\ttexture.colorSpace = colorSpace;\n\n\t\t\t}\n\n\t\t\tmaterialParams[ mapName ] = texture;\n\n\t\t\treturn texture;\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Assigns final material to a Mesh, Line, or Points instance. The instance\n\t * already has a material (generated from the glTF material options alone)\n\t * but reuse of the same glTF material may require multiple threejs materials\n\t * to accommodate different primitive types, defines, etc. New materials will\n\t * be created if necessary, and reused from a cache.\n\t *\n\t * @private\n\t * @param {Object3D} mesh Mesh, Line, or Points instance.\n\t */\n\tassignFinalMaterial( mesh ) {\n\n\t\tconst geometry = mesh.geometry;\n\t\tlet material = mesh.material;\n\n\t\tconst useDerivativeTangents = geometry.attributes.tangent === undefined;\n\t\tconst useVertexColors = geometry.attributes.color !== undefined;\n\t\tconst useFlatShading = geometry.attributes.normal === undefined;\n\n\t\tif ( mesh.isPoints ) {\n\n\t\t\tconst cacheKey = 'PointsMaterial:' + material.uuid;\n\n\t\t\tlet pointsMaterial = this.cache.get( cacheKey );\n\n\t\t\tif ( ! pointsMaterial ) {\n\n\t\t\t\tpointsMaterial = new PointsMaterial();\n\t\t\t\tMaterial.prototype.copy.call( pointsMaterial, material );\n\t\t\t\tpointsMaterial.color.copy( material.color );\n\t\t\t\tpointsMaterial.map = material.map;\n\t\t\t\tpointsMaterial.sizeAttenuation = false; // glTF spec says points should be 1px\n\n\t\t\t\tthis.cache.add( cacheKey, pointsMaterial );\n\n\t\t\t}\n\n\t\t\tmaterial = pointsMaterial;\n\n\t\t} else if ( mesh.isLine ) {\n\n\t\t\tconst cacheKey = 'LineBasicMaterial:' + material.uuid;\n\n\t\t\tlet lineMaterial = this.cache.get( cacheKey );\n\n\t\t\tif ( ! lineMaterial ) {\n\n\t\t\t\tlineMaterial = new LineBasicMaterial();\n\t\t\t\tMaterial.prototype.copy.call( lineMaterial, material );\n\t\t\t\tlineMaterial.color.copy( material.color );\n\t\t\t\tlineMaterial.map = material.map;\n\n\t\t\t\tthis.cache.add( cacheKey, lineMaterial );\n\n\t\t\t}\n\n\t\t\tmaterial = lineMaterial;\n\n\t\t}\n\n\t\t// Clone the material if it will be modified\n\t\tif ( useDerivativeTangents || useVertexColors || useFlatShading ) {\n\n\t\t\tlet cacheKey = 'ClonedMaterial:' + material.uuid + ':';\n\n\t\t\tif ( useDerivativeTangents ) cacheKey += 'derivative-tangents:';\n\t\t\tif ( useVertexColors ) cacheKey += 'vertex-colors:';\n\t\t\tif ( useFlatShading ) cacheKey += 'flat-shading:';\n\n\t\t\tlet cachedMaterial = this.cache.get( cacheKey );\n\n\t\t\tif ( ! cachedMaterial ) {\n\n\t\t\t\tcachedMaterial = material.clone();\n\n\t\t\t\tif ( useVertexColors ) cachedMaterial.vertexColors = true;\n\t\t\t\tif ( useFlatShading ) cachedMaterial.flatShading = true;\n\n\t\t\t\tif ( useDerivativeTangents ) {\n\n\t\t\t\t\t// https://github.com/mrdoob/three.js/issues/11438#issuecomment-507003995\n\t\t\t\t\tif ( cachedMaterial.normalScale ) cachedMaterial.normalScale.y *= - 1;\n\t\t\t\t\tif ( cachedMaterial.clearcoatNormalScale ) cachedMaterial.clearcoatNormalScale.y *= - 1;\n\n\t\t\t\t}\n\n\t\t\t\tthis.cache.add( cacheKey, cachedMaterial );\n\n\t\t\t\tthis.associations.set( cachedMaterial, this.associations.get( material ) );\n\n\t\t\t}\n\n\t\t\tmaterial = cachedMaterial;\n\n\t\t}\n\n\t\tmesh.material = material;\n\n\t}\n\n\tgetMaterialType( /* materialIndex */ ) {\n\n\t\treturn MeshStandardMaterial;\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#materials\n\t *\n\t * @private\n\t * @param {number} materialIndex\n\t * @return {Promise<Material>}\n\t */\n\tloadMaterial( materialIndex ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\t\tconst extensions = this.extensions;\n\t\tconst materialDef = json.materials[ materialIndex ];\n\n\t\tlet materialType;\n\t\tconst materialParams = {};\n\t\tconst materialExtensions = materialDef.extensions || {};\n\n\t\tconst pending = [];\n\n\t\tif ( materialExtensions[ EXTENSIONS.KHR_MATERIALS_UNLIT ] ) {\n\n\t\t\tconst kmuExtension = extensions[ EXTENSIONS.KHR_MATERIALS_UNLIT ];\n\t\t\tmaterialType = kmuExtension.getMaterialType();\n\t\t\tpending.push( kmuExtension.extendParams( materialParams, materialDef, parser ) );\n\n\t\t} else {\n\n\t\t\t// Specification:\n\t\t\t// https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#metallic-roughness-material\n\n\t\t\tconst metallicRoughness = materialDef.pbrMetallicRoughness || {};\n\n\t\t\tmaterialParams.color = new Color( 1.0, 1.0, 1.0 );\n\t\t\tmaterialParams.opacity = 1.0;\n\n\t\t\tif ( Array.isArray( metallicRoughness.baseColorFactor ) ) {\n\n\t\t\t\tconst array = metallicRoughness.baseColorFactor;\n\n\t\t\t\tmaterialParams.color.setRGB( array[ 0 ], array[ 1 ], array[ 2 ], LinearSRGBColorSpace );\n\t\t\t\tmaterialParams.opacity = array[ 3 ];\n\n\t\t\t}\n\n\t\t\tif ( metallicRoughness.baseColorTexture !== undefined ) {\n\n\t\t\t\tpending.push( parser.assignTexture( materialParams, 'map', metallicRoughness.baseColorTexture, SRGBColorSpace ) );\n\n\t\t\t}\n\n\t\t\tmaterialParams.metalness = metallicRoughness.metallicFactor !== undefined ? metallicRoughness.metallicFactor : 1.0;\n\t\t\tmaterialParams.roughness = metallicRoughness.roughnessFactor !== undefined ? metallicRoughness.roughnessFactor : 1.0;\n\n\t\t\tif ( metallicRoughness.metallicRoughnessTexture !== undefined ) {\n\n\t\t\t\tpending.push( parser.assignTexture( materialParams, 'metalnessMap', metallicRoughness.metallicRoughnessTexture ) );\n\t\t\t\tpending.push( parser.assignTexture( materialParams, 'roughnessMap', metallicRoughness.metallicRoughnessTexture ) );\n\n\t\t\t}\n\n\t\t\tmaterialType = this._invokeOne( function ( ext ) {\n\n\t\t\t\treturn ext.getMaterialType && ext.getMaterialType( materialIndex );\n\n\t\t\t} );\n\n\t\t\tpending.push( Promise.all( this._invokeAll( function ( ext ) {\n\n\t\t\t\treturn ext.extendMaterialParams && ext.extendMaterialParams( materialIndex, materialParams );\n\n\t\t\t} ) ) );\n\n\t\t}\n\n\t\tif ( materialDef.doubleSided === true ) {\n\n\t\t\tmaterialParams.side = DoubleSide;\n\n\t\t}\n\n\t\tconst alphaMode = materialDef.alphaMode || ALPHA_MODES.OPAQUE;\n\n\t\tif ( alphaMode === ALPHA_MODES.BLEND ) {\n\n\t\t\tmaterialParams.transparent = true;\n\n\t\t\t// See: https://github.com/mrdoob/three.js/issues/17706\n\t\t\tmaterialParams.depthWrite = false;\n\n\t\t} else {\n\n\t\t\tmaterialParams.transparent = false;\n\n\t\t\tif ( alphaMode === ALPHA_MODES.MASK ) {\n\n\t\t\t\tmaterialParams.alphaTest = materialDef.alphaCutoff !== undefined ? materialDef.alphaCutoff : 0.5;\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( materialDef.normalTexture !== undefined && materialType !== MeshBasicMaterial ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'normalMap', materialDef.normalTexture ) );\n\n\t\t\tmaterialParams.normalScale = new Vector2( 1, 1 );\n\n\t\t\tif ( materialDef.normalTexture.scale !== undefined ) {\n\n\t\t\t\tconst scale = materialDef.normalTexture.scale;\n\n\t\t\t\tmaterialParams.normalScale.set( scale, scale );\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( materialDef.occlusionTexture !== undefined && materialType !== MeshBasicMaterial ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'aoMap', materialDef.occlusionTexture ) );\n\n\t\t\tif ( materialDef.occlusionTexture.strength !== undefined ) {\n\n\t\t\t\tmaterialParams.aoMapIntensity = materialDef.occlusionTexture.strength;\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( materialDef.emissiveFactor !== undefined && materialType !== MeshBasicMaterial ) {\n\n\t\t\tconst emissiveFactor = materialDef.emissiveFactor;\n\t\t\tmaterialParams.emissive = new Color().setRGB( emissiveFactor[ 0 ], emissiveFactor[ 1 ], emissiveFactor[ 2 ], LinearSRGBColorSpace );\n\n\t\t}\n\n\t\tif ( materialDef.emissiveTexture !== undefined && materialType !== MeshBasicMaterial ) {\n\n\t\t\tpending.push( parser.assignTexture( materialParams, 'emissiveMap', materialDef.emissiveTexture, SRGBColorSpace ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending ).then( function () {\n\n\t\t\tconst material = new materialType( materialParams );\n\n\t\t\tif ( materialDef.name ) material.name = materialDef.name;\n\n\t\t\tassignExtrasToUserData( material, materialDef );\n\n\t\t\tparser.associations.set( material, { materials: materialIndex } );\n\n\t\t\tif ( materialDef.extensions ) addUnknownExtensionsToUserData( extensions, material, materialDef );\n\n\t\t\treturn material;\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * When Object3D instances are targeted by animation, they need unique names.\n\t *\n\t * @private\n\t * @param {string} originalName\n\t * @return {string}\n\t */\n\tcreateUniqueName( originalName ) {\n\n\t\tconst sanitizedName = PropertyBinding.sanitizeNodeName( originalName || '' );\n\n\t\tif ( sanitizedName in this.nodeNamesUsed ) {\n\n\t\t\treturn sanitizedName + '_' + ( ++ this.nodeNamesUsed[ sanitizedName ] );\n\n\t\t} else {\n\n\t\t\tthis.nodeNamesUsed[ sanitizedName ] = 0;\n\n\t\t\treturn sanitizedName;\n\n\t\t}\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#geometry\n\t *\n\t * Creates BufferGeometries from primitives.\n\t *\n\t * @private\n\t * @param {Array<GLTF.Primitive>} primitives\n\t * @return {Promise<Array<BufferGeometry>>}\n\t */\n\tloadGeometries( primitives ) {\n\n\t\tconst parser = this;\n\t\tconst extensions = this.extensions;\n\t\tconst cache = this.primitiveCache;\n\n\t\tfunction createDracoPrimitive( primitive ) {\n\n\t\t\treturn extensions[ EXTENSIONS.KHR_DRACO_MESH_COMPRESSION ]\n\t\t\t\t.decodePrimitive( primitive, parser )\n\t\t\t\t.then( function ( geometry ) {\n\n\t\t\t\t\treturn addPrimitiveAttributes( geometry, primitive, parser );\n\n\t\t\t\t} );\n\n\t\t}\n\n\t\tconst pending = [];\n\n\t\tfor ( let i = 0, il = primitives.length; i < il; i ++ ) {\n\n\t\t\tconst primitive = primitives[ i ];\n\t\t\tconst cacheKey = createPrimitiveKey( primitive );\n\n\t\t\t// See if we've already created this geometry\n\t\t\tconst cached = cache[ cacheKey ];\n\n\t\t\tif ( cached ) {\n\n\t\t\t\t// Use the cached geometry if it exists\n\t\t\t\tpending.push( cached.promise );\n\n\t\t\t} else {\n\n\t\t\t\tlet geometryPromise;\n\n\t\t\t\tif ( primitive.extensions && primitive.extensions[ EXTENSIONS.KHR_DRACO_MESH_COMPRESSION ] ) {\n\n\t\t\t\t\t// Use DRACO geometry if available\n\t\t\t\t\tgeometryPromise = createDracoPrimitive( primitive );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t// Otherwise create a new geometry\n\t\t\t\t\tgeometryPromise = addPrimitiveAttributes( new BufferGeometry(), primitive, parser );\n\n\t\t\t\t}\n\n\t\t\t\t// Cache this geometry\n\t\t\t\tcache[ cacheKey ] = { primitive: primitive, promise: geometryPromise };\n\n\t\t\t\tpending.push( geometryPromise );\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Promise.all( pending );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/blob/master/specification/2.0/README.md#meshes\n\t *\n\t * @private\n\t * @param {number} meshIndex\n\t * @return {Promise<Group|Mesh|SkinnedMesh|Line|Points>}\n\t */\n\tloadMesh( meshIndex ) {\n\n\t\tconst parser = this;\n\t\tconst json = this.json;\n\t\tconst extensions = this.extensions;\n\n\t\tconst meshDef = json.meshes[ meshIndex ];\n\t\tconst primitives = meshDef.primitives;\n\n\t\tconst pending = [];\n\n\t\tfor ( let i = 0, il = primitives.length; i < il; i ++ ) {\n\n\t\t\tconst material = primitives[ i ].material === undefined\n\t\t\t\t? createDefaultMaterial( this.cache )\n\t\t\t\t: this.getDependency( 'material', primitives[ i ].material );\n\n\t\t\tpending.push( material );\n\n\t\t}\n\n\t\tpending.push( parser.loadGeometries( primitives ) );\n\n\t\treturn Promise.all( pending ).then( function ( results ) {\n\n\t\t\tconst materials = results.slice( 0, results.length - 1 );\n\t\t\tconst geometries = results[ results.length - 1 ];\n\n\t\t\tconst meshes = [];\n\n\t\t\tfor ( let i = 0, il = geometries.length; i < il; i ++ ) {\n\n\t\t\t\tconst geometry = geometries[ i ];\n\t\t\t\tconst primitive = primitives[ i ];\n\n\t\t\t\t// 1. create Mesh\n\n\t\t\t\tlet mesh;\n\n\t\t\t\tconst material = materials[ i ];\n\n\t\t\t\tif ( primitive.mode === WEBGL_CONSTANTS.TRIANGLES ||\n\t\t\t\t\t\tprimitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP ||\n\t\t\t\t\t\tprimitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN ||\n\t\t\t\t\t\tprimitive.mode === undefined ) {\n\n\t\t\t\t\t// .isSkinnedMesh isn't in glTF spec. See ._markDefs()\n\t\t\t\t\tmesh = meshDef.isSkinnedMesh === true\n\t\t\t\t\t\t? new SkinnedMesh( geometry, material )\n\t\t\t\t\t\t: new Mesh( geometry, material );\n\n\t\t\t\t\tif ( mesh.isSkinnedMesh === true ) {\n\n\t\t\t\t\t\t// normalize skin weights to fix malformed assets (see #15319)\n\t\t\t\t\t\tmesh.normalizeSkinWeights();\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( primitive.mode === WEBGL_CONSTANTS.TRIANGLE_STRIP ) {\n\n\t\t\t\t\t\tmesh.geometry = toTrianglesDrawMode( mesh.geometry, TriangleStripDrawMode );\n\n\t\t\t\t\t} else if ( primitive.mode === WEBGL_CONSTANTS.TRIANGLE_FAN ) {\n\n\t\t\t\t\t\tmesh.geometry = toTrianglesDrawMode( mesh.geometry, TriangleFanDrawMode );\n\n\t\t\t\t\t}\n\n\t\t\t\t} else if ( primitive.mode === WEBGL_CONSTANTS.LINES ) {\n\n\t\t\t\t\tmesh = new LineSegments( geometry, material );\n\n\t\t\t\t} else if ( primitive.mode === WEBGL_CONSTANTS.LINE_STRIP ) {\n\n\t\t\t\t\tmesh = new Line( geometry, material );\n\n\t\t\t\t} else if ( primitive.mode === WEBGL_CONSTANTS.LINE_LOOP ) {\n\n\t\t\t\t\tmesh = new LineLoop( geometry, material );\n\n\t\t\t\t} else if ( primitive.mode === WEBGL_CONSTANTS.POINTS ) {\n\n\t\t\t\t\tmesh = new Points( geometry, material );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tthrow new Error( 'THREE.GLTFLoader: Primitive mode unsupported: ' + primitive.mode );\n\n\t\t\t\t}\n\n\t\t\t\tif ( Object.keys( mesh.geometry.morphAttributes ).length > 0 ) {\n\n\t\t\t\t\tupdateMorphTargets( mesh, meshDef );\n\n\t\t\t\t}\n\n\t\t\t\tmesh.name = parser.createUniqueName( meshDef.name || ( 'mesh_' + meshIndex ) );\n\n\t\t\t\tassignExtrasToUserData( mesh, meshDef );\n\n\t\t\t\tif ( primitive.extensions ) addUnknownExtensionsToUserData( extensions, mesh, primitive );\n\n\t\t\t\tparser.assignFinalMaterial( mesh );\n\n\t\t\t\tmeshes.push( mesh );\n\n\t\t\t}\n\n\t\t\tfor ( let i = 0, il = meshes.length; i < il; i ++ ) {\n\n\t\t\t\tparser.associations.set( meshes[ i ], {\n\t\t\t\t\tmeshes: meshIndex,\n\t\t\t\t\tprimitives: i\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t\tif ( meshes.length === 1 ) {\n\n\t\t\t\tif ( meshDef.extensions ) addUnknownExtensionsToUserData( extensions, meshes[ 0 ], meshDef );\n\n\t\t\t\treturn meshes[ 0 ];\n\n\t\t\t}\n\n\t\t\tconst group = new Group();\n\n\t\t\tif ( meshDef.extensions ) addUnknownExtensionsToUserData( extensions, group, meshDef );\n\n\t\t\tparser.associations.set( group, { meshes: meshIndex } );\n\n\t\t\tfor ( let i = 0, il = meshes.length; i < il; i ++ ) {\n\n\t\t\t\tgroup.add( meshes[ i ] );\n\n\t\t\t}\n\n\t\t\treturn group;\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#cameras\n\t *\n\t * @private\n\t * @param {number} cameraIndex\n\t * @return {Promise<THREE.Camera>}\n\t */\n\tloadCamera( cameraIndex ) {\n\n\t\tlet camera;\n\t\tconst cameraDef = this.json.cameras[ cameraIndex ];\n\t\tconst params = cameraDef[ cameraDef.type ];\n\n\t\tif ( ! params ) {\n\n\t\t\tconsole.warn( 'THREE.GLTFLoader: Missing camera parameters.' );\n\t\t\treturn;\n\n\t\t}\n\n\t\tif ( cameraDef.type === 'perspective' ) {\n\n\t\t\tcamera = new PerspectiveCamera( MathUtils.radToDeg( params.yfov ), params.aspectRatio || 1, params.znear || 1, params.zfar || 2e6 );\n\n\t\t} else if ( cameraDef.type === 'orthographic' ) {\n\n\t\t\tcamera = new OrthographicCamera( - params.xmag, params.xmag, params.ymag, - params.ymag, params.znear, params.zfar );\n\n\t\t}\n\n\t\tif ( cameraDef.name ) camera.name = this.createUniqueName( cameraDef.name );\n\n\t\tassignExtrasToUserData( camera, cameraDef );\n\n\t\treturn Promise.resolve( camera );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#skins\n\t *\n\t * @private\n\t * @param {number} skinIndex\n\t * @return {Promise<Skeleton>}\n\t */\n\tloadSkin( skinIndex ) {\n\n\t\tconst skinDef = this.json.skins[ skinIndex ];\n\n\t\tconst pending = [];\n\n\t\tfor ( let i = 0, il = skinDef.joints.length; i < il; i ++ ) {\n\n\t\t\tpending.push( this._loadNodeShallow( skinDef.joints[ i ] ) );\n\n\t\t}\n\n\t\tif ( skinDef.inverseBindMatrices !== undefined ) {\n\n\t\t\tpending.push( this.getDependency( 'accessor', skinDef.inverseBindMatrices ) );\n\n\t\t} else {\n\n\t\t\tpending.push( null );\n\n\t\t}\n\n\t\treturn Promise.all( pending ).then( function ( results ) {\n\n\t\t\tconst inverseBindMatrices = results.pop();\n\t\t\tconst jointNodes = results;\n\n\t\t\t// Note that bones (joint nodes) may or may not be in the\n\t\t\t// scene graph at this time.\n\n\t\t\tconst bones = [];\n\t\t\tconst boneInverses = [];\n\n\t\t\tfor ( let i = 0, il = jointNodes.length; i < il; i ++ ) {\n\n\t\t\t\tconst jointNode = jointNodes[ i ];\n\n\t\t\t\tif ( jointNode ) {\n\n\t\t\t\t\tbones.push( jointNode );\n\n\t\t\t\t\tconst mat = new Matrix4();\n\n\t\t\t\t\tif ( inverseBindMatrices !== null ) {\n\n\t\t\t\t\t\tmat.fromArray( inverseBindMatrices.array, i * 16 );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tboneInverses.push( mat );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconsole.warn( 'THREE.GLTFLoader: Joint \"%s\" could not be found.', skinDef.joints[ i ] );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn new Skeleton( bones, boneInverses );\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#animations\n\t *\n\t * @private\n\t * @param {number} animationIndex\n\t * @return {Promise<AnimationClip>}\n\t */\n\tloadAnimation( animationIndex ) {\n\n\t\tconst json = this.json;\n\t\tconst parser = this;\n\n\t\tconst animationDef = json.animations[ animationIndex ];\n\t\tconst animationName = animationDef.name ? animationDef.name : 'animation_' + animationIndex;\n\n\t\tconst pendingNodes = [];\n\t\tconst pendingInputAccessors = [];\n\t\tconst pendingOutputAccessors = [];\n\t\tconst pendingSamplers = [];\n\t\tconst pendingTargets = [];\n\n\t\tfor ( let i = 0, il = animationDef.channels.length; i < il; i ++ ) {\n\n\t\t\tconst channel = animationDef.channels[ i ];\n\t\t\tconst sampler = animationDef.samplers[ channel.sampler ];\n\t\t\tconst target = channel.target;\n\t\t\tconst name = target.node;\n\t\t\tconst input = animationDef.parameters !== undefined ? animationDef.parameters[ sampler.input ] : sampler.input;\n\t\t\tconst output = animationDef.parameters !== undefined ? animationDef.parameters[ sampler.output ] : sampler.output;\n\n\t\t\tif ( target.node === undefined ) continue;\n\n\t\t\tpendingNodes.push( this.getDependency( 'node', name ) );\n\t\t\tpendingInputAccessors.push( this.getDependency( 'accessor', input ) );\n\t\t\tpendingOutputAccessors.push( this.getDependency( 'accessor', output ) );\n\t\t\tpendingSamplers.push( sampler );\n\t\t\tpendingTargets.push( target );\n\n\t\t}\n\n\t\treturn Promise.all( [\n\n\t\t\tPromise.all( pendingNodes ),\n\t\t\tPromise.all( pendingInputAccessors ),\n\t\t\tPromise.all( pendingOutputAccessors ),\n\t\t\tPromise.all( pendingSamplers ),\n\t\t\tPromise.all( pendingTargets )\n\n\t\t] ).then( function ( dependencies ) {\n\n\t\t\tconst nodes = dependencies[ 0 ];\n\t\t\tconst inputAccessors = dependencies[ 1 ];\n\t\t\tconst outputAccessors = dependencies[ 2 ];\n\t\t\tconst samplers = dependencies[ 3 ];\n\t\t\tconst targets = dependencies[ 4 ];\n\n\t\t\tconst tracks = [];\n\n\t\t\tfor ( let i = 0, il = nodes.length; i < il; i ++ ) {\n\n\t\t\t\tconst node = nodes[ i ];\n\t\t\t\tconst inputAccessor = inputAccessors[ i ];\n\t\t\t\tconst outputAccessor = outputAccessors[ i ];\n\t\t\t\tconst sampler = samplers[ i ];\n\t\t\t\tconst target = targets[ i ];\n\n\t\t\t\tif ( node === undefined ) continue;\n\n\t\t\t\tif ( node.updateMatrix ) {\n\n\t\t\t\t\tnode.updateMatrix();\n\n\t\t\t\t}\n\n\t\t\t\tconst createdTracks = parser._createAnimationTracks( node, inputAccessor, outputAccessor, sampler, target );\n\n\t\t\t\tif ( createdTracks ) {\n\n\t\t\t\t\tfor ( let k = 0; k < createdTracks.length; k ++ ) {\n\n\t\t\t\t\t\ttracks.push( createdTracks[ k ] );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn new AnimationClip( animationName, undefined, tracks );\n\n\t\t} );\n\n\t}\n\n\tcreateNodeMesh( nodeIndex ) {\n\n\t\tconst json = this.json;\n\t\tconst parser = this;\n\t\tconst nodeDef = json.nodes[ nodeIndex ];\n\n\t\tif ( nodeDef.mesh === undefined ) return null;\n\n\t\treturn parser.getDependency( 'mesh', nodeDef.mesh ).then( function ( mesh ) {\n\n\t\t\tconst node = parser._getNodeRef( parser.meshCache, nodeDef.mesh, mesh );\n\n\t\t\t// if weights are provided on the node, override weights on the mesh.\n\t\t\tif ( nodeDef.weights !== undefined ) {\n\n\t\t\t\tnode.traverse( function ( o ) {\n\n\t\t\t\t\tif ( ! o.isMesh ) return;\n\n\t\t\t\t\tfor ( let i = 0, il = nodeDef.weights.length; i < il; i ++ ) {\n\n\t\t\t\t\t\to.morphTargetInfluences[ i ] = nodeDef.weights[ i ];\n\n\t\t\t\t\t}\n\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t\treturn node;\n\n\t\t} );\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#nodes-and-hierarchy\n\t *\n\t * @private\n\t * @param {number} nodeIndex\n\t * @return {Promise<Object3D>}\n\t */\n\tloadNode( nodeIndex ) {\n\n\t\tconst json = this.json;\n\t\tconst parser = this;\n\n\t\tconst nodeDef = json.nodes[ nodeIndex ];\n\n\t\tconst nodePending = parser._loadNodeShallow( nodeIndex );\n\n\t\tconst childPending = [];\n\t\tconst childrenDef = nodeDef.children || [];\n\n\t\tfor ( let i = 0, il = childrenDef.length; i < il; i ++ ) {\n\n\t\t\tchildPending.push( parser.getDependency( 'node', childrenDef[ i ] ) );\n\n\t\t}\n\n\t\tconst skeletonPending = nodeDef.skin === undefined\n\t\t\t? Promise.resolve( null )\n\t\t\t: parser.getDependency( 'skin', nodeDef.skin );\n\n\t\treturn Promise.all( [\n\t\t\tnodePending,\n\t\t\tPromise.all( childPending ),\n\t\t\tskeletonPending\n\t\t] ).then( function ( results ) {\n\n\t\t\tconst node = results[ 0 ];\n\t\t\tconst children = results[ 1 ];\n\t\t\tconst skeleton = results[ 2 ];\n\n\t\t\tif ( skeleton !== null ) {\n\n\t\t\t\t// This full traverse should be fine because\n\t\t\t\t// child glTF nodes have not been added to this node yet.\n\t\t\t\tnode.traverse( function ( mesh ) {\n\n\t\t\t\t\tif ( ! mesh.isSkinnedMesh ) return;\n\n\t\t\t\t\tmesh.bind( skeleton, _identityMatrix );\n\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t\tfor ( let i = 0, il = children.length; i < il; i ++ ) {\n\n\t\t\t\tnode.add( children[ i ] );\n\n\t\t\t}\n\n\t\t\treturn node;\n\n\t\t} );\n\n\t}\n\n\t// ._loadNodeShallow() parses a single node.\n\t// skin and child nodes are created and added in .loadNode() (no '_' prefix).\n\t_loadNodeShallow( nodeIndex ) {\n\n\t\tconst json = this.json;\n\t\tconst extensions = this.extensions;\n\t\tconst parser = this;\n\n\t\t// This method is called from .loadNode() and .loadSkin().\n\t\t// Cache a node to avoid duplication.\n\n\t\tif ( this.nodeCache[ nodeIndex ] !== undefined ) {\n\n\t\t\treturn this.nodeCache[ nodeIndex ];\n\n\t\t}\n\n\t\tconst nodeDef = json.nodes[ nodeIndex ];\n\n\t\t// reserve node's name before its dependencies, so the root has the intended name.\n\t\tconst nodeName = nodeDef.name ? parser.createUniqueName( nodeDef.name ) : '';\n\n\t\tconst pending = [];\n\n\t\tconst meshPromise = parser._invokeOne( function ( ext ) {\n\n\t\t\treturn ext.createNodeMesh && ext.createNodeMesh( nodeIndex );\n\n\t\t} );\n\n\t\tif ( meshPromise ) {\n\n\t\t\tpending.push( meshPromise );\n\n\t\t}\n\n\t\tif ( nodeDef.camera !== undefined ) {\n\n\t\t\tpending.push( parser.getDependency( 'camera', nodeDef.camera ).then( function ( camera ) {\n\n\t\t\t\treturn parser._getNodeRef( parser.cameraCache, nodeDef.camera, camera );\n\n\t\t\t} ) );\n\n\t\t}\n\n\t\tparser._invokeAll( function ( ext ) {\n\n\t\t\treturn ext.createNodeAttachment && ext.createNodeAttachment( nodeIndex );\n\n\t\t} ).forEach( function ( promise ) {\n\n\t\t\tpending.push( promise );\n\n\t\t} );\n\n\t\tthis.nodeCache[ nodeIndex ] = Promise.all( pending ).then( function ( objects ) {\n\n\t\t\tlet node;\n\n\t\t\t// .isBone isn't in glTF spec. See ._markDefs\n\t\t\tif ( nodeDef.isBone === true ) {\n\n\t\t\t\tnode = new Bone();\n\n\t\t\t} else if ( objects.length > 1 ) {\n\n\t\t\t\tnode = new Group();\n\n\t\t\t} else if ( objects.length === 1 ) {\n\n\t\t\t\tnode = objects[ 0 ];\n\n\t\t\t} else {\n\n\t\t\t\tnode = new Object3D();\n\n\t\t\t}\n\n\t\t\tif ( node !== objects[ 0 ] ) {\n\n\t\t\t\tfor ( let i = 0, il = objects.length; i < il; i ++ ) {\n\n\t\t\t\t\tnode.add( objects[ i ] );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( nodeDef.name ) {\n\n\t\t\t\tnode.userData.name = nodeDef.name;\n\t\t\t\tnode.name = nodeName;\n\n\t\t\t}\n\n\t\t\tassignExtrasToUserData( node, nodeDef );\n\n\t\t\tif ( nodeDef.extensions ) addUnknownExtensionsToUserData( extensions, node, nodeDef );\n\n\t\t\tif ( nodeDef.matrix !== undefined ) {\n\n\t\t\t\tconst matrix = new Matrix4();\n\t\t\t\tmatrix.fromArray( nodeDef.matrix );\n\t\t\t\tnode.applyMatrix4( matrix );\n\n\t\t\t} else {\n\n\t\t\t\tif ( nodeDef.translation !== undefined ) {\n\n\t\t\t\t\tnode.position.fromArray( nodeDef.translation );\n\n\t\t\t\t}\n\n\t\t\t\tif ( nodeDef.rotation !== undefined ) {\n\n\t\t\t\t\tnode.quaternion.fromArray( nodeDef.rotation );\n\n\t\t\t\t}\n\n\t\t\t\tif ( nodeDef.scale !== undefined ) {\n\n\t\t\t\t\tnode.scale.fromArray( nodeDef.scale );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( ! parser.associations.has( node ) ) {\n\n\t\t\t\tparser.associations.set( node, {} );\n\n\t\t\t} else if ( nodeDef.mesh !== undefined && parser.meshCache.refs[ nodeDef.mesh ] > 1 ) {\n\n\t\t\t\tconst mapping = parser.associations.get( node );\n\t\t\t\tparser.associations.set( node, { ...mapping } );\n\n\t\t\t}\n\n\t\t\tparser.associations.get( node ).nodes = nodeIndex;\n\n\t\t\treturn node;\n\n\t\t} );\n\n\t\treturn this.nodeCache[ nodeIndex ];\n\n\t}\n\n\t/**\n\t * Specification: https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#scenes\n\t *\n\t * @private\n\t * @param {number} sceneIndex\n\t * @return {Promise<Group>}\n\t */\n\tloadScene( sceneIndex ) {\n\n\t\tconst extensions = this.extensions;\n\t\tconst sceneDef = this.json.scenes[ sceneIndex ];\n\t\tconst parser = this;\n\n\t\t// Loader returns Group, not Scene.\n\t\t// See: https://github.com/mrdoob/three.js/issues/18342#issuecomment-578981172\n\t\tconst scene = new Group();\n\t\tif ( sceneDef.name ) scene.name = parser.createUniqueName( sceneDef.name );\n\n\t\tassignExtrasToUserData( scene, sceneDef );\n\n\t\tif ( sceneDef.extensions ) addUnknownExtensionsToUserData( extensions, scene, sceneDef );\n\n\t\tconst nodeIds = sceneDef.nodes || [];\n\n\t\tconst pending = [];\n\n\t\tfor ( let i = 0, il = nodeIds.length; i < il; i ++ ) {\n\n\t\t\tpending.push( parser.getDependency( 'node', nodeIds[ i ] ) );\n\n\t\t}\n\n\t\treturn Promise.all( pending ).then( function ( nodes ) {\n\n\t\t\tfor ( let i = 0, il = nodes.length; i < il; i ++ ) {\n\n\t\t\t\tscene.add( nodes[ i ] );\n\n\t\t\t}\n\n\t\t\t// Removes dangling associations, associations that reference a node that\n\t\t\t// didn't make it into the scene.\n\t\t\tconst reduceAssociations = ( node ) => {\n\n\t\t\t\tconst reducedAssociations = new Map();\n\n\t\t\t\tfor ( const [ key, value ] of parser.associations ) {\n\n\t\t\t\t\tif ( key instanceof Material || key instanceof Texture ) {\n\n\t\t\t\t\t\treducedAssociations.set( key, value );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tnode.traverse( ( node ) => {\n\n\t\t\t\t\tconst mappings = parser.associations.get( node );\n\n\t\t\t\t\tif ( mappings != null ) {\n\n\t\t\t\t\t\treducedAssociations.set( node, mappings );\n\n\t\t\t\t\t}\n\n\t\t\t\t} );\n\n\t\t\t\treturn reducedAssociations;\n\n\t\t\t};\n\n\t\t\tparser.associations = reduceAssociations( scene );\n\n\t\t\treturn scene;\n\n\t\t} );\n\n\t}\n\n\t_createAnimationTracks( node, inputAccessor, outputAccessor, sampler, target ) {\n\n\t\tconst tracks = [];\n\n\t\tconst targetName = node.name ? node.name : node.uuid;\n\t\tconst targetNames = [];\n\n\t\tif ( PATH_PROPERTIES[ target.path ] === PATH_PROPERTIES.weights ) {\n\n\t\t\tnode.traverse( function ( object ) {\n\n\t\t\t\tif ( object.morphTargetInfluences ) {\n\n\t\t\t\t\ttargetNames.push( object.name ? object.name : object.uuid );\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t} else {\n\n\t\t\ttargetNames.push( targetName );\n\n\t\t}\n\n\t\tlet TypedKeyframeTrack;\n\n\t\tswitch ( PATH_PROPERTIES[ target.path ] ) {\n\n\t\t\tcase PATH_PROPERTIES.weights:\n\n\t\t\t\tTypedKeyframeTrack = NumberKeyframeTrack;\n\t\t\t\tbreak;\n\n\t\t\tcase PATH_PROPERTIES.rotation:\n\n\t\t\t\tTypedKeyframeTrack = QuaternionKeyframeTrack;\n\t\t\t\tbreak;\n\n\t\t\tcase PATH_PROPERTIES.translation:\n\t\t\tcase PATH_PROPERTIES.scale:\n\n\t\t\t\tTypedKeyframeTrack = VectorKeyframeTrack;\n\t\t\t\tbreak;\n\n\t\t\tdefault:\n\n\t\t\t\tswitch ( outputAccessor.itemSize ) {\n\n\t\t\t\t\tcase 1:\n\t\t\t\t\t\tTypedKeyframeTrack = NumberKeyframeTrack;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 2:\n\t\t\t\t\tcase 3:\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tTypedKeyframeTrack = VectorKeyframeTrack;\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\tconst interpolation = sampler.interpolation !== undefined ? INTERPOLATION[ sampler.interpolation ] : InterpolateLinear;\n\n\n\t\tconst outputArray = this._getArrayFromAccessor( outputAccessor );\n\n\t\tfor ( let j = 0, jl = targetNames.length; j < jl; j ++ ) {\n\n\t\t\tconst track = new TypedKeyframeTrack(\n\t\t\t\ttargetNames[ j ] + '.' + PATH_PROPERTIES[ target.path ],\n\t\t\t\tinputAccessor.array,\n\t\t\t\toutputArray,\n\t\t\t\tinterpolation\n\t\t\t);\n\n\t\t\t// Override interpolation with custom factory method.\n\t\t\tif ( sampler.interpolation === 'CUBICSPLINE' ) {\n\n\t\t\t\tthis._createCubicSplineTrackInterpolant( track );\n\n\t\t\t}\n\n\t\t\ttracks.push( track );\n\n\t\t}\n\n\t\treturn tracks;\n\n\t}\n\n\t_getArrayFromAccessor( accessor ) {\n\n\t\tlet outputArray = accessor.array;\n\n\t\tif ( accessor.normalized ) {\n\n\t\t\tconst scale = getNormalizedComponentScale( outputArray.constructor );\n\t\t\tconst scaled = new Float32Array( outputArray.length );\n\n\t\t\tfor ( let j = 0, jl = outputArray.length; j < jl; j ++ ) {\n\n\t\t\t\tscaled[ j ] = outputArray[ j ] * scale;\n\n\t\t\t}\n\n\t\t\toutputArray = scaled;\n\n\t\t}\n\n\t\treturn outputArray;\n\n\t}\n\n\t_createCubicSplineTrackInterpolant( track ) {\n\n\t\ttrack.createInterpolant = function InterpolantFactoryMethodGLTFCubicSpline( result ) {\n\n\t\t\t// A CUBICSPLINE keyframe in glTF has three output values for each input value,\n\t\t\t// representing inTangent, splineVertex, and outTangent. As a result, track.getValueSize()\n\t\t\t// must be divided by three to get the interpolant's sampleSize argument.\n\n\t\t\tconst interpolantType = ( this instanceof QuaternionKeyframeTrack ) ? GLTFCubicSplineQuaternionInterpolant : GLTFCubicSplineInterpolant;\n\n\t\t\treturn new interpolantType( this.times, this.values, this.getValueSize() / 3, result );\n\n\t\t};\n\n\t\t// Mark as CUBICSPLINE. `track.getInterpolation()` doesn't support custom interpolants.\n\t\ttrack.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline = true;\n\n\t}\n\n}\n\n/**\n *\n * @private\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n */\nfunction computeBounds( geometry, primitiveDef, parser ) {\n\n\tconst attributes = primitiveDef.attributes;\n\n\tconst box = new Box3();\n\n\tif ( attributes.POSITION !== undefined ) {\n\n\t\tconst accessor = parser.json.accessors[ attributes.POSITION ];\n\n\t\tconst min = accessor.min;\n\t\tconst max = accessor.max;\n\n\t\t// glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n\t\tif ( min !== undefined && max !== undefined ) {\n\n\t\t\tbox.set(\n\t\t\t\tnew Vector3( min[ 0 ], min[ 1 ], min[ 2 ] ),\n\t\t\t\tnew Vector3( max[ 0 ], max[ 1 ], max[ 2 ] )\n\t\t\t);\n\n\t\t\tif ( accessor.normalized ) {\n\n\t\t\t\tconst boxScale = getNormalizedComponentScale( WEBGL_COMPONENT_TYPES[ accessor.componentType ] );\n\t\t\t\tbox.min.multiplyScalar( boxScale );\n\t\t\t\tbox.max.multiplyScalar( boxScale );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconsole.warn( 'THREE.GLTFLoader: Missing min/max properties for accessor POSITION.' );\n\n\t\t\treturn;\n\n\t\t}\n\n\t} else {\n\n\t\treturn;\n\n\t}\n\n\tconst targets = primitiveDef.targets;\n\n\tif ( targets !== undefined ) {\n\n\t\tconst maxDisplacement = new Vector3();\n\t\tconst vector = new Vector3();\n\n\t\tfor ( let i = 0, il = targets.length; i < il; i ++ ) {\n\n\t\t\tconst target = targets[ i ];\n\n\t\t\tif ( target.POSITION !== undefined ) {\n\n\t\t\t\tconst accessor = parser.json.accessors[ target.POSITION ];\n\t\t\t\tconst min = accessor.min;\n\t\t\t\tconst max = accessor.max;\n\n\t\t\t\t// glTF requires 'min' and 'max', but VRM (which extends glTF) currently ignores that requirement.\n\n\t\t\t\tif ( min !== undefined && max !== undefined ) {\n\n\t\t\t\t\t// we need to get max of absolute components because target weight is [-1,1]\n\t\t\t\t\tvector.setX( Math.max( Math.abs( min[ 0 ] ), Math.abs( max[ 0 ] ) ) );\n\t\t\t\t\tvector.setY( Math.max( Math.abs( min[ 1 ] ), Math.abs( max[ 1 ] ) ) );\n\t\t\t\t\tvector.setZ( Math.max( Math.abs( min[ 2 ] ), Math.abs( max[ 2 ] ) ) );\n\n\n\t\t\t\t\tif ( accessor.normalized ) {\n\n\t\t\t\t\t\tconst boxScale = getNormalizedComponentScale( WEBGL_COMPONENT_TYPES[ accessor.componentType ] );\n\t\t\t\t\t\tvector.multiplyScalar( boxScale );\n\n\t\t\t\t\t}\n\n\t\t\t\t\t// Note: this assumes that the sum of all weights is at most 1. This isn't quite correct - it's more conservative\n\t\t\t\t\t// to assume that each target can have a max weight of 1. However, for some use cases - notably, when morph targets\n\t\t\t\t\t// are used to implement key-frame animations and as such only two are active at a time - this results in very large\n\t\t\t\t\t// boxes. So for now we make a box that's sometimes a touch too small but is hopefully mostly of reasonable size.\n\t\t\t\t\tmaxDisplacement.max( vector );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconsole.warn( 'THREE.GLTFLoader: Missing min/max properties for accessor POSITION.' );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t// As per comment above this box isn't conservative, but has a reasonable size for a very large number of morph targets.\n\t\tbox.expandByVector( maxDisplacement );\n\n\t}\n\n\tgeometry.boundingBox = box;\n\n\tconst sphere = new Sphere();\n\n\tbox.getCenter( sphere.center );\n\tsphere.radius = box.min.distanceTo( box.max ) / 2;\n\n\tgeometry.boundingSphere = sphere;\n\n}\n\n/**\n *\n * @private\n * @param {BufferGeometry} geometry\n * @param {GLTF.Primitive} primitiveDef\n * @param {GLTFParser} parser\n * @return {Promise<BufferGeometry>}\n */\nfunction addPrimitiveAttributes( geometry, primitiveDef, parser ) {\n\n\tconst attributes = primitiveDef.attributes;\n\n\tconst pending = [];\n\n\tfunction assignAttributeAccessor( accessorIndex, attributeName ) {\n\n\t\treturn parser.getDependency( 'accessor', accessorIndex )\n\t\t\t.then( function ( accessor ) {\n\n\t\t\t\tgeometry.setAttribute( attributeName, accessor );\n\n\t\t\t} );\n\n\t}\n\n\tfor ( const gltfAttributeName in attributes ) {\n\n\t\tconst threeAttributeName = ATTRIBUTES[ gltfAttributeName ] || gltfAttributeName.toLowerCase();\n\n\t\t// Skip attributes already provided by e.g. Draco extension.\n\t\tif ( threeAttributeName in geometry.attributes ) continue;\n\n\t\tpending.push( assignAttributeAccessor( attributes[ gltfAttributeName ], threeAttributeName ) );\n\n\t}\n\n\tif ( primitiveDef.indices !== undefined && ! geometry.index ) {\n\n\t\tconst accessor = parser.getDependency( 'accessor', primitiveDef.indices ).then( function ( accessor ) {\n\n\t\t\tgeometry.setIndex( accessor );\n\n\t\t} );\n\n\t\tpending.push( accessor );\n\n\t}\n\n\tif ( ColorManagement.workingColorSpace !== LinearSRGBColorSpace && 'COLOR_0' in attributes ) {\n\n\t\tconsole.warn( `THREE.GLTFLoader: Converting vertex colors from \"srgb-linear\" to \"${ColorManagement.workingColorSpace}\" not supported.` );\n\n\t}\n\n\tassignExtrasToUserData( geometry, primitiveDef );\n\n\tcomputeBounds( geometry, primitiveDef, parser );\n\n\treturn Promise.all( pending ).then( function () {\n\n\t\treturn primitiveDef.targets !== undefined\n\t\t\t? addMorphTargets( geometry, primitiveDef.targets, parser )\n\t\t\t: geometry;\n\n\t} );\n\n}\n\n/**\n * Loader result of `GLTFLoader`.\n *\n * @typedef {Object} GLTFLoader~LoadObject\n * @property {Array<AnimationClip>} animations - An array of animation clips.\n * @property {Object} asset - Meta data about the loaded asset.\n * @property {Array<Camera>} cameras - An array of cameras.\n * @property {GLTFParser} parser - A reference to the internal parser.\n * @property {Group} scene - The default scene.\n * @property {Array<Group>} scenes - glTF assets might define multiple scenes.\n * @property {Object} userData - Additional data.\n **/\n\nexport { GLTFLoader };\n"], "mappings": "AAAA,SACCA,aAAa,EACbC,IAAI,EACJC,IAAI,EACJC,eAAe,EACfC,cAAc,EACdC,mBAAmB,EACnBC,KAAK,EACLC,eAAe,EACfC,gBAAgB,EAChBC,UAAU,EACVC,UAAU,EACVC,SAAS,EACTC,KAAK,EACLC,iBAAiB,EACjBC,aAAa,EACbC,iBAAiB,EACjBC,0BAA0B,EAC1BC,WAAW,EACXC,mBAAmB,EACnBC,iBAAiB,EACjBC,IAAI,EACJC,iBAAiB,EACjBC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,wBAAwB,EACxBC,yBAAyB,EACzBC,oBAAoB,EACpBC,MAAM,EACNC,WAAW,EACXC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,IAAI,EACJC,iBAAiB,EACjBC,oBAAoB,EACpBC,oBAAoB,EACpBC,sBAAsB,EACtBC,aAAa,EACbC,yBAAyB,EACzBC,0BAA0B,EAC1BC,mBAAmB,EACnBC,QAAQ,EACRC,kBAAkB,EAClBC,iBAAiB,EACjBC,UAAU,EACVC,MAAM,EACNC,cAAc,EACdC,eAAe,EACfC,UAAU,EACVC,uBAAuB,EACvBC,cAAc,EACdC,QAAQ,EACRC,WAAW,EACXC,MAAM,EACNC,SAAS,EACTC,OAAO,EACPC,aAAa,EACbC,mBAAmB,EACnBC,qBAAqB,EACrBC,OAAO,EACPC,OAAO,EACPC,mBAAmB,EACnBC,cAAc,EACdC,wBAAwB,QAClB,OAAO;AACd,SAASC,mBAAmB,QAAQ,iCAAiC;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,SAAStC,MAAM,CAAC;EAE/B;AACD;AACA;AACA;AACA;EACCuC,WAAWA,CAAEC,OAAO,EAAG;IAEtB,KAAK,CAAEA,OAAQ,CAAC;IAEhB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,cAAc,GAAG,IAAI;IAE1B,IAAI,CAACC,eAAe,GAAG,EAAE;IAEzB,IAAI,CAACC,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIC,+BAA+B,CAAED,MAAO,CAAC;IAErD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIE,gCAAgC,CAAEF,MAAO,CAAC;IAEtD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIG,0BAA0B,CAAEH,MAAO,CAAC;IAEhD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAII,wBAAwB,CAAEJ,MAAO,CAAC;IAE9C,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIK,wBAAwB,CAAEL,MAAO,CAAC;IAE9C,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIM,2BAA2B,CAAEN,MAAO,CAAC;IAEjD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIO,kCAAkC,CAAEP,MAAO,CAAC;IAExD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIQ,4BAA4B,CAAER,MAAO,CAAC;IAElD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIS,yBAAyB,CAAET,MAAO,CAAC;IAE/C,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIU,sCAAsC,CAAEV,MAAO,CAAC;IAE5D,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIW,8BAA8B,CAAEX,MAAO,CAAC;IAEpD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIY,iCAAiC,CAAEZ,MAAO,CAAC;IAEvD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIa,gCAAgC,CAAEb,MAAO,CAAC;IAEtD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIc,0BAA0B,CAAEd,MAAO,CAAC;IAEhD,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIe,mBAAmB,CAAEf,MAAO,CAAC;IAEzC,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIgB,sBAAsB,CAAEhB,MAAO,CAAC;IAE5C,CAAE,CAAC;IAEH,IAAI,CAACD,QAAQ,CAAE,UAAWC,MAAM,EAAG;MAElC,OAAO,IAAIiB,qBAAqB,CAAEjB,MAAO,CAAC;IAE3C,CAAE,CAAC;EAEJ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACCkB,IAAIA,CAAEC,GAAG,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAG;IAExC,MAAMC,KAAK,GAAG,IAAI;IAElB,IAAIC,YAAY;IAEhB,IAAK,IAAI,CAACA,YAAY,KAAK,EAAE,EAAG;MAE/BA,YAAY,GAAG,IAAI,CAACA,YAAY;IAEjC,CAAC,MAAM,IAAK,IAAI,CAACC,IAAI,KAAK,EAAE,EAAG;MAE9B;MACA;MACA;MACA;MACA;MACA,MAAMC,WAAW,GAAGvE,WAAW,CAACwE,cAAc,CAAER,GAAI,CAAC;MACrDK,YAAY,GAAGrE,WAAW,CAACyE,UAAU,CAAEF,WAAW,EAAE,IAAI,CAACD,IAAK,CAAC;IAEhE,CAAC,MAAM;MAEND,YAAY,GAAGrE,WAAW,CAACwE,cAAc,CAAER,GAAI,CAAC;IAEjD;;IAEA;IACA;IACA;IACA,IAAI,CAACzB,OAAO,CAACmC,SAAS,CAAEV,GAAI,CAAC;IAE7B,MAAMW,QAAQ,GAAG,SAAAA,CAAWC,CAAC,EAAG;MAE/B,IAAKT,OAAO,EAAG;QAEdA,OAAO,CAAES,CAAE,CAAC;MAEb,CAAC,MAAM;QAENC,OAAO,CAACC,KAAK,CAAEF,CAAE,CAAC;MAEnB;MAEAR,KAAK,CAAC7B,OAAO,CAACwC,SAAS,CAAEf,GAAI,CAAC;MAC9BI,KAAK,CAAC7B,OAAO,CAACyC,OAAO,CAAEhB,GAAI,CAAC;IAE7B,CAAC;IAED,MAAMiB,MAAM,GAAG,IAAIpG,UAAU,CAAE,IAAI,CAAC0D,OAAQ,CAAC;IAE7C0C,MAAM,CAACC,OAAO,CAAE,IAAI,CAACZ,IAAK,CAAC;IAC3BW,MAAM,CAACE,eAAe,CAAE,aAAc,CAAC;IACvCF,MAAM,CAACG,gBAAgB,CAAE,IAAI,CAACC,aAAc,CAAC;IAC7CJ,MAAM,CAACK,kBAAkB,CAAE,IAAI,CAACC,eAAgB,CAAC;IAEjDN,MAAM,CAAClB,IAAI,CAAEC,GAAG,EAAE,UAAWwB,IAAI,EAAG;MAEnC,IAAI;QAEHpB,KAAK,CAACqB,KAAK,CAAED,IAAI,EAAEnB,YAAY,EAAE,UAAWqB,IAAI,EAAG;UAElDzB,MAAM,CAAEyB,IAAK,CAAC;UAEdtB,KAAK,CAAC7B,OAAO,CAACyC,OAAO,CAAEhB,GAAI,CAAC;QAE7B,CAAC,EAAEW,QAAS,CAAC;MAEd,CAAC,CAAC,OAAQC,CAAC,EAAG;QAEbD,QAAQ,CAAEC,CAAE,CAAC;MAEd;IAED,CAAC,EAAEV,UAAU,EAAES,QAAS,CAAC;EAE1B;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACCgB,cAAcA,CAAEnD,WAAW,EAAG;IAE7B,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,OAAO,IAAI;EAEZ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACCoD,aAAaA,CAAEnD,UAAU,EAAG;IAE3B,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B,OAAO,IAAI;EAEZ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACCoD,iBAAiBA,CAAEnD,cAAc,EAAG;IAEnC,IAAI,CAACA,cAAc,GAAGA,cAAc;IACpC,OAAO,IAAI;EAEZ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACCE,QAAQA,CAAEkD,QAAQ,EAAG;IAEpB,IAAK,IAAI,CAACnD,eAAe,CAACoD,OAAO,CAAED,QAAS,CAAC,KAAK,CAAE,CAAC,EAAG;MAEvD,IAAI,CAACnD,eAAe,CAACqD,IAAI,CAAEF,QAAS,CAAC;IAEtC;IAEA,OAAO,IAAI;EAEZ;;EAEA;AACD;AACA;AACA;AACA;AACA;EACCG,UAAUA,CAAEH,QAAQ,EAAG;IAEtB,IAAK,IAAI,CAACnD,eAAe,CAACoD,OAAO,CAAED,QAAS,CAAC,KAAK,CAAE,CAAC,EAAG;MAEvD,IAAI,CAACnD,eAAe,CAACuD,MAAM,CAAE,IAAI,CAACvD,eAAe,CAACoD,OAAO,CAAED,QAAS,CAAC,EAAE,CAAE,CAAC;IAE3E;IAEA,OAAO,IAAI;EAEZ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACCL,KAAKA,CAAED,IAAI,EAAElB,IAAI,EAAEL,MAAM,EAAEE,OAAO,EAAG;IAEpC,IAAIgC,IAAI;IACR,MAAMC,UAAU,GAAG,CAAC,CAAC;IACrB,MAAMC,OAAO,GAAG,CAAC,CAAC;IAClB,MAAMC,WAAW,GAAG,IAAIC,WAAW,CAAC,CAAC;IAErC,IAAK,OAAOf,IAAI,KAAK,QAAQ,EAAG;MAE/BW,IAAI,GAAGK,IAAI,CAACf,KAAK,CAAED,IAAK,CAAC;IAE1B,CAAC,MAAM,IAAKA,IAAI,YAAYiB,WAAW,EAAG;MAEzC,MAAMC,KAAK,GAAGJ,WAAW,CAACK,MAAM,CAAE,IAAIC,UAAU,CAAEpB,IAAI,EAAE,CAAC,EAAE,CAAE,CAAE,CAAC;MAEhE,IAAKkB,KAAK,KAAKG,6BAA6B,EAAG;QAE9C,IAAI;UAEHT,UAAU,CAAEU,UAAU,CAACC,eAAe,CAAE,GAAG,IAAIC,mBAAmB,CAAExB,IAAK,CAAC;QAE3E,CAAC,CAAC,OAAQV,KAAK,EAAG;UAEjB,IAAKX,OAAO,EAAGA,OAAO,CAAEW,KAAM,CAAC;UAC/B;QAED;QAEAqB,IAAI,GAAGK,IAAI,CAACf,KAAK,CAAEW,UAAU,CAAEU,UAAU,CAACC,eAAe,CAAE,CAACE,OAAQ,CAAC;MAEtE,CAAC,MAAM;QAENd,IAAI,GAAGK,IAAI,CAACf,KAAK,CAAEa,WAAW,CAACK,MAAM,CAAEnB,IAAK,CAAE,CAAC;MAEhD;IAED,CAAC,MAAM;MAENW,IAAI,GAAGX,IAAI;IAEZ;IAEA,IAAKW,IAAI,CAACe,KAAK,KAAKC,SAAS,IAAIhB,IAAI,CAACe,KAAK,CAACE,OAAO,CAAE,CAAC,CAAE,GAAG,CAAC,EAAG;MAE9D,IAAKjD,OAAO,EAAGA,OAAO,CAAE,IAAIkD,KAAK,CAAE,yEAA0E,CAAE,CAAC;MAChH;IAED;IAEA,MAAMxE,MAAM,GAAG,IAAIyE,UAAU,CAAEnB,IAAI,EAAE;MAEpC7B,IAAI,EAAEA,IAAI,IAAI,IAAI,CAACD,YAAY,IAAI,EAAE;MACrCkD,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BlC,aAAa,EAAE,IAAI,CAACA,aAAa;MACjC9C,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBE,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,cAAc,EAAE,IAAI,CAACA;IAEtB,CAAE,CAAC;IAEHG,MAAM,CAAC2E,UAAU,CAACpC,gBAAgB,CAAE,IAAI,CAACC,aAAc,CAAC;IAExD,KAAM,IAAIoC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC9E,eAAe,CAAC+E,MAAM,EAAED,CAAC,EAAG,EAAG;MAExD,MAAME,MAAM,GAAG,IAAI,CAAChF,eAAe,CAAE8E,CAAC,CAAE,CAAE5E,MAAO,CAAC;MAElD,IAAK,CAAE8E,MAAM,CAACC,IAAI,EAAG/C,OAAO,CAACC,KAAK,CAAE,sDAAuD,CAAC;MAE5FuB,OAAO,CAAEsB,MAAM,CAACC,IAAI,CAAE,GAAGD,MAAM;;MAE/B;MACA;MACA;MACA;MACAvB,UAAU,CAAEuB,MAAM,CAACC,IAAI,CAAE,GAAG,IAAI;IAEjC;IAEA,IAAKzB,IAAI,CAAC0B,cAAc,EAAG;MAE1B,KAAM,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,IAAI,CAAC0B,cAAc,CAACH,MAAM,EAAE,EAAGD,CAAC,EAAG;QAEvD,MAAMK,aAAa,GAAG3B,IAAI,CAAC0B,cAAc,CAAEJ,CAAC,CAAE;QAC9C,MAAMM,kBAAkB,GAAG5B,IAAI,CAAC4B,kBAAkB,IAAI,EAAE;QAExD,QAASD,aAAa;UAErB,KAAKhB,UAAU,CAACkB,mBAAmB;YAClC5B,UAAU,CAAE0B,aAAa,CAAE,GAAG,IAAIG,2BAA2B,CAAC,CAAC;YAC/D;UAED,KAAKnB,UAAU,CAACoB,0BAA0B;YACzC9B,UAAU,CAAE0B,aAAa,CAAE,GAAG,IAAIK,iCAAiC,CAAEhC,IAAI,EAAE,IAAI,CAAC3D,WAAY,CAAC;YAC7F;UAED,KAAKsE,UAAU,CAACsB,qBAAqB;YACpChC,UAAU,CAAE0B,aAAa,CAAE,GAAG,IAAIO,6BAA6B,CAAC,CAAC;YACjE;UAED,KAAKvB,UAAU,CAACwB,qBAAqB;YACpClC,UAAU,CAAE0B,aAAa,CAAE,GAAG,IAAIS,6BAA6B,CAAC,CAAC;YACjE;UAED;YAEC,IAAKR,kBAAkB,CAAChC,OAAO,CAAE+B,aAAc,CAAC,IAAI,CAAC,IAAIzB,OAAO,CAAEyB,aAAa,CAAE,KAAKX,SAAS,EAAG;cAEjGtC,OAAO,CAAC2D,IAAI,CAAE,uCAAuC,GAAGV,aAAa,GAAG,IAAK,CAAC;YAE/E;QAEF;MAED;IAED;IAEAjF,MAAM,CAAC4F,aAAa,CAAErC,UAAW,CAAC;IAClCvD,MAAM,CAAC6F,UAAU,CAAErC,OAAQ,CAAC;IAC5BxD,MAAM,CAAC4C,KAAK,CAAExB,MAAM,EAAEE,OAAQ,CAAC;EAEhC;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACCwE,UAAUA,CAAEnD,IAAI,EAAElB,IAAI,EAAG;IAExB,MAAMF,KAAK,GAAG,IAAI;IAElB,OAAO,IAAIwE,OAAO,CAAE,UAAWC,OAAO,EAAEC,MAAM,EAAG;MAEhD1E,KAAK,CAACqB,KAAK,CAAED,IAAI,EAAElB,IAAI,EAAEuE,OAAO,EAAEC,MAAO,CAAC;IAE3C,CAAE,CAAC;EAEJ;AAED;;AAEA;;AAEA,SAASC,YAAYA,CAAA,EAAG;EAEvB,IAAIC,OAAO,GAAG,CAAC,CAAC;EAEhB,OAAO;IAENC,GAAG,EAAE,SAAAA,CAAWC,GAAG,EAAG;MAErB,OAAOF,OAAO,CAAEE,GAAG,CAAE;IAEtB,CAAC;IAEDC,GAAG,EAAE,SAAAA,CAAWD,GAAG,EAAEE,MAAM,EAAG;MAE7BJ,OAAO,CAAEE,GAAG,CAAE,GAAGE,MAAM;IAExB,CAAC;IAEDC,MAAM,EAAE,SAAAA,CAAWH,GAAG,EAAG;MAExB,OAAOF,OAAO,CAAEE,GAAG,CAAE;IAEtB,CAAC;IAEDI,SAAS,EAAE,SAAAA,CAAA,EAAY;MAEtBN,OAAO,GAAG,CAAC,CAAC;IAEb;EAED,CAAC;AAEF;;AAEA;AACA;AACA;;AAEA,MAAMlC,UAAU,GAAG;EAClBC,eAAe,EAAE,iBAAiB;EAClCmB,0BAA0B,EAAE,4BAA4B;EACxDqB,mBAAmB,EAAE,qBAAqB;EAC1CC,uBAAuB,EAAE,yBAAyB;EAClDC,wBAAwB,EAAE,0BAA0B;EACpDC,iBAAiB,EAAE,mBAAmB;EACtCC,mBAAmB,EAAE,qBAAqB;EAC1CC,sBAAsB,EAAE,wBAAwB;EAChDC,0BAA0B,EAAE,4BAA4B;EACxDC,yBAAyB,EAAE,2BAA2B;EACtDC,wBAAwB,EAAE,0BAA0B;EACpD/B,mBAAmB,EAAE,qBAAqB;EAC1CgC,oBAAoB,EAAE,sBAAsB;EAC5CC,kBAAkB,EAAE,oBAAoB;EACxC7B,qBAAqB,EAAE,uBAAuB;EAC9CE,qBAAqB,EAAE,uBAAuB;EAC9C4B,+BAA+B,EAAE,iCAAiC;EAClEC,kBAAkB,EAAE,oBAAoB;EACxCC,gBAAgB,EAAE,kBAAkB;EACpCC,gBAAgB,EAAE,kBAAkB;EACpCC,uBAAuB,EAAE,yBAAyB;EAClDC,uBAAuB,EAAE;AAC1B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM3G,mBAAmB,CAAC;EAEzBtB,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+E,IAAI,GAAGd,UAAU,CAACyC,mBAAmB;;IAE1C;IACA,IAAI,CAACiB,KAAK,GAAG;MAAEC,IAAI,EAAE,CAAC,CAAC;MAAEC,IAAI,EAAE,CAAC;IAAE,CAAC;EAEpC;EAEAC,SAASA,CAAA,EAAG;IAEX,MAAM9H,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAM+H,QAAQ,GAAG,IAAI,CAAC/H,MAAM,CAACsD,IAAI,CAAC0E,KAAK,IAAI,EAAE;IAE7C,KAAM,IAAIC,SAAS,GAAG,CAAC,EAAEC,UAAU,GAAGH,QAAQ,CAAClD,MAAM,EAAEoD,SAAS,GAAGC,UAAU,EAAED,SAAS,EAAG,EAAG;MAE7F,MAAME,OAAO,GAAGJ,QAAQ,CAAEE,SAAS,CAAE;MAErC,IAAKE,OAAO,CAAC5E,UAAU,IAClB4E,OAAO,CAAC5E,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,IAC/BoD,OAAO,CAAC5E,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,CAACqD,KAAK,KAAK9D,SAAS,EAAG;QAE1DtE,MAAM,CAACqI,WAAW,CAAE,IAAI,CAACV,KAAK,EAAEQ,OAAO,CAAC5E,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,CAACqD,KAAM,CAAC;MAExE;IAED;EAED;EAEAE,UAAUA,CAAEC,UAAU,EAAG;IAExB,MAAMvI,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMwI,QAAQ,GAAG,QAAQ,GAAGD,UAAU;IACtC,IAAIE,UAAU,GAAGzI,MAAM,CAAC2H,KAAK,CAACvB,GAAG,CAAEoC,QAAS,CAAC;IAE7C,IAAKC,UAAU,EAAG,OAAOA,UAAU;IAEnC,MAAMnF,IAAI,GAAGtD,MAAM,CAACsD,IAAI;IACxB,MAAMC,UAAU,GAAKD,IAAI,CAACC,UAAU,IAAID,IAAI,CAACC,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,IAAM,CAAC,CAAC;IAC5E,MAAM2D,SAAS,GAAGnF,UAAU,CAACoF,MAAM,IAAI,EAAE;IACzC,MAAMC,QAAQ,GAAGF,SAAS,CAAEH,UAAU,CAAE;IACxC,IAAIM,SAAS;IAEb,MAAMC,KAAK,GAAG,IAAIlN,KAAK,CAAE,QAAS,CAAC;IAEnC,IAAKgN,QAAQ,CAACE,KAAK,KAAKxE,SAAS,EAAGwE,KAAK,CAACC,MAAM,CAAEH,QAAQ,CAACE,KAAK,CAAE,CAAC,CAAE,EAAEF,QAAQ,CAACE,KAAK,CAAE,CAAC,CAAE,EAAEF,QAAQ,CAACE,KAAK,CAAE,CAAC,CAAE,EAAE7L,oBAAqB,CAAC;IAEvI,MAAM+L,KAAK,GAAGJ,QAAQ,CAACI,KAAK,KAAK1E,SAAS,GAAGsE,QAAQ,CAACI,KAAK,GAAG,CAAC;IAE/D,QAASJ,QAAQ,CAACK,IAAI;MAErB,KAAK,aAAa;QACjBJ,SAAS,GAAG,IAAI/M,gBAAgB,CAAEgN,KAAM,CAAC;QACzCD,SAAS,CAACK,MAAM,CAACC,QAAQ,CAACC,GAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAE,CAAC;QAC1CP,SAAS,CAACvC,GAAG,CAAEuC,SAAS,CAACK,MAAO,CAAC;QACjC;MAED,KAAK,OAAO;QACXL,SAAS,GAAG,IAAI1K,UAAU,CAAE2K,KAAM,CAAC;QACnCD,SAAS,CAACQ,QAAQ,GAAGL,KAAK;QAC1B;MAED,KAAK,MAAM;QACVH,SAAS,GAAG,IAAIhK,SAAS,CAAEiK,KAAM,CAAC;QAClCD,SAAS,CAACQ,QAAQ,GAAGL,KAAK;QAC1B;QACAJ,QAAQ,CAACU,IAAI,GAAGV,QAAQ,CAACU,IAAI,IAAI,CAAC,CAAC;QACnCV,QAAQ,CAACU,IAAI,CAACC,cAAc,GAAGX,QAAQ,CAACU,IAAI,CAACC,cAAc,KAAKjF,SAAS,GAAGsE,QAAQ,CAACU,IAAI,CAACC,cAAc,GAAG,CAAC;QAC5GX,QAAQ,CAACU,IAAI,CAACE,cAAc,GAAGZ,QAAQ,CAACU,IAAI,CAACE,cAAc,KAAKlF,SAAS,GAAGsE,QAAQ,CAACU,IAAI,CAACE,cAAc,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;QACxHb,SAAS,CAACc,KAAK,GAAGf,QAAQ,CAACU,IAAI,CAACE,cAAc;QAC9CX,SAAS,CAACe,QAAQ,GAAG,GAAG,GAAGhB,QAAQ,CAACU,IAAI,CAACC,cAAc,GAAGX,QAAQ,CAACU,IAAI,CAACE,cAAc;QACtFX,SAAS,CAACK,MAAM,CAACC,QAAQ,CAACC,GAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAE,CAAC;QAC1CP,SAAS,CAACvC,GAAG,CAAEuC,SAAS,CAACK,MAAO,CAAC;QACjC;MAED;QACC,MAAM,IAAI1E,KAAK,CAAE,2CAA2C,GAAGoE,QAAQ,CAACK,IAAK,CAAC;IAEhF;;IAEA;IACA;IACAJ,SAAS,CAACM,QAAQ,CAACC,GAAG,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;IAEjCS,sBAAsB,CAAEhB,SAAS,EAAED,QAAS,CAAC;IAE7C,IAAKA,QAAQ,CAACkB,SAAS,KAAKxF,SAAS,EAAGuE,SAAS,CAACiB,SAAS,GAAGlB,QAAQ,CAACkB,SAAS;IAEhFjB,SAAS,CAAC9D,IAAI,GAAG/E,MAAM,CAAC+J,gBAAgB,CAAEnB,QAAQ,CAAC7D,IAAI,IAAM,QAAQ,GAAGwD,UAAa,CAAC;IAEtFE,UAAU,GAAG1C,OAAO,CAACC,OAAO,CAAE6C,SAAU,CAAC;IAEzC7I,MAAM,CAAC2H,KAAK,CAACrB,GAAG,CAAEkC,QAAQ,EAAEC,UAAW,CAAC;IAExC,OAAOA,UAAU;EAElB;EAEAuB,aAAaA,CAAEf,IAAI,EAAEgB,KAAK,EAAG;IAE5B,IAAKhB,IAAI,KAAK,OAAO,EAAG;IAExB,OAAO,IAAI,CAACX,UAAU,CAAE2B,KAAM,CAAC;EAEhC;EAEAC,oBAAoBA,CAAEjC,SAAS,EAAG;IAEjC,MAAMkC,IAAI,GAAG,IAAI;IACjB,MAAMnK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMsD,IAAI,GAAGtD,MAAM,CAACsD,IAAI;IACxB,MAAM6E,OAAO,GAAG7E,IAAI,CAAC0E,KAAK,CAAEC,SAAS,CAAE;IACvC,MAAMW,QAAQ,GAAKT,OAAO,CAAC5E,UAAU,IAAI4E,OAAO,CAAC5E,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,IAAM,CAAC,CAAC;IAChF,MAAMwD,UAAU,GAAGK,QAAQ,CAACR,KAAK;IAEjC,IAAKG,UAAU,KAAKjE,SAAS,EAAG,OAAO,IAAI;IAE3C,OAAO,IAAI,CAACgE,UAAU,CAAEC,UAAW,CAAC,CAAC6B,IAAI,CAAE,UAAWhC,KAAK,EAAG;MAE7D,OAAOpI,MAAM,CAACqK,WAAW,CAAEF,IAAI,CAACxC,KAAK,EAAEY,UAAU,EAAEH,KAAM,CAAC;IAE3D,CAAE,CAAC;EAEJ;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMhD,2BAA2B,CAAC;EAEjC3F,WAAWA,CAAA,EAAG;IAEb,IAAI,CAACsF,IAAI,GAAGd,UAAU,CAACkB,mBAAmB;EAE3C;EAEAmF,eAAeA,CAAA,EAAG;IAEjB,OAAO9M,iBAAiB;EAEzB;EAEA+M,YAAYA,CAAEC,cAAc,EAAEC,WAAW,EAAEzK,MAAM,EAAG;IAEnD,MAAM0K,OAAO,GAAG,EAAE;IAElBF,cAAc,CAAC1B,KAAK,GAAG,IAAIlN,KAAK,CAAE,GAAG,EAAE,GAAG,EAAE,GAAI,CAAC;IACjD4O,cAAc,CAACG,OAAO,GAAG,GAAG;IAE5B,MAAMC,iBAAiB,GAAGH,WAAW,CAACI,oBAAoB;IAE1D,IAAKD,iBAAiB,EAAG;MAExB,IAAKE,KAAK,CAACC,OAAO,CAAEH,iBAAiB,CAACI,eAAgB,CAAC,EAAG;QAEzD,MAAMC,KAAK,GAAGL,iBAAiB,CAACI,eAAe;QAE/CR,cAAc,CAAC1B,KAAK,CAACC,MAAM,CAAEkC,KAAK,CAAE,CAAC,CAAE,EAAEA,KAAK,CAAE,CAAC,CAAE,EAAEA,KAAK,CAAE,CAAC,CAAE,EAAEhO,oBAAqB,CAAC;QACvFuN,cAAc,CAACG,OAAO,GAAGM,KAAK,CAAE,CAAC,CAAE;MAEpC;MAEA,IAAKL,iBAAiB,CAACM,gBAAgB,KAAK5G,SAAS,EAAG;QAEvDoG,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,KAAK,EAAEI,iBAAiB,CAACM,gBAAgB,EAAE7L,cAAe,CAAE,CAAC;MAElH;IAED;IAEA,OAAO0G,OAAO,CAACqF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMhK,sCAAsC,CAAC;EAE5CjB,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+E,IAAI,GAAGd,UAAU,CAACoD,+BAA+B;EAEvD;EAEAgE,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMxK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAMwF,gBAAgB,GAAGf,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,CAACyG,gBAAgB;IAE7E,IAAKA,gBAAgB,KAAKlH,SAAS,EAAG;MAErCkG,cAAc,CAACiB,iBAAiB,GAAGD,gBAAgB;IAEpD;IAEA,OAAOzF,OAAO,CAACC,OAAO,CAAC,CAAC;EAEzB;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM/F,+BAA+B,CAAC;EAErCR,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+E,IAAI,GAAGd,UAAU,CAAC0C,uBAAuB;EAE/C;EAEA2D,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMtL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOtH,oBAAoB;EAE5B;EAEA4N,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMxK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAM0E,OAAO,GAAG,EAAE;IAElB,MAAMgB,SAAS,GAAGjB,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE;IAErD,IAAK2G,SAAS,CAACC,eAAe,KAAKrH,SAAS,EAAG;MAE9CkG,cAAc,CAACoB,SAAS,GAAGF,SAAS,CAACC,eAAe;IAErD;IAEA,IAAKD,SAAS,CAACG,gBAAgB,KAAKvH,SAAS,EAAG;MAE/CoG,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,cAAc,EAAEkB,SAAS,CAACG,gBAAiB,CAAE,CAAC;IAEnG;IAEA,IAAKH,SAAS,CAACI,wBAAwB,KAAKxH,SAAS,EAAG;MAEvDkG,cAAc,CAACuB,kBAAkB,GAAGL,SAAS,CAACI,wBAAwB;IAEvE;IAEA,IAAKJ,SAAS,CAACM,yBAAyB,KAAK1H,SAAS,EAAG;MAExDoG,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,uBAAuB,EAAEkB,SAAS,CAACM,yBAA0B,CAAE,CAAC;IAErH;IAEA,IAAKN,SAAS,CAACO,sBAAsB,KAAK3H,SAAS,EAAG;MAErDoG,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,oBAAoB,EAAEkB,SAAS,CAACO,sBAAuB,CAAE,CAAC;MAE9G,IAAKP,SAAS,CAACO,sBAAsB,CAACC,KAAK,KAAK5H,SAAS,EAAG;QAE3D,MAAM4H,KAAK,GAAGR,SAAS,CAACO,sBAAsB,CAACC,KAAK;QAEpD1B,cAAc,CAAC2B,oBAAoB,GAAG,IAAIjN,OAAO,CAAEgN,KAAK,EAAEA,KAAM,CAAC;MAElE;IAED;IAEA,OAAOnG,OAAO,CAACqF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMxK,gCAAgC,CAAC;EAEtCT,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+E,IAAI,GAAGd,UAAU,CAAC2C,wBAAwB;EAEhD;EAEA0D,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMtL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOtH,oBAAoB;EAE5B;EAEA4N,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMxK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAM0F,SAAS,GAAGjB,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE;IAErDyF,cAAc,CAAC4B,UAAU,GAAGV,SAAS,CAACU,UAAU,KAAK9H,SAAS,GAAGoH,SAAS,CAACU,UAAU,GAAG,CAAC;IAEzF,OAAOrG,OAAO,CAACC,OAAO,CAAC,CAAC;EAEzB;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMpF,iCAAiC,CAAC;EAEvCnB,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+E,IAAI,GAAGd,UAAU,CAACgD,yBAAyB;EAEjD;EAEAqD,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMtL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOtH,oBAAoB;EAE5B;EAEA4N,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMxK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAM0E,OAAO,GAAG,EAAE;IAElB,MAAMgB,SAAS,GAAGjB,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE;IAErD,IAAK2G,SAAS,CAACW,iBAAiB,KAAK/H,SAAS,EAAG;MAEhDkG,cAAc,CAAC8B,WAAW,GAAGZ,SAAS,CAACW,iBAAiB;IAEzD;IAEA,IAAKX,SAAS,CAACa,kBAAkB,KAAKjI,SAAS,EAAG;MAEjDoG,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,gBAAgB,EAAEkB,SAAS,CAACa,kBAAmB,CAAE,CAAC;IAEvG;IAEA,IAAKb,SAAS,CAACc,cAAc,KAAKlI,SAAS,EAAG;MAE7CkG,cAAc,CAACiC,cAAc,GAAGf,SAAS,CAACc,cAAc;IAEzD;IAEA,IAAKhC,cAAc,CAACkC,yBAAyB,KAAKpI,SAAS,EAAG;MAE7DkG,cAAc,CAACkC,yBAAyB,GAAG,CAAE,GAAG,EAAE,GAAG,CAAE;IAExD;IAEA,IAAKhB,SAAS,CAACiB,2BAA2B,KAAKrI,SAAS,EAAG;MAE1DkG,cAAc,CAACkC,yBAAyB,CAAE,CAAC,CAAE,GAAGhB,SAAS,CAACiB,2BAA2B;IAEtF;IAEA,IAAKjB,SAAS,CAACkB,2BAA2B,KAAKtI,SAAS,EAAG;MAE1DkG,cAAc,CAACkC,yBAAyB,CAAE,CAAC,CAAE,GAAGhB,SAAS,CAACkB,2BAA2B;IAEtF;IAEA,IAAKlB,SAAS,CAACmB,2BAA2B,KAAKvI,SAAS,EAAG;MAE1DoG,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,yBAAyB,EAAEkB,SAAS,CAACmB,2BAA4B,CAAE,CAAC;IAEzH;IAEA,OAAO9G,OAAO,CAACqF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMpK,2BAA2B,CAAC;EAEjCb,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+E,IAAI,GAAGd,UAAU,CAAC6C,mBAAmB;EAE3C;EAEAwD,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMtL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOtH,oBAAoB;EAE5B;EAEA4N,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMxK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAM0E,OAAO,GAAG,EAAE;IAElBF,cAAc,CAACsC,UAAU,GAAG,IAAIlR,KAAK,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;IAChD4O,cAAc,CAACuC,cAAc,GAAG,CAAC;IACjCvC,cAAc,CAACwC,KAAK,GAAG,CAAC;IAExB,MAAMtB,SAAS,GAAGjB,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE;IAErD,IAAK2G,SAAS,CAACuB,gBAAgB,KAAK3I,SAAS,EAAG;MAE/C,MAAM4I,WAAW,GAAGxB,SAAS,CAACuB,gBAAgB;MAC9CzC,cAAc,CAACsC,UAAU,CAAC/D,MAAM,CAAEmE,WAAW,CAAE,CAAC,CAAE,EAAEA,WAAW,CAAE,CAAC,CAAE,EAAEA,WAAW,CAAE,CAAC,CAAE,EAAEjQ,oBAAqB,CAAC;IAE/G;IAEA,IAAKyO,SAAS,CAACyB,oBAAoB,KAAK7I,SAAS,EAAG;MAEnDkG,cAAc,CAACuC,cAAc,GAAGrB,SAAS,CAACyB,oBAAoB;IAE/D;IAEA,IAAKzB,SAAS,CAAC0B,iBAAiB,KAAK9I,SAAS,EAAG;MAEhDoG,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,eAAe,EAAEkB,SAAS,CAAC0B,iBAAiB,EAAE/N,cAAe,CAAE,CAAC;IAErH;IAEA,IAAKqM,SAAS,CAAC2B,qBAAqB,KAAK/I,SAAS,EAAG;MAEpDoG,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,mBAAmB,EAAEkB,SAAS,CAAC2B,qBAAsB,CAAE,CAAC;IAE7G;IAEA,OAAOtH,OAAO,CAACqF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMnK,kCAAkC,CAAC;EAExCd,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+E,IAAI,GAAGd,UAAU,CAAC+C,0BAA0B;EAElD;EAEAsD,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMtL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOtH,oBAAoB;EAE5B;EAEA4N,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMxK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAM0E,OAAO,GAAG,EAAE;IAElB,MAAMgB,SAAS,GAAGjB,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE;IAErD,IAAK2G,SAAS,CAAC4B,kBAAkB,KAAKhJ,SAAS,EAAG;MAEjDkG,cAAc,CAAC+C,YAAY,GAAG7B,SAAS,CAAC4B,kBAAkB;IAE3D;IAEA,IAAK5B,SAAS,CAAC8B,mBAAmB,KAAKlJ,SAAS,EAAG;MAElDoG,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,iBAAiB,EAAEkB,SAAS,CAAC8B,mBAAoB,CAAE,CAAC;IAEzG;IAEA,OAAOzH,OAAO,CAACqF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMlK,4BAA4B,CAAC;EAElCf,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+E,IAAI,GAAGd,UAAU,CAACkD,oBAAoB;EAE5C;EAEAmD,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMtL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOtH,oBAAoB;EAE5B;EAEA4N,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMxK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAM0E,OAAO,GAAG,EAAE;IAElB,MAAMgB,SAAS,GAAGjB,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE;IAErDyF,cAAc,CAACiD,SAAS,GAAG/B,SAAS,CAACgC,eAAe,KAAKpJ,SAAS,GAAGoH,SAAS,CAACgC,eAAe,GAAG,CAAC;IAElG,IAAKhC,SAAS,CAACiC,gBAAgB,KAAKrJ,SAAS,EAAG;MAE/CoG,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,cAAc,EAAEkB,SAAS,CAACiC,gBAAiB,CAAE,CAAC;IAEnG;IAEAnD,cAAc,CAACoD,mBAAmB,GAAGlC,SAAS,CAACkC,mBAAmB,IAAIC,QAAQ;IAE9E,MAAMC,UAAU,GAAGpC,SAAS,CAACqC,gBAAgB,IAAI,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE;IAC5DvD,cAAc,CAACuD,gBAAgB,GAAG,IAAInS,KAAK,CAAC,CAAC,CAACmN,MAAM,CAAE+E,UAAU,CAAE,CAAC,CAAE,EAAEA,UAAU,CAAE,CAAC,CAAE,EAAEA,UAAU,CAAE,CAAC,CAAE,EAAE7Q,oBAAqB,CAAC;IAE/H,OAAO8I,OAAO,CAACqF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMjK,yBAAyB,CAAC;EAE/BhB,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+E,IAAI,GAAGd,UAAU,CAAC4C,iBAAiB;EAEzC;EAEAyD,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMtL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOtH,oBAAoB;EAE5B;EAEA4N,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMxK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAM0F,SAAS,GAAGjB,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE;IAErDyF,cAAc,CAACwD,GAAG,GAAGtC,SAAS,CAACsC,GAAG,KAAK1J,SAAS,GAAGoH,SAAS,CAACsC,GAAG,GAAG,GAAG;IAEtE,OAAOjI,OAAO,CAACC,OAAO,CAAC,CAAC;EAEzB;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMrF,8BAA8B,CAAC;EAEpClB,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+E,IAAI,GAAGd,UAAU,CAAC8C,sBAAsB;EAE9C;EAEAuD,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMtL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOtH,oBAAoB;EAE5B;EAEA4N,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMxK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAM0E,OAAO,GAAG,EAAE;IAElB,MAAMgB,SAAS,GAAGjB,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE;IAErDyF,cAAc,CAACyD,iBAAiB,GAAGvC,SAAS,CAACwC,cAAc,KAAK5J,SAAS,GAAGoH,SAAS,CAACwC,cAAc,GAAG,GAAG;IAE1G,IAAKxC,SAAS,CAACyC,eAAe,KAAK7J,SAAS,EAAG;MAE9CoG,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,sBAAsB,EAAEkB,SAAS,CAACyC,eAAgB,CAAE,CAAC;IAE1G;IAEA,MAAML,UAAU,GAAGpC,SAAS,CAAC0C,mBAAmB,IAAI,CAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE;IAC/D5D,cAAc,CAAC6D,aAAa,GAAG,IAAIzS,KAAK,CAAC,CAAC,CAACmN,MAAM,CAAE+E,UAAU,CAAE,CAAC,CAAE,EAAEA,UAAU,CAAE,CAAC,CAAE,EAAEA,UAAU,CAAE,CAAC,CAAE,EAAE7Q,oBAAqB,CAAC;IAE5H,IAAKyO,SAAS,CAAC4C,oBAAoB,KAAKhK,SAAS,EAAG;MAEnDoG,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,kBAAkB,EAAEkB,SAAS,CAAC4C,oBAAoB,EAAEjP,cAAe,CAAE,CAAC;IAE3H;IAEA,OAAO0G,OAAO,CAACqF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;AAED;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM5J,0BAA0B,CAAC;EAEhCrB,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+E,IAAI,GAAGd,UAAU,CAACqD,kBAAkB;EAE1C;EAEAgD,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMtL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOtH,oBAAoB;EAE5B;EAEA4N,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMxK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAM0E,OAAO,GAAG,EAAE;IAElB,MAAMgB,SAAS,GAAGjB,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE;IAErDyF,cAAc,CAAC+D,SAAS,GAAG7C,SAAS,CAAC8C,UAAU,KAAKlK,SAAS,GAAGoH,SAAS,CAAC8C,UAAU,GAAG,GAAG;IAE1F,IAAK9C,SAAS,CAAC+C,WAAW,KAAKnK,SAAS,EAAG;MAE1CoG,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,SAAS,EAAEkB,SAAS,CAAC+C,WAAY,CAAE,CAAC;IAEzF;IAEA,OAAO1I,OAAO,CAACqF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM7J,gCAAgC,CAAC;EAEtCpB,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+E,IAAI,GAAGd,UAAU,CAACiD,wBAAwB;EAEhD;EAEAoD,eAAeA,CAAEgB,aAAa,EAAG;IAEhC,MAAMtL,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG,OAAO,IAAI;IAEpF,OAAOtH,oBAAoB;EAE5B;EAEA4N,oBAAoBA,CAAEC,aAAa,EAAEd,cAAc,EAAG;IAErD,MAAMxK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMyK,WAAW,GAAGzK,MAAM,CAACsD,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAE1D,IAAK,CAAEb,WAAW,CAAClH,UAAU,IAAI,CAAEkH,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG;MAExE,OAAOgB,OAAO,CAACC,OAAO,CAAC,CAAC;IAEzB;IAEA,MAAM0E,OAAO,GAAG,EAAE;IAElB,MAAMgB,SAAS,GAAGjB,WAAW,CAAClH,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE;IAErD,IAAK2G,SAAS,CAACgD,kBAAkB,KAAKpK,SAAS,EAAG;MAEjDkG,cAAc,CAACmE,UAAU,GAAGjD,SAAS,CAACgD,kBAAkB;IAEzD;IAEA,IAAKhD,SAAS,CAACkD,kBAAkB,KAAKtK,SAAS,EAAG;MAEjDkG,cAAc,CAACoE,kBAAkB,GAAGlD,SAAS,CAACkD,kBAAkB;IAEjE;IAEA,IAAKlD,SAAS,CAACmD,iBAAiB,KAAKvK,SAAS,EAAG;MAEhDoG,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,eAAe,EAAEkB,SAAS,CAACmD,iBAAkB,CAAE,CAAC;IAErG;IAEA,OAAO9I,OAAO,CAACqF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMvK,0BAA0B,CAAC;EAEhCV,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+E,IAAI,GAAGd,UAAU,CAACmD,kBAAkB;EAE1C;EAEA0H,WAAWA,CAAEC,YAAY,EAAG;IAE3B,MAAM/O,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMsD,IAAI,GAAGtD,MAAM,CAACsD,IAAI;IAExB,MAAM0L,UAAU,GAAG1L,IAAI,CAAC2L,QAAQ,CAAEF,YAAY,CAAE;IAEhD,IAAK,CAAEC,UAAU,CAACzL,UAAU,IAAI,CAAEyL,UAAU,CAACzL,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG;MAEtE,OAAO,IAAI;IAEZ;IAEA,MAAM2G,SAAS,GAAGsD,UAAU,CAACzL,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE;IACpD,MAAM3C,MAAM,GAAGpC,MAAM,CAACkP,OAAO,CAACtP,UAAU;IAExC,IAAK,CAAEwC,MAAM,EAAG;MAEf,IAAKkB,IAAI,CAAC4B,kBAAkB,IAAI5B,IAAI,CAAC4B,kBAAkB,CAAChC,OAAO,CAAE,IAAI,CAAC6B,IAAK,CAAC,IAAI,CAAC,EAAG;QAEnF,MAAM,IAAIP,KAAK,CAAE,6EAA8E,CAAC;MAEjG,CAAC,MAAM;QAEN;QACA,OAAO,IAAI;MAEZ;IAED;IAEA,OAAOxE,MAAM,CAACmP,gBAAgB,CAAEJ,YAAY,EAAErD,SAAS,CAAC0D,MAAM,EAAEhN,MAAO,CAAC;EAEzE;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMhC,wBAAwB,CAAC;EAE9BX,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+E,IAAI,GAAGd,UAAU,CAACsD,gBAAgB;EAExC;EAEAuH,WAAWA,CAAEC,YAAY,EAAG;IAE3B,MAAMhK,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAM/E,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMsD,IAAI,GAAGtD,MAAM,CAACsD,IAAI;IAExB,MAAM0L,UAAU,GAAG1L,IAAI,CAAC2L,QAAQ,CAAEF,YAAY,CAAE;IAEhD,IAAK,CAAEC,UAAU,CAACzL,UAAU,IAAI,CAAEyL,UAAU,CAACzL,UAAU,CAAEwB,IAAI,CAAE,EAAG;MAEjE,OAAO,IAAI;IAEZ;IAEA,MAAM2G,SAAS,GAAGsD,UAAU,CAACzL,UAAU,CAAEwB,IAAI,CAAE;IAC/C,MAAMqK,MAAM,GAAG9L,IAAI,CAAC+L,MAAM,CAAE3D,SAAS,CAAC0D,MAAM,CAAE;IAE9C,IAAIhN,MAAM,GAAGpC,MAAM,CAACsP,aAAa;IACjC,IAAKF,MAAM,CAACG,GAAG,EAAG;MAEjB,MAAMC,OAAO,GAAGxP,MAAM,CAACkP,OAAO,CAACxP,OAAO,CAAC+P,UAAU,CAAEL,MAAM,CAACG,GAAI,CAAC;MAC/D,IAAKC,OAAO,KAAK,IAAI,EAAGpN,MAAM,GAAGoN,OAAO;IAEzC;IAEA,OAAOxP,MAAM,CAACmP,gBAAgB,CAAEJ,YAAY,EAAErD,SAAS,CAAC0D,MAAM,EAAEhN,MAAO,CAAC;EAEzE;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM/B,wBAAwB,CAAC;EAE9BZ,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+E,IAAI,GAAGd,UAAU,CAACuD,gBAAgB;EAExC;EAEAsH,WAAWA,CAAEC,YAAY,EAAG;IAE3B,MAAMhK,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAM/E,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMsD,IAAI,GAAGtD,MAAM,CAACsD,IAAI;IAExB,MAAM0L,UAAU,GAAG1L,IAAI,CAAC2L,QAAQ,CAAEF,YAAY,CAAE;IAEhD,IAAK,CAAEC,UAAU,CAACzL,UAAU,IAAI,CAAEyL,UAAU,CAACzL,UAAU,CAAEwB,IAAI,CAAE,EAAG;MAEjE,OAAO,IAAI;IAEZ;IAEA,MAAM2G,SAAS,GAAGsD,UAAU,CAACzL,UAAU,CAAEwB,IAAI,CAAE;IAC/C,MAAMqK,MAAM,GAAG9L,IAAI,CAAC+L,MAAM,CAAE3D,SAAS,CAAC0D,MAAM,CAAE;IAE9C,IAAIhN,MAAM,GAAGpC,MAAM,CAACsP,aAAa;IACjC,IAAKF,MAAM,CAACG,GAAG,EAAG;MAEjB,MAAMC,OAAO,GAAGxP,MAAM,CAACkP,OAAO,CAACxP,OAAO,CAAC+P,UAAU,CAAEL,MAAM,CAACG,GAAI,CAAC;MAC/D,IAAKC,OAAO,KAAK,IAAI,EAAGpN,MAAM,GAAGoN,OAAO;IAEzC;IAEA,OAAOxP,MAAM,CAACmP,gBAAgB,CAAEJ,YAAY,EAAErD,SAAS,CAAC0D,MAAM,EAAEhN,MAAO,CAAC;EAEzE;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMpB,sBAAsB,CAAC;EAE5BvB,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAAC+E,IAAI,GAAGd,UAAU,CAACwD,uBAAuB;IAC9C,IAAI,CAACzH,MAAM,GAAGA,MAAM;EAErB;EAEA0P,cAAcA,CAAEzF,KAAK,EAAG;IAEvB,MAAM3G,IAAI,GAAG,IAAI,CAACtD,MAAM,CAACsD,IAAI;IAC7B,MAAMqM,UAAU,GAAGrM,IAAI,CAACsM,WAAW,CAAE3F,KAAK,CAAE;IAE5C,IAAK0F,UAAU,CAACpM,UAAU,IAAIoM,UAAU,CAACpM,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,EAAG;MAElE,MAAM8K,YAAY,GAAGF,UAAU,CAACpM,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE;MAEvD,MAAM+K,MAAM,GAAG,IAAI,CAAC9P,MAAM,CAACgK,aAAa,CAAE,QAAQ,EAAE6F,YAAY,CAACC,MAAO,CAAC;MACzE,MAAMC,OAAO,GAAG,IAAI,CAAC/P,MAAM,CAACkP,OAAO,CAACrP,cAAc;MAElD,IAAK,CAAEkQ,OAAO,IAAI,CAAEA,OAAO,CAACC,SAAS,EAAG;QAEvC,IAAK1M,IAAI,CAAC4B,kBAAkB,IAAI5B,IAAI,CAAC4B,kBAAkB,CAAChC,OAAO,CAAE,IAAI,CAAC6B,IAAK,CAAC,IAAI,CAAC,EAAG;UAEnF,MAAM,IAAIP,KAAK,CAAE,oFAAqF,CAAC;QAExG,CAAC,MAAM;UAEN;UACA,OAAO,IAAI;QAEZ;MAED;MAEA,OAAOsL,MAAM,CAAC1F,IAAI,CAAE,UAAW6F,GAAG,EAAG;QAEpC,MAAMC,UAAU,GAAGL,YAAY,CAACK,UAAU,IAAI,CAAC;QAC/C,MAAMC,UAAU,GAAGN,YAAY,CAACM,UAAU,IAAI,CAAC;QAE/C,MAAMC,KAAK,GAAGP,YAAY,CAACO,KAAK;QAChC,MAAMC,MAAM,GAAGR,YAAY,CAACS,UAAU;QAEtC,MAAMlB,MAAM,GAAG,IAAIrL,UAAU,CAAEkM,GAAG,EAAEC,UAAU,EAAEC,UAAW,CAAC;QAE5D,IAAKJ,OAAO,CAACQ,qBAAqB,EAAG;UAEpC,OAAOR,OAAO,CAACQ,qBAAqB,CAAEH,KAAK,EAAEC,MAAM,EAAEjB,MAAM,EAAES,YAAY,CAACW,IAAI,EAAEX,YAAY,CAACY,MAAO,CAAC,CAACrG,IAAI,CAAE,UAAW6F,GAAG,EAAG;YAE5H,OAAOA,GAAG,CAACH,MAAM;UAElB,CAAE,CAAC;QAEJ,CAAC,MAAM;UAEN;UACA,OAAOC,OAAO,CAACW,KAAK,CAACtG,IAAI,CAAE,YAAY;YAEtC,MAAMuG,MAAM,GAAG,IAAI/M,WAAW,CAAEwM,KAAK,GAAGC,MAAO,CAAC;YAChDN,OAAO,CAACa,gBAAgB,CAAE,IAAI7M,UAAU,CAAE4M,MAAO,CAAC,EAAEP,KAAK,EAAEC,MAAM,EAAEjB,MAAM,EAAES,YAAY,CAACW,IAAI,EAAEX,YAAY,CAACY,MAAO,CAAC;YACnH,OAAOE,MAAM;UAEd,CAAE,CAAC;QAEJ;MAED,CAAE,CAAC;IAEJ,CAAC,MAAM;MAEN,OAAO,IAAI;IAEZ;EAED;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM1P,qBAAqB,CAAC;EAE3BxB,WAAWA,CAAEO,MAAM,EAAG;IAErB,IAAI,CAAC+E,IAAI,GAAGd,UAAU,CAACyD,uBAAuB;IAC9C,IAAI,CAAC1H,MAAM,GAAGA,MAAM;EAErB;EAEA6Q,cAAcA,CAAE5I,SAAS,EAAG;IAE3B,MAAM3E,IAAI,GAAG,IAAI,CAACtD,MAAM,CAACsD,IAAI;IAC7B,MAAM6E,OAAO,GAAG7E,IAAI,CAAC0E,KAAK,CAAEC,SAAS,CAAE;IAEvC,IAAK,CAAEE,OAAO,CAAC5E,UAAU,IAAI,CAAE4E,OAAO,CAAC5E,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,IAC7DoD,OAAO,CAAC2I,IAAI,KAAKxM,SAAS,EAAG;MAE7B,OAAO,IAAI;IAEZ;IAEA,MAAMyM,OAAO,GAAGzN,IAAI,CAAC0N,MAAM,CAAE7I,OAAO,CAAC2I,IAAI,CAAE;;IAE3C;;IAEA,KAAM,MAAMG,SAAS,IAAIF,OAAO,CAACG,UAAU,EAAG;MAE7C,IAAKD,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACC,SAAS,IAC/CH,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACE,cAAc,IACjDJ,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACG,YAAY,IAC/CL,SAAS,CAACT,IAAI,KAAKlM,SAAS,EAAG;QAEhC,OAAO,IAAI;MAEZ;IAED;IAEA,MAAMuL,YAAY,GAAG1H,OAAO,CAAC5E,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE;IACpD,MAAMwM,aAAa,GAAG1B,YAAY,CAAC2B,UAAU;;IAE7C;;IAEA,MAAM9G,OAAO,GAAG,EAAE;IAClB,MAAM8G,UAAU,GAAG,CAAC,CAAC;IAErB,KAAM,MAAMnL,GAAG,IAAIkL,aAAa,EAAG;MAElC7G,OAAO,CAACvH,IAAI,CAAE,IAAI,CAACnD,MAAM,CAACgK,aAAa,CAAE,UAAU,EAAEuH,aAAa,CAAElL,GAAG,CAAG,CAAC,CAAC+D,IAAI,CAAEqH,QAAQ,IAAI;QAE7FD,UAAU,CAAEnL,GAAG,CAAE,GAAGoL,QAAQ;QAC5B,OAAOD,UAAU,CAAEnL,GAAG,CAAE;MAEzB,CAAE,CAAE,CAAC;IAEN;IAEA,IAAKqE,OAAO,CAAC7F,MAAM,GAAG,CAAC,EAAG;MAEzB,OAAO,IAAI;IAEZ;IAEA6F,OAAO,CAACvH,IAAI,CAAE,IAAI,CAACnD,MAAM,CAAC6Q,cAAc,CAAE5I,SAAU,CAAE,CAAC;IAEvD,OAAOlC,OAAO,CAACqF,GAAG,CAAEV,OAAQ,CAAC,CAACN,IAAI,CAAEsH,OAAO,IAAI;MAE9C,MAAMC,UAAU,GAAGD,OAAO,CAACE,GAAG,CAAC,CAAC;MAChC,MAAMZ,MAAM,GAAGW,UAAU,CAACE,OAAO,GAAGF,UAAU,CAACG,QAAQ,GAAG,CAAEH,UAAU,CAAE;MACxE,MAAMvB,KAAK,GAAGsB,OAAO,CAAE,CAAC,CAAE,CAACtB,KAAK,CAAC,CAAC;MAClC,MAAM2B,eAAe,GAAG,EAAE;MAE1B,KAAM,MAAMjB,IAAI,IAAIE,MAAM,EAAG;QAE5B;QACA,MAAMgB,CAAC,GAAG,IAAI1U,OAAO,CAAC,CAAC;QACvB,MAAM2U,CAAC,GAAG,IAAI9S,OAAO,CAAC,CAAC;QACvB,MAAM+S,CAAC,GAAG,IAAI3T,UAAU,CAAC,CAAC;QAC1B,MAAM4T,CAAC,GAAG,IAAIhT,OAAO,CAAE,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;QAEhC,MAAMiT,aAAa,GAAG,IAAIhW,aAAa,CAAE0U,IAAI,CAACuB,QAAQ,EAAEvB,IAAI,CAACwB,QAAQ,EAAElC,KAAM,CAAC;QAE9E,KAAM,IAAIxL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwL,KAAK,EAAExL,CAAC,EAAG,EAAG;UAElC,IAAK4M,UAAU,CAACe,WAAW,EAAG;YAE7BN,CAAC,CAACO,mBAAmB,CAAEhB,UAAU,CAACe,WAAW,EAAE3N,CAAE,CAAC;UAEnD;UAEA,IAAK4M,UAAU,CAACiB,QAAQ,EAAG;YAE1BP,CAAC,CAACM,mBAAmB,CAAEhB,UAAU,CAACiB,QAAQ,EAAE7N,CAAE,CAAC;UAEhD;UAEA,IAAK4M,UAAU,CAACkB,KAAK,EAAG;YAEvBP,CAAC,CAACK,mBAAmB,CAAEhB,UAAU,CAACkB,KAAK,EAAE9N,CAAE,CAAC;UAE7C;UAEAwN,aAAa,CAACO,WAAW,CAAE/N,CAAC,EAAEoN,CAAC,CAACY,OAAO,CAAEX,CAAC,EAAEC,CAAC,EAAEC,CAAE,CAAE,CAAC;QAErD;;QAEA;QACA,KAAM,MAAMU,aAAa,IAAIrB,UAAU,EAAG;UAEzC,IAAKqB,aAAa,KAAK,UAAU,EAAG;YAEnC,MAAMC,IAAI,GAAGtB,UAAU,CAAEqB,aAAa,CAAE;YACxCT,aAAa,CAACW,aAAa,GAAG,IAAIzT,wBAAwB,CAAEwT,IAAI,CAAC7H,KAAK,EAAE6H,IAAI,CAACE,QAAQ,EAAEF,IAAI,CAACG,UAAW,CAAC;UAEzG,CAAC,MAAM,IAAKJ,aAAa,KAAK,aAAa,IACzCA,aAAa,KAAK,UAAU,IAC5BA,aAAa,KAAK,OAAO,EAAG;YAE7B/B,IAAI,CAACuB,QAAQ,CAACa,YAAY,CAAEL,aAAa,EAAErB,UAAU,CAAEqB,aAAa,CAAG,CAAC;UAEzE;QAED;;QAEA;QACA7U,QAAQ,CAACmV,SAAS,CAACC,IAAI,CAACC,IAAI,CAAEjB,aAAa,EAAEtB,IAAK,CAAC;QAEnD,IAAI,CAAC9Q,MAAM,CAACsT,mBAAmB,CAAElB,aAAc,CAAC;QAEhDL,eAAe,CAAC5O,IAAI,CAAEiP,aAAc,CAAC;MAEtC;MAEA,IAAKT,UAAU,CAACE,OAAO,EAAG;QAEzBF,UAAU,CAAC4B,KAAK,CAAC,CAAC;QAElB5B,UAAU,CAACrL,GAAG,CAAE,GAAIyL,eAAgB,CAAC;QAErC,OAAOJ,UAAU;MAElB;MAEA,OAAOI,eAAe,CAAE,CAAC,CAAE;IAE5B,CAAE,CAAC;EAEJ;AAED;;AAEA;AACA,MAAM/N,6BAA6B,GAAG,MAAM;AAC5C,MAAMwP,8BAA8B,GAAG,EAAE;AACzC,MAAMC,4BAA4B,GAAG;EAAE9P,IAAI,EAAE,UAAU;EAAE+P,GAAG,EAAE;AAAW,CAAC;AAE1E,MAAMvP,mBAAmB,CAAC;EAEzB1E,WAAWA,CAAEkD,IAAI,EAAG;IAEnB,IAAI,CAACoC,IAAI,GAAGd,UAAU,CAACC,eAAe;IACtC,IAAI,CAACE,OAAO,GAAG,IAAI;IACnB,IAAI,CAACuP,IAAI,GAAG,IAAI;IAEhB,MAAMC,UAAU,GAAG,IAAIC,QAAQ,CAAElR,IAAI,EAAE,CAAC,EAAE6Q,8BAA+B,CAAC;IAC1E,MAAM/P,WAAW,GAAG,IAAIC,WAAW,CAAC,CAAC;IAErC,IAAI,CAACoQ,MAAM,GAAG;MACbjQ,KAAK,EAAEJ,WAAW,CAACK,MAAM,CAAE,IAAIC,UAAU,CAAEpB,IAAI,CAACoR,KAAK,CAAE,CAAC,EAAE,CAAE,CAAE,CAAE,CAAC;MACjExP,OAAO,EAAEqP,UAAU,CAACI,SAAS,CAAE,CAAC,EAAE,IAAK,CAAC;MACxCnP,MAAM,EAAE+O,UAAU,CAACI,SAAS,CAAE,CAAC,EAAE,IAAK;IACvC,CAAC;IAED,IAAK,IAAI,CAACF,MAAM,CAACjQ,KAAK,KAAKG,6BAA6B,EAAG;MAE1D,MAAM,IAAIQ,KAAK,CAAE,mDAAoD,CAAC;IAEvE,CAAC,MAAM,IAAK,IAAI,CAACsP,MAAM,CAACvP,OAAO,GAAG,GAAG,EAAG;MAEvC,MAAM,IAAIC,KAAK,CAAE,gDAAiD,CAAC;IAEpE;IAEA,MAAMyP,mBAAmB,GAAG,IAAI,CAACH,MAAM,CAACjP,MAAM,GAAG2O,8BAA8B;IAC/E,MAAMU,SAAS,GAAG,IAAIL,QAAQ,CAAElR,IAAI,EAAE6Q,8BAA+B,CAAC;IACtE,IAAIW,UAAU,GAAG,CAAC;IAElB,OAAQA,UAAU,GAAGF,mBAAmB,EAAG;MAE1C,MAAMG,WAAW,GAAGF,SAAS,CAACF,SAAS,CAAEG,UAAU,EAAE,IAAK,CAAC;MAC3DA,UAAU,IAAI,CAAC;MAEf,MAAME,SAAS,GAAGH,SAAS,CAACF,SAAS,CAAEG,UAAU,EAAE,IAAK,CAAC;MACzDA,UAAU,IAAI,CAAC;MAEf,IAAKE,SAAS,KAAKZ,4BAA4B,CAAC9P,IAAI,EAAG;QAEtD,MAAM2Q,YAAY,GAAG,IAAIvQ,UAAU,CAAEpB,IAAI,EAAE6Q,8BAA8B,GAAGW,UAAU,EAAEC,WAAY,CAAC;QACrG,IAAI,CAAChQ,OAAO,GAAGX,WAAW,CAACK,MAAM,CAAEwQ,YAAa,CAAC;MAElD,CAAC,MAAM,IAAKD,SAAS,KAAKZ,4BAA4B,CAACC,GAAG,EAAG;QAE5D,MAAMxD,UAAU,GAAGsD,8BAA8B,GAAGW,UAAU;QAC9D,IAAI,CAACR,IAAI,GAAGhR,IAAI,CAACoR,KAAK,CAAE7D,UAAU,EAAEA,UAAU,GAAGkE,WAAY,CAAC;MAE/D;;MAEA;;MAEAD,UAAU,IAAIC,WAAW;IAE1B;IAEA,IAAK,IAAI,CAAChQ,OAAO,KAAK,IAAI,EAAG;MAE5B,MAAM,IAAII,KAAK,CAAE,2CAA4C,CAAC;IAE/D;EAED;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMc,iCAAiC,CAAC;EAEvC7F,WAAWA,CAAE6D,IAAI,EAAE3D,WAAW,EAAG;IAEhC,IAAK,CAAEA,WAAW,EAAG;MAEpB,MAAM,IAAI6E,KAAK,CAAE,qDAAsD,CAAC;IAEzE;IAEA,IAAI,CAACO,IAAI,GAAGd,UAAU,CAACoB,0BAA0B;IACjD,IAAI,CAAC/B,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC3D,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACA,WAAW,CAAC4U,OAAO,CAAC,CAAC;EAE3B;EAEAC,eAAeA,CAAEvD,SAAS,EAAEjR,MAAM,EAAG;IAEpC,MAAMsD,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAM3D,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,MAAM8U,eAAe,GAAGxD,SAAS,CAAC1N,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,CAAC4K,UAAU;IACpE,MAAM+E,gBAAgB,GAAGzD,SAAS,CAAC1N,UAAU,CAAE,IAAI,CAACwB,IAAI,CAAE,CAACyM,UAAU;IACrE,MAAMmD,iBAAiB,GAAG,CAAC,CAAC;IAC5B,MAAMC,sBAAsB,GAAG,CAAC,CAAC;IACjC,MAAMC,gBAAgB,GAAG,CAAC,CAAC;IAE3B,KAAM,MAAMhC,aAAa,IAAI6B,gBAAgB,EAAG;MAE/C,MAAMI,kBAAkB,GAAGC,UAAU,CAAElC,aAAa,CAAE,IAAIA,aAAa,CAACmC,WAAW,CAAC,CAAC;MAErFL,iBAAiB,CAAEG,kBAAkB,CAAE,GAAGJ,gBAAgB,CAAE7B,aAAa,CAAE;IAE5E;IAEA,KAAM,MAAMA,aAAa,IAAI5B,SAAS,CAACO,UAAU,EAAG;MAEnD,MAAMsD,kBAAkB,GAAGC,UAAU,CAAElC,aAAa,CAAE,IAAIA,aAAa,CAACmC,WAAW,CAAC,CAAC;MAErF,IAAKN,gBAAgB,CAAE7B,aAAa,CAAE,KAAKvO,SAAS,EAAG;QAEtD,MAAM2Q,WAAW,GAAG3R,IAAI,CAAC4R,SAAS,CAAEjE,SAAS,CAACO,UAAU,CAAEqB,aAAa,CAAE,CAAE;QAC3E,MAAMsC,aAAa,GAAGC,qBAAqB,CAAEH,WAAW,CAACE,aAAa,CAAE;QAExEN,gBAAgB,CAAEC,kBAAkB,CAAE,GAAGK,aAAa,CAACpQ,IAAI;QAC3D6P,sBAAsB,CAAEE,kBAAkB,CAAE,GAAGG,WAAW,CAAChC,UAAU,KAAK,IAAI;MAE/E;IAED;IAEA,OAAOjT,MAAM,CAACgK,aAAa,CAAE,YAAY,EAAEyK,eAAgB,CAAC,CAACrK,IAAI,CAAE,UAAWuF,UAAU,EAAG;MAE1F,OAAO,IAAI5J,OAAO,CAAE,UAAWC,OAAO,EAAEC,MAAM,EAAG;QAEhDtG,WAAW,CAAC0V,eAAe,CAAE1F,UAAU,EAAE,UAAW0C,QAAQ,EAAG;UAE9D,KAAM,MAAMQ,aAAa,IAAIR,QAAQ,CAACb,UAAU,EAAG;YAElD,MAAM8D,SAAS,GAAGjD,QAAQ,CAACb,UAAU,CAAEqB,aAAa,CAAE;YACtD,MAAMI,UAAU,GAAG2B,sBAAsB,CAAE/B,aAAa,CAAE;YAE1D,IAAKI,UAAU,KAAK3O,SAAS,EAAGgR,SAAS,CAACrC,UAAU,GAAGA,UAAU;UAElE;UAEAjN,OAAO,CAAEqM,QAAS,CAAC;QAEpB,CAAC,EAAEsC,iBAAiB,EAAEE,gBAAgB,EAAE5X,oBAAoB,EAAEgJ,MAAO,CAAC;MAEvE,CAAE,CAAC;IAEJ,CAAE,CAAC;EAEJ;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMT,6BAA6B,CAAC;EAEnC/F,WAAWA,CAAA,EAAG;IAEb,IAAI,CAACsF,IAAI,GAAGd,UAAU,CAACsB,qBAAqB;EAE7C;EAEAgQ,aAAaA,CAAEC,OAAO,EAAEC,SAAS,EAAG;IAEnC,IAAK,CAAEA,SAAS,CAACC,QAAQ,KAAKpR,SAAS,IAAImR,SAAS,CAACC,QAAQ,KAAKF,OAAO,CAACG,OAAO,KAC7EF,SAAS,CAACG,MAAM,KAAKtR,SAAS,IAC9BmR,SAAS,CAACI,QAAQ,KAAKvR,SAAS,IAChCmR,SAAS,CAACvJ,KAAK,KAAK5H,SAAS,EAAG;MAEnC;MACA,OAAOkR,OAAO;IAEf;IAEAA,OAAO,GAAGA,OAAO,CAACM,KAAK,CAAC,CAAC;IAEzB,IAAKL,SAAS,CAACC,QAAQ,KAAKpR,SAAS,EAAG;MAEvCkR,OAAO,CAACG,OAAO,GAAGF,SAAS,CAACC,QAAQ;IAErC;IAEA,IAAKD,SAAS,CAACG,MAAM,KAAKtR,SAAS,EAAG;MAErCkR,OAAO,CAACI,MAAM,CAACG,SAAS,CAAEN,SAAS,CAACG,MAAO,CAAC;IAE7C;IAEA,IAAKH,SAAS,CAACI,QAAQ,KAAKvR,SAAS,EAAG;MAEvCkR,OAAO,CAACK,QAAQ,GAAGJ,SAAS,CAACI,QAAQ;IAEtC;IAEA,IAAKJ,SAAS,CAACvJ,KAAK,KAAK5H,SAAS,EAAG;MAEpCkR,OAAO,CAACQ,MAAM,CAACD,SAAS,CAAEN,SAAS,CAACvJ,KAAM,CAAC;IAE5C;IAEAsJ,OAAO,CAACS,WAAW,GAAG,IAAI;IAE1B,OAAOT,OAAO;EAEf;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM9P,6BAA6B,CAAC;EAEnCjG,WAAWA,CAAA,EAAG;IAEb,IAAI,CAACsF,IAAI,GAAGd,UAAU,CAACwB,qBAAqB;EAE7C;AAED;;AAEA;AACA;AACA;;AAEA;AACA;AACA,MAAMyQ,0BAA0B,SAAS3Z,WAAW,CAAC;EAEpDkD,WAAWA,CAAE0W,kBAAkB,EAAEC,YAAY,EAAEC,UAAU,EAAEC,YAAY,EAAG;IAEzE,KAAK,CAAEH,kBAAkB,EAAEC,YAAY,EAAEC,UAAU,EAAEC,YAAa,CAAC;EAEpE;EAEAC,gBAAgBA,CAAEtM,KAAK,EAAG;IAEzB;IACA;;IAEA,MAAM0G,MAAM,GAAG,IAAI,CAAC2F,YAAY;MAC/BE,MAAM,GAAG,IAAI,CAACJ,YAAY;MAC1BK,SAAS,GAAG,IAAI,CAACA,SAAS;MAC1Bb,MAAM,GAAG3L,KAAK,GAAGwM,SAAS,GAAG,CAAC,GAAGA,SAAS;IAE3C,KAAM,IAAI7R,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAK6R,SAAS,EAAE7R,CAAC,EAAG,EAAG;MAExC+L,MAAM,CAAE/L,CAAC,CAAE,GAAG4R,MAAM,CAAEZ,MAAM,GAAGhR,CAAC,CAAE;IAEnC;IAEA,OAAO+L,MAAM;EAEd;EAEA+F,YAAYA,CAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,EAAE,EAAG;IAE7B,MAAMnG,MAAM,GAAG,IAAI,CAAC2F,YAAY;IAChC,MAAME,MAAM,GAAG,IAAI,CAACJ,YAAY;IAChC,MAAM/F,MAAM,GAAG,IAAI,CAACoG,SAAS;IAE7B,MAAMM,OAAO,GAAG1G,MAAM,GAAG,CAAC;IAC1B,MAAM2G,OAAO,GAAG3G,MAAM,GAAG,CAAC;IAE1B,MAAM4G,EAAE,GAAGH,EAAE,GAAGF,EAAE;IAElB,MAAM3E,CAAC,GAAG,CAAE4E,CAAC,GAAGD,EAAE,IAAKK,EAAE;IACzB,MAAMC,EAAE,GAAGjF,CAAC,GAAGA,CAAC;IAChB,MAAMkF,GAAG,GAAGD,EAAE,GAAGjF,CAAC;IAElB,MAAMmF,OAAO,GAAGT,EAAE,GAAGK,OAAO;IAC5B,MAAMK,OAAO,GAAGD,OAAO,GAAGJ,OAAO;IAEjC,MAAMM,EAAE,GAAG,CAAE,CAAC,GAAGH,GAAG,GAAG,CAAC,GAAGD,EAAE;IAC7B,MAAMK,EAAE,GAAGJ,GAAG,GAAGD,EAAE;IACnB,MAAMM,EAAE,GAAG,CAAC,GAAGF,EAAE;IACjB,MAAMG,EAAE,GAAGF,EAAE,GAAGL,EAAE,GAAGjF,CAAC;;IAEtB;IACA;IACA,KAAM,IAAIrN,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAKyL,MAAM,EAAEzL,CAAC,EAAG,EAAG;MAErC,MAAM8S,EAAE,GAAGlB,MAAM,CAAEa,OAAO,GAAGzS,CAAC,GAAGyL,MAAM,CAAE,CAAC,CAAC;MAC3C,MAAMsH,EAAE,GAAGnB,MAAM,CAAEa,OAAO,GAAGzS,CAAC,GAAGmS,OAAO,CAAE,GAAGE,EAAE,CAAC,CAAC;MACjD,MAAMW,EAAE,GAAGpB,MAAM,CAAEY,OAAO,GAAGxS,CAAC,GAAGyL,MAAM,CAAE,CAAC,CAAC;MAC3C,MAAMwH,EAAE,GAAGrB,MAAM,CAAEY,OAAO,GAAGxS,CAAC,CAAE,GAAGqS,EAAE,CAAC,CAAC;;MAEvCtG,MAAM,CAAE/L,CAAC,CAAE,GAAG4S,EAAE,GAAGE,EAAE,GAAGD,EAAE,GAAGE,EAAE,GAAGL,EAAE,GAAGM,EAAE,GAAGL,EAAE,GAAGM,EAAE;IAEpD;IAEA,OAAOlH,MAAM;EAEd;AAED;AAEA,MAAMmH,WAAW,GAAG,IAAIvZ,UAAU,CAAC,CAAC;AAEpC,MAAMwZ,oCAAoC,SAAS7B,0BAA0B,CAAC;EAE7EQ,YAAYA,CAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,EAAE,EAAG;IAE7B,MAAMnG,MAAM,GAAG,KAAK,CAAC+F,YAAY,CAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,EAAG,CAAC;IAElDgB,WAAW,CAAC/B,SAAS,CAAEpF,MAAO,CAAC,CAACqH,SAAS,CAAC,CAAC,CAACC,OAAO,CAAEtH,MAAO,CAAC;IAE7D,OAAOA,MAAM;EAEd;AAED;;AAGA;AACA;AACA;;AAEA;;AAEA,MAAMQ,eAAe,GAAG;EACvB+G,KAAK,EAAE,IAAI;EACX;EACAC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,KAAK;EACbC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,SAAS,EAAE,CAAC;EACZC,UAAU,EAAE,CAAC;EACb1H,SAAS,EAAE,CAAC;EACZC,cAAc,EAAE,CAAC;EACjBC,YAAY,EAAE,CAAC;EACfyH,aAAa,EAAE,IAAI;EACnBC,cAAc,EAAE;AACjB,CAAC;AAED,MAAM5D,qBAAqB,GAAG;EAC7B,IAAI,EAAE6D,SAAS;EACf,IAAI,EAAElV,UAAU;EAChB,IAAI,EAAEmV,UAAU;EAChB,IAAI,EAAEC,WAAW;EACjB,IAAI,EAAEC,WAAW;EACjB,IAAI,EAAEC;AACP,CAAC;AAED,MAAMC,aAAa,GAAG;EACrB,IAAI,EAAE1b,aAAa;EACnB,IAAI,EAAEd,YAAY;EAClB,IAAI,EAAEgB,0BAA0B;EAChC,IAAI,EAAEd,yBAAyB;EAC/B,IAAI,EAAEa,yBAAyB;EAC/B,IAAI,EAAEd;AACP,CAAC;AAED,MAAMwc,eAAe,GAAG;EACvB,KAAK,EAAE5d,mBAAmB;EAC1B,KAAK,EAAEgC,sBAAsB;EAC7B,KAAK,EAAEc;AACR,CAAC;AAED,MAAM+a,gBAAgB,GAAG;EACxB,QAAQ,EAAE,CAAC;EACX,MAAM,EAAE,CAAC;EACT,MAAM,EAAE,CAAC;EACT,MAAM,EAAE,CAAC;EACT,MAAM,EAAE,CAAC;EACT,MAAM,EAAE,CAAC;EACT,MAAM,EAAE;AACT,CAAC;AAED,MAAMzE,UAAU,GAAG;EAClB0E,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,SAAS;EAClBC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,KAAK;EACjBC,OAAO,EAAE,OAAO;EAChBC,SAAS,EAAE,YAAY;EACvBC,QAAQ,EAAE;AACX,CAAC;AAED,MAAMC,eAAe,GAAG;EACvBjO,KAAK,EAAE,OAAO;EACdkO,WAAW,EAAE,UAAU;EACvBvE,QAAQ,EAAE,YAAY;EACtBwE,OAAO,EAAE;AACV,CAAC;AAED,MAAMC,aAAa,GAAG;EACrBC,WAAW,EAAEjW,SAAS;EAAE;EACC;EACzBkU,MAAM,EAAE/b,iBAAiB;EACzB+d,IAAI,EAAEhe;AACP,CAAC;AAED,MAAMie,WAAW,GAAG;EACnBC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE;AACR,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAAElT,KAAK,EAAG;EAEvC,IAAKA,KAAK,CAAE,iBAAiB,CAAE,KAAKrD,SAAS,EAAG;IAE/CqD,KAAK,CAAE,iBAAiB,CAAE,GAAG,IAAIjK,oBAAoB,CAAE;MACtDoL,KAAK,EAAE,QAAQ;MACfgS,QAAQ,EAAE,QAAQ;MAClBC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAE,IAAI;MACfC,IAAI,EAAElf;IACP,CAAE,CAAC;EAEJ;EAEA,OAAO0L,KAAK,CAAE,iBAAiB,CAAE;AAElC;AAEA,SAASyT,8BAA8BA,CAAEC,eAAe,EAAE9U,MAAM,EAAE+U,SAAS,EAAG;EAE7E;;EAEA,KAAM,MAAMvW,IAAI,IAAIuW,SAAS,CAAC/X,UAAU,EAAG;IAE1C,IAAK8X,eAAe,CAAEtW,IAAI,CAAE,KAAKT,SAAS,EAAG;MAE5CiC,MAAM,CAACgV,QAAQ,CAACC,cAAc,GAAGjV,MAAM,CAACgV,QAAQ,CAACC,cAAc,IAAI,CAAC,CAAC;MACrEjV,MAAM,CAACgV,QAAQ,CAACC,cAAc,CAAEzW,IAAI,CAAE,GAAGuW,SAAS,CAAC/X,UAAU,CAAEwB,IAAI,CAAE;IAEtE;EAED;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8E,sBAAsBA,CAAEtD,MAAM,EAAEkV,OAAO,EAAG;EAElD,IAAKA,OAAO,CAACC,MAAM,KAAKpX,SAAS,EAAG;IAEnC,IAAK,OAAOmX,OAAO,CAACC,MAAM,KAAK,QAAQ,EAAG;MAEzCC,MAAM,CAACC,MAAM,CAAErV,MAAM,CAACgV,QAAQ,EAAEE,OAAO,CAACC,MAAO,CAAC;IAEjD,CAAC,MAAM;MAEN1Z,OAAO,CAAC2D,IAAI,CAAE,qDAAqD,GAAG8V,OAAO,CAACC,MAAO,CAAC;IAEvF;EAED;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,eAAeA,CAAExJ,QAAQ,EAAEyJ,OAAO,EAAE9b,MAAM,EAAG;EAErD,IAAI+b,gBAAgB,GAAG,KAAK;EAC5B,IAAIC,cAAc,GAAG,KAAK;EAC1B,IAAIC,aAAa,GAAG,KAAK;EAEzB,KAAM,IAAIrX,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAGJ,OAAO,CAACjX,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;IAEpD,MAAMsE,MAAM,GAAG4S,OAAO,CAAElX,CAAC,CAAE;IAE3B,IAAKsE,MAAM,CAACuQ,QAAQ,KAAKnV,SAAS,EAAGyX,gBAAgB,GAAG,IAAI;IAC5D,IAAK7S,MAAM,CAACwQ,MAAM,KAAKpV,SAAS,EAAG0X,cAAc,GAAG,IAAI;IACxD,IAAK9S,MAAM,CAAC8Q,OAAO,KAAK1V,SAAS,EAAG2X,aAAa,GAAG,IAAI;IAExD,IAAKF,gBAAgB,IAAIC,cAAc,IAAIC,aAAa,EAAG;EAE5D;EAEA,IAAK,CAAEF,gBAAgB,IAAI,CAAEC,cAAc,IAAI,CAAEC,aAAa,EAAG,OAAOlW,OAAO,CAACC,OAAO,CAAEqM,QAAS,CAAC;EAEnG,MAAM8J,wBAAwB,GAAG,EAAE;EACnC,MAAMC,sBAAsB,GAAG,EAAE;EACjC,MAAMC,qBAAqB,GAAG,EAAE;EAEhC,KAAM,IAAIzX,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAGJ,OAAO,CAACjX,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;IAEpD,MAAMsE,MAAM,GAAG4S,OAAO,CAAElX,CAAC,CAAE;IAE3B,IAAKmX,gBAAgB,EAAG;MAEvB,MAAMO,eAAe,GAAGpT,MAAM,CAACuQ,QAAQ,KAAKnV,SAAS,GAClDtE,MAAM,CAACgK,aAAa,CAAE,UAAU,EAAEd,MAAM,CAACuQ,QAAS,CAAC,GACnDpH,QAAQ,CAACb,UAAU,CAACrI,QAAQ;MAE/BgT,wBAAwB,CAAChZ,IAAI,CAAEmZ,eAAgB,CAAC;IAEjD;IAEA,IAAKN,cAAc,EAAG;MAErB,MAAMM,eAAe,GAAGpT,MAAM,CAACwQ,MAAM,KAAKpV,SAAS,GAChDtE,MAAM,CAACgK,aAAa,CAAE,UAAU,EAAEd,MAAM,CAACwQ,MAAO,CAAC,GACjDrH,QAAQ,CAACb,UAAU,CAAC+K,MAAM;MAE7BH,sBAAsB,CAACjZ,IAAI,CAAEmZ,eAAgB,CAAC;IAE/C;IAEA,IAAKL,aAAa,EAAG;MAEpB,MAAMK,eAAe,GAAGpT,MAAM,CAAC8Q,OAAO,KAAK1V,SAAS,GACjDtE,MAAM,CAACgK,aAAa,CAAE,UAAU,EAAEd,MAAM,CAAC8Q,OAAQ,CAAC,GAClD3H,QAAQ,CAACb,UAAU,CAAC1I,KAAK;MAE5BuT,qBAAqB,CAAClZ,IAAI,CAAEmZ,eAAgB,CAAC;IAE9C;EAED;EAEA,OAAOvW,OAAO,CAACqF,GAAG,CAAE,CACnBrF,OAAO,CAACqF,GAAG,CAAE+Q,wBAAyB,CAAC,EACvCpW,OAAO,CAACqF,GAAG,CAAEgR,sBAAuB,CAAC,EACrCrW,OAAO,CAACqF,GAAG,CAAEiR,qBAAsB,CAAC,CACnC,CAAC,CAACjS,IAAI,CAAE,UAAW8K,SAAS,EAAG;IAEhC,MAAMsH,cAAc,GAAGtH,SAAS,CAAE,CAAC,CAAE;IACrC,MAAMuH,YAAY,GAAGvH,SAAS,CAAE,CAAC,CAAE;IACnC,MAAMwH,WAAW,GAAGxH,SAAS,CAAE,CAAC,CAAE;IAElC,IAAK6G,gBAAgB,EAAG1J,QAAQ,CAACsK,eAAe,CAACxT,QAAQ,GAAGqT,cAAc;IAC1E,IAAKR,cAAc,EAAG3J,QAAQ,CAACsK,eAAe,CAACJ,MAAM,GAAGE,YAAY;IACpE,IAAKR,aAAa,EAAG5J,QAAQ,CAACsK,eAAe,CAAC7T,KAAK,GAAG4T,WAAW;IACjErK,QAAQ,CAACuK,oBAAoB,GAAG,IAAI;IAEpC,OAAOvK,QAAQ;EAEhB,CAAE,CAAC;AAEJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwK,kBAAkBA,CAAE/L,IAAI,EAAEC,OAAO,EAAG;EAE5CD,IAAI,CAAC+L,kBAAkB,CAAC,CAAC;EAEzB,IAAK9L,OAAO,CAACsJ,OAAO,KAAK/V,SAAS,EAAG;IAEpC,KAAM,IAAIM,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAGnL,OAAO,CAACsJ,OAAO,CAACxV,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;MAE5DkM,IAAI,CAACgM,qBAAqB,CAAElY,CAAC,CAAE,GAAGmM,OAAO,CAACsJ,OAAO,CAAEzV,CAAC,CAAE;IAEvD;EAED;;EAEA;EACA,IAAKmM,OAAO,CAAC2K,MAAM,IAAI5Q,KAAK,CAACC,OAAO,CAAEgG,OAAO,CAAC2K,MAAM,CAACqB,WAAY,CAAC,EAAG;IAEpE,MAAMA,WAAW,GAAGhM,OAAO,CAAC2K,MAAM,CAACqB,WAAW;IAE9C,IAAKjM,IAAI,CAACgM,qBAAqB,CAACjY,MAAM,KAAKkY,WAAW,CAAClY,MAAM,EAAG;MAE/DiM,IAAI,CAACkM,qBAAqB,GAAG,CAAC,CAAC;MAE/B,KAAM,IAAIpY,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAGa,WAAW,CAAClY,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;QAExDkM,IAAI,CAACkM,qBAAqB,CAAED,WAAW,CAAEnY,CAAC,CAAE,CAAE,GAAGA,CAAC;MAEnD;IAED,CAAC,MAAM;MAEN5C,OAAO,CAAC2D,IAAI,CAAE,sEAAuE,CAAC;IAEvF;EAED;AAED;AAEA,SAASsX,kBAAkBA,CAAEC,YAAY,EAAG;EAE3C,IAAIC,WAAW;EAEf,MAAMC,cAAc,GAAGF,YAAY,CAAC3Z,UAAU,IAAI2Z,YAAY,CAAC3Z,UAAU,CAAEU,UAAU,CAACoB,0BAA0B,CAAE;EAElH,IAAK+X,cAAc,EAAG;IAErBD,WAAW,GAAG,QAAQ,GAAGC,cAAc,CAACzN,UAAU,GAC9C,GAAG,GAAGyN,cAAc,CAACC,OAAO,GAC5B,GAAG,GAAGC,mBAAmB,CAAEF,cAAc,CAAC5L,UAAW,CAAC;EAE3D,CAAC,MAAM;IAEN2L,WAAW,GAAGD,YAAY,CAACG,OAAO,GAAG,GAAG,GAAGC,mBAAmB,CAAEJ,YAAY,CAAC1L,UAAW,CAAC,GAAG,GAAG,GAAG0L,YAAY,CAAC1M,IAAI;EAEpH;EAEA,IAAK0M,YAAY,CAACpB,OAAO,KAAKxX,SAAS,EAAG;IAEzC,KAAM,IAAIM,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAGgB,YAAY,CAACpB,OAAO,CAACjX,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;MAEjEuY,WAAW,IAAI,GAAG,GAAGG,mBAAmB,CAAEJ,YAAY,CAACpB,OAAO,CAAElX,CAAC,CAAG,CAAC;IAEtE;EAED;EAEA,OAAOuY,WAAW;AAEnB;AAEA,SAASG,mBAAmBA,CAAE9L,UAAU,EAAG;EAE1C,IAAI+L,aAAa,GAAG,EAAE;EAEtB,MAAMC,IAAI,GAAG7B,MAAM,CAAC6B,IAAI,CAAEhM,UAAW,CAAC,CAACiM,IAAI,CAAC,CAAC;EAE7C,KAAM,IAAI7Y,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAGsB,IAAI,CAAC3Y,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;IAEjD2Y,aAAa,IAAIC,IAAI,CAAE5Y,CAAC,CAAE,GAAG,GAAG,GAAG4M,UAAU,CAAEgM,IAAI,CAAE5Y,CAAC,CAAE,CAAE,GAAG,GAAG;EAEjE;EAEA,OAAO2Y,aAAa;AAErB;AAEA,SAASG,2BAA2BA,CAAEje,WAAW,EAAG;EAEnD;EACA;;EAEA,QAASA,WAAW;IAEnB,KAAKwZ,SAAS;MACb,OAAO,CAAC,GAAG,GAAG;IAEf,KAAKlV,UAAU;MACd,OAAO,CAAC,GAAG,GAAG;IAEf,KAAKmV,UAAU;MACd,OAAO,CAAC,GAAG,KAAK;IAEjB,KAAKC,WAAW;MACf,OAAO,CAAC,GAAG,KAAK;IAEjB;MACC,MAAM,IAAI3U,KAAK,CAAE,mEAAoE,CAAC;EAExF;AAED;AAEA,SAASmZ,mBAAmBA,CAAEpO,GAAG,EAAG;EAEnC,IAAKA,GAAG,CAACqO,MAAM,CAAE,gBAAiB,CAAC,GAAG,CAAC,IAAIrO,GAAG,CAACqO,MAAM,CAAE,oBAAqB,CAAC,KAAK,CAAC,EAAG,OAAO,YAAY;EACzG,IAAKrO,GAAG,CAACqO,MAAM,CAAE,eAAgB,CAAC,GAAG,CAAC,IAAIrO,GAAG,CAACqO,MAAM,CAAE,oBAAqB,CAAC,KAAK,CAAC,EAAG,OAAO,YAAY;EACxG,IAAKrO,GAAG,CAACqO,MAAM,CAAE,eAAgB,CAAC,GAAG,CAAC,IAAIrO,GAAG,CAACqO,MAAM,CAAE,oBAAqB,CAAC,KAAK,CAAC,EAAG,OAAO,YAAY;EAExG,OAAO,WAAW;AAEnB;AAEA,MAAMC,eAAe,GAAG,IAAIvgB,OAAO,CAAC,CAAC;;AAErC;;AAEA,MAAMmH,UAAU,CAAC;EAEhBhF,WAAWA,CAAE6D,IAAI,GAAG,CAAC,CAAC,EAAE4L,OAAO,GAAG,CAAC,CAAC,EAAG;IAEtC,IAAI,CAAC5L,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;IACpB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAAC0L,OAAO,GAAGA,OAAO;;IAEtB;IACA,IAAI,CAACvH,KAAK,GAAG,IAAIzB,YAAY,CAAC,CAAC;;IAE/B;IACA,IAAI,CAAC4X,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;;IAE7B;IACA,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;;IAExB;IACA,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;;IAEnB;IACA,IAAI,CAACC,SAAS,GAAG;MAAEtW,IAAI,EAAE,CAAC,CAAC;MAAEC,IAAI,EAAE,CAAC;IAAE,CAAC;IACvC,IAAI,CAACsW,WAAW,GAAG;MAAEvW,IAAI,EAAE,CAAC,CAAC;MAAEC,IAAI,EAAE,CAAC;IAAE,CAAC;IACzC,IAAI,CAACuW,UAAU,GAAG;MAAExW,IAAI,EAAE,CAAC,CAAC;MAAEC,IAAI,EAAE,CAAC;IAAE,CAAC;IAExC,IAAI,CAACwW,WAAW,GAAG,CAAC,CAAC;IACrB,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;;IAEtB;IACA,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC;;IAEvB;IACA;;IAEA,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,aAAa,GAAG,CAAE,CAAC;IACvB,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,cAAc,GAAG,CAAE,CAAC;IAExB,IAAK,OAAOC,SAAS,KAAK,WAAW,EAAG;MAEvC,MAAMC,SAAS,GAAGD,SAAS,CAACC,SAAS;MAErCL,QAAQ,GAAG,gCAAgC,CAACM,IAAI,CAAED,SAAU,CAAC,KAAK,IAAI;MACtE,MAAME,WAAW,GAAGF,SAAS,CAACG,KAAK,CAAE,gBAAiB,CAAC;MACvDP,aAAa,GAAGD,QAAQ,IAAIO,WAAW,GAAGE,QAAQ,CAAEF,WAAW,CAAE,CAAC,CAAE,EAAE,EAAG,CAAC,GAAG,CAAE,CAAC;MAEhFL,SAAS,GAAGG,SAAS,CAAC3b,OAAO,CAAE,SAAU,CAAC,GAAG,CAAE,CAAC;MAChDyb,cAAc,GAAGD,SAAS,GAAGG,SAAS,CAACG,KAAK,CAAE,qBAAsB,CAAC,CAAE,CAAC,CAAE,GAAG,CAAE,CAAC;IAEjF;IAEA,IAAK,OAAOE,iBAAiB,KAAK,WAAW,IAAMV,QAAQ,IAAIC,aAAa,GAAG,EAAI,IAAMC,SAAS,IAAIC,cAAc,GAAG,EAAI,EAAG;MAE7H,IAAI,CAACrP,aAAa,GAAG,IAAIvQ,aAAa,CAAE,IAAI,CAACmQ,OAAO,CAACxP,OAAQ,CAAC;IAE/D,CAAC,MAAM;MAEN,IAAI,CAAC4P,aAAa,GAAG,IAAInT,iBAAiB,CAAE,IAAI,CAAC+S,OAAO,CAACxP,OAAQ,CAAC;IAEnE;IAEA,IAAI,CAAC4P,aAAa,CAAC6P,cAAc,CAAE,IAAI,CAACjQ,OAAO,CAACxK,WAAY,CAAC;IAC7D,IAAI,CAAC4K,aAAa,CAAC/M,gBAAgB,CAAE,IAAI,CAAC2M,OAAO,CAAC1M,aAAc,CAAC;IAEjE,IAAI,CAACmC,UAAU,GAAG,IAAI3I,UAAU,CAAE,IAAI,CAACkT,OAAO,CAACxP,OAAQ,CAAC;IACxD,IAAI,CAACiF,UAAU,CAACrC,eAAe,CAAE,aAAc,CAAC;IAEhD,IAAK,IAAI,CAAC4M,OAAO,CAACxK,WAAW,KAAK,iBAAiB,EAAG;MAErD,IAAI,CAACC,UAAU,CAAClC,kBAAkB,CAAE,IAAK,CAAC;IAE3C;EAED;EAEAmD,aAAaA,CAAErC,UAAU,EAAG;IAE3B,IAAI,CAACA,UAAU,GAAGA,UAAU;EAE7B;EAEAsC,UAAUA,CAAErC,OAAO,EAAG;IAErB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAEvB;EAEAZ,KAAKA,CAAExB,MAAM,EAAEE,OAAO,EAAG;IAExB,MAAMtB,MAAM,GAAG,IAAI;IACnB,MAAMsD,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMC,UAAU,GAAG,IAAI,CAACA,UAAU;;IAElC;IACA,IAAI,CAACoE,KAAK,CAAClB,SAAS,CAAC,CAAC;IACtB,IAAI,CAACwX,SAAS,GAAG,CAAC,CAAC;;IAEnB;IACA,IAAI,CAACmB,UAAU,CAAE,UAAWC,GAAG,EAAG;MAEjC,OAAOA,GAAG,CAACvX,SAAS,IAAIuX,GAAG,CAACvX,SAAS,CAAC,CAAC;IAExC,CAAE,CAAC;IAEH/B,OAAO,CAACqF,GAAG,CAAE,IAAI,CAACgU,UAAU,CAAE,UAAWC,GAAG,EAAG;MAE9C,OAAOA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACC,UAAU,CAAC,CAAC;IAE1C,CAAE,CAAE,CAAC,CAAClV,IAAI,CAAE,YAAY;MAEvB,OAAOrE,OAAO,CAACqF,GAAG,CAAE,CAEnBpL,MAAM,CAACuf,eAAe,CAAE,OAAQ,CAAC,EACjCvf,MAAM,CAACuf,eAAe,CAAE,WAAY,CAAC,EACrCvf,MAAM,CAACuf,eAAe,CAAE,QAAS,CAAC,CAEjC,CAAC;IAEJ,CAAE,CAAC,CAACnV,IAAI,CAAE,UAAWoV,YAAY,EAAG;MAEnC,MAAM7O,MAAM,GAAG;QACd8O,KAAK,EAAED,YAAY,CAAE,CAAC,CAAE,CAAElc,IAAI,CAACmc,KAAK,IAAI,CAAC,CAAE;QAC3CC,MAAM,EAAEF,YAAY,CAAE,CAAC,CAAE;QACzBG,UAAU,EAAEH,YAAY,CAAE,CAAC,CAAE;QAC7BI,OAAO,EAAEJ,YAAY,CAAE,CAAC,CAAE;QAC1Bnb,KAAK,EAAEf,IAAI,CAACe,KAAK;QACjBrE,MAAM,EAAEA,MAAM;QACdub,QAAQ,EAAE,CAAC;MACZ,CAAC;MAEDH,8BAA8B,CAAE7X,UAAU,EAAEoN,MAAM,EAAErN,IAAK,CAAC;MAE1DuG,sBAAsB,CAAE8G,MAAM,EAAErN,IAAK,CAAC;MAEtC,OAAOyC,OAAO,CAACqF,GAAG,CAAEpL,MAAM,CAACof,UAAU,CAAE,UAAWC,GAAG,EAAG;QAEvD,OAAOA,GAAG,CAACQ,SAAS,IAAIR,GAAG,CAACQ,SAAS,CAAElP,MAAO,CAAC;MAEhD,CAAE,CAAE,CAAC,CAACvG,IAAI,CAAE,YAAY;QAEvB,KAAM,MAAMqV,KAAK,IAAI9O,MAAM,CAAC+O,MAAM,EAAG;UAEpCD,KAAK,CAACK,iBAAiB,CAAC,CAAC;QAE1B;QAEA1e,MAAM,CAAEuP,MAAO,CAAC;MAEjB,CAAE,CAAC;IAEJ,CAAE,CAAC,CAACoP,KAAK,CAAEze,OAAQ,CAAC;EAErB;;EAEA;AACD;AACA;AACA;AACA;EACCwG,SAASA,CAAA,EAAG;IAEX,MAAMC,QAAQ,GAAG,IAAI,CAACzE,IAAI,CAAC0E,KAAK,IAAI,EAAE;IACtC,MAAMgY,QAAQ,GAAG,IAAI,CAAC1c,IAAI,CAAC2c,KAAK,IAAI,EAAE;IACtC,MAAMC,QAAQ,GAAG,IAAI,CAAC5c,IAAI,CAAC0N,MAAM,IAAI,EAAE;;IAEvC;IACA;IACA,KAAM,IAAImP,SAAS,GAAG,CAAC,EAAEC,UAAU,GAAGJ,QAAQ,CAACnb,MAAM,EAAEsb,SAAS,GAAGC,UAAU,EAAED,SAAS,EAAG,EAAG;MAE7F,MAAME,MAAM,GAAGL,QAAQ,CAAEG,SAAS,CAAE,CAACE,MAAM;MAE3C,KAAM,IAAIzb,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAGmE,MAAM,CAACxb,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;QAEnDmD,QAAQ,CAAEsY,MAAM,CAAEzb,CAAC,CAAE,CAAE,CAAC0b,MAAM,GAAG,IAAI;MAEtC;IAED;;IAEA;IACA;IACA,KAAM,IAAIrY,SAAS,GAAG,CAAC,EAAEC,UAAU,GAAGH,QAAQ,CAAClD,MAAM,EAAEoD,SAAS,GAAGC,UAAU,EAAED,SAAS,EAAG,EAAG;MAE7F,MAAME,OAAO,GAAGJ,QAAQ,CAAEE,SAAS,CAAE;MAErC,IAAKE,OAAO,CAAC2I,IAAI,KAAKxM,SAAS,EAAG;QAEjC,IAAI,CAAC+D,WAAW,CAAE,IAAI,CAAC6V,SAAS,EAAE/V,OAAO,CAAC2I,IAAK,CAAC;;QAEhD;QACA;QACA;QACA,IAAK3I,OAAO,CAACoY,IAAI,KAAKjc,SAAS,EAAG;UAEjC4b,QAAQ,CAAE/X,OAAO,CAAC2I,IAAI,CAAE,CAAC0P,aAAa,GAAG,IAAI;QAE9C;MAED;MAEA,IAAKrY,OAAO,CAACsY,MAAM,KAAKnc,SAAS,EAAG;QAEnC,IAAI,CAAC+D,WAAW,CAAE,IAAI,CAAC8V,WAAW,EAAEhW,OAAO,CAACsY,MAAO,CAAC;MAErD;IAED;EAED;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACCpY,WAAWA,CAAEV,KAAK,EAAEsC,KAAK,EAAG;IAE3B,IAAKA,KAAK,KAAK3F,SAAS,EAAG;IAE3B,IAAKqD,KAAK,CAACC,IAAI,CAAEqC,KAAK,CAAE,KAAK3F,SAAS,EAAG;MAExCqD,KAAK,CAACC,IAAI,CAAEqC,KAAK,CAAE,GAAGtC,KAAK,CAACE,IAAI,CAAEoC,KAAK,CAAE,GAAG,CAAC;IAE9C;IAEAtC,KAAK,CAACC,IAAI,CAAEqC,KAAK,CAAE,EAAG;EAEvB;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACCI,WAAWA,CAAE1C,KAAK,EAAEsC,KAAK,EAAE1D,MAAM,EAAG;IAEnC,IAAKoB,KAAK,CAACC,IAAI,CAAEqC,KAAK,CAAE,IAAI,CAAC,EAAG,OAAO1D,MAAM;IAE7C,MAAMma,GAAG,GAAGna,MAAM,CAACuP,KAAK,CAAC,CAAC;;IAE1B;IACA;IACA,MAAM6K,cAAc,GAAGA,CAAEC,QAAQ,EAAE9K,KAAK,KAAM;MAE7C,MAAM+K,QAAQ,GAAG,IAAI,CAAC/C,YAAY,CAAC1X,GAAG,CAAEwa,QAAS,CAAC;MAClD,IAAKC,QAAQ,IAAI,IAAI,EAAG;QAEvB,IAAI,CAAC/C,YAAY,CAAC1U,GAAG,CAAE0M,KAAK,EAAE+K,QAAS,CAAC;MAEzC;MAEA,KAAM,MAAM,CAAEjc,CAAC,EAAEkc,KAAK,CAAE,IAAIF,QAAQ,CAAC9O,QAAQ,CAACiP,OAAO,CAAC,CAAC,EAAG;QAEzDJ,cAAc,CAAEG,KAAK,EAAEhL,KAAK,CAAChE,QAAQ,CAAElN,CAAC,CAAG,CAAC;MAE7C;IAED,CAAC;IAED+b,cAAc,CAAEpa,MAAM,EAAEma,GAAI,CAAC;IAE7BA,GAAG,CAAC3b,IAAI,IAAI,YAAY,GAAK4C,KAAK,CAACE,IAAI,CAAEoC,KAAK,CAAE,EAAK;IAErD,OAAOyW,GAAG;EAEX;EAEAM,UAAUA,CAAEC,IAAI,EAAG;IAElB,MAAM1d,UAAU,GAAGoY,MAAM,CAACnF,MAAM,CAAE,IAAI,CAAChT,OAAQ,CAAC;IAChDD,UAAU,CAACJ,IAAI,CAAE,IAAK,CAAC;IAEvB,KAAM,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,UAAU,CAACsB,MAAM,EAAED,CAAC,EAAG,EAAG;MAE9C,MAAM+L,MAAM,GAAGsQ,IAAI,CAAE1d,UAAU,CAAEqB,CAAC,CAAG,CAAC;MAEtC,IAAK+L,MAAM,EAAG,OAAOA,MAAM;IAE5B;IAEA,OAAO,IAAI;EAEZ;EAEAyO,UAAUA,CAAE6B,IAAI,EAAG;IAElB,MAAM1d,UAAU,GAAGoY,MAAM,CAACnF,MAAM,CAAE,IAAI,CAAChT,OAAQ,CAAC;IAChDD,UAAU,CAAC2d,OAAO,CAAE,IAAK,CAAC;IAE1B,MAAMxW,OAAO,GAAG,EAAE;IAElB,KAAM,IAAI9F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,UAAU,CAACsB,MAAM,EAAED,CAAC,EAAG,EAAG;MAE9C,MAAM+L,MAAM,GAAGsQ,IAAI,CAAE1d,UAAU,CAAEqB,CAAC,CAAG,CAAC;MAEtC,IAAK+L,MAAM,EAAGjG,OAAO,CAACvH,IAAI,CAAEwN,MAAO,CAAC;IAErC;IAEA,OAAOjG,OAAO;EAEf;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACCV,aAAaA,CAAEf,IAAI,EAAEgB,KAAK,EAAG;IAE5B,MAAMzB,QAAQ,GAAGS,IAAI,GAAG,GAAG,GAAGgB,KAAK;IACnC,IAAIxB,UAAU,GAAG,IAAI,CAACd,KAAK,CAACvB,GAAG,CAAEoC,QAAS,CAAC;IAE3C,IAAK,CAAEC,UAAU,EAAG;MAEnB,QAASQ,IAAI;QAEZ,KAAK,OAAO;UACXR,UAAU,GAAG,IAAI,CAAC0Y,SAAS,CAAElX,KAAM,CAAC;UACpC;QAED,KAAK,MAAM;UACVxB,UAAU,GAAG,IAAI,CAACuY,UAAU,CAAE,UAAW3B,GAAG,EAAG;YAE9C,OAAOA,GAAG,CAAC+B,QAAQ,IAAI/B,GAAG,CAAC+B,QAAQ,CAAEnX,KAAM,CAAC;UAE7C,CAAE,CAAC;UACH;QAED,KAAK,MAAM;UACVxB,UAAU,GAAG,IAAI,CAACuY,UAAU,CAAE,UAAW3B,GAAG,EAAG;YAE9C,OAAOA,GAAG,CAACgC,QAAQ,IAAIhC,GAAG,CAACgC,QAAQ,CAAEpX,KAAM,CAAC;UAE7C,CAAE,CAAC;UACH;QAED,KAAK,UAAU;UACdxB,UAAU,GAAG,IAAI,CAAC6Y,YAAY,CAAErX,KAAM,CAAC;UACvC;QAED,KAAK,YAAY;UAChBxB,UAAU,GAAG,IAAI,CAACuY,UAAU,CAAE,UAAW3B,GAAG,EAAG;YAE9C,OAAOA,GAAG,CAAC3P,cAAc,IAAI2P,GAAG,CAAC3P,cAAc,CAAEzF,KAAM,CAAC;UAEzD,CAAE,CAAC;UACH;QAED,KAAK,QAAQ;UACZxB,UAAU,GAAG,IAAI,CAAC8Y,UAAU,CAAEtX,KAAM,CAAC;UACrC;QAED,KAAK,UAAU;UACdxB,UAAU,GAAG,IAAI,CAACuY,UAAU,CAAE,UAAW3B,GAAG,EAAG;YAE9C,OAAOA,GAAG,CAACmC,YAAY,IAAInC,GAAG,CAACmC,YAAY,CAAEvX,KAAM,CAAC;UAErD,CAAE,CAAC;UACH;QAED,KAAK,SAAS;UACbxB,UAAU,GAAG,IAAI,CAACuY,UAAU,CAAE,UAAW3B,GAAG,EAAG;YAE9C,OAAOA,GAAG,CAACvQ,WAAW,IAAIuQ,GAAG,CAACvQ,WAAW,CAAE7E,KAAM,CAAC;UAEnD,CAAE,CAAC;UACH;QAED,KAAK,MAAM;UACVxB,UAAU,GAAG,IAAI,CAACgZ,QAAQ,CAAExX,KAAM,CAAC;UACnC;QAED,KAAK,WAAW;UACfxB,UAAU,GAAG,IAAI,CAACuY,UAAU,CAAE,UAAW3B,GAAG,EAAG;YAE9C,OAAOA,GAAG,CAACqC,aAAa,IAAIrC,GAAG,CAACqC,aAAa,CAAEzX,KAAM,CAAC;UAEvD,CAAE,CAAC;UACH;QAED,KAAK,QAAQ;UACZxB,UAAU,GAAG,IAAI,CAACkZ,UAAU,CAAE1X,KAAM,CAAC;UACrC;QAED;UACCxB,UAAU,GAAG,IAAI,CAACuY,UAAU,CAAE,UAAW3B,GAAG,EAAG;YAE9C,OAAOA,GAAG,IAAI,IAAI,IAAIA,GAAG,CAACrV,aAAa,IAAIqV,GAAG,CAACrV,aAAa,CAAEf,IAAI,EAAEgB,KAAM,CAAC;UAE5E,CAAE,CAAC;UAEH,IAAK,CAAExB,UAAU,EAAG;YAEnB,MAAM,IAAIjE,KAAK,CAAE,gBAAgB,GAAGyE,IAAK,CAAC;UAE3C;UAEA;MAEF;MAEA,IAAI,CAACtB,KAAK,CAACrB,GAAG,CAAEkC,QAAQ,EAAEC,UAAW,CAAC;IAEvC;IAEA,OAAOA,UAAU;EAElB;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACC8W,eAAeA,CAAEtW,IAAI,EAAG;IAEvB,IAAIuW,YAAY,GAAG,IAAI,CAAC7X,KAAK,CAACvB,GAAG,CAAE6C,IAAK,CAAC;IAEzC,IAAK,CAAEuW,YAAY,EAAG;MAErB,MAAMxf,MAAM,GAAG,IAAI;MACnB,MAAM4hB,IAAI,GAAG,IAAI,CAACte,IAAI,CAAE2F,IAAI,IAAKA,IAAI,KAAK,MAAM,GAAG,IAAI,GAAG,GAAG,CAAE,CAAE,IAAI,EAAE;MAEvEuW,YAAY,GAAGzZ,OAAO,CAACqF,GAAG,CAAEwW,IAAI,CAACC,GAAG,CAAE,UAAWC,GAAG,EAAE7X,KAAK,EAAG;QAE7D,OAAOjK,MAAM,CAACgK,aAAa,CAAEf,IAAI,EAAEgB,KAAM,CAAC;MAE3C,CAAE,CAAE,CAAC;MAEL,IAAI,CAACtC,KAAK,CAACrB,GAAG,CAAE2C,IAAI,EAAEuW,YAAa,CAAC;IAErC;IAEA,OAAOA,YAAY;EAEpB;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACC+B,UAAUA,CAAEQ,WAAW,EAAG;IAEzB,MAAMC,SAAS,GAAG,IAAI,CAAC1e,IAAI,CAAC2e,OAAO,CAAEF,WAAW,CAAE;IAClD,MAAM3f,MAAM,GAAG,IAAI,CAACuC,UAAU;IAE9B,IAAKqd,SAAS,CAAC/Y,IAAI,IAAI+Y,SAAS,CAAC/Y,IAAI,KAAK,aAAa,EAAG;MAEzD,MAAM,IAAIzE,KAAK,CAAE,oBAAoB,GAAGwd,SAAS,CAAC/Y,IAAI,GAAG,gCAAiC,CAAC;IAE5F;;IAEA;IACA,IAAK+Y,SAAS,CAACzS,GAAG,KAAKjL,SAAS,IAAIyd,WAAW,KAAK,CAAC,EAAG;MAEvD,OAAOhc,OAAO,CAACC,OAAO,CAAE,IAAI,CAACzC,UAAU,CAAEU,UAAU,CAACC,eAAe,CAAE,CAACyP,IAAK,CAAC;IAE7E;IAEA,MAAMzE,OAAO,GAAG,IAAI,CAACA,OAAO;IAE5B,OAAO,IAAInJ,OAAO,CAAE,UAAWC,OAAO,EAAEC,MAAM,EAAG;MAEhD7D,MAAM,CAAClB,IAAI,CAAE/D,WAAW,CAACyE,UAAU,CAAEogB,SAAS,CAACzS,GAAG,EAAEL,OAAO,CAACzN,IAAK,CAAC,EAAEuE,OAAO,EAAE1B,SAAS,EAAE,YAAY;QAEnG2B,MAAM,CAAE,IAAIzB,KAAK,CAAE,2CAA2C,GAAGwd,SAAS,CAACzS,GAAG,GAAG,IAAK,CAAE,CAAC;MAE1F,CAAE,CAAC;IAEJ,CAAE,CAAC;EAEJ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACCG,cAAcA,CAAE+E,eAAe,EAAG;IAEjC,MAAMyN,aAAa,GAAG,IAAI,CAAC5e,IAAI,CAACsM,WAAW,CAAE6E,eAAe,CAAE;IAE9D,OAAO,IAAI,CAACzK,aAAa,CAAE,QAAQ,EAAEkY,aAAa,CAACpS,MAAO,CAAC,CAAC1F,IAAI,CAAE,UAAW0F,MAAM,EAAG;MAErF,MAAMK,UAAU,GAAG+R,aAAa,CAAC/R,UAAU,IAAI,CAAC;MAChD,MAAMD,UAAU,GAAGgS,aAAa,CAAChS,UAAU,IAAI,CAAC;MAChD,OAAOJ,MAAM,CAACiE,KAAK,CAAE7D,UAAU,EAAEA,UAAU,GAAGC,UAAW,CAAC;IAE3D,CAAE,CAAC;EAEJ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACCmR,YAAYA,CAAEa,aAAa,EAAG;IAE7B,MAAMniB,MAAM,GAAG,IAAI;IACnB,MAAMsD,IAAI,GAAG,IAAI,CAACA,IAAI;IAEtB,MAAM2R,WAAW,GAAG,IAAI,CAAC3R,IAAI,CAAC4R,SAAS,CAAEiN,aAAa,CAAE;IAExD,IAAKlN,WAAW,CAACtF,UAAU,KAAKrL,SAAS,IAAI2Q,WAAW,CAACmN,MAAM,KAAK9d,SAAS,EAAG;MAE/E,MAAM0O,QAAQ,GAAGwG,gBAAgB,CAAEvE,WAAW,CAAChM,IAAI,CAAE;MACrD,MAAMoZ,UAAU,GAAGjN,qBAAqB,CAAEH,WAAW,CAACE,aAAa,CAAE;MACrE,MAAMlC,UAAU,GAAGgC,WAAW,CAAChC,UAAU,KAAK,IAAI;MAElD,MAAMhI,KAAK,GAAG,IAAIoX,UAAU,CAAEpN,WAAW,CAAC7E,KAAK,GAAG4C,QAAS,CAAC;MAC5D,OAAOjN,OAAO,CAACC,OAAO,CAAE,IAAIvK,eAAe,CAAEwP,KAAK,EAAE+H,QAAQ,EAAEC,UAAW,CAAE,CAAC;IAE7E;IAEA,MAAMqP,kBAAkB,GAAG,EAAE;IAE7B,IAAKrN,WAAW,CAACtF,UAAU,KAAKrL,SAAS,EAAG;MAE3Cge,kBAAkB,CAACnf,IAAI,CAAE,IAAI,CAAC6G,aAAa,CAAE,YAAY,EAAEiL,WAAW,CAACtF,UAAW,CAAE,CAAC;IAEtF,CAAC,MAAM;MAEN2S,kBAAkB,CAACnf,IAAI,CAAE,IAAK,CAAC;IAEhC;IAEA,IAAK8R,WAAW,CAACmN,MAAM,KAAK9d,SAAS,EAAG;MAEvCge,kBAAkB,CAACnf,IAAI,CAAE,IAAI,CAAC6G,aAAa,CAAE,YAAY,EAAEiL,WAAW,CAACmN,MAAM,CAAC/E,OAAO,CAAC1N,UAAW,CAAE,CAAC;MACpG2S,kBAAkB,CAACnf,IAAI,CAAE,IAAI,CAAC6G,aAAa,CAAE,YAAY,EAAEiL,WAAW,CAACmN,MAAM,CAAC5L,MAAM,CAAC7G,UAAW,CAAE,CAAC;IAEpG;IAEA,OAAO5J,OAAO,CAACqF,GAAG,CAAEkX,kBAAmB,CAAC,CAAClY,IAAI,CAAE,UAAWwF,WAAW,EAAG;MAEvE,MAAMD,UAAU,GAAGC,WAAW,CAAE,CAAC,CAAE;MAEnC,MAAMoD,QAAQ,GAAGwG,gBAAgB,CAAEvE,WAAW,CAAChM,IAAI,CAAE;MACrD,MAAMoZ,UAAU,GAAGjN,qBAAqB,CAAEH,WAAW,CAACE,aAAa,CAAE;;MAErE;MACA,MAAMoN,YAAY,GAAGF,UAAU,CAACG,iBAAiB;MACjD,MAAMC,SAAS,GAAGF,YAAY,GAAGvP,QAAQ;MACzC,MAAM9C,UAAU,GAAG+E,WAAW,CAAC/E,UAAU,IAAI,CAAC;MAC9C,MAAMI,UAAU,GAAG2E,WAAW,CAACtF,UAAU,KAAKrL,SAAS,GAAGhB,IAAI,CAACsM,WAAW,CAAEqF,WAAW,CAACtF,UAAU,CAAE,CAACW,UAAU,GAAGhM,SAAS;MAC3H,MAAM2O,UAAU,GAAGgC,WAAW,CAAChC,UAAU,KAAK,IAAI;MAClD,IAAIhI,KAAK,EAAEyX,eAAe;;MAE1B;MACA,IAAKpS,UAAU,IAAIA,UAAU,KAAKmS,SAAS,EAAG;QAE7C;QACA;QACA,MAAME,OAAO,GAAGlZ,IAAI,CAACmZ,KAAK,CAAE1S,UAAU,GAAGI,UAAW,CAAC;QACrD,MAAMuS,UAAU,GAAG,oBAAoB,GAAG5N,WAAW,CAACtF,UAAU,GAAG,GAAG,GAAGsF,WAAW,CAACE,aAAa,GAAG,GAAG,GAAGwN,OAAO,GAAG,GAAG,GAAG1N,WAAW,CAAC7E,KAAK;QAC5I,IAAI0S,EAAE,GAAG9iB,MAAM,CAAC2H,KAAK,CAACvB,GAAG,CAAEyc,UAAW,CAAC;QAEvC,IAAK,CAAEC,EAAE,EAAG;UAEX7X,KAAK,GAAG,IAAIoX,UAAU,CAAE1S,UAAU,EAAEgT,OAAO,GAAGrS,UAAU,EAAE2E,WAAW,CAAC7E,KAAK,GAAGE,UAAU,GAAGiS,YAAa,CAAC;;UAEzG;UACAO,EAAE,GAAG,IAAIzmB,iBAAiB,CAAE4O,KAAK,EAAEqF,UAAU,GAAGiS,YAAa,CAAC;UAE9DviB,MAAM,CAAC2H,KAAK,CAACrB,GAAG,CAAEuc,UAAU,EAAEC,EAAG,CAAC;QAEnC;QAEAJ,eAAe,GAAG,IAAIpmB,0BAA0B,CAAEwmB,EAAE,EAAE9P,QAAQ,EAAI9C,UAAU,GAAGI,UAAU,GAAKiS,YAAY,EAAEtP,UAAW,CAAC;MAEzH,CAAC,MAAM;QAEN,IAAKtD,UAAU,KAAK,IAAI,EAAG;UAE1B1E,KAAK,GAAG,IAAIoX,UAAU,CAAEpN,WAAW,CAAC7E,KAAK,GAAG4C,QAAS,CAAC;QAEvD,CAAC,MAAM;UAEN/H,KAAK,GAAG,IAAIoX,UAAU,CAAE1S,UAAU,EAAEO,UAAU,EAAE+E,WAAW,CAAC7E,KAAK,GAAG4C,QAAS,CAAC;QAE/E;QAEA0P,eAAe,GAAG,IAAIjnB,eAAe,CAAEwP,KAAK,EAAE+H,QAAQ,EAAEC,UAAW,CAAC;MAErE;;MAEA;MACA,IAAKgC,WAAW,CAACmN,MAAM,KAAK9d,SAAS,EAAG;QAEvC,MAAMye,eAAe,GAAGvJ,gBAAgB,CAACwJ,MAAM;QAC/C,MAAMC,iBAAiB,GAAG7N,qBAAqB,CAAEH,WAAW,CAACmN,MAAM,CAAC/E,OAAO,CAAClI,aAAa,CAAE;QAE3F,MAAM+N,iBAAiB,GAAGjO,WAAW,CAACmN,MAAM,CAAC/E,OAAO,CAACnN,UAAU,IAAI,CAAC;QACpE,MAAMiT,gBAAgB,GAAGlO,WAAW,CAACmN,MAAM,CAAC5L,MAAM,CAACtG,UAAU,IAAI,CAAC;QAElE,MAAMkT,aAAa,GAAG,IAAIH,iBAAiB,CAAErT,WAAW,CAAE,CAAC,CAAE,EAAEsT,iBAAiB,EAAEjO,WAAW,CAACmN,MAAM,CAAChS,KAAK,GAAG2S,eAAgB,CAAC;QAC9H,MAAMM,YAAY,GAAG,IAAIhB,UAAU,CAAEzS,WAAW,CAAE,CAAC,CAAE,EAAEuT,gBAAgB,EAAElO,WAAW,CAACmN,MAAM,CAAChS,KAAK,GAAG4C,QAAS,CAAC;QAE9G,IAAKrD,UAAU,KAAK,IAAI,EAAG;UAE1B;UACA+S,eAAe,GAAG,IAAIjnB,eAAe,CAAEinB,eAAe,CAACzX,KAAK,CAAC8I,KAAK,CAAC,CAAC,EAAE2O,eAAe,CAAC1P,QAAQ,EAAE0P,eAAe,CAACzP,UAAW,CAAC;QAE7H;;QAEA;QACAyP,eAAe,CAACzP,UAAU,GAAG,KAAK;QAElC,KAAM,IAAIrO,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAGkH,aAAa,CAACve,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;UAE1D,MAAMqF,KAAK,GAAGmZ,aAAa,CAAExe,CAAC,CAAE;UAEhC8d,eAAe,CAACY,IAAI,CAAErZ,KAAK,EAAEoZ,YAAY,CAAEze,CAAC,GAAGoO,QAAQ,CAAG,CAAC;UAC3D,IAAKA,QAAQ,IAAI,CAAC,EAAG0P,eAAe,CAACa,IAAI,CAAEtZ,KAAK,EAAEoZ,YAAY,CAAEze,CAAC,GAAGoO,QAAQ,GAAG,CAAC,CAAG,CAAC;UACpF,IAAKA,QAAQ,IAAI,CAAC,EAAG0P,eAAe,CAACc,IAAI,CAAEvZ,KAAK,EAAEoZ,YAAY,CAAEze,CAAC,GAAGoO,QAAQ,GAAG,CAAC,CAAG,CAAC;UACpF,IAAKA,QAAQ,IAAI,CAAC,EAAG0P,eAAe,CAACe,IAAI,CAAExZ,KAAK,EAAEoZ,YAAY,CAAEze,CAAC,GAAGoO,QAAQ,GAAG,CAAC,CAAG,CAAC;UACpF,IAAKA,QAAQ,IAAI,CAAC,EAAG,MAAM,IAAIxO,KAAK,CAAE,mEAAoE,CAAC;QAE5G;QAEAke,eAAe,CAACzP,UAAU,GAAGA,UAAU;MAExC;MAEA,OAAOyP,eAAe;IAEvB,CAAE,CAAC;EAEJ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACC5T,WAAWA,CAAEC,YAAY,EAAG;IAE3B,MAAMzL,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAM4L,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMF,UAAU,GAAG1L,IAAI,CAAC2L,QAAQ,CAAEF,YAAY,CAAE;IAChD,MAAM2U,WAAW,GAAG1U,UAAU,CAACI,MAAM;IACrC,MAAMuU,SAAS,GAAGrgB,IAAI,CAAC+L,MAAM,CAAEqU,WAAW,CAAE;IAE5C,IAAIthB,MAAM,GAAG,IAAI,CAACkN,aAAa;IAE/B,IAAKqU,SAAS,CAACpU,GAAG,EAAG;MAEpB,MAAMC,OAAO,GAAGN,OAAO,CAACxP,OAAO,CAAC+P,UAAU,CAAEkU,SAAS,CAACpU,GAAI,CAAC;MAC3D,IAAKC,OAAO,KAAK,IAAI,EAAGpN,MAAM,GAAGoN,OAAO;IAEzC;IAEA,OAAO,IAAI,CAACL,gBAAgB,CAAEJ,YAAY,EAAE2U,WAAW,EAAEthB,MAAO,CAAC;EAElE;EAEA+M,gBAAgBA,CAAEJ,YAAY,EAAE2U,WAAW,EAAEthB,MAAM,EAAG;IAErD,MAAMpC,MAAM,GAAG,IAAI;IACnB,MAAMsD,IAAI,GAAG,IAAI,CAACA,IAAI;IAEtB,MAAM0L,UAAU,GAAG1L,IAAI,CAAC2L,QAAQ,CAAEF,YAAY,CAAE;IAChD,MAAM4U,SAAS,GAAGrgB,IAAI,CAAC+L,MAAM,CAAEqU,WAAW,CAAE;IAE5C,MAAMlb,QAAQ,GAAG,CAAEmb,SAAS,CAACpU,GAAG,IAAIoU,SAAS,CAAChU,UAAU,IAAK,GAAG,GAAGX,UAAU,CAAC4U,OAAO;IAErF,IAAK,IAAI,CAACtF,YAAY,CAAE9V,QAAQ,CAAE,EAAG;MAEpC;MACA,OAAO,IAAI,CAAC8V,YAAY,CAAE9V,QAAQ,CAAE;IAErC;IAEA,MAAMqb,OAAO,GAAG,IAAI,CAACC,eAAe,CAAEJ,WAAW,EAAEthB,MAAO,CAAC,CAACgI,IAAI,CAAE,UAAWoL,OAAO,EAAG;MAEtFA,OAAO,CAACuO,KAAK,GAAG,KAAK;MAErBvO,OAAO,CAACzQ,IAAI,GAAGiK,UAAU,CAACjK,IAAI,IAAI4e,SAAS,CAAC5e,IAAI,IAAI,EAAE;MAEtD,IAAKyQ,OAAO,CAACzQ,IAAI,KAAK,EAAE,IAAI,OAAO4e,SAAS,CAACpU,GAAG,KAAK,QAAQ,IAAIoU,SAAS,CAACpU,GAAG,CAACyU,UAAU,CAAE,aAAc,CAAC,KAAK,KAAK,EAAG;QAEtHxO,OAAO,CAACzQ,IAAI,GAAG4e,SAAS,CAACpU,GAAG;MAE7B;MAEA,MAAM0U,QAAQ,GAAG3gB,IAAI,CAAC2gB,QAAQ,IAAI,CAAC,CAAC;MACpC,MAAML,OAAO,GAAGK,QAAQ,CAAEjV,UAAU,CAAC4U,OAAO,CAAE,IAAI,CAAC,CAAC;MAEpDpO,OAAO,CAAC0O,SAAS,GAAG5K,aAAa,CAAEsK,OAAO,CAACM,SAAS,CAAE,IAAIpnB,YAAY;MACtE0Y,OAAO,CAAC2O,SAAS,GAAG7K,aAAa,CAAEsK,OAAO,CAACO,SAAS,CAAE,IAAIpnB,wBAAwB;MAClFyY,OAAO,CAAC4O,KAAK,GAAG7K,eAAe,CAAEqK,OAAO,CAACQ,KAAK,CAAE,IAAI3lB,cAAc;MAClE+W,OAAO,CAAC6O,KAAK,GAAG9K,eAAe,CAAEqK,OAAO,CAACS,KAAK,CAAE,IAAI5lB,cAAc;MAClE+W,OAAO,CAAC8O,eAAe,GAAG,CAAE9O,OAAO,CAAC+O,mBAAmB,IAAI/O,OAAO,CAAC2O,SAAS,KAAKvmB,aAAa,IAAI4X,OAAO,CAAC2O,SAAS,KAAKrnB,YAAY;MAEpIkD,MAAM,CAAC8d,YAAY,CAAC1U,GAAG,CAAEoM,OAAO,EAAE;QAAEvG,QAAQ,EAAEF;MAAa,CAAE,CAAC;MAE9D,OAAOyG,OAAO;IAEf,CAAE,CAAC,CAACuK,KAAK,CAAE,YAAY;MAEtB,OAAO,IAAI;IAEZ,CAAE,CAAC;IAEH,IAAI,CAACzB,YAAY,CAAE9V,QAAQ,CAAE,GAAGqb,OAAO;IAEvC,OAAOA,OAAO;EAEf;EAEAC,eAAeA,CAAEJ,WAAW,EAAEthB,MAAM,EAAG;IAEtC,MAAMpC,MAAM,GAAG,IAAI;IACnB,MAAMsD,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAM4L,OAAO,GAAG,IAAI,CAACA,OAAO;IAE5B,IAAK,IAAI,CAACmP,WAAW,CAAEqF,WAAW,CAAE,KAAKpf,SAAS,EAAG;MAEpD,OAAO,IAAI,CAAC+Z,WAAW,CAAEqF,WAAW,CAAE,CAACtZ,IAAI,CAAIoL,OAAO,IAAMA,OAAO,CAACM,KAAK,CAAC,CAAE,CAAC;IAE9E;IAEA,MAAM6N,SAAS,GAAGrgB,IAAI,CAAC+L,MAAM,CAAEqU,WAAW,CAAE;IAE5C,MAAMc,GAAG,GAAGra,IAAI,CAACqa,GAAG,IAAIra,IAAI,CAACsa,SAAS;IAEtC,IAAIC,SAAS,GAAGf,SAAS,CAACpU,GAAG,IAAI,EAAE;IACnC,IAAIoV,WAAW,GAAG,KAAK;IAEvB,IAAKhB,SAAS,CAAChU,UAAU,KAAKrL,SAAS,EAAG;MAEzC;;MAEAogB,SAAS,GAAG1kB,MAAM,CAACgK,aAAa,CAAE,YAAY,EAAE2Z,SAAS,CAAChU,UAAW,CAAC,CAACvF,IAAI,CAAE,UAAWuF,UAAU,EAAG;QAEpGgV,WAAW,GAAG,IAAI;QAClB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAE,CAAElV,UAAU,CAAE,EAAE;UAAE1G,IAAI,EAAE0a,SAAS,CAACmB;QAAS,CAAE,CAAC;QACrEJ,SAAS,GAAGF,GAAG,CAACO,eAAe,CAAEH,IAAK,CAAC;QACvC,OAAOF,SAAS;MAEjB,CAAE,CAAC;IAEJ,CAAC,MAAM,IAAKf,SAAS,CAACpU,GAAG,KAAKjL,SAAS,EAAG;MAEzC,MAAM,IAAIE,KAAK,CAAE,0BAA0B,GAAGkf,WAAW,GAAG,gCAAiC,CAAC;IAE/F;IAEA,MAAMG,OAAO,GAAG9d,OAAO,CAACC,OAAO,CAAE0e,SAAU,CAAC,CAACta,IAAI,CAAE,UAAWsa,SAAS,EAAG;MAEzE,OAAO,IAAI3e,OAAO,CAAE,UAAWC,OAAO,EAAEC,MAAM,EAAG;QAEhD,IAAI7E,MAAM,GAAG4E,OAAO;QAEpB,IAAK5D,MAAM,CAAC4iB,mBAAmB,KAAK,IAAI,EAAG;UAE1C5jB,MAAM,GAAG,SAAAA,CAAW6jB,WAAW,EAAG;YAEjC,MAAMzP,OAAO,GAAG,IAAI1W,OAAO,CAAEmmB,WAAY,CAAC;YAC1CzP,OAAO,CAACS,WAAW,GAAG,IAAI;YAE1BjQ,OAAO,CAAEwP,OAAQ,CAAC;UAEnB,CAAC;QAEF;QAEApT,MAAM,CAAClB,IAAI,CAAE/D,WAAW,CAACyE,UAAU,CAAE8iB,SAAS,EAAExV,OAAO,CAACzN,IAAK,CAAC,EAAEL,MAAM,EAAEkD,SAAS,EAAE2B,MAAO,CAAC;MAE5F,CAAE,CAAC;IAEJ,CAAE,CAAC,CAACmE,IAAI,CAAE,UAAWoL,OAAO,EAAG;MAE9B;;MAEA,IAAKmP,WAAW,KAAK,IAAI,EAAG;QAE3BH,GAAG,CAACU,eAAe,CAAER,SAAU,CAAC;MAEjC;MAEA7a,sBAAsB,CAAE2L,OAAO,EAAEmO,SAAU,CAAC;MAE5CnO,OAAO,CAAC+F,QAAQ,CAACuJ,QAAQ,GAAGnB,SAAS,CAACmB,QAAQ,IAAInH,mBAAmB,CAAEgG,SAAS,CAACpU,GAAI,CAAC;MAEtF,OAAOiG,OAAO;IAEf,CAAE,CAAC,CAACuK,KAAK,CAAE,UAAW9d,KAAK,EAAG;MAE7BD,OAAO,CAACC,KAAK,CAAE,0CAA0C,EAAEyiB,SAAU,CAAC;MACtE,MAAMziB,KAAK;IAEZ,CAAE,CAAC;IAEH,IAAI,CAACoc,WAAW,CAAEqF,WAAW,CAAE,GAAGG,OAAO;IACzC,OAAOA,OAAO;EAEf;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC1Y,aAAaA,CAAEX,cAAc,EAAE2a,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAG;IAE5D,MAAMrlB,MAAM,GAAG,IAAI;IAEnB,OAAO,IAAI,CAACgK,aAAa,CAAE,SAAS,EAAEob,MAAM,CAACnb,KAAM,CAAC,CAACG,IAAI,CAAE,UAAWoL,OAAO,EAAG;MAE/E,IAAK,CAAEA,OAAO,EAAG,OAAO,IAAI;MAE5B,IAAK4P,MAAM,CAAC1P,QAAQ,KAAKpR,SAAS,IAAI8gB,MAAM,CAAC1P,QAAQ,GAAG,CAAC,EAAG;QAE3DF,OAAO,GAAGA,OAAO,CAACM,KAAK,CAAC,CAAC;QACzBN,OAAO,CAACG,OAAO,GAAGyP,MAAM,CAAC1P,QAAQ;MAElC;MAEA,IAAK1V,MAAM,CAACuD,UAAU,CAAEU,UAAU,CAACsB,qBAAqB,CAAE,EAAG;QAE5D,MAAMkQ,SAAS,GAAG2P,MAAM,CAAC7hB,UAAU,KAAKe,SAAS,GAAG8gB,MAAM,CAAC7hB,UAAU,CAAEU,UAAU,CAACsB,qBAAqB,CAAE,GAAGjB,SAAS;QAErH,IAAKmR,SAAS,EAAG;UAEhB,MAAM6P,aAAa,GAAGtlB,MAAM,CAAC8d,YAAY,CAAC1X,GAAG,CAAEoP,OAAQ,CAAC;UACxDA,OAAO,GAAGxV,MAAM,CAACuD,UAAU,CAAEU,UAAU,CAACsB,qBAAqB,CAAE,CAACgQ,aAAa,CAAEC,OAAO,EAAEC,SAAU,CAAC;UACnGzV,MAAM,CAAC8d,YAAY,CAAC1U,GAAG,CAAEoM,OAAO,EAAE8P,aAAc,CAAC;QAElD;MAED;MAEA,IAAKD,UAAU,KAAK/gB,SAAS,EAAG;QAE/BkR,OAAO,CAAC6P,UAAU,GAAGA,UAAU;MAEhC;MAEA7a,cAAc,CAAE2a,OAAO,CAAE,GAAG3P,OAAO;MAEnC,OAAOA,OAAO;IAEf,CAAE,CAAC;EAEJ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACClC,mBAAmBA,CAAExC,IAAI,EAAG;IAE3B,MAAMuB,QAAQ,GAAGvB,IAAI,CAACuB,QAAQ;IAC9B,IAAIC,QAAQ,GAAGxB,IAAI,CAACwB,QAAQ;IAE5B,MAAMiT,qBAAqB,GAAGlT,QAAQ,CAACb,UAAU,CAACgU,OAAO,KAAKlhB,SAAS;IACvE,MAAMmhB,eAAe,GAAGpT,QAAQ,CAACb,UAAU,CAAC1I,KAAK,KAAKxE,SAAS;IAC/D,MAAMohB,cAAc,GAAGrT,QAAQ,CAACb,UAAU,CAAC+K,MAAM,KAAKjY,SAAS;IAE/D,IAAKwM,IAAI,CAAC6U,QAAQ,EAAG;MAEpB,MAAMnd,QAAQ,GAAG,iBAAiB,GAAG8J,QAAQ,CAACsT,IAAI;MAElD,IAAIC,cAAc,GAAG,IAAI,CAACle,KAAK,CAACvB,GAAG,CAAEoC,QAAS,CAAC;MAE/C,IAAK,CAAEqd,cAAc,EAAG;QAEvBA,cAAc,GAAG,IAAIxnB,cAAc,CAAC,CAAC;QACrCjB,QAAQ,CAAC+V,SAAS,CAACC,IAAI,CAACC,IAAI,CAAEwS,cAAc,EAAEvT,QAAS,CAAC;QACxDuT,cAAc,CAAC/c,KAAK,CAACsK,IAAI,CAAEd,QAAQ,CAACxJ,KAAM,CAAC;QAC3C+c,cAAc,CAAChE,GAAG,GAAGvP,QAAQ,CAACuP,GAAG;QACjCgE,cAAc,CAACC,eAAe,GAAG,KAAK,CAAC,CAAC;;QAExC,IAAI,CAACne,KAAK,CAACrB,GAAG,CAAEkC,QAAQ,EAAEqd,cAAe,CAAC;MAE3C;MAEAvT,QAAQ,GAAGuT,cAAc;IAE1B,CAAC,MAAM,IAAK/U,IAAI,CAACiV,MAAM,EAAG;MAEzB,MAAMvd,QAAQ,GAAG,oBAAoB,GAAG8J,QAAQ,CAACsT,IAAI;MAErD,IAAII,YAAY,GAAG,IAAI,CAACre,KAAK,CAACvB,GAAG,CAAEoC,QAAS,CAAC;MAE7C,IAAK,CAAEwd,YAAY,EAAG;QAErBA,YAAY,GAAG,IAAIrpB,iBAAiB,CAAC,CAAC;QACtCS,QAAQ,CAAC+V,SAAS,CAACC,IAAI,CAACC,IAAI,CAAE2S,YAAY,EAAE1T,QAAS,CAAC;QACtD0T,YAAY,CAACld,KAAK,CAACsK,IAAI,CAAEd,QAAQ,CAACxJ,KAAM,CAAC;QACzCkd,YAAY,CAACnE,GAAG,GAAGvP,QAAQ,CAACuP,GAAG;QAE/B,IAAI,CAACla,KAAK,CAACrB,GAAG,CAAEkC,QAAQ,EAAEwd,YAAa,CAAC;MAEzC;MAEA1T,QAAQ,GAAG0T,YAAY;IAExB;;IAEA;IACA,IAAKT,qBAAqB,IAAIE,eAAe,IAAIC,cAAc,EAAG;MAEjE,IAAIld,QAAQ,GAAG,iBAAiB,GAAG8J,QAAQ,CAACsT,IAAI,GAAG,GAAG;MAEtD,IAAKL,qBAAqB,EAAG/c,QAAQ,IAAI,sBAAsB;MAC/D,IAAKid,eAAe,EAAGjd,QAAQ,IAAI,gBAAgB;MACnD,IAAKkd,cAAc,EAAGld,QAAQ,IAAI,eAAe;MAEjD,IAAIyd,cAAc,GAAG,IAAI,CAACte,KAAK,CAACvB,GAAG,CAAEoC,QAAS,CAAC;MAE/C,IAAK,CAAEyd,cAAc,EAAG;QAEvBA,cAAc,GAAG3T,QAAQ,CAACwD,KAAK,CAAC,CAAC;QAEjC,IAAK2P,eAAe,EAAGQ,cAAc,CAACC,YAAY,GAAG,IAAI;QACzD,IAAKR,cAAc,EAAGO,cAAc,CAACE,WAAW,GAAG,IAAI;QAEvD,IAAKZ,qBAAqB,EAAG;UAE5B;UACA,IAAKU,cAAc,CAACG,WAAW,EAAGH,cAAc,CAACG,WAAW,CAACC,CAAC,IAAI,CAAE,CAAC;UACrE,IAAKJ,cAAc,CAAC9Z,oBAAoB,EAAG8Z,cAAc,CAAC9Z,oBAAoB,CAACka,CAAC,IAAI,CAAE,CAAC;QAExF;QAEA,IAAI,CAAC1e,KAAK,CAACrB,GAAG,CAAEkC,QAAQ,EAAEyd,cAAe,CAAC;QAE1C,IAAI,CAACnI,YAAY,CAAC1U,GAAG,CAAE6c,cAAc,EAAE,IAAI,CAACnI,YAAY,CAAC1X,GAAG,CAAEkM,QAAS,CAAE,CAAC;MAE3E;MAEAA,QAAQ,GAAG2T,cAAc;IAE1B;IAEAnV,IAAI,CAACwB,QAAQ,GAAGA,QAAQ;EAEzB;EAEAhI,eAAeA,CAAE;EAAA,EAAsB;IAEtC,OAAO5M,oBAAoB;EAE5B;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACC8jB,YAAYA,CAAElW,aAAa,EAAG;IAE7B,MAAMtL,MAAM,GAAG,IAAI;IACnB,MAAMsD,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMC,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,MAAMkH,WAAW,GAAGnH,IAAI,CAACiI,SAAS,CAAED,aAAa,CAAE;IAEnD,IAAIgb,YAAY;IAChB,MAAM9b,cAAc,GAAG,CAAC,CAAC;IACzB,MAAM+b,kBAAkB,GAAG9b,WAAW,CAAClH,UAAU,IAAI,CAAC,CAAC;IAEvD,MAAMmH,OAAO,GAAG,EAAE;IAElB,IAAK6b,kBAAkB,CAAEtiB,UAAU,CAACkB,mBAAmB,CAAE,EAAG;MAE3D,MAAMqhB,YAAY,GAAGjjB,UAAU,CAAEU,UAAU,CAACkB,mBAAmB,CAAE;MACjEmhB,YAAY,GAAGE,YAAY,CAAClc,eAAe,CAAC,CAAC;MAC7CI,OAAO,CAACvH,IAAI,CAAEqjB,YAAY,CAACjc,YAAY,CAAEC,cAAc,EAAEC,WAAW,EAAEzK,MAAO,CAAE,CAAC;IAEjF,CAAC,MAAM;MAEN;MACA;;MAEA,MAAM4K,iBAAiB,GAAGH,WAAW,CAACI,oBAAoB,IAAI,CAAC,CAAC;MAEhEL,cAAc,CAAC1B,KAAK,GAAG,IAAIlN,KAAK,CAAE,GAAG,EAAE,GAAG,EAAE,GAAI,CAAC;MACjD4O,cAAc,CAACG,OAAO,GAAG,GAAG;MAE5B,IAAKG,KAAK,CAACC,OAAO,CAAEH,iBAAiB,CAACI,eAAgB,CAAC,EAAG;QAEzD,MAAMC,KAAK,GAAGL,iBAAiB,CAACI,eAAe;QAE/CR,cAAc,CAAC1B,KAAK,CAACC,MAAM,CAAEkC,KAAK,CAAE,CAAC,CAAE,EAAEA,KAAK,CAAE,CAAC,CAAE,EAAEA,KAAK,CAAE,CAAC,CAAE,EAAEhO,oBAAqB,CAAC;QACvFuN,cAAc,CAACG,OAAO,GAAGM,KAAK,CAAE,CAAC,CAAE;MAEpC;MAEA,IAAKL,iBAAiB,CAACM,gBAAgB,KAAK5G,SAAS,EAAG;QAEvDoG,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,KAAK,EAAEI,iBAAiB,CAACM,gBAAgB,EAAE7L,cAAe,CAAE,CAAC;MAElH;MAEAmL,cAAc,CAACuQ,SAAS,GAAGnQ,iBAAiB,CAAC6b,cAAc,KAAKniB,SAAS,GAAGsG,iBAAiB,CAAC6b,cAAc,GAAG,GAAG;MAClHjc,cAAc,CAACwQ,SAAS,GAAGpQ,iBAAiB,CAAC8b,eAAe,KAAKpiB,SAAS,GAAGsG,iBAAiB,CAAC8b,eAAe,GAAG,GAAG;MAEpH,IAAK9b,iBAAiB,CAAC+b,wBAAwB,KAAKriB,SAAS,EAAG;QAE/DoG,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,cAAc,EAAEI,iBAAiB,CAAC+b,wBAAyB,CAAE,CAAC;QAClHjc,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,cAAc,EAAEI,iBAAiB,CAAC+b,wBAAyB,CAAE,CAAC;MAEnH;MAEAL,YAAY,GAAG,IAAI,CAACtF,UAAU,CAAE,UAAW3B,GAAG,EAAG;QAEhD,OAAOA,GAAG,CAAC/U,eAAe,IAAI+U,GAAG,CAAC/U,eAAe,CAAEgB,aAAc,CAAC;MAEnE,CAAE,CAAC;MAEHZ,OAAO,CAACvH,IAAI,CAAE4C,OAAO,CAACqF,GAAG,CAAE,IAAI,CAACgU,UAAU,CAAE,UAAWC,GAAG,EAAG;QAE5D,OAAOA,GAAG,CAAChU,oBAAoB,IAAIgU,GAAG,CAAChU,oBAAoB,CAAEC,aAAa,EAAEd,cAAe,CAAC;MAE7F,CAAE,CAAE,CAAE,CAAC;IAER;IAEA,IAAKC,WAAW,CAACmc,WAAW,KAAK,IAAI,EAAG;MAEvCpc,cAAc,CAAC2Q,IAAI,GAAGpf,UAAU;IAEjC;IAEA,MAAM8qB,SAAS,GAAGpc,WAAW,CAACoc,SAAS,IAAIpM,WAAW,CAACC,MAAM;IAE7D,IAAKmM,SAAS,KAAKpM,WAAW,CAACG,KAAK,EAAG;MAEtCpQ,cAAc,CAACyQ,WAAW,GAAG,IAAI;;MAEjC;MACAzQ,cAAc,CAACsc,UAAU,GAAG,KAAK;IAElC,CAAC,MAAM;MAENtc,cAAc,CAACyQ,WAAW,GAAG,KAAK;MAElC,IAAK4L,SAAS,KAAKpM,WAAW,CAACE,IAAI,EAAG;QAErCnQ,cAAc,CAACuc,SAAS,GAAGtc,WAAW,CAACuc,WAAW,KAAK1iB,SAAS,GAAGmG,WAAW,CAACuc,WAAW,GAAG,GAAG;MAEjG;IAED;IAEA,IAAKvc,WAAW,CAACwc,aAAa,KAAK3iB,SAAS,IAAIgiB,YAAY,KAAK9oB,iBAAiB,EAAG;MAEpFkN,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,WAAW,EAAEC,WAAW,CAACwc,aAAc,CAAE,CAAC;MAE9Fzc,cAAc,CAAC4b,WAAW,GAAG,IAAIlnB,OAAO,CAAE,CAAC,EAAE,CAAE,CAAC;MAEhD,IAAKuL,WAAW,CAACwc,aAAa,CAAC/a,KAAK,KAAK5H,SAAS,EAAG;QAEpD,MAAM4H,KAAK,GAAGzB,WAAW,CAACwc,aAAa,CAAC/a,KAAK;QAE7C1B,cAAc,CAAC4b,WAAW,CAAChd,GAAG,CAAE8C,KAAK,EAAEA,KAAM,CAAC;MAE/C;IAED;IAEA,IAAKzB,WAAW,CAACyc,gBAAgB,KAAK5iB,SAAS,IAAIgiB,YAAY,KAAK9oB,iBAAiB,EAAG;MAEvFkN,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,OAAO,EAAEC,WAAW,CAACyc,gBAAiB,CAAE,CAAC;MAE7F,IAAKzc,WAAW,CAACyc,gBAAgB,CAACC,QAAQ,KAAK7iB,SAAS,EAAG;QAE1DkG,cAAc,CAAC4c,cAAc,GAAG3c,WAAW,CAACyc,gBAAgB,CAACC,QAAQ;MAEtE;IAED;IAEA,IAAK1c,WAAW,CAAC4c,cAAc,KAAK/iB,SAAS,IAAIgiB,YAAY,KAAK9oB,iBAAiB,EAAG;MAErF,MAAM6pB,cAAc,GAAG5c,WAAW,CAAC4c,cAAc;MACjD7c,cAAc,CAACsQ,QAAQ,GAAG,IAAIlf,KAAK,CAAC,CAAC,CAACmN,MAAM,CAAEse,cAAc,CAAE,CAAC,CAAE,EAAEA,cAAc,CAAE,CAAC,CAAE,EAAEA,cAAc,CAAE,CAAC,CAAE,EAAEpqB,oBAAqB,CAAC;IAEpI;IAEA,IAAKwN,WAAW,CAAC6c,eAAe,KAAKhjB,SAAS,IAAIgiB,YAAY,KAAK9oB,iBAAiB,EAAG;MAEtFkN,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACmL,aAAa,CAAEX,cAAc,EAAE,aAAa,EAAEC,WAAW,CAAC6c,eAAe,EAAEjoB,cAAe,CAAE,CAAC;IAEnH;IAEA,OAAO0G,OAAO,CAACqF,GAAG,CAAEV,OAAQ,CAAC,CAACN,IAAI,CAAE,YAAY;MAE/C,MAAMkI,QAAQ,GAAG,IAAIgU,YAAY,CAAE9b,cAAe,CAAC;MAEnD,IAAKC,WAAW,CAAC1F,IAAI,EAAGuN,QAAQ,CAACvN,IAAI,GAAG0F,WAAW,CAAC1F,IAAI;MAExD8E,sBAAsB,CAAEyI,QAAQ,EAAE7H,WAAY,CAAC;MAE/CzK,MAAM,CAAC8d,YAAY,CAAC1U,GAAG,CAAEkJ,QAAQ,EAAE;QAAE/G,SAAS,EAAED;MAAc,CAAE,CAAC;MAEjE,IAAKb,WAAW,CAAClH,UAAU,EAAG6X,8BAA8B,CAAE7X,UAAU,EAAE+O,QAAQ,EAAE7H,WAAY,CAAC;MAEjG,OAAO6H,QAAQ;IAEhB,CAAE,CAAC;EAEJ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACCvI,gBAAgBA,CAAEwd,YAAY,EAAG;IAEhC,MAAMC,aAAa,GAAGlpB,eAAe,CAACmpB,gBAAgB,CAAEF,YAAY,IAAI,EAAG,CAAC;IAE5E,IAAKC,aAAa,IAAI,IAAI,CAACjJ,aAAa,EAAG;MAE1C,OAAOiJ,aAAa,GAAG,GAAG,GAAK,EAAG,IAAI,CAACjJ,aAAa,CAAEiJ,aAAa,CAAI;IAExE,CAAC,MAAM;MAEN,IAAI,CAACjJ,aAAa,CAAEiJ,aAAa,CAAE,GAAG,CAAC;MAEvC,OAAOA,aAAa;IAErB;EAED;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACCE,cAAcA,CAAExW,UAAU,EAAG;IAE5B,MAAMlR,MAAM,GAAG,IAAI;IACnB,MAAMuD,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,MAAMoE,KAAK,GAAG,IAAI,CAACqW,cAAc;IAEjC,SAAS2J,oBAAoBA,CAAE1W,SAAS,EAAG;MAE1C,OAAO1N,UAAU,CAAEU,UAAU,CAACoB,0BAA0B,CAAE,CACxDmP,eAAe,CAAEvD,SAAS,EAAEjR,MAAO,CAAC,CACpCoK,IAAI,CAAE,UAAWiI,QAAQ,EAAG;QAE5B,OAAOuV,sBAAsB,CAAEvV,QAAQ,EAAEpB,SAAS,EAAEjR,MAAO,CAAC;MAE7D,CAAE,CAAC;IAEL;IAEA,MAAM0K,OAAO,GAAG,EAAE;IAElB,KAAM,IAAI9F,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAGhL,UAAU,CAACrM,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;MAEvD,MAAMqM,SAAS,GAAGC,UAAU,CAAEtM,CAAC,CAAE;MACjC,MAAM4D,QAAQ,GAAGyU,kBAAkB,CAAEhM,SAAU,CAAC;;MAEhD;MACA,MAAM4W,MAAM,GAAGlgB,KAAK,CAAEa,QAAQ,CAAE;MAEhC,IAAKqf,MAAM,EAAG;QAEb;QACAnd,OAAO,CAACvH,IAAI,CAAE0kB,MAAM,CAAChE,OAAQ,CAAC;MAE/B,CAAC,MAAM;QAEN,IAAIiE,eAAe;QAEnB,IAAK7W,SAAS,CAAC1N,UAAU,IAAI0N,SAAS,CAAC1N,UAAU,CAAEU,UAAU,CAACoB,0BAA0B,CAAE,EAAG;UAE5F;UACAyiB,eAAe,GAAGH,oBAAoB,CAAE1W,SAAU,CAAC;QAEpD,CAAC,MAAM;UAEN;UACA6W,eAAe,GAAGF,sBAAsB,CAAE,IAAIlsB,cAAc,CAAC,CAAC,EAAEuV,SAAS,EAAEjR,MAAO,CAAC;QAEpF;;QAEA;QACA2H,KAAK,CAAEa,QAAQ,CAAE,GAAG;UAAEyI,SAAS,EAAEA,SAAS;UAAE4S,OAAO,EAAEiE;QAAgB,CAAC;QAEtEpd,OAAO,CAACvH,IAAI,CAAE2kB,eAAgB,CAAC;MAEhC;IAED;IAEA,OAAO/hB,OAAO,CAACqF,GAAG,CAAEV,OAAQ,CAAC;EAE9B;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACC2W,QAAQA,CAAE0G,SAAS,EAAG;IAErB,MAAM/nB,MAAM,GAAG,IAAI;IACnB,MAAMsD,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMC,UAAU,GAAG,IAAI,CAACA,UAAU;IAElC,MAAMwN,OAAO,GAAGzN,IAAI,CAAC0N,MAAM,CAAE+W,SAAS,CAAE;IACxC,MAAM7W,UAAU,GAAGH,OAAO,CAACG,UAAU;IAErC,MAAMxG,OAAO,GAAG,EAAE;IAElB,KAAM,IAAI9F,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAGhL,UAAU,CAACrM,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;MAEvD,MAAM0N,QAAQ,GAAGpB,UAAU,CAAEtM,CAAC,CAAE,CAAC0N,QAAQ,KAAKhO,SAAS,GACpDuW,qBAAqB,CAAE,IAAI,CAAClT,KAAM,CAAC,GACnC,IAAI,CAACqC,aAAa,CAAE,UAAU,EAAEkH,UAAU,CAAEtM,CAAC,CAAE,CAAC0N,QAAS,CAAC;MAE7D5H,OAAO,CAACvH,IAAI,CAAEmP,QAAS,CAAC;IAEzB;IAEA5H,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAAC0nB,cAAc,CAAExW,UAAW,CAAE,CAAC;IAEnD,OAAOnL,OAAO,CAACqF,GAAG,CAAEV,OAAQ,CAAC,CAACN,IAAI,CAAE,UAAWsH,OAAO,EAAG;MAExD,MAAMnG,SAAS,GAAGmG,OAAO,CAACqC,KAAK,CAAE,CAAC,EAAErC,OAAO,CAAC7M,MAAM,GAAG,CAAE,CAAC;MACxD,MAAMmjB,UAAU,GAAGtW,OAAO,CAAEA,OAAO,CAAC7M,MAAM,GAAG,CAAC,CAAE;MAEhD,MAAMmM,MAAM,GAAG,EAAE;MAEjB,KAAM,IAAIpM,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAG8L,UAAU,CAACnjB,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;QAEvD,MAAMyN,QAAQ,GAAG2V,UAAU,CAAEpjB,CAAC,CAAE;QAChC,MAAMqM,SAAS,GAAGC,UAAU,CAAEtM,CAAC,CAAE;;QAEjC;;QAEA,IAAIkM,IAAI;QAER,MAAMwB,QAAQ,GAAG/G,SAAS,CAAE3G,CAAC,CAAE;QAE/B,IAAKqM,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACC,SAAS,IAC/CH,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACE,cAAc,IACjDJ,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACG,YAAY,IAC/CL,SAAS,CAACT,IAAI,KAAKlM,SAAS,EAAG;UAEhC;UACAwM,IAAI,GAAGC,OAAO,CAACyP,aAAa,KAAK,IAAI,GAClC,IAAI7hB,WAAW,CAAE0T,QAAQ,EAAEC,QAAS,CAAC,GACrC,IAAI/U,IAAI,CAAE8U,QAAQ,EAAEC,QAAS,CAAC;UAEjC,IAAKxB,IAAI,CAAC0P,aAAa,KAAK,IAAI,EAAG;YAElC;YACA1P,IAAI,CAACmX,oBAAoB,CAAC,CAAC;UAE5B;UAEA,IAAKhX,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACE,cAAc,EAAG;YAExDP,IAAI,CAACuB,QAAQ,GAAG9S,mBAAmB,CAAEuR,IAAI,CAACuB,QAAQ,EAAEpT,qBAAsB,CAAC;UAE5E,CAAC,MAAM,IAAKgS,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACG,YAAY,EAAG;YAE7DR,IAAI,CAACuB,QAAQ,GAAG9S,mBAAmB,CAAEuR,IAAI,CAACuB,QAAQ,EAAErT,mBAAoB,CAAC;UAE1E;QAED,CAAC,MAAM,IAAKiS,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACyH,KAAK,EAAG;UAEtD9H,IAAI,GAAG,IAAIjU,YAAY,CAAEwV,QAAQ,EAAEC,QAAS,CAAC;QAE9C,CAAC,MAAM,IAAKrB,SAAS,CAACT,IAAI,KAAKW,eAAe,CAAC2H,UAAU,EAAG;UAE3DhI,IAAI,GAAG,IAAIpU,IAAI,CAAE2V,QAAQ,EAAEC,QAAS,CAAC;QAEtC,CAAC,MAAM,IAAKrB,SAAS,CAACT,IAAI,KAAKW,eAAe,CAAC0H,SAAS,EAAG;UAE1D/H,IAAI,GAAG,IAAIlU,QAAQ,CAAEyV,QAAQ,EAAEC,QAAS,CAAC;QAE1C,CAAC,MAAM,IAAKrB,SAAS,CAACT,IAAI,KAAKW,eAAe,CAACwH,MAAM,EAAG;UAEvD7H,IAAI,GAAG,IAAI1S,MAAM,CAAEiU,QAAQ,EAAEC,QAAS,CAAC;QAExC,CAAC,MAAM;UAEN,MAAM,IAAI9N,KAAK,CAAE,gDAAgD,GAAGyM,SAAS,CAACT,IAAK,CAAC;QAErF;QAEA,IAAKmL,MAAM,CAAC6B,IAAI,CAAE1M,IAAI,CAACuB,QAAQ,CAACsK,eAAgB,CAAC,CAAC9X,MAAM,GAAG,CAAC,EAAG;UAE9DgY,kBAAkB,CAAE/L,IAAI,EAAEC,OAAQ,CAAC;QAEpC;QAEAD,IAAI,CAAC/L,IAAI,GAAG/E,MAAM,CAAC+J,gBAAgB,CAAEgH,OAAO,CAAChM,IAAI,IAAM,OAAO,GAAGgjB,SAAY,CAAC;QAE9Ele,sBAAsB,CAAEiH,IAAI,EAAEC,OAAQ,CAAC;QAEvC,IAAKE,SAAS,CAAC1N,UAAU,EAAG6X,8BAA8B,CAAE7X,UAAU,EAAEuN,IAAI,EAAEG,SAAU,CAAC;QAEzFjR,MAAM,CAACsT,mBAAmB,CAAExC,IAAK,CAAC;QAElCE,MAAM,CAAC7N,IAAI,CAAE2N,IAAK,CAAC;MAEpB;MAEA,KAAM,IAAIlM,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAGlL,MAAM,CAACnM,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;QAEnD5E,MAAM,CAAC8d,YAAY,CAAC1U,GAAG,CAAE4H,MAAM,CAAEpM,CAAC,CAAE,EAAE;UACrCoM,MAAM,EAAE+W,SAAS;UACjB7W,UAAU,EAAEtM;QACb,CAAE,CAAC;MAEJ;MAEA,IAAKoM,MAAM,CAACnM,MAAM,KAAK,CAAC,EAAG;QAE1B,IAAKkM,OAAO,CAACxN,UAAU,EAAG6X,8BAA8B,CAAE7X,UAAU,EAAEyN,MAAM,CAAE,CAAC,CAAE,EAAED,OAAQ,CAAC;QAE5F,OAAOC,MAAM,CAAE,CAAC,CAAE;MAEnB;MAEA,MAAMkX,KAAK,GAAG,IAAIhsB,KAAK,CAAC,CAAC;MAEzB,IAAK6U,OAAO,CAACxN,UAAU,EAAG6X,8BAA8B,CAAE7X,UAAU,EAAE2kB,KAAK,EAAEnX,OAAQ,CAAC;MAEtF/Q,MAAM,CAAC8d,YAAY,CAAC1U,GAAG,CAAE8e,KAAK,EAAE;QAAElX,MAAM,EAAE+W;MAAU,CAAE,CAAC;MAEvD,KAAM,IAAInjB,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAGlL,MAAM,CAACnM,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;QAEnDsjB,KAAK,CAAC5hB,GAAG,CAAE0K,MAAM,CAAEpM,CAAC,CAAG,CAAC;MAEzB;MAEA,OAAOsjB,KAAK;IAEb,CAAE,CAAC;EAEJ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACCvG,UAAUA,CAAEwG,WAAW,EAAG;IAEzB,IAAI1H,MAAM;IACV,MAAM2H,SAAS,GAAG,IAAI,CAAC9kB,IAAI,CAACsc,OAAO,CAAEuI,WAAW,CAAE;IAClD,MAAME,MAAM,GAAGD,SAAS,CAAEA,SAAS,CAACnf,IAAI,CAAE;IAE1C,IAAK,CAAEof,MAAM,EAAG;MAEfrmB,OAAO,CAAC2D,IAAI,CAAE,8CAA+C,CAAC;MAC9D;IAED;IAEA,IAAKyiB,SAAS,CAACnf,IAAI,KAAK,aAAa,EAAG;MAEvCwX,MAAM,GAAG,IAAIviB,iBAAiB,CAAEb,SAAS,CAACirB,QAAQ,CAAED,MAAM,CAACE,IAAK,CAAC,EAAEF,MAAM,CAACG,WAAW,IAAI,CAAC,EAAEH,MAAM,CAACI,KAAK,IAAI,CAAC,EAAEJ,MAAM,CAACK,IAAI,IAAI,GAAI,CAAC;IAEpI,CAAC,MAAM,IAAKN,SAAS,CAACnf,IAAI,KAAK,cAAc,EAAG;MAE/CwX,MAAM,GAAG,IAAIxiB,kBAAkB,CAAE,CAAEoqB,MAAM,CAACM,IAAI,EAAEN,MAAM,CAACM,IAAI,EAAEN,MAAM,CAACO,IAAI,EAAE,CAAEP,MAAM,CAACO,IAAI,EAAEP,MAAM,CAACI,KAAK,EAAEJ,MAAM,CAACK,IAAK,CAAC;IAErH;IAEA,IAAKN,SAAS,CAACrjB,IAAI,EAAG0b,MAAM,CAAC1b,IAAI,GAAG,IAAI,CAACgF,gBAAgB,CAAEqe,SAAS,CAACrjB,IAAK,CAAC;IAE3E8E,sBAAsB,CAAE4W,MAAM,EAAE2H,SAAU,CAAC;IAE3C,OAAOriB,OAAO,CAACC,OAAO,CAAEya,MAAO,CAAC;EAEjC;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACCgB,QAAQA,CAAEtB,SAAS,EAAG;IAErB,MAAM0I,OAAO,GAAG,IAAI,CAACvlB,IAAI,CAAC2c,KAAK,CAAEE,SAAS,CAAE;IAE5C,MAAMzV,OAAO,GAAG,EAAE;IAElB,KAAM,IAAI9F,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAG2M,OAAO,CAACxI,MAAM,CAACxb,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;MAE3D8F,OAAO,CAACvH,IAAI,CAAE,IAAI,CAAC2lB,gBAAgB,CAAED,OAAO,CAACxI,MAAM,CAAEzb,CAAC,CAAG,CAAE,CAAC;IAE7D;IAEA,IAAKikB,OAAO,CAACE,mBAAmB,KAAKzkB,SAAS,EAAG;MAEhDoG,OAAO,CAACvH,IAAI,CAAE,IAAI,CAAC6G,aAAa,CAAE,UAAU,EAAE6e,OAAO,CAACE,mBAAoB,CAAE,CAAC;IAE9E,CAAC,MAAM;MAENre,OAAO,CAACvH,IAAI,CAAE,IAAK,CAAC;IAErB;IAEA,OAAO4C,OAAO,CAACqF,GAAG,CAAEV,OAAQ,CAAC,CAACN,IAAI,CAAE,UAAWsH,OAAO,EAAG;MAExD,MAAMqX,mBAAmB,GAAGrX,OAAO,CAACE,GAAG,CAAC,CAAC;MACzC,MAAMoX,UAAU,GAAGtX,OAAO;;MAE1B;MACA;;MAEA,MAAMuX,KAAK,GAAG,EAAE;MAChB,MAAMC,YAAY,GAAG,EAAE;MAEvB,KAAM,IAAItkB,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAG8M,UAAU,CAACnkB,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;QAEvD,MAAMukB,SAAS,GAAGH,UAAU,CAAEpkB,CAAC,CAAE;QAEjC,IAAKukB,SAAS,EAAG;UAEhBF,KAAK,CAAC9lB,IAAI,CAAEgmB,SAAU,CAAC;UAEvB,MAAMC,GAAG,GAAG,IAAI9rB,OAAO,CAAC,CAAC;UAEzB,IAAKyrB,mBAAmB,KAAK,IAAI,EAAG;YAEnCK,GAAG,CAACrT,SAAS,CAAEgT,mBAAmB,CAAC9d,KAAK,EAAErG,CAAC,GAAG,EAAG,CAAC;UAEnD;UAEAskB,YAAY,CAAC/lB,IAAI,CAAEimB,GAAI,CAAC;QAEzB,CAAC,MAAM;UAENpnB,OAAO,CAAC2D,IAAI,CAAE,kDAAkD,EAAEkjB,OAAO,CAACxI,MAAM,CAAEzb,CAAC,CAAG,CAAC;QAExF;MAED;MAEA,OAAO,IAAIlG,QAAQ,CAAEuqB,KAAK,EAAEC,YAAa,CAAC;IAE3C,CAAE,CAAC;EAEJ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACCxH,aAAaA,CAAE2H,cAAc,EAAG;IAE/B,MAAM/lB,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMtD,MAAM,GAAG,IAAI;IAEnB,MAAMspB,YAAY,GAAGhmB,IAAI,CAACqc,UAAU,CAAE0J,cAAc,CAAE;IACtD,MAAME,aAAa,GAAGD,YAAY,CAACvkB,IAAI,GAAGukB,YAAY,CAACvkB,IAAI,GAAG,YAAY,GAAGskB,cAAc;IAE3F,MAAMG,YAAY,GAAG,EAAE;IACvB,MAAMC,qBAAqB,GAAG,EAAE;IAChC,MAAMC,sBAAsB,GAAG,EAAE;IACjC,MAAMC,eAAe,GAAG,EAAE;IAC1B,MAAMC,cAAc,GAAG,EAAE;IAEzB,KAAM,IAAIhlB,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAGoN,YAAY,CAACO,QAAQ,CAAChlB,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;MAElE,MAAM+Q,OAAO,GAAG2T,YAAY,CAACO,QAAQ,CAAEjlB,CAAC,CAAE;MAC1C,MAAMgf,OAAO,GAAG0F,YAAY,CAACrF,QAAQ,CAAEtO,OAAO,CAACiO,OAAO,CAAE;MACxD,MAAM1a,MAAM,GAAGyM,OAAO,CAACzM,MAAM;MAC7B,MAAMnE,IAAI,GAAGmE,MAAM,CAAC4gB,IAAI;MACxB,MAAMC,KAAK,GAAGT,YAAY,CAACU,UAAU,KAAK1lB,SAAS,GAAGglB,YAAY,CAACU,UAAU,CAAEpG,OAAO,CAACmG,KAAK,CAAE,GAAGnG,OAAO,CAACmG,KAAK;MAC9G,MAAME,MAAM,GAAGX,YAAY,CAACU,UAAU,KAAK1lB,SAAS,GAAGglB,YAAY,CAACU,UAAU,CAAEpG,OAAO,CAACqG,MAAM,CAAE,GAAGrG,OAAO,CAACqG,MAAM;MAEjH,IAAK/gB,MAAM,CAAC4gB,IAAI,KAAKxlB,SAAS,EAAG;MAEjCklB,YAAY,CAACrmB,IAAI,CAAE,IAAI,CAAC6G,aAAa,CAAE,MAAM,EAAEjF,IAAK,CAAE,CAAC;MACvD0kB,qBAAqB,CAACtmB,IAAI,CAAE,IAAI,CAAC6G,aAAa,CAAE,UAAU,EAAE+f,KAAM,CAAE,CAAC;MACrEL,sBAAsB,CAACvmB,IAAI,CAAE,IAAI,CAAC6G,aAAa,CAAE,UAAU,EAAEigB,MAAO,CAAE,CAAC;MACvEN,eAAe,CAACxmB,IAAI,CAAEygB,OAAQ,CAAC;MAC/BgG,cAAc,CAACzmB,IAAI,CAAE+F,MAAO,CAAC;IAE9B;IAEA,OAAOnD,OAAO,CAACqF,GAAG,CAAE,CAEnBrF,OAAO,CAACqF,GAAG,CAAEoe,YAAa,CAAC,EAC3BzjB,OAAO,CAACqF,GAAG,CAAEqe,qBAAsB,CAAC,EACpC1jB,OAAO,CAACqF,GAAG,CAAEse,sBAAuB,CAAC,EACrC3jB,OAAO,CAACqF,GAAG,CAAEue,eAAgB,CAAC,EAC9B5jB,OAAO,CAACqF,GAAG,CAAEwe,cAAe,CAAC,CAE5B,CAAC,CAACxf,IAAI,CAAE,UAAWoV,YAAY,EAAG;MAEnC,MAAMxX,KAAK,GAAGwX,YAAY,CAAE,CAAC,CAAE;MAC/B,MAAM0K,cAAc,GAAG1K,YAAY,CAAE,CAAC,CAAE;MACxC,MAAM2K,eAAe,GAAG3K,YAAY,CAAE,CAAC,CAAE;MACzC,MAAMyE,QAAQ,GAAGzE,YAAY,CAAE,CAAC,CAAE;MAClC,MAAM1D,OAAO,GAAG0D,YAAY,CAAE,CAAC,CAAE;MAEjC,MAAM4K,MAAM,GAAG,EAAE;MAEjB,KAAM,IAAIxlB,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAGlU,KAAK,CAACnD,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;QAElD,MAAMklB,IAAI,GAAG9hB,KAAK,CAAEpD,CAAC,CAAE;QACvB,MAAMylB,aAAa,GAAGH,cAAc,CAAEtlB,CAAC,CAAE;QACzC,MAAM0lB,cAAc,GAAGH,eAAe,CAAEvlB,CAAC,CAAE;QAC3C,MAAMgf,OAAO,GAAGK,QAAQ,CAAErf,CAAC,CAAE;QAC7B,MAAMsE,MAAM,GAAG4S,OAAO,CAAElX,CAAC,CAAE;QAE3B,IAAKklB,IAAI,KAAKxlB,SAAS,EAAG;QAE1B,IAAKwlB,IAAI,CAACS,YAAY,EAAG;UAExBT,IAAI,CAACS,YAAY,CAAC,CAAC;QAEpB;QAEA,MAAMC,aAAa,GAAGxqB,MAAM,CAACyqB,sBAAsB,CAAEX,IAAI,EAAEO,aAAa,EAAEC,cAAc,EAAE1G,OAAO,EAAE1a,MAAO,CAAC;QAE3G,IAAKshB,aAAa,EAAG;UAEpB,KAAM,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,aAAa,CAAC3lB,MAAM,EAAE6lB,CAAC,EAAG,EAAG;YAEjDN,MAAM,CAACjnB,IAAI,CAAEqnB,aAAa,CAAEE,CAAC,CAAG,CAAC;UAElC;QAED;MAED;MAEA,OAAO,IAAIpvB,aAAa,CAAEiuB,aAAa,EAAEjlB,SAAS,EAAE8lB,MAAO,CAAC;IAE7D,CAAE,CAAC;EAEJ;EAEAvZ,cAAcA,CAAE5I,SAAS,EAAG;IAE3B,MAAM3E,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMtD,MAAM,GAAG,IAAI;IACnB,MAAMmI,OAAO,GAAG7E,IAAI,CAAC0E,KAAK,CAAEC,SAAS,CAAE;IAEvC,IAAKE,OAAO,CAAC2I,IAAI,KAAKxM,SAAS,EAAG,OAAO,IAAI;IAE7C,OAAOtE,MAAM,CAACgK,aAAa,CAAE,MAAM,EAAE7B,OAAO,CAAC2I,IAAK,CAAC,CAAC1G,IAAI,CAAE,UAAW0G,IAAI,EAAG;MAE3E,MAAMgZ,IAAI,GAAG9pB,MAAM,CAACqK,WAAW,CAAErK,MAAM,CAACke,SAAS,EAAE/V,OAAO,CAAC2I,IAAI,EAAEA,IAAK,CAAC;;MAEvE;MACA,IAAK3I,OAAO,CAACkS,OAAO,KAAK/V,SAAS,EAAG;QAEpCwlB,IAAI,CAACa,QAAQ,CAAE,UAAWC,CAAC,EAAG;UAE7B,IAAK,CAAEA,CAAC,CAACC,MAAM,EAAG;UAElB,KAAM,IAAIjmB,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAG/T,OAAO,CAACkS,OAAO,CAACxV,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;YAE5DgmB,CAAC,CAAC9N,qBAAqB,CAAElY,CAAC,CAAE,GAAGuD,OAAO,CAACkS,OAAO,CAAEzV,CAAC,CAAE;UAEpD;QAED,CAAE,CAAC;MAEJ;MAEA,OAAOklB,IAAI;IAEZ,CAAE,CAAC;EAEJ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACC1I,QAAQA,CAAEnZ,SAAS,EAAG;IAErB,MAAM3E,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMtD,MAAM,GAAG,IAAI;IAEnB,MAAMmI,OAAO,GAAG7E,IAAI,CAAC0E,KAAK,CAAEC,SAAS,CAAE;IAEvC,MAAM6iB,WAAW,GAAG9qB,MAAM,CAAC8oB,gBAAgB,CAAE7gB,SAAU,CAAC;IAExD,MAAM8iB,YAAY,GAAG,EAAE;IACvB,MAAMC,WAAW,GAAG7iB,OAAO,CAAC2J,QAAQ,IAAI,EAAE;IAE1C,KAAM,IAAIlN,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAG8O,WAAW,CAACnmB,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;MAExDmmB,YAAY,CAAC5nB,IAAI,CAAEnD,MAAM,CAACgK,aAAa,CAAE,MAAM,EAAEghB,WAAW,CAAEpmB,CAAC,CAAG,CAAE,CAAC;IAEtE;IAEA,MAAMqmB,eAAe,GAAG9iB,OAAO,CAACoY,IAAI,KAAKjc,SAAS,GAC/CyB,OAAO,CAACC,OAAO,CAAE,IAAK,CAAC,GACvBhG,MAAM,CAACgK,aAAa,CAAE,MAAM,EAAE7B,OAAO,CAACoY,IAAK,CAAC;IAE/C,OAAOxa,OAAO,CAACqF,GAAG,CAAE,CACnB0f,WAAW,EACX/kB,OAAO,CAACqF,GAAG,CAAE2f,YAAa,CAAC,EAC3BE,eAAe,CACd,CAAC,CAAC7gB,IAAI,CAAE,UAAWsH,OAAO,EAAG;MAE9B,MAAMoY,IAAI,GAAGpY,OAAO,CAAE,CAAC,CAAE;MACzB,MAAMI,QAAQ,GAAGJ,OAAO,CAAE,CAAC,CAAE;MAC7B,MAAMwZ,QAAQ,GAAGxZ,OAAO,CAAE,CAAC,CAAE;MAE7B,IAAKwZ,QAAQ,KAAK,IAAI,EAAG;QAExB;QACA;QACApB,IAAI,CAACa,QAAQ,CAAE,UAAW7Z,IAAI,EAAG;UAEhC,IAAK,CAAEA,IAAI,CAAC0P,aAAa,EAAG;UAE5B1P,IAAI,CAACqa,IAAI,CAAED,QAAQ,EAAErN,eAAgB,CAAC;QAEvC,CAAE,CAAC;MAEJ;MAEA,KAAM,IAAIjZ,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAGpK,QAAQ,CAACjN,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;QAErDklB,IAAI,CAACxjB,GAAG,CAAEwL,QAAQ,CAAElN,CAAC,CAAG,CAAC;MAE1B;MAEA,OAAOklB,IAAI;IAEZ,CAAE,CAAC;EAEJ;;EAEA;EACA;EACAhB,gBAAgBA,CAAE7gB,SAAS,EAAG;IAE7B,MAAM3E,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMC,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,MAAMvD,MAAM,GAAG,IAAI;;IAEnB;IACA;;IAEA,IAAK,IAAI,CAACie,SAAS,CAAEhW,SAAS,CAAE,KAAK3D,SAAS,EAAG;MAEhD,OAAO,IAAI,CAAC2Z,SAAS,CAAEhW,SAAS,CAAE;IAEnC;IAEA,MAAME,OAAO,GAAG7E,IAAI,CAAC0E,KAAK,CAAEC,SAAS,CAAE;;IAEvC;IACA,MAAMmjB,QAAQ,GAAGjjB,OAAO,CAACpD,IAAI,GAAG/E,MAAM,CAAC+J,gBAAgB,CAAE5B,OAAO,CAACpD,IAAK,CAAC,GAAG,EAAE;IAE5E,MAAM2F,OAAO,GAAG,EAAE;IAElB,MAAM2gB,WAAW,GAAGrrB,MAAM,CAACghB,UAAU,CAAE,UAAW3B,GAAG,EAAG;MAEvD,OAAOA,GAAG,CAACxO,cAAc,IAAIwO,GAAG,CAACxO,cAAc,CAAE5I,SAAU,CAAC;IAE7D,CAAE,CAAC;IAEH,IAAKojB,WAAW,EAAG;MAElB3gB,OAAO,CAACvH,IAAI,CAAEkoB,WAAY,CAAC;IAE5B;IAEA,IAAKljB,OAAO,CAACsY,MAAM,KAAKnc,SAAS,EAAG;MAEnCoG,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACgK,aAAa,CAAE,QAAQ,EAAE7B,OAAO,CAACsY,MAAO,CAAC,CAACrW,IAAI,CAAE,UAAWqW,MAAM,EAAG;QAExF,OAAOzgB,MAAM,CAACqK,WAAW,CAAErK,MAAM,CAACme,WAAW,EAAEhW,OAAO,CAACsY,MAAM,EAAEA,MAAO,CAAC;MAExE,CAAE,CAAE,CAAC;IAEN;IAEAzgB,MAAM,CAACof,UAAU,CAAE,UAAWC,GAAG,EAAG;MAEnC,OAAOA,GAAG,CAACnV,oBAAoB,IAAImV,GAAG,CAACnV,oBAAoB,CAAEjC,SAAU,CAAC;IAEzE,CAAE,CAAC,CAACqjB,OAAO,CAAE,UAAWzH,OAAO,EAAG;MAEjCnZ,OAAO,CAACvH,IAAI,CAAE0gB,OAAQ,CAAC;IAExB,CAAE,CAAC;IAEH,IAAI,CAAC5F,SAAS,CAAEhW,SAAS,CAAE,GAAGlC,OAAO,CAACqF,GAAG,CAAEV,OAAQ,CAAC,CAACN,IAAI,CAAE,UAAWjE,OAAO,EAAG;MAE/E,IAAI2jB,IAAI;;MAER;MACA,IAAK3hB,OAAO,CAACmY,MAAM,KAAK,IAAI,EAAG;QAE9BwJ,IAAI,GAAG,IAAIvuB,IAAI,CAAC,CAAC;MAElB,CAAC,MAAM,IAAK4K,OAAO,CAACtB,MAAM,GAAG,CAAC,EAAG;QAEhCilB,IAAI,GAAG,IAAI5tB,KAAK,CAAC,CAAC;MAEnB,CAAC,MAAM,IAAKiK,OAAO,CAACtB,MAAM,KAAK,CAAC,EAAG;QAElCilB,IAAI,GAAG3jB,OAAO,CAAE,CAAC,CAAE;MAEpB,CAAC,MAAM;QAEN2jB,IAAI,GAAG,IAAI9rB,QAAQ,CAAC,CAAC;MAEtB;MAEA,IAAK8rB,IAAI,KAAK3jB,OAAO,CAAE,CAAC,CAAE,EAAG;QAE5B,KAAM,IAAIvB,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAG/V,OAAO,CAACtB,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;UAEpDklB,IAAI,CAACxjB,GAAG,CAAEH,OAAO,CAAEvB,CAAC,CAAG,CAAC;QAEzB;MAED;MAEA,IAAKuD,OAAO,CAACpD,IAAI,EAAG;QAEnB+kB,IAAI,CAACvO,QAAQ,CAACxW,IAAI,GAAGoD,OAAO,CAACpD,IAAI;QACjC+kB,IAAI,CAAC/kB,IAAI,GAAGqmB,QAAQ;MAErB;MAEAvhB,sBAAsB,CAAEigB,IAAI,EAAE3hB,OAAQ,CAAC;MAEvC,IAAKA,OAAO,CAAC5E,UAAU,EAAG6X,8BAA8B,CAAE7X,UAAU,EAAEumB,IAAI,EAAE3hB,OAAQ,CAAC;MAErF,IAAKA,OAAO,CAACojB,MAAM,KAAKjnB,SAAS,EAAG;QAEnC,MAAMinB,MAAM,GAAG,IAAIjuB,OAAO,CAAC,CAAC;QAC5BiuB,MAAM,CAACxV,SAAS,CAAE5N,OAAO,CAACojB,MAAO,CAAC;QAClCzB,IAAI,CAAC0B,YAAY,CAAED,MAAO,CAAC;MAE5B,CAAC,MAAM;QAEN,IAAKpjB,OAAO,CAACiS,WAAW,KAAK9V,SAAS,EAAG;UAExCwlB,IAAI,CAAC3gB,QAAQ,CAAC4M,SAAS,CAAE5N,OAAO,CAACiS,WAAY,CAAC;QAE/C;QAEA,IAAKjS,OAAO,CAAC0N,QAAQ,KAAKvR,SAAS,EAAG;UAErCwlB,IAAI,CAAC2B,UAAU,CAAC1V,SAAS,CAAE5N,OAAO,CAAC0N,QAAS,CAAC;QAE9C;QAEA,IAAK1N,OAAO,CAAC+D,KAAK,KAAK5H,SAAS,EAAG;UAElCwlB,IAAI,CAAC5d,KAAK,CAAC6J,SAAS,CAAE5N,OAAO,CAAC+D,KAAM,CAAC;QAEtC;MAED;MAEA,IAAK,CAAElM,MAAM,CAAC8d,YAAY,CAAC4N,GAAG,CAAE5B,IAAK,CAAC,EAAG;QAExC9pB,MAAM,CAAC8d,YAAY,CAAC1U,GAAG,CAAE0gB,IAAI,EAAE,CAAC,CAAE,CAAC;MAEpC,CAAC,MAAM,IAAK3hB,OAAO,CAAC2I,IAAI,KAAKxM,SAAS,IAAItE,MAAM,CAACke,SAAS,CAACtW,IAAI,CAAEO,OAAO,CAAC2I,IAAI,CAAE,GAAG,CAAC,EAAG;QAErF,MAAM6a,OAAO,GAAG3rB,MAAM,CAAC8d,YAAY,CAAC1X,GAAG,CAAE0jB,IAAK,CAAC;QAC/C9pB,MAAM,CAAC8d,YAAY,CAAC1U,GAAG,CAAE0gB,IAAI,EAAE;UAAE,GAAG6B;QAAQ,CAAE,CAAC;MAEhD;MAEA3rB,MAAM,CAAC8d,YAAY,CAAC1X,GAAG,CAAE0jB,IAAK,CAAC,CAAC9hB,KAAK,GAAGC,SAAS;MAEjD,OAAO6hB,IAAI;IAEZ,CAAE,CAAC;IAEH,OAAO,IAAI,CAAC7L,SAAS,CAAEhW,SAAS,CAAE;EAEnC;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACCkZ,SAASA,CAAEyK,UAAU,EAAG;IAEvB,MAAMroB,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,MAAMsoB,QAAQ,GAAG,IAAI,CAACvoB,IAAI,CAACoc,MAAM,CAAEkM,UAAU,CAAE;IAC/C,MAAM5rB,MAAM,GAAG,IAAI;;IAEnB;IACA;IACA,MAAMyf,KAAK,GAAG,IAAIvjB,KAAK,CAAC,CAAC;IACzB,IAAK2vB,QAAQ,CAAC9mB,IAAI,EAAG0a,KAAK,CAAC1a,IAAI,GAAG/E,MAAM,CAAC+J,gBAAgB,CAAE8hB,QAAQ,CAAC9mB,IAAK,CAAC;IAE1E8E,sBAAsB,CAAE4V,KAAK,EAAEoM,QAAS,CAAC;IAEzC,IAAKA,QAAQ,CAACtoB,UAAU,EAAG6X,8BAA8B,CAAE7X,UAAU,EAAEkc,KAAK,EAAEoM,QAAS,CAAC;IAExF,MAAMC,OAAO,GAAGD,QAAQ,CAAC7jB,KAAK,IAAI,EAAE;IAEpC,MAAM0C,OAAO,GAAG,EAAE;IAElB,KAAM,IAAI9F,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAG4P,OAAO,CAACjnB,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;MAEpD8F,OAAO,CAACvH,IAAI,CAAEnD,MAAM,CAACgK,aAAa,CAAE,MAAM,EAAE8hB,OAAO,CAAElnB,CAAC,CAAG,CAAE,CAAC;IAE7D;IAEA,OAAOmB,OAAO,CAACqF,GAAG,CAAEV,OAAQ,CAAC,CAACN,IAAI,CAAE,UAAWpC,KAAK,EAAG;MAEtD,KAAM,IAAIpD,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAGlU,KAAK,CAACnD,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;QAElD6a,KAAK,CAACnZ,GAAG,CAAE0B,KAAK,CAAEpD,CAAC,CAAG,CAAC;MAExB;;MAEA;MACA;MACA,MAAMmnB,kBAAkB,GAAKjC,IAAI,IAAM;QAEtC,MAAMkC,mBAAmB,GAAG,IAAIjO,GAAG,CAAC,CAAC;QAErC,KAAM,MAAM,CAAE1X,GAAG,EAAE4lB,KAAK,CAAE,IAAIjsB,MAAM,CAAC8d,YAAY,EAAG;UAEnD,IAAKzX,GAAG,YAAYjJ,QAAQ,IAAIiJ,GAAG,YAAYvH,OAAO,EAAG;YAExDktB,mBAAmB,CAAC5iB,GAAG,CAAE/C,GAAG,EAAE4lB,KAAM,CAAC;UAEtC;QAED;QAEAnC,IAAI,CAACa,QAAQ,CAAIb,IAAI,IAAM;UAE1B,MAAMjJ,QAAQ,GAAG7gB,MAAM,CAAC8d,YAAY,CAAC1X,GAAG,CAAE0jB,IAAK,CAAC;UAEhD,IAAKjJ,QAAQ,IAAI,IAAI,EAAG;YAEvBmL,mBAAmB,CAAC5iB,GAAG,CAAE0gB,IAAI,EAAEjJ,QAAS,CAAC;UAE1C;QAED,CAAE,CAAC;QAEH,OAAOmL,mBAAmB;MAE3B,CAAC;MAEDhsB,MAAM,CAAC8d,YAAY,GAAGiO,kBAAkB,CAAEtM,KAAM,CAAC;MAEjD,OAAOA,KAAK;IAEb,CAAE,CAAC;EAEJ;EAEAgL,sBAAsBA,CAAEX,IAAI,EAAEO,aAAa,EAAEC,cAAc,EAAE1G,OAAO,EAAE1a,MAAM,EAAG;IAE9E,MAAMkhB,MAAM,GAAG,EAAE;IAEjB,MAAM8B,UAAU,GAAGpC,IAAI,CAAC/kB,IAAI,GAAG+kB,IAAI,CAAC/kB,IAAI,GAAG+kB,IAAI,CAAClE,IAAI;IACpD,MAAM7I,WAAW,GAAG,EAAE;IAEtB,IAAK5C,eAAe,CAAEjR,MAAM,CAACzH,IAAI,CAAE,KAAK0Y,eAAe,CAACE,OAAO,EAAG;MAEjEyP,IAAI,CAACa,QAAQ,CAAE,UAAWpkB,MAAM,EAAG;QAElC,IAAKA,MAAM,CAACuW,qBAAqB,EAAG;UAEnCC,WAAW,CAAC5Z,IAAI,CAAEoD,MAAM,CAACxB,IAAI,GAAGwB,MAAM,CAACxB,IAAI,GAAGwB,MAAM,CAACqf,IAAK,CAAC;QAE5D;MAED,CAAE,CAAC;IAEJ,CAAC,MAAM;MAEN7I,WAAW,CAAC5Z,IAAI,CAAE+oB,UAAW,CAAC;IAE/B;IAEA,IAAIC,kBAAkB;IAEtB,QAAShS,eAAe,CAAEjR,MAAM,CAACzH,IAAI,CAAE;MAEtC,KAAK0Y,eAAe,CAACE,OAAO;QAE3B8R,kBAAkB,GAAGpuB,mBAAmB;QACxC;MAED,KAAKoc,eAAe,CAACtE,QAAQ;QAE5BsW,kBAAkB,GAAG3tB,uBAAuB;QAC5C;MAED,KAAK2b,eAAe,CAACC,WAAW;MAChC,KAAKD,eAAe,CAACjO,KAAK;QAEzBigB,kBAAkB,GAAG/sB,mBAAmB;QACxC;MAED;QAEC,QAASkrB,cAAc,CAACtX,QAAQ;UAE/B,KAAK,CAAC;YACLmZ,kBAAkB,GAAGpuB,mBAAmB;YACxC;UACD,KAAK,CAAC;UACN,KAAK,CAAC;UACN;YACCouB,kBAAkB,GAAG/sB,mBAAmB;YACxC;QAEF;QAEA;IAEF;IAEA,MAAMgtB,aAAa,GAAGxI,OAAO,CAACwI,aAAa,KAAK9nB,SAAS,GAAGgW,aAAa,CAAEsJ,OAAO,CAACwI,aAAa,CAAE,GAAG3vB,iBAAiB;IAGtH,MAAM4vB,WAAW,GAAG,IAAI,CAACC,qBAAqB,CAAEhC,cAAe,CAAC;IAEhE,KAAM,IAAIiC,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGzP,WAAW,CAAClY,MAAM,EAAE0nB,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAG,EAAG;MAExD,MAAME,KAAK,GAAG,IAAIN,kBAAkB,CACnCpP,WAAW,CAAEwP,CAAC,CAAE,GAAG,GAAG,GAAGpS,eAAe,CAAEjR,MAAM,CAACzH,IAAI,CAAE,EACvD4oB,aAAa,CAACpf,KAAK,EACnBohB,WAAW,EACXD,aACD,CAAC;;MAED;MACA,IAAKxI,OAAO,CAACwI,aAAa,KAAK,aAAa,EAAG;QAE9C,IAAI,CAACM,kCAAkC,CAAED,KAAM,CAAC;MAEjD;MAEArC,MAAM,CAACjnB,IAAI,CAAEspB,KAAM,CAAC;IAErB;IAEA,OAAOrC,MAAM;EAEd;EAEAkC,qBAAqBA,CAAE7a,QAAQ,EAAG;IAEjC,IAAI4a,WAAW,GAAG5a,QAAQ,CAACxG,KAAK;IAEhC,IAAKwG,QAAQ,CAACwB,UAAU,EAAG;MAE1B,MAAM/G,KAAK,GAAGwR,2BAA2B,CAAE2O,WAAW,CAAC5sB,WAAY,CAAC;MACpE,MAAMktB,MAAM,GAAG,IAAItT,YAAY,CAAEgT,WAAW,CAACxnB,MAAO,CAAC;MAErD,KAAM,IAAI0nB,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGH,WAAW,CAACxnB,MAAM,EAAE0nB,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAG,EAAG;QAExDI,MAAM,CAAEJ,CAAC,CAAE,GAAGF,WAAW,CAAEE,CAAC,CAAE,GAAGrgB,KAAK;MAEvC;MAEAmgB,WAAW,GAAGM,MAAM;IAErB;IAEA,OAAON,WAAW;EAEnB;EAEAK,kCAAkCA,CAAED,KAAK,EAAG;IAE3CA,KAAK,CAACG,iBAAiB,GAAG,SAASC,uCAAuCA,CAAElc,MAAM,EAAG;MAEpF;MACA;MACA;;MAEA,MAAMmc,eAAe,GAAK,IAAI,YAAYtuB,uBAAuB,GAAKuZ,oCAAoC,GAAG7B,0BAA0B;MAEvI,OAAO,IAAI4W,eAAe,CAAE,IAAI,CAACC,KAAK,EAAE,IAAI,CAACvW,MAAM,EAAE,IAAI,CAACwW,YAAY,CAAC,CAAC,GAAG,CAAC,EAAErc,MAAO,CAAC;IAEvF,CAAC;;IAED;IACA8b,KAAK,CAACG,iBAAiB,CAACK,yCAAyC,GAAG,IAAI;EAEzE;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAAE7a,QAAQ,EAAE6K,YAAY,EAAEld,MAAM,EAAG;EAExD,MAAMwR,UAAU,GAAG0L,YAAY,CAAC1L,UAAU;EAE1C,MAAM2b,GAAG,GAAG,IAAI3xB,IAAI,CAAC,CAAC;EAEtB,IAAKgW,UAAU,CAACiI,QAAQ,KAAKnV,SAAS,EAAG;IAExC,MAAMmN,QAAQ,GAAGzR,MAAM,CAACsD,IAAI,CAAC4R,SAAS,CAAE1D,UAAU,CAACiI,QAAQ,CAAE;IAE7D,MAAM2T,GAAG,GAAG3b,QAAQ,CAAC2b,GAAG;IACxB,MAAMC,GAAG,GAAG5b,QAAQ,CAAC4b,GAAG;;IAExB;;IAEA,IAAKD,GAAG,KAAK9oB,SAAS,IAAI+oB,GAAG,KAAK/oB,SAAS,EAAG;MAE7C6oB,GAAG,CAAC/jB,GAAG,CACN,IAAIjK,OAAO,CAAEiuB,GAAG,CAAE,CAAC,CAAE,EAAEA,GAAG,CAAE,CAAC,CAAE,EAAEA,GAAG,CAAE,CAAC,CAAG,CAAC,EAC3C,IAAIjuB,OAAO,CAAEkuB,GAAG,CAAE,CAAC,CAAE,EAAEA,GAAG,CAAE,CAAC,CAAE,EAAEA,GAAG,CAAE,CAAC,CAAG,CAC3C,CAAC;MAED,IAAK5b,QAAQ,CAACwB,UAAU,EAAG;QAE1B,MAAMqa,QAAQ,GAAG5P,2BAA2B,CAAEtI,qBAAqB,CAAE3D,QAAQ,CAAC0D,aAAa,CAAG,CAAC;QAC/FgY,GAAG,CAACC,GAAG,CAACG,cAAc,CAAED,QAAS,CAAC;QAClCH,GAAG,CAACE,GAAG,CAACE,cAAc,CAAED,QAAS,CAAC;MAEnC;IAED,CAAC,MAAM;MAENtrB,OAAO,CAAC2D,IAAI,CAAE,qEAAsE,CAAC;MAErF;IAED;EAED,CAAC,MAAM;IAEN;EAED;EAEA,MAAMmW,OAAO,GAAGoB,YAAY,CAACpB,OAAO;EAEpC,IAAKA,OAAO,KAAKxX,SAAS,EAAG;IAE5B,MAAMkpB,eAAe,GAAG,IAAIruB,OAAO,CAAC,CAAC;IACrC,MAAMsuB,MAAM,GAAG,IAAItuB,OAAO,CAAC,CAAC;IAE5B,KAAM,IAAIyF,CAAC,GAAG,CAAC,EAAEsX,EAAE,GAAGJ,OAAO,CAACjX,MAAM,EAAED,CAAC,GAAGsX,EAAE,EAAEtX,CAAC,EAAG,EAAG;MAEpD,MAAMsE,MAAM,GAAG4S,OAAO,CAAElX,CAAC,CAAE;MAE3B,IAAKsE,MAAM,CAACuQ,QAAQ,KAAKnV,SAAS,EAAG;QAEpC,MAAMmN,QAAQ,GAAGzR,MAAM,CAACsD,IAAI,CAAC4R,SAAS,CAAEhM,MAAM,CAACuQ,QAAQ,CAAE;QACzD,MAAM2T,GAAG,GAAG3b,QAAQ,CAAC2b,GAAG;QACxB,MAAMC,GAAG,GAAG5b,QAAQ,CAAC4b,GAAG;;QAExB;;QAEA,IAAKD,GAAG,KAAK9oB,SAAS,IAAI+oB,GAAG,KAAK/oB,SAAS,EAAG;UAE7C;UACAmpB,MAAM,CAACnK,IAAI,CAAE7Z,IAAI,CAAC4jB,GAAG,CAAE5jB,IAAI,CAACikB,GAAG,CAAEN,GAAG,CAAE,CAAC,CAAG,CAAC,EAAE3jB,IAAI,CAACikB,GAAG,CAAEL,GAAG,CAAE,CAAC,CAAG,CAAE,CAAE,CAAC;UACrEI,MAAM,CAAClK,IAAI,CAAE9Z,IAAI,CAAC4jB,GAAG,CAAE5jB,IAAI,CAACikB,GAAG,CAAEN,GAAG,CAAE,CAAC,CAAG,CAAC,EAAE3jB,IAAI,CAACikB,GAAG,CAAEL,GAAG,CAAE,CAAC,CAAG,CAAE,CAAE,CAAC;UACrEI,MAAM,CAACjK,IAAI,CAAE/Z,IAAI,CAAC4jB,GAAG,CAAE5jB,IAAI,CAACikB,GAAG,CAAEN,GAAG,CAAE,CAAC,CAAG,CAAC,EAAE3jB,IAAI,CAACikB,GAAG,CAAEL,GAAG,CAAE,CAAC,CAAG,CAAE,CAAE,CAAC;UAGrE,IAAK5b,QAAQ,CAACwB,UAAU,EAAG;YAE1B,MAAMqa,QAAQ,GAAG5P,2BAA2B,CAAEtI,qBAAqB,CAAE3D,QAAQ,CAAC0D,aAAa,CAAG,CAAC;YAC/FsY,MAAM,CAACF,cAAc,CAAED,QAAS,CAAC;UAElC;;UAEA;UACA;UACA;UACA;UACAE,eAAe,CAACH,GAAG,CAAEI,MAAO,CAAC;QAE9B,CAAC,MAAM;UAENzrB,OAAO,CAAC2D,IAAI,CAAE,qEAAsE,CAAC;QAEtF;MAED;IAED;;IAEA;IACAwnB,GAAG,CAACQ,cAAc,CAAEH,eAAgB,CAAC;EAEtC;EAEAnb,QAAQ,CAACub,WAAW,GAAGT,GAAG;EAE1B,MAAMU,MAAM,GAAG,IAAIjvB,MAAM,CAAC,CAAC;EAE3BuuB,GAAG,CAACW,SAAS,CAAED,MAAM,CAACE,MAAO,CAAC;EAC9BF,MAAM,CAACG,MAAM,GAAGb,GAAG,CAACC,GAAG,CAACa,UAAU,CAAEd,GAAG,CAACE,GAAI,CAAC,GAAG,CAAC;EAEjDhb,QAAQ,CAAC6b,cAAc,GAAGL,MAAM;AAEjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASjG,sBAAsBA,CAAEvV,QAAQ,EAAE6K,YAAY,EAAEld,MAAM,EAAG;EAEjE,MAAMwR,UAAU,GAAG0L,YAAY,CAAC1L,UAAU;EAE1C,MAAM9G,OAAO,GAAG,EAAE;EAElB,SAASyjB,uBAAuBA,CAAEhM,aAAa,EAAEtP,aAAa,EAAG;IAEhE,OAAO7S,MAAM,CAACgK,aAAa,CAAE,UAAU,EAAEmY,aAAc,CAAC,CACtD/X,IAAI,CAAE,UAAWqH,QAAQ,EAAG;MAE5BY,QAAQ,CAACa,YAAY,CAAEL,aAAa,EAAEpB,QAAS,CAAC;IAEjD,CAAE,CAAC;EAEL;EAEA,KAAM,MAAM2c,iBAAiB,IAAI5c,UAAU,EAAG;IAE7C,MAAMsD,kBAAkB,GAAGC,UAAU,CAAEqZ,iBAAiB,CAAE,IAAIA,iBAAiB,CAACpZ,WAAW,CAAC,CAAC;;IAE7F;IACA,IAAKF,kBAAkB,IAAIzC,QAAQ,CAACb,UAAU,EAAG;IAEjD9G,OAAO,CAACvH,IAAI,CAAEgrB,uBAAuB,CAAE3c,UAAU,CAAE4c,iBAAiB,CAAE,EAAEtZ,kBAAmB,CAAE,CAAC;EAE/F;EAEA,IAAKoI,YAAY,CAACG,OAAO,KAAK/Y,SAAS,IAAI,CAAE+N,QAAQ,CAACpI,KAAK,EAAG;IAE7D,MAAMwH,QAAQ,GAAGzR,MAAM,CAACgK,aAAa,CAAE,UAAU,EAAEkT,YAAY,CAACG,OAAQ,CAAC,CAACjT,IAAI,CAAE,UAAWqH,QAAQ,EAAG;MAErGY,QAAQ,CAACgc,QAAQ,CAAE5c,QAAS,CAAC;IAE9B,CAAE,CAAC;IAEH/G,OAAO,CAACvH,IAAI,CAAEsO,QAAS,CAAC;EAEzB;EAEA,IAAK5V,eAAe,CAACyyB,iBAAiB,KAAKrxB,oBAAoB,IAAI,SAAS,IAAIuU,UAAU,EAAG;IAE5FxP,OAAO,CAAC2D,IAAI,CAAE,qEAAqE9J,eAAe,CAACyyB,iBAAiB,kBAAmB,CAAC;EAEzI;EAEAzkB,sBAAsB,CAAEwI,QAAQ,EAAE6K,YAAa,CAAC;EAEhDgQ,aAAa,CAAE7a,QAAQ,EAAE6K,YAAY,EAAEld,MAAO,CAAC;EAE/C,OAAO+F,OAAO,CAACqF,GAAG,CAAEV,OAAQ,CAAC,CAACN,IAAI,CAAE,YAAY;IAE/C,OAAO8S,YAAY,CAACpB,OAAO,KAAKxX,SAAS,GACtCuX,eAAe,CAAExJ,QAAQ,EAAE6K,YAAY,CAACpB,OAAO,EAAE9b,MAAO,CAAC,GACzDqS,QAAQ;EAEZ,CAAE,CAAC;AAEJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS7S,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}