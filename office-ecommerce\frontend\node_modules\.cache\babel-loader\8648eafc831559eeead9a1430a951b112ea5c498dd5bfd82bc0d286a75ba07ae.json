{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { DiscardMaterial } from '../materials/DiscardMaterial.js';\nfunction isLight(object) {\n  return object.isLight;\n}\nfunction isGeometry(object) {\n  return !!object.geometry;\n}\nconst accumulativeContext = /*#__PURE__*/React.createContext(null);\nconst SoftShadowMaterial = shaderMaterial({\n  color: new THREE.Color(),\n  blend: 2.0,\n  alphaTest: 0.75,\n  opacity: 0,\n  map: null\n}, `varying vec2 vUv;\n   void main() {\n     gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n     vUv = uv;\n   }`, `varying vec2 vUv;\n   uniform sampler2D map;\n   uniform vec3 color;\n   uniform float opacity;\n   uniform float alphaTest;\n   uniform float blend;\n   void main() {\n     vec4 sampledDiffuseColor = texture2D(map, vUv);\n     gl_FragColor = vec4(color * sampledDiffuseColor.r * blend, max(0.0, (1.0 - (sampledDiffuseColor.r + sampledDiffuseColor.g + sampledDiffuseColor.b) / alphaTest)) * opacity);\n     #include <tonemapping_fragment>\n     #include <encodings_fragment>\n   }`);\nconst AccumulativeShadows = /*#__PURE__*/React.forwardRef(({\n  children,\n  temporal,\n  frames = 40,\n  limit = Infinity,\n  blend = 20,\n  scale = 10,\n  opacity = 1,\n  alphaTest = 0.75,\n  color = 'black',\n  colorBlend = 2,\n  resolution = 1024,\n  toneMapped = true,\n  ...props\n}, forwardRef) => {\n  extend({\n    SoftShadowMaterial\n  });\n  const gl = useThree(state => state.gl);\n  const scene = useThree(state => state.scene);\n  const camera = useThree(state => state.camera);\n  const invalidate = useThree(state => state.invalidate);\n  const gPlane = React.useRef(null);\n  const gLights = React.useRef(null);\n  const [plm] = React.useState(() => new ProgressiveLightMap(gl, scene, resolution));\n  React.useLayoutEffect(() => {\n    plm.configure(gPlane.current);\n  }, []);\n  const api = React.useMemo(() => ({\n    lights: new Map(),\n    temporal: !!temporal,\n    frames: Math.max(2, frames),\n    blend: Math.max(2, frames === Infinity ? blend : frames),\n    count: 0,\n    getMesh: () => gPlane.current,\n    reset: () => {\n      // Clear buffers, reset opacities, set frame count to 0\n      plm.clear();\n      const material = gPlane.current.material;\n      material.opacity = 0;\n      material.alphaTest = 0;\n      api.count = 0;\n    },\n    update: (frames = 1) => {\n      // Adapt the opacity-blend ratio to the number of frames\n      const material = gPlane.current.material;\n      if (!api.temporal) {\n        material.opacity = opacity;\n        material.alphaTest = alphaTest;\n      } else {\n        material.opacity = Math.min(opacity, material.opacity + opacity / api.blend);\n        material.alphaTest = Math.min(alphaTest, material.alphaTest + alphaTest / api.blend);\n      } // Switch accumulative lights on\n\n      gLights.current.visible = true; // Collect scene lights and meshes\n\n      plm.prepare(); // Update the lightmap and the accumulative lights\n\n      for (let i = 0; i < frames; i++) {\n        api.lights.forEach(light => light.update());\n        plm.update(camera, api.blend);\n      } // Switch lights off\n\n      gLights.current.visible = false; // Restore lights and meshes\n\n      plm.finish();\n    }\n  }), [plm, camera, scene, temporal, frames, blend, opacity, alphaTest]);\n  React.useLayoutEffect(() => {\n    // Reset internals, buffers, ...\n    api.reset(); // Update lightmap\n\n    if (!api.temporal && api.frames !== Infinity) api.update(api.blend);\n  }); // Expose api, allow children to set itself as the main light source\n\n  React.useImperativeHandle(forwardRef, () => api, [api]);\n  useFrame(() => {\n    if ((api.temporal || api.frames === Infinity) && api.count < api.frames && api.count < limit) {\n      invalidate();\n      api.update();\n      api.count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"group\", {\n    traverse: () => null,\n    ref: gLights\n  }, /*#__PURE__*/React.createElement(accumulativeContext.Provider, {\n    value: api\n  }, children)), /*#__PURE__*/React.createElement(\"mesh\", {\n    receiveShadow: true,\n    ref: gPlane,\n    scale: scale,\n    rotation: [-Math.PI / 2, 0, 0]\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"softShadowMaterial\", {\n    transparent: true,\n    depthWrite: false,\n    toneMapped: toneMapped,\n    color: color,\n    blend: colorBlend,\n    map: plm.progressiveLightMap2.texture\n  })));\n});\nconst RandomizedLight = /*#__PURE__*/React.forwardRef(({\n  castShadow = true,\n  bias = 0.001,\n  mapSize = 512,\n  size = 5,\n  near = 0.5,\n  far = 500,\n  frames = 1,\n  position = [0, 0, 0],\n  radius = 1,\n  amount = 8,\n  intensity = 1,\n  ambient = 0.5,\n  ...props\n}, forwardRef) => {\n  const gLights = React.useRef(null);\n  const length = new THREE.Vector3(...position).length();\n  const parent = React.useContext(accumulativeContext);\n  const update = React.useCallback(() => {\n    let light;\n    if (gLights.current) {\n      for (let l = 0; l < gLights.current.children.length; l++) {\n        light = gLights.current.children[l];\n        if (Math.random() > ambient) {\n          light.position.set(position[0] + THREE.MathUtils.randFloatSpread(radius), position[1] + THREE.MathUtils.randFloatSpread(radius), position[2] + THREE.MathUtils.randFloatSpread(radius));\n        } else {\n          let lambda = Math.acos(2 * Math.random() - 1) - Math.PI / 2.0;\n          let phi = 2 * Math.PI * Math.random();\n          light.position.set(Math.cos(lambda) * Math.cos(phi) * length, Math.abs(Math.cos(lambda) * Math.sin(phi) * length), Math.sin(lambda) * length);\n        }\n      }\n    }\n  }, [radius, ambient, length, ...position]);\n  const api = React.useMemo(() => ({\n    update\n  }), [update]);\n  React.useImperativeHandle(forwardRef, () => api, [api]);\n  React.useLayoutEffect(() => {\n    const group = gLights.current;\n    if (parent) parent.lights.set(group.uuid, api);\n    return () => void parent.lights.delete(group.uuid);\n  }, [parent, api]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: gLights\n  }, props), Array.from({\n    length: amount\n  }, (_, index) => /*#__PURE__*/React.createElement(\"directionalLight\", {\n    key: index,\n    castShadow: castShadow,\n    \"shadow-bias\": bias,\n    \"shadow-mapSize\": [mapSize, mapSize],\n    intensity: intensity / amount\n  }, /*#__PURE__*/React.createElement(\"orthographicCamera\", {\n    attach: \"shadow-camera\",\n    args: [-size, size, size, -size, near, far]\n  }))));\n}); // Based on \"Progressive Light Map Accumulator\", by [zalo](https://github.com/zalo/)\n\nclass ProgressiveLightMap {\n  constructor(renderer, scene, res = 1024) {\n    this.renderer = renderer;\n    this.res = res;\n    this.scene = scene;\n    this.buffer1Active = false;\n    this.lights = [];\n    this.meshes = [];\n    this.object = null;\n    this.clearColor = new THREE.Color();\n    this.clearAlpha = 0; // Create the Progressive LightMap Texture\n\n    const format = /(Android|iPad|iPhone|iPod)/g.test(navigator.userAgent) ? THREE.HalfFloatType : THREE.FloatType;\n    this.progressiveLightMap1 = new THREE.WebGLRenderTarget(this.res, this.res, {\n      type: format\n    });\n    this.progressiveLightMap2 = new THREE.WebGLRenderTarget(this.res, this.res, {\n      type: format\n    }); // Inject some spicy new logic into a standard phong material\n\n    this.discardMat = new DiscardMaterial();\n    this.targetMat = new THREE.MeshLambertMaterial({\n      fog: false\n    });\n    this.previousShadowMap = {\n      value: this.progressiveLightMap1.texture\n    };\n    this.averagingWindow = {\n      value: 100\n    };\n    this.targetMat.onBeforeCompile = shader => {\n      // Vertex Shader: Set Vertex Positions to the Unwrapped UV Positions\n      shader.vertexShader = 'varying vec2 vUv;\\n' + shader.vertexShader.slice(0, -1) + 'vUv = uv; gl_Position = vec4((uv - 0.5) * 2.0, 1.0, 1.0); }'; // Fragment Shader: Set Pixels to average in the Previous frame's Shadows\n\n      const bodyStart = shader.fragmentShader.indexOf('void main() {');\n      shader.fragmentShader = 'varying vec2 vUv;\\n' + shader.fragmentShader.slice(0, bodyStart) + 'uniform sampler2D previousShadowMap;\\n\tuniform float averagingWindow;\\n' + shader.fragmentShader.slice(bodyStart - 1, -1) + `\\nvec3 texelOld = texture2D(previousShadowMap, vUv).rgb;\n        gl_FragColor.rgb = mix(texelOld, gl_FragColor.rgb, 1.0/ averagingWindow);\n      }`; // Set the Previous Frame's Texture Buffer and Averaging Window\n\n      shader.uniforms.previousShadowMap = this.previousShadowMap;\n      shader.uniforms.averagingWindow = this.averagingWindow;\n    };\n  }\n  clear() {\n    this.renderer.getClearColor(this.clearColor);\n    this.clearAlpha = this.renderer.getClearAlpha();\n    this.renderer.setClearColor('black', 1);\n    this.renderer.setRenderTarget(this.progressiveLightMap1);\n    this.renderer.clear();\n    this.renderer.setRenderTarget(this.progressiveLightMap2);\n    this.renderer.clear();\n    this.renderer.setRenderTarget(null);\n    this.renderer.setClearColor(this.clearColor, this.clearAlpha);\n    this.lights = [];\n    this.meshes = [];\n    this.scene.traverse(object => {\n      if (isGeometry(object)) {\n        this.meshes.push({\n          object,\n          material: object.material\n        });\n      } else if (isLight(object)) {\n        this.lights.push({\n          object,\n          intensity: object.intensity\n        });\n      }\n    });\n  }\n  prepare() {\n    this.lights.forEach(light => light.object.intensity = 0);\n    this.meshes.forEach(mesh => mesh.object.material = this.discardMat);\n  }\n  finish() {\n    this.lights.forEach(light => light.object.intensity = light.intensity);\n    this.meshes.forEach(mesh => mesh.object.material = mesh.material);\n  }\n  configure(object) {\n    this.object = object;\n  }\n  update(camera, blendWindow = 100) {\n    if (!this.object) return; // Set each object's material to the UV Unwrapped Surface Mapping Version\n\n    this.averagingWindow.value = blendWindow;\n    this.object.material = this.targetMat; // Ping-pong two surface buffers for reading/writing\n\n    const activeMap = this.buffer1Active ? this.progressiveLightMap1 : this.progressiveLightMap2;\n    const inactiveMap = this.buffer1Active ? this.progressiveLightMap2 : this.progressiveLightMap1; // Render the object's surface maps\n\n    const oldBg = this.scene.background;\n    this.scene.background = null;\n    this.renderer.setRenderTarget(activeMap);\n    this.previousShadowMap.value = inactiveMap.texture;\n    this.buffer1Active = !this.buffer1Active;\n    this.renderer.render(this.scene, camera);\n    this.renderer.setRenderTarget(null);\n    this.scene.background = oldBg;\n  }\n}\nexport { AccumulativeShadows, RandomizedLight, accumulativeContext };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "extend", "useThree", "useFrame", "shaderMaterial", "DiscardMaterial", "isLight", "object", "isGeometry", "geometry", "accumulativeContext", "createContext", "SoftShadowMaterial", "color", "Color", "blend", "alphaTest", "opacity", "map", "AccumulativeShadows", "forwardRef", "children", "temporal", "frames", "limit", "Infinity", "scale", "colorBlend", "resolution", "toneMapped", "props", "gl", "state", "scene", "camera", "invalidate", "gPlane", "useRef", "gLights", "plm", "useState", "ProgressiveLightMap", "useLayoutEffect", "configure", "current", "api", "useMemo", "lights", "Map", "Math", "max", "count", "<PERSON><PERSON><PERSON>", "reset", "clear", "material", "update", "min", "visible", "prepare", "i", "for<PERSON>ach", "light", "finish", "useImperativeHandle", "createElement", "traverse", "ref", "Provider", "value", "receiveShadow", "rotation", "PI", "transparent", "depthWrite", "progressiveLightMap2", "texture", "RandomizedLight", "<PERSON><PERSON><PERSON><PERSON>", "bias", "mapSize", "size", "near", "far", "position", "radius", "amount", "intensity", "ambient", "length", "Vector3", "parent", "useContext", "useCallback", "l", "random", "set", "MathUtils", "randFloatSpread", "lambda", "acos", "phi", "cos", "abs", "sin", "group", "uuid", "delete", "Array", "from", "_", "index", "key", "attach", "args", "constructor", "renderer", "res", "buffer1Active", "meshes", "clearColor", "clearAlpha", "format", "test", "navigator", "userAgent", "HalfFloatType", "FloatType", "progressiveLightMap1", "WebGLRenderTarget", "type", "discardMat", "targetMat", "MeshLambertMaterial", "fog", "previousShadowMap", "averagingWindow", "onBeforeCompile", "shader", "vertexShader", "slice", "bodyStart", "fragmentShader", "indexOf", "uniforms", "getClearColor", "getClearAlpha", "setClearColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "push", "mesh", "blendWindow", "activeMap", "inactiveMap", "oldBg", "background", "render"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/AccumulativeShadows.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { DiscardMaterial } from '../materials/DiscardMaterial.js';\n\nfunction isLight(object) {\n  return object.isLight;\n}\n\nfunction isGeometry(object) {\n  return !!object.geometry;\n}\n\nconst accumulativeContext = /*#__PURE__*/React.createContext(null);\nconst SoftShadowMaterial = shaderMaterial({\n  color: new THREE.Color(),\n  blend: 2.0,\n  alphaTest: 0.75,\n  opacity: 0,\n  map: null\n}, `varying vec2 vUv;\n   void main() {\n     gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n     vUv = uv;\n   }`, `varying vec2 vUv;\n   uniform sampler2D map;\n   uniform vec3 color;\n   uniform float opacity;\n   uniform float alphaTest;\n   uniform float blend;\n   void main() {\n     vec4 sampledDiffuseColor = texture2D(map, vUv);\n     gl_FragColor = vec4(color * sampledDiffuseColor.r * blend, max(0.0, (1.0 - (sampledDiffuseColor.r + sampledDiffuseColor.g + sampledDiffuseColor.b) / alphaTest)) * opacity);\n     #include <tonemapping_fragment>\n     #include <encodings_fragment>\n   }`);\nconst AccumulativeShadows = /*#__PURE__*/React.forwardRef(({\n  children,\n  temporal,\n  frames = 40,\n  limit = Infinity,\n  blend = 20,\n  scale = 10,\n  opacity = 1,\n  alphaTest = 0.75,\n  color = 'black',\n  colorBlend = 2,\n  resolution = 1024,\n  toneMapped = true,\n  ...props\n}, forwardRef) => {\n  extend({\n    SoftShadowMaterial\n  });\n  const gl = useThree(state => state.gl);\n  const scene = useThree(state => state.scene);\n  const camera = useThree(state => state.camera);\n  const invalidate = useThree(state => state.invalidate);\n  const gPlane = React.useRef(null);\n  const gLights = React.useRef(null);\n  const [plm] = React.useState(() => new ProgressiveLightMap(gl, scene, resolution));\n  React.useLayoutEffect(() => {\n    plm.configure(gPlane.current);\n  }, []);\n  const api = React.useMemo(() => ({\n    lights: new Map(),\n    temporal: !!temporal,\n    frames: Math.max(2, frames),\n    blend: Math.max(2, frames === Infinity ? blend : frames),\n    count: 0,\n    getMesh: () => gPlane.current,\n    reset: () => {\n      // Clear buffers, reset opacities, set frame count to 0\n      plm.clear();\n      const material = gPlane.current.material;\n      material.opacity = 0;\n      material.alphaTest = 0;\n      api.count = 0;\n    },\n    update: (frames = 1) => {\n      // Adapt the opacity-blend ratio to the number of frames\n      const material = gPlane.current.material;\n\n      if (!api.temporal) {\n        material.opacity = opacity;\n        material.alphaTest = alphaTest;\n      } else {\n        material.opacity = Math.min(opacity, material.opacity + opacity / api.blend);\n        material.alphaTest = Math.min(alphaTest, material.alphaTest + alphaTest / api.blend);\n      } // Switch accumulative lights on\n\n\n      gLights.current.visible = true; // Collect scene lights and meshes\n\n      plm.prepare(); // Update the lightmap and the accumulative lights\n\n      for (let i = 0; i < frames; i++) {\n        api.lights.forEach(light => light.update());\n        plm.update(camera, api.blend);\n      } // Switch lights off\n\n\n      gLights.current.visible = false; // Restore lights and meshes\n\n      plm.finish();\n    }\n  }), [plm, camera, scene, temporal, frames, blend, opacity, alphaTest]);\n  React.useLayoutEffect(() => {\n    // Reset internals, buffers, ...\n    api.reset(); // Update lightmap\n\n    if (!api.temporal && api.frames !== Infinity) api.update(api.blend);\n  }); // Expose api, allow children to set itself as the main light source\n\n  React.useImperativeHandle(forwardRef, () => api, [api]);\n  useFrame(() => {\n    if ((api.temporal || api.frames === Infinity) && api.count < api.frames && api.count < limit) {\n      invalidate();\n      api.update();\n      api.count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"group\", {\n    traverse: () => null,\n    ref: gLights\n  }, /*#__PURE__*/React.createElement(accumulativeContext.Provider, {\n    value: api\n  }, children)), /*#__PURE__*/React.createElement(\"mesh\", {\n    receiveShadow: true,\n    ref: gPlane,\n    scale: scale,\n    rotation: [-Math.PI / 2, 0, 0]\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"softShadowMaterial\", {\n    transparent: true,\n    depthWrite: false,\n    toneMapped: toneMapped,\n    color: color,\n    blend: colorBlend,\n    map: plm.progressiveLightMap2.texture\n  })));\n});\nconst RandomizedLight = /*#__PURE__*/React.forwardRef(({\n  castShadow = true,\n  bias = 0.001,\n  mapSize = 512,\n  size = 5,\n  near = 0.5,\n  far = 500,\n  frames = 1,\n  position = [0, 0, 0],\n  radius = 1,\n  amount = 8,\n  intensity = 1,\n  ambient = 0.5,\n  ...props\n}, forwardRef) => {\n  const gLights = React.useRef(null);\n  const length = new THREE.Vector3(...position).length();\n  const parent = React.useContext(accumulativeContext);\n  const update = React.useCallback(() => {\n    let light;\n\n    if (gLights.current) {\n      for (let l = 0; l < gLights.current.children.length; l++) {\n        light = gLights.current.children[l];\n\n        if (Math.random() > ambient) {\n          light.position.set(position[0] + THREE.MathUtils.randFloatSpread(radius), position[1] + THREE.MathUtils.randFloatSpread(radius), position[2] + THREE.MathUtils.randFloatSpread(radius));\n        } else {\n          let lambda = Math.acos(2 * Math.random() - 1) - Math.PI / 2.0;\n          let phi = 2 * Math.PI * Math.random();\n          light.position.set(Math.cos(lambda) * Math.cos(phi) * length, Math.abs(Math.cos(lambda) * Math.sin(phi) * length), Math.sin(lambda) * length);\n        }\n      }\n    }\n  }, [radius, ambient, length, ...position]);\n  const api = React.useMemo(() => ({\n    update\n  }), [update]);\n  React.useImperativeHandle(forwardRef, () => api, [api]);\n  React.useLayoutEffect(() => {\n    const group = gLights.current;\n    if (parent) parent.lights.set(group.uuid, api);\n    return () => void parent.lights.delete(group.uuid);\n  }, [parent, api]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: gLights\n  }, props), Array.from({\n    length: amount\n  }, (_, index) => /*#__PURE__*/React.createElement(\"directionalLight\", {\n    key: index,\n    castShadow: castShadow,\n    \"shadow-bias\": bias,\n    \"shadow-mapSize\": [mapSize, mapSize],\n    intensity: intensity / amount\n  }, /*#__PURE__*/React.createElement(\"orthographicCamera\", {\n    attach: \"shadow-camera\",\n    args: [-size, size, size, -size, near, far]\n  }))));\n}); // Based on \"Progressive Light Map Accumulator\", by [zalo](https://github.com/zalo/)\n\nclass ProgressiveLightMap {\n  constructor(renderer, scene, res = 1024) {\n    this.renderer = renderer;\n    this.res = res;\n    this.scene = scene;\n    this.buffer1Active = false;\n    this.lights = [];\n    this.meshes = [];\n    this.object = null;\n    this.clearColor = new THREE.Color();\n    this.clearAlpha = 0; // Create the Progressive LightMap Texture\n\n    const format = /(Android|iPad|iPhone|iPod)/g.test(navigator.userAgent) ? THREE.HalfFloatType : THREE.FloatType;\n    this.progressiveLightMap1 = new THREE.WebGLRenderTarget(this.res, this.res, {\n      type: format\n    });\n    this.progressiveLightMap2 = new THREE.WebGLRenderTarget(this.res, this.res, {\n      type: format\n    }); // Inject some spicy new logic into a standard phong material\n\n    this.discardMat = new DiscardMaterial();\n    this.targetMat = new THREE.MeshLambertMaterial({\n      fog: false\n    });\n    this.previousShadowMap = {\n      value: this.progressiveLightMap1.texture\n    };\n    this.averagingWindow = {\n      value: 100\n    };\n\n    this.targetMat.onBeforeCompile = shader => {\n      // Vertex Shader: Set Vertex Positions to the Unwrapped UV Positions\n      shader.vertexShader = 'varying vec2 vUv;\\n' + shader.vertexShader.slice(0, -1) + 'vUv = uv; gl_Position = vec4((uv - 0.5) * 2.0, 1.0, 1.0); }'; // Fragment Shader: Set Pixels to average in the Previous frame's Shadows\n\n      const bodyStart = shader.fragmentShader.indexOf('void main() {');\n      shader.fragmentShader = 'varying vec2 vUv;\\n' + shader.fragmentShader.slice(0, bodyStart) + 'uniform sampler2D previousShadowMap;\\n\tuniform float averagingWindow;\\n' + shader.fragmentShader.slice(bodyStart - 1, -1) + `\\nvec3 texelOld = texture2D(previousShadowMap, vUv).rgb;\n        gl_FragColor.rgb = mix(texelOld, gl_FragColor.rgb, 1.0/ averagingWindow);\n      }`; // Set the Previous Frame's Texture Buffer and Averaging Window\n\n      shader.uniforms.previousShadowMap = this.previousShadowMap;\n      shader.uniforms.averagingWindow = this.averagingWindow;\n    };\n  }\n\n  clear() {\n    this.renderer.getClearColor(this.clearColor);\n    this.clearAlpha = this.renderer.getClearAlpha();\n    this.renderer.setClearColor('black', 1);\n    this.renderer.setRenderTarget(this.progressiveLightMap1);\n    this.renderer.clear();\n    this.renderer.setRenderTarget(this.progressiveLightMap2);\n    this.renderer.clear();\n    this.renderer.setRenderTarget(null);\n    this.renderer.setClearColor(this.clearColor, this.clearAlpha);\n    this.lights = [];\n    this.meshes = [];\n    this.scene.traverse(object => {\n      if (isGeometry(object)) {\n        this.meshes.push({\n          object,\n          material: object.material\n        });\n      } else if (isLight(object)) {\n        this.lights.push({\n          object,\n          intensity: object.intensity\n        });\n      }\n    });\n  }\n\n  prepare() {\n    this.lights.forEach(light => light.object.intensity = 0);\n    this.meshes.forEach(mesh => mesh.object.material = this.discardMat);\n  }\n\n  finish() {\n    this.lights.forEach(light => light.object.intensity = light.intensity);\n    this.meshes.forEach(mesh => mesh.object.material = mesh.material);\n  }\n\n  configure(object) {\n    this.object = object;\n  }\n\n  update(camera, blendWindow = 100) {\n    if (!this.object) return; // Set each object's material to the UV Unwrapped Surface Mapping Version\n\n    this.averagingWindow.value = blendWindow;\n    this.object.material = this.targetMat; // Ping-pong two surface buffers for reading/writing\n\n    const activeMap = this.buffer1Active ? this.progressiveLightMap1 : this.progressiveLightMap2;\n    const inactiveMap = this.buffer1Active ? this.progressiveLightMap2 : this.progressiveLightMap1; // Render the object's surface maps\n\n    const oldBg = this.scene.background;\n    this.scene.background = null;\n    this.renderer.setRenderTarget(activeMap);\n    this.previousShadowMap.value = inactiveMap.texture;\n    this.buffer1Active = !this.buffer1Active;\n    this.renderer.render(this.scene, camera);\n    this.renderer.setRenderTarget(null);\n    this.scene.background = oldBg;\n  }\n\n}\n\nexport { AccumulativeShadows, RandomizedLight, accumulativeContext };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAC/D,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,eAAe,QAAQ,iCAAiC;AAEjE,SAASC,OAAOA,CAACC,MAAM,EAAE;EACvB,OAAOA,MAAM,CAACD,OAAO;AACvB;AAEA,SAASE,UAAUA,CAACD,MAAM,EAAE;EAC1B,OAAO,CAAC,CAACA,MAAM,CAACE,QAAQ;AAC1B;AAEA,MAAMC,mBAAmB,GAAG,aAAaV,KAAK,CAACW,aAAa,CAAC,IAAI,CAAC;AAClE,MAAMC,kBAAkB,GAAGR,cAAc,CAAC;EACxCS,KAAK,EAAE,IAAId,KAAK,CAACe,KAAK,CAAC,CAAC;EACxBC,KAAK,EAAE,GAAG;EACVC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,CAAC;EACVC,GAAG,EAAE;AACP,CAAC,EAAE;AACH;AACA;AACA;AACA,KAAK,EAAE;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,CAAC;AACN,MAAMC,mBAAmB,GAAG,aAAanB,KAAK,CAACoB,UAAU,CAAC,CAAC;EACzDC,QAAQ;EACRC,QAAQ;EACRC,MAAM,GAAG,EAAE;EACXC,KAAK,GAAGC,QAAQ;EAChBV,KAAK,GAAG,EAAE;EACVW,KAAK,GAAG,EAAE;EACVT,OAAO,GAAG,CAAC;EACXD,SAAS,GAAG,IAAI;EAChBH,KAAK,GAAG,OAAO;EACfc,UAAU,GAAG,CAAC;EACdC,UAAU,GAAG,IAAI;EACjBC,UAAU,GAAG,IAAI;EACjB,GAAGC;AACL,CAAC,EAAEV,UAAU,KAAK;EAChBnB,MAAM,CAAC;IACLW;EACF,CAAC,CAAC;EACF,MAAMmB,EAAE,GAAG7B,QAAQ,CAAC8B,KAAK,IAAIA,KAAK,CAACD,EAAE,CAAC;EACtC,MAAME,KAAK,GAAG/B,QAAQ,CAAC8B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC;EAC5C,MAAMC,MAAM,GAAGhC,QAAQ,CAAC8B,KAAK,IAAIA,KAAK,CAACE,MAAM,CAAC;EAC9C,MAAMC,UAAU,GAAGjC,QAAQ,CAAC8B,KAAK,IAAIA,KAAK,CAACG,UAAU,CAAC;EACtD,MAAMC,MAAM,GAAGpC,KAAK,CAACqC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMC,OAAO,GAAGtC,KAAK,CAACqC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAACE,GAAG,CAAC,GAAGvC,KAAK,CAACwC,QAAQ,CAAC,MAAM,IAAIC,mBAAmB,CAACV,EAAE,EAAEE,KAAK,EAAEL,UAAU,CAAC,CAAC;EAClF5B,KAAK,CAAC0C,eAAe,CAAC,MAAM;IAC1BH,GAAG,CAACI,SAAS,CAACP,MAAM,CAACQ,OAAO,CAAC;EAC/B,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,GAAG,GAAG7C,KAAK,CAAC8C,OAAO,CAAC,OAAO;IAC/BC,MAAM,EAAE,IAAIC,GAAG,CAAC,CAAC;IACjB1B,QAAQ,EAAE,CAAC,CAACA,QAAQ;IACpBC,MAAM,EAAE0B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE3B,MAAM,CAAC;IAC3BR,KAAK,EAAEkC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE3B,MAAM,KAAKE,QAAQ,GAAGV,KAAK,GAAGQ,MAAM,CAAC;IACxD4B,KAAK,EAAE,CAAC;IACRC,OAAO,EAAEA,CAAA,KAAMhB,MAAM,CAACQ,OAAO;IAC7BS,KAAK,EAAEA,CAAA,KAAM;MACX;MACAd,GAAG,CAACe,KAAK,CAAC,CAAC;MACX,MAAMC,QAAQ,GAAGnB,MAAM,CAACQ,OAAO,CAACW,QAAQ;MACxCA,QAAQ,CAACtC,OAAO,GAAG,CAAC;MACpBsC,QAAQ,CAACvC,SAAS,GAAG,CAAC;MACtB6B,GAAG,CAACM,KAAK,GAAG,CAAC;IACf,CAAC;IACDK,MAAM,EAAEA,CAACjC,MAAM,GAAG,CAAC,KAAK;MACtB;MACA,MAAMgC,QAAQ,GAAGnB,MAAM,CAACQ,OAAO,CAACW,QAAQ;MAExC,IAAI,CAACV,GAAG,CAACvB,QAAQ,EAAE;QACjBiC,QAAQ,CAACtC,OAAO,GAAGA,OAAO;QAC1BsC,QAAQ,CAACvC,SAAS,GAAGA,SAAS;MAChC,CAAC,MAAM;QACLuC,QAAQ,CAACtC,OAAO,GAAGgC,IAAI,CAACQ,GAAG,CAACxC,OAAO,EAAEsC,QAAQ,CAACtC,OAAO,GAAGA,OAAO,GAAG4B,GAAG,CAAC9B,KAAK,CAAC;QAC5EwC,QAAQ,CAACvC,SAAS,GAAGiC,IAAI,CAACQ,GAAG,CAACzC,SAAS,EAAEuC,QAAQ,CAACvC,SAAS,GAAGA,SAAS,GAAG6B,GAAG,CAAC9B,KAAK,CAAC;MACtF,CAAC,CAAC;;MAGFuB,OAAO,CAACM,OAAO,CAACc,OAAO,GAAG,IAAI,CAAC,CAAC;;MAEhCnB,GAAG,CAACoB,OAAO,CAAC,CAAC,CAAC,CAAC;;MAEf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrC,MAAM,EAAEqC,CAAC,EAAE,EAAE;QAC/Bf,GAAG,CAACE,MAAM,CAACc,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACN,MAAM,CAAC,CAAC,CAAC;QAC3CjB,GAAG,CAACiB,MAAM,CAACtB,MAAM,EAAEW,GAAG,CAAC9B,KAAK,CAAC;MAC/B,CAAC,CAAC;;MAGFuB,OAAO,CAACM,OAAO,CAACc,OAAO,GAAG,KAAK,CAAC,CAAC;;MAEjCnB,GAAG,CAACwB,MAAM,CAAC,CAAC;IACd;EACF,CAAC,CAAC,EAAE,CAACxB,GAAG,EAAEL,MAAM,EAAED,KAAK,EAAEX,QAAQ,EAAEC,MAAM,EAAER,KAAK,EAAEE,OAAO,EAAED,SAAS,CAAC,CAAC;EACtEhB,KAAK,CAAC0C,eAAe,CAAC,MAAM;IAC1B;IACAG,GAAG,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC;;IAEb,IAAI,CAACR,GAAG,CAACvB,QAAQ,IAAIuB,GAAG,CAACtB,MAAM,KAAKE,QAAQ,EAAEoB,GAAG,CAACW,MAAM,CAACX,GAAG,CAAC9B,KAAK,CAAC;EACrE,CAAC,CAAC,CAAC,CAAC;;EAEJf,KAAK,CAACgE,mBAAmB,CAAC5C,UAAU,EAAE,MAAMyB,GAAG,EAAE,CAACA,GAAG,CAAC,CAAC;EACvD1C,QAAQ,CAAC,MAAM;IACb,IAAI,CAAC0C,GAAG,CAACvB,QAAQ,IAAIuB,GAAG,CAACtB,MAAM,KAAKE,QAAQ,KAAKoB,GAAG,CAACM,KAAK,GAAGN,GAAG,CAACtB,MAAM,IAAIsB,GAAG,CAACM,KAAK,GAAG3B,KAAK,EAAE;MAC5FW,UAAU,CAAC,CAAC;MACZU,GAAG,CAACW,MAAM,CAAC,CAAC;MACZX,GAAG,CAACM,KAAK,EAAE;IACb;EACF,CAAC,CAAC;EACF,OAAO,aAAanD,KAAK,CAACiE,aAAa,CAAC,OAAO,EAAEnC,KAAK,EAAE,aAAa9B,KAAK,CAACiE,aAAa,CAAC,OAAO,EAAE;IAChGC,QAAQ,EAAEA,CAAA,KAAM,IAAI;IACpBC,GAAG,EAAE7B;EACP,CAAC,EAAE,aAAatC,KAAK,CAACiE,aAAa,CAACvD,mBAAmB,CAAC0D,QAAQ,EAAE;IAChEC,KAAK,EAAExB;EACT,CAAC,EAAExB,QAAQ,CAAC,CAAC,EAAE,aAAarB,KAAK,CAACiE,aAAa,CAAC,MAAM,EAAE;IACtDK,aAAa,EAAE,IAAI;IACnBH,GAAG,EAAE/B,MAAM;IACXV,KAAK,EAAEA,KAAK;IACZ6C,QAAQ,EAAE,CAAC,CAACtB,IAAI,CAACuB,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;EAC/B,CAAC,EAAE,aAAaxE,KAAK,CAACiE,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,aAAajE,KAAK,CAACiE,aAAa,CAAC,oBAAoB,EAAE;IACjHQ,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,KAAK;IACjB7C,UAAU,EAAEA,UAAU;IACtBhB,KAAK,EAAEA,KAAK;IACZE,KAAK,EAAEY,UAAU;IACjBT,GAAG,EAAEqB,GAAG,CAACoC,oBAAoB,CAACC;EAChC,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACF,MAAMC,eAAe,GAAG,aAAa7E,KAAK,CAACoB,UAAU,CAAC,CAAC;EACrD0D,UAAU,GAAG,IAAI;EACjBC,IAAI,GAAG,KAAK;EACZC,OAAO,GAAG,GAAG;EACbC,IAAI,GAAG,CAAC;EACRC,IAAI,GAAG,GAAG;EACVC,GAAG,GAAG,GAAG;EACT5D,MAAM,GAAG,CAAC;EACV6D,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACpBC,MAAM,GAAG,CAAC;EACVC,MAAM,GAAG,CAAC;EACVC,SAAS,GAAG,CAAC;EACbC,OAAO,GAAG,GAAG;EACb,GAAG1D;AACL,CAAC,EAAEV,UAAU,KAAK;EAChB,MAAMkB,OAAO,GAAGtC,KAAK,CAACqC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMoD,MAAM,GAAG,IAAI1F,KAAK,CAAC2F,OAAO,CAAC,GAAGN,QAAQ,CAAC,CAACK,MAAM,CAAC,CAAC;EACtD,MAAME,MAAM,GAAG3F,KAAK,CAAC4F,UAAU,CAAClF,mBAAmB,CAAC;EACpD,MAAM8C,MAAM,GAAGxD,KAAK,CAAC6F,WAAW,CAAC,MAAM;IACrC,IAAI/B,KAAK;IAET,IAAIxB,OAAO,CAACM,OAAO,EAAE;MACnB,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxD,OAAO,CAACM,OAAO,CAACvB,QAAQ,CAACoE,MAAM,EAAEK,CAAC,EAAE,EAAE;QACxDhC,KAAK,GAAGxB,OAAO,CAACM,OAAO,CAACvB,QAAQ,CAACyE,CAAC,CAAC;QAEnC,IAAI7C,IAAI,CAAC8C,MAAM,CAAC,CAAC,GAAGP,OAAO,EAAE;UAC3B1B,KAAK,CAACsB,QAAQ,CAACY,GAAG,CAACZ,QAAQ,CAAC,CAAC,CAAC,GAAGrF,KAAK,CAACkG,SAAS,CAACC,eAAe,CAACb,MAAM,CAAC,EAAED,QAAQ,CAAC,CAAC,CAAC,GAAGrF,KAAK,CAACkG,SAAS,CAACC,eAAe,CAACb,MAAM,CAAC,EAAED,QAAQ,CAAC,CAAC,CAAC,GAAGrF,KAAK,CAACkG,SAAS,CAACC,eAAe,CAACb,MAAM,CAAC,CAAC;QACzL,CAAC,MAAM;UACL,IAAIc,MAAM,GAAGlD,IAAI,CAACmD,IAAI,CAAC,CAAC,GAAGnD,IAAI,CAAC8C,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG9C,IAAI,CAACuB,EAAE,GAAG,GAAG;UAC7D,IAAI6B,GAAG,GAAG,CAAC,GAAGpD,IAAI,CAACuB,EAAE,GAAGvB,IAAI,CAAC8C,MAAM,CAAC,CAAC;UACrCjC,KAAK,CAACsB,QAAQ,CAACY,GAAG,CAAC/C,IAAI,CAACqD,GAAG,CAACH,MAAM,CAAC,GAAGlD,IAAI,CAACqD,GAAG,CAACD,GAAG,CAAC,GAAGZ,MAAM,EAAExC,IAAI,CAACsD,GAAG,CAACtD,IAAI,CAACqD,GAAG,CAACH,MAAM,CAAC,GAAGlD,IAAI,CAACuD,GAAG,CAACH,GAAG,CAAC,GAAGZ,MAAM,CAAC,EAAExC,IAAI,CAACuD,GAAG,CAACL,MAAM,CAAC,GAAGV,MAAM,CAAC;QAC/I;MACF;IACF;EACF,CAAC,EAAE,CAACJ,MAAM,EAAEG,OAAO,EAAEC,MAAM,EAAE,GAAGL,QAAQ,CAAC,CAAC;EAC1C,MAAMvC,GAAG,GAAG7C,KAAK,CAAC8C,OAAO,CAAC,OAAO;IAC/BU;EACF,CAAC,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACbxD,KAAK,CAACgE,mBAAmB,CAAC5C,UAAU,EAAE,MAAMyB,GAAG,EAAE,CAACA,GAAG,CAAC,CAAC;EACvD7C,KAAK,CAAC0C,eAAe,CAAC,MAAM;IAC1B,MAAM+D,KAAK,GAAGnE,OAAO,CAACM,OAAO;IAC7B,IAAI+C,MAAM,EAAEA,MAAM,CAAC5C,MAAM,CAACiD,GAAG,CAACS,KAAK,CAACC,IAAI,EAAE7D,GAAG,CAAC;IAC9C,OAAO,MAAM,KAAK8C,MAAM,CAAC5C,MAAM,CAAC4D,MAAM,CAACF,KAAK,CAACC,IAAI,CAAC;EACpD,CAAC,EAAE,CAACf,MAAM,EAAE9C,GAAG,CAAC,CAAC;EACjB,OAAO,aAAa7C,KAAK,CAACiE,aAAa,CAAC,OAAO,EAAEnE,QAAQ,CAAC;IACxDqE,GAAG,EAAE7B;EACP,CAAC,EAAER,KAAK,CAAC,EAAE8E,KAAK,CAACC,IAAI,CAAC;IACpBpB,MAAM,EAAEH;EACV,CAAC,EAAE,CAACwB,CAAC,EAAEC,KAAK,KAAK,aAAa/G,KAAK,CAACiE,aAAa,CAAC,kBAAkB,EAAE;IACpE+C,GAAG,EAAED,KAAK;IACVjC,UAAU,EAAEA,UAAU;IACtB,aAAa,EAAEC,IAAI;IACnB,gBAAgB,EAAE,CAACC,OAAO,EAAEA,OAAO,CAAC;IACpCO,SAAS,EAAEA,SAAS,GAAGD;EACzB,CAAC,EAAE,aAAatF,KAAK,CAACiE,aAAa,CAAC,oBAAoB,EAAE;IACxDgD,MAAM,EAAE,eAAe;IACvBC,IAAI,EAAE,CAAC,CAACjC,IAAI,EAAEA,IAAI,EAAEA,IAAI,EAAE,CAACA,IAAI,EAAEC,IAAI,EAAEC,GAAG;EAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC,CAAC;;AAEJ,MAAM1C,mBAAmB,CAAC;EACxB0E,WAAWA,CAACC,QAAQ,EAAEnF,KAAK,EAAEoF,GAAG,GAAG,IAAI,EAAE;IACvC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACpF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACqF,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACvE,MAAM,GAAG,EAAE;IAChB,IAAI,CAACwE,MAAM,GAAG,EAAE;IAChB,IAAI,CAAChH,MAAM,GAAG,IAAI;IAClB,IAAI,CAACiH,UAAU,GAAG,IAAIzH,KAAK,CAACe,KAAK,CAAC,CAAC;IACnC,IAAI,CAAC2G,UAAU,GAAG,CAAC,CAAC,CAAC;;IAErB,MAAMC,MAAM,GAAG,6BAA6B,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,GAAG9H,KAAK,CAAC+H,aAAa,GAAG/H,KAAK,CAACgI,SAAS;IAC9G,IAAI,CAACC,oBAAoB,GAAG,IAAIjI,KAAK,CAACkI,iBAAiB,CAAC,IAAI,CAACZ,GAAG,EAAE,IAAI,CAACA,GAAG,EAAE;MAC1Ea,IAAI,EAAER;IACR,CAAC,CAAC;IACF,IAAI,CAAC/C,oBAAoB,GAAG,IAAI5E,KAAK,CAACkI,iBAAiB,CAAC,IAAI,CAACZ,GAAG,EAAE,IAAI,CAACA,GAAG,EAAE;MAC1Ea,IAAI,EAAER;IACR,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAI,CAACS,UAAU,GAAG,IAAI9H,eAAe,CAAC,CAAC;IACvC,IAAI,CAAC+H,SAAS,GAAG,IAAIrI,KAAK,CAACsI,mBAAmB,CAAC;MAC7CC,GAAG,EAAE;IACP,CAAC,CAAC;IACF,IAAI,CAACC,iBAAiB,GAAG;MACvBlE,KAAK,EAAE,IAAI,CAAC2D,oBAAoB,CAACpD;IACnC,CAAC;IACD,IAAI,CAAC4D,eAAe,GAAG;MACrBnE,KAAK,EAAE;IACT,CAAC;IAED,IAAI,CAAC+D,SAAS,CAACK,eAAe,GAAGC,MAAM,IAAI;MACzC;MACAA,MAAM,CAACC,YAAY,GAAG,qBAAqB,GAAGD,MAAM,CAACC,YAAY,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,6DAA6D,CAAC,CAAC;;MAEhJ,MAAMC,SAAS,GAAGH,MAAM,CAACI,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC;MAChEL,MAAM,CAACI,cAAc,GAAG,qBAAqB,GAAGJ,MAAM,CAACI,cAAc,CAACF,KAAK,CAAC,CAAC,EAAEC,SAAS,CAAC,GAAG,yEAAyE,GAAGH,MAAM,CAACI,cAAc,CAACF,KAAK,CAACC,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG;AAC/N;AACA,QAAQ,CAAC,CAAC;;MAEJH,MAAM,CAACM,QAAQ,CAACT,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;MAC1DG,MAAM,CAACM,QAAQ,CAACR,eAAe,GAAG,IAAI,CAACA,eAAe;IACxD,CAAC;EACH;EAEAlF,KAAKA,CAAA,EAAG;IACN,IAAI,CAAC8D,QAAQ,CAAC6B,aAAa,CAAC,IAAI,CAACzB,UAAU,CAAC;IAC5C,IAAI,CAACC,UAAU,GAAG,IAAI,CAACL,QAAQ,CAAC8B,aAAa,CAAC,CAAC;IAC/C,IAAI,CAAC9B,QAAQ,CAAC+B,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC;IACvC,IAAI,CAAC/B,QAAQ,CAACgC,eAAe,CAAC,IAAI,CAACpB,oBAAoB,CAAC;IACxD,IAAI,CAACZ,QAAQ,CAAC9D,KAAK,CAAC,CAAC;IACrB,IAAI,CAAC8D,QAAQ,CAACgC,eAAe,CAAC,IAAI,CAACzE,oBAAoB,CAAC;IACxD,IAAI,CAACyC,QAAQ,CAAC9D,KAAK,CAAC,CAAC;IACrB,IAAI,CAAC8D,QAAQ,CAACgC,eAAe,CAAC,IAAI,CAAC;IACnC,IAAI,CAAChC,QAAQ,CAAC+B,aAAa,CAAC,IAAI,CAAC3B,UAAU,EAAE,IAAI,CAACC,UAAU,CAAC;IAC7D,IAAI,CAAC1E,MAAM,GAAG,EAAE;IAChB,IAAI,CAACwE,MAAM,GAAG,EAAE;IAChB,IAAI,CAACtF,KAAK,CAACiC,QAAQ,CAAC3D,MAAM,IAAI;MAC5B,IAAIC,UAAU,CAACD,MAAM,CAAC,EAAE;QACtB,IAAI,CAACgH,MAAM,CAAC8B,IAAI,CAAC;UACf9I,MAAM;UACNgD,QAAQ,EAAEhD,MAAM,CAACgD;QACnB,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIjD,OAAO,CAACC,MAAM,CAAC,EAAE;QAC1B,IAAI,CAACwC,MAAM,CAACsG,IAAI,CAAC;UACf9I,MAAM;UACNgF,SAAS,EAAEhF,MAAM,CAACgF;QACpB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEA5B,OAAOA,CAAA,EAAG;IACR,IAAI,CAACZ,MAAM,CAACc,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACvD,MAAM,CAACgF,SAAS,GAAG,CAAC,CAAC;IACxD,IAAI,CAACgC,MAAM,CAAC1D,OAAO,CAACyF,IAAI,IAAIA,IAAI,CAAC/I,MAAM,CAACgD,QAAQ,GAAG,IAAI,CAAC4E,UAAU,CAAC;EACrE;EAEApE,MAAMA,CAAA,EAAG;IACP,IAAI,CAAChB,MAAM,CAACc,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACvD,MAAM,CAACgF,SAAS,GAAGzB,KAAK,CAACyB,SAAS,CAAC;IACtE,IAAI,CAACgC,MAAM,CAAC1D,OAAO,CAACyF,IAAI,IAAIA,IAAI,CAAC/I,MAAM,CAACgD,QAAQ,GAAG+F,IAAI,CAAC/F,QAAQ,CAAC;EACnE;EAEAZ,SAASA,CAACpC,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACtB;EAEAiD,MAAMA,CAACtB,MAAM,EAAEqH,WAAW,GAAG,GAAG,EAAE;IAChC,IAAI,CAAC,IAAI,CAAChJ,MAAM,EAAE,OAAO,CAAC;;IAE1B,IAAI,CAACiI,eAAe,CAACnE,KAAK,GAAGkF,WAAW;IACxC,IAAI,CAAChJ,MAAM,CAACgD,QAAQ,GAAG,IAAI,CAAC6E,SAAS,CAAC,CAAC;;IAEvC,MAAMoB,SAAS,GAAG,IAAI,CAAClC,aAAa,GAAG,IAAI,CAACU,oBAAoB,GAAG,IAAI,CAACrD,oBAAoB;IAC5F,MAAM8E,WAAW,GAAG,IAAI,CAACnC,aAAa,GAAG,IAAI,CAAC3C,oBAAoB,GAAG,IAAI,CAACqD,oBAAoB,CAAC,CAAC;;IAEhG,MAAM0B,KAAK,GAAG,IAAI,CAACzH,KAAK,CAAC0H,UAAU;IACnC,IAAI,CAAC1H,KAAK,CAAC0H,UAAU,GAAG,IAAI;IAC5B,IAAI,CAACvC,QAAQ,CAACgC,eAAe,CAACI,SAAS,CAAC;IACxC,IAAI,CAACjB,iBAAiB,CAAClE,KAAK,GAAGoF,WAAW,CAAC7E,OAAO;IAClD,IAAI,CAAC0C,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC,IAAI,CAACF,QAAQ,CAACwC,MAAM,CAAC,IAAI,CAAC3H,KAAK,EAAEC,MAAM,CAAC;IACxC,IAAI,CAACkF,QAAQ,CAACgC,eAAe,CAAC,IAAI,CAAC;IACnC,IAAI,CAACnH,KAAK,CAAC0H,UAAU,GAAGD,KAAK;EAC/B;AAEF;AAEA,SAASvI,mBAAmB,EAAE0D,eAAe,EAAEnE,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}