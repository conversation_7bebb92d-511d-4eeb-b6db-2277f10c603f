{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Environment } from './Environment.js';\nimport { ContactShadows } from './ContactShadows.js';\nimport { Center } from './Center.js';\nimport { AccumulativeShadows, RandomizedLight } from './AccumulativeShadows.js';\nimport { Bounds, useBounds } from './Bounds.js';\nconst presets = {\n  rembrandt: {\n    main: [1, 2, 1],\n    fill: [-2, -0.5, -2]\n  },\n  portrait: {\n    main: [-1, 2, 0.5],\n    fill: [-1, 0.5, -1.5]\n  },\n  upfront: {\n    main: [0, 2, 1],\n    fill: [-1, 0.5, -1.5]\n  },\n  soft: {\n    main: [-2, 4, 4],\n    fill: [-1, 0.5, -1.5]\n  }\n};\nfunction Refit({\n  radius,\n  adjustCamera\n}) {\n  const api = useBounds();\n  React.useEffect(() => {\n    if (adjustCamera) api.refresh().clip().fit();\n  }, [radius, adjustCamera]);\n  return null;\n}\nfunction Stage({\n  children,\n  center,\n  adjustCamera = true,\n  intensity = 0.5,\n  shadows = 'contact',\n  environment = 'city',\n  preset = 'rembrandt',\n  ...props\n}) {\n  var _bias, _normalBias, _size, _offset, _amount, _radius, _ambient, _intensity;\n  const config = typeof preset === 'string' ? presets[preset] : preset;\n  const [{\n    radius,\n    height\n  }, set] = React.useState({\n    radius: 0,\n    width: 0,\n    height: 0,\n    depth: 0\n  });\n  const shadowBias = (_bias = shadows == null ? void 0 : shadows.bias) !== null && _bias !== void 0 ? _bias : -0.0001;\n  const normalBias = (_normalBias = shadows == null ? void 0 : shadows.normalBias) !== null && _normalBias !== void 0 ? _normalBias : 0;\n  const shadowSize = (_size = shadows == null ? void 0 : shadows.size) !== null && _size !== void 0 ? _size : 1024;\n  const shadowOffset = (_offset = shadows == null ? void 0 : shadows.offset) !== null && _offset !== void 0 ? _offset : 0;\n  const contactShadow = shadows === 'contact' || (shadows == null ? void 0 : shadows.type) === 'contact';\n  const accumulativeShadow = shadows === 'accumulative' || (shadows == null ? void 0 : shadows.type) === 'accumulative';\n  const shadowSpread = {\n    ...(typeof shadows === 'object' ? shadows : {})\n  };\n  const environmentProps = !environment ? null : typeof environment === 'string' ? {\n    preset: environment\n  } : environment;\n  const onCentered = React.useCallback(props => {\n    const {\n      width,\n      height,\n      depth,\n      boundingSphere\n    } = props;\n    set({\n      radius: boundingSphere.radius,\n      width,\n      height,\n      depth\n    });\n    if (center != null && center.onCentered) center.onCentered(props);\n  }, []);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"ambientLight\", {\n    intensity: intensity / 3\n  }), /*#__PURE__*/React.createElement(\"spotLight\", {\n    penumbra: 1,\n    position: [config.main[0] * radius, config.main[1] * radius, config.main[2] * radius],\n    intensity: intensity * 2,\n    castShadow: !!shadows,\n    \"shadow-bias\": shadowBias,\n    \"shadow-normalBias\": normalBias,\n    \"shadow-mapSize\": shadowSize\n  }), /*#__PURE__*/React.createElement(\"pointLight\", {\n    position: [config.fill[0] * radius, config.fill[1] * radius, config.fill[2] * radius],\n    intensity: intensity\n  }), /*#__PURE__*/React.createElement(Bounds, _extends({\n    fit: !!adjustCamera,\n    clip: !!adjustCamera,\n    margin: Number(adjustCamera),\n    observe: true\n  }, props), /*#__PURE__*/React.createElement(Refit, {\n    radius: radius,\n    adjustCamera: adjustCamera\n  }), /*#__PURE__*/React.createElement(Center, _extends({}, center, {\n    position: [0, shadowOffset / 2, 0],\n    onCentered: onCentered\n  }), children)), /*#__PURE__*/React.createElement(\"group\", {\n    position: [0, -height / 2 - shadowOffset / 2, 0]\n  }, contactShadow && /*#__PURE__*/React.createElement(ContactShadows, _extends({\n    scale: radius * 4,\n    far: radius,\n    blur: 2\n  }, shadowSpread)), accumulativeShadow && /*#__PURE__*/React.createElement(AccumulativeShadows, _extends({\n    temporal: true,\n    frames: 100,\n    alphaTest: 0.9,\n    toneMapped: true,\n    scale: radius * 4\n  }, shadowSpread), /*#__PURE__*/React.createElement(RandomizedLight, {\n    amount: (_amount = shadowSpread.amount) !== null && _amount !== void 0 ? _amount : 8,\n    radius: (_radius = shadowSpread.radius) !== null && _radius !== void 0 ? _radius : radius,\n    ambient: (_ambient = shadowSpread.ambient) !== null && _ambient !== void 0 ? _ambient : 0.5,\n    intensity: (_intensity = shadowSpread.intensity) !== null && _intensity !== void 0 ? _intensity : 1,\n    position: [config.main[0] * radius, config.main[1] * radius, config.main[2] * radius],\n    size: radius * 4,\n    bias: -shadowBias,\n    mapSize: shadowSize\n  }))), environment && /*#__PURE__*/React.createElement(Environment, environmentProps));\n}\nexport { Stage };", "map": {"version": 3, "names": ["_extends", "React", "Environment", "ContactShadows", "Center", "AccumulativeShadows", "RandomizedLight", "Bounds", "useBounds", "presets", "<PERSON><PERSON><PERSON><PERSON>", "main", "fill", "portrait", "upfront", "soft", "Refit", "radius", "adjustCamera", "api", "useEffect", "refresh", "clip", "fit", "Stage", "children", "center", "intensity", "shadows", "environment", "preset", "props", "_bias", "_normalBias", "_size", "_offset", "_amount", "_radius", "_ambient", "_intensity", "config", "height", "set", "useState", "width", "depth", "<PERSON><PERSON><PERSON>", "bias", "normalBias", "shadowSize", "size", "shadowOffset", "offset", "contactShadow", "type", "accumulativeShadow", "shadowSpread", "environmentProps", "onCentered", "useCallback", "boundingSphere", "createElement", "Fragment", "penumbra", "position", "<PERSON><PERSON><PERSON><PERSON>", "margin", "Number", "observe", "scale", "far", "blur", "temporal", "frames", "alphaTest", "toneMapped", "amount", "ambient", "mapSize"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Stage.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Environment } from './Environment.js';\nimport { ContactShadows } from './ContactShadows.js';\nimport { Center } from './Center.js';\nimport { AccumulativeShadows, RandomizedLight } from './AccumulativeShadows.js';\nimport { Bounds, useBounds } from './Bounds.js';\n\nconst presets = {\n  rembrandt: {\n    main: [1, 2, 1],\n    fill: [-2, -0.5, -2]\n  },\n  portrait: {\n    main: [-1, 2, 0.5],\n    fill: [-1, 0.5, -1.5]\n  },\n  upfront: {\n    main: [0, 2, 1],\n    fill: [-1, 0.5, -1.5]\n  },\n  soft: {\n    main: [-2, 4, 4],\n    fill: [-1, 0.5, -1.5]\n  }\n};\n\nfunction Refit({\n  radius,\n  adjustCamera\n}) {\n  const api = useBounds();\n  React.useEffect(() => {\n    if (adjustCamera) api.refresh().clip().fit();\n  }, [radius, adjustCamera]);\n  return null;\n}\n\nfunction Stage({\n  children,\n  center,\n  adjustCamera = true,\n  intensity = 0.5,\n  shadows = 'contact',\n  environment = 'city',\n  preset = 'rembrandt',\n  ...props\n}) {\n  var _bias, _normalBias, _size, _offset, _amount, _radius, _ambient, _intensity;\n\n  const config = typeof preset === 'string' ? presets[preset] : preset;\n  const [{\n    radius,\n    height\n  }, set] = React.useState({\n    radius: 0,\n    width: 0,\n    height: 0,\n    depth: 0\n  });\n  const shadowBias = (_bias = shadows == null ? void 0 : shadows.bias) !== null && _bias !== void 0 ? _bias : -0.0001;\n  const normalBias = (_normalBias = shadows == null ? void 0 : shadows.normalBias) !== null && _normalBias !== void 0 ? _normalBias : 0;\n  const shadowSize = (_size = shadows == null ? void 0 : shadows.size) !== null && _size !== void 0 ? _size : 1024;\n  const shadowOffset = (_offset = shadows == null ? void 0 : shadows.offset) !== null && _offset !== void 0 ? _offset : 0;\n  const contactShadow = shadows === 'contact' || (shadows == null ? void 0 : shadows.type) === 'contact';\n  const accumulativeShadow = shadows === 'accumulative' || (shadows == null ? void 0 : shadows.type) === 'accumulative';\n  const shadowSpread = { ...(typeof shadows === 'object' ? shadows : {})\n  };\n  const environmentProps = !environment ? null : typeof environment === 'string' ? {\n    preset: environment\n  } : environment;\n  const onCentered = React.useCallback(props => {\n    const {\n      width,\n      height,\n      depth,\n      boundingSphere\n    } = props;\n    set({\n      radius: boundingSphere.radius,\n      width,\n      height,\n      depth\n    });\n    if (center != null && center.onCentered) center.onCentered(props);\n  }, []);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"ambientLight\", {\n    intensity: intensity / 3\n  }), /*#__PURE__*/React.createElement(\"spotLight\", {\n    penumbra: 1,\n    position: [config.main[0] * radius, config.main[1] * radius, config.main[2] * radius],\n    intensity: intensity * 2,\n    castShadow: !!shadows,\n    \"shadow-bias\": shadowBias,\n    \"shadow-normalBias\": normalBias,\n    \"shadow-mapSize\": shadowSize\n  }), /*#__PURE__*/React.createElement(\"pointLight\", {\n    position: [config.fill[0] * radius, config.fill[1] * radius, config.fill[2] * radius],\n    intensity: intensity\n  }), /*#__PURE__*/React.createElement(Bounds, _extends({\n    fit: !!adjustCamera,\n    clip: !!adjustCamera,\n    margin: Number(adjustCamera),\n    observe: true\n  }, props), /*#__PURE__*/React.createElement(Refit, {\n    radius: radius,\n    adjustCamera: adjustCamera\n  }), /*#__PURE__*/React.createElement(Center, _extends({}, center, {\n    position: [0, shadowOffset / 2, 0],\n    onCentered: onCentered\n  }), children)), /*#__PURE__*/React.createElement(\"group\", {\n    position: [0, -height / 2 - shadowOffset / 2, 0]\n  }, contactShadow && /*#__PURE__*/React.createElement(ContactShadows, _extends({\n    scale: radius * 4,\n    far: radius,\n    blur: 2\n  }, shadowSpread)), accumulativeShadow && /*#__PURE__*/React.createElement(AccumulativeShadows, _extends({\n    temporal: true,\n    frames: 100,\n    alphaTest: 0.9,\n    toneMapped: true,\n    scale: radius * 4\n  }, shadowSpread), /*#__PURE__*/React.createElement(RandomizedLight, {\n    amount: (_amount = shadowSpread.amount) !== null && _amount !== void 0 ? _amount : 8,\n    radius: (_radius = shadowSpread.radius) !== null && _radius !== void 0 ? _radius : radius,\n    ambient: (_ambient = shadowSpread.ambient) !== null && _ambient !== void 0 ? _ambient : 0.5,\n    intensity: (_intensity = shadowSpread.intensity) !== null && _intensity !== void 0 ? _intensity : 1,\n    position: [config.main[0] * radius, config.main[1] * radius, config.main[2] * radius],\n    size: radius * 4,\n    bias: -shadowBias,\n    mapSize: shadowSize\n  }))), environment && /*#__PURE__*/React.createElement(Environment, environmentProps));\n}\n\nexport { Stage };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,mBAAmB,EAAEC,eAAe,QAAQ,0BAA0B;AAC/E,SAASC,MAAM,EAAEC,SAAS,QAAQ,aAAa;AAE/C,MAAMC,OAAO,GAAG;EACdC,SAAS,EAAE;IACTC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACfC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;EACrB,CAAC;EACDC,QAAQ,EAAE;IACRF,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;IAClBC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG;EACtB,CAAC;EACDE,OAAO,EAAE;IACPH,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACfC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG;EACtB,CAAC;EACDG,IAAI,EAAE;IACJJ,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAChBC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG;EACtB;AACF,CAAC;AAED,SAASI,KAAKA,CAAC;EACbC,MAAM;EACNC;AACF,CAAC,EAAE;EACD,MAAMC,GAAG,GAAGX,SAAS,CAAC,CAAC;EACvBP,KAAK,CAACmB,SAAS,CAAC,MAAM;IACpB,IAAIF,YAAY,EAAEC,GAAG,CAACE,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC;EAC9C,CAAC,EAAE,CAACN,MAAM,EAAEC,YAAY,CAAC,CAAC;EAC1B,OAAO,IAAI;AACb;AAEA,SAASM,KAAKA,CAAC;EACbC,QAAQ;EACRC,MAAM;EACNR,YAAY,GAAG,IAAI;EACnBS,SAAS,GAAG,GAAG;EACfC,OAAO,GAAG,SAAS;EACnBC,WAAW,GAAG,MAAM;EACpBC,MAAM,GAAG,WAAW;EACpB,GAAGC;AACL,CAAC,EAAE;EACD,IAAIC,KAAK,EAAEC,WAAW,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU;EAE9E,MAAMC,MAAM,GAAG,OAAOV,MAAM,KAAK,QAAQ,GAAGrB,OAAO,CAACqB,MAAM,CAAC,GAAGA,MAAM;EACpE,MAAM,CAAC;IACLb,MAAM;IACNwB;EACF,CAAC,EAAEC,GAAG,CAAC,GAAGzC,KAAK,CAAC0C,QAAQ,CAAC;IACvB1B,MAAM,EAAE,CAAC;IACT2B,KAAK,EAAE,CAAC;IACRH,MAAM,EAAE,CAAC;IACTI,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAMC,UAAU,GAAG,CAACd,KAAK,GAAGJ,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACmB,IAAI,MAAM,IAAI,IAAIf,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC,MAAM;EACnH,MAAMgB,UAAU,GAAG,CAACf,WAAW,GAAGL,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACoB,UAAU,MAAM,IAAI,IAAIf,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG,CAAC;EACrI,MAAMgB,UAAU,GAAG,CAACf,KAAK,GAAGN,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACsB,IAAI,MAAM,IAAI,IAAIhB,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,IAAI;EAChH,MAAMiB,YAAY,GAAG,CAAChB,OAAO,GAAGP,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACwB,MAAM,MAAM,IAAI,IAAIjB,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAG,CAAC;EACvH,MAAMkB,aAAa,GAAGzB,OAAO,KAAK,SAAS,IAAI,CAACA,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC0B,IAAI,MAAM,SAAS;EACtG,MAAMC,kBAAkB,GAAG3B,OAAO,KAAK,cAAc,IAAI,CAACA,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC0B,IAAI,MAAM,cAAc;EACrH,MAAME,YAAY,GAAG;IAAE,IAAI,OAAO5B,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAG,CAAC,CAAC;EACrE,CAAC;EACD,MAAM6B,gBAAgB,GAAG,CAAC5B,WAAW,GAAG,IAAI,GAAG,OAAOA,WAAW,KAAK,QAAQ,GAAG;IAC/EC,MAAM,EAAED;EACV,CAAC,GAAGA,WAAW;EACf,MAAM6B,UAAU,GAAGzD,KAAK,CAAC0D,WAAW,CAAC5B,KAAK,IAAI;IAC5C,MAAM;MACJa,KAAK;MACLH,MAAM;MACNI,KAAK;MACLe;IACF,CAAC,GAAG7B,KAAK;IACTW,GAAG,CAAC;MACFzB,MAAM,EAAE2C,cAAc,CAAC3C,MAAM;MAC7B2B,KAAK;MACLH,MAAM;MACNI;IACF,CAAC,CAAC;IACF,IAAInB,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACgC,UAAU,EAAEhC,MAAM,CAACgC,UAAU,CAAC3B,KAAK,CAAC;EACnE,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAa9B,KAAK,CAAC4D,aAAa,CAAC5D,KAAK,CAAC6D,QAAQ,EAAE,IAAI,EAAE,aAAa7D,KAAK,CAAC4D,aAAa,CAAC,cAAc,EAAE;IAC7GlC,SAAS,EAAEA,SAAS,GAAG;EACzB,CAAC,CAAC,EAAE,aAAa1B,KAAK,CAAC4D,aAAa,CAAC,WAAW,EAAE;IAChDE,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,CAACxB,MAAM,CAAC7B,IAAI,CAAC,CAAC,CAAC,GAAGM,MAAM,EAAEuB,MAAM,CAAC7B,IAAI,CAAC,CAAC,CAAC,GAAGM,MAAM,EAAEuB,MAAM,CAAC7B,IAAI,CAAC,CAAC,CAAC,GAAGM,MAAM,CAAC;IACrFU,SAAS,EAAEA,SAAS,GAAG,CAAC;IACxBsC,UAAU,EAAE,CAAC,CAACrC,OAAO;IACrB,aAAa,EAAEkB,UAAU;IACzB,mBAAmB,EAAEE,UAAU;IAC/B,gBAAgB,EAAEC;EACpB,CAAC,CAAC,EAAE,aAAahD,KAAK,CAAC4D,aAAa,CAAC,YAAY,EAAE;IACjDG,QAAQ,EAAE,CAACxB,MAAM,CAAC5B,IAAI,CAAC,CAAC,CAAC,GAAGK,MAAM,EAAEuB,MAAM,CAAC5B,IAAI,CAAC,CAAC,CAAC,GAAGK,MAAM,EAAEuB,MAAM,CAAC5B,IAAI,CAAC,CAAC,CAAC,GAAGK,MAAM,CAAC;IACrFU,SAAS,EAAEA;EACb,CAAC,CAAC,EAAE,aAAa1B,KAAK,CAAC4D,aAAa,CAACtD,MAAM,EAAEP,QAAQ,CAAC;IACpDuB,GAAG,EAAE,CAAC,CAACL,YAAY;IACnBI,IAAI,EAAE,CAAC,CAACJ,YAAY;IACpBgD,MAAM,EAAEC,MAAM,CAACjD,YAAY,CAAC;IAC5BkD,OAAO,EAAE;EACX,CAAC,EAAErC,KAAK,CAAC,EAAE,aAAa9B,KAAK,CAAC4D,aAAa,CAAC7C,KAAK,EAAE;IACjDC,MAAM,EAAEA,MAAM;IACdC,YAAY,EAAEA;EAChB,CAAC,CAAC,EAAE,aAAajB,KAAK,CAAC4D,aAAa,CAACzD,MAAM,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAE0B,MAAM,EAAE;IAChEsC,QAAQ,EAAE,CAAC,CAAC,EAAEb,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC;IAClCO,UAAU,EAAEA;EACd,CAAC,CAAC,EAAEjC,QAAQ,CAAC,CAAC,EAAE,aAAaxB,KAAK,CAAC4D,aAAa,CAAC,OAAO,EAAE;IACxDG,QAAQ,EAAE,CAAC,CAAC,EAAE,CAACvB,MAAM,GAAG,CAAC,GAAGU,YAAY,GAAG,CAAC,EAAE,CAAC;EACjD,CAAC,EAAEE,aAAa,IAAI,aAAapD,KAAK,CAAC4D,aAAa,CAAC1D,cAAc,EAAEH,QAAQ,CAAC;IAC5EqE,KAAK,EAAEpD,MAAM,GAAG,CAAC;IACjBqD,GAAG,EAAErD,MAAM;IACXsD,IAAI,EAAE;EACR,CAAC,EAAEf,YAAY,CAAC,CAAC,EAAED,kBAAkB,IAAI,aAAatD,KAAK,CAAC4D,aAAa,CAACxD,mBAAmB,EAAEL,QAAQ,CAAC;IACtGwE,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE,GAAG;IACdC,UAAU,EAAE,IAAI;IAChBN,KAAK,EAAEpD,MAAM,GAAG;EAClB,CAAC,EAAEuC,YAAY,CAAC,EAAE,aAAavD,KAAK,CAAC4D,aAAa,CAACvD,eAAe,EAAE;IAClEsE,MAAM,EAAE,CAACxC,OAAO,GAAGoB,YAAY,CAACoB,MAAM,MAAM,IAAI,IAAIxC,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAG,CAAC;IACpFnB,MAAM,EAAE,CAACoB,OAAO,GAAGmB,YAAY,CAACvC,MAAM,MAAM,IAAI,IAAIoB,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAGpB,MAAM;IACzF4D,OAAO,EAAE,CAACvC,QAAQ,GAAGkB,YAAY,CAACqB,OAAO,MAAM,IAAI,IAAIvC,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAG,GAAG;IAC3FX,SAAS,EAAE,CAACY,UAAU,GAAGiB,YAAY,CAAC7B,SAAS,MAAM,IAAI,IAAIY,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG,CAAC;IACnGyB,QAAQ,EAAE,CAACxB,MAAM,CAAC7B,IAAI,CAAC,CAAC,CAAC,GAAGM,MAAM,EAAEuB,MAAM,CAAC7B,IAAI,CAAC,CAAC,CAAC,GAAGM,MAAM,EAAEuB,MAAM,CAAC7B,IAAI,CAAC,CAAC,CAAC,GAAGM,MAAM,CAAC;IACrFiC,IAAI,EAAEjC,MAAM,GAAG,CAAC;IAChB8B,IAAI,EAAE,CAACD,UAAU;IACjBgC,OAAO,EAAE7B;EACX,CAAC,CAAC,CAAC,CAAC,EAAEpB,WAAW,IAAI,aAAa5B,KAAK,CAAC4D,aAAa,CAAC3D,WAAW,EAAEuD,gBAAgB,CAAC,CAAC;AACvF;AAEA,SAASjC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}