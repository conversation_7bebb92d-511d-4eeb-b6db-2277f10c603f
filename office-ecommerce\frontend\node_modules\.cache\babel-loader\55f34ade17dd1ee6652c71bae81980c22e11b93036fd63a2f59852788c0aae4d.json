{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\admin\\\\ProductManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { toast } from 'react-toastify';\nimport ProductFormModal from './modals/ProductFormModal';\nimport ProductDetailsModal from './modals/ProductDetailsModal';\nimport ConfirmationModal from '../modals/ConfirmationModal';\nimport { productsApi } from '../../services/api';\nimport websocketService from '../../services/websocketService';\nimport './ProductManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductManagement = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('');\n  const [sortBy, setSortBy] = useState('ProductName');\n  const [sortDirection, setSortDirection] = useState('ASC');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(20);\n  const [totalCount, setTotalCount] = useState(0);\n  const [totalPages, setTotalPages] = useState(0);\n\n  // Modal states\n  const [showProductForm, setShowProductForm] = useState(false);\n  const [showProductDetails, setShowProductDetails] = useState(false);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [editingProduct, setEditingProduct] = useState(null);\n\n  // Fetch products with filters and pagination\n  const fetchProducts = useCallback(async () => {\n    try {\n      setLoading(true);\n      const params = {\n        page: currentPage,\n        limit: pageSize,\n        search: searchTerm || undefined,\n        category: selectedCategory || undefined,\n        status: selectedStatus || undefined,\n        sortBy,\n        sortDirection\n      };\n      const response = await productsApi.getProducts(params);\n      if (response.success) {\n        setProducts(response.data);\n        setTotalCount(response.pagination.totalCount);\n        setTotalPages(response.pagination.totalPages);\n      } else {\n        toast.error('Failed to fetch products');\n      }\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      toast.error('Error loading products');\n    } finally {\n      setLoading(false);\n    }\n  }, [currentPage, pageSize, searchTerm, selectedCategory, selectedStatus, sortBy, sortDirection]);\n\n  // Fetch categories\n  const fetchCategories = useCallback(async () => {\n    try {\n      const response = await productsApi.getCategories();\n      if (response.success) {\n        setCategories(response.data);\n      }\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n    }\n  }, []);\n  useEffect(() => {\n    fetchProducts();\n  }, [fetchProducts]);\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n\n  // WebSocket integration for real-time updates\n  useEffect(() => {\n    // Subscribe to product updates\n    websocketService.subscribeToProducts();\n\n    // Set up event listeners\n    const handleProductCreated = data => {\n      toast.success(`New product \"${data.ProductName}\" has been created`);\n      fetchProducts(); // Refresh the product list\n    };\n    const handleProductUpdated = data => {\n      toast.info(`Product \"${data.ProductName}\" has been updated`);\n      fetchProducts(); // Refresh the product list\n    };\n    const handleProductDeleted = data => {\n      toast.warning(`Product has been deleted`);\n      fetchProducts(); // Refresh the product list\n    };\n    const handleProductFileUploaded = data => {\n      toast.success(`File uploaded for product ID: ${data.productId}`);\n      // Optionally refresh specific product or entire list\n      fetchProducts();\n    };\n\n    // Register event listeners\n    websocketService.on('productCreated', handleProductCreated);\n    websocketService.on('productUpdated', handleProductUpdated);\n    websocketService.on('productDeleted', handleProductDeleted);\n    websocketService.on('productFileUploaded', handleProductFileUploaded);\n\n    // Cleanup function\n    return () => {\n      websocketService.off('productCreated', handleProductCreated);\n      websocketService.off('productUpdated', handleProductUpdated);\n      websocketService.off('productDeleted', handleProductDeleted);\n      websocketService.off('productFileUploaded', handleProductFileUploaded);\n    };\n  }, [fetchProducts]);\n\n  // Handle search with debouncing\n  const handleSearch = useCallback(value => {\n    setSearchTerm(value);\n    setCurrentPage(1);\n  }, []);\n\n  // Handle sorting\n  const handleSort = field => {\n    if (sortBy === field) {\n      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');\n    } else {\n      setSortBy(field);\n      setSortDirection('ASC');\n    }\n    setCurrentPage(1);\n  };\n\n  // Handle product actions\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setShowProductForm(true);\n  };\n  const handleEditProduct = product => {\n    setEditingProduct(product);\n    setShowProductForm(true);\n  };\n  const handleViewProduct = async productId => {\n    try {\n      const response = await productsApi.getProductById(productId);\n      if (response.success) {\n        setSelectedProduct(response.data);\n        setShowProductDetails(true);\n      } else {\n        toast.error('Failed to load product details');\n      }\n    } catch (error) {\n      console.error('Error fetching product details:', error);\n      toast.error('Error loading product details');\n    }\n  };\n  const handleDeleteProduct = product => {\n    setSelectedProduct(product);\n    setShowDeleteConfirm(true);\n  };\n  const confirmDeleteProduct = async () => {\n    try {\n      const response = await productsApi.deleteProduct(selectedProduct.ProductID);\n      if (response.success) {\n        toast.success('Product deleted successfully');\n        fetchProducts();\n      } else {\n        toast.error('Failed to delete product');\n      }\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      toast.error('Error deleting product');\n    } finally {\n      setShowDeleteConfirm(false);\n      setSelectedProduct(null);\n    }\n  };\n  const handleProductSaved = () => {\n    setShowProductForm(false);\n    setEditingProduct(null);\n    fetchProducts();\n    toast.success(editingProduct ? 'Product updated successfully' : 'Product created successfully');\n  };\n\n  // Utility functions\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n  const getStatusBadgeColor = status => {\n    switch (status) {\n      case 'Active':\n        return '#27ae60';\n      case 'Draft':\n        return '#f39c12';\n      case 'Inactive':\n        return '#95a5a6';\n      case 'Discontinued':\n        return '#e74c3c';\n      case 'Pending Review':\n        return '#3498db';\n      default:\n        return '#95a5a6';\n    }\n  };\n  const getSortIcon = field => {\n    if (sortBy !== field) return '↕️';\n    return sortDirection === 'ASC' ? '↑' : '↓';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading products...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-card-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"admin-card-title\",\n        children: \"Product Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"admin-btn admin-btn-primary\",\n        onClick: handleAddProduct,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"btn-icon\",\n          children: \"+\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), \"Add New Product\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-filters\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"search\",\n              children: \"Search Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"search\",\n              type: \"text\",\n              placeholder: \"Search by name, code, or description...\",\n              value: searchTerm,\n              onChange: e => handleSearch(e.target.value),\n              className: \"admin-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"category\",\n              children: \"Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"category\",\n              value: selectedCategory,\n              onChange: e => setSelectedCategory(e.target.value),\n              className: \"admin-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.CategoryID,\n                children: category.CategoryName\n              }, category.CategoryID, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"status\",\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"status\",\n              value: selectedStatus,\n              onChange: e => setSelectedStatus(e.target.value),\n              className: \"admin-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All Statuses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Active\",\n                children: \"Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Draft\",\n                children: \"Draft\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Inactive\",\n                children: \"Inactive\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Discontinued\",\n                children: \"Discontinued\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Pending Review\",\n                children: \"Pending Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"pageSize\",\n              children: \"Items per page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"pageSize\",\n              value: pageSize,\n              onChange: e => {\n                setPageSize(parseInt(e.target.value));\n                setCurrentPage(1);\n              },\n              className: \"admin-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: 10,\n                children: \"10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 20,\n                children: \"20\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 50,\n                children: \"50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 100,\n                children: \"100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"admin-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"sortable\",\n                onClick: () => handleSort('ProductName'),\n                children: [\"Product Name \", getSortIcon('ProductName')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"sortable\",\n                onClick: () => handleSort('BasePrice'),\n                children: [\"Price \", getSortIcon('BasePrice')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"sortable\",\n                onClick: () => handleSort('Status'),\n                children: [\"Status \", getSortIcon('Status')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Files\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"sortable\",\n                onClick: () => handleSort('UpdatedAt'),\n                children: [\"Last Updated \", getSortIcon('UpdatedAt')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: products.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"7\",\n                className: \"no-data\",\n                children: \"No products found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this) : products.map(product => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: product.ProductName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: product.ProductCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.CategoryName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatCurrency(product.BasePrice)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"status-badge\",\n                  style: {\n                    backgroundColor: getStatusBadgeColor(product.Status)\n                  },\n                  children: product.Status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-counts\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-count\",\n                    children: [\"\\uD83D\\uDCF7 \", product.ImageCount || 0]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-count\",\n                    children: [\"\\uD83C\\uDFAF \", product.ModelCount || 0]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.UpdatedAt ? new Date(product.UpdatedAt).toLocaleDateString() : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"action-buttons\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"admin-btn admin-btn-secondary btn-small\",\n                    onClick: () => handleViewProduct(product.ProductID),\n                    title: \"View Details\",\n                    children: \"\\uD83D\\uDC41\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"admin-btn admin-btn-secondary btn-small\",\n                    onClick: () => handleEditProduct(product),\n                    title: \"Edit Product\",\n                    children: \"\\u270F\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"admin-btn admin-btn-danger btn-small\",\n                    onClick: () => handleDeleteProduct(product),\n                    title: \"Delete Product\",\n                    children: \"\\uD83D\\uDDD1\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 21\n              }, this)]\n            }, product.ProductID, true, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pagination\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-info\",\n          children: [\"Showing \", (currentPage - 1) * pageSize + 1, \" to \", Math.min(currentPage * pageSize, totalCount), \" of \", totalCount, \" products\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn admin-btn-secondary btn-small\",\n            onClick: () => setCurrentPage(1),\n            disabled: currentPage === 1,\n            children: \"First\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn admin-btn-secondary btn-small\",\n            onClick: () => setCurrentPage(currentPage - 1),\n            disabled: currentPage === 1,\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"page-info\",\n            children: [\"Page \", currentPage, \" of \", totalPages]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn admin-btn-secondary btn-small\",\n            onClick: () => setCurrentPage(currentPage + 1),\n            disabled: currentPage === totalPages,\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn admin-btn-secondary btn-small\",\n            onClick: () => setCurrentPage(totalPages),\n            disabled: currentPage === totalPages,\n            children: \"Last\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 7\n    }, this), showProductForm && /*#__PURE__*/_jsxDEV(ProductFormModal, {\n      product: editingProduct,\n      categories: categories,\n      onSave: handleProductSaved,\n      onClose: () => {\n        setShowProductForm(false);\n        setEditingProduct(null);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 466,\n      columnNumber: 9\n    }, this), showProductDetails && selectedProduct && /*#__PURE__*/_jsxDEV(ProductDetailsModal, {\n      product: selectedProduct,\n      onClose: () => {\n        setShowProductDetails(false);\n        setSelectedProduct(null);\n      },\n      onEdit: () => {\n        setEditingProduct(selectedProduct.product);\n        setShowProductDetails(false);\n        setShowProductForm(true);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 478,\n      columnNumber: 9\n    }, this), showDeleteConfirm && selectedProduct && /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n      title: \"Delete Product\",\n      message: `Are you sure you want to delete \"${selectedProduct.ProductName}\"? This action cannot be undone.`,\n      confirmText: \"Delete\",\n      cancelText: \"Cancel\",\n      onConfirm: confirmDeleteProduct,\n      onCancel: () => {\n        setShowDeleteConfirm(false);\n        setSelectedProduct(null);\n      },\n      type: \"danger\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 493,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 231,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductManagement, \"TPNr0vrGcbdkLocBG9h82YZtOHA=\");\n_c = ProductManagement;\nexport default ProductManagement;\nvar _c;\n$RefreshReg$(_c, \"ProductManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "toast", "ProductFormModal", "ProductDetailsModal", "ConfirmationModal", "productsApi", "websocketService", "jsxDEV", "_jsxDEV", "ProductManagement", "_s", "products", "setProducts", "categories", "setCategories", "loading", "setLoading", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedStatus", "setSelectedStatus", "sortBy", "setSortBy", "sortDirection", "setSortDirection", "currentPage", "setCurrentPage", "pageSize", "setPageSize", "totalCount", "setTotalCount", "totalPages", "setTotalPages", "showProductForm", "setShowProductForm", "showProductDetails", "setShowProductDetails", "showDeleteConfirm", "setShowDeleteConfirm", "selectedProduct", "setSelectedProduct", "editingProduct", "setEditingProduct", "fetchProducts", "params", "page", "limit", "search", "undefined", "category", "status", "response", "getProducts", "success", "data", "pagination", "error", "console", "fetchCategories", "getCategories", "subscribeToProducts", "handleProductCreated", "ProductName", "handleProductUpdated", "info", "handleProductDeleted", "warning", "handleProductFileUploaded", "productId", "on", "off", "handleSearch", "value", "handleSort", "field", "handleAddProduct", "handleEditProduct", "product", "handleViewProduct", "getProductById", "handleDeleteProduct", "confirmDeleteProduct", "deleteProduct", "ProductID", "handleProductSaved", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "getStatusBadgeColor", "getSortIcon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "htmlFor", "id", "type", "placeholder", "onChange", "e", "target", "map", "CategoryID", "CategoryName", "parseInt", "length", "colSpan", "ProductCode", "BasePrice", "backgroundColor", "Status", "ImageCount", "ModelCount", "UpdatedAt", "Date", "toLocaleDateString", "title", "Math", "min", "disabled", "onSave", "onClose", "onEdit", "message", "confirmText", "cancelText", "onConfirm", "onCancel", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/admin/ProductManagement.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { toast } from 'react-toastify';\nimport ProductFormModal from './modals/ProductFormModal';\nimport ProductDetailsModal from './modals/ProductDetailsModal';\nimport ConfirmationModal from '../modals/ConfirmationModal';\nimport { productsApi } from '../../services/api';\nimport websocketService from '../../services/websocketService';\nimport './ProductManagement.css';\n\nconst ProductManagement = () => {\n  const [products, setProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('');\n  const [sortBy, setSortBy] = useState('ProductName');\n  const [sortDirection, setSortDirection] = useState('ASC');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(20);\n  const [totalCount, setTotalCount] = useState(0);\n  const [totalPages, setTotalPages] = useState(0);\n\n  // Modal states\n  const [showProductForm, setShowProductForm] = useState(false);\n  const [showProductDetails, setShowProductDetails] = useState(false);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [editingProduct, setEditingProduct] = useState(null);\n\n  // Fetch products with filters and pagination\n  const fetchProducts = useCallback(async () => {\n    try {\n      setLoading(true);\n      const params = {\n        page: currentPage,\n        limit: pageSize,\n        search: searchTerm || undefined,\n        category: selectedCategory || undefined,\n        status: selectedStatus || undefined,\n        sortBy,\n        sortDirection\n      };\n\n      const response = await productsApi.getProducts(params);\n\n      if (response.success) {\n        setProducts(response.data);\n        setTotalCount(response.pagination.totalCount);\n        setTotalPages(response.pagination.totalPages);\n      } else {\n        toast.error('Failed to fetch products');\n      }\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      toast.error('Error loading products');\n    } finally {\n      setLoading(false);\n    }\n  }, [currentPage, pageSize, searchTerm, selectedCategory, selectedStatus, sortBy, sortDirection]);\n\n  // Fetch categories\n  const fetchCategories = useCallback(async () => {\n    try {\n      const response = await productsApi.getCategories();\n      if (response.success) {\n        setCategories(response.data);\n      }\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n    }\n  }, []);\n\n  useEffect(() => {\n    fetchProducts();\n  }, [fetchProducts]);\n\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n\n  // WebSocket integration for real-time updates\n  useEffect(() => {\n    // Subscribe to product updates\n    websocketService.subscribeToProducts();\n\n    // Set up event listeners\n    const handleProductCreated = (data) => {\n      toast.success(`New product \"${data.ProductName}\" has been created`);\n      fetchProducts(); // Refresh the product list\n    };\n\n    const handleProductUpdated = (data) => {\n      toast.info(`Product \"${data.ProductName}\" has been updated`);\n      fetchProducts(); // Refresh the product list\n    };\n\n    const handleProductDeleted = (data) => {\n      toast.warning(`Product has been deleted`);\n      fetchProducts(); // Refresh the product list\n    };\n\n    const handleProductFileUploaded = (data) => {\n      toast.success(`File uploaded for product ID: ${data.productId}`);\n      // Optionally refresh specific product or entire list\n      fetchProducts();\n    };\n\n    // Register event listeners\n    websocketService.on('productCreated', handleProductCreated);\n    websocketService.on('productUpdated', handleProductUpdated);\n    websocketService.on('productDeleted', handleProductDeleted);\n    websocketService.on('productFileUploaded', handleProductFileUploaded);\n\n    // Cleanup function\n    return () => {\n      websocketService.off('productCreated', handleProductCreated);\n      websocketService.off('productUpdated', handleProductUpdated);\n      websocketService.off('productDeleted', handleProductDeleted);\n      websocketService.off('productFileUploaded', handleProductFileUploaded);\n    };\n  }, [fetchProducts]);\n\n  // Handle search with debouncing\n  const handleSearch = useCallback((value) => {\n    setSearchTerm(value);\n    setCurrentPage(1);\n  }, []);\n\n  // Handle sorting\n  const handleSort = (field) => {\n    if (sortBy === field) {\n      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');\n    } else {\n      setSortBy(field);\n      setSortDirection('ASC');\n    }\n    setCurrentPage(1);\n  };\n\n  // Handle product actions\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setShowProductForm(true);\n  };\n\n  const handleEditProduct = (product) => {\n    setEditingProduct(product);\n    setShowProductForm(true);\n  };\n\n  const handleViewProduct = async (productId) => {\n    try {\n      const response = await productsApi.getProductById(productId);\n      if (response.success) {\n        setSelectedProduct(response.data);\n        setShowProductDetails(true);\n      } else {\n        toast.error('Failed to load product details');\n      }\n    } catch (error) {\n      console.error('Error fetching product details:', error);\n      toast.error('Error loading product details');\n    }\n  };\n\n  const handleDeleteProduct = (product) => {\n    setSelectedProduct(product);\n    setShowDeleteConfirm(true);\n  };\n\n  const confirmDeleteProduct = async () => {\n    try {\n      const response = await productsApi.deleteProduct(selectedProduct.ProductID);\n      if (response.success) {\n        toast.success('Product deleted successfully');\n        fetchProducts();\n      } else {\n        toast.error('Failed to delete product');\n      }\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      toast.error('Error deleting product');\n    } finally {\n      setShowDeleteConfirm(false);\n      setSelectedProduct(null);\n    }\n  };\n\n  const handleProductSaved = () => {\n    setShowProductForm(false);\n    setEditingProduct(null);\n    fetchProducts();\n    toast.success(editingProduct ? 'Product updated successfully' : 'Product created successfully');\n  };\n\n  // Utility functions\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n\n  const getStatusBadgeColor = (status) => {\n    switch (status) {\n      case 'Active': return '#27ae60';\n      case 'Draft': return '#f39c12';\n      case 'Inactive': return '#95a5a6';\n      case 'Discontinued': return '#e74c3c';\n      case 'Pending Review': return '#3498db';\n      default: return '#95a5a6';\n    }\n  };\n\n  const getSortIcon = (field) => {\n    if (sortBy !== field) return '↕️';\n    return sortDirection === 'ASC' ? '↑' : '↓';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"admin-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading products...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"product-management\">\n      {/* Header */}\n      <div className=\"admin-card-header\">\n        <h1 className=\"admin-card-title\">Product Management</h1>\n        <button\n          className=\"admin-btn admin-btn-primary\"\n          onClick={handleAddProduct}\n        >\n          <span className=\"btn-icon\">+</span>\n          Add New Product\n        </button>\n      </div>\n\n      {/* Filters and Search */}\n      <div className=\"admin-card\">\n        <div className=\"product-filters\">\n          <div className=\"filter-row\">\n            <div className=\"filter-group\">\n              <label htmlFor=\"search\">Search Products</label>\n              <input\n                id=\"search\"\n                type=\"text\"\n                placeholder=\"Search by name, code, or description...\"\n                value={searchTerm}\n                onChange={(e) => handleSearch(e.target.value)}\n                className=\"admin-input\"\n              />\n            </div>\n\n            <div className=\"filter-group\">\n              <label htmlFor=\"category\">Category</label>\n              <select\n                id=\"category\"\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n                className=\"admin-select\"\n              >\n                <option value=\"\">All Categories</option>\n                {categories.map(category => (\n                  <option key={category.CategoryID} value={category.CategoryID}>\n                    {category.CategoryName}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div className=\"filter-group\">\n              <label htmlFor=\"status\">Status</label>\n              <select\n                id=\"status\"\n                value={selectedStatus}\n                onChange={(e) => setSelectedStatus(e.target.value)}\n                className=\"admin-select\"\n              >\n                <option value=\"\">All Statuses</option>\n                <option value=\"Active\">Active</option>\n                <option value=\"Draft\">Draft</option>\n                <option value=\"Inactive\">Inactive</option>\n                <option value=\"Discontinued\">Discontinued</option>\n                <option value=\"Pending Review\">Pending Review</option>\n              </select>\n            </div>\n\n            <div className=\"filter-group\">\n              <label htmlFor=\"pageSize\">Items per page</label>\n              <select\n                id=\"pageSize\"\n                value={pageSize}\n                onChange={(e) => {\n                  setPageSize(parseInt(e.target.value));\n                  setCurrentPage(1);\n                }}\n                className=\"admin-select\"\n              >\n                <option value={10}>10</option>\n                <option value={20}>20</option>\n                <option value={50}>50</option>\n                <option value={100}>100</option>\n              </select>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Products Table */}\n      <div className=\"admin-card\">\n        <div className=\"table-container\">\n          <table className=\"admin-table\">\n            <thead>\n              <tr>\n                <th\n                  className=\"sortable\"\n                  onClick={() => handleSort('ProductName')}\n                >\n                  Product Name {getSortIcon('ProductName')}\n                </th>\n                <th>Category</th>\n                <th\n                  className=\"sortable\"\n                  onClick={() => handleSort('BasePrice')}\n                >\n                  Price {getSortIcon('BasePrice')}\n                </th>\n                <th\n                  className=\"sortable\"\n                  onClick={() => handleSort('Status')}\n                >\n                  Status {getSortIcon('Status')}\n                </th>\n                <th>Files</th>\n                <th\n                  className=\"sortable\"\n                  onClick={() => handleSort('UpdatedAt')}\n                >\n                  Last Updated {getSortIcon('UpdatedAt')}\n                </th>\n                <th>Actions</th>\n              </tr>\n            </thead>\n            <tbody>\n              {products.length === 0 ? (\n                <tr>\n                  <td colSpan=\"7\" className=\"no-data\">\n                    No products found\n                  </td>\n                </tr>\n              ) : (\n                products.map(product => (\n                  <tr key={product.ProductID}>\n                    <td>\n                      <div className=\"product-info\">\n                        <strong>{product.ProductName}</strong>\n                        <small>{product.ProductCode}</small>\n                      </div>\n                    </td>\n                    <td>{product.CategoryName}</td>\n                    <td>{formatCurrency(product.BasePrice)}</td>\n                    <td>\n                      <span\n                        className=\"status-badge\"\n                        style={{ backgroundColor: getStatusBadgeColor(product.Status) }}\n                      >\n                        {product.Status}\n                      </span>\n                    </td>\n                    <td>\n                      <div className=\"file-counts\">\n                        <span className=\"file-count\">\n                          📷 {product.ImageCount || 0}\n                        </span>\n                        <span className=\"file-count\">\n                          🎯 {product.ModelCount || 0}\n                        </span>\n                      </div>\n                    </td>\n                    <td>\n                      {product.UpdatedAt ? new Date(product.UpdatedAt).toLocaleDateString() : '-'}\n                    </td>\n                    <td>\n                      <div className=\"action-buttons\">\n                        <button\n                          className=\"admin-btn admin-btn-secondary btn-small\"\n                          onClick={() => handleViewProduct(product.ProductID)}\n                          title=\"View Details\"\n                        >\n                          👁️\n                        </button>\n                        <button\n                          className=\"admin-btn admin-btn-secondary btn-small\"\n                          onClick={() => handleEditProduct(product)}\n                          title=\"Edit Product\"\n                        >\n                          ✏️\n                        </button>\n                        <button\n                          className=\"admin-btn admin-btn-danger btn-small\"\n                          onClick={() => handleDeleteProduct(product)}\n                          title=\"Delete Product\"\n                        >\n                          🗑️\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))\n              )}\n            </tbody>\n          </table>\n        </div>\n\n        {/* Pagination */}\n        {totalPages > 1 && (\n          <div className=\"pagination\">\n            <div className=\"pagination-info\">\n              Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} products\n            </div>\n            <div className=\"pagination-controls\">\n              <button\n                className=\"admin-btn admin-btn-secondary btn-small\"\n                onClick={() => setCurrentPage(1)}\n                disabled={currentPage === 1}\n              >\n                First\n              </button>\n              <button\n                className=\"admin-btn admin-btn-secondary btn-small\"\n                onClick={() => setCurrentPage(currentPage - 1)}\n                disabled={currentPage === 1}\n              >\n                Previous\n              </button>\n              <span className=\"page-info\">\n                Page {currentPage} of {totalPages}\n              </span>\n              <button\n                className=\"admin-btn admin-btn-secondary btn-small\"\n                onClick={() => setCurrentPage(currentPage + 1)}\n                disabled={currentPage === totalPages}\n              >\n                Next\n              </button>\n              <button\n                className=\"admin-btn admin-btn-secondary btn-small\"\n                onClick={() => setCurrentPage(totalPages)}\n                disabled={currentPage === totalPages}\n              >\n                Last\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Modals */}\n      {showProductForm && (\n        <ProductFormModal\n          product={editingProduct}\n          categories={categories}\n          onSave={handleProductSaved}\n          onClose={() => {\n            setShowProductForm(false);\n            setEditingProduct(null);\n          }}\n        />\n      )}\n\n      {showProductDetails && selectedProduct && (\n        <ProductDetailsModal\n          product={selectedProduct}\n          onClose={() => {\n            setShowProductDetails(false);\n            setSelectedProduct(null);\n          }}\n          onEdit={() => {\n            setEditingProduct(selectedProduct.product);\n            setShowProductDetails(false);\n            setShowProductForm(true);\n          }}\n        />\n      )}\n\n      {showDeleteConfirm && selectedProduct && (\n        <ConfirmationModal\n          title=\"Delete Product\"\n          message={`Are you sure you want to delete \"${selectedProduct.ProductName}\"? This action cannot be undone.`}\n          confirmText=\"Delete\"\n          cancelText=\"Cancel\"\n          onConfirm={confirmDeleteProduct}\n          onCancel={() => {\n            setShowDeleteConfirm(false);\n            setSelectedProduct(null);\n          }}\n          type=\"danger\"\n        />\n      )}\n    </div>\n  );\n};\n\nexport default ProductManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,OAAOC,iBAAiB,MAAM,6BAA6B;AAC3D,SAASC,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyB,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,aAAa,CAAC;EACnD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC6C,cAAc,EAAEC,iBAAiB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAM+C,aAAa,GAAG7C,WAAW,CAAC,YAAY;IAC5C,IAAI;MACFgB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM8B,MAAM,GAAG;QACbC,IAAI,EAAEpB,WAAW;QACjBqB,KAAK,EAAEnB,QAAQ;QACfoB,MAAM,EAAEhC,UAAU,IAAIiC,SAAS;QAC/BC,QAAQ,EAAEhC,gBAAgB,IAAI+B,SAAS;QACvCE,MAAM,EAAE/B,cAAc,IAAI6B,SAAS;QACnC3B,MAAM;QACNE;MACF,CAAC;MAED,MAAM4B,QAAQ,GAAG,MAAMhD,WAAW,CAACiD,WAAW,CAACR,MAAM,CAAC;MAEtD,IAAIO,QAAQ,CAACE,OAAO,EAAE;QACpB3C,WAAW,CAACyC,QAAQ,CAACG,IAAI,CAAC;QAC1BxB,aAAa,CAACqB,QAAQ,CAACI,UAAU,CAAC1B,UAAU,CAAC;QAC7CG,aAAa,CAACmB,QAAQ,CAACI,UAAU,CAACxB,UAAU,CAAC;MAC/C,CAAC,MAAM;QACLhC,KAAK,CAACyD,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDzD,KAAK,CAACyD,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACR1C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACW,WAAW,EAAEE,QAAQ,EAAEZ,UAAU,EAAEE,gBAAgB,EAAEE,cAAc,EAAEE,MAAM,EAAEE,aAAa,CAAC,CAAC;;EAEhG;EACA,MAAMmC,eAAe,GAAG5D,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF,MAAMqD,QAAQ,GAAG,MAAMhD,WAAW,CAACwD,aAAa,CAAC,CAAC;MAClD,IAAIR,QAAQ,CAACE,OAAO,EAAE;QACpBzC,aAAa,CAACuC,QAAQ,CAACG,IAAI,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN3D,SAAS,CAAC,MAAM;IACd8C,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB9C,SAAS,CAAC,MAAM;IACd6D,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;;EAErB;EACA7D,SAAS,CAAC,MAAM;IACd;IACAO,gBAAgB,CAACwD,mBAAmB,CAAC,CAAC;;IAEtC;IACA,MAAMC,oBAAoB,GAAIP,IAAI,IAAK;MACrCvD,KAAK,CAACsD,OAAO,CAAC,gBAAgBC,IAAI,CAACQ,WAAW,oBAAoB,CAAC;MACnEnB,aAAa,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IAED,MAAMoB,oBAAoB,GAAIT,IAAI,IAAK;MACrCvD,KAAK,CAACiE,IAAI,CAAC,YAAYV,IAAI,CAACQ,WAAW,oBAAoB,CAAC;MAC5DnB,aAAa,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IAED,MAAMsB,oBAAoB,GAAIX,IAAI,IAAK;MACrCvD,KAAK,CAACmE,OAAO,CAAC,0BAA0B,CAAC;MACzCvB,aAAa,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IAED,MAAMwB,yBAAyB,GAAIb,IAAI,IAAK;MAC1CvD,KAAK,CAACsD,OAAO,CAAC,iCAAiCC,IAAI,CAACc,SAAS,EAAE,CAAC;MAChE;MACAzB,aAAa,CAAC,CAAC;IACjB,CAAC;;IAED;IACAvC,gBAAgB,CAACiE,EAAE,CAAC,gBAAgB,EAAER,oBAAoB,CAAC;IAC3DzD,gBAAgB,CAACiE,EAAE,CAAC,gBAAgB,EAAEN,oBAAoB,CAAC;IAC3D3D,gBAAgB,CAACiE,EAAE,CAAC,gBAAgB,EAAEJ,oBAAoB,CAAC;IAC3D7D,gBAAgB,CAACiE,EAAE,CAAC,qBAAqB,EAAEF,yBAAyB,CAAC;;IAErE;IACA,OAAO,MAAM;MACX/D,gBAAgB,CAACkE,GAAG,CAAC,gBAAgB,EAAET,oBAAoB,CAAC;MAC5DzD,gBAAgB,CAACkE,GAAG,CAAC,gBAAgB,EAAEP,oBAAoB,CAAC;MAC5D3D,gBAAgB,CAACkE,GAAG,CAAC,gBAAgB,EAAEL,oBAAoB,CAAC;MAC5D7D,gBAAgB,CAACkE,GAAG,CAAC,qBAAqB,EAAEH,yBAAyB,CAAC;IACxE,CAAC;EACH,CAAC,EAAE,CAACxB,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM4B,YAAY,GAAGzE,WAAW,CAAE0E,KAAK,IAAK;IAC1CxD,aAAa,CAACwD,KAAK,CAAC;IACpB9C,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM+C,UAAU,GAAIC,KAAK,IAAK;IAC5B,IAAIrD,MAAM,KAAKqD,KAAK,EAAE;MACpBlD,gBAAgB,CAACD,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;IAC5D,CAAC,MAAM;MACLD,SAAS,CAACoD,KAAK,CAAC;MAChBlD,gBAAgB,CAAC,KAAK,CAAC;IACzB;IACAE,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMiD,gBAAgB,GAAGA,CAAA,KAAM;IAC7BjC,iBAAiB,CAAC,IAAI,CAAC;IACvBR,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM0C,iBAAiB,GAAIC,OAAO,IAAK;IACrCnC,iBAAiB,CAACmC,OAAO,CAAC;IAC1B3C,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM4C,iBAAiB,GAAG,MAAOV,SAAS,IAAK;IAC7C,IAAI;MACF,MAAMjB,QAAQ,GAAG,MAAMhD,WAAW,CAAC4E,cAAc,CAACX,SAAS,CAAC;MAC5D,IAAIjB,QAAQ,CAACE,OAAO,EAAE;QACpBb,kBAAkB,CAACW,QAAQ,CAACG,IAAI,CAAC;QACjClB,qBAAqB,CAAC,IAAI,CAAC;MAC7B,CAAC,MAAM;QACLrC,KAAK,CAACyD,KAAK,CAAC,gCAAgC,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDzD,KAAK,CAACyD,KAAK,CAAC,+BAA+B,CAAC;IAC9C;EACF,CAAC;EAED,MAAMwB,mBAAmB,GAAIH,OAAO,IAAK;IACvCrC,kBAAkB,CAACqC,OAAO,CAAC;IAC3BvC,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAM2C,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAM9B,QAAQ,GAAG,MAAMhD,WAAW,CAAC+E,aAAa,CAAC3C,eAAe,CAAC4C,SAAS,CAAC;MAC3E,IAAIhC,QAAQ,CAACE,OAAO,EAAE;QACpBtD,KAAK,CAACsD,OAAO,CAAC,8BAA8B,CAAC;QAC7CV,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACL5C,KAAK,CAACyD,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CzD,KAAK,CAACyD,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACRlB,oBAAoB,CAAC,KAAK,CAAC;MAC3BE,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC;EAED,MAAM4C,kBAAkB,GAAGA,CAAA,KAAM;IAC/BlD,kBAAkB,CAAC,KAAK,CAAC;IACzBQ,iBAAiB,CAAC,IAAI,CAAC;IACvBC,aAAa,CAAC,CAAC;IACf5C,KAAK,CAACsD,OAAO,CAACZ,cAAc,GAAG,8BAA8B,GAAG,8BAA8B,CAAC;EACjG,CAAC;;EAED;EACA,MAAM4C,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,mBAAmB,GAAI1C,MAAM,IAAK;IACtC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,cAAc;QAAE,OAAO,SAAS;MACrC,KAAK,gBAAgB;QAAE,OAAO,SAAS;MACvC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAM2C,WAAW,GAAInB,KAAK,IAAK;IAC7B,IAAIrD,MAAM,KAAKqD,KAAK,EAAE,OAAO,IAAI;IACjC,OAAOnD,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG;EAC5C,CAAC;EAED,IAAIV,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKwF,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BzF,OAAA;QAAKwF,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvC7F,OAAA;QAAAyF,QAAA,EAAG;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAEV;EAEA,oBACE7F,OAAA;IAAKwF,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBAEjCzF,OAAA;MAAKwF,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCzF,OAAA;QAAIwF,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxD7F,OAAA;QACEwF,SAAS,EAAC,6BAA6B;QACvCM,OAAO,EAAEzB,gBAAiB;QAAAoB,QAAA,gBAE1BzF,OAAA;UAAMwF,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,mBAErC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN7F,OAAA;MAAKwF,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzBzF,OAAA;QAAKwF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BzF,OAAA;UAAKwF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzF,OAAA;YAAKwF,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzF,OAAA;cAAO+F,OAAO,EAAC,QAAQ;cAAAN,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/C7F,OAAA;cACEgG,EAAE,EAAC,QAAQ;cACXC,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,yCAAyC;cACrDhC,KAAK,EAAEzD,UAAW;cAClB0F,QAAQ,EAAGC,CAAC,IAAKnC,YAAY,CAACmC,CAAC,CAACC,MAAM,CAACnC,KAAK,CAAE;cAC9CsB,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN7F,OAAA;YAAKwF,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzF,OAAA;cAAO+F,OAAO,EAAC,UAAU;cAAAN,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1C7F,OAAA;cACEgG,EAAE,EAAC,UAAU;cACb9B,KAAK,EAAEvD,gBAAiB;cACxBwF,QAAQ,EAAGC,CAAC,IAAKxF,mBAAmB,CAACwF,CAAC,CAACC,MAAM,CAACnC,KAAK,CAAE;cACrDsB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAExBzF,OAAA;gBAAQkE,KAAK,EAAC,EAAE;gBAAAuB,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACvCxF,UAAU,CAACiG,GAAG,CAAC3D,QAAQ,iBACtB3C,OAAA;gBAAkCkE,KAAK,EAAEvB,QAAQ,CAAC4D,UAAW;gBAAAd,QAAA,EAC1D9C,QAAQ,CAAC6D;cAAY,GADX7D,QAAQ,CAAC4D,UAAU;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAExB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7F,OAAA;YAAKwF,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzF,OAAA;cAAO+F,OAAO,EAAC,QAAQ;cAAAN,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtC7F,OAAA;cACEgG,EAAE,EAAC,QAAQ;cACX9B,KAAK,EAAErD,cAAe;cACtBsF,QAAQ,EAAGC,CAAC,IAAKtF,iBAAiB,CAACsF,CAAC,CAACC,MAAM,CAACnC,KAAK,CAAE;cACnDsB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAExBzF,OAAA;gBAAQkE,KAAK,EAAC,EAAE;gBAAAuB,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtC7F,OAAA;gBAAQkE,KAAK,EAAC,QAAQ;gBAAAuB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtC7F,OAAA;gBAAQkE,KAAK,EAAC,OAAO;gBAAAuB,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC7F,OAAA;gBAAQkE,KAAK,EAAC,UAAU;gBAAAuB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1C7F,OAAA;gBAAQkE,KAAK,EAAC,cAAc;gBAAAuB,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClD7F,OAAA;gBAAQkE,KAAK,EAAC,gBAAgB;gBAAAuB,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7F,OAAA;YAAKwF,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzF,OAAA;cAAO+F,OAAO,EAAC,UAAU;cAAAN,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChD7F,OAAA;cACEgG,EAAE,EAAC,UAAU;cACb9B,KAAK,EAAE7C,QAAS;cAChB8E,QAAQ,EAAGC,CAAC,IAAK;gBACf9E,WAAW,CAACmF,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACnC,KAAK,CAAC,CAAC;gBACrC9C,cAAc,CAAC,CAAC,CAAC;cACnB,CAAE;cACFoE,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAExBzF,OAAA;gBAAQkE,KAAK,EAAE,EAAG;gBAAAuB,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9B7F,OAAA;gBAAQkE,KAAK,EAAE,EAAG;gBAAAuB,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9B7F,OAAA;gBAAQkE,KAAK,EAAE,EAAG;gBAAAuB,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9B7F,OAAA;gBAAQkE,KAAK,EAAE,GAAI;gBAAAuB,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7F,OAAA;MAAKwF,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBzF,OAAA;QAAKwF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BzF,OAAA;UAAOwF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC5BzF,OAAA;YAAAyF,QAAA,eACEzF,OAAA;cAAAyF,QAAA,gBACEzF,OAAA;gBACEwF,SAAS,EAAC,UAAU;gBACpBM,OAAO,EAAEA,CAAA,KAAM3B,UAAU,CAAC,aAAa,CAAE;gBAAAsB,QAAA,GAC1C,eACc,EAACF,WAAW,CAAC,aAAa,CAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACL7F,OAAA;gBAAAyF,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjB7F,OAAA;gBACEwF,SAAS,EAAC,UAAU;gBACpBM,OAAO,EAAEA,CAAA,KAAM3B,UAAU,CAAC,WAAW,CAAE;gBAAAsB,QAAA,GACxC,QACO,EAACF,WAAW,CAAC,WAAW,CAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACL7F,OAAA;gBACEwF,SAAS,EAAC,UAAU;gBACpBM,OAAO,EAAEA,CAAA,KAAM3B,UAAU,CAAC,QAAQ,CAAE;gBAAAsB,QAAA,GACrC,SACQ,EAACF,WAAW,CAAC,QAAQ,CAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACL7F,OAAA;gBAAAyF,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACd7F,OAAA;gBACEwF,SAAS,EAAC,UAAU;gBACpBM,OAAO,EAAEA,CAAA,KAAM3B,UAAU,CAAC,WAAW,CAAE;gBAAAsB,QAAA,GACxC,eACc,EAACF,WAAW,CAAC,WAAW,CAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACL7F,OAAA;gBAAAyF,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR7F,OAAA;YAAAyF,QAAA,EACGtF,QAAQ,CAACuG,MAAM,KAAK,CAAC,gBACpB1G,OAAA;cAAAyF,QAAA,eACEzF,OAAA;gBAAI2G,OAAO,EAAC,GAAG;gBAACnB,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAEpC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GAEL1F,QAAQ,CAACmG,GAAG,CAAC/B,OAAO,iBAClBvE,OAAA;cAAAyF,QAAA,gBACEzF,OAAA;gBAAAyF,QAAA,eACEzF,OAAA;kBAAKwF,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BzF,OAAA;oBAAAyF,QAAA,EAASlB,OAAO,CAACf;kBAAW;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eACtC7F,OAAA;oBAAAyF,QAAA,EAAQlB,OAAO,CAACqC;kBAAW;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL7F,OAAA;gBAAAyF,QAAA,EAAKlB,OAAO,CAACiC;cAAY;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/B7F,OAAA;gBAAAyF,QAAA,EAAKV,cAAc,CAACR,OAAO,CAACsC,SAAS;cAAC;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5C7F,OAAA;gBAAAyF,QAAA,eACEzF,OAAA;kBACEwF,SAAS,EAAC,cAAc;kBACxBL,KAAK,EAAE;oBAAE2B,eAAe,EAAExB,mBAAmB,CAACf,OAAO,CAACwC,MAAM;kBAAE,CAAE;kBAAAtB,QAAA,EAE/DlB,OAAO,CAACwC;gBAAM;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL7F,OAAA;gBAAAyF,QAAA,eACEzF,OAAA;kBAAKwF,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BzF,OAAA;oBAAMwF,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAC,eACxB,EAAClB,OAAO,CAACyC,UAAU,IAAI,CAAC;kBAAA;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACP7F,OAAA;oBAAMwF,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAC,eACxB,EAAClB,OAAO,CAAC0C,UAAU,IAAI,CAAC;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL7F,OAAA;gBAAAyF,QAAA,EACGlB,OAAO,CAAC2C,SAAS,GAAG,IAAIC,IAAI,CAAC5C,OAAO,CAAC2C,SAAS,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;cAAG;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACL7F,OAAA;gBAAAyF,QAAA,eACEzF,OAAA;kBAAKwF,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BzF,OAAA;oBACEwF,SAAS,EAAC,yCAAyC;oBACnDM,OAAO,EAAEA,CAAA,KAAMtB,iBAAiB,CAACD,OAAO,CAACM,SAAS,CAAE;oBACpDwC,KAAK,EAAC,cAAc;oBAAA5B,QAAA,EACrB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT7F,OAAA;oBACEwF,SAAS,EAAC,yCAAyC;oBACnDM,OAAO,EAAEA,CAAA,KAAMxB,iBAAiB,CAACC,OAAO,CAAE;oBAC1C8C,KAAK,EAAC,cAAc;oBAAA5B,QAAA,EACrB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT7F,OAAA;oBACEwF,SAAS,EAAC,sCAAsC;oBAChDM,OAAO,EAAEA,CAAA,KAAMpB,mBAAmB,CAACH,OAAO,CAAE;oBAC5C8C,KAAK,EAAC,gBAAgB;oBAAA5B,QAAA,EACvB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAtDEtB,OAAO,CAACM,SAAS;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuDtB,CACL;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGLpE,UAAU,GAAG,CAAC,iBACbzB,OAAA;QAAKwF,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBzF,OAAA;UAAKwF,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,UACvB,EAAE,CAACtE,WAAW,GAAG,CAAC,IAAIE,QAAQ,GAAI,CAAC,EAAC,MAAI,EAACiG,IAAI,CAACC,GAAG,CAACpG,WAAW,GAAGE,QAAQ,EAAEE,UAAU,CAAC,EAAC,MAAI,EAACA,UAAU,EAAC,WAChH;QAAA;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN7F,OAAA;UAAKwF,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCzF,OAAA;YACEwF,SAAS,EAAC,yCAAyC;YACnDM,OAAO,EAAEA,CAAA,KAAM1E,cAAc,CAAC,CAAC,CAAE;YACjCoG,QAAQ,EAAErG,WAAW,KAAK,CAAE;YAAAsE,QAAA,EAC7B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7F,OAAA;YACEwF,SAAS,EAAC,yCAAyC;YACnDM,OAAO,EAAEA,CAAA,KAAM1E,cAAc,CAACD,WAAW,GAAG,CAAC,CAAE;YAC/CqG,QAAQ,EAAErG,WAAW,KAAK,CAAE;YAAAsE,QAAA,EAC7B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7F,OAAA;YAAMwF,SAAS,EAAC,WAAW;YAAAC,QAAA,GAAC,OACrB,EAACtE,WAAW,EAAC,MAAI,EAACM,UAAU;UAAA;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACP7F,OAAA;YACEwF,SAAS,EAAC,yCAAyC;YACnDM,OAAO,EAAEA,CAAA,KAAM1E,cAAc,CAACD,WAAW,GAAG,CAAC,CAAE;YAC/CqG,QAAQ,EAAErG,WAAW,KAAKM,UAAW;YAAAgE,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7F,OAAA;YACEwF,SAAS,EAAC,yCAAyC;YACnDM,OAAO,EAAEA,CAAA,KAAM1E,cAAc,CAACK,UAAU,CAAE;YAC1C+F,QAAQ,EAAErG,WAAW,KAAKM,UAAW;YAAAgE,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLlE,eAAe,iBACd3B,OAAA,CAACN,gBAAgB;MACf6E,OAAO,EAAEpC,cAAe;MACxB9B,UAAU,EAAEA,UAAW;MACvBoH,MAAM,EAAE3C,kBAAmB;MAC3B4C,OAAO,EAAEA,CAAA,KAAM;QACb9F,kBAAkB,CAAC,KAAK,CAAC;QACzBQ,iBAAiB,CAAC,IAAI,CAAC;MACzB;IAAE;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EAEAhE,kBAAkB,IAAII,eAAe,iBACpCjC,OAAA,CAACL,mBAAmB;MAClB4E,OAAO,EAAEtC,eAAgB;MACzByF,OAAO,EAAEA,CAAA,KAAM;QACb5F,qBAAqB,CAAC,KAAK,CAAC;QAC5BI,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAE;MACFyF,MAAM,EAAEA,CAAA,KAAM;QACZvF,iBAAiB,CAACH,eAAe,CAACsC,OAAO,CAAC;QAC1CzC,qBAAqB,CAAC,KAAK,CAAC;QAC5BF,kBAAkB,CAAC,IAAI,CAAC;MAC1B;IAAE;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EAEA9D,iBAAiB,IAAIE,eAAe,iBACnCjC,OAAA,CAACJ,iBAAiB;MAChByH,KAAK,EAAC,gBAAgB;MACtBO,OAAO,EAAE,oCAAoC3F,eAAe,CAACuB,WAAW,kCAAmC;MAC3GqE,WAAW,EAAC,QAAQ;MACpBC,UAAU,EAAC,QAAQ;MACnBC,SAAS,EAAEpD,oBAAqB;MAChCqD,QAAQ,EAAEA,CAAA,KAAM;QACdhG,oBAAoB,CAAC,KAAK,CAAC;QAC3BE,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAE;MACF+D,IAAI,EAAC;IAAQ;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC3F,EAAA,CAlfID,iBAAiB;AAAAgI,EAAA,GAAjBhI,iBAAiB;AAofvB,eAAeA,iBAAiB;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}