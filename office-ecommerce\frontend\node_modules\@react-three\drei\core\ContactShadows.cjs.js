"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("three"),a=require("@react-three/fiber"),n=require("three-stdlib");function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var a=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,a.get?a:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var i=o(e),l=u(r),s=u(t);const c=l.forwardRef((({scale:e=10,frames:r=1/0,opacity:t=1,width:o=1,height:u=1,blur:c=1,far:d=10,resolution:f=512,smooth:h=!0,color:m="#000000",depthWrite:p=!1,renderOrder:g,...v},b)=>{const M=l.useRef(null),y=a.useThree((e=>e.scene)),w=a.useThree((e=>e.gl)),T=l.useRef(null);o*=Array.isArray(e)?e[0]:e||1,u*=Array.isArray(e)?e[1]:e||1;const[x,O,R,S,j,C,P]=l.useMemo((()=>{const e=new s.WebGLRenderTarget(f,f),r=new s.WebGLRenderTarget(f,f);r.texture.generateMipmaps=e.texture.generateMipmaps=!1;const t=new s.PlaneGeometry(o,u).rotateX(Math.PI/2),a=new s.Mesh(t),i=new s.MeshDepthMaterial;i.depthTest=i.depthWrite=!1,i.onBeforeCompile=e=>{e.uniforms={...e.uniforms,ucolor:{value:new s.Color(m)}},e.fragmentShader=e.fragmentShader.replace("void main() {","uniform vec3 ucolor;\n           void main() {\n          "),e.fragmentShader=e.fragmentShader.replace("vec4( vec3( 1.0 - fragCoordZ ), opacity );","vec4( ucolor * fragCoordZ * 2.0, ( 1.0 - fragCoordZ ) * 1.0 );")};const l=new s.ShaderMaterial(n.HorizontalBlurShader),c=new s.ShaderMaterial(n.VerticalBlurShader);return c.depthTest=l.depthTest=!1,[e,t,i,a,l,c,r]}),[f,o,u,e,m]),q=e=>{S.visible=!0,S.material=j,j.uniforms.tDiffuse.value=x.texture,j.uniforms.h.value=1*e/256,w.setRenderTarget(P),w.render(S,T.current),S.material=C,C.uniforms.tDiffuse.value=P.texture,C.uniforms.v.value=1*e/256,w.setRenderTarget(x),w.render(S,T.current),S.visible=!1};let E,W,k=0;return a.useFrame((()=>{T.current&&(r===1/0||k<r)&&(k++,E=y.background,W=y.overrideMaterial,M.current.visible=!1,y.background=null,y.overrideMaterial=R,w.setRenderTarget(x),w.render(y,T.current),q(c),h&&q(.4*c),w.setRenderTarget(null),M.current.visible=!0,y.overrideMaterial=W,y.background=E)})),l.useImperativeHandle(b,(()=>M.current),[]),l.createElement("group",i.default({"rotation-x":Math.PI/2},v,{ref:M}),l.createElement("mesh",{renderOrder:g,geometry:O,scale:[1,-1,1],rotation:[-Math.PI/2,0,0]},l.createElement("meshBasicMaterial",{transparent:!0,map:x.texture,opacity:t,depthWrite:p})),l.createElement("orthographicCamera",{ref:T,args:[-o/2,o/2,u/2,-u/2,0,d]}))}));exports.ContactShadows=c;
