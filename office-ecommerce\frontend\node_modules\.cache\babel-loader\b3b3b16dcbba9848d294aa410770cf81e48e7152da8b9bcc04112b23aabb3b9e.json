{"ast": null, "code": "import * as React from 'react';\nimport { addEffect, addAfterEffect } from '@react-three/fiber';\nimport StatsImpl from 'stats.js';\nimport { useEffectfulState } from '../helpers/useEffectfulState.js';\nfunction Stats({\n  showPanel = 0,\n  className,\n  parent\n}) {\n  const stats = useEffectfulState(() => new StatsImpl(), []);\n  React.useEffect(() => {\n    if (stats) {\n      const node = parent && parent.current || document.body;\n      stats.showPanel(showPanel);\n      node == null ? void 0 : node.appendChild(stats.dom);\n      if (className) stats.dom.classList.add(...className.split(' ').filter(cls => cls));\n      const begin = addEffect(() => stats.begin());\n      const end = addAfterEffect(() => stats.end());\n      return () => {\n        node == null ? void 0 : node.removeChild(stats.dom);\n        begin();\n        end();\n      };\n    }\n  }, [parent, stats, className, showPanel]);\n  return null;\n}\nexport { Stats };", "map": {"version": 3, "names": ["React", "addEffect", "addAfterEffect", "StatsImpl", "useEffectfulState", "Stats", "showPanel", "className", "parent", "stats", "useEffect", "node", "current", "document", "body", "append<PERSON><PERSON><PERSON>", "dom", "classList", "add", "split", "filter", "cls", "begin", "end", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Stats.js"], "sourcesContent": ["import * as React from 'react';\nimport { addEffect, addAfterEffect } from '@react-three/fiber';\nimport StatsImpl from 'stats.js';\nimport { useEffectfulState } from '../helpers/useEffectfulState.js';\n\nfunction Stats({\n  showPanel = 0,\n  className,\n  parent\n}) {\n  const stats = useEffectfulState(() => new StatsImpl(), []);\n  React.useEffect(() => {\n    if (stats) {\n      const node = parent && parent.current || document.body;\n      stats.showPanel(showPanel);\n      node == null ? void 0 : node.appendChild(stats.dom);\n      if (className) stats.dom.classList.add(...className.split(' ').filter(cls => cls));\n      const begin = addEffect(() => stats.begin());\n      const end = addAfterEffect(() => stats.end());\n      return () => {\n        node == null ? void 0 : node.removeChild(stats.dom);\n        begin();\n        end();\n      };\n    }\n  }, [parent, stats, className, showPanel]);\n  return null;\n}\n\nexport { Stats };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,cAAc,QAAQ,oBAAoB;AAC9D,OAAOC,SAAS,MAAM,UAAU;AAChC,SAASC,iBAAiB,QAAQ,iCAAiC;AAEnE,SAASC,KAAKA,CAAC;EACbC,SAAS,GAAG,CAAC;EACbC,SAAS;EACTC;AACF,CAAC,EAAE;EACD,MAAMC,KAAK,GAAGL,iBAAiB,CAAC,MAAM,IAAID,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAC1DH,KAAK,CAACU,SAAS,CAAC,MAAM;IACpB,IAAID,KAAK,EAAE;MACT,MAAME,IAAI,GAAGH,MAAM,IAAIA,MAAM,CAACI,OAAO,IAAIC,QAAQ,CAACC,IAAI;MACtDL,KAAK,CAACH,SAAS,CAACA,SAAS,CAAC;MAC1BK,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACI,WAAW,CAACN,KAAK,CAACO,GAAG,CAAC;MACnD,IAAIT,SAAS,EAAEE,KAAK,CAACO,GAAG,CAACC,SAAS,CAACC,GAAG,CAAC,GAAGX,SAAS,CAACY,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAAC,CAAC;MAClF,MAAMC,KAAK,GAAGrB,SAAS,CAAC,MAAMQ,KAAK,CAACa,KAAK,CAAC,CAAC,CAAC;MAC5C,MAAMC,GAAG,GAAGrB,cAAc,CAAC,MAAMO,KAAK,CAACc,GAAG,CAAC,CAAC,CAAC;MAC7C,OAAO,MAAM;QACXZ,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACa,WAAW,CAACf,KAAK,CAACO,GAAG,CAAC;QACnDM,KAAK,CAAC,CAAC;QACPC,GAAG,CAAC,CAAC;MACP,CAAC;IACH;EACF,CAAC,EAAE,CAACf,MAAM,EAAEC,KAAK,EAAEF,SAAS,EAAED,SAAS,CAAC,CAAC;EACzC,OAAO,IAAI;AACb;AAEA,SAASD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}