{"ast": null, "code": "import * as THREE from 'three';\nimport * as React from 'react';\nimport { shaderMaterial } from '../core/shaderMaterial.js';\nconst WireframeMaterialShaders = {\n  uniforms: {\n    strokeOpacity: 1,\n    fillOpacity: 0.25,\n    fillMix: 0,\n    thickness: 0.05,\n    colorBackfaces: false,\n    dashInvert: true,\n    dash: false,\n    dashRepeats: 4,\n    dashLength: 0.5,\n    squeeze: false,\n    squeezeMin: 0.2,\n    squeezeMax: 1,\n    stroke: new THREE.Color('#ff0000'),\n    backfaceStroke: new THREE.Color('#0000ff'),\n    fill: new THREE.Color('#00ff00')\n  },\n  vertex: /* glsl */\n  `\n\t  attribute vec3 barycentric;\n\t\n\t\tvarying vec3 v_edges_Barycentric;\n\t\tvarying vec3 v_edges_Position;\n\n\t\tvoid initWireframe() {\n\t\t\tv_edges_Barycentric = barycentric;\n\t\t\tv_edges_Position = position.xyz;\n\t\t}\n\t  `,\n  fragment: /* glsl */\n  `\n\t\t#ifndef PI\n\t  \t#define PI 3.1415926535897932384626433832795\n\t\t#endif\n  \n\t  varying vec3 v_edges_Barycentric;\n\t  varying vec3 v_edges_Position;\n  \n\t  uniform float strokeOpacity;\n\t  uniform float fillOpacity;\n\t  uniform float fillMix;\n\t  uniform float thickness;\n\t  uniform bool colorBackfaces;\n  \n\t  // Dash\n\t  uniform bool dashInvert;\n\t  uniform bool dash;\n\t  uniform bool dashOnly;\n\t  uniform float dashRepeats;\n\t  uniform float dashLength;\n  \n\t  // Squeeze\n\t  uniform bool squeeze;\n\t  uniform float squeezeMin;\n\t  uniform float squeezeMax;\n  \n\t  // Colors\n\t  uniform vec3 stroke;\n\t  uniform vec3 backfaceStroke;\n\t  uniform vec3 fill;\n  \n\t  // This is like\n\t  float wireframe_aastep(float threshold, float dist) {\n\t\t  float afwidth = fwidth(dist) * 0.5;\n\t\t  return smoothstep(threshold - afwidth, threshold + afwidth, dist);\n\t  }\n  \n\t  float wireframe_map(float value, float min1, float max1, float min2, float max2) {\n\t\t  return min2 + (value - min1) * (max2 - min2) / (max1 - min1);\n\t  }\n  \n\t  float getWireframe() {\n\t\t\tvec3 barycentric = v_edges_Barycentric;\n\t\t\n\t\t\t// Distance from center of each triangle to its edges.\n\t\t\tfloat d = min(min(barycentric.x, barycentric.y), barycentric.z);\n\n\t\t\t// for dashed rendering, we can use this to get the 0 .. 1 value of the line length\n\t\t\tfloat positionAlong = max(barycentric.x, barycentric.y);\n\t\t\tif (barycentric.y < barycentric.x && barycentric.y < barycentric.z) {\n\t\t\t\tpositionAlong = 1.0 - positionAlong;\n\t\t\t}\n\n\t\t\t// the thickness of the stroke\n\t\t\tfloat computedThickness = wireframe_map(thickness, 0.0, 1.0, 0.0, 0.34);\n\n\t\t\t// if we want to shrink the thickness toward the center of the line segment\n\t\t\tif (squeeze) {\n\t\t\t\tcomputedThickness *= mix(squeezeMin, squeezeMax, (1.0 - sin(positionAlong * PI)));\n\t\t\t}\n\n\t\t\t// Create dash pattern\n\t\t\tif (dash) {\n\t\t\t\t// here we offset the stroke position depending on whether it\n\t\t\t\t// should overlap or not\n\t\t\t\tfloat offset = 1.0 / dashRepeats * dashLength / 2.0;\n\t\t\t\tif (!dashInvert) {\n\t\t\t\t\toffset += 1.0 / dashRepeats / 2.0;\n\t\t\t\t}\n\n\t\t\t\t// if we should animate the dash or not\n\t\t\t\t// if (dashAnimate) {\n\t\t\t\t// \toffset += time * 0.22;\n\t\t\t\t// }\n\n\t\t\t\t// create the repeating dash pattern\n\t\t\t\tfloat pattern = fract((positionAlong + offset) * dashRepeats);\n\t\t\t\tcomputedThickness *= 1.0 - wireframe_aastep(dashLength, pattern);\n\t\t\t}\n\n\t\t\t// compute the anti-aliased stroke edge  \n\t\t\tfloat edge = 1.0 - wireframe_aastep(computedThickness, d);\n\n\t\t\treturn edge;\n\t  }\n\t  `\n};\nconst WireframeMaterial = shaderMaterial(WireframeMaterialShaders.uniforms, WireframeMaterialShaders.vertex + /* glsl */\n`\n  \tvoid main() {\n\t\tinitWireframe();\n\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n\t}\n  `, WireframeMaterialShaders.fragment + /* glsl */\n`\n  void main () {\n\t\t// Compute color\n\n\t\tfloat edge = getWireframe();\n\t\tvec4 colorStroke = vec4(stroke, edge);\n\n\t\t#ifdef FLIP_SIDED\n\t\t\tcolorStroke.rgb = backfaceStroke;\n\t\t#endif\n    \n\t\tvec4 colorFill = vec4(fill, fillOpacity);\n\t\tvec4 outColor = mix(colorFill, colorStroke, edge * strokeOpacity);\n\n\t\tgl_FragColor = outColor;\n\t}\n  `);\nfunction setWireframeOverride(material, uniforms) {\n  material.onBeforeCompile = shader => {\n    shader.uniforms = {\n      ...shader.uniforms,\n      ...uniforms\n    };\n    shader.vertexShader = shader.vertexShader.replace('void main() {', `\n\t\t  ${WireframeMaterialShaders.vertex}\n\t\t  void main() {\n\t\t\tinitWireframe();\n\t\t`);\n    shader.fragmentShader = shader.fragmentShader.replace('void main() {', `\n\t\t  ${WireframeMaterialShaders.fragment}\n\t\t  void main() {\n\t\t`);\n    shader.fragmentShader = shader.fragmentShader.replace('#include <color_fragment>', /* glsl */\n    `\n\t\t  #include <color_fragment>\n\t\t\t  float edge = getWireframe();\n\t\t  vec4 colorStroke = vec4(stroke, edge);\n\t\t  #ifdef FLIP_SIDED\n\t\t\tcolorStroke.rgb = backfaceStroke;\n\t\t  #endif\n\t\t  vec4 colorFill = vec4(mix(diffuseColor.rgb, fill, fillMix), mix(diffuseColor.a, fillOpacity, fillMix));\n\t\t  vec4 outColor = mix(colorFill, colorStroke, edge * strokeOpacity);\n\n\t\t  diffuseColor.rgb = outColor.rgb;\n\t\t  diffuseColor.a *= outColor.a;\n\t\t`);\n  };\n  material.side = THREE.DoubleSide;\n  material.transparent = true;\n}\nfunction useWireframeUniforms(uniforms, props) {\n  React.useEffect(() => {\n    var _props$fillOpacity;\n    return void (uniforms.fillOpacity.value = (_props$fillOpacity = props.fillOpacity) !== null && _props$fillOpacity !== void 0 ? _props$fillOpacity : uniforms.fillOpacity.value);\n  }, [props.fillOpacity]);\n  React.useEffect(() => {\n    var _props$fillMix;\n    return void (uniforms.fillMix.value = (_props$fillMix = props.fillMix) !== null && _props$fillMix !== void 0 ? _props$fillMix : uniforms.fillMix.value);\n  }, [props.fillMix]);\n  React.useEffect(() => {\n    var _props$strokeOpacity;\n    return void (uniforms.strokeOpacity.value = (_props$strokeOpacity = props.strokeOpacity) !== null && _props$strokeOpacity !== void 0 ? _props$strokeOpacity : uniforms.strokeOpacity.value);\n  }, [props.strokeOpacity]);\n  React.useEffect(() => {\n    var _props$thickness;\n    return void (uniforms.thickness.value = (_props$thickness = props.thickness) !== null && _props$thickness !== void 0 ? _props$thickness : uniforms.thickness.value);\n  }, [props.thickness]);\n  React.useEffect(() => void (uniforms.colorBackfaces.value = !!props.colorBackfaces), [props.colorBackfaces]);\n  React.useEffect(() => void (uniforms.dash.value = !!props.dash), [props.dash]);\n  React.useEffect(() => void (uniforms.dashInvert.value = !!props.dashInvert), [props.dashInvert]);\n  React.useEffect(() => {\n    var _props$dashRepeats;\n    return void (uniforms.dashRepeats.value = (_props$dashRepeats = props.dashRepeats) !== null && _props$dashRepeats !== void 0 ? _props$dashRepeats : uniforms.dashRepeats.value);\n  }, [props.dashRepeats]);\n  React.useEffect(() => {\n    var _props$dashLength;\n    return void (uniforms.dashLength.value = (_props$dashLength = props.dashLength) !== null && _props$dashLength !== void 0 ? _props$dashLength : uniforms.dashLength.value);\n  }, [props.dashLength]);\n  React.useEffect(() => void (uniforms.squeeze.value = !!props.squeeze), [props.squeeze]);\n  React.useEffect(() => {\n    var _props$squeezeMin;\n    return void (uniforms.squeezeMin.value = (_props$squeezeMin = props.squeezeMin) !== null && _props$squeezeMin !== void 0 ? _props$squeezeMin : uniforms.squeezeMin.value);\n  }, [props.squeezeMin]);\n  React.useEffect(() => {\n    var _props$squeezeMax;\n    return void (uniforms.squeezeMax.value = (_props$squeezeMax = props.squeezeMax) !== null && _props$squeezeMax !== void 0 ? _props$squeezeMax : uniforms.squeezeMax.value);\n  }, [props.squeezeMax]);\n  React.useEffect(() => void (uniforms.stroke.value = props.stroke ? new THREE.Color(props.stroke) : uniforms.stroke.value), [props.stroke]);\n  React.useEffect(() => void (uniforms.fill.value = props.fill ? new THREE.Color(props.fill) : uniforms.fill.value), [props.fill]);\n  React.useEffect(() => void (uniforms.backfaceStroke.value = props.backfaceStroke ? new THREE.Color(props.backfaceStroke) : uniforms.backfaceStroke.value), [props.backfaceStroke]);\n}\nexport { WireframeMaterial, WireframeMaterialShaders, setWireframeOverride, useWireframeUniforms };", "map": {"version": 3, "names": ["THREE", "React", "shaderMaterial", "WireframeMaterialShaders", "uniforms", "strokeOpacity", "fillOpacity", "fillMix", "thickness", "colorBackfaces", "dashInvert", "dash", "dashRepeats", "<PERSON><PERSON><PERSON><PERSON>", "squeeze", "squeezeMin", "squeezeMax", "stroke", "Color", "backfaceStroke", "fill", "vertex", "fragment", "WireframeMaterial", "setWireframeOverride", "material", "onBeforeCompile", "shader", "vertexShader", "replace", "fragmentShader", "side", "DoubleSide", "transparent", "useWireframeUniforms", "props", "useEffect", "_props$fillOpacity", "value", "_props$fillMix", "_props$strokeOpacity", "_props$thickness", "_props$dashRepeats", "_props$dashLength", "_props$squeezeMin", "_props$squeezeMax"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/materials/WireframeMaterial.js"], "sourcesContent": ["import * as THREE from 'three';\nimport * as React from 'react';\nimport { shaderMaterial } from '../core/shaderMaterial.js';\n\nconst WireframeMaterialShaders = {\n  uniforms: {\n    strokeOpacity: 1,\n    fillOpacity: 0.25,\n    fillMix: 0,\n    thickness: 0.05,\n    colorBackfaces: false,\n    dashInvert: true,\n    dash: false,\n    dashRepeats: 4,\n    dashLength: 0.5,\n    squeeze: false,\n    squeezeMin: 0.2,\n    squeezeMax: 1,\n    stroke: new THREE.Color('#ff0000'),\n    backfaceStroke: new THREE.Color('#0000ff'),\n    fill: new THREE.Color('#00ff00')\n  },\n  vertex:\n  /* glsl */\n  `\n\t  attribute vec3 barycentric;\n\t\n\t\tvarying vec3 v_edges_Barycentric;\n\t\tvarying vec3 v_edges_Position;\n\n\t\tvoid initWireframe() {\n\t\t\tv_edges_Barycentric = barycentric;\n\t\t\tv_edges_Position = position.xyz;\n\t\t}\n\t  `,\n  fragment:\n  /* glsl */\n  `\n\t\t#ifndef PI\n\t  \t#define PI 3.1415926535897932384626433832795\n\t\t#endif\n  \n\t  varying vec3 v_edges_Barycentric;\n\t  varying vec3 v_edges_Position;\n  \n\t  uniform float strokeOpacity;\n\t  uniform float fillOpacity;\n\t  uniform float fillMix;\n\t  uniform float thickness;\n\t  uniform bool colorBackfaces;\n  \n\t  // Dash\n\t  uniform bool dashInvert;\n\t  uniform bool dash;\n\t  uniform bool dashOnly;\n\t  uniform float dashRepeats;\n\t  uniform float dashLength;\n  \n\t  // Squeeze\n\t  uniform bool squeeze;\n\t  uniform float squeezeMin;\n\t  uniform float squeezeMax;\n  \n\t  // Colors\n\t  uniform vec3 stroke;\n\t  uniform vec3 backfaceStroke;\n\t  uniform vec3 fill;\n  \n\t  // This is like\n\t  float wireframe_aastep(float threshold, float dist) {\n\t\t  float afwidth = fwidth(dist) * 0.5;\n\t\t  return smoothstep(threshold - afwidth, threshold + afwidth, dist);\n\t  }\n  \n\t  float wireframe_map(float value, float min1, float max1, float min2, float max2) {\n\t\t  return min2 + (value - min1) * (max2 - min2) / (max1 - min1);\n\t  }\n  \n\t  float getWireframe() {\n\t\t\tvec3 barycentric = v_edges_Barycentric;\n\t\t\n\t\t\t// Distance from center of each triangle to its edges.\n\t\t\tfloat d = min(min(barycentric.x, barycentric.y), barycentric.z);\n\n\t\t\t// for dashed rendering, we can use this to get the 0 .. 1 value of the line length\n\t\t\tfloat positionAlong = max(barycentric.x, barycentric.y);\n\t\t\tif (barycentric.y < barycentric.x && barycentric.y < barycentric.z) {\n\t\t\t\tpositionAlong = 1.0 - positionAlong;\n\t\t\t}\n\n\t\t\t// the thickness of the stroke\n\t\t\tfloat computedThickness = wireframe_map(thickness, 0.0, 1.0, 0.0, 0.34);\n\n\t\t\t// if we want to shrink the thickness toward the center of the line segment\n\t\t\tif (squeeze) {\n\t\t\t\tcomputedThickness *= mix(squeezeMin, squeezeMax, (1.0 - sin(positionAlong * PI)));\n\t\t\t}\n\n\t\t\t// Create dash pattern\n\t\t\tif (dash) {\n\t\t\t\t// here we offset the stroke position depending on whether it\n\t\t\t\t// should overlap or not\n\t\t\t\tfloat offset = 1.0 / dashRepeats * dashLength / 2.0;\n\t\t\t\tif (!dashInvert) {\n\t\t\t\t\toffset += 1.0 / dashRepeats / 2.0;\n\t\t\t\t}\n\n\t\t\t\t// if we should animate the dash or not\n\t\t\t\t// if (dashAnimate) {\n\t\t\t\t// \toffset += time * 0.22;\n\t\t\t\t// }\n\n\t\t\t\t// create the repeating dash pattern\n\t\t\t\tfloat pattern = fract((positionAlong + offset) * dashRepeats);\n\t\t\t\tcomputedThickness *= 1.0 - wireframe_aastep(dashLength, pattern);\n\t\t\t}\n\n\t\t\t// compute the anti-aliased stroke edge  \n\t\t\tfloat edge = 1.0 - wireframe_aastep(computedThickness, d);\n\n\t\t\treturn edge;\n\t  }\n\t  `\n};\nconst WireframeMaterial = shaderMaterial(WireframeMaterialShaders.uniforms, WireframeMaterialShaders.vertex +\n/* glsl */\n`\n  \tvoid main() {\n\t\tinitWireframe();\n\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n\t}\n  `, WireframeMaterialShaders.fragment +\n/* glsl */\n`\n  void main () {\n\t\t// Compute color\n\n\t\tfloat edge = getWireframe();\n\t\tvec4 colorStroke = vec4(stroke, edge);\n\n\t\t#ifdef FLIP_SIDED\n\t\t\tcolorStroke.rgb = backfaceStroke;\n\t\t#endif\n    \n\t\tvec4 colorFill = vec4(fill, fillOpacity);\n\t\tvec4 outColor = mix(colorFill, colorStroke, edge * strokeOpacity);\n\n\t\tgl_FragColor = outColor;\n\t}\n  `);\nfunction setWireframeOverride(material, uniforms) {\n  material.onBeforeCompile = shader => {\n    shader.uniforms = { ...shader.uniforms,\n      ...uniforms\n    };\n    shader.vertexShader = shader.vertexShader.replace('void main() {', `\n\t\t  ${WireframeMaterialShaders.vertex}\n\t\t  void main() {\n\t\t\tinitWireframe();\n\t\t`);\n    shader.fragmentShader = shader.fragmentShader.replace('void main() {', `\n\t\t  ${WireframeMaterialShaders.fragment}\n\t\t  void main() {\n\t\t`);\n    shader.fragmentShader = shader.fragmentShader.replace('#include <color_fragment>',\n    /* glsl */\n    `\n\t\t  #include <color_fragment>\n\t\t\t  float edge = getWireframe();\n\t\t  vec4 colorStroke = vec4(stroke, edge);\n\t\t  #ifdef FLIP_SIDED\n\t\t\tcolorStroke.rgb = backfaceStroke;\n\t\t  #endif\n\t\t  vec4 colorFill = vec4(mix(diffuseColor.rgb, fill, fillMix), mix(diffuseColor.a, fillOpacity, fillMix));\n\t\t  vec4 outColor = mix(colorFill, colorStroke, edge * strokeOpacity);\n\n\t\t  diffuseColor.rgb = outColor.rgb;\n\t\t  diffuseColor.a *= outColor.a;\n\t\t`);\n  };\n\n  material.side = THREE.DoubleSide;\n  material.transparent = true;\n}\nfunction useWireframeUniforms(uniforms, props) {\n  React.useEffect(() => {\n    var _props$fillOpacity;\n\n    return void (uniforms.fillOpacity.value = (_props$fillOpacity = props.fillOpacity) !== null && _props$fillOpacity !== void 0 ? _props$fillOpacity : uniforms.fillOpacity.value);\n  }, [props.fillOpacity]);\n  React.useEffect(() => {\n    var _props$fillMix;\n\n    return void (uniforms.fillMix.value = (_props$fillMix = props.fillMix) !== null && _props$fillMix !== void 0 ? _props$fillMix : uniforms.fillMix.value);\n  }, [props.fillMix]);\n  React.useEffect(() => {\n    var _props$strokeOpacity;\n\n    return void (uniforms.strokeOpacity.value = (_props$strokeOpacity = props.strokeOpacity) !== null && _props$strokeOpacity !== void 0 ? _props$strokeOpacity : uniforms.strokeOpacity.value);\n  }, [props.strokeOpacity]);\n  React.useEffect(() => {\n    var _props$thickness;\n\n    return void (uniforms.thickness.value = (_props$thickness = props.thickness) !== null && _props$thickness !== void 0 ? _props$thickness : uniforms.thickness.value);\n  }, [props.thickness]);\n  React.useEffect(() => void (uniforms.colorBackfaces.value = !!props.colorBackfaces), [props.colorBackfaces]);\n  React.useEffect(() => void (uniforms.dash.value = !!props.dash), [props.dash]);\n  React.useEffect(() => void (uniforms.dashInvert.value = !!props.dashInvert), [props.dashInvert]);\n  React.useEffect(() => {\n    var _props$dashRepeats;\n\n    return void (uniforms.dashRepeats.value = (_props$dashRepeats = props.dashRepeats) !== null && _props$dashRepeats !== void 0 ? _props$dashRepeats : uniforms.dashRepeats.value);\n  }, [props.dashRepeats]);\n  React.useEffect(() => {\n    var _props$dashLength;\n\n    return void (uniforms.dashLength.value = (_props$dashLength = props.dashLength) !== null && _props$dashLength !== void 0 ? _props$dashLength : uniforms.dashLength.value);\n  }, [props.dashLength]);\n  React.useEffect(() => void (uniforms.squeeze.value = !!props.squeeze), [props.squeeze]);\n  React.useEffect(() => {\n    var _props$squeezeMin;\n\n    return void (uniforms.squeezeMin.value = (_props$squeezeMin = props.squeezeMin) !== null && _props$squeezeMin !== void 0 ? _props$squeezeMin : uniforms.squeezeMin.value);\n  }, [props.squeezeMin]);\n  React.useEffect(() => {\n    var _props$squeezeMax;\n\n    return void (uniforms.squeezeMax.value = (_props$squeezeMax = props.squeezeMax) !== null && _props$squeezeMax !== void 0 ? _props$squeezeMax : uniforms.squeezeMax.value);\n  }, [props.squeezeMax]);\n  React.useEffect(() => void (uniforms.stroke.value = props.stroke ? new THREE.Color(props.stroke) : uniforms.stroke.value), [props.stroke]);\n  React.useEffect(() => void (uniforms.fill.value = props.fill ? new THREE.Color(props.fill) : uniforms.fill.value), [props.fill]);\n  React.useEffect(() => void (uniforms.backfaceStroke.value = props.backfaceStroke ? new THREE.Color(props.backfaceStroke) : uniforms.backfaceStroke.value), [props.backfaceStroke]);\n}\n\nexport { WireframeMaterial, WireframeMaterialShaders, setWireframeOverride, useWireframeUniforms };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,2BAA2B;AAE1D,MAAMC,wBAAwB,GAAG;EAC/BC,QAAQ,EAAE;IACRC,aAAa,EAAE,CAAC;IAChBC,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,IAAI;IACfC,cAAc,EAAE,KAAK;IACrBC,UAAU,EAAE,IAAI;IAChBC,IAAI,EAAE,KAAK;IACXC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,GAAG;IACfC,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,GAAG;IACfC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAE,IAAIjB,KAAK,CAACkB,KAAK,CAAC,SAAS,CAAC;IAClCC,cAAc,EAAE,IAAInB,KAAK,CAACkB,KAAK,CAAC,SAAS,CAAC;IAC1CE,IAAI,EAAE,IAAIpB,KAAK,CAACkB,KAAK,CAAC,SAAS;EACjC,CAAC;EACDG,MAAM,EACN;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;EACFC,QAAQ,EACR;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,MAAMC,iBAAiB,GAAGrB,cAAc,CAACC,wBAAwB,CAACC,QAAQ,EAAED,wBAAwB,CAACkB,MAAM,GAC3G;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,EAAElB,wBAAwB,CAACmB,QAAQ,GACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,CAAC;AACJ,SAASE,oBAAoBA,CAACC,QAAQ,EAAErB,QAAQ,EAAE;EAChDqB,QAAQ,CAACC,eAAe,GAAGC,MAAM,IAAI;IACnCA,MAAM,CAACvB,QAAQ,GAAG;MAAE,GAAGuB,MAAM,CAACvB,QAAQ;MACpC,GAAGA;IACL,CAAC;IACDuB,MAAM,CAACC,YAAY,GAAGD,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,eAAe,EAAE;AACvE,MAAM1B,wBAAwB,CAACkB,MAAM;AACrC;AACA;AACA,GAAG,CAAC;IACAM,MAAM,CAACG,cAAc,GAAGH,MAAM,CAACG,cAAc,CAACD,OAAO,CAAC,eAAe,EAAE;AAC3E,MAAM1B,wBAAwB,CAACmB,QAAQ;AACvC;AACA,GAAG,CAAC;IACAK,MAAM,CAACG,cAAc,GAAGH,MAAM,CAACG,cAAc,CAACD,OAAO,CAAC,2BAA2B,EACjF;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,CAAC;EACF,CAAC;EAEDJ,QAAQ,CAACM,IAAI,GAAG/B,KAAK,CAACgC,UAAU;EAChCP,QAAQ,CAACQ,WAAW,GAAG,IAAI;AAC7B;AACA,SAASC,oBAAoBA,CAAC9B,QAAQ,EAAE+B,KAAK,EAAE;EAC7ClC,KAAK,CAACmC,SAAS,CAAC,MAAM;IACpB,IAAIC,kBAAkB;IAEtB,OAAO,MAAMjC,QAAQ,CAACE,WAAW,CAACgC,KAAK,GAAG,CAACD,kBAAkB,GAAGF,KAAK,CAAC7B,WAAW,MAAM,IAAI,IAAI+B,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAGjC,QAAQ,CAACE,WAAW,CAACgC,KAAK,CAAC;EACjL,CAAC,EAAE,CAACH,KAAK,CAAC7B,WAAW,CAAC,CAAC;EACvBL,KAAK,CAACmC,SAAS,CAAC,MAAM;IACpB,IAAIG,cAAc;IAElB,OAAO,MAAMnC,QAAQ,CAACG,OAAO,CAAC+B,KAAK,GAAG,CAACC,cAAc,GAAGJ,KAAK,CAAC5B,OAAO,MAAM,IAAI,IAAIgC,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGnC,QAAQ,CAACG,OAAO,CAAC+B,KAAK,CAAC;EACzJ,CAAC,EAAE,CAACH,KAAK,CAAC5B,OAAO,CAAC,CAAC;EACnBN,KAAK,CAACmC,SAAS,CAAC,MAAM;IACpB,IAAII,oBAAoB;IAExB,OAAO,MAAMpC,QAAQ,CAACC,aAAa,CAACiC,KAAK,GAAG,CAACE,oBAAoB,GAAGL,KAAK,CAAC9B,aAAa,MAAM,IAAI,IAAImC,oBAAoB,KAAK,KAAK,CAAC,GAAGA,oBAAoB,GAAGpC,QAAQ,CAACC,aAAa,CAACiC,KAAK,CAAC;EAC7L,CAAC,EAAE,CAACH,KAAK,CAAC9B,aAAa,CAAC,CAAC;EACzBJ,KAAK,CAACmC,SAAS,CAAC,MAAM;IACpB,IAAIK,gBAAgB;IAEpB,OAAO,MAAMrC,QAAQ,CAACI,SAAS,CAAC8B,KAAK,GAAG,CAACG,gBAAgB,GAAGN,KAAK,CAAC3B,SAAS,MAAM,IAAI,IAAIiC,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGrC,QAAQ,CAACI,SAAS,CAAC8B,KAAK,CAAC;EACrK,CAAC,EAAE,CAACH,KAAK,CAAC3B,SAAS,CAAC,CAAC;EACrBP,KAAK,CAACmC,SAAS,CAAC,MAAM,MAAMhC,QAAQ,CAACK,cAAc,CAAC6B,KAAK,GAAG,CAAC,CAACH,KAAK,CAAC1B,cAAc,CAAC,EAAE,CAAC0B,KAAK,CAAC1B,cAAc,CAAC,CAAC;EAC5GR,KAAK,CAACmC,SAAS,CAAC,MAAM,MAAMhC,QAAQ,CAACO,IAAI,CAAC2B,KAAK,GAAG,CAAC,CAACH,KAAK,CAACxB,IAAI,CAAC,EAAE,CAACwB,KAAK,CAACxB,IAAI,CAAC,CAAC;EAC9EV,KAAK,CAACmC,SAAS,CAAC,MAAM,MAAMhC,QAAQ,CAACM,UAAU,CAAC4B,KAAK,GAAG,CAAC,CAACH,KAAK,CAACzB,UAAU,CAAC,EAAE,CAACyB,KAAK,CAACzB,UAAU,CAAC,CAAC;EAChGT,KAAK,CAACmC,SAAS,CAAC,MAAM;IACpB,IAAIM,kBAAkB;IAEtB,OAAO,MAAMtC,QAAQ,CAACQ,WAAW,CAAC0B,KAAK,GAAG,CAACI,kBAAkB,GAAGP,KAAK,CAACvB,WAAW,MAAM,IAAI,IAAI8B,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAGtC,QAAQ,CAACQ,WAAW,CAAC0B,KAAK,CAAC;EACjL,CAAC,EAAE,CAACH,KAAK,CAACvB,WAAW,CAAC,CAAC;EACvBX,KAAK,CAACmC,SAAS,CAAC,MAAM;IACpB,IAAIO,iBAAiB;IAErB,OAAO,MAAMvC,QAAQ,CAACS,UAAU,CAACyB,KAAK,GAAG,CAACK,iBAAiB,GAAGR,KAAK,CAACtB,UAAU,MAAM,IAAI,IAAI8B,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAGvC,QAAQ,CAACS,UAAU,CAACyB,KAAK,CAAC;EAC3K,CAAC,EAAE,CAACH,KAAK,CAACtB,UAAU,CAAC,CAAC;EACtBZ,KAAK,CAACmC,SAAS,CAAC,MAAM,MAAMhC,QAAQ,CAACU,OAAO,CAACwB,KAAK,GAAG,CAAC,CAACH,KAAK,CAACrB,OAAO,CAAC,EAAE,CAACqB,KAAK,CAACrB,OAAO,CAAC,CAAC;EACvFb,KAAK,CAACmC,SAAS,CAAC,MAAM;IACpB,IAAIQ,iBAAiB;IAErB,OAAO,MAAMxC,QAAQ,CAACW,UAAU,CAACuB,KAAK,GAAG,CAACM,iBAAiB,GAAGT,KAAK,CAACpB,UAAU,MAAM,IAAI,IAAI6B,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAGxC,QAAQ,CAACW,UAAU,CAACuB,KAAK,CAAC;EAC3K,CAAC,EAAE,CAACH,KAAK,CAACpB,UAAU,CAAC,CAAC;EACtBd,KAAK,CAACmC,SAAS,CAAC,MAAM;IACpB,IAAIS,iBAAiB;IAErB,OAAO,MAAMzC,QAAQ,CAACY,UAAU,CAACsB,KAAK,GAAG,CAACO,iBAAiB,GAAGV,KAAK,CAACnB,UAAU,MAAM,IAAI,IAAI6B,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAGzC,QAAQ,CAACY,UAAU,CAACsB,KAAK,CAAC;EAC3K,CAAC,EAAE,CAACH,KAAK,CAACnB,UAAU,CAAC,CAAC;EACtBf,KAAK,CAACmC,SAAS,CAAC,MAAM,MAAMhC,QAAQ,CAACa,MAAM,CAACqB,KAAK,GAAGH,KAAK,CAAClB,MAAM,GAAG,IAAIjB,KAAK,CAACkB,KAAK,CAACiB,KAAK,CAAClB,MAAM,CAAC,GAAGb,QAAQ,CAACa,MAAM,CAACqB,KAAK,CAAC,EAAE,CAACH,KAAK,CAAClB,MAAM,CAAC,CAAC;EAC1IhB,KAAK,CAACmC,SAAS,CAAC,MAAM,MAAMhC,QAAQ,CAACgB,IAAI,CAACkB,KAAK,GAAGH,KAAK,CAACf,IAAI,GAAG,IAAIpB,KAAK,CAACkB,KAAK,CAACiB,KAAK,CAACf,IAAI,CAAC,GAAGhB,QAAQ,CAACgB,IAAI,CAACkB,KAAK,CAAC,EAAE,CAACH,KAAK,CAACf,IAAI,CAAC,CAAC;EAChInB,KAAK,CAACmC,SAAS,CAAC,MAAM,MAAMhC,QAAQ,CAACe,cAAc,CAACmB,KAAK,GAAGH,KAAK,CAAChB,cAAc,GAAG,IAAInB,KAAK,CAACkB,KAAK,CAACiB,KAAK,CAAChB,cAAc,CAAC,GAAGf,QAAQ,CAACe,cAAc,CAACmB,KAAK,CAAC,EAAE,CAACH,KAAK,CAAChB,cAAc,CAAC,CAAC;AACpL;AAEA,SAASI,iBAAiB,EAAEpB,wBAAwB,EAAEqB,oBAAoB,EAAEU,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}