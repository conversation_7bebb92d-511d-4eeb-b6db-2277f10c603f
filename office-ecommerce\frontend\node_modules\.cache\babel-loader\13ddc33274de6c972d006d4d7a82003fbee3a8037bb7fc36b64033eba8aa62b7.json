{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useLoader } from '@react-three/fiber';\nimport * as React from 'react';\nimport { forwardRef, useMemo, useEffect, Fragment } from 'react';\nimport { DoubleSide } from 'three';\nimport { SVGLoader } from 'three-stdlib';\nconst Svg = /*#__PURE__*/forwardRef(function R3FSvg({\n  src,\n  skipFill,\n  skipStrokes,\n  fillMaterial,\n  strokeMaterial,\n  fillMeshProps,\n  strokeMeshProps,\n  ...props\n}, ref) {\n  const svg = useLoader(SVGLoader, !src.startsWith('<svg') ? src : `data:image/svg+xml;utf8,${src}`);\n  const strokeGeometries = useMemo(() => skipStrokes ? [] : svg.paths.map(path => {\n    var _path$userData;\n    return ((_path$userData = path.userData) == null ? void 0 : _path$userData.style.stroke) === undefined || path.userData.style.stroke === 'none' ? null : path.subPaths.map(subPath => SVGLoader.pointsToStroke(subPath.getPoints(), path.userData.style));\n  }), [svg, skipStrokes]);\n  useEffect(() => {\n    return () => strokeGeometries.forEach(group => group && group.map(g => g.dispose()));\n  }, [strokeGeometries]);\n  return /*#__PURE__*/React.createElement(\"object3D\", _extends({\n    ref: ref\n  }, props), /*#__PURE__*/React.createElement(\"object3D\", {\n    scale: [1, -1, 1]\n  }, svg.paths.map((path, p) => {\n    var _path$userData2, _path$userData3;\n    return /*#__PURE__*/React.createElement(Fragment, {\n      key: p\n    }, !skipFill && ((_path$userData2 = path.userData) == null ? void 0 : _path$userData2.style.fill) !== undefined && path.userData.style.fill !== 'none' && SVGLoader.createShapes(path).map((shape, s) => /*#__PURE__*/React.createElement(\"mesh\", _extends({\n      key: s\n    }, fillMeshProps), /*#__PURE__*/React.createElement(\"shapeGeometry\", {\n      args: [shape]\n    }), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", _extends({\n      color: path.userData.style.fill,\n      opacity: path.userData.style.fillOpacity,\n      transparent: true,\n      side: DoubleSide,\n      depthWrite: false\n    }, fillMaterial)))), !skipStrokes && ((_path$userData3 = path.userData) == null ? void 0 : _path$userData3.style.stroke) !== undefined && path.userData.style.stroke !== 'none' && path.subPaths.map((_subPath, s) => /*#__PURE__*/React.createElement(\"mesh\", _extends({\n      key: s,\n      geometry: strokeGeometries[p][s]\n    }, strokeMeshProps), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", _extends({\n      color: path.userData.style.stroke,\n      opacity: path.userData.style.strokeOpacity,\n      transparent: true,\n      side: DoubleSide,\n      depthWrite: false\n    }, strokeMaterial)))));\n  })));\n});\nexport { Svg };", "map": {"version": 3, "names": ["_extends", "useLoader", "React", "forwardRef", "useMemo", "useEffect", "Fragment", "DoubleSide", "SVGLoader", "Svg", "R3FSvg", "src", "skip<PERSON>ill", "skipStrokes", "fillMaterial", "strokeMaterial", "fillMeshProps", "strokeMeshProps", "props", "ref", "svg", "startsWith", "strokeGeometries", "paths", "map", "path", "_path$userData", "userData", "style", "stroke", "undefined", "subPaths", "subPath", "pointsToStroke", "getPoints", "for<PERSON>ach", "group", "g", "dispose", "createElement", "scale", "p", "_path$userData2", "_path$userData3", "key", "fill", "createShapes", "shape", "s", "args", "color", "opacity", "fillOpacity", "transparent", "side", "depthWrite", "_subPath", "geometry", "strokeOpacity"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Svg.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useLoader } from '@react-three/fiber';\nimport * as React from 'react';\nimport { forwardRef, useMemo, useEffect, Fragment } from 'react';\nimport { DoubleSide } from 'three';\nimport { SVGLoader } from 'three-stdlib';\n\nconst Svg = /*#__PURE__*/forwardRef(function R3FSvg({\n  src,\n  skipFill,\n  skipStrokes,\n  fillMaterial,\n  strokeMaterial,\n  fillMeshProps,\n  strokeMeshProps,\n  ...props\n}, ref) {\n  const svg = useLoader(SVGLoader, !src.startsWith('<svg') ? src : `data:image/svg+xml;utf8,${src}`);\n  const strokeGeometries = useMemo(() => skipStrokes ? [] : svg.paths.map(path => {\n    var _path$userData;\n\n    return ((_path$userData = path.userData) == null ? void 0 : _path$userData.style.stroke) === undefined || path.userData.style.stroke === 'none' ? null : path.subPaths.map(subPath => SVGLoader.pointsToStroke(subPath.getPoints(), path.userData.style));\n  }), [svg, skipStrokes]);\n  useEffect(() => {\n    return () => strokeGeometries.forEach(group => group && group.map(g => g.dispose()));\n  }, [strokeGeometries]);\n  return /*#__PURE__*/React.createElement(\"object3D\", _extends({\n    ref: ref\n  }, props), /*#__PURE__*/React.createElement(\"object3D\", {\n    scale: [1, -1, 1]\n  }, svg.paths.map((path, p) => {\n    var _path$userData2, _path$userData3;\n\n    return /*#__PURE__*/React.createElement(Fragment, {\n      key: p\n    }, !skipFill && ((_path$userData2 = path.userData) == null ? void 0 : _path$userData2.style.fill) !== undefined && path.userData.style.fill !== 'none' && SVGLoader.createShapes(path).map((shape, s) => /*#__PURE__*/React.createElement(\"mesh\", _extends({\n      key: s\n    }, fillMeshProps), /*#__PURE__*/React.createElement(\"shapeGeometry\", {\n      args: [shape]\n    }), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", _extends({\n      color: path.userData.style.fill,\n      opacity: path.userData.style.fillOpacity,\n      transparent: true,\n      side: DoubleSide,\n      depthWrite: false\n    }, fillMaterial)))), !skipStrokes && ((_path$userData3 = path.userData) == null ? void 0 : _path$userData3.style.stroke) !== undefined && path.userData.style.stroke !== 'none' && path.subPaths.map((_subPath, s) => /*#__PURE__*/React.createElement(\"mesh\", _extends({\n      key: s,\n      geometry: strokeGeometries[p][s]\n    }, strokeMeshProps), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", _extends({\n      color: path.userData.style.stroke,\n      opacity: path.userData.style.strokeOpacity,\n      transparent: true,\n      side: DoubleSide,\n      depthWrite: false\n    }, strokeMaterial)))));\n  })));\n});\n\nexport { Svg };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAChE,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,SAAS,QAAQ,cAAc;AAExC,MAAMC,GAAG,GAAG,aAAaN,UAAU,CAAC,SAASO,MAAMA,CAAC;EAClDC,GAAG;EACHC,QAAQ;EACRC,WAAW;EACXC,YAAY;EACZC,cAAc;EACdC,aAAa;EACbC,eAAe;EACf,GAAGC;AACL,CAAC,EAAEC,GAAG,EAAE;EACN,MAAMC,GAAG,GAAGnB,SAAS,CAACO,SAAS,EAAE,CAACG,GAAG,CAACU,UAAU,CAAC,MAAM,CAAC,GAAGV,GAAG,GAAG,2BAA2BA,GAAG,EAAE,CAAC;EAClG,MAAMW,gBAAgB,GAAGlB,OAAO,CAAC,MAAMS,WAAW,GAAG,EAAE,GAAGO,GAAG,CAACG,KAAK,CAACC,GAAG,CAACC,IAAI,IAAI;IAC9E,IAAIC,cAAc;IAElB,OAAO,CAAC,CAACA,cAAc,GAAGD,IAAI,CAACE,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,cAAc,CAACE,KAAK,CAACC,MAAM,MAAMC,SAAS,IAAIL,IAAI,CAACE,QAAQ,CAACC,KAAK,CAACC,MAAM,KAAK,MAAM,GAAG,IAAI,GAAGJ,IAAI,CAACM,QAAQ,CAACP,GAAG,CAACQ,OAAO,IAAIxB,SAAS,CAACyB,cAAc,CAACD,OAAO,CAACE,SAAS,CAAC,CAAC,EAAET,IAAI,CAACE,QAAQ,CAACC,KAAK,CAAC,CAAC;EAC3P,CAAC,CAAC,EAAE,CAACR,GAAG,EAAEP,WAAW,CAAC,CAAC;EACvBR,SAAS,CAAC,MAAM;IACd,OAAO,MAAMiB,gBAAgB,CAACa,OAAO,CAACC,KAAK,IAAIA,KAAK,IAAIA,KAAK,CAACZ,GAAG,CAACa,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;EACtF,CAAC,EAAE,CAAChB,gBAAgB,CAAC,CAAC;EACtB,OAAO,aAAapB,KAAK,CAACqC,aAAa,CAAC,UAAU,EAAEvC,QAAQ,CAAC;IAC3DmB,GAAG,EAAEA;EACP,CAAC,EAAED,KAAK,CAAC,EAAE,aAAahB,KAAK,CAACqC,aAAa,CAAC,UAAU,EAAE;IACtDC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;EAClB,CAAC,EAAEpB,GAAG,CAACG,KAAK,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEgB,CAAC,KAAK;IAC5B,IAAIC,eAAe,EAAEC,eAAe;IAEpC,OAAO,aAAazC,KAAK,CAACqC,aAAa,CAACjC,QAAQ,EAAE;MAChDsC,GAAG,EAAEH;IACP,CAAC,EAAE,CAAC7B,QAAQ,IAAI,CAAC,CAAC8B,eAAe,GAAGjB,IAAI,CAACE,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGe,eAAe,CAACd,KAAK,CAACiB,IAAI,MAAMf,SAAS,IAAIL,IAAI,CAACE,QAAQ,CAACC,KAAK,CAACiB,IAAI,KAAK,MAAM,IAAIrC,SAAS,CAACsC,YAAY,CAACrB,IAAI,CAAC,CAACD,GAAG,CAAC,CAACuB,KAAK,EAAEC,CAAC,KAAK,aAAa9C,KAAK,CAACqC,aAAa,CAAC,MAAM,EAAEvC,QAAQ,CAAC;MACzP4C,GAAG,EAAEI;IACP,CAAC,EAAEhC,aAAa,CAAC,EAAE,aAAad,KAAK,CAACqC,aAAa,CAAC,eAAe,EAAE;MACnEU,IAAI,EAAE,CAACF,KAAK;IACd,CAAC,CAAC,EAAE,aAAa7C,KAAK,CAACqC,aAAa,CAAC,mBAAmB,EAAEvC,QAAQ,CAAC;MACjEkD,KAAK,EAAEzB,IAAI,CAACE,QAAQ,CAACC,KAAK,CAACiB,IAAI;MAC/BM,OAAO,EAAE1B,IAAI,CAACE,QAAQ,CAACC,KAAK,CAACwB,WAAW;MACxCC,WAAW,EAAE,IAAI;MACjBC,IAAI,EAAE/C,UAAU;MAChBgD,UAAU,EAAE;IACd,CAAC,EAAEzC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAACD,WAAW,IAAI,CAAC,CAAC8B,eAAe,GAAGlB,IAAI,CAACE,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgB,eAAe,CAACf,KAAK,CAACC,MAAM,MAAMC,SAAS,IAAIL,IAAI,CAACE,QAAQ,CAACC,KAAK,CAACC,MAAM,KAAK,MAAM,IAAIJ,IAAI,CAACM,QAAQ,CAACP,GAAG,CAAC,CAACgC,QAAQ,EAAER,CAAC,KAAK,aAAa9C,KAAK,CAACqC,aAAa,CAAC,MAAM,EAAEvC,QAAQ,CAAC;MACtQ4C,GAAG,EAAEI,CAAC;MACNS,QAAQ,EAAEnC,gBAAgB,CAACmB,CAAC,CAAC,CAACO,CAAC;IACjC,CAAC,EAAE/B,eAAe,CAAC,EAAE,aAAaf,KAAK,CAACqC,aAAa,CAAC,mBAAmB,EAAEvC,QAAQ,CAAC;MAClFkD,KAAK,EAAEzB,IAAI,CAACE,QAAQ,CAACC,KAAK,CAACC,MAAM;MACjCsB,OAAO,EAAE1B,IAAI,CAACE,QAAQ,CAACC,KAAK,CAAC8B,aAAa;MAC1CL,WAAW,EAAE,IAAI;MACjBC,IAAI,EAAE/C,UAAU;MAChBgD,UAAU,EAAE;IACd,CAAC,EAAExC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AAEF,SAASN,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}