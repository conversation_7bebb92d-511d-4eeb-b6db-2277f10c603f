"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("./Html.cjs.js"),r=require("./CycleRaycast.cjs.js"),s=require("./useCursor.cjs.js"),o=require("./Loader.cjs.js"),t=require("./ScrollControls.cjs.js"),i=require("./PresentationControls.cjs.js"),c=require("./KeyboardControls.cjs.js"),a=require("./Select.cjs.js"),u=require("./View.cjs.js"),n=require("./pivotControls/index.cjs.js"),j=require("../core/Billboard.cjs.js"),p=require("../core/ScreenSpace.cjs.js"),l=require("../core/QuadraticBezierLine.cjs.js"),x=require("../core/CubicBezierLine.cjs.js"),q=require("../core/CatmullRomLine.cjs.js"),d=require("../core/Line.cjs.js"),m=require("../core/PositionalAudio.cjs.js"),C=require("../core/Text.cjs.js"),h=require("../core/Text3D.cjs.js"),S=require("../core/Effects.cjs.js"),M=require("../core/GradientTexture.cjs.js"),b=require("../core/Image.cjs.js"),P=require("../core/Edges.cjs.js"),T=require("../core/Trail.cjs.js"),f=require("../core/Sampler.cjs.js"),v=require("../core/ComputedAttribute.cjs.js"),B=require("../core/Clone.cjs.js"),g=require("../core/MarchingCubes.cjs.js"),A=require("../core/Decal.cjs.js"),L=require("../core/Svg.cjs.js"),R=require("../core/Gltf.cjs.js"),D=require("../core/AsciiRenderer.cjs.js"),E=require("../core/OrthographicCamera.cjs.js"),F=require("../core/PerspectiveCamera.cjs.js"),w=require("../core/CubeCamera.cjs.js"),k=require("../core/DeviceOrientationControls.cjs.js"),G=require("../core/FlyControls.cjs.js"),z=require("../core/MapControls.cjs.js"),y=require("../core/OrbitControls.cjs.js"),O=require("../core/TrackballControls.cjs.js"),I=require("../core/ArcballControls.cjs.js"),H=require("../core/TransformControls.cjs.js"),V=require("../core/PointerLockControls.cjs.js"),K=require("../core/FirstPersonControls.cjs.js"),W=require("../core/CameraControls.cjs.js"),Q=require("../core/GizmoHelper.cjs.js"),X=require("../core/GizmoViewcube.cjs.js"),N=require("../core/GizmoViewport.cjs.js"),U=require("../core/Grid.cjs.js"),_=require("../core/useCubeTexture.cjs.js"),J=require("../core/useFBX.cjs.js"),Y=require("../core/useGLTF.cjs.js"),Z=require("../core/useKTX2.cjs.js"),$=require("../core/useProgress.cjs.js"),ee=require("../core/useTexture.cjs.js"),re=require("../core/useVideoTexture.cjs.js"),se=require("../core/useFont.cjs.js"),oe=require("../core/Stats.cjs.js"),te=require("../core/useDepthBuffer.cjs.js"),ie=require("../core/useAspect.cjs.js"),ce=require("../core/useCamera.cjs.js"),ae=require("../core/useDetectGPU.cjs.js"),ue=require("../core/useHelper.cjs.js"),ne=require("../core/useBVH.cjs.js"),je=require("../core/useContextBridge.cjs.js"),pe=require("../core/useAnimations.cjs.js"),le=require("../core/useFBO.cjs.js"),xe=require("../core/useIntersect.cjs.js"),qe=require("../core/useBoxProjectedEnv.cjs.js"),de=require("../core/BBAnchor.cjs.js"),me=require("../core/useTrailTexture.cjs.js"),Ce=require("../core/useCubeCamera.cjs.js"),he=require("../core/Example.cjs.js"),Se=require("../core/SpriteAnimator.cjs.js"),Me=require("../core/CurveModifier.cjs.js"),be=require("../core/MeshDistortMaterial.cjs.js"),Pe=require("../core/MeshWobbleMaterial.cjs.js"),Te=require("../core/MeshReflectorMaterial.cjs.js"),fe=require("../core/MeshRefractionMaterial.cjs.js"),ve=require("../core/MeshTransmissionMaterial.cjs.js"),Be=require("../core/MeshDiscardMaterial.cjs.js"),ge=require("../core/PointMaterial.cjs.js"),Ae=require("../core/shaderMaterial.cjs.js"),Le=require("../core/softShadows.cjs.js"),Re=require("../core/shapes.cjs.js"),De=require("../core/Facemesh.cjs.js"),Ee=require("../core/RoundedBox.cjs.js"),Fe=require("../core/ScreenQuad.cjs.js"),we=require("../core/Center.cjs.js"),ke=require("../core/Resize.cjs.js"),Ge=require("../core/Bounds.cjs.js"),ze=require("../core/CameraShake.cjs.js"),ye=require("../core/Float.cjs.js"),Oe=require("../core/Stage.cjs.js"),Ie=require("../core/Backdrop.cjs.js"),He=require("../core/Shadow.cjs.js"),Ve=require("../core/Caustics.cjs.js"),Ke=require("../core/ContactShadows.cjs.js"),We=require("../core/AccumulativeShadows.cjs.js"),Qe=require("../core/Reflector.cjs.js"),Xe=require("../core/SpotLight.cjs.js"),Ne=require("../core/Environment.cjs.js"),Ue=require("../core/Lightformer.cjs.js"),_e=require("../core/Sky.cjs.js"),Je=require("../core/Stars.cjs.js"),Ye=require("../core/Cloud.cjs.js"),Ze=require("../core/Sparkles.cjs.js"),$e=require("../core/useEnvironment.cjs.js"),er=require("../core/useMatcapTexture.cjs.js"),rr=require("../core/useNormalTexture.cjs.js"),sr=require("../core/Wireframe.cjs.js"),or=require("../core/Points.cjs.js"),tr=require("../core/Instances.cjs.js"),ir=require("../core/Segments.cjs.js"),cr=require("../core/Detailed.cjs.js"),ar=require("../core/Preload.cjs.js"),ur=require("../core/BakeShadows.cjs.js"),nr=require("../core/meshBounds.cjs.js"),jr=require("../core/AdaptiveDpr.cjs.js"),pr=require("../core/AdaptiveEvents.cjs.js"),lr=require("../core/PerformanceMonitor.cjs.js"),xr=require("../core/RenderTexture.cjs.js"),qr=require("../core/Mask.cjs.js"),dr=require("../core/Hud.cjs.js");require("@babel/runtime/helpers/extends"),require("react"),require("react-dom/client"),require("three"),require("@react-three/fiber"),require("zustand"),require("react-merge-refs"),require("maath"),require("@react-spring/three"),require("@use-gesture/react"),require("zustand/middleware"),require("three-stdlib"),require("zustand/shallow"),require("./pivotControls/AxisArrow.cjs.js"),require("./pivotControls/context.cjs.js"),require("./pivotControls/PlaneSlider.cjs.js"),require("./pivotControls/AxisRotator.cjs.js"),require("lodash.clamp"),require("troika-three-text"),require("suspend-react"),require("meshline"),require("lodash.pick"),require("lodash.omit"),require("camera-controls"),require("stats.js"),require("../helpers/useEffectfulState.cjs.js"),require("detect-gpu"),require("three-mesh-bvh"),require("../materials/BlurPass.cjs.js"),require("../materials/ConvolutionMaterial.cjs.js"),require("../materials/MeshReflectorMaterial.cjs.js"),require("../materials/MeshRefractionMaterial.cjs.js"),require("../materials/DiscardMaterial.cjs.js"),require("../helpers/environment-assets.cjs.js"),require("../materials/SpotLightMaterial.cjs.js"),require("../materials/WireframeMaterial.cjs.js"),require("react-composer"),exports.Html=e.Html,exports.CycleRaycast=r.CycleRaycast,exports.useCursor=s.useCursor,exports.Loader=o.Loader,exports.Scroll=t.Scroll,exports.ScrollControls=t.ScrollControls,exports.useScroll=t.useScroll,exports.PresentationControls=i.PresentationControls,exports.KeyboardControls=c.KeyboardControls,exports.useKeyboardControls=c.useKeyboardControls,exports.Select=a.Select,exports.useSelect=a.useSelect,exports.View=u.View,exports.PivotControls=n.PivotControls,exports.calculateScaleFactor=n.calculateScaleFactor,exports.Billboard=j.Billboard,exports.ScreenSpace=p.ScreenSpace,exports.QuadraticBezierLine=l.QuadraticBezierLine,exports.CubicBezierLine=x.CubicBezierLine,exports.CatmullRomLine=q.CatmullRomLine,exports.Line=d.Line,exports.PositionalAudio=m.PositionalAudio,exports.Text=C.Text,exports.Text3D=h.Text3D,exports.Effects=S.Effects,exports.isWebGL2Available=S.isWebGL2Available,exports.GradientTexture=M.GradientTexture,exports.Image=b.Image,exports.Edges=P.Edges,exports.Trail=T.Trail,exports.useTrail=T.useTrail,exports.Sampler=f.Sampler,exports.useSurfaceSampler=f.useSurfaceSampler,exports.ComputedAttribute=v.ComputedAttribute,exports.Clone=B.Clone,exports.MarchingCube=g.MarchingCube,exports.MarchingCubes=g.MarchingCubes,exports.MarchingPlane=g.MarchingPlane,exports.Decal=A.Decal,exports.Svg=L.Svg,exports.Gltf=R.Gltf,exports.AsciiRenderer=D.AsciiRenderer,exports.OrthographicCamera=E.OrthographicCamera,exports.PerspectiveCamera=F.PerspectiveCamera,exports.CubeCamera=w.CubeCamera,exports.DeviceOrientationControls=k.DeviceOrientationControls,exports.FlyControls=G.FlyControls,exports.MapControls=z.MapControls,exports.OrbitControls=y.OrbitControls,exports.TrackballControls=O.TrackballControls,exports.ArcballControls=I.ArcballControls,exports.TransformControls=H.TransformControls,exports.PointerLockControls=V.PointerLockControls,exports.FirstPersonControls=K.FirstPersonControls,exports.CameraControls=W.CameraControls,exports.GizmoHelper=Q.GizmoHelper,exports.useGizmoContext=Q.useGizmoContext,exports.GizmoViewcube=X.GizmoViewcube,exports.GizmoViewport=N.GizmoViewport,exports.Grid=U.Grid,exports.useCubeTexture=_.useCubeTexture,exports.useFBX=J.useFBX,exports.useGLTF=Y.useGLTF,exports.useKTX2=Z.useKTX2,exports.useProgress=$.useProgress,exports.IsObject=ee.IsObject,exports.useTexture=ee.useTexture,exports.useVideoTexture=re.useVideoTexture,exports.useFont=se.useFont,exports.Stats=oe.Stats,exports.useDepthBuffer=te.useDepthBuffer,exports.useAspect=ie.useAspect,exports.useCamera=ce.useCamera,exports.useDetectGPU=ae.useDetectGPU,exports.useHelper=ue.useHelper,exports.Bvh=ne.Bvh,exports.useBVH=ne.useBVH,exports.useContextBridge=je.useContextBridge,exports.useAnimations=pe.useAnimations,exports.useFBO=le.useFBO,exports.useIntersect=xe.useIntersect,exports.useBoxProjectedEnv=qe.useBoxProjectedEnv,exports.BBAnchor=de.BBAnchor,exports.useTrailTexture=me.useTrailTexture,exports.useCubeCamera=Ce.useCubeCamera,exports.Example=he.Example,exports.SpriteAnimator=Se.SpriteAnimator,exports.CurveModifier=Me.CurveModifier,exports.MeshDistortMaterial=be.MeshDistortMaterial,exports.MeshWobbleMaterial=Pe.MeshWobbleMaterial,exports.MeshReflectorMaterial=Te.MeshReflectorMaterial,exports.MeshRefractionMaterial=fe.MeshRefractionMaterial,exports.MeshTransmissionMaterial=ve.MeshTransmissionMaterial,exports.MeshDiscardMaterial=Be.MeshDiscardMaterial,exports.PointMaterial=ge.PointMaterial,exports.PointMaterialImpl=ge.PointMaterialImpl,exports.shaderMaterial=Ae.shaderMaterial,exports.SoftShadows=Le.SoftShadows,exports.Box=Re.Box,exports.Capsule=Re.Capsule,exports.Circle=Re.Circle,exports.Cone=Re.Cone,exports.Cylinder=Re.Cylinder,exports.Dodecahedron=Re.Dodecahedron,exports.Extrude=Re.Extrude,exports.Icosahedron=Re.Icosahedron,exports.Lathe=Re.Lathe,exports.Octahedron=Re.Octahedron,exports.Plane=Re.Plane,exports.Polyhedron=Re.Polyhedron,exports.Ring=Re.Ring,exports.Shape=Re.Shape,exports.Sphere=Re.Sphere,exports.Tetrahedron=Re.Tetrahedron,exports.Torus=Re.Torus,exports.TorusKnot=Re.TorusKnot,exports.Tube=Re.Tube,exports.Facemesh=De.Facemesh,exports.FacemeshDatas=De.FacemeshDatas,exports.RoundedBox=Ee.RoundedBox,exports.ScreenQuad=Fe.ScreenQuad,exports.Center=we.Center,exports.Resize=ke.Resize,exports.Bounds=Ge.Bounds,exports.useBounds=Ge.useBounds,exports.CameraShake=ze.CameraShake,exports.Float=ye.Float,exports.Stage=Oe.Stage,exports.Backdrop=Ie.Backdrop,exports.Shadow=He.Shadow,exports.Caustics=Ve.Caustics,exports.ContactShadows=Ke.ContactShadows,exports.AccumulativeShadows=We.AccumulativeShadows,exports.RandomizedLight=We.RandomizedLight,exports.accumulativeContext=We.accumulativeContext,exports.Reflector=Qe.Reflector,exports.SpotLight=Xe.SpotLight,exports.SpotLightShadow=Xe.SpotLightShadow,exports.Environment=Ne.Environment,exports.EnvironmentCube=Ne.EnvironmentCube,exports.EnvironmentMap=Ne.EnvironmentMap,exports.EnvironmentPortal=Ne.EnvironmentPortal,exports.Lightformer=Ue.Lightformer,exports.Sky=_e.Sky,exports.calcPosFromAngles=_e.calcPosFromAngles,exports.Stars=Je.Stars,exports.Cloud=Ye.Cloud,exports.Sparkles=Ze.Sparkles,exports.useEnvironment=$e.useEnvironment,exports.useMatcapTexture=er.useMatcapTexture,exports.useNormalTexture=rr.useNormalTexture,exports.Wireframe=sr.Wireframe,exports.Point=or.Point,exports.Points=or.Points,exports.PointsBuffer=or.PointsBuffer,exports.PositionPoint=or.PositionPoint,exports.Instance=tr.Instance,exports.Instances=tr.Instances,exports.Merged=tr.Merged,exports.Segment=ir.Segment,exports.SegmentObject=ir.SegmentObject,exports.Segments=ir.Segments,exports.Detailed=cr.Detailed,exports.Preload=ar.Preload,exports.BakeShadows=ur.BakeShadows,exports.meshBounds=nr.meshBounds,exports.AdaptiveDpr=jr.AdaptiveDpr,exports.AdaptiveEvents=pr.AdaptiveEvents,exports.PerformanceMonitor=lr.PerformanceMonitor,exports.usePerformanceMonitor=lr.usePerformanceMonitor,exports.RenderTexture=xr.RenderTexture,exports.Mask=qr.Mask,exports.useMask=qr.useMask,exports.Hud=dr.Hud;
