{"ast": null, "code": "import * as THREE from 'three';\nimport * as React from 'react';\nimport { useThree, createPortal, useFrame } from '@react-three/fiber';\nfunction RenderHud({\n  defaultScene,\n  defaultCamera,\n  renderPriority = 1\n}) {\n  const {\n    gl,\n    scene,\n    camera\n  } = useThree();\n  let oldCLear;\n  useFrame(() => {\n    oldCLear = gl.autoClear;\n    if (renderPriority === 1) {\n      // Clear scene and render the default scene\n      gl.autoClear = true;\n      gl.render(defaultScene, defaultCamera);\n    } // Disable cleaning and render the portal with its own camera\n\n    gl.autoClear = false;\n    gl.clearDepth();\n    gl.render(scene, camera); // Restore default\n\n    gl.autoClear = oldCLear;\n  }, renderPriority);\n  return /*#__PURE__*/React.createElement(React.Fragment, null);\n}\nfunction Hud({\n  children,\n  renderPriority = 1\n}) {\n  const {\n    scene: defaultScene,\n    camera: defaultCamera\n  } = useThree();\n  const [hudScene] = React.useState(() => new THREE.Scene());\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal(/*#__PURE__*/React.createElement(React.Fragment, null, children, /*#__PURE__*/React.createElement(RenderHud, {\n    defaultScene: defaultScene,\n    defaultCamera: defaultCamera,\n    renderPriority: renderPriority\n  })), hudScene, {\n    events: {\n      priority: renderPriority + 1\n    }\n  }));\n}\nexport { Hud };", "map": {"version": 3, "names": ["THREE", "React", "useThree", "createPortal", "useFrame", "RenderHud", "defaultScene", "defaultCamera", "renderPriority", "gl", "scene", "camera", "oldCLear", "autoClear", "render", "clear<PERSON><PERSON>h", "createElement", "Fragment", "<PERSON><PERSON>", "children", "hudScene", "useState", "Scene", "events", "priority"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Hud.js"], "sourcesContent": ["import * as THREE from 'three';\nimport * as React from 'react';\nimport { useThree, createPortal, useFrame } from '@react-three/fiber';\n\nfunction RenderHud({\n  defaultScene,\n  defaultCamera,\n  renderPriority = 1\n}) {\n  const {\n    gl,\n    scene,\n    camera\n  } = useThree();\n  let oldCLear;\n  useFrame(() => {\n    oldCLear = gl.autoClear;\n\n    if (renderPriority === 1) {\n      // Clear scene and render the default scene\n      gl.autoClear = true;\n      gl.render(defaultScene, defaultCamera);\n    } // Disable cleaning and render the portal with its own camera\n\n\n    gl.autoClear = false;\n    gl.clearDepth();\n    gl.render(scene, camera); // Restore default\n\n    gl.autoClear = oldCLear;\n  }, renderPriority);\n  return /*#__PURE__*/React.createElement(React.Fragment, null);\n}\n\nfunction Hud({\n  children,\n  renderPriority = 1\n}) {\n  const {\n    scene: defaultScene,\n    camera: defaultCamera\n  } = useThree();\n  const [hudScene] = React.useState(() => new THREE.Scene());\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal( /*#__PURE__*/React.createElement(React.Fragment, null, children, /*#__PURE__*/React.createElement(RenderHud, {\n    defaultScene: defaultScene,\n    defaultCamera: defaultCamera,\n    renderPriority: renderPriority\n  })), hudScene, {\n    events: {\n      priority: renderPriority + 1\n    }\n  }));\n}\n\nexport { Hud };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,oBAAoB;AAErE,SAASC,SAASA,CAAC;EACjBC,YAAY;EACZC,aAAa;EACbC,cAAc,GAAG;AACnB,CAAC,EAAE;EACD,MAAM;IACJC,EAAE;IACFC,KAAK;IACLC;EACF,CAAC,GAAGT,QAAQ,CAAC,CAAC;EACd,IAAIU,QAAQ;EACZR,QAAQ,CAAC,MAAM;IACbQ,QAAQ,GAAGH,EAAE,CAACI,SAAS;IAEvB,IAAIL,cAAc,KAAK,CAAC,EAAE;MACxB;MACAC,EAAE,CAACI,SAAS,GAAG,IAAI;MACnBJ,EAAE,CAACK,MAAM,CAACR,YAAY,EAAEC,aAAa,CAAC;IACxC,CAAC,CAAC;;IAGFE,EAAE,CAACI,SAAS,GAAG,KAAK;IACpBJ,EAAE,CAACM,UAAU,CAAC,CAAC;IACfN,EAAE,CAACK,MAAM,CAACJ,KAAK,EAAEC,MAAM,CAAC,CAAC,CAAC;;IAE1BF,EAAE,CAACI,SAAS,GAAGD,QAAQ;EACzB,CAAC,EAAEJ,cAAc,CAAC;EAClB,OAAO,aAAaP,KAAK,CAACe,aAAa,CAACf,KAAK,CAACgB,QAAQ,EAAE,IAAI,CAAC;AAC/D;AAEA,SAASC,GAAGA,CAAC;EACXC,QAAQ;EACRX,cAAc,GAAG;AACnB,CAAC,EAAE;EACD,MAAM;IACJE,KAAK,EAAEJ,YAAY;IACnBK,MAAM,EAAEJ;EACV,CAAC,GAAGL,QAAQ,CAAC,CAAC;EACd,MAAM,CAACkB,QAAQ,CAAC,GAAGnB,KAAK,CAACoB,QAAQ,CAAC,MAAM,IAAIrB,KAAK,CAACsB,KAAK,CAAC,CAAC,CAAC;EAC1D,OAAO,aAAarB,KAAK,CAACe,aAAa,CAACf,KAAK,CAACgB,QAAQ,EAAE,IAAI,EAAEd,YAAY,CAAE,aAAaF,KAAK,CAACe,aAAa,CAACf,KAAK,CAACgB,QAAQ,EAAE,IAAI,EAAEE,QAAQ,EAAE,aAAalB,KAAK,CAACe,aAAa,CAACX,SAAS,EAAE;IACvLC,YAAY,EAAEA,YAAY;IAC1BC,aAAa,EAAEA,aAAa;IAC5BC,cAAc,EAAEA;EAClB,CAAC,CAAC,CAAC,EAAEY,QAAQ,EAAE;IACbG,MAAM,EAAE;MACNC,QAAQ,EAAEhB,cAAc,GAAG;IAC7B;EACF,CAAC,CAAC,CAAC;AACL;AAEA,SAASU,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}