{"version": 3, "file": "Water.js", "sources": ["../../src/objects/Water.js"], "sourcesContent": ["import {\n  Color,\n  FrontSide,\n  Matrix4,\n  <PERSON>sh,\n  PerspectiveCamera,\n  Plane,\n  ShaderMaterial,\n  UniformsLib,\n  UniformsUtils,\n  Vector3,\n  Vector4,\n  WebGLRenderTarget,\n} from 'three'\nimport { version } from '../_polyfill/constants'\n\n/**\n * Work based on :\n * https://github.com/Slayvin: Flat mirror for three.js\n * https://home.adelphi.edu/~stemkoski/ : An implementation of water shader based on the flat mirror\n * http://29a.ch/ && http://29a.ch/slides/2012/webglwater/ : Water shader explanations in WebGL\n */\n\nclass Water extends Mesh {\n  constructor(geometry, options = {}) {\n    super(geometry)\n\n    this.isWater = true\n\n    const scope = this\n\n    const textureWidth = options.textureWidth !== undefined ? options.textureWidth : 512\n    const textureHeight = options.textureHeight !== undefined ? options.textureHeight : 512\n\n    const clipBias = options.clipBias !== undefined ? options.clipBias : 0.0\n    const alpha = options.alpha !== undefined ? options.alpha : 1.0\n    const time = options.time !== undefined ? options.time : 0.0\n    const normalSampler = options.waterNormals !== undefined ? options.waterNormals : null\n    const sunDirection = options.sunDirection !== undefined ? options.sunDirection : new Vector3(0.70707, 0.70707, 0.0)\n    const sunColor = new Color(options.sunColor !== undefined ? options.sunColor : 0xffffff)\n    const waterColor = new Color(options.waterColor !== undefined ? options.waterColor : 0x7f7f7f)\n    const eye = options.eye !== undefined ? options.eye : new Vector3(0, 0, 0)\n    const distortionScale = options.distortionScale !== undefined ? options.distortionScale : 20.0\n    const side = options.side !== undefined ? options.side : FrontSide\n    const fog = options.fog !== undefined ? options.fog : false\n\n    //\n\n    const mirrorPlane = new Plane()\n    const normal = new Vector3()\n    const mirrorWorldPosition = new Vector3()\n    const cameraWorldPosition = new Vector3()\n    const rotationMatrix = new Matrix4()\n    const lookAtPosition = new Vector3(0, 0, -1)\n    const clipPlane = new Vector4()\n\n    const view = new Vector3()\n    const target = new Vector3()\n    const q = new Vector4()\n\n    const textureMatrix = new Matrix4()\n\n    const mirrorCamera = new PerspectiveCamera()\n\n    const renderTarget = new WebGLRenderTarget(textureWidth, textureHeight)\n\n    const mirrorShader = {\n      uniforms: UniformsUtils.merge([\n        UniformsLib['fog'],\n        UniformsLib['lights'],\n        {\n          normalSampler: { value: null },\n          mirrorSampler: { value: null },\n          alpha: { value: 1.0 },\n          time: { value: 0.0 },\n          size: { value: 1.0 },\n          distortionScale: { value: 20.0 },\n          textureMatrix: { value: new Matrix4() },\n          sunColor: { value: new Color(0x7f7f7f) },\n          sunDirection: { value: new Vector3(0.70707, 0.70707, 0) },\n          eye: { value: new Vector3() },\n          waterColor: { value: new Color(0x555555) },\n        },\n      ]),\n\n      vertexShader: /* glsl */ `\n\t\t\t\tuniform mat4 textureMatrix;\n\t\t\t\tuniform float time;\n\n\t\t\t\tvarying vec4 mirrorCoord;\n\t\t\t\tvarying vec4 worldPosition;\n\n\t\t\t\t#include <common>\n\t\t\t\t#include <fog_pars_vertex>\n\t\t\t\t#include <shadowmap_pars_vertex>\n\t\t\t\t#include <logdepthbuf_pars_vertex>\n\n\t\t\t\tvoid main() {\n\t\t\t\t\tmirrorCoord = modelMatrix * vec4( position, 1.0 );\n\t\t\t\t\tworldPosition = mirrorCoord.xyzw;\n\t\t\t\t\tmirrorCoord = textureMatrix * mirrorCoord;\n\t\t\t\t\tvec4 mvPosition =  modelViewMatrix * vec4( position, 1.0 );\n\t\t\t\t\tgl_Position = projectionMatrix * mvPosition;\n\n\t\t\t\t#include <beginnormal_vertex>\n\t\t\t\t#include <defaultnormal_vertex>\n\t\t\t\t#include <logdepthbuf_vertex>\n\t\t\t\t#include <fog_vertex>\n\t\t\t\t#include <shadowmap_vertex>\n\t\t\t}`,\n\n      fragmentShader: /* glsl */ `\n\t\t\t\tuniform sampler2D mirrorSampler;\n\t\t\t\tuniform float alpha;\n\t\t\t\tuniform float time;\n\t\t\t\tuniform float size;\n\t\t\t\tuniform float distortionScale;\n\t\t\t\tuniform sampler2D normalSampler;\n\t\t\t\tuniform vec3 sunColor;\n\t\t\t\tuniform vec3 sunDirection;\n\t\t\t\tuniform vec3 eye;\n\t\t\t\tuniform vec3 waterColor;\n\n\t\t\t\tvarying vec4 mirrorCoord;\n\t\t\t\tvarying vec4 worldPosition;\n\n\t\t\t\tvec4 getNoise( vec2 uv ) {\n\t\t\t\t\tvec2 uv0 = ( uv / 103.0 ) + vec2(time / 17.0, time / 29.0);\n\t\t\t\t\tvec2 uv1 = uv / 107.0-vec2( time / -19.0, time / 31.0 );\n\t\t\t\t\tvec2 uv2 = uv / vec2( 8907.0, 9803.0 ) + vec2( time / 101.0, time / 97.0 );\n\t\t\t\t\tvec2 uv3 = uv / vec2( 1091.0, 1027.0 ) - vec2( time / 109.0, time / -113.0 );\n\t\t\t\t\tvec4 noise = texture2D( normalSampler, uv0 ) +\n\t\t\t\t\t\ttexture2D( normalSampler, uv1 ) +\n\t\t\t\t\t\ttexture2D( normalSampler, uv2 ) +\n\t\t\t\t\t\ttexture2D( normalSampler, uv3 );\n\t\t\t\t\treturn noise * 0.5 - 1.0;\n\t\t\t\t}\n\n\t\t\t\tvoid sunLight( const vec3 surfaceNormal, const vec3 eyeDirection, float shiny, float spec, float diffuse, inout vec3 diffuseColor, inout vec3 specularColor ) {\n\t\t\t\t\tvec3 reflection = normalize( reflect( -sunDirection, surfaceNormal ) );\n\t\t\t\t\tfloat direction = max( 0.0, dot( eyeDirection, reflection ) );\n\t\t\t\t\tspecularColor += pow( direction, shiny ) * sunColor * spec;\n\t\t\t\t\tdiffuseColor += max( dot( sunDirection, surfaceNormal ), 0.0 ) * sunColor * diffuse;\n\t\t\t\t}\n\n\t\t\t\t#include <common>\n\t\t\t\t#include <packing>\n\t\t\t\t#include <bsdfs>\n\t\t\t\t#include <fog_pars_fragment>\n\t\t\t\t#include <logdepthbuf_pars_fragment>\n\t\t\t\t#include <lights_pars_begin>\n\t\t\t\t#include <shadowmap_pars_fragment>\n\t\t\t\t#include <shadowmask_pars_fragment>\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\t#include <logdepthbuf_fragment>\n\t\t\t\t\tvec4 noise = getNoise( worldPosition.xz * size );\n\t\t\t\t\tvec3 surfaceNormal = normalize( noise.xzy * vec3( 1.5, 1.0, 1.5 ) );\n\n\t\t\t\t\tvec3 diffuseLight = vec3(0.0);\n\t\t\t\t\tvec3 specularLight = vec3(0.0);\n\n\t\t\t\t\tvec3 worldToEye = eye-worldPosition.xyz;\n\t\t\t\t\tvec3 eyeDirection = normalize( worldToEye );\n\t\t\t\t\tsunLight( surfaceNormal, eyeDirection, 100.0, 2.0, 0.5, diffuseLight, specularLight );\n\n\t\t\t\t\tfloat distance = length(worldToEye);\n\n\t\t\t\t\tvec2 distortion = surfaceNormal.xz * ( 0.001 + 1.0 / distance ) * distortionScale;\n\t\t\t\t\tvec3 reflectionSample = vec3( texture2D( mirrorSampler, mirrorCoord.xy / mirrorCoord.w + distortion ) );\n\n\t\t\t\t\tfloat theta = max( dot( eyeDirection, surfaceNormal ), 0.0 );\n\t\t\t\t\tfloat rf0 = 0.3;\n\t\t\t\t\tfloat reflectance = rf0 + ( 1.0 - rf0 ) * pow( ( 1.0 - theta ), 5.0 );\n\t\t\t\t\tvec3 scatter = max( 0.0, dot( surfaceNormal, eyeDirection ) ) * waterColor;\n\t\t\t\t\tvec3 albedo = mix( ( sunColor * diffuseLight * 0.3 + scatter ) * getShadowMask(), ( vec3( 0.1 ) + reflectionSample * 0.9 + reflectionSample * specularLight ), reflectance);\n\t\t\t\t\tvec3 outgoingLight = albedo;\n\t\t\t\t\tgl_FragColor = vec4( outgoingLight, alpha );\n\n\t\t\t\t\t#include <tonemapping_fragment>\n\t\t\t\t\t#include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n\t\t\t\t\t#include <fog_fragment>\t\n\t\t\t\t}`,\n    }\n\n    const material = new ShaderMaterial({\n      fragmentShader: mirrorShader.fragmentShader,\n      vertexShader: mirrorShader.vertexShader,\n      uniforms: UniformsUtils.clone(mirrorShader.uniforms),\n      lights: true,\n      side: side,\n      fog: fog,\n    })\n\n    material.uniforms['mirrorSampler'].value = renderTarget.texture\n    material.uniforms['textureMatrix'].value = textureMatrix\n    material.uniforms['alpha'].value = alpha\n    material.uniforms['time'].value = time\n    material.uniforms['normalSampler'].value = normalSampler\n    material.uniforms['sunColor'].value = sunColor\n    material.uniforms['waterColor'].value = waterColor\n    material.uniforms['sunDirection'].value = sunDirection\n    material.uniforms['distortionScale'].value = distortionScale\n\n    material.uniforms['eye'].value = eye\n\n    scope.material = material\n\n    scope.onBeforeRender = function (renderer, scene, camera) {\n      mirrorWorldPosition.setFromMatrixPosition(scope.matrixWorld)\n      cameraWorldPosition.setFromMatrixPosition(camera.matrixWorld)\n\n      rotationMatrix.extractRotation(scope.matrixWorld)\n\n      normal.set(0, 0, 1)\n      normal.applyMatrix4(rotationMatrix)\n\n      view.subVectors(mirrorWorldPosition, cameraWorldPosition)\n\n      // Avoid rendering when mirror is facing away\n\n      if (view.dot(normal) > 0) return\n\n      view.reflect(normal).negate()\n      view.add(mirrorWorldPosition)\n\n      rotationMatrix.extractRotation(camera.matrixWorld)\n\n      lookAtPosition.set(0, 0, -1)\n      lookAtPosition.applyMatrix4(rotationMatrix)\n      lookAtPosition.add(cameraWorldPosition)\n\n      target.subVectors(mirrorWorldPosition, lookAtPosition)\n      target.reflect(normal).negate()\n      target.add(mirrorWorldPosition)\n\n      mirrorCamera.position.copy(view)\n      mirrorCamera.up.set(0, 1, 0)\n      mirrorCamera.up.applyMatrix4(rotationMatrix)\n      mirrorCamera.up.reflect(normal)\n      mirrorCamera.lookAt(target)\n\n      mirrorCamera.far = camera.far // Used in WebGLBackground\n\n      mirrorCamera.updateMatrixWorld()\n      mirrorCamera.projectionMatrix.copy(camera.projectionMatrix)\n\n      // Update the texture matrix\n      textureMatrix.set(0.5, 0.0, 0.0, 0.5, 0.0, 0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0)\n      textureMatrix.multiply(mirrorCamera.projectionMatrix)\n      textureMatrix.multiply(mirrorCamera.matrixWorldInverse)\n\n      // Now update projection matrix with new clip plane, implementing code from: http://www.terathon.com/code/oblique.html\n      // Paper explaining this technique: http://www.terathon.com/lengyel/Lengyel-Oblique.pdf\n      mirrorPlane.setFromNormalAndCoplanarPoint(normal, mirrorWorldPosition)\n      mirrorPlane.applyMatrix4(mirrorCamera.matrixWorldInverse)\n\n      clipPlane.set(mirrorPlane.normal.x, mirrorPlane.normal.y, mirrorPlane.normal.z, mirrorPlane.constant)\n\n      const projectionMatrix = mirrorCamera.projectionMatrix\n\n      q.x = (Math.sign(clipPlane.x) + projectionMatrix.elements[8]) / projectionMatrix.elements[0]\n      q.y = (Math.sign(clipPlane.y) + projectionMatrix.elements[9]) / projectionMatrix.elements[5]\n      q.z = -1.0\n      q.w = (1.0 + projectionMatrix.elements[10]) / projectionMatrix.elements[14]\n\n      // Calculate the scaled plane vector\n      clipPlane.multiplyScalar(2.0 / clipPlane.dot(q))\n\n      // Replacing the third row of the projection matrix\n      projectionMatrix.elements[2] = clipPlane.x\n      projectionMatrix.elements[6] = clipPlane.y\n      projectionMatrix.elements[10] = clipPlane.z + 1.0 - clipBias\n      projectionMatrix.elements[14] = clipPlane.w\n\n      eye.setFromMatrixPosition(camera.matrixWorld)\n\n      // Render\n\n      const currentRenderTarget = renderer.getRenderTarget()\n\n      const currentXrEnabled = renderer.xr.enabled\n      const currentShadowAutoUpdate = renderer.shadowMap.autoUpdate\n\n      scope.visible = false\n\n      renderer.xr.enabled = false // Avoid camera modification and recursion\n      renderer.shadowMap.autoUpdate = false // Avoid re-computing shadows\n\n      renderer.setRenderTarget(renderTarget)\n\n      renderer.state.buffers.depth.setMask(true) // make sure the depth buffer is writable so it can be properly cleared, see #18897\n\n      if (renderer.autoClear === false) renderer.clear()\n      renderer.render(scene, mirrorCamera)\n\n      scope.visible = true\n\n      renderer.xr.enabled = currentXrEnabled\n      renderer.shadowMap.autoUpdate = currentShadowAutoUpdate\n\n      renderer.setRenderTarget(currentRenderTarget)\n\n      // Restore viewport\n\n      const viewport = camera.viewport\n\n      if (viewport !== undefined) {\n        renderer.state.viewport(viewport)\n      }\n    }\n  }\n}\n\nexport { Water }\n"], "names": [], "mappings": ";;AAuBA,MAAM,cAAc,KAAK;AAAA,EACvB,YAAY,UAAU,UAAU,IAAI;AAClC,UAAM,QAAQ;AAEd,SAAK,UAAU;AAEf,UAAM,QAAQ;AAEd,UAAM,eAAe,QAAQ,iBAAiB,SAAY,QAAQ,eAAe;AACjF,UAAM,gBAAgB,QAAQ,kBAAkB,SAAY,QAAQ,gBAAgB;AAEpF,UAAM,WAAW,QAAQ,aAAa,SAAY,QAAQ,WAAW;AACrE,UAAM,QAAQ,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAC5D,UAAM,OAAO,QAAQ,SAAS,SAAY,QAAQ,OAAO;AACzD,UAAM,gBAAgB,QAAQ,iBAAiB,SAAY,QAAQ,eAAe;AAClF,UAAM,eAAe,QAAQ,iBAAiB,SAAY,QAAQ,eAAe,IAAI,QAAQ,SAAS,SAAS,CAAG;AAClH,UAAM,WAAW,IAAI,MAAM,QAAQ,aAAa,SAAY,QAAQ,WAAW,QAAQ;AACvF,UAAM,aAAa,IAAI,MAAM,QAAQ,eAAe,SAAY,QAAQ,aAAa,OAAQ;AAC7F,UAAM,MAAM,QAAQ,QAAQ,SAAY,QAAQ,MAAM,IAAI,QAAQ,GAAG,GAAG,CAAC;AACzE,UAAM,kBAAkB,QAAQ,oBAAoB,SAAY,QAAQ,kBAAkB;AAC1F,UAAM,OAAO,QAAQ,SAAS,SAAY,QAAQ,OAAO;AACzD,UAAM,MAAM,QAAQ,QAAQ,SAAY,QAAQ,MAAM;AAItD,UAAM,cAAc,IAAI,MAAO;AAC/B,UAAM,SAAS,IAAI,QAAS;AAC5B,UAAM,sBAAsB,IAAI,QAAS;AACzC,UAAM,sBAAsB,IAAI,QAAS;AACzC,UAAM,iBAAiB,IAAI,QAAS;AACpC,UAAM,iBAAiB,IAAI,QAAQ,GAAG,GAAG,EAAE;AAC3C,UAAM,YAAY,IAAI,QAAS;AAE/B,UAAM,OAAO,IAAI,QAAS;AAC1B,UAAM,SAAS,IAAI,QAAS;AAC5B,UAAM,IAAI,IAAI,QAAS;AAEvB,UAAM,gBAAgB,IAAI,QAAS;AAEnC,UAAM,eAAe,IAAI,kBAAmB;AAE5C,UAAM,eAAe,IAAI,kBAAkB,cAAc,aAAa;AAEtE,UAAM,eAAe;AAAA,MACnB,UAAU,cAAc,MAAM;AAAA,QAC5B,YAAY,KAAK;AAAA,QACjB,YAAY,QAAQ;AAAA,QACpB;AAAA,UACE,eAAe,EAAE,OAAO,KAAM;AAAA,UAC9B,eAAe,EAAE,OAAO,KAAM;AAAA,UAC9B,OAAO,EAAE,OAAO,EAAK;AAAA,UACrB,MAAM,EAAE,OAAO,EAAK;AAAA,UACpB,MAAM,EAAE,OAAO,EAAK;AAAA,UACpB,iBAAiB,EAAE,OAAO,GAAM;AAAA,UAChC,eAAe,EAAE,OAAO,IAAI,UAAW;AAAA,UACvC,UAAU,EAAE,OAAO,IAAI,MAAM,OAAQ,EAAG;AAAA,UACxC,cAAc,EAAE,OAAO,IAAI,QAAQ,SAAS,SAAS,CAAC,EAAG;AAAA,UACzD,KAAK,EAAE,OAAO,IAAI,UAAW;AAAA,UAC7B,YAAY,EAAE,OAAO,IAAI,MAAM,OAAQ,EAAG;AAAA,QAC3C;AAAA,MACT,CAAO;AAAA,MAED;AAAA;AAAA,QAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA0BzB;AAAA;AAAA,QAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAsEhB,WAAW,MAAM,wBAAwB;AAAA;AAAA;AAAA;AAAA,IAGrD;AAED,UAAM,WAAW,IAAI,eAAe;AAAA,MAClC,gBAAgB,aAAa;AAAA,MAC7B,cAAc,aAAa;AAAA,MAC3B,UAAU,cAAc,MAAM,aAAa,QAAQ;AAAA,MACnD,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,IACN,CAAK;AAED,aAAS,SAAS,eAAe,EAAE,QAAQ,aAAa;AACxD,aAAS,SAAS,eAAe,EAAE,QAAQ;AAC3C,aAAS,SAAS,OAAO,EAAE,QAAQ;AACnC,aAAS,SAAS,MAAM,EAAE,QAAQ;AAClC,aAAS,SAAS,eAAe,EAAE,QAAQ;AAC3C,aAAS,SAAS,UAAU,EAAE,QAAQ;AACtC,aAAS,SAAS,YAAY,EAAE,QAAQ;AACxC,aAAS,SAAS,cAAc,EAAE,QAAQ;AAC1C,aAAS,SAAS,iBAAiB,EAAE,QAAQ;AAE7C,aAAS,SAAS,KAAK,EAAE,QAAQ;AAEjC,UAAM,WAAW;AAEjB,UAAM,iBAAiB,SAAU,UAAU,OAAO,QAAQ;AACxD,0BAAoB,sBAAsB,MAAM,WAAW;AAC3D,0BAAoB,sBAAsB,OAAO,WAAW;AAE5D,qBAAe,gBAAgB,MAAM,WAAW;AAEhD,aAAO,IAAI,GAAG,GAAG,CAAC;AAClB,aAAO,aAAa,cAAc;AAElC,WAAK,WAAW,qBAAqB,mBAAmB;AAIxD,UAAI,KAAK,IAAI,MAAM,IAAI;AAAG;AAE1B,WAAK,QAAQ,MAAM,EAAE,OAAQ;AAC7B,WAAK,IAAI,mBAAmB;AAE5B,qBAAe,gBAAgB,OAAO,WAAW;AAEjD,qBAAe,IAAI,GAAG,GAAG,EAAE;AAC3B,qBAAe,aAAa,cAAc;AAC1C,qBAAe,IAAI,mBAAmB;AAEtC,aAAO,WAAW,qBAAqB,cAAc;AACrD,aAAO,QAAQ,MAAM,EAAE,OAAQ;AAC/B,aAAO,IAAI,mBAAmB;AAE9B,mBAAa,SAAS,KAAK,IAAI;AAC/B,mBAAa,GAAG,IAAI,GAAG,GAAG,CAAC;AAC3B,mBAAa,GAAG,aAAa,cAAc;AAC3C,mBAAa,GAAG,QAAQ,MAAM;AAC9B,mBAAa,OAAO,MAAM;AAE1B,mBAAa,MAAM,OAAO;AAE1B,mBAAa,kBAAmB;AAChC,mBAAa,iBAAiB,KAAK,OAAO,gBAAgB;AAG1D,oBAAc,IAAI,KAAK,GAAK,GAAK,KAAK,GAAK,KAAK,GAAK,KAAK,GAAK,GAAK,KAAK,KAAK,GAAK,GAAK,GAAK,CAAG;AAChG,oBAAc,SAAS,aAAa,gBAAgB;AACpD,oBAAc,SAAS,aAAa,kBAAkB;AAItD,kBAAY,8BAA8B,QAAQ,mBAAmB;AACrE,kBAAY,aAAa,aAAa,kBAAkB;AAExD,gBAAU,IAAI,YAAY,OAAO,GAAG,YAAY,OAAO,GAAG,YAAY,OAAO,GAAG,YAAY,QAAQ;AAEpG,YAAM,mBAAmB,aAAa;AAEtC,QAAE,KAAK,KAAK,KAAK,UAAU,CAAC,IAAI,iBAAiB,SAAS,CAAC,KAAK,iBAAiB,SAAS,CAAC;AAC3F,QAAE,KAAK,KAAK,KAAK,UAAU,CAAC,IAAI,iBAAiB,SAAS,CAAC,KAAK,iBAAiB,SAAS,CAAC;AAC3F,QAAE,IAAI;AACN,QAAE,KAAK,IAAM,iBAAiB,SAAS,EAAE,KAAK,iBAAiB,SAAS,EAAE;AAG1E,gBAAU,eAAe,IAAM,UAAU,IAAI,CAAC,CAAC;AAG/C,uBAAiB,SAAS,CAAC,IAAI,UAAU;AACzC,uBAAiB,SAAS,CAAC,IAAI,UAAU;AACzC,uBAAiB,SAAS,EAAE,IAAI,UAAU,IAAI,IAAM;AACpD,uBAAiB,SAAS,EAAE,IAAI,UAAU;AAE1C,UAAI,sBAAsB,OAAO,WAAW;AAI5C,YAAM,sBAAsB,SAAS,gBAAiB;AAEtD,YAAM,mBAAmB,SAAS,GAAG;AACrC,YAAM,0BAA0B,SAAS,UAAU;AAEnD,YAAM,UAAU;AAEhB,eAAS,GAAG,UAAU;AACtB,eAAS,UAAU,aAAa;AAEhC,eAAS,gBAAgB,YAAY;AAErC,eAAS,MAAM,QAAQ,MAAM,QAAQ,IAAI;AAEzC,UAAI,SAAS,cAAc;AAAO,iBAAS,MAAO;AAClD,eAAS,OAAO,OAAO,YAAY;AAEnC,YAAM,UAAU;AAEhB,eAAS,GAAG,UAAU;AACtB,eAAS,UAAU,aAAa;AAEhC,eAAS,gBAAgB,mBAAmB;AAI5C,YAAM,WAAW,OAAO;AAExB,UAAI,aAAa,QAAW;AAC1B,iBAAS,MAAM,SAAS,QAAQ;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACH;"}