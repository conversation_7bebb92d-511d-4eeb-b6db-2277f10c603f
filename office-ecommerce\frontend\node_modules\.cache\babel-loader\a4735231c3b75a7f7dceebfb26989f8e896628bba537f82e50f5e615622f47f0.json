{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { applyProps } from '@react-three/fiber';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport mergeRefs from 'react-merge-refs';\nconst Lightformer = /*#__PURE__*/React.forwardRef(({\n  args,\n  map,\n  toneMapped = false,\n  color = 'white',\n  form: Form = 'rect',\n  intensity = 1,\n  scale = 1,\n  target,\n  children,\n  ...props\n}, forwardRef) => {\n  // Apply emissive power\n  const ref = React.useRef(null);\n  React.useLayoutEffect(() => {\n    if (!children && !props.material) {\n      applyProps(ref.current.material, {\n        color\n      });\n      ref.current.material.color.multiplyScalar(intensity);\n    }\n  }, [color, intensity, children, props.material]); // Target light\n\n  React.useLayoutEffect(() => {\n    if (target) ref.current.lookAt(Array.isArray(target) ? new THREE.Vector3(...target) : target);\n  }, [target]); // Fix 2-dimensional scale\n\n  scale = Array.isArray(scale) && scale.length === 2 ? [scale[0], scale[1], 1] : scale;\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: mergeRefs([ref, forwardRef]),\n    scale: scale\n  }, props), Form === 'circle' ? /*#__PURE__*/React.createElement(\"ringGeometry\", {\n    args: [0, 1, 64]\n  }) : Form === 'ring' ? /*#__PURE__*/React.createElement(\"ringGeometry\", {\n    args: [0.5, 1, 64]\n  }) : Form === 'rect' ? /*#__PURE__*/React.createElement(\"planeGeometry\", null) : /*#__PURE__*/React.createElement(Form, {\n    args: args\n  }), children ? children : !props.material ? /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    toneMapped: toneMapped,\n    map: map,\n    side: THREE.DoubleSide\n  }) : null);\n});\nexport { Lightformer };", "map": {"version": 3, "names": ["_extends", "applyProps", "React", "THREE", "mergeRefs", "Lightformer", "forwardRef", "args", "map", "toneMapped", "color", "form", "Form", "intensity", "scale", "target", "children", "props", "ref", "useRef", "useLayoutEffect", "material", "current", "multiplyScalar", "lookAt", "Array", "isArray", "Vector3", "length", "createElement", "side", "DoubleSide"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Lightformer.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { applyProps } from '@react-three/fiber';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport mergeRefs from 'react-merge-refs';\n\nconst Lightformer = /*#__PURE__*/React.forwardRef(({\n  args,\n  map,\n  toneMapped = false,\n  color = 'white',\n  form: Form = 'rect',\n  intensity = 1,\n  scale = 1,\n  target,\n  children,\n  ...props\n}, forwardRef) => {\n  // Apply emissive power\n  const ref = React.useRef(null);\n  React.useLayoutEffect(() => {\n    if (!children && !props.material) {\n      applyProps(ref.current.material, {\n        color\n      });\n      ref.current.material.color.multiplyScalar(intensity);\n    }\n  }, [color, intensity, children, props.material]); // Target light\n\n  React.useLayoutEffect(() => {\n    if (target) ref.current.lookAt(Array.isArray(target) ? new THREE.Vector3(...target) : target);\n  }, [target]); // Fix 2-dimensional scale\n\n  scale = Array.isArray(scale) && scale.length === 2 ? [scale[0], scale[1], 1] : scale;\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: mergeRefs([ref, forwardRef]),\n    scale: scale\n  }, props), Form === 'circle' ? /*#__PURE__*/React.createElement(\"ringGeometry\", {\n    args: [0, 1, 64]\n  }) : Form === 'ring' ? /*#__PURE__*/React.createElement(\"ringGeometry\", {\n    args: [0.5, 1, 64]\n  }) : Form === 'rect' ? /*#__PURE__*/React.createElement(\"planeGeometry\", null) : /*#__PURE__*/React.createElement(Form, {\n    args: args\n  }), children ? children : !props.material ? /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    toneMapped: toneMapped,\n    map: map,\n    side: THREE.DoubleSide\n  }) : null);\n});\n\nexport { Lightformer };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,kBAAkB;AAExC,MAAMC,WAAW,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,CAAC;EACjDC,IAAI;EACJC,GAAG;EACHC,UAAU,GAAG,KAAK;EAClBC,KAAK,GAAG,OAAO;EACfC,IAAI,EAAEC,IAAI,GAAG,MAAM;EACnBC,SAAS,GAAG,CAAC;EACbC,KAAK,GAAG,CAAC;EACTC,MAAM;EACNC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEX,UAAU,KAAK;EAChB;EACA,MAAMY,GAAG,GAAGhB,KAAK,CAACiB,MAAM,CAAC,IAAI,CAAC;EAC9BjB,KAAK,CAACkB,eAAe,CAAC,MAAM;IAC1B,IAAI,CAACJ,QAAQ,IAAI,CAACC,KAAK,CAACI,QAAQ,EAAE;MAChCpB,UAAU,CAACiB,GAAG,CAACI,OAAO,CAACD,QAAQ,EAAE;QAC/BX;MACF,CAAC,CAAC;MACFQ,GAAG,CAACI,OAAO,CAACD,QAAQ,CAACX,KAAK,CAACa,cAAc,CAACV,SAAS,CAAC;IACtD;EACF,CAAC,EAAE,CAACH,KAAK,EAAEG,SAAS,EAAEG,QAAQ,EAAEC,KAAK,CAACI,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAElDnB,KAAK,CAACkB,eAAe,CAAC,MAAM;IAC1B,IAAIL,MAAM,EAAEG,GAAG,CAACI,OAAO,CAACE,MAAM,CAACC,KAAK,CAACC,OAAO,CAACX,MAAM,CAAC,GAAG,IAAIZ,KAAK,CAACwB,OAAO,CAAC,GAAGZ,MAAM,CAAC,GAAGA,MAAM,CAAC;EAC/F,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEdD,KAAK,GAAGW,KAAK,CAACC,OAAO,CAACZ,KAAK,CAAC,IAAIA,KAAK,CAACc,MAAM,KAAK,CAAC,GAAG,CAACd,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGA,KAAK;EACpF,OAAO,aAAaZ,KAAK,CAAC2B,aAAa,CAAC,MAAM,EAAE7B,QAAQ,CAAC;IACvDkB,GAAG,EAAEd,SAAS,CAAC,CAACc,GAAG,EAAEZ,UAAU,CAAC,CAAC;IACjCQ,KAAK,EAAEA;EACT,CAAC,EAAEG,KAAK,CAAC,EAAEL,IAAI,KAAK,QAAQ,GAAG,aAAaV,KAAK,CAAC2B,aAAa,CAAC,cAAc,EAAE;IAC9EtB,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;EACjB,CAAC,CAAC,GAAGK,IAAI,KAAK,MAAM,GAAG,aAAaV,KAAK,CAAC2B,aAAa,CAAC,cAAc,EAAE;IACtEtB,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;EACnB,CAAC,CAAC,GAAGK,IAAI,KAAK,MAAM,GAAG,aAAaV,KAAK,CAAC2B,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,GAAG,aAAa3B,KAAK,CAAC2B,aAAa,CAACjB,IAAI,EAAE;IACtHL,IAAI,EAAEA;EACR,CAAC,CAAC,EAAES,QAAQ,GAAGA,QAAQ,GAAG,CAACC,KAAK,CAACI,QAAQ,GAAG,aAAanB,KAAK,CAAC2B,aAAa,CAAC,mBAAmB,EAAE;IAChGpB,UAAU,EAAEA,UAAU;IACtBD,GAAG,EAAEA,GAAG;IACRsB,IAAI,EAAE3B,KAAK,CAAC4B;EACd,CAAC,CAAC,GAAG,IAAI,CAAC;AACZ,CAAC,CAAC;AAEF,SAAS1B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}