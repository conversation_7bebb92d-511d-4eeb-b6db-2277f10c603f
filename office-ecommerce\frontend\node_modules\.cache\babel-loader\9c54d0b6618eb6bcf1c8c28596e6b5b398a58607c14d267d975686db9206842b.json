{"ast": null, "code": "import * as React from 'react';\nimport { addEffect, addAfterEffect } from '@react-three/fiber';\nfunction useIntersect(onChange) {\n  const ref = React.useRef(null);\n  const check = React.useRef(false);\n  const temp = React.useRef(false);\n  const callback = React.useRef(onChange);\n  React.useLayoutEffect(() => void (callback.current = onChange), [onChange]);\n  React.useEffect(() => {\n    const obj = ref.current;\n    if (obj) {\n      // Stamp out frustum check pre-emptively\n      const unsub1 = addEffect(() => {\n        check.current = false;\n        return true;\n      }); // If the object is inside the frustum three will call onRender\n\n      const oldOnRender = obj.onBeforeRender;\n      obj.onBeforeRender = () => check.current = true; // Compare the check value against the temp value, if it differs set state\n\n      const unsub2 = addAfterEffect(() => {\n        if (check.current !== temp.current) callback.current == null ? void 0 : callback.current(temp.current = check.current);\n        return true;\n      });\n      return () => {\n        obj.onBeforeRender = oldOnRender;\n        unsub1();\n        unsub2();\n      };\n    }\n  }, []);\n  return ref;\n}\nexport { useIntersect };", "map": {"version": 3, "names": ["React", "addEffect", "addAfterEffect", "useIntersect", "onChange", "ref", "useRef", "check", "temp", "callback", "useLayoutEffect", "current", "useEffect", "obj", "unsub1", "old<PERSON>n<PERSON><PERSON>", "onBeforeRender", "unsub2"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useIntersect.js"], "sourcesContent": ["import * as React from 'react';\nimport { addEffect, addAfterEffect } from '@react-three/fiber';\n\nfunction useIntersect(onChange) {\n  const ref = React.useRef(null);\n  const check = React.useRef(false);\n  const temp = React.useRef(false);\n  const callback = React.useRef(onChange);\n  React.useLayoutEffect(() => void (callback.current = onChange), [onChange]);\n  React.useEffect(() => {\n    const obj = ref.current;\n\n    if (obj) {\n      // Stamp out frustum check pre-emptively\n      const unsub1 = addEffect(() => {\n        check.current = false;\n        return true;\n      }); // If the object is inside the frustum three will call onRender\n\n      const oldOnRender = obj.onBeforeRender;\n\n      obj.onBeforeRender = () => check.current = true; // Compare the check value against the temp value, if it differs set state\n\n\n      const unsub2 = addAfterEffect(() => {\n        if (check.current !== temp.current) callback.current == null ? void 0 : callback.current(temp.current = check.current);\n        return true;\n      });\n      return () => {\n        obj.onBeforeRender = oldOnRender;\n        unsub1();\n        unsub2();\n      };\n    }\n  }, []);\n  return ref;\n}\n\nexport { useIntersect };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,cAAc,QAAQ,oBAAoB;AAE9D,SAASC,YAAYA,CAACC,QAAQ,EAAE;EAC9B,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,KAAK,GAAGP,KAAK,CAACM,MAAM,CAAC,KAAK,CAAC;EACjC,MAAME,IAAI,GAAGR,KAAK,CAACM,MAAM,CAAC,KAAK,CAAC;EAChC,MAAMG,QAAQ,GAAGT,KAAK,CAACM,MAAM,CAACF,QAAQ,CAAC;EACvCJ,KAAK,CAACU,eAAe,CAAC,MAAM,MAAMD,QAAQ,CAACE,OAAO,GAAGP,QAAQ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAC3EJ,KAAK,CAACY,SAAS,CAAC,MAAM;IACpB,MAAMC,GAAG,GAAGR,GAAG,CAACM,OAAO;IAEvB,IAAIE,GAAG,EAAE;MACP;MACA,MAAMC,MAAM,GAAGb,SAAS,CAAC,MAAM;QAC7BM,KAAK,CAACI,OAAO,GAAG,KAAK;QACrB,OAAO,IAAI;MACb,CAAC,CAAC,CAAC,CAAC;;MAEJ,MAAMI,WAAW,GAAGF,GAAG,CAACG,cAAc;MAEtCH,GAAG,CAACG,cAAc,GAAG,MAAMT,KAAK,CAACI,OAAO,GAAG,IAAI,CAAC,CAAC;;MAGjD,MAAMM,MAAM,GAAGf,cAAc,CAAC,MAAM;QAClC,IAAIK,KAAK,CAACI,OAAO,KAAKH,IAAI,CAACG,OAAO,EAAEF,QAAQ,CAACE,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGF,QAAQ,CAACE,OAAO,CAACH,IAAI,CAACG,OAAO,GAAGJ,KAAK,CAACI,OAAO,CAAC;QACtH,OAAO,IAAI;MACb,CAAC,CAAC;MACF,OAAO,MAAM;QACXE,GAAG,CAACG,cAAc,GAAGD,WAAW;QAChCD,MAAM,CAAC,CAAC;QACRG,MAAM,CAAC,CAAC;MACV,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAOZ,GAAG;AACZ;AAEA,SAASF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}