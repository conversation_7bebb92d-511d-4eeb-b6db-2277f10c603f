{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\nimport { useFBO } from './useFBO.js';\nconst isFunction = node => typeof node === 'function';\nconst PerspectiveCamera = /*#__PURE__*/React.forwardRef(({\n  envMap,\n  resolution = 256,\n  frames = Infinity,\n  makeDefault,\n  children,\n  ...props\n}, ref) => {\n  const set = useThree(({\n    set\n  }) => set);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const size = useThree(({\n    size\n  }) => size);\n  const cameraRef = React.useRef(null);\n  const groupRef = React.useRef(null);\n  const fbo = useFBO(resolution);\n  React.useLayoutEffect(() => {\n    if (!props.manual) {\n      cameraRef.current.aspect = size.width / size.height;\n    }\n  }, [size, props]);\n  React.useLayoutEffect(() => {\n    cameraRef.current.updateProjectionMatrix();\n  });\n  let count = 0;\n  let oldEnvMap = null;\n  const functional = isFunction(children);\n  useFrame(state => {\n    if (functional && (frames === Infinity || count < frames)) {\n      groupRef.current.visible = false;\n      state.gl.setRenderTarget(fbo);\n      oldEnvMap = state.scene.background;\n      if (envMap) state.scene.background = envMap;\n      state.gl.render(state.scene, cameraRef.current);\n      state.scene.background = oldEnvMap;\n      state.gl.setRenderTarget(null);\n      groupRef.current.visible = true;\n      count++;\n    }\n  });\n  React.useLayoutEffect(() => {\n    if (makeDefault) {\n      const oldCam = camera;\n      set(() => ({\n        camera: cameraRef.current\n      }));\n      return () => set(() => ({\n        camera: oldCam\n      }));\n    } // The camera should not be part of the dependency list because this components camera is a stable reference\n    // that must exchange the default, and clean up after itself on unmount.\n  }, [cameraRef, makeDefault, set]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"perspectiveCamera\", _extends({\n    ref: mergeRefs([cameraRef, ref])\n  }, props), !functional && children), /*#__PURE__*/React.createElement(\"group\", {\n    ref: groupRef\n  }, functional && children(fbo.texture)));\n});\nexport { PerspectiveCamera };", "map": {"version": 3, "names": ["_extends", "React", "useThree", "useFrame", "mergeRefs", "useFBO", "isFunction", "node", "PerspectiveCamera", "forwardRef", "envMap", "resolution", "frames", "Infinity", "makeDefault", "children", "props", "ref", "set", "camera", "size", "cameraRef", "useRef", "groupRef", "fbo", "useLayoutEffect", "manual", "current", "aspect", "width", "height", "updateProjectionMatrix", "count", "oldEnvMap", "functional", "state", "visible", "gl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scene", "background", "render", "oldCam", "createElement", "Fragment", "texture"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/PerspectiveCamera.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\nimport { useFBO } from './useFBO.js';\n\nconst isFunction = node => typeof node === 'function';\n\nconst PerspectiveCamera = /*#__PURE__*/React.forwardRef(({\n  envMap,\n  resolution = 256,\n  frames = Infinity,\n  makeDefault,\n  children,\n  ...props\n}, ref) => {\n  const set = useThree(({\n    set\n  }) => set);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const size = useThree(({\n    size\n  }) => size);\n  const cameraRef = React.useRef(null);\n  const groupRef = React.useRef(null);\n  const fbo = useFBO(resolution);\n  React.useLayoutEffect(() => {\n    if (!props.manual) {\n      cameraRef.current.aspect = size.width / size.height;\n    }\n  }, [size, props]);\n  React.useLayoutEffect(() => {\n    cameraRef.current.updateProjectionMatrix();\n  });\n  let count = 0;\n  let oldEnvMap = null;\n  const functional = isFunction(children);\n  useFrame(state => {\n    if (functional && (frames === Infinity || count < frames)) {\n      groupRef.current.visible = false;\n      state.gl.setRenderTarget(fbo);\n      oldEnvMap = state.scene.background;\n      if (envMap) state.scene.background = envMap;\n      state.gl.render(state.scene, cameraRef.current);\n      state.scene.background = oldEnvMap;\n      state.gl.setRenderTarget(null);\n      groupRef.current.visible = true;\n      count++;\n    }\n  });\n  React.useLayoutEffect(() => {\n    if (makeDefault) {\n      const oldCam = camera;\n      set(() => ({\n        camera: cameraRef.current\n      }));\n      return () => set(() => ({\n        camera: oldCam\n      }));\n    } // The camera should not be part of the dependency list because this components camera is a stable reference\n    // that must exchange the default, and clean up after itself on unmount.\n\n  }, [cameraRef, makeDefault, set]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"perspectiveCamera\", _extends({\n    ref: mergeRefs([cameraRef, ref])\n  }, props), !functional && children), /*#__PURE__*/React.createElement(\"group\", {\n    ref: groupRef\n  }, functional && children(fbo.texture)));\n});\n\nexport { PerspectiveCamera };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,OAAOC,SAAS,MAAM,kBAAkB;AACxC,SAASC,MAAM,QAAQ,aAAa;AAEpC,MAAMC,UAAU,GAAGC,IAAI,IAAI,OAAOA,IAAI,KAAK,UAAU;AAErD,MAAMC,iBAAiB,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAC;EACvDC,MAAM;EACNC,UAAU,GAAG,GAAG;EAChBC,MAAM,GAAGC,QAAQ;EACjBC,WAAW;EACXC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,GAAG,GAAGhB,QAAQ,CAAC,CAAC;IACpBgB;EACF,CAAC,KAAKA,GAAG,CAAC;EACV,MAAMC,MAAM,GAAGjB,QAAQ,CAAC,CAAC;IACvBiB;EACF,CAAC,KAAKA,MAAM,CAAC;EACb,MAAMC,IAAI,GAAGlB,QAAQ,CAAC,CAAC;IACrBkB;EACF,CAAC,KAAKA,IAAI,CAAC;EACX,MAAMC,SAAS,GAAGpB,KAAK,CAACqB,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMC,QAAQ,GAAGtB,KAAK,CAACqB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAME,GAAG,GAAGnB,MAAM,CAACM,UAAU,CAAC;EAC9BV,KAAK,CAACwB,eAAe,CAAC,MAAM;IAC1B,IAAI,CAACT,KAAK,CAACU,MAAM,EAAE;MACjBL,SAAS,CAACM,OAAO,CAACC,MAAM,GAAGR,IAAI,CAACS,KAAK,GAAGT,IAAI,CAACU,MAAM;IACrD;EACF,CAAC,EAAE,CAACV,IAAI,EAAEJ,KAAK,CAAC,CAAC;EACjBf,KAAK,CAACwB,eAAe,CAAC,MAAM;IAC1BJ,SAAS,CAACM,OAAO,CAACI,sBAAsB,CAAC,CAAC;EAC5C,CAAC,CAAC;EACF,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,SAAS,GAAG,IAAI;EACpB,MAAMC,UAAU,GAAG5B,UAAU,CAACS,QAAQ,CAAC;EACvCZ,QAAQ,CAACgC,KAAK,IAAI;IAChB,IAAID,UAAU,KAAKtB,MAAM,KAAKC,QAAQ,IAAImB,KAAK,GAAGpB,MAAM,CAAC,EAAE;MACzDW,QAAQ,CAACI,OAAO,CAACS,OAAO,GAAG,KAAK;MAChCD,KAAK,CAACE,EAAE,CAACC,eAAe,CAACd,GAAG,CAAC;MAC7BS,SAAS,GAAGE,KAAK,CAACI,KAAK,CAACC,UAAU;MAClC,IAAI9B,MAAM,EAAEyB,KAAK,CAACI,KAAK,CAACC,UAAU,GAAG9B,MAAM;MAC3CyB,KAAK,CAACE,EAAE,CAACI,MAAM,CAACN,KAAK,CAACI,KAAK,EAAElB,SAAS,CAACM,OAAO,CAAC;MAC/CQ,KAAK,CAACI,KAAK,CAACC,UAAU,GAAGP,SAAS;MAClCE,KAAK,CAACE,EAAE,CAACC,eAAe,CAAC,IAAI,CAAC;MAC9Bf,QAAQ,CAACI,OAAO,CAACS,OAAO,GAAG,IAAI;MAC/BJ,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACF/B,KAAK,CAACwB,eAAe,CAAC,MAAM;IAC1B,IAAIX,WAAW,EAAE;MACf,MAAM4B,MAAM,GAAGvB,MAAM;MACrBD,GAAG,CAAC,OAAO;QACTC,MAAM,EAAEE,SAAS,CAACM;MACpB,CAAC,CAAC,CAAC;MACH,OAAO,MAAMT,GAAG,CAAC,OAAO;QACtBC,MAAM,EAAEuB;MACV,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IACF;EAEF,CAAC,EAAE,CAACrB,SAAS,EAAEP,WAAW,EAAEI,GAAG,CAAC,CAAC;EACjC,OAAO,aAAajB,KAAK,CAAC0C,aAAa,CAAC1C,KAAK,CAAC2C,QAAQ,EAAE,IAAI,EAAE,aAAa3C,KAAK,CAAC0C,aAAa,CAAC,mBAAmB,EAAE3C,QAAQ,CAAC;IAC3HiB,GAAG,EAAEb,SAAS,CAAC,CAACiB,SAAS,EAAEJ,GAAG,CAAC;EACjC,CAAC,EAAED,KAAK,CAAC,EAAE,CAACkB,UAAU,IAAInB,QAAQ,CAAC,EAAE,aAAad,KAAK,CAAC0C,aAAa,CAAC,OAAO,EAAE;IAC7E1B,GAAG,EAAEM;EACP,CAAC,EAAEW,UAAU,IAAInB,QAAQ,CAACS,GAAG,CAACqB,OAAO,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC;AAEF,SAASrC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}