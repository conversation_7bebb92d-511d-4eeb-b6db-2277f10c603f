{"ast": null, "code": "import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { Line } from '../../core/Line.js';\nimport { Html } from '../Html.js';\nimport { context } from './context.js';\nconst decomposeIntoBasis = (e1, e2, offset) => {\n  const i1 = Math.abs(e1.x) >= Math.abs(e1.y) && Math.abs(e1.x) >= Math.abs(e1.z) ? 0 : Math.abs(e1.y) >= Math.abs(e1.x) && Math.abs(e1.y) >= Math.abs(e1.z) ? 1 : 2;\n  const e2DegrowthOrder = [0, 1, 2].sort((a, b) => Math.abs(e2.getComponent(b)) - Math.abs(e2.getComponent(a)));\n  const i2 = i1 === e2DegrowthOrder[0] ? e2DegrowthOrder[1] : e2DegrowthOrder[0];\n  const a1 = e1.getComponent(i1);\n  const a2 = e1.getComponent(i2);\n  const b1 = e2.getComponent(i1);\n  const b2 = e2.getComponent(i2);\n  const c1 = offset.getComponent(i1);\n  const c2 = offset.getComponent(i2);\n  const y = (c2 - c1 * (a2 / a1)) / (b2 - b1 * (a2 / a1));\n  const x = (c1 - y * b1) / a1;\n  return [x, y];\n};\nconst ray = new THREE.Ray();\nconst intersection = new THREE.Vector3();\nconst offsetMatrix = new THREE.Matrix4();\nconst PlaneSlider = ({\n  dir1,\n  dir2,\n  axis\n}) => {\n  const {\n    translation,\n    translationLimits,\n    annotations,\n    annotationsClass,\n    depthTest,\n    scale,\n    lineWidth,\n    fixed,\n    axisColors,\n    hoveredColor,\n    opacity,\n    onDragStart,\n    onDrag,\n    onDragEnd,\n    userData\n  } = React.useContext(context); // @ts-expect-error new in @react-three/fiber@7.0.5\n\n  const camControls = useThree(state => state.controls);\n  const divRef = React.useRef(null);\n  const objRef = React.useRef(null);\n  const clickInfo = React.useRef(null);\n  const offsetX0 = React.useRef(0);\n  const offsetY0 = React.useRef(0);\n  const [isHovered, setIsHovered] = React.useState(false);\n  const onPointerDown = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.innerText = `${translation.current[(axis + 1) % 3].toFixed(2)}, ${translation.current[(axis + 2) % 3].toFixed(2)}`;\n      divRef.current.style.display = 'block';\n    }\n    e.stopPropagation();\n    const clickPoint = e.point.clone();\n    const origin = new THREE.Vector3().setFromMatrixPosition(objRef.current.matrixWorld);\n    const e1 = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 0).normalize();\n    const e2 = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 1).normalize();\n    const normal = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 2).normalize();\n    const plane = new THREE.Plane().setFromNormalAndCoplanarPoint(normal, origin);\n    clickInfo.current = {\n      clickPoint,\n      e1,\n      e2,\n      plane\n    };\n    offsetX0.current = translation.current[(axis + 1) % 3];\n    offsetY0.current = translation.current[(axis + 2) % 3];\n    onDragStart({\n      component: 'Slider',\n      axis,\n      origin,\n      directions: [e1, e2, normal]\n    });\n    camControls && (camControls.enabled = false); // @ts-ignore\n\n    e.target.setPointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragStart, axis]);\n  const onPointerMove = React.useCallback(e => {\n    e.stopPropagation();\n    if (!isHovered) setIsHovered(true);\n    if (clickInfo.current) {\n      const {\n        clickPoint,\n        e1,\n        e2,\n        plane\n      } = clickInfo.current;\n      const [minX, maxX] = (translationLimits == null ? void 0 : translationLimits[(axis + 1) % 3]) || [undefined, undefined];\n      const [minY, maxY] = (translationLimits == null ? void 0 : translationLimits[(axis + 2) % 3]) || [undefined, undefined];\n      ray.copy(e.ray);\n      ray.intersectPlane(plane, intersection);\n      ray.direction.negate();\n      ray.intersectPlane(plane, intersection);\n      intersection.sub(clickPoint);\n      let [offsetX, offsetY] = decomposeIntoBasis(e1, e2, intersection);\n      /* let offsetY = (intersection.y - (intersection.x * e1.y) / e1.x) / (e2.y - (e2.x * e1.y) / e1.x)\n      let offsetX = (intersection.x - offsetY * e2.x) / e1.x */\n\n      if (minX !== undefined) {\n        offsetX = Math.max(offsetX, minX - offsetX0.current);\n      }\n      if (maxX !== undefined) {\n        offsetX = Math.min(offsetX, maxX - offsetX0.current);\n      }\n      if (minY !== undefined) {\n        offsetY = Math.max(offsetY, minY - offsetY0.current);\n      }\n      if (maxY !== undefined) {\n        offsetY = Math.min(offsetY, maxY - offsetY0.current);\n      }\n      translation.current[(axis + 1) % 3] = offsetX0.current + offsetX;\n      translation.current[(axis + 2) % 3] = offsetY0.current + offsetY;\n      if (annotations) {\n        divRef.current.innerText = `${translation.current[(axis + 1) % 3].toFixed(2)}, ${translation.current[(axis + 2) % 3].toFixed(2)}`;\n      }\n      offsetMatrix.makeTranslation(offsetX * e1.x + offsetY * e2.x, offsetX * e1.y + offsetY * e2.y, offsetX * e1.z + offsetY * e2.z);\n      onDrag(offsetMatrix);\n    }\n  }, [annotations, onDrag, isHovered, translation, translationLimits, axis]);\n  const onPointerUp = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.style.display = 'none';\n    }\n    e.stopPropagation();\n    clickInfo.current = null;\n    onDragEnd();\n    camControls && (camControls.enabled = true); // @ts-ignore\n\n    e.target.releasePointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragEnd]);\n  const onPointerOut = React.useCallback(e => {\n    e.stopPropagation();\n    setIsHovered(false);\n  }, []);\n  const matrixL = React.useMemo(() => {\n    const dir1N = dir1.clone().normalize();\n    const dir2N = dir2.clone().normalize();\n    return new THREE.Matrix4().makeBasis(dir1N, dir2N, dir1N.clone().cross(dir2N));\n  }, [dir1, dir2]);\n  const pos1 = fixed ? 1 / 7 : scale / 7;\n  const length = fixed ? 0.225 : scale * 0.225;\n  const color = isHovered ? hoveredColor : axisColors[axis];\n  const points = React.useMemo(() => [new THREE.Vector3(0, 0, 0), new THREE.Vector3(0, length, 0), new THREE.Vector3(length, length, 0), new THREE.Vector3(length, 0, 0), new THREE.Vector3(0, 0, 0)], [length]);\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: objRef,\n    matrix: matrixL,\n    matrixAutoUpdate: false\n  }, annotations && /*#__PURE__*/React.createElement(Html, {\n    position: [0, 0, 0]\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'none',\n      background: '#151520',\n      color: 'white',\n      padding: '6px 8px',\n      borderRadius: 7,\n      whiteSpace: 'nowrap'\n    },\n    className: annotationsClass,\n    ref: divRef\n  })), /*#__PURE__*/React.createElement(\"group\", {\n    position: [pos1 * 1.7, pos1 * 1.7, 0]\n  }, /*#__PURE__*/React.createElement(\"mesh\", {\n    visible: true,\n    onPointerDown: onPointerDown,\n    onPointerMove: onPointerMove,\n    onPointerUp: onPointerUp,\n    onPointerOut: onPointerOut,\n    scale: length,\n    userData: userData\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    depthTest: depthTest,\n    color: color,\n    polygonOffset: true,\n    polygonOffsetFactor: -10,\n    side: THREE.DoubleSide,\n    fog: false\n  })), /*#__PURE__*/React.createElement(Line, {\n    position: [-length / 2, -length / 2, 0],\n    transparent: true,\n    depthTest: depthTest,\n    points: points,\n    lineWidth: lineWidth,\n    color: color,\n    opacity: opacity,\n    polygonOffset: true,\n    polygonOffsetFactor: -10,\n    userData: userData,\n    fog: false\n  })));\n};\nexport { PlaneSlider };", "map": {"version": 3, "names": ["React", "THREE", "useThree", "Line", "Html", "context", "decomposeIntoBasis", "e1", "e2", "offset", "i1", "Math", "abs", "x", "y", "z", "e2DegrowthOrder", "sort", "a", "b", "getComponent", "i2", "a1", "a2", "b1", "b2", "c1", "c2", "ray", "<PERSON>", "intersection", "Vector3", "offsetMatrix", "Matrix4", "PlaneSlider", "dir1", "dir2", "axis", "translation", "translationLimits", "annotations", "annotationsClass", "depthTest", "scale", "lineWidth", "fixed", "axisColors", "hoveredColor", "opacity", "onDragStart", "onDrag", "onDragEnd", "userData", "useContext", "camControls", "state", "controls", "divRef", "useRef", "objRef", "clickInfo", "offsetX0", "offsetY0", "isHovered", "setIsHovered", "useState", "onPointerDown", "useCallback", "e", "current", "innerText", "toFixed", "style", "display", "stopPropagation", "clickPoint", "point", "clone", "origin", "setFromMatrixPosition", "matrixWorld", "setFromMatrixColumn", "normalize", "normal", "plane", "Plane", "setFromNormalAndCoplanarPoint", "component", "directions", "enabled", "target", "setPointerCapture", "pointerId", "onPointerMove", "minX", "maxX", "undefined", "minY", "maxY", "copy", "intersectPlane", "direction", "negate", "sub", "offsetX", "offsetY", "max", "min", "makeTranslation", "onPointerUp", "releasePointerCapture", "onPointerOut", "matrixL", "useMemo", "dir1N", "dir2N", "makeBasis", "cross", "pos1", "length", "color", "points", "createElement", "ref", "matrix", "matrixAutoUpdate", "position", "background", "padding", "borderRadius", "whiteSpace", "className", "visible", "transparent", "polygonOffset", "polygonOffsetFactor", "side", "DoubleSide", "fog"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/web/pivotControls/PlaneSlider.js"], "sourcesContent": ["import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { Line } from '../../core/Line.js';\nimport { Html } from '../Html.js';\nimport { context } from './context.js';\n\nconst decomposeIntoBasis = (e1, e2, offset) => {\n  const i1 = Math.abs(e1.x) >= Math.abs(e1.y) && Math.abs(e1.x) >= Math.abs(e1.z) ? 0 : Math.abs(e1.y) >= Math.abs(e1.x) && Math.abs(e1.y) >= Math.abs(e1.z) ? 1 : 2;\n  const e2DegrowthOrder = [0, 1, 2].sort((a, b) => Math.abs(e2.getComponent(b)) - Math.abs(e2.getComponent(a)));\n  const i2 = i1 === e2DegrowthOrder[0] ? e2DegrowthOrder[1] : e2DegrowthOrder[0];\n  const a1 = e1.getComponent(i1);\n  const a2 = e1.getComponent(i2);\n  const b1 = e2.getComponent(i1);\n  const b2 = e2.getComponent(i2);\n  const c1 = offset.getComponent(i1);\n  const c2 = offset.getComponent(i2);\n  const y = (c2 - c1 * (a2 / a1)) / (b2 - b1 * (a2 / a1));\n  const x = (c1 - y * b1) / a1;\n  return [x, y];\n};\n\nconst ray = new THREE.Ray();\nconst intersection = new THREE.Vector3();\nconst offsetMatrix = new THREE.Matrix4();\nconst PlaneSlider = ({\n  dir1,\n  dir2,\n  axis\n}) => {\n  const {\n    translation,\n    translationLimits,\n    annotations,\n    annotationsClass,\n    depthTest,\n    scale,\n    lineWidth,\n    fixed,\n    axisColors,\n    hoveredColor,\n    opacity,\n    onDragStart,\n    onDrag,\n    onDragEnd,\n    userData\n  } = React.useContext(context); // @ts-expect-error new in @react-three/fiber@7.0.5\n\n  const camControls = useThree(state => state.controls);\n  const divRef = React.useRef(null);\n  const objRef = React.useRef(null);\n  const clickInfo = React.useRef(null);\n  const offsetX0 = React.useRef(0);\n  const offsetY0 = React.useRef(0);\n  const [isHovered, setIsHovered] = React.useState(false);\n  const onPointerDown = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.innerText = `${translation.current[(axis + 1) % 3].toFixed(2)}, ${translation.current[(axis + 2) % 3].toFixed(2)}`;\n      divRef.current.style.display = 'block';\n    }\n\n    e.stopPropagation();\n    const clickPoint = e.point.clone();\n    const origin = new THREE.Vector3().setFromMatrixPosition(objRef.current.matrixWorld);\n    const e1 = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 0).normalize();\n    const e2 = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 1).normalize();\n    const normal = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 2).normalize();\n    const plane = new THREE.Plane().setFromNormalAndCoplanarPoint(normal, origin);\n    clickInfo.current = {\n      clickPoint,\n      e1,\n      e2,\n      plane\n    };\n    offsetX0.current = translation.current[(axis + 1) % 3];\n    offsetY0.current = translation.current[(axis + 2) % 3];\n    onDragStart({\n      component: 'Slider',\n      axis,\n      origin,\n      directions: [e1, e2, normal]\n    });\n    camControls && (camControls.enabled = false); // @ts-ignore\n\n    e.target.setPointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragStart, axis]);\n  const onPointerMove = React.useCallback(e => {\n    e.stopPropagation();\n    if (!isHovered) setIsHovered(true);\n\n    if (clickInfo.current) {\n      const {\n        clickPoint,\n        e1,\n        e2,\n        plane\n      } = clickInfo.current;\n      const [minX, maxX] = (translationLimits == null ? void 0 : translationLimits[(axis + 1) % 3]) || [undefined, undefined];\n      const [minY, maxY] = (translationLimits == null ? void 0 : translationLimits[(axis + 2) % 3]) || [undefined, undefined];\n      ray.copy(e.ray);\n      ray.intersectPlane(plane, intersection);\n      ray.direction.negate();\n      ray.intersectPlane(plane, intersection);\n      intersection.sub(clickPoint);\n      let [offsetX, offsetY] = decomposeIntoBasis(e1, e2, intersection);\n      /* let offsetY = (intersection.y - (intersection.x * e1.y) / e1.x) / (e2.y - (e2.x * e1.y) / e1.x)\n      let offsetX = (intersection.x - offsetY * e2.x) / e1.x */\n\n      if (minX !== undefined) {\n        offsetX = Math.max(offsetX, minX - offsetX0.current);\n      }\n\n      if (maxX !== undefined) {\n        offsetX = Math.min(offsetX, maxX - offsetX0.current);\n      }\n\n      if (minY !== undefined) {\n        offsetY = Math.max(offsetY, minY - offsetY0.current);\n      }\n\n      if (maxY !== undefined) {\n        offsetY = Math.min(offsetY, maxY - offsetY0.current);\n      }\n\n      translation.current[(axis + 1) % 3] = offsetX0.current + offsetX;\n      translation.current[(axis + 2) % 3] = offsetY0.current + offsetY;\n\n      if (annotations) {\n        divRef.current.innerText = `${translation.current[(axis + 1) % 3].toFixed(2)}, ${translation.current[(axis + 2) % 3].toFixed(2)}`;\n      }\n\n      offsetMatrix.makeTranslation(offsetX * e1.x + offsetY * e2.x, offsetX * e1.y + offsetY * e2.y, offsetX * e1.z + offsetY * e2.z);\n      onDrag(offsetMatrix);\n    }\n  }, [annotations, onDrag, isHovered, translation, translationLimits, axis]);\n  const onPointerUp = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.style.display = 'none';\n    }\n\n    e.stopPropagation();\n    clickInfo.current = null;\n    onDragEnd();\n    camControls && (camControls.enabled = true); // @ts-ignore\n\n    e.target.releasePointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragEnd]);\n  const onPointerOut = React.useCallback(e => {\n    e.stopPropagation();\n    setIsHovered(false);\n  }, []);\n  const matrixL = React.useMemo(() => {\n    const dir1N = dir1.clone().normalize();\n    const dir2N = dir2.clone().normalize();\n    return new THREE.Matrix4().makeBasis(dir1N, dir2N, dir1N.clone().cross(dir2N));\n  }, [dir1, dir2]);\n  const pos1 = fixed ? 1 / 7 : scale / 7;\n  const length = fixed ? 0.225 : scale * 0.225;\n  const color = isHovered ? hoveredColor : axisColors[axis];\n  const points = React.useMemo(() => [new THREE.Vector3(0, 0, 0), new THREE.Vector3(0, length, 0), new THREE.Vector3(length, length, 0), new THREE.Vector3(length, 0, 0), new THREE.Vector3(0, 0, 0)], [length]);\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: objRef,\n    matrix: matrixL,\n    matrixAutoUpdate: false\n  }, annotations && /*#__PURE__*/React.createElement(Html, {\n    position: [0, 0, 0]\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'none',\n      background: '#151520',\n      color: 'white',\n      padding: '6px 8px',\n      borderRadius: 7,\n      whiteSpace: 'nowrap'\n    },\n    className: annotationsClass,\n    ref: divRef\n  })), /*#__PURE__*/React.createElement(\"group\", {\n    position: [pos1 * 1.7, pos1 * 1.7, 0]\n  }, /*#__PURE__*/React.createElement(\"mesh\", {\n    visible: true,\n    onPointerDown: onPointerDown,\n    onPointerMove: onPointerMove,\n    onPointerUp: onPointerUp,\n    onPointerOut: onPointerOut,\n    scale: length,\n    userData: userData\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    depthTest: depthTest,\n    color: color,\n    polygonOffset: true,\n    polygonOffsetFactor: -10,\n    side: THREE.DoubleSide,\n    fog: false\n  })), /*#__PURE__*/React.createElement(Line, {\n    position: [-length / 2, -length / 2, 0],\n    transparent: true,\n    depthTest: depthTest,\n    points: points,\n    lineWidth: lineWidth,\n    color: color,\n    opacity: opacity,\n    polygonOffset: true,\n    polygonOffsetFactor: -10,\n    userData: userData,\n    fog: false\n  })));\n};\n\nexport { PlaneSlider };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,IAAI,QAAQ,oBAAoB;AACzC,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,OAAO,QAAQ,cAAc;AAEtC,MAAMC,kBAAkB,GAAGA,CAACC,EAAE,EAAEC,EAAE,EAAEC,MAAM,KAAK;EAC7C,MAAMC,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACL,EAAE,CAACM,CAAC,CAAC,IAAIF,IAAI,CAACC,GAAG,CAACL,EAAE,CAACO,CAAC,CAAC,IAAIH,IAAI,CAACC,GAAG,CAACL,EAAE,CAACM,CAAC,CAAC,IAAIF,IAAI,CAACC,GAAG,CAACL,EAAE,CAACQ,CAAC,CAAC,GAAG,CAAC,GAAGJ,IAAI,CAACC,GAAG,CAACL,EAAE,CAACO,CAAC,CAAC,IAAIH,IAAI,CAACC,GAAG,CAACL,EAAE,CAACM,CAAC,CAAC,IAAIF,IAAI,CAACC,GAAG,CAACL,EAAE,CAACO,CAAC,CAAC,IAAIH,IAAI,CAACC,GAAG,CAACL,EAAE,CAACQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EAClK,MAAMC,eAAe,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKR,IAAI,CAACC,GAAG,CAACJ,EAAE,CAACY,YAAY,CAACD,CAAC,CAAC,CAAC,GAAGR,IAAI,CAACC,GAAG,CAACJ,EAAE,CAACY,YAAY,CAACF,CAAC,CAAC,CAAC,CAAC;EAC7G,MAAMG,EAAE,GAAGX,EAAE,KAAKM,eAAe,CAAC,CAAC,CAAC,GAAGA,eAAe,CAAC,CAAC,CAAC,GAAGA,eAAe,CAAC,CAAC,CAAC;EAC9E,MAAMM,EAAE,GAAGf,EAAE,CAACa,YAAY,CAACV,EAAE,CAAC;EAC9B,MAAMa,EAAE,GAAGhB,EAAE,CAACa,YAAY,CAACC,EAAE,CAAC;EAC9B,MAAMG,EAAE,GAAGhB,EAAE,CAACY,YAAY,CAACV,EAAE,CAAC;EAC9B,MAAMe,EAAE,GAAGjB,EAAE,CAACY,YAAY,CAACC,EAAE,CAAC;EAC9B,MAAMK,EAAE,GAAGjB,MAAM,CAACW,YAAY,CAACV,EAAE,CAAC;EAClC,MAAMiB,EAAE,GAAGlB,MAAM,CAACW,YAAY,CAACC,EAAE,CAAC;EAClC,MAAMP,CAAC,GAAG,CAACa,EAAE,GAAGD,EAAE,IAAIH,EAAE,GAAGD,EAAE,CAAC,KAAKG,EAAE,GAAGD,EAAE,IAAID,EAAE,GAAGD,EAAE,CAAC,CAAC;EACvD,MAAMT,CAAC,GAAG,CAACa,EAAE,GAAGZ,CAAC,GAAGU,EAAE,IAAIF,EAAE;EAC5B,OAAO,CAACT,CAAC,EAAEC,CAAC,CAAC;AACf,CAAC;AAED,MAAMc,GAAG,GAAG,IAAI3B,KAAK,CAAC4B,GAAG,CAAC,CAAC;AAC3B,MAAMC,YAAY,GAAG,IAAI7B,KAAK,CAAC8B,OAAO,CAAC,CAAC;AACxC,MAAMC,YAAY,GAAG,IAAI/B,KAAK,CAACgC,OAAO,CAAC,CAAC;AACxC,MAAMC,WAAW,GAAGA,CAAC;EACnBC,IAAI;EACJC,IAAI;EACJC;AACF,CAAC,KAAK;EACJ,MAAM;IACJC,WAAW;IACXC,iBAAiB;IACjBC,WAAW;IACXC,gBAAgB;IAChBC,SAAS;IACTC,KAAK;IACLC,SAAS;IACTC,KAAK;IACLC,UAAU;IACVC,YAAY;IACZC,OAAO;IACPC,WAAW;IACXC,MAAM;IACNC,SAAS;IACTC;EACF,CAAC,GAAGpD,KAAK,CAACqD,UAAU,CAAChD,OAAO,CAAC,CAAC,CAAC;;EAE/B,MAAMiD,WAAW,GAAGpD,QAAQ,CAACqD,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;EACrD,MAAMC,MAAM,GAAGzD,KAAK,CAAC0D,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMC,MAAM,GAAG3D,KAAK,CAAC0D,MAAM,CAAC,IAAI,CAAC;EACjC,MAAME,SAAS,GAAG5D,KAAK,CAAC0D,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMG,QAAQ,GAAG7D,KAAK,CAAC0D,MAAM,CAAC,CAAC,CAAC;EAChC,MAAMI,QAAQ,GAAG9D,KAAK,CAAC0D,MAAM,CAAC,CAAC,CAAC;EAChC,MAAM,CAACK,SAAS,EAAEC,YAAY,CAAC,GAAGhE,KAAK,CAACiE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMC,aAAa,GAAGlE,KAAK,CAACmE,WAAW,CAACC,CAAC,IAAI;IAC3C,IAAI5B,WAAW,EAAE;MACfiB,MAAM,CAACY,OAAO,CAACC,SAAS,GAAG,GAAGhC,WAAW,CAAC+B,OAAO,CAAC,CAAChC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAACkC,OAAO,CAAC,CAAC,CAAC,KAAKjC,WAAW,CAAC+B,OAAO,CAAC,CAAChC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAACkC,OAAO,CAAC,CAAC,CAAC,EAAE;MACjId,MAAM,CAACY,OAAO,CAACG,KAAK,CAACC,OAAO,GAAG,OAAO;IACxC;IAEAL,CAAC,CAACM,eAAe,CAAC,CAAC;IACnB,MAAMC,UAAU,GAAGP,CAAC,CAACQ,KAAK,CAACC,KAAK,CAAC,CAAC;IAClC,MAAMC,MAAM,GAAG,IAAI7E,KAAK,CAAC8B,OAAO,CAAC,CAAC,CAACgD,qBAAqB,CAACpB,MAAM,CAACU,OAAO,CAACW,WAAW,CAAC;IACpF,MAAMzE,EAAE,GAAG,IAAIN,KAAK,CAAC8B,OAAO,CAAC,CAAC,CAACkD,mBAAmB,CAACtB,MAAM,CAACU,OAAO,CAACW,WAAW,EAAE,CAAC,CAAC,CAACE,SAAS,CAAC,CAAC;IAC7F,MAAM1E,EAAE,GAAG,IAAIP,KAAK,CAAC8B,OAAO,CAAC,CAAC,CAACkD,mBAAmB,CAACtB,MAAM,CAACU,OAAO,CAACW,WAAW,EAAE,CAAC,CAAC,CAACE,SAAS,CAAC,CAAC;IAC7F,MAAMC,MAAM,GAAG,IAAIlF,KAAK,CAAC8B,OAAO,CAAC,CAAC,CAACkD,mBAAmB,CAACtB,MAAM,CAACU,OAAO,CAACW,WAAW,EAAE,CAAC,CAAC,CAACE,SAAS,CAAC,CAAC;IACjG,MAAME,KAAK,GAAG,IAAInF,KAAK,CAACoF,KAAK,CAAC,CAAC,CAACC,6BAA6B,CAACH,MAAM,EAAEL,MAAM,CAAC;IAC7ElB,SAAS,CAACS,OAAO,GAAG;MAClBM,UAAU;MACVpE,EAAE;MACFC,EAAE;MACF4E;IACF,CAAC;IACDvB,QAAQ,CAACQ,OAAO,GAAG/B,WAAW,CAAC+B,OAAO,CAAC,CAAChC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IACtDyB,QAAQ,CAACO,OAAO,GAAG/B,WAAW,CAAC+B,OAAO,CAAC,CAAChC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IACtDY,WAAW,CAAC;MACVsC,SAAS,EAAE,QAAQ;MACnBlD,IAAI;MACJyC,MAAM;MACNU,UAAU,EAAE,CAACjF,EAAE,EAAEC,EAAE,EAAE2E,MAAM;IAC7B,CAAC,CAAC;IACF7B,WAAW,KAAKA,WAAW,CAACmC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC;;IAE9CrB,CAAC,CAACsB,MAAM,CAACC,iBAAiB,CAACvB,CAAC,CAACwB,SAAS,CAAC;EACzC,CAAC,EAAE,CAACpD,WAAW,EAAEc,WAAW,EAAEL,WAAW,EAAEZ,IAAI,CAAC,CAAC;EACjD,MAAMwD,aAAa,GAAG7F,KAAK,CAACmE,WAAW,CAACC,CAAC,IAAI;IAC3CA,CAAC,CAACM,eAAe,CAAC,CAAC;IACnB,IAAI,CAACX,SAAS,EAAEC,YAAY,CAAC,IAAI,CAAC;IAElC,IAAIJ,SAAS,CAACS,OAAO,EAAE;MACrB,MAAM;QACJM,UAAU;QACVpE,EAAE;QACFC,EAAE;QACF4E;MACF,CAAC,GAAGxB,SAAS,CAACS,OAAO;MACrB,MAAM,CAACyB,IAAI,EAAEC,IAAI,CAAC,GAAG,CAACxD,iBAAiB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAAC,CAACF,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC2D,SAAS,EAAEA,SAAS,CAAC;MACvH,MAAM,CAACC,IAAI,EAAEC,IAAI,CAAC,GAAG,CAAC3D,iBAAiB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAAC,CAACF,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC2D,SAAS,EAAEA,SAAS,CAAC;MACvHpE,GAAG,CAACuE,IAAI,CAAC/B,CAAC,CAACxC,GAAG,CAAC;MACfA,GAAG,CAACwE,cAAc,CAAChB,KAAK,EAAEtD,YAAY,CAAC;MACvCF,GAAG,CAACyE,SAAS,CAACC,MAAM,CAAC,CAAC;MACtB1E,GAAG,CAACwE,cAAc,CAAChB,KAAK,EAAEtD,YAAY,CAAC;MACvCA,YAAY,CAACyE,GAAG,CAAC5B,UAAU,CAAC;MAC5B,IAAI,CAAC6B,OAAO,EAAEC,OAAO,CAAC,GAAGnG,kBAAkB,CAACC,EAAE,EAAEC,EAAE,EAAEsB,YAAY,CAAC;MACjE;AACN;;MAEM,IAAIgE,IAAI,KAAKE,SAAS,EAAE;QACtBQ,OAAO,GAAG7F,IAAI,CAAC+F,GAAG,CAACF,OAAO,EAAEV,IAAI,GAAGjC,QAAQ,CAACQ,OAAO,CAAC;MACtD;MAEA,IAAI0B,IAAI,KAAKC,SAAS,EAAE;QACtBQ,OAAO,GAAG7F,IAAI,CAACgG,GAAG,CAACH,OAAO,EAAET,IAAI,GAAGlC,QAAQ,CAACQ,OAAO,CAAC;MACtD;MAEA,IAAI4B,IAAI,KAAKD,SAAS,EAAE;QACtBS,OAAO,GAAG9F,IAAI,CAAC+F,GAAG,CAACD,OAAO,EAAER,IAAI,GAAGnC,QAAQ,CAACO,OAAO,CAAC;MACtD;MAEA,IAAI6B,IAAI,KAAKF,SAAS,EAAE;QACtBS,OAAO,GAAG9F,IAAI,CAACgG,GAAG,CAACF,OAAO,EAAEP,IAAI,GAAGpC,QAAQ,CAACO,OAAO,CAAC;MACtD;MAEA/B,WAAW,CAAC+B,OAAO,CAAC,CAAChC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,GAAGwB,QAAQ,CAACQ,OAAO,GAAGmC,OAAO;MAChElE,WAAW,CAAC+B,OAAO,CAAC,CAAChC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,GAAGyB,QAAQ,CAACO,OAAO,GAAGoC,OAAO;MAEhE,IAAIjE,WAAW,EAAE;QACfiB,MAAM,CAACY,OAAO,CAACC,SAAS,GAAG,GAAGhC,WAAW,CAAC+B,OAAO,CAAC,CAAChC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAACkC,OAAO,CAAC,CAAC,CAAC,KAAKjC,WAAW,CAAC+B,OAAO,CAAC,CAAChC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAACkC,OAAO,CAAC,CAAC,CAAC,EAAE;MACnI;MAEAvC,YAAY,CAAC4E,eAAe,CAACJ,OAAO,GAAGjG,EAAE,CAACM,CAAC,GAAG4F,OAAO,GAAGjG,EAAE,CAACK,CAAC,EAAE2F,OAAO,GAAGjG,EAAE,CAACO,CAAC,GAAG2F,OAAO,GAAGjG,EAAE,CAACM,CAAC,EAAE0F,OAAO,GAAGjG,EAAE,CAACQ,CAAC,GAAG0F,OAAO,GAAGjG,EAAE,CAACO,CAAC,CAAC;MAC/HmC,MAAM,CAAClB,YAAY,CAAC;IACtB;EACF,CAAC,EAAE,CAACQ,WAAW,EAAEU,MAAM,EAAEa,SAAS,EAAEzB,WAAW,EAAEC,iBAAiB,EAAEF,IAAI,CAAC,CAAC;EAC1E,MAAMwE,WAAW,GAAG7G,KAAK,CAACmE,WAAW,CAACC,CAAC,IAAI;IACzC,IAAI5B,WAAW,EAAE;MACfiB,MAAM,CAACY,OAAO,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;IACvC;IAEAL,CAAC,CAACM,eAAe,CAAC,CAAC;IACnBd,SAAS,CAACS,OAAO,GAAG,IAAI;IACxBlB,SAAS,CAAC,CAAC;IACXG,WAAW,KAAKA,WAAW,CAACmC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC;;IAE7CrB,CAAC,CAACsB,MAAM,CAACoB,qBAAqB,CAAC1C,CAAC,CAACwB,SAAS,CAAC;EAC7C,CAAC,EAAE,CAACpD,WAAW,EAAEc,WAAW,EAAEH,SAAS,CAAC,CAAC;EACzC,MAAM4D,YAAY,GAAG/G,KAAK,CAACmE,WAAW,CAACC,CAAC,IAAI;IAC1CA,CAAC,CAACM,eAAe,CAAC,CAAC;IACnBV,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EACN,MAAMgD,OAAO,GAAGhH,KAAK,CAACiH,OAAO,CAAC,MAAM;IAClC,MAAMC,KAAK,GAAG/E,IAAI,CAAC0C,KAAK,CAAC,CAAC,CAACK,SAAS,CAAC,CAAC;IACtC,MAAMiC,KAAK,GAAG/E,IAAI,CAACyC,KAAK,CAAC,CAAC,CAACK,SAAS,CAAC,CAAC;IACtC,OAAO,IAAIjF,KAAK,CAACgC,OAAO,CAAC,CAAC,CAACmF,SAAS,CAACF,KAAK,EAAEC,KAAK,EAAED,KAAK,CAACrC,KAAK,CAAC,CAAC,CAACwC,KAAK,CAACF,KAAK,CAAC,CAAC;EAChF,CAAC,EAAE,CAAChF,IAAI,EAAEC,IAAI,CAAC,CAAC;EAChB,MAAMkF,IAAI,GAAGzE,KAAK,GAAG,CAAC,GAAG,CAAC,GAAGF,KAAK,GAAG,CAAC;EACtC,MAAM4E,MAAM,GAAG1E,KAAK,GAAG,KAAK,GAAGF,KAAK,GAAG,KAAK;EAC5C,MAAM6E,KAAK,GAAGzD,SAAS,GAAGhB,YAAY,GAAGD,UAAU,CAACT,IAAI,CAAC;EACzD,MAAMoF,MAAM,GAAGzH,KAAK,CAACiH,OAAO,CAAC,MAAM,CAAC,IAAIhH,KAAK,CAAC8B,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI9B,KAAK,CAAC8B,OAAO,CAAC,CAAC,EAAEwF,MAAM,EAAE,CAAC,CAAC,EAAE,IAAItH,KAAK,CAAC8B,OAAO,CAACwF,MAAM,EAAEA,MAAM,EAAE,CAAC,CAAC,EAAE,IAAItH,KAAK,CAAC8B,OAAO,CAACwF,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAItH,KAAK,CAAC8B,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAACwF,MAAM,CAAC,CAAC;EAC9M,OAAO,aAAavH,KAAK,CAAC0H,aAAa,CAAC,OAAO,EAAE;IAC/CC,GAAG,EAAEhE,MAAM;IACXiE,MAAM,EAAEZ,OAAO;IACfa,gBAAgB,EAAE;EACpB,CAAC,EAAErF,WAAW,IAAI,aAAaxC,KAAK,CAAC0H,aAAa,CAACtH,IAAI,EAAE;IACvD0H,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EACpB,CAAC,EAAE,aAAa9H,KAAK,CAAC0H,aAAa,CAAC,KAAK,EAAE;IACzClD,KAAK,EAAE;MACLC,OAAO,EAAE,MAAM;MACfsD,UAAU,EAAE,SAAS;MACrBP,KAAK,EAAE,OAAO;MACdQ,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE;IACd,CAAC;IACDC,SAAS,EAAE1F,gBAAgB;IAC3BkF,GAAG,EAAElE;EACP,CAAC,CAAC,CAAC,EAAE,aAAazD,KAAK,CAAC0H,aAAa,CAAC,OAAO,EAAE;IAC7CI,QAAQ,EAAE,CAACR,IAAI,GAAG,GAAG,EAAEA,IAAI,GAAG,GAAG,EAAE,CAAC;EACtC,CAAC,EAAE,aAAatH,KAAK,CAAC0H,aAAa,CAAC,MAAM,EAAE;IAC1CU,OAAO,EAAE,IAAI;IACblE,aAAa,EAAEA,aAAa;IAC5B2B,aAAa,EAAEA,aAAa;IAC5BgB,WAAW,EAAEA,WAAW;IACxBE,YAAY,EAAEA,YAAY;IAC1BpE,KAAK,EAAE4E,MAAM;IACbnE,QAAQ,EAAEA;EACZ,CAAC,EAAE,aAAapD,KAAK,CAAC0H,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,aAAa1H,KAAK,CAAC0H,aAAa,CAAC,mBAAmB,EAAE;IAChHW,WAAW,EAAE,IAAI;IACjB3F,SAAS,EAAEA,SAAS;IACpB8E,KAAK,EAAEA,KAAK;IACZc,aAAa,EAAE,IAAI;IACnBC,mBAAmB,EAAE,CAAC,EAAE;IACxBC,IAAI,EAAEvI,KAAK,CAACwI,UAAU;IACtBC,GAAG,EAAE;EACP,CAAC,CAAC,CAAC,EAAE,aAAa1I,KAAK,CAAC0H,aAAa,CAACvH,IAAI,EAAE;IAC1C2H,QAAQ,EAAE,CAAC,CAACP,MAAM,GAAG,CAAC,EAAE,CAACA,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;IACvCc,WAAW,EAAE,IAAI;IACjB3F,SAAS,EAAEA,SAAS;IACpB+E,MAAM,EAAEA,MAAM;IACd7E,SAAS,EAAEA,SAAS;IACpB4E,KAAK,EAAEA,KAAK;IACZxE,OAAO,EAAEA,OAAO;IAChBsF,aAAa,EAAE,IAAI;IACnBC,mBAAmB,EAAE,CAAC,EAAE;IACxBnF,QAAQ,EAAEA,QAAQ;IAClBsF,GAAG,EAAE;EACP,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AAED,SAASxG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}