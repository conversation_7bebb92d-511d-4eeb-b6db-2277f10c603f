"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("@react-three/fiber"),t=require("three");function n(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var a=n(e),u=n(t);exports.SpriteAnimator=({startFrame:e,endFrame:t,fps:n,frameName:s,textureDataURL:c,textureImageURL:o,loop:f,numberOfFrames:i,autoPlay:m,animationNames:l,onStart:p,onEnd:h,onLoopEnd:w,onFrame:y,play:d,pause:S,flipX:x,alphaTest:g,children:b,...z},F)=>{r.useThree((e=>e.viewport));const A=a.useRef(null),[E,O]=a.useState(!1),R=a.useRef(),j=a.useRef(),v=a.useRef(window.performance.now()),L=a.useRef(),P=a.useRef(e||0),T=a.useRef(s||""),N=1e3/(n||30),[M,k]=a.useState(new u.Texture),_=a.useRef(0),[q,C]=a.useState([1,1,1]),D=x?-1:1;const I=(e,r)=>{const t=r/e;return j.current.scale.set(1,t,1),[1,t,1]};a.useEffect((()=>{if(c&&o)!function(e,r,t){const n=new u.TextureLoader,a=fetch(e).then((e=>e.json())),s=new Promise((e=>{n.load(r,e)}));Promise.all([a,s]).then((e=>{t(e[0],e[1])}))}(c,o,U);else if(o){const e=new u.TextureLoader;new Promise((r=>{e.load(o,r)})).then((e=>{U(null,e)}))}}),[]),a.useLayoutEffect((()=>{X()}),[M]),a.useEffect((()=>{}),[S]),a.useEffect((()=>{T.current!==s&&s&&(P.current=0,T.current=s)}),[s]);const U=(e,r)=>{if(null===e){if(r&&i){const e=r.image.width,t=r.image.height,n=e/i,a=t;if(L.current=r,_.current=i,A.current={frames:[],meta:{version:"1.0",size:{w:e,h:t},scale:"1"}},parseInt(n.toString(),10)===n)for(let e=0;e<i;e++)A.current.frames.push({frame:{x:e*n,y:0,w:n,h:a},rotated:!1,trimmed:!1,spriteSourceSize:{x:0,y:0,w:n,h:a},sourceSize:{w:n,h:t}})}}else if(r){A.current=e,A.current.frames=Array.isArray(e.frames)?e.frames:W(),_.current=Array.isArray(e.frames)?e.frames.length:Object.keys(e.frames).length,L.current=r;const{w:t,h:n}=B(e.frames).sourceSize,a=I(t,n);C(a),R.current&&(R.current.map=r)}r.premultiplyAlpha=!1,k(r)},W=()=>{const e={},r=A.current,t=l;if(t)for(let n=0;n<t.length;n++){e[t[n]]=[];for(let a in r.frames){const u=r.frames[a],s=u.frame,c=s.x,o=s.y,f=s.w,i=s.h,m=u.sourceSize.w,l=u.sourceSize.h;"string"==typeof a&&-1!==a.toLowerCase().indexOf(t[n].toLowerCase())&&e[t[n]].push({x:c,y:o,w:f,h:i,frame:s,sourceSize:{w:m,h:l}})}}return e},X=()=>{if(!A.current)return;const{meta:{size:e},frames:r}=A.current,{w:t,h:n}=Array.isArray(r)?r[0].sourceSize:s&&r[s]?r[s][0].sourceSize:{w:0,h:0};R.current.map.wrapS=R.current.map.wrapT=u.RepeatWrapping,R.current.map.center.set(0,0),R.current.map.repeat.set(1*D/(e.w/t),1/(e.h/n));const a=1/((e.h-1)/n);R.current.map.offset.x=0,R.current.map.offset.y=1-a,O(!0),p&&p({currentFrameName:s,currentFrame:P.current})};r.useFrame(((r,n)=>{var a,u;null!=(a=A.current)&&a.frames&&null!=(u=R.current)&&u.map&&(S||(m||d)&&((()=>{const r=window.performance.now(),n=r-v.current,{meta:{size:a},frames:u}=A.current,{w:c,h:o}=B(u).sourceSize,i=Array.isArray(u)?u:s?u[s]:[];let m=0,l=0;const p=t||i.length-1;if(P.current>p&&(P.current=f&&null!=e?e:0,f?null==w||w({currentFrameName:s,currentFrame:P.current}):null==h||h({currentFrameName:s,currentFrame:P.current}),!f))return;if(n<=N)return;v.current=r-n%N,I(c,o);const y=(a.w-1)/c,d=(a.h-1)/o,{frame:{x:S,y:x},sourceSize:{w:g,h:b}}=i[P.current],z=1/y,F=1/d;m=D>0?z*(S/g):z*(S/g)-R.current.map.repeat.x,l=Math.abs(1-F)-F*(x/b),R.current.map.offset.x=m,R.current.map.offset.y=l,P.current+=1})(),y&&y({currentFrameName:T.current,currentFrame:P.current})))}));const B=e=>{if(Array.isArray(e))return e[0];if("object"==typeof e&&null!==e){return e[Object.keys(e)[0]][0]}return{w:0,h:0}};return a.createElement("group",z,a.createElement(a.Suspense,{fallback:null},a.createElement("sprite",{ref:j,scale:q},a.createElement("spriteMaterial",{toneMapped:!1,ref:R,map:M,transparent:!0,alphaTest:null!=g?g:0}))),b)};
