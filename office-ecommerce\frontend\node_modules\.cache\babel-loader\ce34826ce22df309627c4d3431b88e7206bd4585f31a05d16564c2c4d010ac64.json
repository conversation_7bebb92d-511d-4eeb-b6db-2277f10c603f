{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, useFrame, createPortal, extend } from '@react-three/fiber';\nimport { Scene, WebGLCubeRenderTarget, HalfFloatType } from 'three';\nimport { GroundProjectedEnv } from 'three-stdlib';\nimport { useEnvironment } from './useEnvironment.js';\nconst isRef = obj => obj.current && obj.current.isScene;\nconst resolveScene = scene => isRef(scene) ? scene.current : scene;\nfunction setEnvProps(background, scene, defaultScene, texture, blur = 0) {\n  const target = resolveScene(scene || defaultScene);\n  const oldbg = target.background;\n  const oldenv = target.environment; // @ts-ignore\n\n  const oldBlur = target.backgroundBlurriness || 0;\n  if (background !== 'only') target.environment = texture;\n  if (background) target.background = texture; // @ts-ignore\n\n  if (background && target.backgroundBlurriness !== undefined) target.backgroundBlurriness = blur;\n  return () => {\n    if (background !== 'only') target.environment = oldenv;\n    if (background) target.background = oldbg; // @ts-ignore\n\n    if (background && target.backgroundBlurriness !== undefined) target.backgroundBlurriness = oldBlur;\n  };\n}\nfunction EnvironmentMap({\n  scene,\n  background = false,\n  blur,\n  map\n}) {\n  const defaultScene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    if (map) return setEnvProps(background, scene, defaultScene, map, blur);\n  }, [defaultScene, scene, map, background, blur]);\n  return null;\n}\nfunction EnvironmentCube({\n  background = false,\n  scene,\n  blur,\n  ...rest\n}) {\n  const texture = useEnvironment(rest);\n  const defaultScene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    return setEnvProps(background, scene, defaultScene, texture, blur);\n  }, [texture, background, scene, defaultScene, blur]);\n  return null;\n}\nfunction EnvironmentPortal({\n  children,\n  near = 1,\n  far = 1000,\n  resolution = 256,\n  frames = 1,\n  map,\n  background = false,\n  blur,\n  scene,\n  files,\n  path,\n  preset = undefined,\n  extensions\n}) {\n  const gl = useThree(state => state.gl);\n  const defaultScene = useThree(state => state.scene);\n  const camera = React.useRef(null);\n  const [virtualScene] = React.useState(() => new Scene());\n  const fbo = React.useMemo(() => {\n    const fbo = new WebGLCubeRenderTarget(resolution);\n    fbo.texture.type = HalfFloatType;\n    return fbo;\n  }, [resolution]);\n  React.useLayoutEffect(() => {\n    if (frames === 1) camera.current.update(gl, virtualScene);\n    return setEnvProps(background, scene, defaultScene, fbo.texture, blur);\n  }, [children, virtualScene, fbo.texture, scene, defaultScene, background, frames, gl]);\n  let count = 1;\n  useFrame(() => {\n    if (frames === Infinity || count < frames) {\n      camera.current.update(gl, virtualScene);\n      count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal(/*#__PURE__*/React.createElement(React.Fragment, null, children, /*#__PURE__*/React.createElement(\"cubeCamera\", {\n    ref: camera,\n    args: [near, far, fbo]\n  }), files || preset ? /*#__PURE__*/React.createElement(EnvironmentCube, {\n    background: true,\n    files: files,\n    preset: preset,\n    path: path,\n    extensions: extensions\n  }) : map ? /*#__PURE__*/React.createElement(EnvironmentMap, {\n    background: true,\n    map: map,\n    extensions: extensions\n  }) : null), virtualScene));\n}\nfunction EnvironmentGround(props) {\n  var _props$ground, _props$ground2, _scale, _props$ground3;\n  const textureDefault = useEnvironment(props);\n  const texture = props.map || textureDefault;\n  React.useMemo(() => extend({\n    GroundProjectedEnvImpl: GroundProjectedEnv\n  }), []);\n  const args = React.useMemo(() => [texture], [texture]);\n  const height = (_props$ground = props.ground) == null ? void 0 : _props$ground.height;\n  const radius = (_props$ground2 = props.ground) == null ? void 0 : _props$ground2.radius;\n  const scale = (_scale = (_props$ground3 = props.ground) == null ? void 0 : _props$ground3.scale) !== null && _scale !== void 0 ? _scale : 1000;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(EnvironmentMap, _extends({}, props, {\n    map: texture\n  })), /*#__PURE__*/React.createElement(\"groundProjectedEnvImpl\", {\n    args: args,\n    scale: scale,\n    height: height,\n    radius: radius\n  }));\n}\nfunction Environment(props) {\n  return props.ground ? /*#__PURE__*/React.createElement(EnvironmentGround, props) : props.map ? /*#__PURE__*/React.createElement(EnvironmentMap, props) : props.children ? /*#__PURE__*/React.createElement(EnvironmentPortal, props) : /*#__PURE__*/React.createElement(EnvironmentCube, props);\n}\nexport { Environment, EnvironmentCube, EnvironmentMap, EnvironmentPortal };", "map": {"version": 3, "names": ["_extends", "React", "useThree", "useFrame", "createPortal", "extend", "Scene", "WebGLCubeRenderTarget", "HalfFloatType", "GroundProjectedEnv", "useEnvironment", "isRef", "obj", "current", "isScene", "resolveScene", "scene", "setEnvProps", "background", "defaultScene", "texture", "blur", "target", "oldbg", "oldenv", "environment", "<PERSON><PERSON><PERSON><PERSON>", "backgroundBlurriness", "undefined", "EnvironmentMap", "map", "state", "useLayoutEffect", "EnvironmentCube", "rest", "EnvironmentPortal", "children", "near", "far", "resolution", "frames", "files", "path", "preset", "extensions", "gl", "camera", "useRef", "virtualScene", "useState", "fbo", "useMemo", "type", "update", "count", "Infinity", "createElement", "Fragment", "ref", "args", "EnvironmentGround", "props", "_props$ground", "_props$ground2", "_scale", "_props$ground3", "textureDefault", "GroundProjectedEnvImpl", "height", "ground", "radius", "scale", "Environment"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Environment.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, useFrame, createPortal, extend } from '@react-three/fiber';\nimport { Scene, WebGLCubeRenderTarget, HalfFloatType } from 'three';\nimport { GroundProjectedEnv } from 'three-stdlib';\nimport { useEnvironment } from './useEnvironment.js';\n\nconst isRef = obj => obj.current && obj.current.isScene;\n\nconst resolveScene = scene => isRef(scene) ? scene.current : scene;\n\nfunction setEnvProps(background, scene, defaultScene, texture, blur = 0) {\n  const target = resolveScene(scene || defaultScene);\n  const oldbg = target.background;\n  const oldenv = target.environment; // @ts-ignore\n\n  const oldBlur = target.backgroundBlurriness || 0;\n  if (background !== 'only') target.environment = texture;\n  if (background) target.background = texture; // @ts-ignore\n\n  if (background && target.backgroundBlurriness !== undefined) target.backgroundBlurriness = blur;\n  return () => {\n    if (background !== 'only') target.environment = oldenv;\n    if (background) target.background = oldbg; // @ts-ignore\n\n    if (background && target.backgroundBlurriness !== undefined) target.backgroundBlurriness = oldBlur;\n  };\n}\n\nfunction EnvironmentMap({\n  scene,\n  background = false,\n  blur,\n  map\n}) {\n  const defaultScene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    if (map) return setEnvProps(background, scene, defaultScene, map, blur);\n  }, [defaultScene, scene, map, background, blur]);\n  return null;\n}\nfunction EnvironmentCube({\n  background = false,\n  scene,\n  blur,\n  ...rest\n}) {\n  const texture = useEnvironment(rest);\n  const defaultScene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    return setEnvProps(background, scene, defaultScene, texture, blur);\n  }, [texture, background, scene, defaultScene, blur]);\n  return null;\n}\nfunction EnvironmentPortal({\n  children,\n  near = 1,\n  far = 1000,\n  resolution = 256,\n  frames = 1,\n  map,\n  background = false,\n  blur,\n  scene,\n  files,\n  path,\n  preset = undefined,\n  extensions\n}) {\n  const gl = useThree(state => state.gl);\n  const defaultScene = useThree(state => state.scene);\n  const camera = React.useRef(null);\n  const [virtualScene] = React.useState(() => new Scene());\n  const fbo = React.useMemo(() => {\n    const fbo = new WebGLCubeRenderTarget(resolution);\n    fbo.texture.type = HalfFloatType;\n    return fbo;\n  }, [resolution]);\n  React.useLayoutEffect(() => {\n    if (frames === 1) camera.current.update(gl, virtualScene);\n    return setEnvProps(background, scene, defaultScene, fbo.texture, blur);\n  }, [children, virtualScene, fbo.texture, scene, defaultScene, background, frames, gl]);\n  let count = 1;\n  useFrame(() => {\n    if (frames === Infinity || count < frames) {\n      camera.current.update(gl, virtualScene);\n      count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal( /*#__PURE__*/React.createElement(React.Fragment, null, children, /*#__PURE__*/React.createElement(\"cubeCamera\", {\n    ref: camera,\n    args: [near, far, fbo]\n  }), files || preset ? /*#__PURE__*/React.createElement(EnvironmentCube, {\n    background: true,\n    files: files,\n    preset: preset,\n    path: path,\n    extensions: extensions\n  }) : map ? /*#__PURE__*/React.createElement(EnvironmentMap, {\n    background: true,\n    map: map,\n    extensions: extensions\n  }) : null), virtualScene));\n}\n\nfunction EnvironmentGround(props) {\n  var _props$ground, _props$ground2, _scale, _props$ground3;\n\n  const textureDefault = useEnvironment(props);\n  const texture = props.map || textureDefault;\n  React.useMemo(() => extend({\n    GroundProjectedEnvImpl: GroundProjectedEnv\n  }), []);\n  const args = React.useMemo(() => [texture], [texture]);\n  const height = (_props$ground = props.ground) == null ? void 0 : _props$ground.height;\n  const radius = (_props$ground2 = props.ground) == null ? void 0 : _props$ground2.radius;\n  const scale = (_scale = (_props$ground3 = props.ground) == null ? void 0 : _props$ground3.scale) !== null && _scale !== void 0 ? _scale : 1000;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(EnvironmentMap, _extends({}, props, {\n    map: texture\n  })), /*#__PURE__*/React.createElement(\"groundProjectedEnvImpl\", {\n    args: args,\n    scale: scale,\n    height: height,\n    radius: radius\n  }));\n}\n\nfunction Environment(props) {\n  return props.ground ? /*#__PURE__*/React.createElement(EnvironmentGround, props) : props.map ? /*#__PURE__*/React.createElement(EnvironmentMap, props) : props.children ? /*#__PURE__*/React.createElement(EnvironmentPortal, props) : /*#__PURE__*/React.createElement(EnvironmentCube, props);\n}\n\nexport { Environment, EnvironmentCube, EnvironmentMap, EnvironmentPortal };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,QAAQ,oBAAoB;AAC7E,SAASC,KAAK,EAAEC,qBAAqB,EAAEC,aAAa,QAAQ,OAAO;AACnE,SAASC,kBAAkB,QAAQ,cAAc;AACjD,SAASC,cAAc,QAAQ,qBAAqB;AAEpD,MAAMC,KAAK,GAAGC,GAAG,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACC,OAAO,CAACC,OAAO;AAEvD,MAAMC,YAAY,GAAGC,KAAK,IAAIL,KAAK,CAACK,KAAK,CAAC,GAAGA,KAAK,CAACH,OAAO,GAAGG,KAAK;AAElE,SAASC,WAAWA,CAACC,UAAU,EAAEF,KAAK,EAAEG,YAAY,EAAEC,OAAO,EAAEC,IAAI,GAAG,CAAC,EAAE;EACvE,MAAMC,MAAM,GAAGP,YAAY,CAACC,KAAK,IAAIG,YAAY,CAAC;EAClD,MAAMI,KAAK,GAAGD,MAAM,CAACJ,UAAU;EAC/B,MAAMM,MAAM,GAAGF,MAAM,CAACG,WAAW,CAAC,CAAC;;EAEnC,MAAMC,OAAO,GAAGJ,MAAM,CAACK,oBAAoB,IAAI,CAAC;EAChD,IAAIT,UAAU,KAAK,MAAM,EAAEI,MAAM,CAACG,WAAW,GAAGL,OAAO;EACvD,IAAIF,UAAU,EAAEI,MAAM,CAACJ,UAAU,GAAGE,OAAO,CAAC,CAAC;;EAE7C,IAAIF,UAAU,IAAII,MAAM,CAACK,oBAAoB,KAAKC,SAAS,EAAEN,MAAM,CAACK,oBAAoB,GAAGN,IAAI;EAC/F,OAAO,MAAM;IACX,IAAIH,UAAU,KAAK,MAAM,EAAEI,MAAM,CAACG,WAAW,GAAGD,MAAM;IACtD,IAAIN,UAAU,EAAEI,MAAM,CAACJ,UAAU,GAAGK,KAAK,CAAC,CAAC;;IAE3C,IAAIL,UAAU,IAAII,MAAM,CAACK,oBAAoB,KAAKC,SAAS,EAAEN,MAAM,CAACK,oBAAoB,GAAGD,OAAO;EACpG,CAAC;AACH;AAEA,SAASG,cAAcA,CAAC;EACtBb,KAAK;EACLE,UAAU,GAAG,KAAK;EAClBG,IAAI;EACJS;AACF,CAAC,EAAE;EACD,MAAMX,YAAY,GAAGjB,QAAQ,CAAC6B,KAAK,IAAIA,KAAK,CAACf,KAAK,CAAC;EACnDf,KAAK,CAAC+B,eAAe,CAAC,MAAM;IAC1B,IAAIF,GAAG,EAAE,OAAOb,WAAW,CAACC,UAAU,EAAEF,KAAK,EAAEG,YAAY,EAAEW,GAAG,EAAET,IAAI,CAAC;EACzE,CAAC,EAAE,CAACF,YAAY,EAAEH,KAAK,EAAEc,GAAG,EAAEZ,UAAU,EAAEG,IAAI,CAAC,CAAC;EAChD,OAAO,IAAI;AACb;AACA,SAASY,eAAeA,CAAC;EACvBf,UAAU,GAAG,KAAK;EAClBF,KAAK;EACLK,IAAI;EACJ,GAAGa;AACL,CAAC,EAAE;EACD,MAAMd,OAAO,GAAGV,cAAc,CAACwB,IAAI,CAAC;EACpC,MAAMf,YAAY,GAAGjB,QAAQ,CAAC6B,KAAK,IAAIA,KAAK,CAACf,KAAK,CAAC;EACnDf,KAAK,CAAC+B,eAAe,CAAC,MAAM;IAC1B,OAAOf,WAAW,CAACC,UAAU,EAAEF,KAAK,EAAEG,YAAY,EAAEC,OAAO,EAAEC,IAAI,CAAC;EACpE,CAAC,EAAE,CAACD,OAAO,EAAEF,UAAU,EAAEF,KAAK,EAAEG,YAAY,EAAEE,IAAI,CAAC,CAAC;EACpD,OAAO,IAAI;AACb;AACA,SAASc,iBAAiBA,CAAC;EACzBC,QAAQ;EACRC,IAAI,GAAG,CAAC;EACRC,GAAG,GAAG,IAAI;EACVC,UAAU,GAAG,GAAG;EAChBC,MAAM,GAAG,CAAC;EACVV,GAAG;EACHZ,UAAU,GAAG,KAAK;EAClBG,IAAI;EACJL,KAAK;EACLyB,KAAK;EACLC,IAAI;EACJC,MAAM,GAAGf,SAAS;EAClBgB;AACF,CAAC,EAAE;EACD,MAAMC,EAAE,GAAG3C,QAAQ,CAAC6B,KAAK,IAAIA,KAAK,CAACc,EAAE,CAAC;EACtC,MAAM1B,YAAY,GAAGjB,QAAQ,CAAC6B,KAAK,IAAIA,KAAK,CAACf,KAAK,CAAC;EACnD,MAAM8B,MAAM,GAAG7C,KAAK,CAAC8C,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAACC,YAAY,CAAC,GAAG/C,KAAK,CAACgD,QAAQ,CAAC,MAAM,IAAI3C,KAAK,CAAC,CAAC,CAAC;EACxD,MAAM4C,GAAG,GAAGjD,KAAK,CAACkD,OAAO,CAAC,MAAM;IAC9B,MAAMD,GAAG,GAAG,IAAI3C,qBAAqB,CAACgC,UAAU,CAAC;IACjDW,GAAG,CAAC9B,OAAO,CAACgC,IAAI,GAAG5C,aAAa;IAChC,OAAO0C,GAAG;EACZ,CAAC,EAAE,CAACX,UAAU,CAAC,CAAC;EAChBtC,KAAK,CAAC+B,eAAe,CAAC,MAAM;IAC1B,IAAIQ,MAAM,KAAK,CAAC,EAAEM,MAAM,CAACjC,OAAO,CAACwC,MAAM,CAACR,EAAE,EAAEG,YAAY,CAAC;IACzD,OAAO/B,WAAW,CAACC,UAAU,EAAEF,KAAK,EAAEG,YAAY,EAAE+B,GAAG,CAAC9B,OAAO,EAAEC,IAAI,CAAC;EACxE,CAAC,EAAE,CAACe,QAAQ,EAAEY,YAAY,EAAEE,GAAG,CAAC9B,OAAO,EAAEJ,KAAK,EAAEG,YAAY,EAAED,UAAU,EAAEsB,MAAM,EAAEK,EAAE,CAAC,CAAC;EACtF,IAAIS,KAAK,GAAG,CAAC;EACbnD,QAAQ,CAAC,MAAM;IACb,IAAIqC,MAAM,KAAKe,QAAQ,IAAID,KAAK,GAAGd,MAAM,EAAE;MACzCM,MAAM,CAACjC,OAAO,CAACwC,MAAM,CAACR,EAAE,EAAEG,YAAY,CAAC;MACvCM,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACF,OAAO,aAAarD,KAAK,CAACuD,aAAa,CAACvD,KAAK,CAACwD,QAAQ,EAAE,IAAI,EAAErD,YAAY,CAAE,aAAaH,KAAK,CAACuD,aAAa,CAACvD,KAAK,CAACwD,QAAQ,EAAE,IAAI,EAAErB,QAAQ,EAAE,aAAanC,KAAK,CAACuD,aAAa,CAAC,YAAY,EAAE;IAC1LE,GAAG,EAAEZ,MAAM;IACXa,IAAI,EAAE,CAACtB,IAAI,EAAEC,GAAG,EAAEY,GAAG;EACvB,CAAC,CAAC,EAAET,KAAK,IAAIE,MAAM,GAAG,aAAa1C,KAAK,CAACuD,aAAa,CAACvB,eAAe,EAAE;IACtEf,UAAU,EAAE,IAAI;IAChBuB,KAAK,EAAEA,KAAK;IACZE,MAAM,EAAEA,MAAM;IACdD,IAAI,EAAEA,IAAI;IACVE,UAAU,EAAEA;EACd,CAAC,CAAC,GAAGd,GAAG,GAAG,aAAa7B,KAAK,CAACuD,aAAa,CAAC3B,cAAc,EAAE;IAC1DX,UAAU,EAAE,IAAI;IAChBY,GAAG,EAAEA,GAAG;IACRc,UAAU,EAAEA;EACd,CAAC,CAAC,GAAG,IAAI,CAAC,EAAEI,YAAY,CAAC,CAAC;AAC5B;AAEA,SAASY,iBAAiBA,CAACC,KAAK,EAAE;EAChC,IAAIC,aAAa,EAAEC,cAAc,EAAEC,MAAM,EAAEC,cAAc;EAEzD,MAAMC,cAAc,GAAGxD,cAAc,CAACmD,KAAK,CAAC;EAC5C,MAAMzC,OAAO,GAAGyC,KAAK,CAAC/B,GAAG,IAAIoC,cAAc;EAC3CjE,KAAK,CAACkD,OAAO,CAAC,MAAM9C,MAAM,CAAC;IACzB8D,sBAAsB,EAAE1D;EAC1B,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,MAAMkD,IAAI,GAAG1D,KAAK,CAACkD,OAAO,CAAC,MAAM,CAAC/B,OAAO,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACtD,MAAMgD,MAAM,GAAG,CAACN,aAAa,GAAGD,KAAK,CAACQ,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,aAAa,CAACM,MAAM;EACrF,MAAME,MAAM,GAAG,CAACP,cAAc,GAAGF,KAAK,CAACQ,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGN,cAAc,CAACO,MAAM;EACvF,MAAMC,KAAK,GAAG,CAACP,MAAM,GAAG,CAACC,cAAc,GAAGJ,KAAK,CAACQ,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,cAAc,CAACM,KAAK,MAAM,IAAI,IAAIP,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG,IAAI;EAC9I,OAAO,aAAa/D,KAAK,CAACuD,aAAa,CAACvD,KAAK,CAACwD,QAAQ,EAAE,IAAI,EAAE,aAAaxD,KAAK,CAACuD,aAAa,CAAC3B,cAAc,EAAE7B,QAAQ,CAAC,CAAC,CAAC,EAAE6D,KAAK,EAAE;IACjI/B,GAAG,EAAEV;EACP,CAAC,CAAC,CAAC,EAAE,aAAanB,KAAK,CAACuD,aAAa,CAAC,wBAAwB,EAAE;IAC9DG,IAAI,EAAEA,IAAI;IACVY,KAAK,EAAEA,KAAK;IACZH,MAAM,EAAEA,MAAM;IACdE,MAAM,EAAEA;EACV,CAAC,CAAC,CAAC;AACL;AAEA,SAASE,WAAWA,CAACX,KAAK,EAAE;EAC1B,OAAOA,KAAK,CAACQ,MAAM,GAAG,aAAapE,KAAK,CAACuD,aAAa,CAACI,iBAAiB,EAAEC,KAAK,CAAC,GAAGA,KAAK,CAAC/B,GAAG,GAAG,aAAa7B,KAAK,CAACuD,aAAa,CAAC3B,cAAc,EAAEgC,KAAK,CAAC,GAAGA,KAAK,CAACzB,QAAQ,GAAG,aAAanC,KAAK,CAACuD,aAAa,CAACrB,iBAAiB,EAAE0B,KAAK,CAAC,GAAG,aAAa5D,KAAK,CAACuD,aAAa,CAACvB,eAAe,EAAE4B,KAAK,CAAC;AACjS;AAEA,SAASW,WAAW,EAAEvC,eAAe,EAAEJ,cAAc,EAAEM,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}