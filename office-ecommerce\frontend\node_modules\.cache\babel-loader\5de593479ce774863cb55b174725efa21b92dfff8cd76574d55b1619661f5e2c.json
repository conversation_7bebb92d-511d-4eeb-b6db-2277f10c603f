{"ast": null, "code": "import * as THREE from 'three';\nimport { useEffect } from 'react';\nimport { useThree } from '@react-three/fiber';\nimport { suspend } from 'suspend-react';\nfunction useVideoTexture(src, props) {\n  const {\n    unsuspend,\n    start,\n    crossOrigin,\n    muted,\n    loop,\n    ...rest\n  } = {\n    unsuspend: 'loadedmetadata',\n    crossOrigin: 'Anonymous',\n    muted: true,\n    loop: true,\n    start: true,\n    playsInline: true,\n    ...props\n  };\n  const gl = useThree(state => state.gl);\n  const texture = suspend(() => new Promise((res, rej) => {\n    const video = Object.assign(document.createElement('video'), {\n      src: typeof src === 'string' && src || undefined,\n      srcObject: src instanceof MediaStream && src || undefined,\n      crossOrigin,\n      loop,\n      muted,\n      ...rest\n    });\n    const texture = new THREE.VideoTexture(video);\n    if ('colorSpace' in texture) texture.colorSpace = gl.outputColorSpace;else texture.encoding = gl.outputEncoding;\n    video.addEventListener(unsuspend, () => res(texture));\n  }), [src]);\n  useEffect(() => void (start && texture.image.play()), [texture, start]);\n  return texture;\n}\nexport { useVideoTexture };", "map": {"version": 3, "names": ["THREE", "useEffect", "useThree", "suspend", "useVideoTexture", "src", "props", "unsuspend", "start", "crossOrigin", "muted", "loop", "rest", "playsInline", "gl", "state", "texture", "Promise", "res", "rej", "video", "Object", "assign", "document", "createElement", "undefined", "srcObject", "MediaStream", "VideoTexture", "colorSpace", "outputColorSpace", "encoding", "outputEncoding", "addEventListener", "image", "play"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useVideoTexture.js"], "sourcesContent": ["import * as THREE from 'three';\nimport { useEffect } from 'react';\nimport { useThree } from '@react-three/fiber';\nimport { suspend } from 'suspend-react';\n\nfunction useVideoTexture(src, props) {\n  const {\n    unsuspend,\n    start,\n    crossOrigin,\n    muted,\n    loop,\n    ...rest\n  } = {\n    unsuspend: 'loadedmetadata',\n    crossOrigin: 'Anonymous',\n    muted: true,\n    loop: true,\n    start: true,\n    playsInline: true,\n    ...props\n  };\n  const gl = useThree(state => state.gl);\n  const texture = suspend(() => new Promise((res, rej) => {\n    const video = Object.assign(document.createElement('video'), {\n      src: typeof src === 'string' && src || undefined,\n      srcObject: src instanceof MediaStream && src || undefined,\n      crossOrigin,\n      loop,\n      muted,\n      ...rest\n    });\n    const texture = new THREE.VideoTexture(video);\n    if ('colorSpace' in texture) texture.colorSpace = gl.outputColorSpace;else texture.encoding = gl.outputEncoding;\n    video.addEventListener(unsuspend, () => res(texture));\n  }), [src]);\n  useEffect(() => void (start && texture.image.play()), [texture, start]);\n  return texture;\n}\n\nexport { useVideoTexture };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,OAAO,QAAQ,eAAe;AAEvC,SAASC,eAAeA,CAACC,GAAG,EAAEC,KAAK,EAAE;EACnC,MAAM;IACJC,SAAS;IACTC,KAAK;IACLC,WAAW;IACXC,KAAK;IACLC,IAAI;IACJ,GAAGC;EACL,CAAC,GAAG;IACFL,SAAS,EAAE,gBAAgB;IAC3BE,WAAW,EAAE,WAAW;IACxBC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,IAAI;IACVH,KAAK,EAAE,IAAI;IACXK,WAAW,EAAE,IAAI;IACjB,GAAGP;EACL,CAAC;EACD,MAAMQ,EAAE,GAAGZ,QAAQ,CAACa,KAAK,IAAIA,KAAK,CAACD,EAAE,CAAC;EACtC,MAAME,OAAO,GAAGb,OAAO,CAAC,MAAM,IAAIc,OAAO,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;IACtD,MAAMC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC,EAAE;MAC3DnB,GAAG,EAAE,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,IAAIoB,SAAS;MAChDC,SAAS,EAAErB,GAAG,YAAYsB,WAAW,IAAItB,GAAG,IAAIoB,SAAS;MACzDhB,WAAW;MACXE,IAAI;MACJD,KAAK;MACL,GAAGE;IACL,CAAC,CAAC;IACF,MAAMI,OAAO,GAAG,IAAIhB,KAAK,CAAC4B,YAAY,CAACR,KAAK,CAAC;IAC7C,IAAI,YAAY,IAAIJ,OAAO,EAAEA,OAAO,CAACa,UAAU,GAAGf,EAAE,CAACgB,gBAAgB,CAAC,KAAKd,OAAO,CAACe,QAAQ,GAAGjB,EAAE,CAACkB,cAAc;IAC/GZ,KAAK,CAACa,gBAAgB,CAAC1B,SAAS,EAAE,MAAMW,GAAG,CAACF,OAAO,CAAC,CAAC;EACvD,CAAC,CAAC,EAAE,CAACX,GAAG,CAAC,CAAC;EACVJ,SAAS,CAAC,MAAM,MAAMO,KAAK,IAAIQ,OAAO,CAACkB,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAACnB,OAAO,EAAER,KAAK,CAAC,CAAC;EACvE,OAAOQ,OAAO;AAChB;AAEA,SAASZ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}