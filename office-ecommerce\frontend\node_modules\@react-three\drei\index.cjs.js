"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("react-dom/client"),n=require("three"),o=require("@react-three/fiber"),a=require("zustand"),i=require("react-merge-refs"),s=require("maath"),l=require("@react-spring/three"),c=require("@use-gesture/react"),u=require("zustand/middleware"),m=require("three-stdlib"),d=require("zustand/shallow"),f=require("troika-three-text"),p=require("suspend-react"),h=require("meshline"),v=require("lodash.pick"),x=require("lodash.omit"),y=require("camera-controls"),g=require("stats.js"),w=require("detect-gpu"),b=require("three-mesh-bvh"),E=require("react-composer"),M=require("lodash.clamp");function z(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function S(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var C=z(e),T=S(t),P=S(r),R=S(n),D=S(o),k=z(a),F=z(i),_=z(d),L=z(v),A=z(x),B=z(y),O=z(g),I=z(E),U=z(M);const V=new n.Vector3,j=new n.Vector3,W=new n.Vector3;function N(e,t,r){const n=V.setFromMatrixPosition(e.matrixWorld);n.project(t);const o=r.width/2,a=r.height/2;return[n.x*o+o,-n.y*a+a]}const G=e=>Math.abs(e)<1e-10?0:e;function H(e,t,r=""){let n="matrix3d(";for(let r=0;16!==r;r++)n+=G(t[r]*e.elements[r])+(15!==r?",":")");return r+n}const $=(q=[1,-1,1,1,1,-1,1,1,1,-1,1,1,1,-1,1,1],e=>H(e,q));var q;const X=(e,t)=>{return H(e,[1/(r=t),1/r,1/r,1,-1/r,-1/r,-1/r,-1,1/r,1/r,1/r,1,1,1,1,1],"translate(-50%,-50%)");var r};const Y=T.forwardRef((({children:e,eps:t=.001,style:r,className:a,prepend:i,center:s,fullscreen:l,portal:c,distanceFactor:u,sprite:m=!1,transform:d=!1,occlude:f,onOcclude:p,castShadow:h,receiveShadow:v,material:x,geometry:y,zIndexRange:g=[16777271,0],calculatePosition:w=N,as:b="div",wrapperClass:E,pointerEvents:M="auto",...z},S)=>{const{gl:R,camera:D,scene:k,size:F,raycaster:_,events:L,viewport:A}=o.useThree(),[B]=T.useState((()=>document.createElement(b))),O=T.useRef(),I=T.useRef(null),U=T.useRef(0),H=T.useRef([0,0]),q=T.useRef(null),Y=T.useRef(null),K=(null==c?void 0:c.current)||L.connected||R.domElement.parentNode,Z=T.useRef(null),Q=T.useRef(!1),J=T.useMemo((()=>f&&"blending"!==f||Array.isArray(f)&&f.length&&function(e){return e&&"object"==typeof e&&"current"in e}(f[0])),[f]);T.useLayoutEffect((()=>{const e=R.domElement;f&&"blending"===f?(e.style.zIndex=`${Math.floor(g[0]/2)}`,e.style.position="absolute",e.style.pointerEvents="none"):(e.style.zIndex=null,e.style.position=null,e.style.pointerEvents=null)}),[f]),T.useLayoutEffect((()=>{if(I.current){const e=O.current=P.createRoot(B);if(k.updateMatrixWorld(),d)B.style.cssText="position:absolute;top:0;left:0;pointer-events:none;overflow:hidden;";else{const e=w(I.current,D,F);B.style.cssText=`position:absolute;top:0;left:0;transform:translate3d(${e[0]}px,${e[1]}px,0);transform-origin:0 0;`}return K&&(i?K.prepend(B):K.appendChild(B)),()=>{K&&K.removeChild(B),e.unmount()}}}),[K,d]),T.useLayoutEffect((()=>{E&&(B.className=E)}),[E]);const ee=T.useMemo((()=>d?{position:"absolute",top:0,left:0,width:F.width,height:F.height,transformStyle:"preserve-3d",pointerEvents:"none"}:{position:"absolute",transform:s?"translate3d(-50%,-50%,0)":"none",...l&&{top:-F.height/2,left:-F.width/2,width:F.width,height:F.height},...r}),[r,s,l,F,d]),te=T.useMemo((()=>({position:"absolute",pointerEvents:M})),[M]);T.useLayoutEffect((()=>{var t,n;(Q.current=!1,d)?null==(t=O.current)||t.render(T.createElement("div",{ref:q,style:ee},T.createElement("div",{ref:Y,style:te},T.createElement("div",{ref:S,className:a,style:r,children:e})))):null==(n=O.current)||n.render(T.createElement("div",{ref:S,style:ee,className:a,children:e}))}));const re=T.useRef(!0);o.useFrame((e=>{if(I.current){D.updateMatrixWorld(),I.current.updateWorldMatrix(!0,!1);const e=d?H.current:w(I.current,D,F);if(d||Math.abs(U.current-D.zoom)>t||Math.abs(H.current[0]-e[0])>t||Math.abs(H.current[1]-e[1])>t){const t=function(e,t){const r=V.setFromMatrixPosition(e.matrixWorld),n=j.setFromMatrixPosition(t.matrixWorld),o=r.sub(n),a=t.getWorldDirection(W);return o.angleTo(a)>Math.PI/2}(I.current,D);let r=!1;J&&("blending"!==f?r=[k]:Array.isArray(f)&&(r=f.map((e=>e.current))));const o=re.current;if(r){const e=function(e,t,r,n){const o=V.setFromMatrixPosition(e.matrixWorld),a=o.clone();a.project(t),r.setFromCamera(a,t);const i=r.intersectObjects(n,!0);if(i.length){const e=i[0].distance;return o.distanceTo(r.ray.origin)<e}return!0}(I.current,D,_,r);re.current=e&&!t}else re.current=!t;o!==re.current&&(p?p(!re.current):B.style.display=re.current?"block":"none");const a=Math.floor(g[0]/2),i=f?J?[g[0],a]:[a-1,0]:g;if(B.style.zIndex=`${function(e,t,r){if(t instanceof n.PerspectiveCamera||t instanceof n.OrthographicCamera){const n=V.setFromMatrixPosition(e.matrixWorld),o=j.setFromMatrixPosition(t.matrixWorld),a=n.distanceTo(o),i=(r[1]-r[0])/(t.far-t.near),s=r[1]-i*t.far;return Math.round(i*a+s)}}(I.current,D,i)}`,d){const[e,t]=[F.width/2,F.height/2],r=D.projectionMatrix.elements[5]*t,{isOrthographicCamera:n,top:o,left:a,bottom:i,right:s}=D,l=$(D.matrixWorldInverse),c=n?`scale(${r})translate(${G(-(s+a)/2)}px,${G((o+i)/2)}px)`:`translateZ(${r}px)`;let d=I.current.matrixWorld;m&&(d=D.matrixWorldInverse.clone().transpose().copyPosition(d).scale(I.current.scale),d.elements[3]=d.elements[7]=d.elements[11]=0,d.elements[15]=1),B.style.width=F.width+"px",B.style.height=F.height+"px",B.style.perspective=n?"":`${r}px`,q.current&&Y.current&&(q.current.style.transform=`${c}${l}translate(${e}px,${t}px)`,Y.current.style.transform=X(d,1/((u||10)/400)))}else{const t=void 0===u?1:function(e,t){if(t instanceof n.OrthographicCamera)return t.zoom;if(t instanceof n.PerspectiveCamera){const r=V.setFromMatrixPosition(e.matrixWorld),n=j.setFromMatrixPosition(t.matrixWorld),o=t.fov*Math.PI/180,a=r.distanceTo(n);return 1/(2*Math.tan(o/2)*a)}return 1}(I.current,D)*u;B.style.transform=`translate3d(${e[0]}px,${e[1]}px,0) scale(${t})`}H.current=e,U.current=D.zoom}}if(!J&&Z.current&&!Q.current)if(d){if(q.current){const e=q.current.children[0];if(null!=e&&e.clientWidth&&null!=e&&e.clientHeight){const{isOrthographicCamera:t}=D;if(t||y)z.scale&&(Array.isArray(z.scale)?z.scale instanceof n.Vector3?Z.current.scale.copy(z.scale.clone().divideScalar(1)):Z.current.scale.set(1/z.scale[0],1/z.scale[1],1/z.scale[2]):Z.current.scale.setScalar(1/z.scale));else{const t=(u||10)/400,r=e.clientWidth*t,n=e.clientHeight*t;Z.current.scale.set(r,n,1)}Q.current=!0}}}else{const t=B.children[0];if(null!=t&&t.clientWidth&&null!=t&&t.clientHeight){const e=1/A.factor,r=t.clientWidth*e,n=t.clientHeight*e;Z.current.scale.set(r,n,1),Q.current=!0}Z.current.lookAt(e.camera.position)}}));const ne=T.useMemo((()=>({vertexShader:d?void 0:'\n          /*\n            This shader is from the THREE\'s SpriteMaterial.\n            We need to turn the backing plane into a Sprite\n            (make it always face the camera) if "transfrom" \n            is false. \n          */\n          #include <common>\n\n          void main() {\n            vec2 center = vec2(0., 1.);\n            float rotation = 0.0;\n            \n            // This is somewhat arbitrary, but it seems to work well\n            // Need to figure out how to derive this dynamically if it even matters\n            float size = 0.03;\n\n            vec4 mvPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );\n            vec2 scale;\n            scale.x = length( vec3( modelMatrix[ 0 ].x, modelMatrix[ 0 ].y, modelMatrix[ 0 ].z ) );\n            scale.y = length( vec3( modelMatrix[ 1 ].x, modelMatrix[ 1 ].y, modelMatrix[ 1 ].z ) );\n\n            bool isPerspective = isPerspectiveMatrix( projectionMatrix );\n            if ( isPerspective ) scale *= - mvPosition.z;\n\n            vec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale * size;\n            vec2 rotatedPosition;\n            rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;\n            rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;\n            mvPosition.xy += rotatedPosition;\n\n            gl_Position = projectionMatrix * mvPosition;\n          }\n      ',fragmentShader:"\n        void main() {\n          gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n        }\n      "})),[d]);return T.createElement("group",C.default({},z,{ref:I}),f&&!J&&T.createElement("mesh",{castShadow:h,receiveShadow:v,ref:Z},y||T.createElement("planeGeometry",null),x||T.createElement("shaderMaterial",{side:n.DoubleSide,vertexShader:ne.vertexShader,fragmentShader:ne.fragmentShader})))}));let K=0;const Z=k.default((e=>(n.DefaultLoadingManager.onStart=(t,r,n)=>{e({active:!0,item:t,loaded:r,total:n,progress:(r-K)/(n-K)*100})},n.DefaultLoadingManager.onLoad=()=>{e({active:!1})},n.DefaultLoadingManager.onError=t=>e((e=>({errors:[...e.errors,t]}))),n.DefaultLoadingManager.onProgress=(t,r,n)=>{r===n&&(K=n),e({active:!0,item:t,loaded:r,total:n,progress:(r-K)/(n-K)*100||100})},{errors:[],active:!1,progress:0,item:"",loaded:0,total:0}))),Q=e=>`Loading ${e.toFixed(2)}%`;const J={container:{position:"absolute",top:0,left:0,width:"100%",height:"100%",background:"#171717",display:"flex",alignItems:"center",justifyContent:"center",transition:"opacity 300ms ease",zIndex:1e3},inner:{width:100,height:3,background:"#272727",textAlign:"center"},bar:{height:3,width:"100%",background:"white",transition:"transform 200ms",transformOrigin:"left center"},data:{display:"inline-block",position:"relative",fontVariantNumeric:"tabular-nums",marginTop:"0.8em",color:"#f0f0f0",fontSize:"0.6em",fontFamily:'-apple-system, BlinkMacSystemFont, "Inter", "Segoe UI", "Helvetica Neue", Helvetica, Arial, Roboto, Ubuntu, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',whiteSpace:"nowrap"}},ee=T.createContext(null);function te(){return T.useContext(ee)}const re=T.forwardRef((({children:e},t)=>{const r=T.useRef(null),n=te(),{width:a,height:i}=o.useThree((e=>e.viewport));return o.useFrame((()=>{r.current.position.x=n.horizontal?-a*(n.pages-1)*n.offset:0,r.current.position.y=n.horizontal?0:i*(n.pages-1)*n.offset})),T.createElement("group",{ref:F.default([t,r])},e)})),ne=T.forwardRef((({children:e,style:t,...r},n)=>{const a=te(),i=T.useRef(null),{width:s,height:l}=o.useThree((e=>e.size)),c=T.useContext(o.context),u=T.useMemo((()=>P.createRoot(a.fixed)),[a.fixed]);return o.useFrame((()=>{a.delta>a.eps&&(i.current.style.transform=`translate3d(${a.horizontal?-s*(a.pages-1)*a.offset:0}px,${a.horizontal?0:l*(a.pages-1)*-a.offset}px,0)`)})),u.render(T.createElement("div",C.default({ref:F.default([n,i]),style:{...t,position:"absolute",top:0,left:0,willChange:"transform"}},r),T.createElement(ee.Provider,{value:a},T.createElement(o.context.Provider,{value:c},e)))),null})),oe=T.forwardRef((({html:e,...t},r)=>{const n=e?ne:re;return T.createElement(n,C.default({ref:r},t))}));const ae=T.createContext(null);const ie=T.createContext([]);const se=T.forwardRef((function({follow:e=!0,lockX:t=!1,lockY:r=!1,lockZ:n=!1,...a},i){const s=T.useRef();return o.useFrame((({camera:o})=>{if(!e||!s.current)return;const a=s.current.rotation.clone();o.getWorldQuaternion(s.current.quaternion),t&&(s.current.rotation.x=a.x),r&&(s.current.rotation.y=a.y),n&&(s.current.rotation.z=a.z)})),T.createElement("group",C.default({ref:F.default([s,i])},a))})),le=T.forwardRef((({children:e,depth:t=-1,...r},n)=>{const a=T.useRef(null);return o.useFrame((({camera:e})=>{a.current.quaternion.copy(e.quaternion),a.current.position.copy(e.position)})),T.createElement("group",C.default({ref:F.default([n,a])},r),T.createElement("group",{"position-z":-t},e))})),ce=T.forwardRef((function({points:e,color:t="black",vertexColors:r,linewidth:a,lineWidth:i,segments:s,dashed:l,...c},u){const d=o.useThree((e=>e.size)),f=T.useMemo((()=>s?new m.LineSegments2:new m.Line2),[s]),[p]=T.useState((()=>new m.LineMaterial)),h=T.useMemo((()=>{const t=s?new m.LineSegmentsGeometry:new m.LineGeometry,o=e.map((e=>{const t=Array.isArray(e);return e instanceof n.Vector3?[e.x,e.y,e.z]:e instanceof n.Vector2?[e.x,e.y,0]:t&&3===e.length?[e[0],e[1],e[2]]:t&&2===e.length?[e[0],e[1],0]:e}));if(t.setPositions(o.flat()),r){const e=r.map((e=>e instanceof n.Color?e.toArray():e));t.setColors(e.flat())}return t}),[e,s,r]);return T.useLayoutEffect((()=>{f.computeLineDistances()}),[e,f]),T.useLayoutEffect((()=>{l?p.defines.USE_DASH="":delete p.defines.USE_DASH,p.needsUpdate=!0}),[l,p]),T.useEffect((()=>()=>h.dispose()),[h]),T.createElement("primitive",C.default({object:f,ref:u},c),T.createElement("primitive",{object:h,attach:"geometry"}),T.createElement("primitive",C.default({object:p,attach:"material",color:t,vertexColors:Boolean(r),resolution:[d.width,d.height],linewidth:null!=a?a:i,dashed:l},c)))})),ue=new n.Vector3,me=T.forwardRef((function({start:e=[0,0,0],end:t=[0,0,0],mid:r,segments:o=20,...a},i){const s=T.useRef(null),[l]=T.useState((()=>new n.QuadraticBezierCurve3(void 0,void 0,void 0))),c=T.useCallback(((e,t,r,o=20)=>(e instanceof n.Vector3?l.v0.copy(e):l.v0.set(...e),t instanceof n.Vector3?l.v2.copy(t):l.v2.set(...t),r instanceof n.Vector3?l.v1.copy(r):Array.isArray(r)?l.v1.set(...r):l.v1.copy(l.v0.clone().add(l.v2.clone().sub(l.v0)).add(ue.set(0,l.v0.y-l.v2.y,0))),l.getPoints(o))),[]);T.useLayoutEffect((()=>{s.current.setPoints=(e,t,r)=>{const n=c(e,t,r);s.current.geometry&&s.current.geometry.setPositions(n.map((e=>e.toArray())).flat())}}),[]);const u=T.useMemo((()=>c(e,t,r,o)),[e,t,r,o]);return T.createElement(ce,C.default({ref:F.default([s,i]),points:u},a))})),de=T.forwardRef((function({start:e,end:t,midA:r,midB:o,segments:a=20,...i},s){const l=T.useMemo((()=>{const i=e instanceof n.Vector3?e:new n.Vector3(...e),s=t instanceof n.Vector3?t:new n.Vector3(...t),l=r instanceof n.Vector3?r:new n.Vector3(...r),c=o instanceof n.Vector3?o:new n.Vector3(...o);return new n.CubicBezierCurve3(i,l,c,s).getPoints(a)}),[e,t,r,o,a]);return T.createElement(ce,C.default({ref:s,points:l},i))})),fe=T.forwardRef((function({points:e,closed:t=!1,curveType:r="centripetal",tension:o=.5,segments:a=20,vertexColors:i,...s},l){const c=T.useMemo((()=>{const a=e.map((e=>e instanceof n.Vector3?e:new n.Vector3(...e)));return new n.CatmullRomCurve3(a,t,r,o)}),[e,t,r,o]),u=T.useMemo((()=>c.getPoints(a)),[c,a]),m=T.useMemo((()=>{if(!i||i.length<2)return;if(i.length===a+1)return i;const e=i.map((e=>e instanceof n.Color?e:new n.Color(...e)));t&&e.push(e[0].clone());const r=[e[0]],o=a/(e.length-1);for(let t=1;t<a;t++){const n=t%o/o,a=Math.floor(t/o);r.push(e[a].clone().lerp(e[a+1],n))}return r.push(e[e.length-1]),r}),[i,a]);return T.createElement(ce,C.default({ref:l,points:u,vertexColors:m},s))})),pe=T.forwardRef((({url:e,distance:t=1,loop:r=!0,autoplay:a,...i},s)=>{const l=T.useRef(),c=o.useThree((({camera:e})=>e)),[u]=T.useState((()=>new n.AudioListener)),m=o.useLoader(n.AudioLoader,e);return T.useEffect((()=>{const e=l.current;e&&(e.setBuffer(m),e.setRefDistance(t),e.setLoop(r),a&&!e.isPlaying&&e.play())}),[m,c,t,r]),T.useEffect((()=>{const e=l.current;return c.add(u),()=>{c.remove(u),e&&(e.isPlaying&&e.stop(),e.source&&e.source._connected&&e.disconnect())}}),[]),T.createElement("positionalAudio",C.default({ref:F.default([l,s]),args:[u]},i))})),he=T.forwardRef((({sdfGlyphSize:e=64,anchorX:t="center",anchorY:r="middle",font:n,fontSize:a=1,children:i,characters:s,onSync:l,...c},u)=>{const m=o.useThree((({invalidate:e})=>e)),[d]=T.useState((()=>new f.Text)),[h,v]=T.useMemo((()=>{const e=[];let t="";return T.Children.forEach(i,(r=>{"string"==typeof r||"number"==typeof r?t+=r:e.push(r)})),[e,t]}),[i]);return p.suspend((()=>new Promise((e=>f.preloadFont({font:n,characters:s},e)))),["troika-text",n,s]),T.useLayoutEffect((()=>{d.sync((()=>{m(),l&&l(d)}))})),T.useEffect((()=>()=>d.dispose()),[d]),T.createElement("primitive",C.default({object:d,ref:u,font:n,text:v,anchorX:t,anchorY:r,fontSize:a,sdfGlyphSize:e},c),h)}));let ve=null;async function xe(e){ve||(ve=new m.FontLoader);let t="string"==typeof e?await(await fetch(e)).json():e;return ve.parse(t)}function ye(e){return p.suspend(xe,[e])}ye.preload=e=>p.preload(xe,[e]),ye.clear=e=>p.clear([e]);const ge=["string","number"],we=T.forwardRef((({font:e,letterSpacing:r=0,lineHeight:n=1,size:a=1,height:i=.2,bevelThickness:s=.1,bevelSize:l=.01,bevelEnabled:c=!1,bevelOffset:u=0,bevelSegments:d=4,curveSegments:f=8,smooth:p,children:h,...v},x)=>{T.useMemo((()=>o.extend({RenamedTextGeometry:m.TextGeometry})),[]);const y=T.useRef(null),g=ye(e),w=t.useMemo((()=>({font:g,size:a,height:i,bevelThickness:s,bevelSize:l,bevelEnabled:c,bevelSegments:d,bevelOffset:u,curveSegments:f,letterSpacing:r,lineHeight:n})),[g,a,i,s,l,c,d,u,f,r,n]),[b,...E]=t.useMemo((()=>(e=>{let t="";const r=[];return T.Children.forEach(e,(e=>{ge.includes(typeof e)?t+=e+"":r.push(e)})),[t,...r]})(h)),[h]),M=T.useMemo((()=>[b,w]),[b,w]);return T.useLayoutEffect((()=>{p&&(y.current.geometry=m.mergeVertices(y.current.geometry,p),y.current.geometry.computeVertexNormals())}),[M,p]),T.useImperativeHandle(x,(()=>y.current),[]),T.createElement("mesh",C.default({},v,{ref:y}),T.createElement("renamedTextGeometry",{args:M}),E)})),be=T.forwardRef((({children:e,multisamping:t=8,renderIndex:r=1,disableRender:a,disableGamma:i,disableRenderPass:s,depthBuffer:l=!0,stencilBuffer:c=!1,anisotropy:u=1,encoding:d,type:f,...p},h)=>{T.useMemo((()=>o.extend({EffectComposer:m.EffectComposer,RenderPass:m.RenderPass,ShaderPass:m.ShaderPass})),[]);const v=T.useRef(),{scene:x,camera:y,gl:g,size:w,viewport:b}=o.useThree(),[E]=T.useState((()=>{const e=new n.WebGLRenderTarget(w.width,w.height,{type:f||n.HalfFloatType,format:n.RGBAFormat,depthBuffer:l,stencilBuffer:c,anisotropy:u});return f===n.UnsignedByteType&&null!=d&&("colorSpace"in e?e.texture.colorSpace=d:e.texture.encoding=d),e.samples=t,e}));T.useEffect((()=>{var e,t;null==(e=v.current)||e.setSize(w.width,w.height),null==(t=v.current)||t.setPixelRatio(b.dpr)}),[g,w,b.dpr]),o.useFrame((()=>{var e;a||null==(e=v.current)||e.render()}),r);const M=[];return s||M.push(T.createElement("renderPass",{key:"renderpass",attach:`passes-${M.length}`,args:[x,y]})),i||M.push(T.createElement("shaderPass",{attach:`passes-${M.length}`,key:"gammapass",args:[m.GammaCorrectionShader]})),T.Children.forEach(e,(e=>{e&&M.push(T.cloneElement(e,{key:M.length,attach:`passes-${M.length}`}))})),T.createElement("effectComposer",C.default({ref:F.default([h,v]),args:[g,E]},p),M)}));function Ee(e,t,r,n){const o=class extends R.ShaderMaterial{constructor(o={}){const a=Object.entries(e);super({uniforms:a.reduce(((e,[t,r])=>({...e,...R.UniformsUtils.clone({[t]:{value:r}})})),{}),vertexShader:t,fragmentShader:r}),this.key="",a.forEach((([e])=>Object.defineProperty(this,e,{get:()=>this.uniforms[e].value,set:t=>this.uniforms[e].value=t}))),Object.assign(this,o),n&&n(this)}};return o.key=R.MathUtils.generateUUID(),o}const Me=e=>e===Object(e)&&!Array.isArray(e)&&"function"!=typeof e;function ze(e,r){const a=o.useThree((e=>e.gl)),i=o.useLoader(n.TextureLoader,Me(e)?Object.values(e):e);if(t.useLayoutEffect((()=>{null==r||r(i)}),[r]),t.useEffect((()=>{(Array.isArray(i)?i:[i]).forEach(a.initTexture)}),[a,i]),Me(e)){const t=Object.keys(e),r={};return t.forEach((e=>Object.assign(r,{[e]:i[t.indexOf(e)]}))),r}return i}ze.preload=e=>o.useLoader.preload(n.TextureLoader,e),ze.clear=e=>o.useLoader.clear(n.TextureLoader,e);const Se=Ee({color:new R.Color("white"),scale:[1,1],imageBounds:[1,1],map:null,zoom:1,grayscale:0,opacity:1},"\n  varying vec2 vUv;\n  void main() {\n    gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n    vUv = uv;\n  }\n","\n  // mostly from https://gist.github.com/statico/df64c5d167362ecf7b34fca0b1459a44\n  varying vec2 vUv;\n  uniform vec2 scale;\n  uniform vec2 imageBounds;\n  uniform vec3 color;\n  uniform sampler2D map;\n  uniform float zoom;\n  uniform float grayscale;\n  uniform float opacity;\n  const vec3 luma = vec3(.299, 0.587, 0.114);\n  vec4 toGrayscale(vec4 color, float intensity) {\n    return vec4(mix(color.rgb, vec3(dot(color.rgb, luma)), intensity), color.a);\n  }\n  vec2 aspect(vec2 size) {\n    return size / min(size.x, size.y);\n  }\n  void main() {\n    vec2 s = aspect(scale);\n    vec2 i = aspect(imageBounds);\n    float rs = s.x / s.y;\n    float ri = i.x / i.y;\n    vec2 new = rs < ri ? vec2(i.x * s.y / i.y, s.y) : vec2(s.x, i.y * s.x / i.x);\n    vec2 offset = (rs < ri ? vec2((new.x - s.x) / 2.0, 0.0) : vec2(0.0, (new.y - s.y) / 2.0)) / new;\n    vec2 uv = vUv * s / new + offset;\n    vec2 zUv = (uv - vec2(0.5, 0.5)) / zoom + vec2(0.5, 0.5);\n    gl_FragColor = toGrayscale(texture2D(map, zUv) * vec4(color, opacity), grayscale);\n    \n    #include <tonemapping_fragment>\n    #include <encodings_fragment>\n  }\n"),Ce=T.forwardRef((({children:e,color:t,segments:r=1,scale:n=1,zoom:a=1,grayscale:i=0,opacity:s=1,texture:l,toneMapped:c,transparent:u,...m},d)=>{o.extend({ImageMaterial:Se});const f=Array.isArray(n)?[n[0],n[1]]:[n,n],p=[l.image.width,l.image.height];return T.createElement("mesh",C.default({ref:d,scale:Array.isArray(n)?[...n,1]:n},m),T.createElement("planeGeometry",{args:[1,1,r,r]}),T.createElement("imageMaterial",{color:t,map:l,zoom:a,grayscale:i,opacity:s,scale:f,imageBounds:p,toneMapped:c,transparent:u}),e)})),Te=T.forwardRef((({url:e,...t},r)=>{const n=ze(e);return T.createElement(Ce,C.default({},t,{texture:n,ref:r}))})),Pe=T.forwardRef((({url:e,...t},r)=>T.createElement(Ce,C.default({},t,{ref:r})))),Re=T.forwardRef(((e,t)=>{if(e.url)return T.createElement(Te,C.default({},e,{ref:t}));if(e.texture)return T.createElement(Pe,C.default({},e,{ref:t}));throw new Error("<Image /> requires a url or texture")})),De=T.forwardRef((({userData:e,children:t,geometry:r,threshold:n=15,color:o="black",...a},i)=>{const s=T.useRef(null);return T.useLayoutEffect((()=>{const e=s.current.parent;if(e){const t=r||e.geometry;t===s.current.userData.currentGeom&&n===s.current.userData.currentThreshold||(s.current.userData.currentGeom=t,s.current.userData.currentThreshold=n,s.current.geometry=new R.EdgesGeometry(t,n))}})),T.useImperativeHandle(i,(()=>s.current)),T.createElement("lineSegments",C.default({ref:s,raycast:()=>null},a),t||T.createElement("lineBasicMaterial",{color:o}))})),ke={width:.2,length:1,decay:1,local:!1,stride:0,interval:1},Fe=(e,t=1)=>(e.set(e.subarray(t)),e.fill(-1/0,-t),e);function _e(e,t){const{length:r,local:a,decay:i,interval:s,stride:l}={...ke,...t},c=T.useRef(),[u]=T.useState((()=>new n.Vector3));T.useLayoutEffect((()=>{e&&(c.current=Float32Array.from({length:10*r*3},((t,r)=>e.position.getComponent(r%3))))}),[r,e]);const m=T.useRef(new n.Vector3),d=T.useRef(0);return o.useFrame((()=>{if(e&&c.current){if(0===d.current){let t;a?t=e.position:(e.getWorldPosition(u),t=u);const r=1*i;for(let e=0;e<r;e++)t.distanceTo(m.current)<l||(Fe(c.current,3),c.current.set(t.toArray(),c.current.length-3));m.current.copy(t)}d.current++,d.current=d.current%s}})),c}const Le=T.forwardRef(((e,t)=>{const{children:r}=e,{width:a,length:i,decay:s,local:l,stride:c,interval:u}={...ke,...e},{color:m="hotpink",attenuation:d,target:f}=e,p=o.useThree((e=>e.size)),v=o.useThree((e=>e.scene)),x=T.useRef(null),[y,g]=T.useState(null),w=_e(y,{length:i,decay:s,local:l,stride:c,interval:u});T.useEffect((()=>{const e=(null==f?void 0:f.current)||x.current.children.find((e=>e instanceof n.Object3D));e&&g(e)}),[w,f]);const b=T.useMemo((()=>new h.MeshLineGeometry),[]),E=T.useMemo((()=>{var e;const t=new h.MeshLineMaterial({lineWidth:.1*a,color:m,sizeAttenuation:1,resolution:new n.Vector2(p.width,p.height)});let o;if(r)if(Array.isArray(r))o=r.find((e=>{const t=e;return"string"==typeof t.type&&"meshLineMaterial"===t.type}));else{const e=r;"string"==typeof e.type&&"meshLineMaterial"===e.type&&(o=e)}return"object"==typeof(null==(e=o)?void 0:e.props)&&t.setValues(o.props),t}),[a,m,p,r]);return T.useEffect((()=>{E.uniforms.resolution.value.set(p.width,p.height)}),[p]),o.useFrame((()=>{w.current&&b.setPoints(w.current,d)})),T.createElement("group",null,o.createPortal(T.createElement("mesh",{ref:t,geometry:b,material:E}),v),T.createElement("group",{ref:x},r))}));function Ae(e,t=16,r,o,a){const[i,s]=T.useState((()=>{const e=Array.from({length:t},(()=>[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])).flat();return new n.InstancedBufferAttribute(Float32Array.from(e),16)}));return T.useLayoutEffect((()=>{if(void 0===e.current)return;const l=new m.MeshSurfaceSampler(e.current);o&&l.setWeightAttribute(o),l.build();const c=new n.Vector3,u=new n.Vector3,d=new n.Color,f=new n.Object3D;e.current.updateMatrixWorld(!0);for(let n=0;n<t;n++)l.sample(c,u,d),"function"==typeof r?r({dummy:f,sampledMesh:e.current,position:c,normal:u,color:d},n):f.position.copy(c),f.updateMatrix(),null!=a&&a.current&&a.current.setMatrixAt(n,f.matrix),f.matrix.toArray(i.array,16*n);null!=a&&a.current&&(a.current.instanceMatrix.needsUpdate=!0),i.needsUpdate=!0,s(i.clone())}),[e,a,o,t,r]),i}const Be=T.forwardRef((({isChild:e=!1,object:t,children:r,deep:n,castShadow:o,receiveShadow:a,inject:i,keys:s,...l},c)=>{var u;const d={keys:s,deep:n,inject:i,castShadow:o,receiveShadow:a};if(t=T.useMemo((()=>{if(!1===e&&!Array.isArray(t)){let e=!1;if(t.traverse((t=>{t.isSkinnedMesh&&(e=!0)})),e)return m.SkeletonUtils.clone(t)}return t}),[t,e]),Array.isArray(t))return T.createElement("group",C.default({},l,{ref:c}),t.map((e=>T.createElement(Be,C.default({key:e.uuid,object:e},d)))),r);const{children:f,...p}=function(e,{keys:t=["near","far","color","distance","decay","penumbra","angle","intensity","skeleton","visible","castShadow","receiveShadow","morphTargetDictionary","morphTargetInfluences","name","geometry","material","position","rotation","scale","up","userData","bindMode","bindMatrix","bindMatrixInverse","skeleton"],deep:r,inject:n,castShadow:o,receiveShadow:a}){let i=L.default(e,t);return r&&(i.geometry&&"materialsOnly"!==r&&(i.geometry=i.geometry.clone()),i.material&&"geometriesOnly"!==r&&(i.material=i.material.clone())),n&&(i="function"==typeof n?{...i,children:n(e)}:T.isValidElement(n)?{...i,children:n}:{...i,...n}),e instanceof R.Mesh&&(o&&(i.castShadow=!0),a&&(i.receiveShadow=!0)),i}(t,d),h=t.type[0].toLowerCase()+t.type.slice(1);return T.createElement(h,C.default({},p,l,{ref:c}),(null==(u=t)?void 0:u.children).map((e=>"Bone"===e.type?T.createElement("primitive",C.default({key:e.uuid,object:e},d)):T.createElement(Be,C.default({key:e.uuid,object:e},d,{isChild:!0})))),r,f)})),Oe=T.createContext(null),Ie=T.forwardRef((({resolution:e=28,maxPolyCount:t=1e4,enableUvs:r=!1,enableColors:n=!1,children:a,...i},s)=>{const l=T.useRef(null),c=T.useMemo((()=>new m.MarchingCubes(e,null,r,n,t)),[e,t,r,n]),u=T.useMemo((()=>({getParent:()=>l})),[]);return o.useFrame((()=>{c.reset()}),-1),T.createElement(T.Fragment,null,T.createElement("primitive",C.default({object:c,ref:F.default([l,s])},i),T.createElement(Oe.Provider,{value:u},a)))})),Ue=T.forwardRef((({strength:e=.5,subtract:t=12,color:r,...n},a)=>{const{getParent:i}=T.useContext(Oe),s=T.useMemo((()=>i()),[i]),l=T.useRef(),c=new R.Vector3;return o.useFrame((n=>{s.current&&l.current&&(l.current.getWorldPosition(c),s.current.addBall(.5+.5*c.x,.5+.5*c.y,.5+.5*c.z,e,t,r))})),T.createElement("group",C.default({ref:F.default([a,l])},n))})),Ve=T.forwardRef((({planeType:e="x",strength:t=.5,subtract:r=12,...n},a)=>{const{getParent:i}=T.useContext(Oe),s=T.useMemo((()=>i()),[i]),l=T.useRef(),c=T.useMemo((()=>"x"===e?"addPlaneX":"y"===e?"addPlaneY":"addPlaneZ"),[e]);return o.useFrame((()=>{s.current&&l.current&&s.current[c](t,r)})),T.createElement("group",C.default({ref:F.default([a,l])},n))}));function je(e=[0,0,0]){return function(e){return Array.isArray(e)}(e)?e:e instanceof R.Vector3||e instanceof R.Euler?[e.x,e.y,e.z]:[e,e,e]}const We=T.forwardRef((function({debug:e,mesh:t,children:r,position:n,rotation:a,scale:i,...s},l){const c=T.useRef(null);T.useImperativeHandle(l,(()=>c.current));const u=T.useRef(null);return T.useLayoutEffect((()=>{const e=(null==t?void 0:t.current)||c.current.parent,r=c.current;if(!(e instanceof R.Mesh))throw new Error('Decal must have a Mesh as parent or specify its "mesh" prop');const s={position:new R.Vector3,rotation:new R.Euler,scale:new R.Vector3(1,1,1)};if(e){o.applyProps(s,{position:n,scale:i});const t=e.matrixWorld.clone();if(e.matrixWorld.identity(),a&&"number"!=typeof a)o.applyProps(s,{rotation:a});else{const t=new R.Object3D;t.position.copy(s.position),t.lookAt(e.position),"number"==typeof a&&t.rotateZ(a),o.applyProps(s,{rotation:t.rotation})}return r.geometry=new m.DecalGeometry(e,s.position,s.rotation,s.scale),u.current&&o.applyProps(u.current,s),e.matrixWorld=t,()=>{r.geometry.dispose()}}}),[t,...je(n),...je(i),...je(a)]),T.createElement("mesh",{ref:c},r||T.createElement("meshStandardMaterial",C.default({transparent:!0,polygonOffset:!0,polygonOffsetFactor:-10},s)),e&&T.createElement("mesh",{ref:u},T.createElement("boxGeometry",null),T.createElement("meshNormalMaterial",{wireframe:!0}),T.createElement("axesHelper",null)))})),Ne=t.forwardRef((function({src:e,skipFill:r,skipStrokes:a,fillMaterial:i,strokeMaterial:s,fillMeshProps:l,strokeMeshProps:c,...u},d){const f=o.useLoader(m.SVGLoader,e.startsWith("<svg")?`data:image/svg+xml;utf8,${e}`:e),p=t.useMemo((()=>a?[]:f.paths.map((e=>{var t;return void 0===(null==(t=e.userData)?void 0:t.style.stroke)||"none"===e.userData.style.stroke?null:e.subPaths.map((t=>m.SVGLoader.pointsToStroke(t.getPoints(),e.userData.style)))}))),[f,a]);return t.useEffect((()=>()=>p.forEach((e=>e&&e.map((e=>e.dispose()))))),[p]),T.createElement("object3D",C.default({ref:d},u),T.createElement("object3D",{scale:[1,-1,1]},f.paths.map(((e,o)=>{var u,d;return T.createElement(t.Fragment,{key:o},!r&&void 0!==(null==(u=e.userData)?void 0:u.style.fill)&&"none"!==e.userData.style.fill&&m.SVGLoader.createShapes(e).map(((t,r)=>T.createElement("mesh",C.default({key:r},l),T.createElement("shapeGeometry",{args:[t]}),T.createElement("meshBasicMaterial",C.default({color:e.userData.style.fill,opacity:e.userData.style.fillOpacity,transparent:!0,side:n.DoubleSide,depthWrite:!1},i))))),!a&&void 0!==(null==(d=e.userData)?void 0:d.style.stroke)&&"none"!==e.userData.style.stroke&&e.subPaths.map(((t,r)=>T.createElement("mesh",C.default({key:r,geometry:p[o][r]},c),T.createElement("meshBasicMaterial",C.default({color:e.userData.style.stroke,opacity:e.userData.style.strokeOpacity,transparent:!0,side:n.DoubleSide,depthWrite:!1},s))))))}))))}));let Ge=null;function He(e,t,r){return n=>{r&&r(n),e&&(Ge||(Ge=new m.DRACOLoader),Ge.setDecoderPath("string"==typeof e?e:"https://www.gstatic.com/draco/versioned/decoders/1.5.5/"),n.setDRACOLoader(Ge)),t&&n.setMeshoptDecoder("function"==typeof m.MeshoptDecoder?m.MeshoptDecoder():m.MeshoptDecoder)}}function $e(e,t=!0,r=!0,n){return o.useLoader(m.GLTFLoader,e,He(t,r,n))}$e.preload=(e,t=!0,r=!0,n)=>o.useLoader.preload(m.GLTFLoader,e,He(t,r,n)),$e.clear=e=>o.useLoader.clear(m.GLTFLoader,e);const qe=T.forwardRef((({src:e,...t},r)=>{const{scene:n}=$e(e);return T.createElement(Be,C.default({ref:r},t,{object:n}))}));function Xe(e,t,r){const n=o.useThree((e=>e.size)),a=o.useThree((e=>e.viewport)),i="number"==typeof e?e:n.width*a.dpr,s="number"==typeof t?t:n.height*a.dpr,l=("number"==typeof e?r:e)||{},{samples:c=0,depth:u,...m}=l,d=T.useMemo((()=>{const e=new R.WebGLRenderTarget(i,s,{minFilter:R.LinearFilter,magFilter:R.LinearFilter,type:R.HalfFloatType,...m});return u&&(e.depthTexture=new R.DepthTexture(i,s,R.FloatType)),e.samples=c,e}),[]);return T.useLayoutEffect((()=>{d.setSize(i,s),c&&(d.samples=c)}),[c,d,i,s]),T.useEffect((()=>()=>d.dispose()),[]),d}const Ye=T.forwardRef((({envMap:e,resolution:t=256,frames:r=1/0,children:n,makeDefault:a,...i},s)=>{const l=o.useThree((({set:e})=>e)),c=o.useThree((({camera:e})=>e)),u=o.useThree((({size:e})=>e)),m=T.useRef(null),d=T.useRef(null),f=Xe(t);T.useLayoutEffect((()=>{i.manual||m.current.updateProjectionMatrix()}),[u,i]),T.useLayoutEffect((()=>{m.current.updateProjectionMatrix()})),T.useLayoutEffect((()=>{if(a){const e=c;return l((()=>({camera:m.current}))),()=>l((()=>({camera:e})))}}),[m,a,l]);let p=0,h=null;const v="function"==typeof n;return o.useFrame((t=>{v&&(r===1/0||p<r)&&(d.current.visible=!1,t.gl.setRenderTarget(f),h=t.scene.background,e&&(t.scene.background=e),t.gl.render(t.scene,m.current),t.scene.background=h,t.gl.setRenderTarget(null),d.current.visible=!0,p++)})),T.createElement(T.Fragment,null,T.createElement("orthographicCamera",C.default({left:u.width/-2,right:u.width/2,top:u.height/2,bottom:u.height/-2,ref:F.default([m,s])},i),!v&&n),T.createElement("group",{ref:d},v&&n(f.texture)))})),Ke=T.forwardRef((({envMap:e,resolution:t=256,frames:r=1/0,makeDefault:n,children:a,...i},s)=>{const l=o.useThree((({set:e})=>e)),c=o.useThree((({camera:e})=>e)),u=o.useThree((({size:e})=>e)),m=T.useRef(null),d=T.useRef(null),f=Xe(t);T.useLayoutEffect((()=>{i.manual||(m.current.aspect=u.width/u.height)}),[u,i]),T.useLayoutEffect((()=>{m.current.updateProjectionMatrix()}));let p=0,h=null;const v="function"==typeof a;return o.useFrame((t=>{v&&(r===1/0||p<r)&&(d.current.visible=!1,t.gl.setRenderTarget(f),h=t.scene.background,e&&(t.scene.background=e),t.gl.render(t.scene,m.current),t.scene.background=h,t.gl.setRenderTarget(null),d.current.visible=!0,p++)})),T.useLayoutEffect((()=>{if(n){const e=c;return l((()=>({camera:m.current}))),()=>l((()=>({camera:e})))}}),[m,n,l]),T.createElement(T.Fragment,null,T.createElement("perspectiveCamera",C.default({ref:F.default([m,s])},i),!v&&a),T.createElement("group",{ref:d},v&&a(f.texture)))}));function Ze({resolution:e=256,near:r=.1,far:a=1e3,envMap:i,fog:s}={}){const l=o.useThree((({gl:e})=>e)),c=o.useThree((({scene:e})=>e)),u=t.useMemo((()=>{const t=new n.WebGLCubeRenderTarget(e);return t.texture.type=n.HalfFloatType,t}),[e]);t.useEffect((()=>()=>{u.dispose()}),[u]);const m=t.useMemo((()=>new R.CubeCamera(r,a,u)),[r,a,u]);let d,f;const p=T.useCallback((()=>{d=c.fog,f=c.background,c.background=i||f,c.fog=s||d,m.update(l,c),c.fog=d,c.background=f}),[l,c,m]);return{fbo:u,camera:m,update:p}}const Qe=T.forwardRef(((e,t)=>{const{camera:r,onChange:n,makeDefault:a,...i}=e,s=o.useThree((e=>e.camera)),l=o.useThree((e=>e.invalidate)),c=o.useThree((e=>e.get)),u=o.useThree((e=>e.set)),d=r||s,[f]=T.useState((()=>new m.DeviceOrientationControls(d)));return T.useEffect((()=>{const e=e=>{l(),n&&n(e)};return null==f||null==f.addEventListener||f.addEventListener("change",e),()=>null==f||null==f.removeEventListener?void 0:f.removeEventListener("change",e)}),[n,f,l]),o.useFrame((()=>null==f?void 0:f.update()),-1),T.useEffect((()=>{const e=f;return null==e||e.connect(),()=>null==e?void 0:e.dispose()}),[f]),T.useEffect((()=>{if(a){const e=c().controls;return u({controls:f}),()=>u({controls:e})}}),[a,f]),f?T.createElement("primitive",C.default({ref:t,object:f},i)):null})),Je=T.forwardRef((({domElement:e,...t},r)=>{const{onChange:n,makeDefault:a,...i}=t,s=o.useThree((e=>e.invalidate)),l=o.useThree((e=>e.camera)),c=o.useThree((e=>e.gl)),u=o.useThree((e=>e.events)),d=o.useThree((e=>e.get)),f=o.useThree((e=>e.set)),p=e||u.connected||c.domElement,h=T.useMemo((()=>new m.FlyControls(l)),[l]);return T.useEffect((()=>(h.connect(p),()=>{h.dispose()})),[p,h,s]),T.useEffect((()=>{const e=e=>{s(),n&&n(e)};return null==h.addEventListener||h.addEventListener("change",e),()=>null==h.removeEventListener?void 0:h.removeEventListener("change",e)}),[n,s]),T.useEffect((()=>{if(a){const e=d().controls;return f({controls:h}),()=>f({controls:e})}}),[a,h]),o.useFrame(((e,t)=>h.update(t))),T.createElement("primitive",C.default({ref:r,object:h,args:[l,p]},i))})),et=T.forwardRef(((e={enableDamping:!0},t)=>{const{domElement:r,camera:n,makeDefault:a,onChange:i,onStart:s,onEnd:l,...c}=e,u=o.useThree((e=>e.invalidate)),d=o.useThree((e=>e.camera)),f=o.useThree((e=>e.gl)),p=o.useThree((e=>e.events)),h=o.useThree((e=>e.set)),v=o.useThree((e=>e.get)),x=r||p.connected||f.domElement,y=n||d,g=T.useMemo((()=>new m.MapControls(y)),[y]);return T.useEffect((()=>{g.connect(x);const e=e=>{u(),i&&i(e)};return g.addEventListener("change",e),s&&g.addEventListener("start",s),l&&g.addEventListener("end",l),()=>{g.dispose(),g.removeEventListener("change",e),s&&g.removeEventListener("start",s),l&&g.removeEventListener("end",l)}}),[i,s,l,g,u,x]),T.useEffect((()=>{if(a){const e=v().controls;return h({controls:g}),()=>h({controls:e})}}),[a,g]),o.useFrame((()=>g.update()),-1),T.createElement("primitive",C.default({ref:t,object:g,enableDamping:!0},c))})),tt=T.forwardRef((({makeDefault:e,camera:t,regress:r,domElement:n,enableDamping:a=!0,keyEvents:i=!1,onChange:s,onStart:l,onEnd:c,...u},d)=>{const f=o.useThree((e=>e.invalidate)),p=o.useThree((e=>e.camera)),h=o.useThree((e=>e.gl)),v=o.useThree((e=>e.events)),x=o.useThree((e=>e.setEvents)),y=o.useThree((e=>e.set)),g=o.useThree((e=>e.get)),w=o.useThree((e=>e.performance)),b=t||p,E=n||v.connected||h.domElement,M=T.useMemo((()=>new m.OrbitControls(b)),[b]);return o.useFrame((()=>{M.enabled&&M.update()}),-1),T.useEffect((()=>(i&&M.connect(!0===i?E:i),M.connect(E),()=>{M.dispose()})),[i,E,r,M,f]),T.useEffect((()=>{const e=e=>{f(),r&&w.regress(),s&&s(e)},t=e=>{l&&l(e)},n=e=>{c&&c(e)};return M.addEventListener("change",e),M.addEventListener("start",t),M.addEventListener("end",n),()=>{M.removeEventListener("start",t),M.removeEventListener("end",n),M.removeEventListener("change",e)}}),[s,l,c,M,f,x]),T.useEffect((()=>{if(e){const e=g().controls;return y({controls:M}),()=>y({controls:e})}}),[e,M]),T.createElement("primitive",C.default({ref:d,object:M,enableDamping:a},u))})),rt=T.forwardRef((({makeDefault:e,camera:t,domElement:r,regress:n,onChange:a,onStart:i,onEnd:s,...l},c)=>{const{invalidate:u,camera:d,gl:f,events:p,set:h,get:v,performance:x,viewport:y}=o.useThree(),g=t||d,w=r||p.connected||f.domElement,b=T.useMemo((()=>new m.TrackballControls(g)),[g]);return o.useFrame((()=>{b.enabled&&b.update()}),-1),T.useEffect((()=>(b.connect(w),()=>{b.dispose()})),[w,n,b,u]),T.useEffect((()=>{const e=e=>{u(),n&&x.regress(),a&&a(e)};return b.addEventListener("change",e),i&&b.addEventListener("start",i),s&&b.addEventListener("end",s),()=>{i&&b.removeEventListener("start",i),s&&b.removeEventListener("end",s),b.removeEventListener("change",e)}}),[a,i,s,b,u]),T.useEffect((()=>{b.handleResize()}),[y]),T.useEffect((()=>{if(e){const e=v().controls;return h({controls:b}),()=>h({controls:e})}}),[e,b]),T.createElement("primitive",C.default({ref:c,object:b},l))})),nt=t.forwardRef((({camera:e,makeDefault:r,regress:n,domElement:a,onChange:i,onStart:s,onEnd:l,...c},u)=>{const d=o.useThree((e=>e.invalidate)),f=o.useThree((e=>e.camera)),p=o.useThree((e=>e.gl)),h=o.useThree((e=>e.events)),v=o.useThree((e=>e.set)),x=o.useThree((e=>e.get)),y=o.useThree((e=>e.performance)),g=e||f,w=a||h.connected||p.domElement,b=t.useMemo((()=>new m.ArcballControls(g)),[g]);return o.useFrame((()=>{b.enabled&&b.update()}),-1),t.useEffect((()=>(b.connect(w),()=>{b.dispose()})),[w,n,b,d]),t.useEffect((()=>{const e=e=>{d(),n&&y.regress(),i&&i(e)};return b.addEventListener("change",e),s&&b.addEventListener("start",s),l&&b.addEventListener("end",l),()=>{b.removeEventListener("change",e),s&&b.removeEventListener("start",s),l&&b.removeEventListener("end",l)}}),[i,s,l]),t.useEffect((()=>{if(r){const e=x().controls;return v({controls:b}),()=>v({controls:e})}}),[r,b]),T.createElement("primitive",C.default({ref:u,object:b},c))})),ot=T.forwardRef((({children:e,domElement:t,onChange:r,onMouseDown:n,onMouseUp:a,onObjectChange:i,object:s,makeDefault:l,...c},u)=>{const d=["enabled","axis","mode","translationSnap","rotationSnap","scaleSnap","space","size","showX","showY","showZ"],{camera:f,...p}=c,h=L.default(p,d),v=A.default(p,d),x=o.useThree((e=>e.controls)),y=o.useThree((e=>e.gl)),g=o.useThree((e=>e.events)),w=o.useThree((e=>e.camera)),b=o.useThree((e=>e.invalidate)),E=o.useThree((e=>e.get)),M=o.useThree((e=>e.set)),z=f||w,S=t||g.connected||y.domElement,P=T.useMemo((()=>new m.TransformControls(z,S)),[z,S]),D=T.useRef();T.useLayoutEffect((()=>(s?P.attach(s instanceof R.Object3D?s:s.current):D.current instanceof R.Object3D&&P.attach(D.current),()=>{P.detach()})),[s,e,P]),T.useEffect((()=>{if(x){const e=e=>x.enabled=!e.value;return P.addEventListener("dragging-changed",e),()=>P.removeEventListener("dragging-changed",e)}}),[P,x]);const k=T.useRef(),F=T.useRef(),_=T.useRef(),B=T.useRef();return T.useLayoutEffect((()=>{k.current=r}),[r]),T.useLayoutEffect((()=>{F.current=n}),[n]),T.useLayoutEffect((()=>{_.current=a}),[a]),T.useLayoutEffect((()=>{B.current=i}),[i]),T.useEffect((()=>{const e=e=>{b(),null==k.current||k.current(e)},t=e=>null==F.current?void 0:F.current(e),r=e=>null==_.current?void 0:_.current(e),n=e=>null==B.current?void 0:B.current(e);return P.addEventListener("change",e),P.addEventListener("mouseDown",t),P.addEventListener("mouseUp",r),P.addEventListener("objectChange",n),()=>{P.removeEventListener("change",e),P.removeEventListener("mouseDown",t),P.removeEventListener("mouseUp",r),P.removeEventListener("objectChange",n)}}),[b,P]),T.useEffect((()=>{if(l){const e=E().controls;return M({controls:P}),()=>M({controls:e})}}),[l,P]),P?T.createElement(T.Fragment,null,T.createElement("primitive",C.default({ref:u,object:P},h)),T.createElement("group",C.default({ref:D},v),e)):null})),at=T.forwardRef((({domElement:e,selector:t,onChange:r,onLock:n,onUnlock:a,enabled:i=!0,makeDefault:s,...l},c)=>{const{camera:u,...d}=l,f=o.useThree((e=>e.setEvents)),p=o.useThree((e=>e.gl)),h=o.useThree((e=>e.camera)),v=o.useThree((e=>e.invalidate)),x=o.useThree((e=>e.events)),y=o.useThree((e=>e.get)),g=o.useThree((e=>e.set)),w=u||h,b=e||x.connected||p.domElement,[E]=T.useState((()=>new m.PointerLockControls(w)));return T.useEffect((()=>{if(i){E.connect(b);const e=y().events.compute;return f({compute(e,t){const r=t.size.width/2,n=t.size.height/2;t.pointer.set(r/t.size.width*2-1,-n/t.size.height*2+1),t.raycaster.setFromCamera(t.pointer,t.camera)}}),()=>{E.disconnect(),f({compute:e})}}}),[i,E]),T.useEffect((()=>{const e=e=>{v(),r&&r(e)};E.addEventListener("change",e),n&&E.addEventListener("lock",n),a&&E.addEventListener("unlock",a);const o=()=>E.lock(),i=t?Array.from(document.querySelectorAll(t)):[document];return i.forEach((e=>e&&e.addEventListener("click",o))),()=>{E.removeEventListener("change",e),n&&E.addEventListener("lock",n),a&&E.addEventListener("unlock",a),i.forEach((e=>e?e.removeEventListener("click",o):void 0))}}),[r,n,a,t,E,v]),T.useEffect((()=>{if(s){const e=y().controls;return g({controls:E}),()=>g({controls:e})}}),[s,E]),T.createElement("primitive",C.default({ref:c,object:E},d))})),it=T.forwardRef((({domElement:e,makeDefault:t,...r},n)=>{const a=o.useThree((e=>e.camera)),i=o.useThree((e=>e.gl)),s=o.useThree((e=>e.events)),l=o.useThree((e=>e.get)),c=o.useThree((e=>e.set)),u=e||s.connected||i.domElement,[d]=T.useState((()=>new m.FirstPersonControls(a,u)));return T.useEffect((()=>{if(t){const e=l().controls;return c({controls:d}),()=>c({controls:e})}}),[t,d]),o.useFrame(((e,t)=>{d.update(t)}),-1),d?T.createElement("primitive",C.default({ref:n,object:d},r)):null})),st=t.forwardRef(((e,r)=>{t.useMemo((()=>{B.default.install({THREE:R}),o.extend({CameraControlsImpl:B.default})}),[]);const{camera:n,domElement:a,makeDefault:i,onStart:s,onEnd:l,onChange:c,regress:u,...m}=e,d=o.useThree((e=>e.camera)),f=o.useThree((e=>e.gl)),p=o.useThree((e=>e.invalidate)),h=o.useThree((e=>e.events)),v=o.useThree((e=>e.setEvents)),x=o.useThree((e=>e.set)),y=o.useThree((e=>e.get)),g=o.useThree((e=>e.performance)),w=n||d,b=a||h.connected||f.domElement,E=t.useMemo((()=>new B.default(w)),[w]);return o.useFrame(((e,t)=>{E.enabled&&E.update(t)}),-1),t.useEffect((()=>(E.connect(b),()=>{E.disconnect()})),[b,E]),T.useEffect((()=>{const e=e=>{p(),u&&g.regress(),c&&c(e)},t=e=>{s&&s(e)},r=e=>{l&&l(e)};return E.addEventListener("update",e),E.addEventListener("controlstart",t),E.addEventListener("controlend",r),E.addEventListener("control",e),E.addEventListener("transitionstart",e),E.addEventListener("wake",e),()=>{E.removeEventListener("update",e),E.removeEventListener("controlstart",t),E.removeEventListener("controlend",r),E.removeEventListener("control",e),E.removeEventListener("transitionstart",e),E.removeEventListener("wake",e)}}),[E,s,l,p,v,u,c]),t.useEffect((()=>{if(i){const e=y().controls;return x({controls:E}),()=>x({controls:e})}}),[i,E]),T.createElement("primitive",C.default({ref:r,object:E},m))}));function lt({defaultScene:e,defaultCamera:t,renderPriority:r=1}){const{gl:n,scene:a,camera:i}=o.useThree();let s;return o.useFrame((()=>{s=n.autoClear,1===r&&(n.autoClear=!0,n.render(e,t)),n.autoClear=!1,n.clearDepth(),n.render(a,i),n.autoClear=s}),r),T.createElement(T.Fragment,null)}function ct({children:e,renderPriority:t=1}){const{scene:r,camera:n}=o.useThree(),[a]=T.useState((()=>new R.Scene));return T.createElement(T.Fragment,null,o.createPortal(T.createElement(T.Fragment,null,e,T.createElement(lt,{defaultScene:r,defaultCamera:n,renderPriority:t})),a,{events:{priority:t+1}}))}const ut=T.createContext({}),mt=()=>T.useContext(ut),dt=2*Math.PI,ft=new n.Object3D,pt=new n.Matrix4,[ht,vt]=[new n.Quaternion,new n.Quaternion],xt=new n.Vector3,yt=new n.Vector3,gt="#f0f0f0",wt="#999",bt="black",Et="black",Mt=["Right","Left","Top","Bottom","Front","Back"],zt=e=>new n.Vector3(...e).multiplyScalar(.38),St=[[1,1,1],[1,1,-1],[1,-1,1],[1,-1,-1],[-1,1,1],[-1,1,-1],[-1,-1,1],[-1,-1,-1]].map(zt),Ct=[.25,.25,.25],Tt=[[1,1,0],[1,0,1],[1,0,-1],[1,-1,0],[0,1,1],[0,1,-1],[0,-1,1],[0,-1,-1],[-1,1,0],[-1,0,1],[-1,0,-1],[-1,-1,0]].map(zt),Pt=Tt.map((e=>e.toArray().map((e=>0==e?.5:.25)))),Rt=({hover:e,index:t,font:r="20px Inter var, Arial, sans-serif",faces:a=Mt,color:i=gt,hoverColor:s=wt,textColor:l=bt,strokeColor:c=Et,opacity:u=1})=>{const m=o.useThree((e=>e.gl)),d=T.useMemo((()=>{const e=document.createElement("canvas");e.width=128,e.height=128;const o=e.getContext("2d");return o.fillStyle=i,o.fillRect(0,0,e.width,e.height),o.strokeStyle=c,o.strokeRect(0,0,e.width,e.height),o.font=r,o.textAlign="center",o.fillStyle=l,o.fillText(a[t].toUpperCase(),64,76),new n.CanvasTexture(e)}),[t,a,r,i,l,c]);return T.createElement("meshLambertMaterial",{map:d,"map-anisotropy":m.capabilities.getMaxAnisotropy()||1,attach:`material-${t}`,color:e?s:"white",transparent:!0,opacity:u})},Dt=e=>{const{tweenCamera:t}=mt(),[r,n]=T.useState(null);return T.createElement("mesh",{onPointerOut:e=>{e.stopPropagation(),n(null)},onPointerMove:e=>{e.stopPropagation(),n(Math.floor(e.faceIndex/2))},onClick:e.onClick||(e=>{e.stopPropagation(),t(e.face.normal)})},[...Array(6)].map(((t,n)=>T.createElement(Rt,C.default({key:n,index:n,hover:r===n},e)))),T.createElement("boxGeometry",null))},kt=({onClick:e,dimensions:t,position:r,hoverColor:n=wt})=>{const{tweenCamera:o}=mt(),[a,i]=T.useState(!1);return T.createElement("mesh",{scale:1.01,position:r,onPointerOver:e=>{e.stopPropagation(),i(!0)},onPointerOut:e=>{e.stopPropagation(),i(!1)},onClick:e||(e=>{e.stopPropagation(),o(r)})},T.createElement("meshBasicMaterial",{color:a?n:"white",transparent:!0,opacity:.6,visible:a}),T.createElement("boxGeometry",{args:t}))};function Ft({scale:e=[.8,.05,.05],color:t,rotation:r}){return T.createElement("group",{rotation:r},T.createElement("mesh",{position:[.4,0,0]},T.createElement("boxGeometry",{args:e}),T.createElement("meshBasicMaterial",{color:t,toneMapped:!1})))}function _t({onClick:e,font:t,disabled:r,arcStyle:a,label:i,labelColor:s,axisHeadScale:l=1,...c}){const u=o.useThree((e=>e.gl)),m=T.useMemo((()=>{const e=document.createElement("canvas");e.width=64,e.height=64;const r=e.getContext("2d");return r.beginPath(),r.arc(32,32,16,0,2*Math.PI),r.closePath(),r.fillStyle=a,r.fill(),i&&(r.font=t,r.textAlign="center",r.fillStyle=s,r.fillText(i,32,41)),new n.CanvasTexture(e)}),[a,i,s,t]),[d,f]=T.useState(!1),p=(i?1:.75)*(d?1.2:1)*l;return T.createElement("sprite",C.default({scale:p,onPointerOver:r?void 0:e=>{e.stopPropagation(),f(!0)},onPointerOut:r?void 0:e||(e=>{e.stopPropagation(),f(!1)})},c),T.createElement("spriteMaterial",{map:m,"map-anisotropy":u.capabilities.getMaxAnisotropy()||1,alphaTest:.3,opacity:i?1:.75,toneMapped:!1}))}const Lt=Ee({cellSize:.5,sectionSize:1,fadeDistance:100,fadeStrength:1,cellThickness:.5,sectionThickness:1,cellColor:new R.Color,sectionColor:new R.Color,infiniteGrid:!1,followCamera:!1},"\n    varying vec3 worldPosition;\n    uniform float fadeDistance;\n    uniform bool infiniteGrid;\n    uniform bool followCamera;\n\n    void main() {\n      worldPosition = position.xzy;\n      if (infiniteGrid) worldPosition *= 1.0 + fadeDistance;\n      if (followCamera) worldPosition.xz +=cameraPosition.xz;\n\n      gl_Position = projectionMatrix * modelViewMatrix * vec4(worldPosition, 1.0);\n    }\n  ","\n    varying vec3 worldPosition;\n    uniform float cellSize;\n    uniform float sectionSize;\n    uniform vec3 cellColor;\n    uniform vec3 sectionColor;\n    uniform float fadeDistance;\n    uniform float fadeStrength;\n    uniform float cellThickness;\n    uniform float sectionThickness;\n\n    float getGrid(float size, float thickness) {\n      vec2 r = worldPosition.xz / size;\n      vec2 grid = abs(fract(r - 0.5) - 0.5) / fwidth(r);\n      float line = min(grid.x, grid.y) + 1. - thickness;\n      return 1.0 - min(line, 1.);\n    }\n\n    void main() {\n      float g1 = getGrid(cellSize, cellThickness);\n      float g2 = getGrid(sectionSize, sectionThickness);\n\n      float d = 1.0 - min(distance(cameraPosition.xz, worldPosition.xz) / fadeDistance, 1.);\n      vec3 color = mix(cellColor, sectionColor, min(1.,sectionThickness * g2));\n\n      gl_FragColor = vec4(color, (g1 + g2) * pow(d,fadeStrength));\n      gl_FragColor.a = mix(0.75 * gl_FragColor.a, gl_FragColor.a, g2);\n      if (gl_FragColor.a <= 0.0) discard;\n\n      #include <tonemapping_fragment>\n      #include <encodings_fragment>\n    }\n  "),At=T.forwardRef((({args:e,cellColor:t="#000000",sectionColor:r="#2080ff",cellSize:n=.5,sectionSize:a=1,followCamera:i=!1,infiniteGrid:s=!1,fadeDistance:l=100,fadeStrength:c=1,cellThickness:u=.5,sectionThickness:m=1,side:d=R.BackSide,...f},p)=>{o.extend({GridMaterial:Lt});const h={cellSize:n,sectionSize:a,cellColor:t,sectionColor:r,cellThickness:u,sectionThickness:m},v={fadeDistance:l,fadeStrength:c,infiniteGrid:s,followCamera:i};return T.createElement("mesh",C.default({ref:p,frustumCulled:!1},f),T.createElement("gridMaterial",C.default({transparent:!0,"extensions-derivatives":!0,side:d},h,v)),T.createElement("planeGeometry",{args:e}))}));function Bt(e,{path:t}){const[r]=o.useLoader(n.CubeTextureLoader,[e],(e=>e.setPath(t)));return r}function Ot(e){return o.useLoader(m.FBXLoader,e)}Bt.preload=(e,{path:t})=>o.useLoader.preload(n.CubeTextureLoader,[e],(e=>e.setPath(t))),Ot.preload=e=>o.useLoader.preload(m.FBXLoader,e),Ot.clear=e=>o.useLoader.clear(m.FBXLoader,e);const It="https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master";function Ut(e,r=`${It}/basis/`){const n=o.useThree((e=>e.gl)),a=o.useLoader(m.KTX2Loader,Me(e)?Object.values(e):e,(e=>{e.detectSupport(n),e.setTranscoderPath(r)}));if(t.useEffect((()=>{(Array.isArray(a)?a:[a]).forEach(n.initTexture)}),[n,a]),Me(e)){const t=Object.keys(e),r={};return t.forEach((e=>Object.assign(r,{[e]:a[t.indexOf(e)]}))),r}return a}function Vt(e,t){"function"==typeof e?e(t):null!=e&&(e.current=t)}Ut.preload=(e,t=`${It}/basis/`)=>o.useLoader.preload(m.KTX2Loader,e,(e=>{e.setTranscoderPath(t)})),Ut.clear=e=>o.useLoader.clear(m.KTX2Loader,e);function jt(e,t,...r){const n=T.useRef(),a=o.useThree((e=>e.scene));return T.useLayoutEffect((()=>{let o;if(e&&null!=e&&e.current&&t&&(n.current=o=new t(e.current,...r)),o)return a.add(o),()=>{n.current=void 0,a.remove(o),null==o.dispose||o.dispose()}}),[a,t,e,...r]),o.useFrame((()=>{var e;null==(e=n.current)||null==e.update||e.update()})),n}const Wt=e=>e.isMesh;const Nt=T.forwardRef((({enabled:e=!0,firstHitOnly:t=!1,children:r,strategy:a=b.SAH,verbose:i=!1,setBoundingBox:s=!0,maxDepth:l=40,maxLeafTris:c=10,...u},m)=>{const d=T.useRef(null),f=o.useThree((e=>e.raycaster));return T.useImperativeHandle(m,(()=>d.current),[]),T.useEffect((()=>{if(e){const e={strategy:a,verbose:i,setBoundingBox:s,maxDepth:l,maxLeafTris:c},r=d.current;return f.firstHitOnly=t,r.traverse((t=>{Wt(t)&&!t.geometry.boundsTree&&t.raycast===n.Mesh.prototype.raycast&&(t.raycast=b.acceleratedRaycast,t.geometry.computeBoundsTree=b.computeBoundsTree,t.geometry.disposeBoundsTree=b.disposeBoundsTree,t.geometry.computeBoundsTree(e))})),()=>{delete f.firstHitOnly,r.traverse((e=>{Wt(e)&&e.geometry.boundsTree&&(e.geometry.disposeBoundsTree(),e.raycast=n.Mesh.prototype.raycast)}))}}})),T.createElement("group",C.default({ref:d},u),r)}));const Gt=new R.Box3,Ht=new R.Vector3;const $t=e=>Math.sqrt(1-Math.pow(e-1,2));class qt{constructor({size:e=256,maxAge:t=750,radius:r=.3,intensity:n=.2,interpolate:o=0,smoothing:a=0,minForce:i=.3,blend:s="screen",ease:l=$t}={}){this.size=e,this.maxAge=t,this.radius=r,this.intensity=n,this.ease=l,this.interpolate=o,this.smoothing=a,this.minForce=i,this.blend=s,this.trail=[],this.force=0,this.initTexture()}initTexture(){this.canvas=document.createElement("canvas"),this.canvas.width=this.canvas.height=this.size,this.ctx=this.canvas.getContext("2d"),this.ctx.fillStyle="black",this.ctx.fillRect(0,0,this.canvas.width,this.canvas.height),this.texture=new n.Texture(this.canvas),this.canvas.id="touchTexture",this.canvas.style.width=this.canvas.style.height=`${this.canvas.width}px`}update(e){this.clear(),this.trail.forEach(((t,r)=>{t.age+=1e3*e,t.age>this.maxAge&&this.trail.splice(r,1)})),this.trail.length||(this.force=0),this.trail.forEach((e=>{this.drawTouch(e)})),this.texture.needsUpdate=!0}clear(){this.ctx.globalCompositeOperation="source-over",this.ctx.fillStyle="black",this.ctx.fillRect(0,0,this.canvas.width,this.canvas.height)}addTouch(e){const t=this.trail[this.trail.length-1];if(t){const r=t.x-e.x,n=t.y-e.y,o=r*r+n*n,a=Math.max(this.minForce,Math.min(1e4*o,1));if(this.force=function(e,t,r=.9){return t*r+e*(1-r)}(a,this.force,this.smoothing),this.interpolate){const e=Math.ceil(o/Math.pow(.5*this.radius/this.interpolate,2));if(e>1)for(let o=1;o<e;o++)this.trail.push({x:t.x-r/e*o,y:t.y-n/e*o,age:0,force:a})}}this.trail.push({x:e.x,y:e.y,age:0,force:this.force})}drawTouch(e){const t={x:e.x*this.size,y:(1-e.y)*this.size};let r=1;r=e.age<.3*this.maxAge?this.ease(e.age/(.3*this.maxAge)):this.ease(1-(e.age-.3*this.maxAge)/(.7*this.maxAge)),r*=e.force,this.ctx.globalCompositeOperation=this.blend;const n=this.size*this.radius*r,o=this.ctx.createRadialGradient(t.x,t.y,Math.max(0,.25*n),t.x,t.y,Math.max(0,n));o.addColorStop(0,`rgba(255, 255, 255, ${this.intensity})`),o.addColorStop(1,"rgba(0, 0, 0, 0.0)"),this.ctx.beginPath(),this.ctx.fillStyle=o,this.ctx.arc(t.x,t.y,Math.max(0,n),0,2*Math.PI),this.ctx.fill()}}const Xt=T.forwardRef((function({children:e,disable:t,disableX:r,disableY:o,disableZ:a,left:i,right:s,top:l,bottom:c,front:u,back:m,onCentered:d,precise:f=!0,cacheKey:p=0,...h},v){const x=T.useRef(null),y=T.useRef(null),g=T.useRef(null);return T.useLayoutEffect((()=>{y.current.matrixWorld.identity();const e=(new n.Box3).setFromObject(g.current,f),p=new n.Vector3,h=new n.Sphere,v=e.max.x-e.min.x,w=e.max.y-e.min.y,b=e.max.z-e.min.z;e.getCenter(p),e.getBoundingSphere(h);const E=l?w/2:c?-w/2:0,M=i?-v/2:s?v/2:0,z=u?b/2:m?-b/2:0;y.current.position.set(t||r?0:-p.x+M,t||o?0:-p.y+E,t||a?0:-p.z+z),void 0!==d&&d({parent:x.current.parent,container:x.current,width:v,height:w,depth:b,boundingBox:e,boundingSphere:h,center:p,verticalAlignment:E,horizontalAlignment:M,depthAlignment:z})}),[p,d,l,i,u,t,r,o,a,f,s,c,m]),T.useImperativeHandle(v,(()=>x.current),[]),T.createElement("group",C.default({ref:x},h),T.createElement("group",{ref:y},T.createElement("group",{ref:g},e)))})),Yt=T.forwardRef((({font:e,color:t="#cbcbcb",bevelSize:r=.04,debug:n=!1,children:o,...a},i)=>{const[s,l]=T.useState(0),c=T.useCallback(((e=1)=>l(s+e)),[s]),u=T.useCallback(((e=1)=>l(s-e)),[s]),m=T.useMemo((()=>({incr:c,decr:u})),[c,u]);return T.useImperativeHandle(i,(()=>m),[m]),T.createElement("group",a,T.createElement(T.Suspense,{fallback:null},T.createElement(Xt,{top:!0,cacheKey:JSON.stringify({counter:s,font:e})},T.createElement(we,{bevelEnabled:!0,bevelSize:r,font:e},n?T.createElement("meshNormalMaterial",{wireframe:!0}):T.createElement("meshStandardMaterial",{color:t}),s))),o)})),Kt=T.forwardRef((({children:e,curve:t},r)=>{const[n]=T.useState((()=>new R.Scene)),[a,i]=T.useState(),s=T.useRef();return T.useEffect((()=>{s.current=new m.Flow(n.children[0]),i(s.current.object3D)}),[e]),T.useEffect((()=>{var e;t&&(null==(e=s.current)||e.updateCurve(0,t))}),[t]),T.useImperativeHandle(r,(()=>({moveAlongCurve:e=>{var t;null==(t=s.current)||t.moveAlongCurve(e)}}))),T.createElement(T.Fragment,null,o.createPortal(e,n),a&&T.createElement("primitive",{object:a}))}));class Zt extends n.MeshPhysicalMaterial{constructor(e={}){super(e),this.setValues(e),this._time={value:0},this._distort={value:.4},this._radius={value:1}}onBeforeCompile(e){e.uniforms.time=this._time,e.uniforms.radius=this._radius,e.uniforms.distort=this._distort,e.vertexShader=`\n      uniform float time;\n      uniform float radius;\n      uniform float distort;\n      #define GLSLIFY 1\nvec3 mod289(vec3 x){return x-floor(x*(1.0/289.0))*289.0;}vec4 mod289(vec4 x){return x-floor(x*(1.0/289.0))*289.0;}vec4 permute(vec4 x){return mod289(((x*34.0)+1.0)*x);}vec4 taylorInvSqrt(vec4 r){return 1.79284291400159-0.85373472095314*r;}float snoise(vec3 v){const vec2 C=vec2(1.0/6.0,1.0/3.0);const vec4 D=vec4(0.0,0.5,1.0,2.0);vec3 i=floor(v+dot(v,C.yyy));vec3 x0=v-i+dot(i,C.xxx);vec3 g=step(x0.yzx,x0.xyz);vec3 l=1.0-g;vec3 i1=min(g.xyz,l.zxy);vec3 i2=max(g.xyz,l.zxy);vec3 x1=x0-i1+C.xxx;vec3 x2=x0-i2+C.yyy;vec3 x3=x0-D.yyy;i=mod289(i);vec4 p=permute(permute(permute(i.z+vec4(0.0,i1.z,i2.z,1.0))+i.y+vec4(0.0,i1.y,i2.y,1.0))+i.x+vec4(0.0,i1.x,i2.x,1.0));float n_=0.142857142857;vec3 ns=n_*D.wyz-D.xzx;vec4 j=p-49.0*floor(p*ns.z*ns.z);vec4 x_=floor(j*ns.z);vec4 y_=floor(j-7.0*x_);vec4 x=x_*ns.x+ns.yyyy;vec4 y=y_*ns.x+ns.yyyy;vec4 h=1.0-abs(x)-abs(y);vec4 b0=vec4(x.xy,y.xy);vec4 b1=vec4(x.zw,y.zw);vec4 s0=floor(b0)*2.0+1.0;vec4 s1=floor(b1)*2.0+1.0;vec4 sh=-step(h,vec4(0.0));vec4 a0=b0.xzyw+s0.xzyw*sh.xxyy;vec4 a1=b1.xzyw+s1.xzyw*sh.zzww;vec3 p0=vec3(a0.xy,h.x);vec3 p1=vec3(a0.zw,h.y);vec3 p2=vec3(a1.xy,h.z);vec3 p3=vec3(a1.zw,h.w);vec4 norm=taylorInvSqrt(vec4(dot(p0,p0),dot(p1,p1),dot(p2,p2),dot(p3,p3)));p0*=norm.x;p1*=norm.y;p2*=norm.z;p3*=norm.w;vec4 m=max(0.6-vec4(dot(x0,x0),dot(x1,x1),dot(x2,x2),dot(x3,x3)),0.0);m=m*m;return 42.0*dot(m*m,vec4(dot(p0,x0),dot(p1,x1),dot(p2,x2),dot(p3,x3)));}\n      ${e.vertexShader}\n    `,e.vertexShader=e.vertexShader.replace("#include <begin_vertex>","\n        float updateTime = time / 50.0;\n        float noise = snoise(vec3(position / 2.0 + updateTime * 5.0));\n        vec3 transformed = vec3(position * (noise * pow(distort, 2.0) + radius));\n        ")}get time(){return this._time.value}set time(e){this._time.value=e}get distort(){return this._distort.value}set distort(e){this._distort.value=e}get radius(){return this._radius.value}set radius(e){this._radius.value=e}}const Qt=T.forwardRef((({speed:e=1,...t},r)=>{const[n]=T.useState((()=>new Zt));return o.useFrame((t=>n&&(n.time=t.clock.getElapsedTime()*e))),T.createElement("primitive",C.default({object:n,ref:r,attach:"material"},t))}));class Jt extends n.MeshStandardMaterial{constructor(e={}){super(e),this.setValues(e),this._time={value:0},this._factor={value:1}}onBeforeCompile(e){e.uniforms.time=this._time,e.uniforms.factor=this._factor,e.vertexShader=`\n      uniform float time;\n      uniform float factor;\n      ${e.vertexShader}\n    `,e.vertexShader=e.vertexShader.replace("#include <begin_vertex>","float theta = sin( time + position.y ) / 2.0 * factor;\n        float c = cos( theta );\n        float s = sin( theta );\n        mat3 m = mat3( c, 0, s, 0, 1, 0, -s, 0, c );\n        vec3 transformed = vec3( position ) * m;\n        vNormal = vNormal * m;")}get time(){return this._time.value}set time(e){this._time.value=e}get factor(){return this._factor.value}set factor(e){this._factor.value=e}}const er=T.forwardRef((({speed:e=1,...t},r)=>{const[n]=T.useState((()=>new Jt));return o.useFrame((t=>n&&(n.time=t.clock.getElapsedTime()*e))),T.createElement("primitive",C.default({object:n,ref:r,attach:"material"},t))}));class tr extends n.ShaderMaterial{constructor(e=new n.Vector2){super({uniforms:{inputBuffer:new n.Uniform(null),depthBuffer:new n.Uniform(null),resolution:new n.Uniform(new n.Vector2),texelSize:new n.Uniform(new n.Vector2),halfTexelSize:new n.Uniform(new n.Vector2),kernel:new n.Uniform(0),scale:new n.Uniform(1),cameraNear:new n.Uniform(0),cameraFar:new n.Uniform(1),minDepthThreshold:new n.Uniform(0),maxDepthThreshold:new n.Uniform(1),depthScale:new n.Uniform(0),depthToBlurRatioBias:new n.Uniform(.25)},fragmentShader:"#include <common>\n        #include <dithering_pars_fragment>      \n        uniform sampler2D inputBuffer;\n        uniform sampler2D depthBuffer;\n        uniform float cameraNear;\n        uniform float cameraFar;\n        uniform float minDepthThreshold;\n        uniform float maxDepthThreshold;\n        uniform float depthScale;\n        uniform float depthToBlurRatioBias;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          float depthFactor = 0.0;\n          \n          #ifdef USE_DEPTH\n            vec4 depth = texture2D(depthBuffer, vUv);\n            depthFactor = smoothstep(minDepthThreshold, maxDepthThreshold, 1.0-(depth.r * depth.a));\n            depthFactor *= depthScale;\n            depthFactor = max(0.0, min(1.0, depthFactor + 0.25));\n          #endif\n          \n          vec4 sum = texture2D(inputBuffer, mix(vUv0, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv1, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv2, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv3, vUv, depthFactor));\n          gl_FragColor = sum * 0.25 ;\n\n          #include <dithering_fragment>\n          #include <tonemapping_fragment>\n          #include <encodings_fragment>\n        }",vertexShader:"uniform vec2 texelSize;\n        uniform vec2 halfTexelSize;\n        uniform float kernel;\n        uniform float scale;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          vec2 uv = position.xy * 0.5 + 0.5;\n          vUv = uv;\n\n          vec2 dUv = (texelSize * vec2(kernel) + halfTexelSize) * scale;\n          vUv0 = vec2(uv.x - dUv.x, uv.y + dUv.y);\n          vUv1 = vec2(uv.x + dUv.x, uv.y + dUv.y);\n          vUv2 = vec2(uv.x + dUv.x, uv.y - dUv.y);\n          vUv3 = vec2(uv.x - dUv.x, uv.y - dUv.y);\n\n          gl_Position = vec4(position.xy, 1.0, 1.0);\n        }",blending:n.NoBlending,depthWrite:!1,depthTest:!1}),this.toneMapped=!1,this.setTexelSize(e.x,e.y),this.kernel=new Float32Array([0,1,2,2,3])}setTexelSize(e,t){this.uniforms.texelSize.value.set(e,t),this.uniforms.halfTexelSize.value.set(e,t).multiplyScalar(.5)}setResolution(e){this.uniforms.resolution.value.copy(e)}}class rr{constructor({gl:e,resolution:t,width:r=500,height:o=500,minDepthThreshold:a=0,maxDepthThreshold:i=1,depthScale:s=0,depthToBlurRatioBias:l=.25}){this.renderToScreen=!1,this.renderTargetA=new n.WebGLRenderTarget(t,t,{minFilter:n.LinearFilter,magFilter:n.LinearFilter,stencilBuffer:!1,depthBuffer:!1,type:n.HalfFloatType}),this.renderTargetB=this.renderTargetA.clone(),this.convolutionMaterial=new tr,this.convolutionMaterial.setTexelSize(1/r,1/o),this.convolutionMaterial.setResolution(new n.Vector2(r,o)),this.scene=new n.Scene,this.camera=new n.Camera,this.convolutionMaterial.uniforms.minDepthThreshold.value=a,this.convolutionMaterial.uniforms.maxDepthThreshold.value=i,this.convolutionMaterial.uniforms.depthScale.value=s,this.convolutionMaterial.uniforms.depthToBlurRatioBias.value=l,this.convolutionMaterial.defines.USE_DEPTH=s>0;const c=new Float32Array([-1,-1,0,3,-1,0,-1,3,0]),u=new Float32Array([0,0,2,0,0,2]),m=new n.BufferGeometry;m.setAttribute("position",new n.BufferAttribute(c,3)),m.setAttribute("uv",new n.BufferAttribute(u,2)),this.screen=new n.Mesh(m,this.convolutionMaterial),this.screen.frustumCulled=!1,this.scene.add(this.screen)}render(e,t,r){const n=this.scene,o=this.camera,a=this.renderTargetA,i=this.renderTargetB;let s=this.convolutionMaterial,l=s.uniforms;l.depthBuffer.value=t.depthTexture;const c=s.kernel;let u,m,d,f=t;for(m=0,d=c.length-1;m<d;++m)u=0==(1&m)?a:i,l.kernel.value=c[m],l.inputBuffer.value=f.texture,e.setRenderTarget(u),e.render(n,o),f=u;l.kernel.value=c[m],l.inputBuffer.value=f.texture,e.setRenderTarget(this.renderToScreen?null:r),e.render(n,o)}}class nr extends n.MeshStandardMaterial{constructor(e={}){super(e),this._tDepth={value:null},this._distortionMap={value:null},this._tDiffuse={value:null},this._tDiffuseBlur={value:null},this._textureMatrix={value:null},this._hasBlur={value:!1},this._mirror={value:0},this._mixBlur={value:0},this._blurStrength={value:.5},this._minDepthThreshold={value:.9},this._maxDepthThreshold={value:1},this._depthScale={value:0},this._depthToBlurRatioBias={value:.25},this._distortion={value:1},this._mixContrast={value:1},this.setValues(e)}onBeforeCompile(e){var t;null!=(t=e.defines)&&t.USE_UV||(e.defines.USE_UV=""),e.uniforms.hasBlur=this._hasBlur,e.uniforms.tDiffuse=this._tDiffuse,e.uniforms.tDepth=this._tDepth,e.uniforms.distortionMap=this._distortionMap,e.uniforms.tDiffuseBlur=this._tDiffuseBlur,e.uniforms.textureMatrix=this._textureMatrix,e.uniforms.mirror=this._mirror,e.uniforms.mixBlur=this._mixBlur,e.uniforms.mixStrength=this._blurStrength,e.uniforms.minDepthThreshold=this._minDepthThreshold,e.uniforms.maxDepthThreshold=this._maxDepthThreshold,e.uniforms.depthScale=this._depthScale,e.uniforms.depthToBlurRatioBias=this._depthToBlurRatioBias,e.uniforms.distortion=this._distortion,e.uniforms.mixContrast=this._mixContrast,e.vertexShader=`\n        uniform mat4 textureMatrix;\n        varying vec4 my_vUv;\n      ${e.vertexShader}`,e.vertexShader=e.vertexShader.replace("#include <project_vertex>","#include <project_vertex>\n        my_vUv = textureMatrix * vec4( position, 1.0 );\n        gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );"),e.fragmentShader=`\n        uniform sampler2D tDiffuse;\n        uniform sampler2D tDiffuseBlur;\n        uniform sampler2D tDepth;\n        uniform sampler2D distortionMap;\n        uniform float distortion;\n        uniform float cameraNear;\n\t\t\t  uniform float cameraFar;\n        uniform bool hasBlur;\n        uniform float mixBlur;\n        uniform float mirror;\n        uniform float mixStrength;\n        uniform float minDepthThreshold;\n        uniform float maxDepthThreshold;\n        uniform float mixContrast;\n        uniform float depthScale;\n        uniform float depthToBlurRatioBias;\n        varying vec4 my_vUv;\n        ${e.fragmentShader}`,e.fragmentShader=e.fragmentShader.replace("#include <emissivemap_fragment>","#include <emissivemap_fragment>\n\n      float distortionFactor = 0.0;\n      #ifdef USE_DISTORTION\n        distortionFactor = texture2D(distortionMap, vUv).r * distortion;\n      #endif\n\n      vec4 new_vUv = my_vUv;\n      new_vUv.x += distortionFactor;\n      new_vUv.y += distortionFactor;\n\n      vec4 base = texture2DProj(tDiffuse, new_vUv);\n      vec4 blur = texture2DProj(tDiffuseBlur, new_vUv);\n\n      vec4 merge = base;\n\n      #ifdef USE_NORMALMAP\n        vec2 normal_uv = vec2(0.0);\n        vec4 normalColor = texture2D(normalMap, vUv * normalScale);\n        vec3 my_normal = normalize( vec3( normalColor.r * 2.0 - 1.0, normalColor.b,  normalColor.g * 2.0 - 1.0 ) );\n        vec3 coord = new_vUv.xyz / new_vUv.w;\n        normal_uv = coord.xy + coord.z * my_normal.xz * 0.05;\n        vec4 base_normal = texture2D(tDiffuse, normal_uv);\n        vec4 blur_normal = texture2D(tDiffuseBlur, normal_uv);\n        merge = base_normal;\n        blur = blur_normal;\n      #endif\n\n      float depthFactor = 0.0001;\n      float blurFactor = 0.0;\n\n      #ifdef USE_DEPTH\n        vec4 depth = texture2DProj(tDepth, new_vUv);\n        depthFactor = smoothstep(minDepthThreshold, maxDepthThreshold, 1.0-(depth.r * depth.a));\n        depthFactor *= depthScale;\n        depthFactor = max(0.0001, min(1.0, depthFactor));\n\n        #ifdef USE_BLUR\n          blur = blur * min(1.0, depthFactor + depthToBlurRatioBias);\n          merge = merge * min(1.0, depthFactor + 0.5);\n        #else\n          merge = merge * depthFactor;\n        #endif\n\n      #endif\n\n      float reflectorRoughnessFactor = roughness;\n      #ifdef USE_ROUGHNESSMAP\n        vec4 reflectorTexelRoughness = texture2D( roughnessMap, vUv );\n        reflectorRoughnessFactor *= reflectorTexelRoughness.g;\n      #endif\n\n      #ifdef USE_BLUR\n        blurFactor = min(1.0, mixBlur * reflectorRoughnessFactor);\n        merge = mix(merge, blur, blurFactor);\n      #endif\n\n      vec4 newMerge = vec4(0.0, 0.0, 0.0, 1.0);\n      newMerge.r = (merge.r - 0.5) * mixContrast + 0.5;\n      newMerge.g = (merge.g - 0.5) * mixContrast + 0.5;\n      newMerge.b = (merge.b - 0.5) * mixContrast + 0.5;\n\n      diffuseColor.rgb = diffuseColor.rgb * ((1.0 - min(1.0, mirror)) + newMerge.rgb * mixStrength);\n      ")}get tDiffuse(){return this._tDiffuse.value}set tDiffuse(e){this._tDiffuse.value=e}get tDepth(){return this._tDepth.value}set tDepth(e){this._tDepth.value=e}get distortionMap(){return this._distortionMap.value}set distortionMap(e){this._distortionMap.value=e}get tDiffuseBlur(){return this._tDiffuseBlur.value}set tDiffuseBlur(e){this._tDiffuseBlur.value=e}get textureMatrix(){return this._textureMatrix.value}set textureMatrix(e){this._textureMatrix.value=e}get hasBlur(){return this._hasBlur.value}set hasBlur(e){this._hasBlur.value=e}get mirror(){return this._mirror.value}set mirror(e){this._mirror.value=e}get mixBlur(){return this._mixBlur.value}set mixBlur(e){this._mixBlur.value=e}get mixStrength(){return this._blurStrength.value}set mixStrength(e){this._blurStrength.value=e}get minDepthThreshold(){return this._minDepthThreshold.value}set minDepthThreshold(e){this._minDepthThreshold.value=e}get maxDepthThreshold(){return this._maxDepthThreshold.value}set maxDepthThreshold(e){this._maxDepthThreshold.value=e}get depthScale(){return this._depthScale.value}set depthScale(e){this._depthScale.value=e}get depthToBlurRatioBias(){return this._depthToBlurRatioBias.value}set depthToBlurRatioBias(e){this._depthToBlurRatioBias.value=e}get distortion(){return this._distortion.value}set distortion(e){this._distortion.value=e}get mixContrast(){return this._mixContrast.value}set mixContrast(e){this._mixContrast.value=e}}o.extend({MeshReflectorMaterialImpl:nr});const or=T.forwardRef((({mixBlur:e=0,mixStrength:t=1,resolution:r=256,blur:a=[0,0],minDepthThreshold:i=.9,maxDepthThreshold:s=1,depthScale:l=0,depthToBlurRatioBias:c=.25,mirror:u=0,distortion:m=1,mixContrast:d=1,distortionMap:f,reflectorOffset:p=0,...h},v)=>{const x=o.useThree((({gl:e})=>e)),y=o.useThree((({camera:e})=>e)),g=o.useThree((({scene:e})=>e)),w=(a=Array.isArray(a)?a:[a,a])[0]+a[1]>0,b=T.useRef(null),[E]=T.useState((()=>new n.Plane)),[M]=T.useState((()=>new n.Vector3)),[z]=T.useState((()=>new n.Vector3)),[S]=T.useState((()=>new n.Vector3)),[P]=T.useState((()=>new n.Matrix4)),[R]=T.useState((()=>new n.Vector3(0,0,-1))),[D]=T.useState((()=>new n.Vector4)),[k]=T.useState((()=>new n.Vector3)),[_]=T.useState((()=>new n.Vector3)),[L]=T.useState((()=>new n.Vector4)),[A]=T.useState((()=>new n.Matrix4)),[B]=T.useState((()=>new n.PerspectiveCamera)),O=T.useCallback((()=>{var e;const t=b.current.parent||(null==(e=b.current)?void 0:e.__r3f.parent);if(!t)return;if(z.setFromMatrixPosition(t.matrixWorld),S.setFromMatrixPosition(y.matrixWorld),P.extractRotation(t.matrixWorld),M.set(0,0,1),M.applyMatrix4(P),z.addScaledVector(M,p),k.subVectors(z,S),k.dot(M)>0)return;k.reflect(M).negate(),k.add(z),P.extractRotation(y.matrixWorld),R.set(0,0,-1),R.applyMatrix4(P),R.add(S),_.subVectors(z,R),_.reflect(M).negate(),_.add(z),B.position.copy(k),B.up.set(0,1,0),B.up.applyMatrix4(P),B.up.reflect(M),B.lookAt(_),B.far=y.far,B.updateMatrixWorld(),B.projectionMatrix.copy(y.projectionMatrix),A.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),A.multiply(B.projectionMatrix),A.multiply(B.matrixWorldInverse),A.multiply(t.matrixWorld),E.setFromNormalAndCoplanarPoint(M,z),E.applyMatrix4(B.matrixWorldInverse),D.set(E.normal.x,E.normal.y,E.normal.z,E.constant);const r=B.projectionMatrix;L.x=(Math.sign(D.x)+r.elements[8])/r.elements[0],L.y=(Math.sign(D.y)+r.elements[9])/r.elements[5],L.z=-1,L.w=(1+r.elements[10])/r.elements[14],D.multiplyScalar(2/D.dot(L)),r.elements[2]=D.x,r.elements[6]=D.y,r.elements[10]=D.z+1,r.elements[14]=D.w}),[y,p]),[I,U,V,j]=T.useMemo((()=>{const o={minFilter:n.LinearFilter,magFilter:n.LinearFilter,type:n.HalfFloatType},p=new n.WebGLRenderTarget(r,r,o);p.depthBuffer=!0,p.depthTexture=new n.DepthTexture(r,r),p.depthTexture.format=n.DepthFormat,p.depthTexture.type=n.UnsignedShortType;const h=new n.WebGLRenderTarget(r,r,o);return[p,h,new rr({gl:x,resolution:r,width:a[0],height:a[1],minDepthThreshold:i,maxDepthThreshold:s,depthScale:l,depthToBlurRatioBias:c}),{mirror:u,textureMatrix:A,mixBlur:e,tDiffuse:p.texture,tDepth:p.depthTexture,tDiffuseBlur:h.texture,hasBlur:w,mixStrength:t,minDepthThreshold:i,maxDepthThreshold:s,depthScale:l,depthToBlurRatioBias:c,distortion:m,distortionMap:f,mixContrast:d,"defines-USE_BLUR":w?"":void 0,"defines-USE_DEPTH":l>0?"":void 0,"defines-USE_DISTORTION":f?"":void 0}]}),[x,a,A,r,u,w,e,t,i,s,l,c,m,f,d]);return o.useFrame((()=>{var e;const t=b.current.parent||(null==(e=b.current)?void 0:e.__r3f.parent);if(!t)return;t.visible=!1;const r=x.xr.enabled,n=x.shadowMap.autoUpdate;O(),x.xr.enabled=!1,x.shadowMap.autoUpdate=!1,x.setRenderTarget(I),x.state.buffers.depth.setMask(!0),x.autoClear||x.clear(),x.render(g,B),w&&V.render(x,I,U),x.xr.enabled=r,x.shadowMap.autoUpdate=n,t.visible=!0,x.setRenderTarget(null)})),T.createElement("meshReflectorMaterialImpl",C.default({attach:"material",key:"key"+j["defines-USE_BLUR"]+j["defines-USE_DEPTH"]+j["defines-USE_DISTORTION"],ref:F.default([b,v])},j,h))})),ar=Ee({envMap:null,bounces:3,ior:2.4,correctMips:!0,aberrationStrength:.01,fresnel:0,bvh:new b.MeshBVHUniformStruct,color:new R.Color("white"),resolution:new R.Vector2,viewMatrixInverse:new R.Matrix4,projectionMatrixInverse:new R.Matrix4},"\n  uniform mat4 viewMatrixInverse;\n\n  varying vec3 vWorldPosition;  \n  varying vec3 vNormal;\n  varying mat4 vModelMatrixInverse;\n\n  #ifdef USE_INSTANCING_COLOR\n    varying vec3 vInstanceColor;\n  #endif\n\n  void main() {        \n    vec4 transformedNormal = vec4(normal, 0.0);\n    vec4 transformedPosition = vec4(position, 1.0);\n    #ifdef USE_INSTANCING\n      transformedNormal = instanceMatrix * transformedNormal;\n      transformedPosition = instanceMatrix * transformedPosition;\n    #endif\n\n    #ifdef USE_INSTANCING\n      vModelMatrixInverse = inverse(modelMatrix * instanceMatrix);\n    #else\n      vModelMatrixInverse = inverse(modelMatrix);\n    #endif\n\n    #ifdef USE_INSTANCING_COLOR\n      vInstanceColor = instanceColor.rgb;\n    #endif\n\n    vWorldPosition = (modelMatrix * transformedPosition).xyz;\n    vNormal = normalize((viewMatrixInverse * vec4(normalMatrix * transformedNormal.xyz, 0.0)).xyz);\n    gl_Position = projectionMatrix * viewMatrix * modelMatrix * transformedPosition;\n  }",`\n  #define ENVMAP_TYPE_CUBE_UV\n  precision highp isampler2D;\n  precision highp usampler2D;\n  varying vec3 vWorldPosition;\n  varying vec3 vNormal;\n  varying mat4 vModelMatrixInverse;\n\n  #ifdef USE_INSTANCING_COLOR\n    varying vec3 vInstanceColor;\n  #endif\n    \n  #ifdef ENVMAP_TYPE_CUBEM\n    uniform samplerCube envMap;\n  #else\n    uniform sampler2D envMap;\n  #endif\n    \n  uniform float bounces;\n  ${b.shaderStructs}\n  ${b.shaderIntersectFunction}\n  uniform BVH bvh;\n  uniform float ior;\n  uniform bool correctMips;\n  uniform vec2 resolution;\n  uniform float fresnel;\n  uniform mat4 modelMatrix;\n  uniform mat4 projectionMatrixInverse;\n  uniform mat4 viewMatrixInverse;\n  uniform float aberrationStrength;\n  uniform vec3 color;\n  \n  float fresnelFunc(vec3 viewDirection, vec3 worldNormal) {\n    return pow( 1.0 + dot( viewDirection, worldNormal), 10.0 );\n  }\n    \n  vec3 totalInternalReflection(vec3 ro, vec3 rd, vec3 normal, float ior, mat4 modelMatrixInverse) {\n    vec3 rayOrigin = ro;\n    vec3 rayDirection = rd;\n    rayDirection = refract(rayDirection, normal, 1.0 / ior);\n    rayOrigin = vWorldPosition + rayDirection * 0.001;\n    rayOrigin = (modelMatrixInverse * vec4(rayOrigin, 1.0)).xyz;\n    rayDirection = normalize((modelMatrixInverse * vec4(rayDirection, 0.0)).xyz);\n    for(float i = 0.0; i < bounces; i++) {\n      uvec4 faceIndices = uvec4( 0u );\n      vec3 faceNormal = vec3( 0.0, 0.0, 1.0 );\n      vec3 barycoord = vec3( 0.0 );\n      float side = 1.0;\n      float dist = 0.0;\n      bvhIntersectFirstHit( bvh, rayOrigin, rayDirection, faceIndices, faceNormal, barycoord, side, dist );\n      vec3 hitPos = rayOrigin + rayDirection * max(dist - 0.001, 0.0);      \n      vec3 tempDir = refract(rayDirection, faceNormal, ior);\n      if (length(tempDir) != 0.0) {\n        rayDirection = tempDir;\n        break;\n      }\n      rayDirection = reflect(rayDirection, faceNormal);\n      rayOrigin = hitPos + rayDirection * 0.01;\n    }\n    rayDirection = normalize((modelMatrix * vec4(rayDirection, 0.0)).xyz);\n    return rayDirection;\n  }\n    \n  #include <common>\n  #include <cube_uv_reflection_fragment>\n    \n  #ifdef ENVMAP_TYPE_CUBEM\n    vec4 textureGradient(samplerCube envMap, vec3 rayDirection, vec3 directionCamPerfect) {\n      return textureGrad(envMap, rayDirection, dFdx(correctMips ? directionCamPerfect: rayDirection), dFdy(correctMips ? directionCamPerfect: rayDirection));\n    }\n  #else\n    vec4 textureGradient(sampler2D envMap, vec3 rayDirection, vec3 directionCamPerfect) {\n      vec2 uvv = equirectUv( rayDirection );\n      vec2 smoothUv = equirectUv( directionCamPerfect );\n      return textureGrad(envMap, uvv, dFdx(correctMips ? smoothUv : uvv), dFdy(correctMips ? smoothUv : uvv));\n    }\n  #endif\n  \n  void main() {\n    vec2 uv = gl_FragCoord.xy / resolution;\n    vec3 directionCamPerfect = (projectionMatrixInverse * vec4(uv * 2.0 - 1.0, 0.0, 1.0)).xyz;\n    directionCamPerfect = (viewMatrixInverse * vec4(directionCamPerfect, 0.0)).xyz;\n    directionCamPerfect = normalize(directionCamPerfect);\n    vec3 normal = vNormal;\n    vec3 rayOrigin = cameraPosition;\n    vec3 rayDirection = normalize(vWorldPosition - cameraPosition);\n    vec3 finalColor;\n    #ifdef CHROMATIC_ABERRATIONS\n      vec3 rayDirectionG = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior, 1.0), vModelMatrixInverse);\n      #ifdef FAST_CHROMA \n        vec3 rayDirectionR = normalize(rayDirectionG + 1.0 * vec3(aberrationStrength / 2.0));\n        vec3 rayDirectionB = normalize(rayDirectionG - 1.0 * vec3(aberrationStrength / 2.0));\n      #else\n        vec3 rayDirectionR = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior * (1.0 - aberrationStrength), 1.0), vModelMatrixInverse);\n        vec3 rayDirectionB = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior * (1.0 + aberrationStrength), 1.0), vModelMatrixInverse);\n      #endif\n      float finalColorR = textureGradient(envMap, rayDirectionR, directionCamPerfect).r;\n      float finalColorG = textureGradient(envMap, rayDirectionG, directionCamPerfect).g;\n      float finalColorB = textureGradient(envMap, rayDirectionB, directionCamPerfect).b;\n      finalColor = vec3(finalColorR, finalColorG, finalColorB);\n    #else\n      rayDirection = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior, 1.0), vModelMatrixInverse);\n      finalColor = textureGradient(envMap, rayDirection, directionCamPerfect).rgb;    \n    #endif\n\n    finalColor *= color;\n    #ifdef USE_INSTANCING_COLOR\n      finalColor *= vInstanceColor;\n    #endif\n\n    vec3 viewDirection = normalize(vWorldPosition - cameraPosition);\n    float nFresnel = fresnelFunc(viewDirection, normal) * fresnel;\n    gl_FragColor = vec4(mix(finalColor, vec3(1.0), nFresnel), 1.0);      \n    #include <tonemapping_fragment>\n    #include <encodings_fragment>\n  }`);const ir=Ee({},"void main() { }","void main() { gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0); discard;  }");class sr extends R.MeshPhysicalMaterial{constructor(e=6,t=!1){super(),this.uniforms={chromaticAberration:{value:.05},transmission:{value:0},_transmission:{value:1},transmissionMap:{value:null},roughness:{value:0},thickness:{value:0},thicknessMap:{value:null},attenuationDistance:{value:1/0},attenuationColor:{value:new R.Color("white")},anisotropy:{value:.1},time:{value:0},distortion:{value:0},distortionScale:{value:.5},temporalDistortion:{value:0},buffer:{value:null}},this.onBeforeCompile=r=>{r.uniforms={...r.uniforms,...this.uniforms},t?r.defines.USE_SAMPLER="":r.defines.USE_TRANSMISSION="",r.fragmentShader="\n      uniform float chromaticAberration;         \n      uniform float anisotropy;      \n      uniform float time;\n      uniform float distortion;\n      uniform float distortionScale;\n      uniform float temporalDistortion;\n      uniform sampler2D buffer;\n\n      vec3 random3(vec3 c) {\n        float j = 4096.0*sin(dot(c,vec3(17.0, 59.4, 15.0)));\n        vec3 r;\n        r.z = fract(512.0*j);\n        j *= .125;\n        r.x = fract(512.0*j);\n        j *= .125;\n        r.y = fract(512.0*j);\n        return r-0.5;\n      }\n\n      float seed = 0.0;\n      uint hash( uint x ) {\n        x += ( x << 10u );\n        x ^= ( x >>  6u );\n        x += ( x <<  3u );\n        x ^= ( x >> 11u );\n        x += ( x << 15u );\n        return x;\n      }\n\n      // Compound versions of the hashing algorithm I whipped together.\n      uint hash( uvec2 v ) { return hash( v.x ^ hash(v.y)                         ); }\n      uint hash( uvec3 v ) { return hash( v.x ^ hash(v.y) ^ hash(v.z)             ); }\n      uint hash( uvec4 v ) { return hash( v.x ^ hash(v.y) ^ hash(v.z) ^ hash(v.w) ); }\n\n      // Construct a float with half-open range [0:1] using low 23 bits.\n      // All zeroes yields 0.0, all ones yields the next smallest representable value below 1.0.\n      float floatConstruct( uint m ) {\n        const uint ieeeMantissa = 0x007FFFFFu; // binary32 mantissa bitmask\n        const uint ieeeOne      = 0x3F800000u; // 1.0 in IEEE binary32\n        m &= ieeeMantissa;                     // Keep only mantissa bits (fractional part)\n        m |= ieeeOne;                          // Add fractional part to 1.0\n        float  f = uintBitsToFloat( m );       // Range [1:2]\n        return f - 1.0;                        // Range [0:1]\n      }\n\n      // Pseudo-random value in half-open range [0:1].\n      float random( float x ) { return floatConstruct(hash(floatBitsToUint(x))); }\n      float random( vec2  v ) { return floatConstruct(hash(floatBitsToUint(v))); }\n      float random( vec3  v ) { return floatConstruct(hash(floatBitsToUint(v))); }\n      float random( vec4  v ) { return floatConstruct(hash(floatBitsToUint(v))); }\n\n      float rand() {\n        float result = random(vec3(gl_FragCoord.xy, seed));\n        seed += 1.0;\n        return result;\n      }\n\n      const float F3 =  0.3333333;\n      const float G3 =  0.1666667;\n\n      float snoise(vec3 p) {\n        vec3 s = floor(p + dot(p, vec3(F3)));\n        vec3 x = p - s + dot(s, vec3(G3));\n        vec3 e = step(vec3(0.0), x - x.yzx);\n        vec3 i1 = e*(1.0 - e.zxy);\n        vec3 i2 = 1.0 - e.zxy*(1.0 - e);\n        vec3 x1 = x - i1 + G3;\n        vec3 x2 = x - i2 + 2.0*G3;\n        vec3 x3 = x - 1.0 + 3.0*G3;\n        vec4 w, d;\n        w.x = dot(x, x);\n        w.y = dot(x1, x1);\n        w.z = dot(x2, x2);\n        w.w = dot(x3, x3);\n        w = max(0.6 - w, 0.0);\n        d.x = dot(random3(s), x);\n        d.y = dot(random3(s + i1), x1);\n        d.z = dot(random3(s + i2), x2);\n        d.w = dot(random3(s + 1.0), x3);\n        w *= w;\n        w *= w;\n        d *= w;\n        return dot(d, vec4(52.0));\n      }\n\n      float snoiseFractal(vec3 m) {\n        return 0.5333333* snoise(m)\n              +0.2666667* snoise(2.0*m)\n              +0.1333333* snoise(4.0*m)\n              +0.0666667* snoise(8.0*m);\n      }\n"+r.fragmentShader,r.fragmentShader=r.fragmentShader.replace("#include <transmission_pars_fragment>","\n        #ifdef USE_TRANSMISSION\n          // Transmission code is based on glTF-Sampler-Viewer\n          // https://github.com/KhronosGroup/glTF-Sample-Viewer\n          uniform float _transmission;\n          uniform float thickness;\n          uniform float attenuationDistance;\n          uniform vec3 attenuationColor;\n          #ifdef USE_TRANSMISSIONMAP\n            uniform sampler2D transmissionMap;\n          #endif\n          #ifdef USE_THICKNESSMAP\n            uniform sampler2D thicknessMap;\n          #endif\n          uniform vec2 transmissionSamplerSize;\n          uniform sampler2D transmissionSamplerMap;\n          uniform mat4 modelMatrix;\n          uniform mat4 projectionMatrix;\n          varying vec3 vWorldPosition;\n          vec3 getVolumeTransmissionRay( const in vec3 n, const in vec3 v, const in float thickness, const in float ior, const in mat4 modelMatrix ) {\n            // Direction of refracted light.\n            vec3 refractionVector = refract( - v, normalize( n ), 1.0 / ior );\n            // Compute rotation-independant scaling of the model matrix.\n            vec3 modelScale;\n            modelScale.x = length( vec3( modelMatrix[ 0 ].xyz ) );\n            modelScale.y = length( vec3( modelMatrix[ 1 ].xyz ) );\n            modelScale.z = length( vec3( modelMatrix[ 2 ].xyz ) );\n            // The thickness is specified in local space.\n            return normalize( refractionVector ) * thickness * modelScale;\n          }\n          float applyIorToRoughness( const in float roughness, const in float ior ) {\n            // Scale roughness with IOR so that an IOR of 1.0 results in no microfacet refraction and\n            // an IOR of 1.5 results in the default amount of microfacet refraction.\n            return roughness * clamp( ior * 2.0 - 2.0, 0.0, 1.0 );\n          }\n          vec4 getTransmissionSample( const in vec2 fragCoord, const in float roughness, const in float ior ) {\n            float framebufferLod = log2( transmissionSamplerSize.x ) * applyIorToRoughness( roughness, ior );            \n            #ifdef USE_SAMPLER\n              #ifdef texture2DLodEXT\n                return texture2DLodEXT(transmissionSamplerMap, fragCoord.xy, framebufferLod);\n              #else\n                return texture2D(transmissionSamplerMap, fragCoord.xy, framebufferLod);\n              #endif\n            #else\n              return texture2D(buffer, fragCoord.xy);\n            #endif\n          }\n          vec3 applyVolumeAttenuation( const in vec3 radiance, const in float transmissionDistance, const in vec3 attenuationColor, const in float attenuationDistance ) {\n            if ( isinf( attenuationDistance ) ) {\n              // Attenuation distance is +∞, i.e. the transmitted color is not attenuated at all.\n              return radiance;\n            } else {\n              // Compute light attenuation using Beer's law.\n              vec3 attenuationCoefficient = -log( attenuationColor ) / attenuationDistance;\n              vec3 transmittance = exp( - attenuationCoefficient * transmissionDistance ); // Beer's law\n              return transmittance * radiance;\n            }\n          }\n          vec4 getIBLVolumeRefraction( const in vec3 n, const in vec3 v, const in float roughness, const in vec3 diffuseColor,\n            const in vec3 specularColor, const in float specularF90, const in vec3 position, const in mat4 modelMatrix,\n            const in mat4 viewMatrix, const in mat4 projMatrix, const in float ior, const in float thickness,\n            const in vec3 attenuationColor, const in float attenuationDistance ) {\n            vec3 transmissionRay = getVolumeTransmissionRay( n, v, thickness, ior, modelMatrix );\n            vec3 refractedRayExit = position + transmissionRay;\n            // Project refracted vector on the framebuffer, while mapping to normalized device coordinates.\n            vec4 ndcPos = projMatrix * viewMatrix * vec4( refractedRayExit, 1.0 );\n            vec2 refractionCoords = ndcPos.xy / ndcPos.w;\n            refractionCoords += 1.0;\n            refractionCoords /= 2.0;\n            // Sample framebuffer to get pixel the refracted ray hits.\n            vec4 transmittedLight = getTransmissionSample( refractionCoords, roughness, ior );\n            vec3 attenuatedColor = applyVolumeAttenuation( transmittedLight.rgb, length( transmissionRay ), attenuationColor, attenuationDistance );\n            // Get the specular component.\n            vec3 F = EnvironmentBRDF( n, v, specularColor, specularF90, roughness );\n            return vec4( ( 1.0 - F ) * attenuatedColor * diffuseColor, transmittedLight.a );\n          }\n        #endif\n"),r.fragmentShader=r.fragmentShader.replace("#include <transmission_fragment>",`  \n        // Improve the refraction to use the world pos\n        material.transmission = _transmission;\n        material.transmissionAlpha = 1.0;\n        material.thickness = thickness;\n        material.attenuationDistance = attenuationDistance;\n        material.attenuationColor = attenuationColor;\n        #ifdef USE_TRANSMISSIONMAP\n          material.transmission *= texture2D( transmissionMap, vUv ).r;\n        #endif\n        #ifdef USE_THICKNESSMAP\n          material.thickness *= texture2D( thicknessMap, vUv ).g;\n        #endif\n        \n        vec3 pos = vWorldPosition;\n        vec3 v = normalize( cameraPosition - pos );\n        vec3 n = inverseTransformDirection( normal, viewMatrix );\n        vec3 transmission = vec3(0.0);\n        float transmissionR, transmissionB, transmissionG;\n        float randomCoords = rand();\n        float thickness_smear = thickness * max(pow(roughnessFactor, 0.33), anisotropy);\n        vec3 distortionNormal = vec3(0.0);\n        vec3 temporalOffset = vec3(time, -time, -time) * temporalDistortion;\n        if (distortion > 0.0) {\n          distortionNormal = distortion * vec3(snoiseFractal(vec3((pos * distortionScale + temporalOffset))), snoiseFractal(vec3(pos.zxy * distortionScale - temporalOffset)), snoiseFractal(vec3(pos.yxz * distortionScale + temporalOffset)));\n        }\n        for (float i = 0.0; i < ${e}.0; i ++) {\n          vec3 sampleNorm = normalize(n + roughnessFactor * roughnessFactor * 2.0 * normalize(vec3(rand() - 0.5, rand() - 0.5, rand() - 0.5)) * pow(rand(), 0.33) + distortionNormal);\n          transmissionR = getIBLVolumeRefraction(\n            sampleNorm, v, material.roughness, material.diffuseColor, material.specularColor, material.specularF90,\n            pos, modelMatrix, viewMatrix, projectionMatrix, material.ior, material.thickness  + thickness_smear * (i + randomCoords) / float(${e}),\n            material.attenuationColor, material.attenuationDistance\n          ).r;\n          transmissionG = getIBLVolumeRefraction(\n            sampleNorm, v, material.roughness, material.diffuseColor, material.specularColor, material.specularF90,\n            pos, modelMatrix, viewMatrix, projectionMatrix, material.ior  * (1.0 + chromaticAberration * (i + randomCoords) / float(${e})) , material.thickness + thickness_smear * (i + randomCoords) / float(${e}),\n            material.attenuationColor, material.attenuationDistance\n          ).g;\n          transmissionB = getIBLVolumeRefraction(\n            sampleNorm, v, material.roughness, material.diffuseColor, material.specularColor, material.specularF90,\n            pos, modelMatrix, viewMatrix, projectionMatrix, material.ior * (1.0 + 2.0 * chromaticAberration * (i + randomCoords) / float(${e})), material.thickness + thickness_smear * (i + randomCoords) / float(${e}),\n            material.attenuationColor, material.attenuationDistance\n          ).b;\n          transmission.r += transmissionR;\n          transmission.g += transmissionG;\n          transmission.b += transmissionB;\n        }\n        transmission /= ${e}.0;\n        totalDiffuse = mix( totalDiffuse, transmission.rgb, material.transmission );\n`)},Object.keys(this.uniforms).forEach((e=>Object.defineProperty(this,e,{get:()=>this.uniforms[e].value,set:t=>this.uniforms[e].value=t})))}}const lr=T.forwardRef((({buffer:e,transmissionSampler:t=!1,backside:r=!1,side:n=R.FrontSide,transmission:a=1,thickness:i=0,backsideThickness:s=0,samples:l=10,resolution:c,backsideResolution:u,background:m,...d},f)=>{o.extend({MeshTransmissionMaterial:sr});const p=T.useRef(null),[h]=T.useState((()=>new ir)),v=Xe(u||c),x=Xe(c);let y,g,w;return o.useFrame((e=>{p.current.time=e.clock.getElapsedTime(),p.current.buffer!==x.texture||t||(w=p.current.__r3f.parent,w&&(g=e.gl.toneMapping,y=e.scene.background,e.gl.toneMapping=R.NoToneMapping,m&&(e.scene.background=m),w.material=h,r&&(e.gl.setRenderTarget(v),e.gl.render(e.scene,e.camera),w.material=p.current,w.material.buffer=v.texture,w.material.thickness=s,w.material.side=R.BackSide),e.gl.setRenderTarget(x),e.gl.render(e.scene,e.camera),w.material.thickness=i,w.material.side=n,w.material.buffer=x.texture,e.scene.background=y,e.gl.setRenderTarget(null),w.material=p.current,e.gl.toneMapping=g))})),T.useImperativeHandle(f,(()=>p.current),[]),T.createElement("meshTransmissionMaterial",C.default({args:[l,t],ref:p},d,{buffer:e||x.texture,_transmission:a,transmission:t?a:0,thickness:i,side:n}))})),cr=T.forwardRef(((e,t)=>(o.extend({DiscardMaterialImpl:ir}),T.createElement("discardMaterialImpl",C.default({ref:t},e)))));class ur extends R.PointsMaterial{constructor(e){super(e),this.onBeforeCompile=(e,t)=>{const{isWebGL2:r}=t.capabilities;e.fragmentShader=e.fragmentShader.replace("#include <output_fragment>",`\n        ${r?"#include <output_fragment>":"#extension GL_OES_standard_derivatives : enable\n#include <output_fragment>"}\n      vec2 cxy = 2.0 * gl_PointCoord - 1.0;\n      float r = dot(cxy, cxy);\n      float delta = fwidth(r);     \n      float mask = 1.0 - smoothstep(1.0 - delta, 1.0 + delta, r);\n      gl_FragColor = vec4(gl_FragColor.rgb, mask * gl_FragColor.a );\n      #include <tonemapping_fragment>\n      #include <encodings_fragment>\n      `)}}}const mr=T.forwardRef(((e,t)=>{const[r]=T.useState((()=>new ur(null)));return T.createElement("primitive",C.default({},e,{object:r,ref:t,attach:"material"}))}));function dr(e,t,r){t.traverse((t=>{t.material&&(e.properties.remove(t.material),null==t.material.dispose||t.material.dispose())})),e.info.programs.length=0,e.compile(t,r)}function fr(e,t){const r=e+"Geometry";return T.forwardRef((({args:e,children:n,...o},a)=>{const i=T.useRef(null);return T.useImperativeHandle(a,(()=>i.current)),T.useLayoutEffect((()=>{null==t||t(i.current)})),T.createElement("mesh",C.default({ref:i},o),T.createElement(r,{attach:"geometry",args:e}),n)}))}const pr=fr("box"),hr=fr("circle"),vr=fr("cone"),xr=fr("cylinder"),yr=fr("sphere"),gr=fr("plane"),wr=fr("tube"),br=fr("torus"),Er=fr("torusKnot"),Mr=fr("tetrahedron"),zr=fr("ring"),Sr=fr("polyhedron"),Cr=fr("icosahedron"),Tr=fr("octahedron"),Pr=fr("dodecahedron"),Rr=fr("extrude"),Dr=fr("lathe"),kr=fr("capsule"),Fr=fr("shape",(({geometry:e})=>{const t=e.attributes.position,r=(new R.Box3).setFromBufferAttribute(t),n=new R.Vector3;r.getSize(n);const o=[];let a=0,i=0,s=0,l=0;for(let e=0;e<t.count;e++)a=t.getX(e),i=t.getY(e),s=(a-r.min.x)/n.x,l=(i-r.min.y)/n.y,o.push(s,l);e.setAttribute("uv",new R.Float32BufferAttribute(o,2))})),_r=new R.Vector3(0,0,-1),Lr=T.forwardRef((({face:e=Ar.SAMPLE_FACE,width:t,height:r,depth:n=1,verticalTri:a=[159,386,200],origin:i,debug:s=!1,children:l,...c},u)=>{var m,d,f;const p=T.useRef(null),h=T.useRef(null),[v]=T.useState((()=>new R.Vector3)),[x]=T.useState((()=>new R.Quaternion)),{invalidate:y}=o.useThree();T.useEffect((()=>{var e;null==(e=h.current)||e.geometry.setIndex(Ar.TRIANGULATION)}),[]);const[g]=T.useState((()=>new R.Vector3)),[w]=T.useState((()=>new R.Vector3)),[b]=T.useState((()=>new R.Vector3)),[E]=T.useState((()=>new R.Vector3)),[M]=T.useState((()=>new R.Vector3)),[z]=T.useState((()=>new R.Vector3));T.useEffect((()=>{var o,l,c;const u=null==(o=h.current)?void 0:o.geometry;if(!u)return;u.setFromPoints(e.keypoints),g.copy(e.keypoints[a[0]]),w.copy(e.keypoints[a[1]]),b.copy(e.keypoints[a[2]]),E.copy(w).sub(g),M.copy(b).sub(g),v.crossVectors(M,E).normalize(),x.setFromUnitVectors(_r,v);const m=x.clone().invert();if(u.computeBoundingBox(),s&&y(),u.center(),u.applyQuaternion(m),null==(l=p.current)||l.setRotationFromQuaternion(x),i){const e=u.getAttribute("position");u.translate(-e.getX(i),-e.getY(i),-e.getZ(i))}null==(c=u.boundingBox)||c.getSize(z);let d=1;t&&(d=t/z.x),r&&(d=r/z.y),n&&(d=n/z.z),1!==d&&u.scale(d,d,d),u.computeVertexNormals(),u.attributes.position.needsUpdate=!0}),[e,t,r,n,a,i,s,y,v,x,g,w,b,E,M,z]);const S=T.useMemo((()=>({meshRef:h,outerRef:p})),[]);return T.useImperativeHandle(u,(()=>S),[S]),T.createElement("group",c,T.createElement("group",{ref:p},T.createElement("mesh",{ref:h},l,s?T.createElement(T.Fragment,null,(null==(m=h.current)||null==(d=m.geometry)?void 0:d.boundingBox)&&T.createElement("box3Helper",{args:[null==(f=h.current)?void 0:f.geometry.boundingBox]}),T.createElement(ce,{points:[[0,0,0],_r],color:65535})):null)))})),Ar={SAMPLE_FACE:{keypoints:[{x:356.2804412841797,y:295.1960563659668,z:-23.786449432373047,name:"lips"},{x:354.8859405517578,y:264.69520568847656,z:-36.718435287475586},{x:355.2180862426758,y:275.3360366821289,z:-21.183712482452393},{x:347.349853515625,y:242.4400234222412,z:-25.093655586242676},{x:354.40135955810547,y:256.67933464050293,z:-38.23572635650635},{x:353.7689971923828,y:247.54886627197266,z:-34.5475435256958},{x:352.1288299560547,y:227.34312057495117,z:-13.095386028289795},{x:303.5013198852539,y:234.67002868652344,z:12.500141859054565,name:"rightEye"},{x:351.09378814697266,y:211.87547206878662,z:-6.413471698760986},{x:350.7115936279297,y:202.1251630783081,z:-6.413471698760986},{x:348.33667755126953,y:168.7741756439209,z:6.483500003814697,name:"faceOval"},{x:356.4806365966797,y:299.2995357513428,z:-23.144519329071045},{x:356.5511703491211,y:302.66146659851074,z:-21.020312309265137},{x:356.6239547729492,y:304.1536331176758,z:-18.137459754943848,name:"lips"},{x:356.5807342529297,y:305.1840591430664,z:-18.767719268798828,name:"lips"},{x:356.8241500854492,y:308.25711250305176,z:-20.16829490661621},{x:357.113037109375,y:312.26277351379395,z:-22.10575819015503},{x:357.34962463378906,y:317.1123218536377,z:-21.837315559387207,name:"lips"},{x:357.6658630371094,y:325.51036834716797,z:-16.27002477645874},{x:355.0201416015625,y:269.36279296875,z:-33.73054027557373},{x:348.5237503051758,y:270.33411026000977,z:-24.93025302886963},{x:279.97331619262695,y:213.24176788330078,z:47.759642601013184,name:"faceOval"},{x:322.66529083251953,y:238.5027265548706,z:5.535193085670471},{x:316.0983657836914,y:239.94489669799805,z:5.777376294136047},{x:309.9431610107422,y:240.24518966674805,z:7.510589361190796},{x:301.31994247436523,y:237.86138534545898,z:13.118728399276733},{x:328.14266204833984,y:235.80496788024902,z:6.646900177001953},{x:313.7326431274414,y:222.11161136627197,z:3.9887237548828125},{x:320.45196533203125,y:221.87729358673096,z:4.601476192474365},{x:307.35679626464844,y:223.63793849945068,z:5.932023525238037},{x:303.0031204223633,y:226.3743782043457,z:8.479321002960205},{x:296.80023193359375,y:242.94299125671387,z:15.931552648544312},{x:332.2352981567383,y:340.77341079711914,z:-10.165848731994629},{x:301.38587951660156,y:233.46447944641113,z:14.764405488967896,name:"rightEye"},{x:279.0147018432617,y:244.37155723571777,z:45.77549457550049},{x:289.60548400878906,y:239.1807460784912,z:23.191204071044922},{x:320.32257080078125,y:267.1292781829834,z:-4.954537749290466},{x:347.64583587646484,y:294.4955062866211,z:-23.062820434570312,name:"lips"},{x:349.28138732910156,y:303.1095886230469,z:-20.238323211669922},{x:338.9453125,y:298.19186210632324,z:-19.456336498260498,name:"lips"},{x:333.36788177490234,y:302.6706790924072,z:-14.776077270507812,name:"lips"},{x:342.89188385009766,y:304.3561363220215,z:-17.752301692962646},{x:337.7375030517578,y:306.0098361968994,z:-13.410515785217285},{x:325.6159210205078,y:316.22995376586914,z:-6.681914925575256},{x:349.0104675292969,y:264.9818515777588,z:-36.274919509887695},{x:347.7138900756836,y:257.5664806365967,z:-37.67549514770508},{x:291.79357528686523,y:218.88171672821045,z:11.578094959259033,name:"rightEyebrow"},{x:332.2689437866211,y:247.56946563720703,z:-3.3730539679527283},{x:332.0074462890625,y:267.1201229095459,z:-19.969879388809204},{x:331.27952575683594,y:263.6967658996582,z:-17.47218608856201},{x:301.04373931884766,y:269.56552505493164,z:3.61815482378006},{x:347.4863815307617,y:249.0706443786621,z:-32.633421421051025},{x:307.26118087768555,y:208.2646894454956,z:1.1591226607561111,name:"rightEyebrow"},{x:297.91919708251953,y:212.22604751586914,z:5.914516448974609,name:"rightEyebrow"},{x:285.1651382446289,y:197.98450469970703,z:36.391637325286865,name:"faceOval"},{x:337.04097747802734,y:211.25229835510254,z:-4.548954665660858},{x:326.5912628173828,y:223.16698551177979,z:6.670243740081787},{x:320.05664825439453,y:309.5834255218506,z:-4.055835008621216},{x:289.6866226196289,y:314.617395401001,z:53.875489234924316,name:"faceOval"},{x:337.4256896972656,y:270.8755302429199,z:-17.67060160636902},{x:343.69922637939453,y:273.0000400543213,z:-18.756048679351807},{x:327.4242401123047,y:309.22399520874023,z:-4.703601002693176,name:"lips"},{x:330.37220001220703,y:308.3323001861572,z:-6.442649960517883},{x:293.87027740478516,y:207.7961826324463,z:9.821539521217346,name:"rightEyebrow"},{x:332.11437225341797,y:271.22812271118164,z:-16.64351224899292},{x:320.1197814941406,y:207.40366458892822,z:-2.48164564371109,name:"rightEyebrow"},{x:318.59575271606445,y:201.07443809509277,z:-3.110446035861969,name:"rightEyebrow"},{x:310.72303771972656,y:175.75075149536133,z:13.328815698623657,name:"faceOval"},{x:289.67578887939453,y:202.29835510253906,z:21.370456218719482},{x:315.30879974365234,y:187.35260009765625,z:5.0304025411605835},{x:287.8936767578125,y:216.54793739318848,z:17.81065821647644,name:"rightEyebrow"},{x:283.9391899108887,y:215.01142501831055,z:32.04984903335571},{x:348.35330963134766,y:299.4155788421631,z:-22.47924566268921},{x:341.1790466308594,y:301.8221855163574,z:-18.977805376052856},{x:335.69713592529297,y:304.4266891479492,z:-14.682706594467163},{x:339.4615173339844,y:272.3654365539551,z:-16.38674020767212},{x:328.99600982666016,y:308.86685371398926,z:-5.616893768310547},{x:332.00313568115234,y:309.1875743865967,z:-10.335084199905396},{x:331.0068130493164,y:307.9274368286133,z:-6.681914925575256,name:"lips"},{x:341.13792419433594,y:266.4876937866211,z:-26.56425952911377},{x:339.02950286865234,y:305.6663703918457,z:-12.33674168586731,name:"lips"},{x:344.22935485839844,y:304.9452781677246,z:-15.161235332489014,name:"lips"},{x:350.1844024658203,y:304.374303817749,z:-17.5305438041687,name:"lips"},{x:348.52630615234375,y:325.9562301635742,z:-16.164982318878174},{x:348.6581802368164,y:317.1624183654785,z:-21.510512828826904,name:"lips"},{x:348.9766311645508,y:312.1923065185547,z:-21.708929538726807},{x:349.2427444458008,y:308.0660820007324,z:-19.643079042434692},{x:349.67491149902344,y:305.42747497558594,z:-18.16080331802368,name:"lips"},{x:337.95589447021484,y:306.6535949707031,z:-12.803598642349243,name:"lips"},{x:337.06878662109375,y:307.63169288635254,z:-14.274203777313232},{x:335.77449798583984,y:309.8449516296387,z:-15.698124170303345},{x:334.6099090576172,y:312.7997016906738,z:-14.764405488967896,name:"lips"},{x:327.2330856323242,y:293.80866050720215,z:-11.864047050476074},{x:280.97679138183594,y:279.79928970336914,z:68.90834331512451,name:"faceOval"},{x:355.13843536376953,y:271.7875671386719,z:-25.350427627563477},{x:334.7235870361328,y:307.4656391143799,z:-9.302158951759338,name:"lips"},{x:333.5293960571289,y:307.89782524108887,z:-10.200862884521484},{x:346.29688262939453,y:276.4256286621094,z:-19.748122692108154},{x:335.16246795654297,y:276.22097969055176,z:-12.313398122787476},{x:345.09132385253906,y:274.7082996368408,z:-19.304605722427368},{x:325.4267883300781,y:252.95130729675293,z:-1.6661019623279572},{x:315.347843170166,y:259.05200958251953,z:-.25604281574487686},{x:330.44933319091797,y:267.7570152282715,z:-14.017432928085327},{x:294.96768951416016,y:185.26001930236816,z:23.903164863586426,name:"faceOval"},{x:299.63531494140625,y:192.7913761138916,z:12.640198469161987},{x:304.5452117919922,y:202.4142837524414,z:3.244667649269104,name:"rightEyebrow"},{x:331.6915512084961,y:320.0467872619629,z:-10.632705688476562},{x:334.5911407470703,y:201.27566814422607,z:-6.133356094360352,name:"rightEyebrow"},{x:331.4815902709961,y:185.44180870056152,z:.6627205014228821},{x:328.05816650390625,y:170.8385467529297,z:7.358860373497009,name:"faceOval"},{x:304.49764251708984,y:239.76297855377197,z:10.387605428695679},{x:290.6382179260254,y:248.85257720947266,z:19.03616428375244},{x:331.5682601928711,y:233.20727348327637,z:7.837390303611755},{x:295.5115509033203,y:228.9834451675415,z:14.41426157951355},{x:336.94332122802734,y:241.8259334564209,z:-5.27842104434967},{x:336.2792205810547,y:262.7049922943115,z:-26.12074375152588},{x:284.4102478027344,y:255.3262710571289,z:25.467140674591064},{x:295.1420593261719,y:253.02655220031738,z:12.430112361907959},{x:303.5196113586426,y:254.20703887939453,z:6.139191389083862},{x:315.73450088500977,y:251.64799690246582,z:3.3788898587226868},{x:324.69661712646484,y:247.56494522094727,z:2.3328344523906708},{x:331.57970428466797,y:243.02241325378418,z:1.1423448473215103},{x:345.6210708618164,y:229.9976634979248,z:-10.825285911560059},{x:286.26644134521484,y:270.37991523742676,z:21.708929538726807},{x:290.2525520324707,y:228.4921360015869,z:17.71728754043579},{x:351.65367126464844,y:269.3400764465332,z:-33.450424671173096},{x:333.1378936767578,y:253.88388633728027,z:-7.230473756790161},{x:277.8318977355957,y:246.95331573486328,z:68.20805549621582,name:"faceOval"},{x:336.6680908203125,y:238.10003757476807,z:.7688578963279724},{x:329.95800018310547,y:269.18323516845703,z:-7.207130789756775},{x:299.17491912841797,y:234.13324356079102,z:15.95489501953125},{x:335.61729431152344,y:258.71752738952637,z:-23.016133308410645},{x:284.1079330444336,y:297.0343494415283,z:63.25934886932373,name:"faceOval"},{x:331.44542694091797,y:230.6892442703247,z:9.92658257484436,name:"rightEye"},{x:341.41536712646484,y:253.01264762878418,z:-29.038610458374023},{x:303.5472869873047,y:327.5896739959717,z:16.725212335586548},{x:304.7756576538086,y:337.4389457702637,z:27.38126277923584,name:"faceOval"},{x:280.80501556396484,y:275.32050132751465,z:45.0752067565918},{x:295.43582916259766,y:318.4501647949219,z:26.2608003616333},{x:281.4303207397461,y:228.7355661392212,z:40.94350814819336},{x:331.2549591064453,y:349.4216537475586,z:-7.376367449760437},{x:352.4247741699219,y:271.7330074310303,z:-24.953596591949463},{x:327.5672912597656,y:260.41900634765625,z:-5.456410646438599},{x:284.5432472229004,y:241.7647933959961,z:29.668869972229004},{x:310,y:235.66174507141113,z:8.502663969993591,name:"rightEye"},{x:315.7071113586426,y:235.7572603225708,z:6.938687562942505,name:"rightEye"},{x:330.41088104248047,y:311.04143142700195,z:-9.325502514839172,name:"lips"},{x:288.5377502441406,y:285.31983375549316,z:21.837315559387207},{x:344.55039978027344,y:359.4300842285156,z:-6.705257892608643,name:"faceOval"},{x:323.41880798339844,y:351.67362213134766,z:7.802375555038452,name:"faceOval"},{x:314.64088439941406,y:346.11894607543945,z:16.36339783668518,name:"faceOval"},{x:349.4945526123047,y:184.8434829711914,z:-.21847527474164963},{x:359.24694061279297,y:359.8348903656006,z:-8.403456211090088,name:"faceOval"},{x:321.26182556152344,y:234.64492321014404,z:6.90950870513916,name:"rightEye"},{x:326.318359375,y:232.90250301361084,z:8.029969334602356,name:"rightEye"},{x:329.6211624145508,y:231.6195774078369,z:9.722331762313843,name:"rightEye"},{x:285.9398078918457,y:228.2351303100586,z:24.650139808654785},{x:325.79288482666016,y:227.88007736206055,z:7.469738721847534,name:"rightEye"},{x:320.1699447631836,y:227.5934886932373,z:6.168370842933655,name:"rightEye"},{x:314.85408782958984,y:227.85282611846924,z:6.2675780057907104,name:"rightEye"},{x:309.3084907531738,y:229.1516876220703,z:7.7031683921813965,name:"rightEye"},{x:305.5621337890625,y:230.92366218566895,z:9.722331762313843,name:"rightEye"},{x:277.8681945800781,y:228.5354232788086,z:59.71122741699219,name:"faceOval"},{x:306.1444664001465,y:235.1954698562622,z:10.603528022766113,name:"rightEye"},{x:355.4478454589844,y:281.96210861206055,z:-20.565123558044434},{x:333.02661895751953,y:288.0105400085449,z:-14.72939133644104},{x:337.15728759765625,y:269.2059516906738,z:-19.8414945602417},{x:345.9898376464844,y:283.5453128814697,z:-20.4834246635437},{x:351.48963928222656,y:219.98916149139404,z:-7.0378947257995605},{x:312.39574432373047,y:336.50628089904785,z:8.671900033950806},{x:321.32152557373047,y:343.1755256652832,z:.9067271649837494},{x:343.78379821777344,y:353.2975959777832,z:-14.355905055999756},{x:296.8791389465332,y:327.91497230529785,z:41.01353645324707,name:"faceOval"},{x:329.6939468383789,y:229.27897453308105,z:8.934508562088013,name:"rightEye"},{x:341.6905212402344,y:241.4073657989502,z:-14.589333534240723},{x:359.03079986572266,y:353.48859786987305,z:-15.803166627883911},{x:333.1861877441406,y:356.43213272094727,z:-1.0234417766332626,name:"faceOval"},{x:283.97483825683594,y:291.4318656921387,z:41.94725513458252},{x:343.33770751953125,y:305.830135345459,z:-15.756480693817139,name:"lips"},{x:342.40283966064453,y:307.7453899383545,z:-17.4021577835083},{x:341.53621673583984,y:311.0595703125,z:-19.047834873199463},{x:340.9107208251953,y:315.4837703704834,z:-18.5576331615448,name:"lips"},{x:339.1478729248047,y:323.42233657836914,z:-14.367576837539673},{x:333.3201599121094,y:307.4406337738037,z:-9.617288708686829},{x:331.2411117553711,y:306.9811820983887,z:-9.669809937477112},{x:329.23255920410156,y:306.0508346557617,z:-9.582273960113525,name:"lips"},{x:322.4586486816406,y:301.33323669433594,z:-7.720675468444824},{x:297.1712112426758,y:286.9552803039551,z:8.240055441856384},{x:341.3060760498047,y:235.4432201385498,z:-7.504753470420837},{x:336.9318389892578,y:224.3451976776123,z:5.829898118972778},{x:332.65323638916016,y:226.70403957366943,z:8.105834126472473},{x:334.67357635498047,y:306.4397621154785,z:-8.981193900108337,name:"lips"},{x:297.4601936340332,y:306.29210472106934,z:15.476365089416504},{x:342.9119110107422,y:222.37077713012695,z:-2.754466235637665},{x:335.4629898071289,y:332.20250129699707,z:-11.823196411132812},{x:353.2412338256836,y:240.56339263916016,z:-27.147831916809082},{x:346.3080596923828,y:236.41446590423584,z:-18.452589511871338},{x:352.6475143432617,y:234.1420555114746,z:-19.748122692108154},{x:337.3209762573242,y:253.39937210083008,z:-16.024924516677856},{x:358.6122131347656,y:344.90861892700195,z:-18.592647314071655},{x:358.1117248535156,y:334.64990615844727,z:-17.49552845954895},{x:346.4450454711914,y:335.0321102142334,z:-16.32838249206543},{x:319.17640686035156,y:320.2833938598633,z:-3.276764452457428},{x:325.2540588378906,y:276.2369728088379,z:-6.460157036781311},{x:326.7214584350586,y:327.3939514160156,z:-7.417217493057251},{x:310.7190132141113,y:277.2265148162842,z:-3.5452082753181458},{x:319.78355407714844,y:284.8238182067871,z:-6.4543211460113525},{x:305.773983001709,y:290.83580017089844,z:.06907138042151928},{x:344.4001770019531,y:344.85408782958984,z:-16.946970224380493},{x:333.1879425048828,y:258.74256134033203,z:-11.90489649772644},{x:313.80598068237305,y:327.08919525146484,z:2.2277912497520447},{x:322.9637908935547,y:334.6819496154785,z:-3.3643004298210144},{x:313.4055519104004,y:311.2166690826416,z:-1.1175429821014404},{x:291.0865783691406,y:298.2831001281738,z:22.467575073242188},{x:305.6580924987793,y:313.3707904815674,z:5.561453700065613},{x:288.23760986328125,y:305.9941864013672,z:36.765122413635254},{x:315.10692596435547,y:296.26991271972656,z:-4.604393839836121},{x:337.50518798828125,y:247.5944423675537,z:-10.597691535949707},{x:338.8450622558594,y:265.47778129577637,z:-27.778091430664062},{x:334.25254821777344,y:269.0671920776367,z:-20.938611030578613},{x:341.64512634277344,y:259.6387195587158,z:-32.189905643463135},{x:331.44081115722656,y:219.0976095199585,z:4.207563698291779},{x:320.56339263916016,y:216.49658203125,z:2.930997312068939},{x:311.21912002563477,y:216.57853603363037,z:2.9674705862998962},{x:303.46256256103516,y:218.54614734649658,z:5.357203483581543},{x:297.99999237060547,y:222.505202293396,z:9.325502514839172},{x:294.93839263916016,y:236.39654159545898,z:18.534289598464966},{x:278.87489318847656,y:259.7095584869385,z:45.68212032318115},{x:300.3782653808594,y:245.38593292236328,z:12.278382778167725},{x:307.06348419189453,y:246.36857986450195,z:8.164191246032715},{x:315.5229187011719,y:245.3949737548828,z:5.503097176551819},{x:323.71395111083984,y:242.75178909301758,z:4.6335723996162415},{x:330.2785873413086,y:239.34658527374268,z:4.937030673027039},{x:334.6982192993164,y:236.0460376739502,z:4.823233783245087},{x:279.3412208557129,y:263.5196113586426,z:70.91583728790283,name:"faceOval"},{x:334.65972900390625,y:271.6648578643799,z:-17.775644063949585},{x:342.05677032470703,y:246.99846267700195,z:-20.84523916244507},{x:344.0357971191406,y:264.5701503753662,z:-32.936880588531494},{x:348.25531005859375,y:268.6645030975342,z:-30.695960521697998},{x:344.12227630615234,y:266.34212493896484,z:-29.808926582336426},{x:337.12318420410156,y:274.2556858062744,z:-15.768152475357056},{x:349.49047088623047,y:269.071683883667,z:-32.51670837402344},{x:350.1683044433594,y:271.4691352844238,z:-24.93025302886963},{x:333.9634704589844,y:230.56639194488525,z:8.89949381351471},{x:338.2147979736328,y:231.4807891845703,z:4.6715047955513},{x:340.4712677001953,y:231.74463272094727,z:-.34996166825294495},{x:303.28975677490234,y:232.24980354309082,z:11.916568279266357,name:"rightEye"},{x:299.4649124145508,y:229.53842639923096,z:12.325069904327393},{x:359.09618377685547,y:241.77349090576172,z:-24.650139808654785},{x:399.46216583251953,y:229.89503860473633,z:15.919880867004395,name:"leftEye"},{x:361.38919830322266,y:269.6129894256592,z:-24.510080814361572},{x:416.9973373413086,y:206.0895538330078,z:53.26857566833496,name:"faceOval"},{x:381.32179260253906,y:235.5476474761963,z:7.6214683055877686},{x:387.8068542480469,y:236.25958442687988,z:8.345099091529846},{x:393.95751953125,y:235.8660364151001,z:10.475142002105713},{x:401.84600830078125,y:232.77019500732422,z:16.760226488113403},{x:375.70568084716797,y:233.48456382751465,z:8.234220147132874},{x:388.17752838134766,y:218.94717693328857,z:6.810300946235657},{x:381.64928436279297,y:219.2656660079956,z:6.711093783378601},{x:394.4760513305664,y:219.66821193695068,z:9.173773527145386},{x:398.8843536376953,y:221.8837022781372,z:12.03328251838684},{x:406.5454864501953,y:237.12156772613525,z:19.7131085395813},{x:383.87447357177734,y:337.6932907104492,z:-8.631049990653992},{x:401.2682342529297,y:228.5916566848755,z:18.359217643737793,name:"leftEye"},{x:422.0449447631836,y:236.73934936523438,z:51.16771221160889},{x:412.69153594970703,y:232.80198097229004,z:27.52131938934326},{x:387.3497772216797,y:263.298397064209,z:-2.8609684109687805},{x:364.5124053955078,y:293.39221000671387,z:-22.397546768188477,name:"lips"},{x:363.62987518310547,y:302.1291446685791,z:-19.643079042434692},{x:373.2334518432617,y:295.8647060394287,z:-18.125789165496826,name:"lips"},{x:378.83365631103516,y:299.5177745819092,z:-13.153743743896484,name:"lips"},{x:369.91477966308594,y:302.5704002380371,z:-16.65518283843994},{x:374.9167251586914,y:303.5416603088379,z:-11.963253021240234},{x:387.58888244628906,y:312.2716999053955,z:-4.680258631706238},{x:360.6635284423828,y:264.31986808776855,z:-35.94811677932739},{x:361.04564666748047,y:256.8225860595703,z:-37.278664112091064},{x:408.3855438232422,y:213.52088928222656,z:15.756480693817139,name:"leftEyebrow"},{x:373.2946014404297,y:245.38101196289062,z:-1.9316278398036957},{x:376.83860778808594,y:264.3721103668213,z:-18.510947227478027},{x:376.9546127319336,y:261.0010528564453,z:-15.989909172058105},{x:406.1498260498047,y:263.5030174255371,z:7.072908878326416},{x:360.07205963134766,y:248.3631706237793,z:-32.16656446456909},{x:393.11119079589844,y:205.10473251342773,z:3.7786373496055603,name:"leftEyebrow"},{x:402.12791442871094,y:207.89000988006592,z:9.383859634399414,name:"leftEyebrow"},{x:410.8693313598633,y:191.6182279586792,z:41.27030849456787,name:"faceOval"},{x:364.9509811401367,y:210.40483474731445,z:-3.758212625980377},{x:375.94444274902344,y:221.1331844329834,z:8.368442058563232},{x:392.1904754638672,y:305.0360298156738,z:-1.752179116010666},{x:419.50225830078125,y:307.25592613220215,z:58.96425247192383,name:"faceOval"},{x:372.0027160644531,y:268.7212657928467,z:-16.631840467453003},{x:366.1614227294922,y:271.6237449645996,z:-18.219159841537476},{x:385.00938415527344,y:305.3863334655762,z:-2.567722797393799},{x:381.99771881103516,y:304.9723720550537,z:-4.575215280056},{x:405.078125,y:203.21216583251953,z:13.713973760604858,name:"leftEyebrow"},{x:377.13207244873047,y:268.4710121154785,z:-15.266278982162476},{x:380.9713363647461,y:205.36980628967285,z:-.7250899076461792,name:"leftEyebrow"},{x:381.7788314819336,y:198.9268398284912,z:-1.184653863310814,name:"leftEyebrow"},{x:385.5204772949219,y:172.1484375,z:16.04826807975769,name:"faceOval"},{x:407.94189453125,y:196.76236152648926,z:25.723915100097656},{x:383.03890228271484,y:184.5157527923584,z:7.393874526023865},{x:411.61781311035156,y:210.79241752624512,z:22.315845489501953,name:"leftEyebrow"},{x:414.30870056152344,y:208.4643030166626,z:37.021894454956055},{x:364.28722381591797,y:298.35777282714844,z:-21.86065673828125},{x:371.3682556152344,y:299.78848457336426,z:-17.834001779556274},{x:376.88201904296875,y:301.6696071624756,z:-13.153743743896484},{x:370.2193832397461,y:270.49095153808594,z:-15.569736957550049},{x:383.5081100463867,y:305.2726364135742,z:-3.673594295978546},{x:380.73760986328125,y:305.96869468688965,z:-8.660228252410889},{x:381.2334442138672,y:304.63574409484863,z:-4.820316135883331,name:"lips"},{x:368.1698989868164,y:264.8884963989258,z:-25.653886795043945},{x:373.5087203979492,y:303.4233856201172,z:-10.95950722694397,name:"lips"},{x:368.4544372558594,y:303.29601287841797,z:-14.169161319732666,name:"lips"},{x:362.76554107666016,y:303.5735607147217,z:-16.911956071853638,name:"lips"},{x:366.60980224609375,y:324.8870658874512,z:-15.616422891616821},{x:365.7067108154297,y:315.95678329467773,z:-20.903596878051758,name:"lips"},{x:365.0083923339844,y:311.2232208251953,z:-21.066999435424805},{x:364.1508102416992,y:307.0583438873291,z:-18.907777070999146},{x:363.37512969970703,y:304.5721435546875,z:-17.42550015449524,name:"lips"},{x:374.580078125,y:304.3059539794922,z:-11.40302300453186,name:"lips"},{x:375.55362701416016,y:305.0998020172119,z:-12.861957550048828},{x:377.2437286376953,y:307.1674346923828,z:-14.215847253799438},{x:378.68587493896484,y:309.9015712738037,z:-13.223772048950195,name:"lips"},{x:383.8992691040039,y:290.29629707336426,z:-9.97326910495758},{x:423.3871841430664,y:271.91688537597656,z:74.37058925628662,name:"faceOval"},{x:377.68043518066406,y:304.62209701538086,z:-7.603961229324341,name:"lips"},{x:379.00428771972656,y:304.9314594268799,z:-8.57852816581726},{x:364.00279998779297,y:275.2813911437988,z:-19.25792098045349},{x:374.68231201171875,y:273.82555961608887,z:-11.28047227859497},{x:365.0354766845703,y:273.4548568725586,z:-18.791062831878662},{x:380.61901092529297,y:249.8848056793213,z:.15501167625188828},{x:391.14158630371094,y:254.7934627532959,z:2.0906515419483185},{x:378.1761169433594,y:264.9612236022949,z:-12.605184316635132},{x:400.9540557861328,y:179.99592304229736,z:27.82477855682373,name:"faceOval"},{x:398.0038833618164,y:188.50656509399414,z:16.094952821731567},{x:394.8717498779297,y:199.0359592437744,z:6.226727366447449,name:"leftEyebrow"},{x:382.10926055908203,y:316.83926582336426,z:-8.946179747581482},{x:366.51588439941406,y:200.32583713531494,z:-5.24632453918457,name:"leftEyebrow"},{x:367.4893569946289,y:183.87210845947266,z:1.9039081037044525},{x:368.6243438720703,y:168.8127565383911,z:8.736093044281006,name:"faceOval"},{x:398.96175384521484,y:234.9675178527832,z:13.713973760604858},{x:412.9645538330078,y:242.23042488098145,z:23.272905349731445},{x:372.05257415771484,y:231.41919136047363,z:9.226294755935669},{x:406.0722351074219,y:223.58965873718262,z:18.370890617370605},{x:368.27442169189453,y:240.2039337158203,z:-4.166713654994965},{x:372.3575210571289,y:260.66442489624023,z:-24.976940155029297},{x:419.2244338989258,y:247.9079246520996,z:30.299127101898193},{x:409.43885803222656,y:246.60913467407227,z:16.398411989212036},{x:401.69139862060547,y:248.76328468322754,z:9.395531415939331},{x:389.7608184814453,y:247.56915092468262,z:5.841569304466248},{x:380.5461883544922,y:244.55984115600586,z:4.263003468513489},{x:373.25817108154297,y:240.80214500427246,z:2.5356262922286987},{x:358.77086639404297,y:229.35615062713623,z:-10.387605428695679},{x:419.5793914794922,y:262.8478717803955,z:26.5175724029541},{x:410.8808898925781,y:222.51372814178467,z:22.199130058288574},{x:358.45714569091797,y:268.91467094421387,z:-33.17030906677246},{x:373.4129333496094,y:251.6385841369629,z:-5.771540403366089},{x:422.5408172607422,y:239.23919677734375,z:74.04378890991211,name:"faceOval"},{x:367.8171920776367,y:236.58040523529053,z:1.820748895406723},{x:378.51959228515625,y:266.2532329559326,z:-5.74819803237915},{x:403.3472442626953,y:229.05112266540527,z:19.689764976501465},{x:372.34840393066406,y:256.6451168060303,z:-21.872329711914062},{x:422.54566192626953,y:289.1587829589844,z:68.67491245269775,name:"faceOval"},{x:371.9297409057617,y:228.90116214752197,z:11.432201862335205,name:"leftEye"},{x:366.21360778808594,y:251.6158962249756,z:-28.19826364517212},{x:409.1571807861328,y:321.3156223297119,z:20.2266526222229},{x:408.52943420410156,y:331.44238471984863,z:31.09278917312622,name:"faceOval"},{x:424.2788314819336,y:267.1992301940918,z:50.467424392700195},{x:415.60352325439453,y:311.6528606414795,z:30.579242706298828},{x:418.12793731689453,y:221.59927368164062,z:46.26569747924805},{x:385.68286895751953,y:346.0184955596924,z:-5.70151150226593},{x:357.82936096191406,y:271.3758373260498,z:-24.836881160736084},{x:379.588623046875,y:257.5071716308594,z:-3.755294680595398},{x:417.4592590332031,y:234.71948146820068,z:34.5475435256958},{x:393.4684371948242,y:231.58967971801758,z:11.408859491348267,name:"leftEye"},{x:387.8864288330078,y:232.14245796203613,z:9.51808214187622,name:"leftEye"},{x:382.4981689453125,y:307.5654888153076,z:-7.522260546684265,name:"lips"},{x:419.00169372558594,y:277.8332805633545,z:26.424202919006348},{x:373.62953186035156,y:357.6375102996826,z:-5.75986921787262,name:"faceOval"},{x:392.8708267211914,y:347.72446632385254,z:10.154176950454712,name:"faceOval"},{x:400.3953552246094,y:341.0005187988281,z:19.39797878265381,name:"faceOval"},{x:382.25440979003906,y:231.66935920715332,z:8.998700976371765,name:"leftEye"},{x:377.14550018310547,y:230.4228687286377,z:9.804032444953918,name:"leftEye"},{x:373.8358688354492,y:229.64950561523438,z:11.292144060134888,name:"leftEye"},{x:414.5794677734375,y:221.67891025543213,z:29.412097930908203},{x:377.00672149658203,y:225.66201210021973,z:9.360517263412476,name:"leftEye"},{x:382.29530334472656,y:224.8431158065796,z:8.32175612449646,name:"leftEye"},{x:387.5133514404297,y:224.49507236480713,z:8.917000889778137,name:"leftEye"},{x:393.15906524658203,y:225.24795055389404,z:10.737749338150024,name:"leftEye"},{x:397.05554962158203,y:226.55359268188477,z:13.002015352249146,name:"leftEye"},{x:420.5299377441406,y:221.014666557312,z:65.40690422058105,name:"faceOval"},{x:397.06920623779297,y:230.6661558151245,z:13.807345628738403,name:"leftEye"},{x:377.94647216796875,y:285.1647090911865,z:-13.305472135543823},{x:372.1118927001953,y:267.1267318725586,z:-18.83774757385254},{x:364.9968719482422,y:282.24411964416504,z:-19.818150997161865},{x:401.973876953125,y:331.20131492614746,z:11.566424369812012},{x:394.3083190917969,y:338.86693954467773,z:3.142542541027069},{x:373.9820861816406,y:351.4504623413086,z:-13.50388765335083},{x:414.3888854980469,y:321.24735832214355,z:45.51872253417969,name:"faceOval"},{x:373.44234466552734,y:227.33163356781006,z:10.626870393753052,name:"leftEye"},{x:364.0731430053711,y:240.31539916992188,z:-13.807345628738403},{x:384.2658233642578,y:353.3793067932129,z:.7385850697755814,name:"faceOval"},{x:423.20526123046875,y:283.5176181793213,z:47.152724266052246},{x:369.42798614501953,y:304.0898895263672,z:-14.647691249847412,name:"lips"},{x:370.63812255859375,y:305.90051651000977,z:-16.211668252944946},{x:371.91192626953125,y:309.0167713165283,z:-17.84567356109619},{x:373.0583953857422,y:313.3545398712158,z:-17.378815412521362,name:"lips"},{x:375.39905548095703,y:321.09289169311523,z:-13.118728399276733},{x:379.2567825317383,y:304.3582534790039,z:-7.924926280975342},{x:381.18797302246094,y:303.7031364440918,z:-7.843226194381714},{x:383.0918502807617,y:302.4884605407715,z:-7.6506465673446655,name:"lips"},{x:389.09461975097656,y:297.1475315093994,z:-5.5497825145721436},{x:411.6408920288086,y:280.24898529052734,z:12.02161192893982},{x:363.3110809326172,y:234.27620887756348,z:-6.775286793708801},{x:366.0474395751953,y:223.29872131347656,z:6.827808618545532},{x:370.34427642822266,y:225.1457118988037,z:9.558931589126587},{x:377.5371551513672,y:303.60079765319824,z:-7.358860373497009,name:"lips"},{x:412.9557800292969,y:299.53579902648926,z:19.39797878265381},{x:360.0810241699219,y:221.72012329101562,z:-2.153385728597641},{x:379.82784271240234,y:329.47723388671875,z:-10.48097848892212},{x:359.08477783203125,y:235.7911491394043,z:-18.079102039337158},{x:369.6688461303711,y:251.5407943725586,z:-14.962821006774902},{x:369.5555114746094,y:333.5307312011719,z:-15.67478060722351},{x:394.0193176269531,y:315.6973171234131,z:-.9920747578144073},{x:383.78997802734375,y:272.7268695831299,z:-4.689012169837952},{x:387.67765045166016,y:323.6722755432129,z:-5.640236139297485},{x:397.8769302368164,y:272.1331214904785,z:-.9395531564950943},{x:389.87476348876953,y:280.5630111694336,z:-4.29218202829361},{x:403.83888244628906,y:285.1167869567871,z:3.0229100584983826},{x:372.5467300415039,y:343.1070327758789,z:-16.153310537338257},{x:374.1112518310547,y:256.3721466064453,z:-10.574349164962769},{x:399.73785400390625,y:321.77515983581543,z:4.849494695663452},{x:392.03365325927734,y:330.56447982788086,z:-1.3407598435878754},{x:398.59134674072266,y:305.93902587890625,z:1.517290621995926},{x:417.95997619628906,y:290.9716987609863,z:26.89105987548828},{x:406.04541778564453,y:307.35154151916504,z:8.666064143180847},{x:420.75328826904297,y:298.40752601623535,z:41.78385257720947},{x:395.4522705078125,y:291.4153575897217,z:-2.1752697229385376},{x:368.6452102661133,y:245.8882999420166,z:-9.453888535499573},{x:370.34900665283203,y:263.56690406799316,z:-26.75100326538086},{x:374.98477935791016,y:266.6126346588135,z:-19.77146625518799},{x:366.99840545654297,y:258.12140464782715,z:-31.372904777526855},{x:371.00616455078125,y:217.63479709625244,z:5.60522198677063},{x:381.30577087402344,y:214.14087295532227,z:4.983716309070587},{x:390.1496124267578,y:213.38221549987793,z:5.593550801277161},{x:397.7696990966797,y:214.3659782409668,z:8.57852816581726},{x:403.1652069091797,y:217.65509605407715,z:13.013685941696167},{x:407.3551940917969,y:230.72525024414062,z:22.444231510162354},{x:424.0876770019531,y:251.7839241027832,z:51.16771221160889},{x:403.50196838378906,y:239.88757610321045,z:15.803166627883911},{x:397.31719970703125,y:241.49806022644043,z:11.233787536621094},{x:388.99425506591797,y:241.4366912841797,z:7.948269248008728},{x:380.7804489135742,y:239.78078842163086,z:6.600214838981628},{x:374.01336669921875,y:237.11946487426758,z:6.349278092384338},{x:369.39125061035156,y:234.35351371765137,z:5.987462401390076},{x:422.9730987548828,y:255.76455116271973,z:76.61150932312012,name:"faceOval"},{x:374.73915100097656,y:269.24214363098145,z:-16.608498096466064},{x:364.61681365966797,y:245.71088790893555,z:-20.02823829650879},{x:365.3834533691406,y:263.34174156188965,z:-32.32996463775635},{x:361.58252716064453,y:267.8273677825928,z:-30.345816612243652},{x:365.37208557128906,y:265.0249671936035,z:-29.178667068481445},{x:372.72605895996094,y:272.05135345458984,z:-14.834434986114502},{x:360.48614501953125,y:268.34827423095703,z:-32.189905643463135},{x:359.9516296386719,y:270.8049201965332,z:-24.650139808654785},{x:369.5049285888672,y:229.01945114135742,z:10.107489824295044},{x:365.5447769165039,y:230.24096488952637,z:5.593550801277161},{x:363.50669860839844,y:230.6208372116089,z:.43622106313705444},{x:399.3529510498047,y:227.65677452087402,z:15.35965085029602,name:"leftEye"},{x:402.5693130493164,y:224.60190296173096,z:15.931552648544312}],box:{xMin:277.8318977355957,yMin:168.7741756439209,xMax:424.2788314819336,yMax:359.8348903656006,width:146.4469337463379,height:191.0607147216797}},TRIANGULATION:[127,34,139,11,0,37,232,231,120,72,37,39,128,121,47,232,121,128,104,69,67,175,171,148,157,154,155,118,50,101,73,39,40,9,151,108,48,115,131,194,204,211,74,40,185,80,42,183,40,92,186,230,229,118,202,212,214,83,18,17,76,61,146,160,29,30,56,157,173,106,204,194,135,214,192,203,165,98,21,71,68,51,45,4,144,24,23,77,146,91,205,50,187,201,200,18,91,106,182,90,91,181,85,84,17,206,203,36,148,171,140,92,40,39,193,189,244,159,158,28,247,246,161,236,3,196,54,68,104,193,168,8,117,228,31,189,193,55,98,97,99,126,47,100,166,79,218,155,154,26,209,49,131,135,136,150,47,126,217,223,52,53,45,51,134,211,170,140,67,69,108,43,106,91,230,119,120,226,130,247,63,53,52,238,20,242,46,70,156,78,62,96,46,53,63,143,34,227,173,155,133,123,117,111,44,125,19,236,134,51,216,206,205,154,153,22,39,37,167,200,201,208,36,142,100,57,212,202,20,60,99,28,158,157,35,226,113,160,159,27,204,202,210,113,225,46,43,202,204,62,76,77,137,123,116,41,38,72,203,129,142,64,98,240,49,102,64,41,73,74,212,216,207,42,74,184,169,170,211,170,149,176,105,66,69,122,6,168,123,147,187,96,77,90,65,55,107,89,90,180,101,100,120,63,105,104,93,137,227,15,86,85,129,102,49,14,87,86,55,8,9,100,47,121,145,23,22,88,89,179,6,122,196,88,95,96,138,172,136,215,58,172,115,48,219,42,80,81,195,3,51,43,146,61,171,175,199,81,82,38,53,46,225,144,163,110,246,33,7,52,65,66,229,228,117,34,127,234,107,108,69,109,108,151,48,64,235,62,78,191,129,209,126,111,35,143,163,161,246,117,123,50,222,65,52,19,125,141,221,55,65,3,195,197,25,7,33,220,237,44,70,71,139,122,193,245,247,130,33,71,21,162,153,158,159,170,169,150,188,174,196,216,186,92,144,160,161,2,97,167,141,125,241,164,167,37,72,38,12,145,159,160,38,82,13,63,68,71,226,35,111,158,153,154,101,50,205,206,92,165,209,198,217,165,167,97,220,115,218,133,112,243,239,238,241,214,135,169,190,173,133,171,208,32,125,44,237,86,87,178,85,86,179,84,85,180,83,84,181,201,83,182,137,93,132,76,62,183,61,76,184,57,61,185,212,57,186,214,207,187,34,143,156,79,239,237,123,137,177,44,1,4,201,194,32,64,102,129,213,215,138,59,166,219,242,99,97,2,94,141,75,59,235,24,110,228,25,130,226,23,24,229,22,23,230,26,22,231,112,26,232,189,190,243,221,56,190,28,56,221,27,28,222,29,27,223,30,29,224,247,30,225,238,79,20,166,59,75,60,75,240,147,177,215,20,79,166,187,147,213,112,233,244,233,128,245,128,114,188,114,217,174,131,115,220,217,198,236,198,131,134,177,132,58,143,35,124,110,163,7,228,110,25,356,389,368,11,302,267,452,350,349,302,303,269,357,343,277,452,453,357,333,332,297,175,152,377,384,398,382,347,348,330,303,304,270,9,336,337,278,279,360,418,262,431,304,408,409,310,415,407,270,409,410,450,348,347,422,430,434,313,314,17,306,307,375,387,388,260,286,414,398,335,406,418,364,367,416,423,358,327,251,284,298,281,5,4,373,374,253,307,320,321,425,427,411,421,313,18,321,405,406,320,404,405,315,16,17,426,425,266,377,400,369,322,391,269,417,465,464,386,257,258,466,260,388,456,399,419,284,332,333,417,285,8,346,340,261,413,441,285,327,460,328,355,371,329,392,439,438,382,341,256,429,420,360,364,394,379,277,343,437,443,444,283,275,440,363,431,262,369,297,338,337,273,375,321,450,451,349,446,342,467,293,334,282,458,461,462,276,353,383,308,324,325,276,300,293,372,345,447,382,398,362,352,345,340,274,1,19,456,248,281,436,427,425,381,256,252,269,391,393,200,199,428,266,330,329,287,273,422,250,462,328,258,286,384,265,353,342,387,259,257,424,431,430,342,353,276,273,335,424,292,325,307,366,447,345,271,303,302,423,266,371,294,455,460,279,278,294,271,272,304,432,434,427,272,407,408,394,430,431,395,369,400,334,333,299,351,417,168,352,280,411,325,319,320,295,296,336,319,403,404,330,348,349,293,298,333,323,454,447,15,16,315,358,429,279,14,15,316,285,336,9,329,349,350,374,380,252,318,402,403,6,197,419,318,319,325,367,364,365,435,367,397,344,438,439,272,271,311,195,5,281,273,287,291,396,428,199,311,271,268,283,444,445,373,254,339,263,466,249,282,334,296,449,347,346,264,447,454,336,296,299,338,10,151,278,439,455,292,407,415,358,371,355,340,345,372,390,249,466,346,347,280,442,443,282,19,94,370,441,442,295,248,419,197,263,255,359,440,275,274,300,383,368,351,412,465,263,467,466,301,368,389,380,374,386,395,378,379,412,351,419,436,426,322,373,390,388,2,164,393,370,462,461,164,0,267,302,11,12,374,373,387,268,12,13,293,300,301,446,261,340,385,384,381,330,266,425,426,423,391,429,355,437,391,327,326,440,457,438,341,382,362,459,457,461,434,430,394,414,463,362,396,369,262,354,461,457,316,403,402,315,404,403,314,405,404,313,406,405,421,418,406,366,401,361,306,408,407,291,409,408,287,410,409,432,436,410,434,416,411,264,368,383,309,438,457,352,376,401,274,275,4,421,428,262,294,327,358,433,416,367,289,455,439,462,370,326,2,326,370,305,460,455,254,449,448,255,261,446,253,450,449,252,451,450,256,452,451,341,453,452,413,464,463,441,413,414,258,442,441,257,443,442,259,444,443,260,445,444,467,342,445,459,458,250,289,392,290,290,328,460,376,433,435,250,290,392,411,416,433,341,463,464,453,464,465,357,465,412,343,412,399,360,363,440,437,399,456,420,456,363,401,435,288,372,383,353,339,255,249,448,261,255,133,243,190,133,155,112,33,246,247,33,130,25,398,384,286,362,398,414,362,463,341,263,359,467,263,249,255,466,467,260,75,60,166,238,239,79,162,127,139,72,11,37,121,232,120,73,72,39,114,128,47,233,232,128,103,104,67,152,175,148,173,157,155,119,118,101,74,73,40,107,9,108,49,48,131,32,194,211,184,74,185,191,80,183,185,40,186,119,230,118,210,202,214,84,83,17,77,76,146,161,160,30,190,56,173,182,106,194,138,135,192,129,203,98,54,21,68,5,51,4,145,144,23,90,77,91,207,205,187,83,201,18,181,91,182,180,90,181,16,85,17,205,206,36,176,148,140,165,92,39,245,193,244,27,159,28,30,247,161,174,236,196,103,54,104,55,193,8,111,117,31,221,189,55,240,98,99,142,126,100,219,166,218,112,155,26,198,209,131,169,135,150,114,47,217,224,223,53,220,45,134,32,211,140,109,67,108,146,43,91,231,230,120,113,226,247,105,63,52,241,238,242,124,46,156,95,78,96,70,46,63,116,143,227,116,123,111,1,44,19,3,236,51,207,216,205,26,154,22,165,39,167,199,200,208,101,36,100,43,57,202,242,20,99,56,28,157,124,35,113,29,160,27,211,204,210,124,113,46,106,43,204,96,62,77,227,137,116,73,41,72,36,203,142,235,64,240,48,49,64,42,41,74,214,212,207,183,42,184,210,169,211,140,170,176,104,105,69,193,122,168,50,123,187,89,96,90,66,65,107,179,89,180,119,101,120,68,63,104,234,93,227,16,15,85,209,129,49,15,14,86,107,55,9,120,100,121,153,145,22,178,88,179,197,6,196,89,88,96,135,138,136,138,215,172,218,115,219,41,42,81,5,195,51,57,43,61,208,171,199,41,81,38,224,53,225,24,144,110,105,52,66,118,229,117,227,34,234,66,107,69,10,109,151,219,48,235,183,62,191,142,129,126,116,111,143,7,163,246,118,117,50,223,222,52,94,19,141,222,221,65,196,3,197,45,220,44,156,70,139,188,122,245,139,71,162,145,153,159,149,170,150,122,188,196,206,216,92,163,144,161,164,2,167,242,141,241,0,164,37,11,72,12,144,145,160,12,38,13,70,63,71,31,226,111,157,158,154,36,101,205,203,206,165,126,209,217,98,165,97,237,220,218,237,239,241,210,214,169,140,171,32,241,125,237,179,86,178,180,85,179,181,84,180,182,83,181,194,201,182,177,137,132,184,76,183,185,61,184,186,57,185,216,212,186,192,214,187,139,34,156,218,79,237,147,123,177,45,44,4,208,201,32,98,64,129,192,213,138,235,59,219,141,242,97,97,2,141,240,75,235,229,24,228,31,25,226,230,23,229,231,22,230,232,26,231,233,112,232,244,189,243,189,221,190,222,28,221,223,27,222,224,29,223,225,30,224,113,247,225,99,60,240,213,147,215,60,20,166,192,187,213,243,112,244,244,233,245,245,128,188,188,114,174,134,131,220,174,217,236,236,198,134,215,177,58,156,143,124,25,110,7,31,228,25,264,356,368,0,11,267,451,452,349,267,302,269,350,357,277,350,452,357,299,333,297,396,175,377,381,384,382,280,347,330,269,303,270,151,9,337,344,278,360,424,418,431,270,304,409,272,310,407,322,270,410,449,450,347,432,422,434,18,313,17,291,306,375,259,387,260,424,335,418,434,364,416,391,423,327,301,251,298,275,281,4,254,373,253,375,307,321,280,425,411,200,421,18,335,321,406,321,320,405,314,315,17,423,426,266,396,377,369,270,322,269,413,417,464,385,386,258,248,456,419,298,284,333,168,417,8,448,346,261,417,413,285,326,327,328,277,355,329,309,392,438,381,382,256,279,429,360,365,364,379,355,277,437,282,443,283,281,275,363,395,431,369,299,297,337,335,273,321,348,450,349,359,446,467,283,293,282,250,458,462,300,276,383,292,308,325,283,276,293,264,372,447,346,352,340,354,274,19,363,456,281,426,436,425,380,381,252,267,269,393,421,200,428,371,266,329,432,287,422,290,250,328,385,258,384,446,265,342,386,387,257,422,424,430,445,342,276,422,273,424,306,292,307,352,366,345,268,271,302,358,423,371,327,294,460,331,279,294,303,271,304,436,432,427,304,272,408,395,394,431,378,395,400,296,334,299,6,351,168,376,352,411,307,325,320,285,295,336,320,319,404,329,330,349,334,293,333,366,323,447,316,15,315,331,358,279,317,14,316,8,285,9,277,329,350,253,374,252,319,318,403,351,6,419,324,318,325,397,367,365,288,435,397,278,344,439,310,272,311,248,195,281,375,273,291,175,396,199,312,311,268,276,283,445,390,373,339,295,282,296,448,449,346,356,264,454,337,336,299,337,338,151,294,278,455,308,292,415,429,358,355,265,340,372,388,390,466,352,346,280,295,442,282,354,19,370,285,441,295,195,248,197,457,440,274,301,300,368,417,351,465,251,301,389,385,380,386,394,395,379,399,412,419,410,436,322,387,373,388,326,2,393,354,370,461,393,164,267,268,302,12,386,374,387,312,268,13,298,293,301,265,446,340,380,385,381,280,330,425,322,426,391,420,429,437,393,391,326,344,440,438,458,459,461,364,434,394,428,396,262,274,354,457,317,316,402,316,315,403,315,314,404,314,313,405,313,421,406,323,366,361,292,306,407,306,291,408,291,287,409,287,432,410,427,434,411,372,264,383,459,309,457,366,352,401,1,274,4,418,421,262,331,294,358,435,433,367,392,289,439,328,462,326,94,2,370,289,305,455,339,254,448,359,255,446,254,253,449,253,252,450,252,256,451,256,341,452,414,413,463,286,441,414,286,258,441,258,257,442,257,259,443,259,260,444,260,467,445,309,459,250,305,289,290,305,290,460,401,376,435,309,250,392,376,411,433,453,341,464,357,453,465,343,357,412,437,343,399,344,360,440,420,437,456,360,420,363,361,401,288,265,372,353,390,339,249,339,448,255]},Br=1e-5;const Or=T.forwardRef((function({args:[e=1,t=1,r=1]=[],radius:o=.05,steps:a=1,smoothness:i=4,creaseAngle:s=.4,children:l,...c},u){const d=T.useMemo((()=>function(e,t,r){const o=new n.Shape,a=r-Br;return o.absarc(Br,Br,Br,-Math.PI/2,-Math.PI,!0),o.absarc(Br,t-2*a,Br,Math.PI,Math.PI/2,!0),o.absarc(e-2*a,t-2*a,Br,Math.PI/2,0,!0),o.absarc(e-2*a,Br,Br,0,-Math.PI/2,!0),o}(e,t,o)),[e,t,o]),f=T.useMemo((()=>({depth:r-2*o,bevelEnabled:!0,bevelSegments:2*i,steps:a,bevelSize:o-Br,bevelThickness:o,curveSegments:i})),[r,o,i]),p=T.useRef();return T.useLayoutEffect((()=>{p.current&&(p.current.center(),m.toCreasedNormals(p.current,s))}),[d,f]),T.createElement("mesh",C.default({ref:u},c),T.createElement("extrudeGeometry",{ref:p,args:[d,f]}),l)}));function Ir(){const e=new R.BufferGeometry,t=new Float32Array([-1,-1,3,-1,-1,3]);return e.setAttribute("position",new R.BufferAttribute(t,2)),e}const Ur=T.forwardRef((function({children:e,...t},r){const n=T.useMemo(Ir,[]);return T.createElement("mesh",C.default({ref:r,geometry:n,frustumCulled:!1},t),e)})),Vr=T.forwardRef((({children:e,width:t,height:r,depth:n,box3:o,precise:a=!0,...i},s)=>{const l=T.useRef(null),c=T.useRef(null),u=T.useRef(null);return T.useLayoutEffect((()=>{c.current.matrixWorld.identity();let e=o||(new R.Box3).setFromObject(u.current,a);const i=e.max.x-e.min.x,s=e.max.y-e.min.y,l=e.max.z-e.min.z;let m=Math.max(i,s,l);t&&(m=i),r&&(m=s),n&&(m=l),c.current.scale.setScalar(1/m)}),[t,r,n,o,a]),T.useImperativeHandle(s,(()=>l.current),[]),T.createElement("group",C.default({ref:l},i),T.createElement("group",{ref:c},T.createElement("group",{ref:u},e)))})),jr=e=>e&&e.isOrthographicCamera,Wr=T.createContext(null);function Nr({children:e,damping:t=6,fit:r,clip:n,observe:a,margin:i=1.2,eps:s=.01,onFit:l}){const c=T.useRef(null),{camera:u,invalidate:m,size:d,controls:f}=o.useThree(),p=f,h=T.useRef(l);function v(e,t){return Math.abs(e.x-t.x)<s&&Math.abs(e.y-t.y)<s&&Math.abs(e.z-t.z)<s}function x(e,t,r,n){e.x=R.MathUtils.damp(e.x,t.x,r,n),e.y=R.MathUtils.damp(e.y,t.y,r,n),e.z=R.MathUtils.damp(e.z,t.z,r,n)}h.current=l;const[y]=T.useState((()=>({animating:!1,focus:new R.Vector3,camera:new R.Vector3,zoom:1}))),[g]=T.useState((()=>({focus:new R.Vector3,camera:new R.Vector3,zoom:1}))),[w]=T.useState((()=>new R.Box3)),b=T.useMemo((()=>{function e(){const e=w.getSize(new R.Vector3),t=w.getCenter(new R.Vector3),r=Math.max(e.x,e.y,e.z),n=jr(u)?4*r:r/(2*Math.atan(Math.PI*u.fov/360)),o=jr(u)?4*r:n/u.aspect,a=i*Math.max(n,o);return{box:w,size:e,center:t,distance:a}}return{getSize:e,refresh(t){if((r=t)&&r.isBox3)w.copy(t);else{const e=t||c.current;e.updateWorldMatrix(!0,!0),w.setFromObject(e)}var r;if(w.isEmpty()){const e=u.position.length()||10;w.setFromCenterAndSize(new R.Vector3,new R.Vector3(e,e,e))}if("OrthographicTrackballControls"===(null==p?void 0:p.constructor.name)){const{distance:t}=e(),r=u.position.clone().sub(p.target).normalize().multiplyScalar(t),n=p.target.clone().add(r);u.position.copy(n)}return this},clip(){const{distance:t}=e();return p&&(p.maxDistance=10*t),u.near=t/100,u.far=100*t,u.updateProjectionMatrix(),p&&p.update(),m(),this},to({position:r,target:n}){y.camera.copy(u.position);const{center:o}=e();return g.camera.set(...r),n?g.focus.set(...n):g.focus.copy(o),t?y.animating=!0:u.position.set(...r),this},fit(){y.camera.copy(u.position),p&&y.focus.copy(p.target);const{center:r,distance:n}=e(),o=r.clone().sub(u.position).normalize().multiplyScalar(n);if(g.camera.copy(r).sub(o),g.focus.copy(r),jr(u)){y.zoom=u.zoom;let e=0,n=0;const o=[new R.Vector3(w.min.x,w.min.y,w.min.z),new R.Vector3(w.min.x,w.max.y,w.min.z),new R.Vector3(w.min.x,w.min.y,w.max.z),new R.Vector3(w.min.x,w.max.y,w.max.z),new R.Vector3(w.max.x,w.max.y,w.max.z),new R.Vector3(w.max.x,w.max.y,w.min.z),new R.Vector3(w.max.x,w.min.y,w.max.z),new R.Vector3(w.max.x,w.min.y,w.min.z)];r.applyMatrix4(u.matrixWorldInverse);for(const t of o)t.applyMatrix4(u.matrixWorldInverse),e=Math.max(e,Math.abs(t.y-r.y)),n=Math.max(n,Math.abs(t.x-r.x));e*=2,n*=2;const a=(u.top-u.bottom)/e,s=(u.right-u.left)/n;g.zoom=Math.min(a,s)/i,t||(u.zoom=g.zoom,u.updateProjectionMatrix())}return t?y.animating=!0:(u.position.copy(g.camera),u.lookAt(g.focus),p&&(p.target.copy(g.focus),p.update())),h.current&&h.current(this.getSize()),m(),this}}}),[w,u,p,i,t,m]);T.useLayoutEffect((()=>{if(p){const e=()=>y.animating=!1;return p.addEventListener("start",e),()=>p.removeEventListener("start",e)}}),[p]);const E=T.useRef(0);return T.useLayoutEffect((()=>{(a||0==E.current++)&&(b.refresh(),r&&b.fit(),n&&b.clip())}),[d,n,r,a,u,p]),o.useFrame(((e,r)=>{if(y.animating){if(x(y.focus,g.focus,t,r),x(y.camera,g.camera,t,r),y.zoom=R.MathUtils.damp(y.zoom,g.zoom,t,r),u.position.copy(y.camera),jr(u)&&(u.zoom=y.zoom,u.updateProjectionMatrix()),p?(p.target.copy(y.focus),p.update()):u.lookAt(y.focus),m(),jr(u)&&!(Math.abs(y.zoom-g.zoom)<s))return;if(!jr(u)&&!v(y.camera,g.camera))return;if(p&&!v(y.focus,g.focus))return;y.animating=!1}})),T.createElement("group",{ref:c},T.createElement(Wr.Provider,{value:b},e))}function Gr(){return T.useContext(Wr)}const Hr=T.forwardRef((({intensity:e=1,decay:t,decayRate:r=.65,maxYaw:n=.1,maxPitch:a=.1,maxRoll:i=.1,yawFrequency:s=.1,pitchFrequency:l=.1,rollFrequency:c=.1},u)=>{const d=o.useThree((e=>e.camera)),f=o.useThree((e=>e.controls)),p=T.useRef(e),h=T.useRef(d.rotation.clone()),[v]=T.useState((()=>new m.SimplexNoise)),[x]=T.useState((()=>new m.SimplexNoise)),[y]=T.useState((()=>new m.SimplexNoise)),g=()=>{(p.current<0||p.current>1)&&(p.current=p.current<0?0:1)};return T.useImperativeHandle(u,(()=>({getIntensity:()=>p.current,setIntensity:e=>{p.current=e,g()}})),[]),T.useEffect((()=>{if(f){const e=()=>{h.current=d.rotation.clone()};return f.addEventListener("change",e),e(),()=>{f.removeEventListener("change",e)}}}),[d,f]),o.useFrame(((e,o)=>{const u=Math.pow(p.current,2),m=n*u*v.noise(e.clock.elapsedTime*s,1),f=a*u*x.noise(e.clock.elapsedTime*l,1),w=i*u*y.noise(e.clock.elapsedTime*c,1);d.rotation.set(h.current.x+f,h.current.y+m,h.current.z+w),t&&p.current>0&&(p.current-=r*o,g())})),null})),$r=T.forwardRef((({children:e,enabled:t=!0,speed:r=1,rotationIntensity:n=1,floatIntensity:a=1,floatingRange:i=[-.1,.1],...s},l)=>{const c=T.useRef(null),u=T.useRef(1e4*Math.random());return o.useFrame((e=>{var o,s;if(!t||0===r)return;const l=u.current+e.clock.getElapsedTime();c.current.rotation.x=Math.cos(l/4*r)/8*n,c.current.rotation.y=Math.sin(l/4*r)/8*n,c.current.rotation.z=Math.sin(l/4*r)/20*n;let m=Math.sin(l/4*r)/10;m=R.MathUtils.mapLinear(m,-.1,.1,null!==(o=null==i?void 0:i[0])&&void 0!==o?o:-.1,null!==(s=null==i?void 0:i[1])&&void 0!==s?s:.1),c.current.position.y=m*a,c.current.updateMatrix()})),T.createElement("group",s,T.createElement("group",{ref:F.default([c,l]),matrixAutoUpdate:!1},e))})),qr={sunset:"venice/venice_sunset_1k.hdr",dawn:"kiara/kiara_1_dawn_1k.hdr",night:"dikhololo/dikhololo_night_1k.hdr",warehouse:"empty-wharehouse/empty_warehouse_01_1k.hdr",forest:"forrest-slope/forest_slope_1k.hdr",apartment:"lebombo/lebombo_1k.hdr",studio:"studio-small-3/studio_small_03_1k.hdr",city:"potsdamer-platz/potsdamer_platz_1k.hdr",park:"rooitou/rooitou_park_1k.hdr",lobby:"st-fagans/st_fagans_interior_1k.hdr"};function Xr({files:e=["/px.png","/nx.png","/py.png","/ny.png","/pz.png","/nz.png"],path:t="",preset:r,encoding:a,extensions:i}={}){if(r){if(!(r in qr))throw new Error("Preset must be one of: "+Object.keys(qr).join(", "));e=qr[r],t="https://market-assets.fra1.cdn.digitaloceanspaces.com/market-assets/hdris/"}const s=Array.isArray(e),l=s?n.CubeTextureLoader:m.RGBELoader,c=o.useLoader(l,s?[e]:e,(e=>{e.setPath(t),i&&i(e)})),u=s?c[0]:c;return u.mapping=s?n.CubeReflectionMapping:n.EquirectangularReflectionMapping,u.encoding=(null!=a?a:s)?n.sRGBEncoding:n.LinearEncoding,u}function Yr(e,t,r,n,o=0){const a=(e=>{return(t=e).current&&t.current.isScene?e.current:e;var t})(t||r),i=a.background,s=a.environment,l=a.backgroundBlurriness||0;return"only"!==e&&(a.environment=n),e&&(a.background=n),e&&void 0!==a.backgroundBlurriness&&(a.backgroundBlurriness=o),()=>{"only"!==e&&(a.environment=s),e&&(a.background=i),e&&void 0!==a.backgroundBlurriness&&(a.backgroundBlurriness=l)}}function Kr({scene:e,background:t=!1,blur:r,map:n}){const a=o.useThree((e=>e.scene));return T.useLayoutEffect((()=>{if(n)return Yr(t,e,a,n,r)}),[a,e,n,t,r]),null}function Zr({background:e=!1,scene:t,blur:r,...n}){const a=Xr(n),i=o.useThree((e=>e.scene));return T.useLayoutEffect((()=>Yr(e,t,i,a,r)),[a,e,t,i,r]),null}function Qr({children:e,near:t=1,far:r=1e3,resolution:a=256,frames:i=1,map:s,background:l=!1,blur:c,scene:u,files:m,path:d,preset:f,extensions:p}){const h=o.useThree((e=>e.gl)),v=o.useThree((e=>e.scene)),x=T.useRef(null),[y]=T.useState((()=>new n.Scene)),g=T.useMemo((()=>{const e=new n.WebGLCubeRenderTarget(a);return e.texture.type=n.HalfFloatType,e}),[a]);T.useLayoutEffect((()=>(1===i&&x.current.update(h,y),Yr(l,u,v,g.texture,c))),[e,y,g.texture,u,v,l,i,h]);let w=1;return o.useFrame((()=>{(i===1/0||w<i)&&(x.current.update(h,y),w++)})),T.createElement(T.Fragment,null,o.createPortal(T.createElement(T.Fragment,null,e,T.createElement("cubeCamera",{ref:x,args:[t,r,g]}),m||f?T.createElement(Zr,{background:!0,files:m,preset:f,path:d,extensions:p}):s?T.createElement(Kr,{background:!0,map:s,extensions:p}):null),y))}function Jr(e){var t,r,n,a;const i=Xr(e),s=e.map||i;T.useMemo((()=>o.extend({GroundProjectedEnvImpl:m.GroundProjectedEnv})),[]);const l=T.useMemo((()=>[s]),[s]),c=null==(t=e.ground)?void 0:t.height,u=null==(r=e.ground)?void 0:r.radius,d=null!==(n=null==(a=e.ground)?void 0:a.scale)&&void 0!==n?n:1e3;return T.createElement(T.Fragment,null,T.createElement(Kr,C.default({},e,{map:s})),T.createElement("groundProjectedEnvImpl",{args:l,scale:d,height:c,radius:u}))}function en(e){return e.ground?T.createElement(Jr,e):e.map?T.createElement(Kr,e):e.children?T.createElement(Qr,e):T.createElement(Zr,e)}const tn=T.forwardRef((({scale:e=10,frames:t=1/0,opacity:r=1,width:n=1,height:a=1,blur:i=1,far:s=10,resolution:l=512,smooth:c=!0,color:u="#000000",depthWrite:d=!1,renderOrder:f,...p},h)=>{const v=T.useRef(null),x=o.useThree((e=>e.scene)),y=o.useThree((e=>e.gl)),g=T.useRef(null);n*=Array.isArray(e)?e[0]:e||1,a*=Array.isArray(e)?e[1]:e||1;const[w,b,E,M,z,S,P]=T.useMemo((()=>{const e=new R.WebGLRenderTarget(l,l),t=new R.WebGLRenderTarget(l,l);t.texture.generateMipmaps=e.texture.generateMipmaps=!1;const r=new R.PlaneGeometry(n,a).rotateX(Math.PI/2),o=new R.Mesh(r),i=new R.MeshDepthMaterial;i.depthTest=i.depthWrite=!1,i.onBeforeCompile=e=>{e.uniforms={...e.uniforms,ucolor:{value:new R.Color(u)}},e.fragmentShader=e.fragmentShader.replace("void main() {","uniform vec3 ucolor;\n           void main() {\n          "),e.fragmentShader=e.fragmentShader.replace("vec4( vec3( 1.0 - fragCoordZ ), opacity );","vec4( ucolor * fragCoordZ * 2.0, ( 1.0 - fragCoordZ ) * 1.0 );")};const s=new R.ShaderMaterial(m.HorizontalBlurShader),c=new R.ShaderMaterial(m.VerticalBlurShader);return c.depthTest=s.depthTest=!1,[e,r,i,o,s,c,t]}),[l,n,a,e,u]),D=e=>{M.visible=!0,M.material=z,z.uniforms.tDiffuse.value=w.texture,z.uniforms.h.value=1*e/256,y.setRenderTarget(P),y.render(M,g.current),M.material=S,S.uniforms.tDiffuse.value=P.texture,S.uniforms.v.value=1*e/256,y.setRenderTarget(w),y.render(M,g.current),M.visible=!1};let k,F,_=0;return o.useFrame((()=>{g.current&&(t===1/0||_<t)&&(_++,k=x.background,F=x.overrideMaterial,v.current.visible=!1,x.background=null,x.overrideMaterial=E,y.setRenderTarget(w),y.render(x,g.current),D(i),c&&D(.4*i),y.setRenderTarget(null),v.current.visible=!0,x.overrideMaterial=F,x.background=k)})),T.useImperativeHandle(h,(()=>v.current),[]),T.createElement("group",C.default({"rotation-x":Math.PI/2},p,{ref:v}),T.createElement("mesh",{renderOrder:f,geometry:b,scale:[1,-1,1],rotation:[-Math.PI/2,0,0]},T.createElement("meshBasicMaterial",{transparent:!0,map:w.texture,opacity:r,depthWrite:d})),T.createElement("orthographicCamera",{ref:g,args:[-n/2,n/2,a/2,-a/2,0,s]}))}));const rn=T.createContext(null),nn=Ee({color:new R.Color,blend:2,alphaTest:.75,opacity:0,map:null},"varying vec2 vUv;\n   void main() {\n     gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n     vUv = uv;\n   }","varying vec2 vUv;\n   uniform sampler2D map;\n   uniform vec3 color;\n   uniform float opacity;\n   uniform float alphaTest;\n   uniform float blend;\n   void main() {\n     vec4 sampledDiffuseColor = texture2D(map, vUv);\n     gl_FragColor = vec4(color * sampledDiffuseColor.r * blend, max(0.0, (1.0 - (sampledDiffuseColor.r + sampledDiffuseColor.g + sampledDiffuseColor.b) / alphaTest)) * opacity);\n     #include <tonemapping_fragment>\n     #include <encodings_fragment>\n   }"),on=T.forwardRef((({children:e,temporal:t,frames:r=40,limit:n=1/0,blend:a=20,scale:i=10,opacity:s=1,alphaTest:l=.75,color:c="black",colorBlend:u=2,resolution:m=1024,toneMapped:d=!0,...f},p)=>{o.extend({SoftShadowMaterial:nn});const h=o.useThree((e=>e.gl)),v=o.useThree((e=>e.scene)),x=o.useThree((e=>e.camera)),y=o.useThree((e=>e.invalidate)),g=T.useRef(null),w=T.useRef(null),[b]=T.useState((()=>new sn(h,v,m)));T.useLayoutEffect((()=>{b.configure(g.current)}),[]);const E=T.useMemo((()=>({lights:new Map,temporal:!!t,frames:Math.max(2,r),blend:Math.max(2,r===1/0?a:r),count:0,getMesh:()=>g.current,reset:()=>{b.clear();const e=g.current.material;e.opacity=0,e.alphaTest=0,E.count=0},update:(e=1)=>{const t=g.current.material;E.temporal?(t.opacity=Math.min(s,t.opacity+s/E.blend),t.alphaTest=Math.min(l,t.alphaTest+l/E.blend)):(t.opacity=s,t.alphaTest=l),w.current.visible=!0,b.prepare();for(let t=0;t<e;t++)E.lights.forEach((e=>e.update())),b.update(x,E.blend);w.current.visible=!1,b.finish()}})),[b,x,v,t,r,a,s,l]);return T.useLayoutEffect((()=>{E.reset(),E.temporal||E.frames===1/0||E.update(E.blend)})),T.useImperativeHandle(p,(()=>E),[E]),o.useFrame((()=>{(E.temporal||E.frames===1/0)&&E.count<E.frames&&E.count<n&&(y(),E.update(),E.count++)})),T.createElement("group",f,T.createElement("group",{traverse:()=>null,ref:w},T.createElement(rn.Provider,{value:E},e)),T.createElement("mesh",{receiveShadow:!0,ref:g,scale:i,rotation:[-Math.PI/2,0,0]},T.createElement("planeGeometry",null),T.createElement("softShadowMaterial",{transparent:!0,depthWrite:!1,toneMapped:d,color:c,blend:u,map:b.progressiveLightMap2.texture})))})),an=T.forwardRef((({castShadow:e=!0,bias:t=.001,mapSize:r=512,size:n=5,near:o=.5,far:a=500,frames:i=1,position:s=[0,0,0],radius:l=1,amount:c=8,intensity:u=1,ambient:m=.5,...d},f)=>{const p=T.useRef(null),h=new R.Vector3(...s).length(),v=T.useContext(rn),x=T.useCallback((()=>{let e;if(p.current)for(let t=0;t<p.current.children.length;t++)if(e=p.current.children[t],Math.random()>m)e.position.set(s[0]+R.MathUtils.randFloatSpread(l),s[1]+R.MathUtils.randFloatSpread(l),s[2]+R.MathUtils.randFloatSpread(l));else{let t=Math.acos(2*Math.random()-1)-Math.PI/2,r=2*Math.PI*Math.random();e.position.set(Math.cos(t)*Math.cos(r)*h,Math.abs(Math.cos(t)*Math.sin(r)*h),Math.sin(t)*h)}}),[l,m,h,...s]),y=T.useMemo((()=>({update:x})),[x]);return T.useImperativeHandle(f,(()=>y),[y]),T.useLayoutEffect((()=>{const e=p.current;return v&&v.lights.set(e.uuid,y),()=>{v.lights.delete(e.uuid)}}),[v,y]),T.createElement("group",C.default({ref:p},d),Array.from({length:c},((i,s)=>T.createElement("directionalLight",{key:s,castShadow:e,"shadow-bias":t,"shadow-mapSize":[r,r],intensity:u/c},T.createElement("orthographicCamera",{attach:"shadow-camera",args:[-n,n,n,-n,o,a]})))))}));class sn{constructor(e,t,r=1024){this.renderer=e,this.res=r,this.scene=t,this.buffer1Active=!1,this.lights=[],this.meshes=[],this.object=null,this.clearColor=new R.Color,this.clearAlpha=0;const n=/(Android|iPad|iPhone|iPod)/g.test(navigator.userAgent)?R.HalfFloatType:R.FloatType;this.progressiveLightMap1=new R.WebGLRenderTarget(this.res,this.res,{type:n}),this.progressiveLightMap2=new R.WebGLRenderTarget(this.res,this.res,{type:n}),this.discardMat=new ir,this.targetMat=new R.MeshLambertMaterial({fog:!1}),this.previousShadowMap={value:this.progressiveLightMap1.texture},this.averagingWindow={value:100},this.targetMat.onBeforeCompile=e=>{e.vertexShader="varying vec2 vUv;\n"+e.vertexShader.slice(0,-1)+"vUv = uv; gl_Position = vec4((uv - 0.5) * 2.0, 1.0, 1.0); }";const t=e.fragmentShader.indexOf("void main() {");e.fragmentShader="varying vec2 vUv;\n"+e.fragmentShader.slice(0,t)+"uniform sampler2D previousShadowMap;\n\tuniform float averagingWindow;\n"+e.fragmentShader.slice(t-1,-1)+"\nvec3 texelOld = texture2D(previousShadowMap, vUv).rgb;\n        gl_FragColor.rgb = mix(texelOld, gl_FragColor.rgb, 1.0/ averagingWindow);\n      }",e.uniforms.previousShadowMap=this.previousShadowMap,e.uniforms.averagingWindow=this.averagingWindow}}clear(){this.renderer.getClearColor(this.clearColor),this.clearAlpha=this.renderer.getClearAlpha(),this.renderer.setClearColor("black",1),this.renderer.setRenderTarget(this.progressiveLightMap1),this.renderer.clear(),this.renderer.setRenderTarget(this.progressiveLightMap2),this.renderer.clear(),this.renderer.setRenderTarget(null),this.renderer.setClearColor(this.clearColor,this.clearAlpha),this.lights=[],this.meshes=[],this.scene.traverse((e=>{!function(e){return!!e.geometry}(e)?function(e){return e.isLight}(e)&&this.lights.push({object:e,intensity:e.intensity}):this.meshes.push({object:e,material:e.material})}))}prepare(){this.lights.forEach((e=>e.object.intensity=0)),this.meshes.forEach((e=>e.object.material=this.discardMat))}finish(){this.lights.forEach((e=>e.object.intensity=e.intensity)),this.meshes.forEach((e=>e.object.material=e.material))}configure(e){this.object=e}update(e,t=100){if(!this.object)return;this.averagingWindow.value=t,this.object.material=this.targetMat;const r=this.buffer1Active?this.progressiveLightMap1:this.progressiveLightMap2,n=this.buffer1Active?this.progressiveLightMap2:this.progressiveLightMap1,o=this.scene.background;this.scene.background=null,this.renderer.setRenderTarget(r),this.previousShadowMap.value=n.texture,this.buffer1Active=!this.buffer1Active,this.renderer.render(this.scene,e),this.renderer.setRenderTarget(null),this.scene.background=o}}const ln={rembrandt:{main:[1,2,1],fill:[-2,-.5,-2]},portrait:{main:[-1,2,.5],fill:[-1,.5,-1.5]},upfront:{main:[0,2,1],fill:[-1,.5,-1.5]},soft:{main:[-2,4,4],fill:[-1,.5,-1.5]}};function cn({radius:e,adjustCamera:t}){const r=Gr();return T.useEffect((()=>{t&&r.refresh().clip().fit()}),[e,t]),null}const un=e=>0===e?0:Math.pow(2,10*e-10);const mn=T.forwardRef((({fog:e=!1,renderOrder:t,depthWrite:r=!1,colorStop:o=0,color:a="black",opacity:i=.5,...s},l)=>{const c=T.useMemo((()=>{const e=document.createElement("canvas");e.width=128,e.height=128;const t=e.getContext("2d"),r=t.createRadialGradient(e.width/2,e.height/2,0,e.width/2,e.height/2,e.width/2);return r.addColorStop(o,new n.Color(a).getStyle()),r.addColorStop(1,"rgba(0,0,0,0)"),t.fillStyle=r,t.fillRect(0,0,e.width,e.height),e}),[a,o]);return T.createElement("mesh",C.default({renderOrder:t,ref:l,"rotation-x":-Math.PI/2},s),T.createElement("planeGeometry",null),T.createElement("meshBasicMaterial",{transparent:!0,opacity:i,fog:e,depthWrite:r,side:n.DoubleSide},T.createElement("canvasTexture",{attach:"map",args:[c]})))}));function dn(e=R.FrontSide){const t={value:new R.Matrix4};return Object.assign(new R.MeshNormalMaterial({side:e}),{viewMatrix:t,onBeforeCompile:e=>{e.uniforms.viewMatrix=t,e.fragmentShader="vec3 inverseTransformDirection( in vec3 dir, in mat4 matrix ) {\n           return normalize( ( vec4( dir, 0.0 ) * matrix ).xyz );\n         }\n"+e.fragmentShader.replace("#include <normal_fragment_maps>","#include <normal_fragment_maps>\n           normal = inverseTransformDirection( normal, viewMatrix );\n")}})}const fn=Ee({causticsTexture:null,causticsTextureB:null,color:new R.Color,lightProjMatrix:new R.Matrix4,lightViewMatrix:new R.Matrix4},"varying vec3 vWorldPosition;   \n   void main() {\n     gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n     vec4 worldPosition = modelMatrix * vec4(position, 1.);\n     vWorldPosition = worldPosition.xyz;\n   }","varying vec3 vWorldPosition;\n  uniform vec3 color;\n  uniform sampler2D causticsTexture; \n  uniform sampler2D causticsTextureB; \n  uniform mat4 lightProjMatrix;\n  uniform mat4 lightViewMatrix;\n   void main() {\n    // Apply caustics  \n    vec4 lightSpacePos = lightProjMatrix * lightViewMatrix * vec4(vWorldPosition, 1.0);\n    lightSpacePos.xyz /= lightSpacePos.w;\n    lightSpacePos.xyz = lightSpacePos.xyz * 0.5 + 0.5; \n    vec3 front = texture2D(causticsTexture, lightSpacePos.xy).rgb;\n    vec3 back = texture2D(causticsTextureB, lightSpacePos.xy).rgb;\n    gl_FragColor = vec4((front + back) * color, 1.0);\n    #include <tonemapping_fragment>\n    #include <encodings_fragment>\n   }"),pn=Ee({cameraMatrixWorld:new R.Matrix4,cameraProjectionMatrixInv:new R.Matrix4,normalTexture:null,depthTexture:null,lightDir:new R.Vector3(0,1,0),lightPlaneNormal:new R.Vector3(0,1,0),lightPlaneConstant:0,near:.1,far:100,modelMatrix:new R.Matrix4,worldRadius:1/40,ior:1.1,bounces:0,resolution:1024,size:10,intensity:.5},"\n  varying vec2 vUv;\n  void main() {\n      vUv = uv;\n      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n  }","  \n  uniform mat4 cameraMatrixWorld;\n  uniform mat4 cameraProjectionMatrixInv;\n  uniform vec3 lightDir;\n  uniform vec3 lightPlaneNormal;\n  uniform float lightPlaneConstant;\n  uniform float near;\n  uniform float far;\n  uniform float time;\n  uniform float worldRadius;\n  uniform float resolution;\n  uniform float size;\n  uniform float intensity;\n  uniform float ior;\n  precision highp isampler2D;\n  precision highp usampler2D;\n  uniform sampler2D normalTexture;\n  uniform sampler2D depthTexture;\n  uniform float bounces;\n  varying vec2 vUv;\n  vec3 WorldPosFromDepth(float depth, vec2 coord) {\n    float z = depth * 2.0 - 1.0;\n    vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n    vec4 viewSpacePosition = cameraProjectionMatrixInv * clipSpacePosition;\n    // Perspective division\n    viewSpacePosition /= viewSpacePosition.w;\n    vec4 worldSpacePosition = cameraMatrixWorld * viewSpacePosition;\n    return worldSpacePosition.xyz;\n  }                  \n  float sdPlane( vec3 p, vec3 n, float h ) {\n    // n must be normalized\n    return dot(p,n) + h;\n  }\n  float planeIntersect( vec3 ro, vec3 rd, vec4 p ) {\n    return -(dot(ro,p.xyz)+p.w)/dot(rd,p.xyz);\n  }\n  vec3 totalInternalReflection(vec3 ro, vec3 rd, vec3 pos, vec3 normal, float ior, out vec3 rayOrigin, out vec3 rayDirection) {\n    rayOrigin = ro;\n    rayDirection = rd;\n    rayDirection = refract(rayDirection, normal, 1.0 / ior);\n    rayOrigin = pos + rayDirection * 0.1;\n    return rayDirection;\n  }\n  void main() {\n    // Each sample consists of random offset in the x and y direction\n    float caustic = 0.0;\n    float causticTexelSize = (1.0 / resolution) * size * 2.0;\n    float texelsNeeded = worldRadius / causticTexelSize;\n    float sampleRadius = texelsNeeded / resolution;\n    float sum = 0.0;\n    if (texture2D(depthTexture, vUv).x == 1.0) {\n      gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\n      return;\n    }\n    vec2 offset1 = vec2(-0.5, -0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset2 = vec2(-0.5, 0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset3 = vec2(0.5, 0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset4 = vec2(0.5, -0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 uv1 = vUv + offset1 * sampleRadius;\n    vec2 uv2 = vUv + offset2 * sampleRadius;\n    vec2 uv3 = vUv + offset3 * sampleRadius;\n    vec2 uv4 = vUv + offset4 * sampleRadius;\n    vec3 normal1 = texture2D(normalTexture, uv1, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal2 = texture2D(normalTexture, uv2, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal3 = texture2D(normalTexture, uv3, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal4 = texture2D(normalTexture, uv4, -10.0).rgb * 2.0 - 1.0;\n    float depth1 = texture2D(depthTexture, uv1, -10.0).x;\n    float depth2 = texture2D(depthTexture, uv2, -10.0).x;\n    float depth3 = texture2D(depthTexture, uv3, -10.0).x;\n    float depth4 = texture2D(depthTexture, uv4, -10.0).x;\n    // Sanity check the depths\n    if (depth1 == 1.0 || depth2 == 1.0 || depth3 == 1.0 || depth4 == 1.0) {\n      gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\n      return;\n    }\n    vec3 pos1 = WorldPosFromDepth(depth1, uv1);\n    vec3 pos2 = WorldPosFromDepth(depth2, uv2);\n    vec3 pos3 = WorldPosFromDepth(depth3, uv3);\n    vec3 pos4 = WorldPosFromDepth(depth4, uv4);\n    vec3 originPos1 = WorldPosFromDepth(0.0, uv1);\n    vec3 originPos2 = WorldPosFromDepth(0.0, uv2);\n    vec3 originPos3 = WorldPosFromDepth(0.0, uv3);\n    vec3 originPos4 = WorldPosFromDepth(0.0, uv4);\n    vec3 endPos1, endPos2, endPos3, endPos4;\n    vec3 endDir1, endDir2, endDir3, endDir4;\n    totalInternalReflection(originPos1, lightDir, pos1, normal1, ior, endPos1, endDir1);\n    totalInternalReflection(originPos2, lightDir, pos2, normal2, ior, endPos2, endDir2);\n    totalInternalReflection(originPos3, lightDir, pos3, normal3, ior, endPos3, endDir3);\n    totalInternalReflection(originPos4, lightDir, pos4, normal4, ior, endPos4, endDir4);\n    float lightPosArea = length(cross(originPos2 - originPos1, originPos3 - originPos1)) + length(cross(originPos3 - originPos1, originPos4 - originPos1));\n    float t1 = planeIntersect(endPos1, endDir1, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t2 = planeIntersect(endPos2, endDir2, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t3 = planeIntersect(endPos3, endDir3, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t4 = planeIntersect(endPos4, endDir4, vec4(lightPlaneNormal, lightPlaneConstant));\n    vec3 finalPos1 = endPos1 + endDir1 * t1;\n    vec3 finalPos2 = endPos2 + endDir2 * t2;\n    vec3 finalPos3 = endPos3 + endDir3 * t3;\n    vec3 finalPos4 = endPos4 + endDir4 * t4;\n    float finalArea = length(cross(finalPos2 - finalPos1, finalPos3 - finalPos1)) + length(cross(finalPos3 - finalPos1, finalPos4 - finalPos1));\n    caustic += intensity * (lightPosArea / finalArea);\n    // Calculate the area of the triangle in light spaces\n    gl_FragColor = vec4(vec3(max(caustic, 0.0)), 1.0);\n  }"),hn={depth:!0,minFilter:R.LinearFilter,magFilter:R.LinearFilter,encoding:R.LinearEncoding,type:R.UnsignedByteType},vn={minFilter:R.LinearMipmapLinearFilter,magFilter:R.LinearFilter,encoding:R.LinearEncoding,format:R.RGBAFormat,type:R.FloatType,generateMipmaps:!0},xn=T.forwardRef((({debug:e,children:t,frames:r=1,ior:n=1.1,color:a="white",causticsOnly:i=!1,backside:s=!1,backsideIOR:l=1.1,worldRadius:c=.3125,intensity:u=.05,resolution:d=2024,lightSource:f=[5,5,5],...p},h)=>{o.extend({CausticsProjectionMaterial:fn});const v=T.useRef(null),x=T.useRef(null),y=T.useRef(null),g=T.useRef(null),w=o.useThree((e=>e.gl)),b=jt(e&&x,R.CameraHelper),E=Xe(d,d,hn),M=Xe(d,d,hn),z=Xe(d,d,vn),S=Xe(d,d,vn),[P]=T.useState((()=>dn())),[D]=T.useState((()=>dn(R.BackSide))),[k]=T.useState((()=>new pn)),[F]=T.useState((()=>new m.FullScreenQuad(k)));T.useLayoutEffect((()=>{v.current.updateWorldMatrix(!1,!0)}));let _=0;const L=new R.Vector3,A=new R.Frustum,B=new R.Matrix4,O=new R.Plane,I=new R.Vector3,U=new R.Vector3,V=new R.Box3,j=new R.Vector3;return o.useFrame(((t,o)=>{if(r===1/0||_++<r){var a,m;Array.isArray(f)?I.fromArray(f).normalize():I.copy(v.current.worldToLocal(f.current.getWorldPosition(L)).normalize()),U.copy(I).multiplyScalar(-1);let t=[];null==(a=y.current.parent)||a.matrixWorld.identity(),V.setFromObject(y.current,!0),t.push(new R.Vector3(V.min.x,V.min.y,V.min.z)),t.push(new R.Vector3(V.min.x,V.min.y,V.max.z)),t.push(new R.Vector3(V.min.x,V.max.y,V.min.z)),t.push(new R.Vector3(V.min.x,V.max.y,V.max.z)),t.push(new R.Vector3(V.max.x,V.min.y,V.min.z)),t.push(new R.Vector3(V.max.x,V.min.y,V.max.z)),t.push(new R.Vector3(V.max.x,V.max.y,V.min.z)),t.push(new R.Vector3(V.max.x,V.max.y,V.max.z));const r=t.map((e=>e.clone()));V.getCenter(j),t=t.map((e=>e.clone().sub(j)));const o=O.set(U,0),p=t.map((e=>o.projectPoint(e,new R.Vector3))),h=p.reduce(((e,t)=>e.add(t)),L.set(0,0,0)).divideScalar(p.length),C=p.map((e=>e.distanceTo(h))).reduce(((e,t)=>Math.max(e,t))),T=t.map((e=>e.dot(I))).reduce(((e,t)=>Math.max(e,t)));x.current.position.copy(I.clone().multiplyScalar(T).add(j)),x.current.lookAt(y.current.localToWorld(j.clone()));const _=B.lookAt(x.current.position,j,L.set(0,1,0));x.current.left=-C,x.current.right=C,x.current.top=C,x.current.bottom=-C;const W=L.set(0,C,0).applyMatrix4(_),N=(x.current.position.y+W.y)/I.y;x.current.near=.1,x.current.far=N,x.current.updateProjectionMatrix(),x.current.updateMatrixWorld();const G=r.map((e=>e.add(I.clone().multiplyScalar(-e.y/I.y)))),H=G.reduce(((e,t)=>e.add(t)),L.set(0,0,0)).divideScalar(G.length),$=2*G.map((e=>Math.hypot(e.x-H.x,e.z-H.z))).reduce(((e,t)=>Math.max(e,t)));g.current.scale.setScalar($),g.current.position.copy(H),e&&(null==(m=b.current)||m.update()),D.viewMatrix.value=P.viewMatrix.value=x.current.matrixWorldInverse;const q=A.setFromProjectionMatrix(B.multiplyMatrices(x.current.projectionMatrix,x.current.matrixWorldInverse)).planes[4];k.cameraMatrixWorld=x.current.matrixWorld,k.cameraProjectionMatrixInv=x.current.projectionMatrixInverse,k.lightDir=U,k.lightPlaneNormal=q.normal,k.lightPlaneConstant=q.constant,k.near=x.current.near,k.far=x.current.far,k.resolution=d,k.size=C,k.intensity=u,k.worldRadius=c,y.current.visible=!0,w.setRenderTarget(E),w.clear(),y.current.overrideMaterial=P,w.render(y.current,x.current),w.setRenderTarget(M),w.clear(),s&&(y.current.overrideMaterial=D,w.render(y.current,x.current)),y.current.overrideMaterial=null,k.ior=n,g.current.material.lightProjMatrix=x.current.projectionMatrix,g.current.material.lightViewMatrix=x.current.matrixWorldInverse,k.normalTexture=E.texture,k.depthTexture=E.depthTexture,w.setRenderTarget(z),w.clear(),F.render(w),k.ior=l,k.normalTexture=M.texture,k.depthTexture=M.depthTexture,w.setRenderTarget(S),w.clear(),s&&F.render(w),w.setRenderTarget(null),i&&(y.current.visible=!1)}})),T.useImperativeHandle(h,(()=>v.current),[]),T.createElement("group",C.default({ref:v},p),T.createElement("scene",{ref:y},T.createElement("orthographicCamera",{ref:x,up:[0,1,0]}),t),T.createElement("mesh",{renderOrder:2,ref:g,"rotation-x":-Math.PI/2},T.createElement("planeGeometry",null),T.createElement("causticsProjectionMaterial",{transparent:!0,color:a,causticsTexture:z.texture,causticsTextureB:S.texture,blending:R.CustomBlending,blendSrc:R.OneFactor,blendDst:R.SrcAlphaFactor,depthWrite:!1}),e&&T.createElement(De,null,T.createElement("lineBasicMaterial",{color:"#ffff00",toneMapped:!1}))))}));o.extend({MeshReflectorMaterial:nr});const yn=T.forwardRef((({mixBlur:e=0,mixStrength:t=.5,resolution:r=256,blur:a=[0,0],args:i=[1,1],minDepthThreshold:s=.9,maxDepthThreshold:l=1,depthScale:c=0,depthToBlurRatioBias:u=.25,mirror:m=0,children:d,debug:f=0,distortion:p=1,mixContrast:h=1,distortionMap:v,...x},y)=>{T.useEffect((()=>{console.warn("Reflector has been deprecated and will be removed next major. Replace it with <MeshReflectorMaterial />!")}),[]);const g=o.useThree((({gl:e})=>e)),w=o.useThree((({camera:e})=>e)),b=o.useThree((({scene:e})=>e)),E=(a=Array.isArray(a)?a:[a,a])[0]+a[1]>0,M=T.useRef(null),[z]=T.useState((()=>new n.Plane)),[S]=T.useState((()=>new n.Vector3)),[P]=T.useState((()=>new n.Vector3)),[R]=T.useState((()=>new n.Vector3)),[D]=T.useState((()=>new n.Matrix4)),[k]=T.useState((()=>new n.Vector3(0,0,-1))),[_]=T.useState((()=>new n.Vector4)),[L]=T.useState((()=>new n.Vector3)),[A]=T.useState((()=>new n.Vector3)),[B]=T.useState((()=>new n.Vector4)),[O]=T.useState((()=>new n.Matrix4)),[I]=T.useState((()=>new n.PerspectiveCamera)),U=T.useCallback((()=>{if(P.setFromMatrixPosition(M.current.matrixWorld),R.setFromMatrixPosition(w.matrixWorld),D.extractRotation(M.current.matrixWorld),S.set(0,0,1),S.applyMatrix4(D),L.subVectors(P,R),L.dot(S)>0)return;L.reflect(S).negate(),L.add(P),D.extractRotation(w.matrixWorld),k.set(0,0,-1),k.applyMatrix4(D),k.add(R),A.subVectors(P,k),A.reflect(S).negate(),A.add(P),I.position.copy(L),I.up.set(0,1,0),I.up.applyMatrix4(D),I.up.reflect(S),I.lookAt(A),I.far=w.far,I.updateMatrixWorld(),I.projectionMatrix.copy(w.projectionMatrix),O.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),O.multiply(I.projectionMatrix),O.multiply(I.matrixWorldInverse),O.multiply(M.current.matrixWorld),z.setFromNormalAndCoplanarPoint(S,P),z.applyMatrix4(I.matrixWorldInverse),_.set(z.normal.x,z.normal.y,z.normal.z,z.constant);const e=I.projectionMatrix;B.x=(Math.sign(_.x)+e.elements[8])/e.elements[0],B.y=(Math.sign(_.y)+e.elements[9])/e.elements[5],B.z=-1,B.w=(1+e.elements[10])/e.elements[14],_.multiplyScalar(2/_.dot(B)),e.elements[2]=_.x,e.elements[6]=_.y,e.elements[10]=_.z+1,e.elements[14]=_.w}),[]),[V,j,W,N]=T.useMemo((()=>{const o={type:n.HalfFloatType,minFilter:n.LinearFilter,magFilter:n.LinearFilter},i=new n.WebGLRenderTarget(r,r,o);i.depthBuffer=!0,i.depthTexture=new n.DepthTexture(r,r),i.depthTexture.format=n.DepthFormat,i.depthTexture.type=n.UnsignedShortType;const d=new n.WebGLRenderTarget(r,r,o);return[i,d,new rr({gl:g,resolution:r,width:a[0],height:a[1],minDepthThreshold:s,maxDepthThreshold:l,depthScale:c,depthToBlurRatioBias:u}),{mirror:m,textureMatrix:O,mixBlur:e,tDiffuse:i.texture,tDepth:i.depthTexture,tDiffuseBlur:d.texture,hasBlur:E,mixStrength:t,minDepthThreshold:s,maxDepthThreshold:l,depthScale:c,depthToBlurRatioBias:u,transparent:!0,debug:f,distortion:p,distortionMap:v,mixContrast:h,"defines-USE_BLUR":E?"":void 0,"defines-USE_DEPTH":c>0?"":void 0,"defines-USE_DISTORTION":v?"":void 0}]}),[g,a,O,r,m,E,e,t,s,l,c,u,f,p,v,h]);return o.useFrame((()=>{if(null==M||!M.current)return;M.current.visible=!1;const e=g.xr.enabled,t=g.shadowMap.autoUpdate;U(),g.xr.enabled=!1,g.shadowMap.autoUpdate=!1,g.setRenderTarget(V),g.state.buffers.depth.setMask(!0),g.autoClear||g.clear(),g.render(b,I),E&&W.render(g,V,j),g.xr.enabled=e,g.shadowMap.autoUpdate=t,M.current.visible=!0,g.setRenderTarget(null)})),T.createElement("mesh",C.default({ref:F.default([M,y])},x),T.createElement("planeGeometry",{args:i}),d?d("meshReflectorMaterial",N):T.createElement("meshReflectorMaterial",N))}));class gn extends n.ShaderMaterial{constructor(){super({uniforms:{depth:{value:null},opacity:{value:1},attenuation:{value:2.5},anglePower:{value:12},spotPosition:{value:new n.Vector3(0,0,0)},lightColor:{value:new n.Color("white")},cameraNear:{value:0},cameraFar:{value:1},resolution:{value:new n.Vector2(0,0)}},transparent:!0,depthWrite:!1,vertexShader:"\n      varying vec3 vNormal;\n      varying vec3 vWorldPosition;\n      varying float vViewZ;\n      varying float vIntensity;\n      uniform vec3 spotPosition;\n      uniform float attenuation;      \n\n      void main() {\n        // compute intensity\n        vNormal = normalize( normalMatrix * normal );\n        vec4 worldPosition\t= modelMatrix * vec4( position, 1.0 );\n        vWorldPosition = worldPosition.xyz;\n        vec4 viewPosition = viewMatrix * worldPosition;\n        vViewZ = viewPosition.z;\n        float intensity\t= distance(worldPosition.xyz, spotPosition) / attenuation;\n        intensity\t= 1.0 - clamp(intensity, 0.0, 1.0);\n        vIntensity = intensity;        \n        // set gl_Position\n        gl_Position\t= projectionMatrix * viewPosition;\n\n      }",fragmentShader:"\n      #include <packing>\n\n      varying vec3 vNormal;\n      varying vec3 vWorldPosition;\n      uniform vec3 lightColor;\n      uniform vec3 spotPosition;\n      uniform float attenuation;\n      uniform float anglePower;\n      uniform sampler2D depth;\n      uniform vec2 resolution;\n      uniform float cameraNear;\n      uniform float cameraFar;\n      varying float vViewZ;\n      varying float vIntensity;\n      uniform float opacity;\n\n      float readDepth( sampler2D depthSampler, vec2 coord ) {\n        float fragCoordZ = texture2D( depthSampler, coord ).x;\n        float viewZ = perspectiveDepthToViewZ(fragCoordZ, cameraNear, cameraFar);\n        return viewZ;\n      }\n\n      void main() {\n        float d = 1.0;\n        bool isSoft = resolution[0] > 0.0 && resolution[1] > 0.0;\n        if (isSoft) {\n          vec2 sUv = gl_FragCoord.xy / resolution;\n          d = readDepth(depth, sUv);\n        }\n        float intensity = vIntensity;\n        vec3 normal\t= vec3(vNormal.x, vNormal.y, abs(vNormal.z));\n        float angleIntensity\t= pow( dot(normal, vec3(0.0, 0.0, 1.0)), anglePower );\n        intensity\t*= angleIntensity;\n        // fades when z is close to sampled depth, meaning the cone is intersecting existing geometry\n        if (isSoft) {\n          intensity\t*= smoothstep(0., 1., vViewZ - d);\n        }\n        gl_FragColor = vec4(lightColor, intensity * opacity);\n\n        #include <tonemapping_fragment>\n\t      #include <encodings_fragment>\n      }"})}}function wn({opacity:e=1,radiusTop:t,radiusBottom:r,depthBuffer:a,color:i="white",distance:s=5,angle:l=.15,attenuation:c=5,anglePower:u=5}){const m=T.useRef(null),d=o.useThree((e=>e.size)),f=o.useThree((e=>e.camera)),p=o.useThree((e=>e.viewport.dpr)),[h]=T.useState((()=>new gn)),[v]=T.useState((()=>new n.Vector3));t=void 0===t?.1:t,r=void 0===r?7*l:r,o.useFrame((()=>{h.uniforms.spotPosition.value.copy(m.current.getWorldPosition(v)),m.current.lookAt(m.current.parent.target.getWorldPosition(v))}));const x=T.useMemo((()=>{const e=new n.CylinderGeometry(t,r,s,128,64,!0);return e.applyMatrix4((new n.Matrix4).makeTranslation(0,-s/2,0)),e.applyMatrix4((new n.Matrix4).makeRotationX(-Math.PI/2)),e}),[s,t,r]);return T.createElement(T.Fragment,null,T.createElement("mesh",{ref:m,geometry:x,raycast:()=>null},T.createElement("primitive",{object:h,attach:"material","uniforms-opacity-value":e,"uniforms-lightColor-value":i,"uniforms-attenuation-value":c,"uniforms-anglePower-value":u,"uniforms-depth-value":a,"uniforms-cameraNear-value":f.near,"uniforms-cameraFar-value":f.far,"uniforms-resolution-value":a?[d.width*p,d.height*p]:[0,0]})))}function bn(e,t,r,a,i){const[[s,l]]=T.useState((()=>[new n.Vector3,new n.Vector3]));T.useLayoutEffect((()=>{if(!(null==(t=e.current)?void 0:t.isSpotLight))throw new Error("SpotlightShadow must be a child of a SpotLight");var t;e.current.shadow.mapSize.set(r,a),e.current.shadow.needsUpdate=!0}),[e,r,a]),o.useFrame((()=>{if(!e.current)return;const r=e.current.position,n=e.current.target.position;l.copy(n).sub(r);var o=l.length();l.normalize().multiplyScalar(o*i),s.copy(r).add(l),t.current.position.copy(s),t.current.lookAt(e.current.target.position)}))}function En({distance:e=.4,alphaTest:t=.5,map:r,shader:a="#define GLSLIFY 1\nvarying vec2 vUv;uniform sampler2D uShadowMap;uniform float uTime;void main(){vec3 color=texture2D(uShadowMap,vUv).xyz;gl_FragColor=vec4(color,1.);}",width:i=512,height:s=512,scale:l=1,children:c,...u}){const d=T.useRef(null),f=u.spotlightRef,p=u.debug;bn(f,d,i,s,e);const h=T.useMemo((()=>new n.WebGLRenderTarget(i,s,{format:n.RGBAFormat,encoding:n.LinearEncoding,stencilBuffer:!1})),[i,s]),v=T.useRef({uShadowMap:{value:r},uTime:{value:0}});T.useEffect((()=>{v.current.uShadowMap.value=r}),[r]);const x=T.useMemo((()=>new m.FullScreenQuad(new n.ShaderMaterial({uniforms:v.current,vertexShader:"\n          varying vec2 vUv;\n\n          void main() {\n            vUv = uv;\n            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n          }\n          ",fragmentShader:a}))),[a]);return T.useEffect((()=>()=>{x.material.dispose(),x.dispose()}),[x]),T.useEffect((()=>()=>h.dispose()),[h]),o.useFrame((({gl:e},t)=>{v.current.uTime.value+=t,e.setRenderTarget(h),x.render(e),e.setRenderTarget(null)})),T.createElement(T.Fragment,null,T.createElement("mesh",{ref:d,scale:l,castShadow:!0},T.createElement("planeGeometry",null),T.createElement("meshBasicMaterial",{transparent:!0,side:n.DoubleSide,alphaTest:t,alphaMap:h.texture,"alphaMap-wrapS":n.RepeatWrapping,"alphaMap-wrapT":n.RepeatWrapping,opacity:p?1:0},c)))}function Mn({distance:e=.4,alphaTest:t=.5,map:r,width:o=512,height:a=512,scale:i,children:s,...l}){const c=T.useRef(null),u=l.spotlightRef,m=l.debug;return bn(u,c,o,a,e),T.createElement(T.Fragment,null,T.createElement("mesh",{ref:c,scale:i,castShadow:!0},T.createElement("planeGeometry",null),T.createElement("meshBasicMaterial",{transparent:!0,side:n.DoubleSide,alphaTest:t,alphaMap:r,"alphaMap-wrapS":n.RepeatWrapping,"alphaMap-wrapT":n.RepeatWrapping,opacity:m?1:0},s)))}const zn=T.forwardRef((({opacity:e=1,radiusTop:t,radiusBottom:r,depthBuffer:n,color:o="white",distance:a=5,angle:i=.15,attenuation:s=5,anglePower:l=5,volumetric:c=!0,debug:u=!1,children:m,...d},f)=>{const p=T.useRef(null);return T.createElement("group",null,u&&p.current&&T.createElement("spotLightHelper",{args:[p.current]}),T.createElement("spotLight",C.default({ref:F.default([f,p]),angle:i,color:o,distance:a,castShadow:!0},d),c&&T.createElement(wn,{debug:u,opacity:e,radiusTop:t,radiusBottom:r,depthBuffer:n,color:o,distance:a,angle:i,attenuation:s,anglePower:l})),m&&T.cloneElement(m,{spotlightRef:p,debug:u}))})),Sn=T.forwardRef((({args:e,map:t,toneMapped:r=!1,color:n="white",form:a="rect",intensity:i=1,scale:s=1,target:l,children:c,...u},m)=>{const d=T.useRef(null);return T.useLayoutEffect((()=>{c||u.material||(o.applyProps(d.current.material,{color:n}),d.current.material.color.multiplyScalar(i))}),[n,i,c,u.material]),T.useLayoutEffect((()=>{l&&d.current.lookAt(Array.isArray(l)?new R.Vector3(...l):l)}),[l]),s=Array.isArray(s)&&2===s.length?[s[0],s[1],1]:s,T.createElement("mesh",C.default({ref:F.default([d,m]),scale:s},u),"circle"===a?T.createElement("ringGeometry",{args:[0,1,64]}):"ring"===a?T.createElement("ringGeometry",{args:[.5,1,64]}):"rect"===a?T.createElement("planeGeometry",null):T.createElement(a,{args:e}),c||(u.material?null:T.createElement("meshBasicMaterial",{toneMapped:r,map:t,side:R.DoubleSide})))}));function Cn(e,t,r=new n.Vector3){const o=Math.PI*(e-.5),a=2*Math.PI*(t-.5);return r.x=Math.cos(a),r.y=Math.sin(o),r.z=Math.sin(a),r}const Tn=T.forwardRef((({inclination:e=.6,azimuth:t=.1,distance:r=1e3,mieCoefficient:o=.005,mieDirectionalG:a=.8,rayleigh:i=.5,turbidity:s=10,sunPosition:l=Cn(e,t),...c},u)=>{const d=T.useMemo((()=>(new n.Vector3).setScalar(r)),[r]),[f]=T.useState((()=>new m.Sky));return T.createElement("primitive",C.default({object:f,ref:u,"material-uniforms-mieCoefficient-value":o,"material-uniforms-mieDirectionalG-value":a,"material-uniforms-rayleigh-value":i,"material-uniforms-sunPosition-value":l,"material-uniforms-turbidity-value":s,scale:d},c))}));class Pn extends n.ShaderMaterial{constructor(){super({uniforms:{time:{value:0},fade:{value:1}},vertexShader:"\n      uniform float time;\n      attribute float size;\n      varying vec3 vColor;\n      void main() {\n        vColor = color;\n        vec4 mvPosition = modelViewMatrix * vec4(position, 0.5);\n        gl_PointSize = size * (30.0 / -mvPosition.z) * (3.0 + sin(time + 100.0));\n        gl_Position = projectionMatrix * mvPosition;\n      }",fragmentShader:"\n      uniform sampler2D pointTexture;\n      uniform float fade;\n      varying vec3 vColor;\n      void main() {\n        float opacity = 1.0;\n        if (fade == 1.0) {\n          float d = distance(gl_PointCoord, vec2(0.5, 0.5));\n          opacity = 1.0 / (1.0 + exp(16.0 * (d - 0.25)));\n        }\n        gl_FragColor = vec4(vColor, opacity);\n\n        #include <tonemapping_fragment>\n\t      #include <encodings_fragment>\n      }"})}}const Rn=e=>(new n.Vector3).setFromSpherical(new n.Spherical(e,Math.acos(1-2*Math.random()),2*Math.random()*Math.PI)),Dn=T.forwardRef((({radius:e=100,depth:t=50,count:r=5e3,saturation:a=0,factor:i=4,fade:s=!1,speed:l=1},c)=>{const u=T.useRef(),[m,d,f]=T.useMemo((()=>{const o=[],s=[],l=Array.from({length:r},(()=>(.5+.5*Math.random())*i)),c=new n.Color;let u=e+t;const m=t/r;for(let e=0;e<r;e++)u-=m*Math.random(),o.push(...Rn(u).toArray()),c.setHSL(e/r,a,.9),s.push(c.r,c.g,c.b);return[new Float32Array(o),new Float32Array(s),new Float32Array(l)]}),[r,t,i,e,a]);o.useFrame((e=>u.current&&(u.current.uniforms.time.value=e.clock.getElapsedTime()*l)));const[p]=T.useState((()=>new Pn));return T.createElement("points",{ref:c},T.createElement("bufferGeometry",null,T.createElement("bufferAttribute",{attach:"attributes-position",args:[m,3]}),T.createElement("bufferAttribute",{attach:"attributes-color",args:[d,3]}),T.createElement("bufferAttribute",{attach:"attributes-size",args:[f,1]})),T.createElement("primitive",{ref:u,object:p,attach:"material",blending:n.AdditiveBlending,"uniforms-fade-value":s,depthWrite:!1,transparent:!0,vertexColors:!0}))}));const kn=Ee({time:0,pixelRatio:1}," uniform float pixelRatio;\n    uniform float time;\n    attribute float size;  \n    attribute float speed;  \n    attribute float opacity;\n    attribute vec3 noise;\n    attribute vec3 color;\n    varying vec3 vColor;\n    varying float vOpacity;\n    void main() {\n      vec4 modelPosition = modelMatrix * vec4(position, 1.0);\n      modelPosition.y += sin(time * speed + modelPosition.x * noise.x * 100.0) * 0.2;\n      modelPosition.z += cos(time * speed + modelPosition.x * noise.y * 100.0) * 0.2;\n      modelPosition.x += cos(time * speed + modelPosition.x * noise.z * 100.0) * 0.2;\n      vec4 viewPosition = viewMatrix * modelPosition;\n      vec4 projectionPostion = projectionMatrix * viewPosition;\n      gl_Position = projectionPostion;\n      gl_PointSize = size * 25. * pixelRatio;\n      gl_PointSize *= (1.0 / - viewPosition.z);\n      vColor = color;\n      vOpacity = opacity;\n    }"," varying vec3 vColor;\n    varying float vOpacity;\n    void main() {\n      float distanceToCenter = distance(gl_PointCoord, vec2(0.5));\n      float strength = 0.05 / distanceToCenter - 0.1;\n      gl_FragColor = vec4(vColor, strength * vOpacity);\n      #include <tonemapping_fragment>\n      #include <encodings_fragment>\n    }"),Fn=e=>e&&e.constructor===Float32Array,_n=e=>e instanceof R.Vector2||e instanceof R.Vector3||e instanceof R.Vector4,Ln=e=>Array.isArray(e)?e:_n(e)?e.toArray():[e,e,e];function An(e,t,r){return T.useMemo((()=>{if(void 0!==t){if(Fn(t))return t;if(t instanceof R.Color){const r=Array.from({length:3*e},(()=>(e=>[e.r,e.g,e.b])(t))).flat();return Float32Array.from(r)}if(_n(t)||Array.isArray(t)){const r=Array.from({length:3*e},(()=>Ln(t))).flat();return Float32Array.from(r)}return Float32Array.from({length:e},(()=>t))}return Float32Array.from({length:e},r)}),[t])}const Bn=T.forwardRef((({noise:e=1,count:t=100,speed:r=1,opacity:n=1,scale:a=1,size:i,color:s,children:l,...c},u)=>{T.useMemo((()=>o.extend({SparklesImplMaterial:kn})),[]);const m=T.useRef(null),d=o.useThree((e=>e.viewport.dpr)),f=T.useMemo((()=>Float32Array.from(Array.from({length:t},(()=>Ln(a).map(R.MathUtils.randFloatSpread))).flat())),[t,a]),p=An(t,i,Math.random),h=An(t,n),v=An(t,r),x=An(3*t,e),y=An(void 0===s?3*t:t,Fn(s)?s:new R.Color(s),(()=>1));return o.useFrame((e=>{m.current&&m.current.material&&(m.current.material.time=e.clock.elapsedTime)})),T.useImperativeHandle(u,(()=>m.current),[]),T.createElement("points",C.default({key:`particle-${t}-${JSON.stringify(a)}`},c,{ref:m}),T.createElement("bufferGeometry",null,T.createElement("bufferAttribute",{attach:"attributes-position",args:[f,3]}),T.createElement("bufferAttribute",{attach:"attributes-size",args:[p,1]}),T.createElement("bufferAttribute",{attach:"attributes-opacity",args:[h,1]}),T.createElement("bufferAttribute",{attach:"attributes-speed",args:[v,1]}),T.createElement("bufferAttribute",{attach:"attributes-color",args:[y,3]}),T.createElement("bufferAttribute",{attach:"attributes-noise",args:[x,3]})),l||T.createElement("sparklesImplMaterial",{transparent:!0,pixelRatio:d,depthWrite:!1}))}));const On={uniforms:{strokeOpacity:1,fillOpacity:.25,fillMix:0,thickness:.05,colorBackfaces:!1,dashInvert:!0,dash:!1,dashRepeats:4,dashLength:.5,squeeze:!1,squeezeMin:.2,squeezeMax:1,stroke:new R.Color("#ff0000"),backfaceStroke:new R.Color("#0000ff"),fill:new R.Color("#00ff00")},vertex:"\n\t  attribute vec3 barycentric;\n\t\n\t\tvarying vec3 v_edges_Barycentric;\n\t\tvarying vec3 v_edges_Position;\n\n\t\tvoid initWireframe() {\n\t\t\tv_edges_Barycentric = barycentric;\n\t\t\tv_edges_Position = position.xyz;\n\t\t}\n\t  ",fragment:"\n\t\t#ifndef PI\n\t  \t#define PI 3.1415926535897932384626433832795\n\t\t#endif\n  \n\t  varying vec3 v_edges_Barycentric;\n\t  varying vec3 v_edges_Position;\n  \n\t  uniform float strokeOpacity;\n\t  uniform float fillOpacity;\n\t  uniform float fillMix;\n\t  uniform float thickness;\n\t  uniform bool colorBackfaces;\n  \n\t  // Dash\n\t  uniform bool dashInvert;\n\t  uniform bool dash;\n\t  uniform bool dashOnly;\n\t  uniform float dashRepeats;\n\t  uniform float dashLength;\n  \n\t  // Squeeze\n\t  uniform bool squeeze;\n\t  uniform float squeezeMin;\n\t  uniform float squeezeMax;\n  \n\t  // Colors\n\t  uniform vec3 stroke;\n\t  uniform vec3 backfaceStroke;\n\t  uniform vec3 fill;\n  \n\t  // This is like\n\t  float wireframe_aastep(float threshold, float dist) {\n\t\t  float afwidth = fwidth(dist) * 0.5;\n\t\t  return smoothstep(threshold - afwidth, threshold + afwidth, dist);\n\t  }\n  \n\t  float wireframe_map(float value, float min1, float max1, float min2, float max2) {\n\t\t  return min2 + (value - min1) * (max2 - min2) / (max1 - min1);\n\t  }\n  \n\t  float getWireframe() {\n\t\t\tvec3 barycentric = v_edges_Barycentric;\n\t\t\n\t\t\t// Distance from center of each triangle to its edges.\n\t\t\tfloat d = min(min(barycentric.x, barycentric.y), barycentric.z);\n\n\t\t\t// for dashed rendering, we can use this to get the 0 .. 1 value of the line length\n\t\t\tfloat positionAlong = max(barycentric.x, barycentric.y);\n\t\t\tif (barycentric.y < barycentric.x && barycentric.y < barycentric.z) {\n\t\t\t\tpositionAlong = 1.0 - positionAlong;\n\t\t\t}\n\n\t\t\t// the thickness of the stroke\n\t\t\tfloat computedThickness = wireframe_map(thickness, 0.0, 1.0, 0.0, 0.34);\n\n\t\t\t// if we want to shrink the thickness toward the center of the line segment\n\t\t\tif (squeeze) {\n\t\t\t\tcomputedThickness *= mix(squeezeMin, squeezeMax, (1.0 - sin(positionAlong * PI)));\n\t\t\t}\n\n\t\t\t// Create dash pattern\n\t\t\tif (dash) {\n\t\t\t\t// here we offset the stroke position depending on whether it\n\t\t\t\t// should overlap or not\n\t\t\t\tfloat offset = 1.0 / dashRepeats * dashLength / 2.0;\n\t\t\t\tif (!dashInvert) {\n\t\t\t\t\toffset += 1.0 / dashRepeats / 2.0;\n\t\t\t\t}\n\n\t\t\t\t// if we should animate the dash or not\n\t\t\t\t// if (dashAnimate) {\n\t\t\t\t// \toffset += time * 0.22;\n\t\t\t\t// }\n\n\t\t\t\t// create the repeating dash pattern\n\t\t\t\tfloat pattern = fract((positionAlong + offset) * dashRepeats);\n\t\t\t\tcomputedThickness *= 1.0 - wireframe_aastep(dashLength, pattern);\n\t\t\t}\n\n\t\t\t// compute the anti-aliased stroke edge  \n\t\t\tfloat edge = 1.0 - wireframe_aastep(computedThickness, d);\n\n\t\t\treturn edge;\n\t  }\n\t  "},In=Ee(On.uniforms,On.vertex+"\n  \tvoid main() {\n\t\tinitWireframe();\n\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n\t}\n  ",On.fragment+"\n  void main () {\n\t\t// Compute color\n\n\t\tfloat edge = getWireframe();\n\t\tvec4 colorStroke = vec4(stroke, edge);\n\n\t\t#ifdef FLIP_SIDED\n\t\t\tcolorStroke.rgb = backfaceStroke;\n\t\t#endif\n    \n\t\tvec4 colorFill = vec4(fill, fillOpacity);\n\t\tvec4 outColor = mix(colorFill, colorStroke, edge * strokeOpacity);\n\n\t\tgl_FragColor = outColor;\n\t}\n  ");function Un(e){return void 0!==(null==e?void 0:e.current)}function Vn(e){return"WireframeGeometry"===e.type}function jn(e){const t=null!=(r=e)&&r.current?e.current:e;var r;if(function(e){return!(null==e||!e.isBufferGeometry)}(t))return t;{if(Vn(t))throw new Error("Wireframe: WireframeGeometry is not supported.");const e=t.parent;if(function(e){return!(null==e||!e.geometry)}(e)){if(Vn(e.geometry))throw new Error("Wireframe: WireframeGeometry is not supported.");return e.geometry}}}function Wn(e,t){if(e.index){console.warn("Wireframe: Requires non-indexed geometry, converting to non-indexed geometry.");const t=e.toNonIndexed();e.copy(t),e.setIndex(null)}const r=function(e,t){const r=e.getAttribute("position").count,n=[];for(let e=0;e<r;e++){const r=t?1:0;e%2==0?n.push(0,0,1,0,1,0,1,0,r):n.push(0,1,0,0,0,1,1,0,r)}return new R.BufferAttribute(Float32Array.from(n),3)}(e,t);e.setAttribute("barycentric",r)}function Nn({geometry:e,simplify:t=!1,...r}){const[n,o]=T.useState(null);T.useLayoutEffect((()=>{const r=jn(e);if(!r)throw new Error("Wireframe: geometry prop must be a BufferGeometry or a ref to a BufferGeometry.");Wn(r,t),Un(e)&&o(r)}),[t,e]);const a=Un(e)?n:e;return T.createElement(T.Fragment,null,a&&T.createElement("mesh",{geometry:a},T.createElement("meshWireframeMaterial",C.default({attach:"material",transparent:!0,side:R.DoubleSide,polygonOffset:!0,polygonOffsetFactor:-4},r,{extensions:{derivatives:!0,fragDepth:!1,drawBuffers:!1,shaderTextureLOD:!1}}))))}function Gn({simplify:e=!1,...t}){const r=T.useRef(null),n=T.useMemo((()=>function(){const e={};for(const t in On.uniforms)e[t]={value:On.uniforms[t]};return e}()),[On.uniforms]);return function(e,t){T.useEffect((()=>{var r;e.fillOpacity.value=null!==(r=t.fillOpacity)&&void 0!==r?r:e.fillOpacity.value}),[t.fillOpacity]),T.useEffect((()=>{var r;e.fillMix.value=null!==(r=t.fillMix)&&void 0!==r?r:e.fillMix.value}),[t.fillMix]),T.useEffect((()=>{var r;e.strokeOpacity.value=null!==(r=t.strokeOpacity)&&void 0!==r?r:e.strokeOpacity.value}),[t.strokeOpacity]),T.useEffect((()=>{var r;e.thickness.value=null!==(r=t.thickness)&&void 0!==r?r:e.thickness.value}),[t.thickness]),T.useEffect((()=>{e.colorBackfaces.value=!!t.colorBackfaces}),[t.colorBackfaces]),T.useEffect((()=>{e.dash.value=!!t.dash}),[t.dash]),T.useEffect((()=>{e.dashInvert.value=!!t.dashInvert}),[t.dashInvert]),T.useEffect((()=>{var r;e.dashRepeats.value=null!==(r=t.dashRepeats)&&void 0!==r?r:e.dashRepeats.value}),[t.dashRepeats]),T.useEffect((()=>{var r;e.dashLength.value=null!==(r=t.dashLength)&&void 0!==r?r:e.dashLength.value}),[t.dashLength]),T.useEffect((()=>{e.squeeze.value=!!t.squeeze}),[t.squeeze]),T.useEffect((()=>{var r;e.squeezeMin.value=null!==(r=t.squeezeMin)&&void 0!==r?r:e.squeezeMin.value}),[t.squeezeMin]),T.useEffect((()=>{var r;e.squeezeMax.value=null!==(r=t.squeezeMax)&&void 0!==r?r:e.squeezeMax.value}),[t.squeezeMax]),T.useEffect((()=>{e.stroke.value=t.stroke?new R.Color(t.stroke):e.stroke.value}),[t.stroke]),T.useEffect((()=>{e.fill.value=t.fill?new R.Color(t.fill):e.fill.value}),[t.fill]),T.useEffect((()=>{e.backfaceStroke.value=t.backfaceStroke?new R.Color(t.backfaceStroke):e.backfaceStroke.value}),[t.backfaceStroke])}(n,t),T.useLayoutEffect((()=>{const t=jn(r);if(!t)throw new Error("Wireframe: Must be a child of a Mesh, Line or Points object or specify a geometry prop.");const n=t.clone();return Wn(t,e),()=>{t.copy(n),n.dispose()}}),[e]),T.useLayoutEffect((()=>{const e=r.current.parent,t=e.material.clone();return function(e,t){e.onBeforeCompile=e=>{e.uniforms={...e.uniforms,...t},e.vertexShader=e.vertexShader.replace("void main() {",`\n\t\t  ${On.vertex}\n\t\t  void main() {\n\t\t\tinitWireframe();\n\t\t`),e.fragmentShader=e.fragmentShader.replace("void main() {",`\n\t\t  ${On.fragment}\n\t\t  void main() {\n\t\t`),e.fragmentShader=e.fragmentShader.replace("#include <color_fragment>","\n\t\t  #include <color_fragment>\n\t\t\t  float edge = getWireframe();\n\t\t  vec4 colorStroke = vec4(stroke, edge);\n\t\t  #ifdef FLIP_SIDED\n\t\t\tcolorStroke.rgb = backfaceStroke;\n\t\t  #endif\n\t\t  vec4 colorFill = vec4(mix(diffuseColor.rgb, fill, fillMix), mix(diffuseColor.a, fillOpacity, fillMix));\n\t\t  vec4 outColor = mix(colorFill, colorStroke, edge * strokeOpacity);\n\n\t\t  diffuseColor.rgb = outColor.rgb;\n\t\t  diffuseColor.a *= outColor.a;\n\t\t")},e.side=R.DoubleSide,e.transparent=!0}(e.material,n),()=>{e.material.dispose(),e.material=t}}),[]),T.createElement("object3D",{ref:r})}D.extend({MeshWireframeMaterial:In});const Hn=new R.Matrix4,$n=new R.Ray,qn=new R.Sphere,Xn=new R.Vector3;class Yn extends R.Group{constructor(){super(),this.size=0,this.color=new R.Color("white"),this.instance={current:void 0},this.instanceKey={current:void 0}}get geometry(){var e;return null==(e=this.instance.current)?void 0:e.geometry}raycast(e,t){var r,n;const o=this.instance.current;if(!o||!o.geometry)return;const a=o.userData.instances.indexOf(this.instanceKey);if(-1===a||a>o.geometry.drawRange.count)return;const i=null!==(r=null==(n=e.params.Points)?void 0:n.threshold)&&void 0!==r?r:1;if(qn.set(this.getWorldPosition(Xn),i),!1===e.ray.intersectsSphere(qn))return;Hn.copy(o.matrixWorld).invert(),$n.copy(e.ray).applyMatrix4(Hn);const s=i/((this.scale.x+this.scale.y+this.scale.z)/3),l=s*s,c=$n.distanceSqToPoint(this.position);if(c<l){const r=new R.Vector3;$n.closestPointToPoint(this.position,r),r.applyMatrix4(this.matrixWorld);const n=e.ray.origin.distanceTo(r);if(n<e.near||n>e.far)return;t.push({distance:n,distanceToRay:Math.sqrt(c),point:r,index:a,face:null,object:this})}}}let Kn,Zn;const Qn=T.createContext(null),Jn=new R.Matrix4,eo=new R.Vector3,to=T.forwardRef((({children:e,range:t,limit:r=1e3,...n},a)=>{const i=T.useRef(null),[s,l]=T.useState([]),[[c,u,m]]=T.useState((()=>[new Float32Array(3*r),Float32Array.from({length:3*r},(()=>1)),Float32Array.from({length:r},(()=>1))]));T.useEffect((()=>{i.current.geometry.attributes.position.needsUpdate=!0})),o.useFrame((()=>{for(i.current.updateMatrix(),i.current.updateMatrixWorld(),Jn.copy(i.current.matrixWorld).invert(),i.current.geometry.drawRange.count=Math.min(r,void 0!==t?t:r,s.length),Kn=0;Kn<s.length;Kn++)Zn=s[Kn].current,Zn.getWorldPosition(eo).applyMatrix4(Jn),eo.toArray(c,3*Kn),i.current.geometry.attributes.position.needsUpdate=!0,Zn.matrixWorldNeedsUpdate=!0,Zn.color.toArray(u,3*Kn),i.current.geometry.attributes.color.needsUpdate=!0,m.set([Zn.size],Kn),i.current.geometry.attributes.size.needsUpdate=!0}));const d=T.useMemo((()=>({getParent:()=>i,subscribe:e=>(l((t=>[...t,e])),()=>l((t=>t.filter((t=>t.current!==e.current)))))})),[]);return T.createElement("points",C.default({userData:{instances:s},matrixAutoUpdate:!1,ref:F.default([a,i]),raycast:()=>null},n),T.createElement("bufferGeometry",null,T.createElement("bufferAttribute",{attach:"attributes-position",count:c.length/3,array:c,itemSize:3,usage:R.DynamicDrawUsage}),T.createElement("bufferAttribute",{attach:"attributes-color",count:u.length/3,array:u,itemSize:3,usage:R.DynamicDrawUsage}),T.createElement("bufferAttribute",{attach:"attributes-size",count:m.length,array:m,itemSize:1,usage:R.DynamicDrawUsage})),T.createElement(Qn.Provider,{value:d},e))})),ro=T.forwardRef((({children:e,...t},r)=>{T.useMemo((()=>o.extend({PositionPoint:Yn})),[]);const n=T.useRef(),{subscribe:a,getParent:i}=T.useContext(Qn);return T.useLayoutEffect((()=>a(n)),[]),T.createElement("positionPoint",C.default({instance:i(),instanceKey:n,ref:F.default([r,n])},t),e)})),no=T.forwardRef((({children:e,positions:t,colors:r,sizes:n,stride:a=3,...i},s)=>{const l=T.useRef(null);return o.useFrame((()=>{const e=l.current.geometry.attributes;e.position.needsUpdate=!0,r&&(e.color.needsUpdate=!0),n&&(e.size.needsUpdate=!0)})),T.createElement("points",C.default({ref:F.default([s,l])},i),T.createElement("bufferGeometry",null,T.createElement("bufferAttribute",{attach:"attributes-position",count:t.length/a,array:t,itemSize:a,usage:R.DynamicDrawUsage}),r&&T.createElement("bufferAttribute",{attach:"attributes-color",count:r.length/a,array:r,itemSize:3,usage:R.DynamicDrawUsage}),n&&T.createElement("bufferAttribute",{attach:"attributes-size",count:n.length/a,array:n,itemSize:1,usage:R.DynamicDrawUsage})),e)})),oo=T.forwardRef(((e,t)=>e.positions instanceof Float32Array?T.createElement(no,C.default({},e,{ref:t})):T.createElement(to,C.default({},e,{ref:t})))),ao=new R.Matrix4,io=new R.Matrix4,so=[],lo=new R.Mesh;class co extends R.Group{constructor(){super(),this.color=new R.Color("white"),this.instance={current:void 0},this.instanceKey={current:void 0}}get geometry(){var e;return null==(e=this.instance.current)?void 0:e.geometry}raycast(e,t){const r=this.instance.current;if(!r)return;if(!r.geometry||!r.material)return;lo.geometry=r.geometry;const n=r.matrixWorld,o=r.userData.instances.indexOf(this.instanceKey);if(!(-1===o||o>r.count)){r.getMatrixAt(o,ao),io.multiplyMatrices(n,ao),lo.matrixWorld=io,r.material instanceof R.Material?lo.material.side=r.material.side:lo.material.side=r.material[0].side,lo.raycast(e,so);for(let e=0,r=so.length;e<r;e++){const r=so[e];r.instanceId=o,r.object=this,t.push(r)}so.length=0}}}const uo=T.createContext(null),mo=new R.Matrix4,fo=new R.Matrix4,po=new R.Matrix4,ho=new R.Vector3,vo=new R.Quaternion,xo=new R.Vector3,yo=T.forwardRef((({context:e,children:t,...r},n)=>{T.useMemo((()=>o.extend({PositionMesh:co})),[]);const a=T.useRef(),{subscribe:i,getParent:s}=T.useContext(e||uo);return T.useLayoutEffect((()=>i(a)),[]),T.createElement("positionMesh",C.default({instance:s(),instanceKey:a,ref:F.default([n,a])},r),t)})),go=T.forwardRef((({children:e,range:t,limit:r=1e3,frames:n=1/0,...a},i)=>{const[{context:s,instance:l}]=T.useState((()=>{const e=T.createContext(null);return{context:e,instance:T.forwardRef(((t,r)=>T.createElement(yo,C.default({context:e},t,{ref:r}))))}})),c=T.useRef(null),[u,m]=T.useState([]),[[d,f]]=T.useState((()=>{const e=new Float32Array(16*r);for(let t=0;t<r;t++)po.identity().toArray(e,16*t);return[e,new Float32Array([...new Array(3*r)].map((()=>1)))]}));T.useEffect((()=>{c.current.instanceMatrix.needsUpdate=!0}));let p=0,h=0;o.useFrame((()=>{if(n===1/0||p<n){c.current.updateMatrix(),c.current.updateMatrixWorld(),mo.copy(c.current.matrixWorld).invert(),h=Math.min(r,void 0!==t?t:r,u.length),c.current.count=h,c.current.instanceMatrix.updateRange.count=16*h,c.current.instanceColor.updateRange.count=3*h;for(let e=0;e<u.length;e++){const t=u[e].current;t.matrixWorld.decompose(ho,vo,xo),fo.compose(ho,vo,xo).premultiply(mo),fo.toArray(d,16*e),c.current.instanceMatrix.needsUpdate=!0,t.color.toArray(f,3*e),c.current.instanceColor.needsUpdate=!0}p++}}));const v=T.useMemo((()=>({getParent:()=>c,subscribe:e=>(m((t=>[...t,e])),()=>m((t=>t.filter((t=>t.current!==e.current)))))})),[]);return T.createElement("instancedMesh",C.default({userData:{instances:u},matrixAutoUpdate:!1,ref:F.default([i,c]),args:[null,null,0],raycast:()=>null},a),T.createElement("instancedBufferAttribute",{attach:"instanceMatrix",count:d.length/16,array:d,itemSize:16,usage:R.DynamicDrawUsage}),T.createElement("instancedBufferAttribute",{attach:"instanceColor",count:f.length/3,array:f,itemSize:3,usage:R.DynamicDrawUsage}),"function"==typeof e?T.createElement(s.Provider,{value:v},e(l)):T.createElement(uo.Provider,{value:v},e))})),wo=T.forwardRef((function({meshes:e,children:t,...r},n){const o=Array.isArray(e);if(!o)for(const t of Object.keys(e))e[t].isMesh||delete e[t];return T.createElement("group",{ref:n},T.createElement(I.default,{components:(o?e:Object.values(e)).map((({geometry:e,material:t})=>T.createElement(go,C.default({key:e.uuid,geometry:e,material:t},r))))},(r=>o?t(...r):t(Object.keys(e).filter((t=>e[t].isMesh)).reduce(((e,t,n)=>({...e,[t]:r[n]})),{})))))})),bo=T.createContext(null),Eo=T.forwardRef(((e,t)=>{T.useMemo((()=>o.extend({SegmentObject:Mo})),[]);const{limit:r=1e3,lineWidth:n=1,children:a,...i}=e,[s,l]=T.useState([]),[c]=T.useState((()=>new m.Line2)),[u]=T.useState((()=>new m.LineMaterial)),[d]=T.useState((()=>new m.LineSegmentsGeometry)),[f]=T.useState((()=>new R.Vector2(512,512))),[p]=T.useState((()=>Array(6*r).fill(0))),[h]=T.useState((()=>Array(6*r).fill(0))),v=T.useMemo((()=>({subscribe:e=>(l((t=>[...t,e])),()=>l((t=>t.filter((t=>t.current!==e.current)))))})),[]);return o.useFrame((()=>{for(let t=0;t<r;t++){var e;const r=null==(e=s[t])?void 0:e.current;r&&(p[6*t+0]=r.start.x,p[6*t+1]=r.start.y,p[6*t+2]=r.start.z,p[6*t+3]=r.end.x,p[6*t+4]=r.end.y,p[6*t+5]=r.end.z,h[6*t+0]=r.color.r,h[6*t+1]=r.color.g,h[6*t+2]=r.color.b,h[6*t+3]=r.color.r,h[6*t+4]=r.color.g,h[6*t+5]=r.color.b)}d.setColors(h),d.setPositions(p),c.computeLineDistances()})),T.createElement("primitive",{object:c,ref:t},T.createElement("primitive",{object:d,attach:"geometry"}),T.createElement("primitive",C.default({object:u,attach:"material",vertexColors:!0,resolution:f,linewidth:n},i)),T.createElement(bo.Provider,{value:v},a))}));class Mo{constructor(){this.color=new R.Color("white"),this.start=new R.Vector3(0,0,0),this.end=new R.Vector3(0,0,0)}}const zo=e=>e instanceof R.Vector3?e:new R.Vector3(..."number"==typeof e?[e,e,e]:e),So=T.forwardRef((({color:e,start:t,end:r},n)=>{const o=T.useContext(bo);if(!o)throw"Segment must used inside Segments component.";const a=T.useRef(null);return T.useLayoutEffect((()=>o.subscribe(a)),[]),T.createElement("segmentObject",{ref:F.default([a,n]),color:e,start:zo(t),end:zo(r)})})),Co=T.forwardRef((({children:e,hysteresis:t=0,distances:r,...n},a)=>{const i=T.useRef(null);return T.useLayoutEffect((()=>{const{current:e}=i;e.levels.length=0,e.children.forEach(((n,o)=>e.levels.push({object:n,hysteresis:t,distance:r[o]})))})),o.useFrame((e=>{var t;return null==(t=i.current)?void 0:t.update(e.camera)})),T.createElement("lOD",C.default({ref:F.default([i,a])},n),e)}));const To=new n.Matrix4,Po=new n.Ray,Ro=new n.Sphere,Do=new n.Vector3;const ko=t.createContext(null);const Fo=T.forwardRef((({children:e,compute:t,width:r,height:n,samples:a=8,renderPriority:i=0,eventPriority:s=0,frames:l=1/0,stencilBuffer:c=!1,depthBuffer:u=!0,generateMipmaps:m=!1,...d},f)=>{const{size:p,viewport:h}=o.useThree(),v=Xe((r||p.width)*h.dpr,(n||p.height)*h.dpr,{samples:a,stencilBuffer:c,depthBuffer:u,generateMipmaps:m}),[x]=T.useState((()=>new R.Scene)),y=T.useCallback(((e,t,r)=>{var n,o;let a=null==(n=v.texture)?void 0:n.__r3f.parent;for(;a&&!(a instanceof R.Object3D);)a=a.__r3f.parent;if(!a)return!1;r.raycaster.camera||r.events.compute(e,r,null==(o=r.previousRoot)?void 0:o.getState());const[i]=r.raycaster.intersectObject(a);if(!i)return!1;const s=i.uv;if(!s)return!1;t.raycaster.setFromCamera(t.pointer.set(2*s.x-1,2*s.y-1),t.camera)}),[]);return T.useImperativeHandle(f,(()=>v.texture),[v]),T.createElement(T.Fragment,null,o.createPortal(T.createElement(_o,{renderPriority:i,frames:l,fbo:v},e),x,{events:{compute:t||y,priority:s}}),T.createElement("primitive",C.default({object:v.texture},d)))}));function _o({frames:e,renderPriority:t,children:r,fbo:n}){let a=0;return o.useFrame((t=>{(e===1/0||a<e)&&(t.gl.setRenderTarget(n),t.gl.render(t.scene,t.camera),t.gl.setRenderTarget(null),a++)}),t),T.createElement(T.Fragment,null,r)}const Lo=T.forwardRef((({id:e=1,colorWrite:t=!1,depthWrite:r=!1,...n},o)=>{const a=T.useRef(null),i=T.useMemo((()=>({colorWrite:t,depthWrite:r,stencilWrite:!0,stencilRef:e,stencilFunc:R.AlwaysStencilFunc,stencilFail:R.ReplaceStencilOp,stencilZFail:R.ReplaceStencilOp,stencilZPass:R.ReplaceStencilOp})),[e,t,r]);return T.useLayoutEffect((()=>{Object.assign(a.current.material,i)})),T.useImperativeHandle(o,(()=>a.current),[]),T.createElement("mesh",C.default({ref:a,renderOrder:-e},n))}));const Ao=new R.Color;function Bo(e){return"top"in e}function Oo({canvasSize:e,scene:t,index:r,children:n,frames:a,rect:i,track:s}){const l=o.useThree((e=>e.get)),c=o.useThree((e=>e.camera)),u=o.useThree((e=>e.scene)),m=o.useThree((e=>e.setEvents));let d=0;return o.useFrame((r=>{var o,l;(a===1/0||d<=a)&&(i.current=null==(o=s.current)?void 0:o.getBoundingClientRect(),d++);if(i.current){const{position:{left:o,bottom:a,width:s,height:m},isOffscreen:d}=function(e,t){const{right:r,top:n,left:o,bottom:a,width:i,height:s}=t,l=t.bottom<0||n>e.height||r<0||t.left>e.width;if(Bo(e)){const t=e.top+e.height-a;return{position:{width:i,height:s,left:o-e.left,top:n,bottom:t,right:r},isOffscreen:l}}return{position:{width:i,height:s,top:n,left:o,bottom:e.height-a,right:r},isOffscreen:l}}(e,i.current),f=s/m;(l=c)&&l.isOrthographicCamera?c.left===s/-2&&c.right===s/2&&c.top===m/2&&c.bottom===m/-2||(Object.assign(c,{left:s/-2,right:s/2,top:m/2,bottom:m/-2}),c.updateProjectionMatrix()):c.aspect!==f&&(c.aspect=f,c.updateProjectionMatrix()),r.gl.setViewport(o,a,s,m),r.gl.setScissor(o,a,s,m),r.gl.setScissorTest(!0),d?(r.gl.getClearColor(Ao),r.gl.setClearColor(Ao,r.gl.getClearAlpha()),r.gl.clear(!0,!0)):r.gl.render(n?u:t,c),r.gl.setScissorTest(!0)}}),r),T.useEffect((()=>{const e=l().events.connected;return m({connected:s.current}),()=>m({connected:e})}),[]),T.useEffect((()=>{Bo(e)||console.warn("Detected @react-three/fiber canvas size does not include position information. <View /> may not work as expected. Upgrade to @react-three/fiber ^8.1.0 for support.\n See https://github.com/pmndrs/drei/issues/944")}),[]),T.createElement(T.Fragment,null,n)}const Io=T.createContext(null),Uo=new R.Vector3,Vo=new R.Vector3,jo=new R.Vector3(0,1,0),Wo=new R.Matrix4,No=({direction:e,axis:t})=>{const{translation:r,translationLimits:n,annotations:a,annotationsClass:i,depthTest:s,scale:l,lineWidth:c,fixed:u,axisColors:m,hoveredColor:d,opacity:f,onDragStart:p,onDrag:h,onDragEnd:v,userData:x}=T.useContext(Io),y=o.useThree((e=>e.controls)),g=T.useRef(null),w=T.useRef(null),b=T.useRef(null),E=T.useRef(0),[M,z]=T.useState(!1),S=T.useCallback((n=>{a&&(g.current.innerText=`${r.current[t].toFixed(2)}`,g.current.style.display="block"),n.stopPropagation();const o=(new R.Matrix4).extractRotation(w.current.matrixWorld),i=n.point.clone(),s=(new R.Vector3).setFromMatrixPosition(w.current.matrixWorld),l=e.clone().applyMatrix4(o).normalize();b.current={clickPoint:i,dir:l},E.current=r.current[t],p({component:"Arrow",axis:t,origin:s,directions:[l]}),y&&(y.enabled=!1),n.target.setPointerCapture(n.pointerId)}),[a,e,y,p,r,t]),C=T.useCallback((e=>{if(e.stopPropagation(),M||z(!0),b.current){const{clickPoint:o,dir:i}=b.current,[s,l]=(null==n?void 0:n[t])||[void 0,void 0];let c=((e,t,r,n)=>{const o=t.dot(t),a=t.dot(e)-t.dot(r),i=t.dot(n);return 0===i?-a/o:(Uo.copy(n).multiplyScalar(o/i).sub(t),Vo.copy(n).multiplyScalar(a/i).add(r).sub(e),-Uo.dot(Vo)/Uo.dot(Uo))})(o,i,e.ray.origin,e.ray.direction);void 0!==s&&(c=Math.max(c,s-E.current)),void 0!==l&&(c=Math.min(c,l-E.current)),r.current[t]=E.current+c,a&&(g.current.innerText=`${r.current[t].toFixed(2)}`),Wo.makeTranslation(i.x*c,i.y*c,i.z*c),h(Wo)}}),[a,h,M,r,n,t]),P=T.useCallback((e=>{a&&(g.current.style.display="none"),e.stopPropagation(),b.current=null,v(),y&&(y.enabled=!0),e.target.releasePointerCapture(e.pointerId)}),[a,y,v]),D=T.useCallback((e=>{e.stopPropagation(),z(!1)}),[]),{cylinderLength:k,coneWidth:F,coneLength:_,matrixL:L}=T.useMemo((()=>{const t=u?c/l*1.6:l/20,r=u?.2:l/5,n=u?1-r:l-r,o=(new R.Quaternion).setFromUnitVectors(jo,e.clone().normalize());return{cylinderLength:n,coneWidth:t,coneLength:r,matrixL:(new R.Matrix4).makeRotationFromQuaternion(o)}}),[e,l,c,u]),A=M?d:m[t];return T.createElement("group",{ref:w},T.createElement("group",{matrix:L,matrixAutoUpdate:!1,onPointerDown:S,onPointerMove:C,onPointerUp:P,onPointerOut:D},a&&T.createElement(Y,{position:[0,-_,0]},T.createElement("div",{style:{display:"none",background:"#151520",color:"white",padding:"6px 8px",borderRadius:7,whiteSpace:"nowrap"},className:i,ref:g})),T.createElement("mesh",{visible:!1,position:[0,(k+_)/2,0],userData:x},T.createElement("cylinderGeometry",{args:[1.4*F,1.4*F,k+_,8,1]})),T.createElement(ce,{transparent:!0,raycast:()=>null,depthTest:s,points:[0,0,0,0,k,0],lineWidth:c,color:A,opacity:f,polygonOffset:!0,renderOrder:1,polygonOffsetFactor:-10,fog:!1}),T.createElement("mesh",{raycast:()=>null,position:[0,k+_/2,0],renderOrder:500},T.createElement("coneGeometry",{args:[F,_,24,1]}),T.createElement("meshBasicMaterial",{transparent:!0,depthTest:s,color:A,opacity:f,polygonOffset:!0,polygonOffsetFactor:-10,fog:!1}))))},Go=new R.Ray,Ho=new R.Vector3,$o=new R.Matrix4,qo=({dir1:e,dir2:t,axis:r})=>{const{translation:n,translationLimits:a,annotations:i,annotationsClass:s,depthTest:l,scale:c,lineWidth:u,fixed:m,axisColors:d,hoveredColor:f,opacity:p,onDragStart:h,onDrag:v,onDragEnd:x,userData:y}=T.useContext(Io),g=o.useThree((e=>e.controls)),w=T.useRef(null),b=T.useRef(null),E=T.useRef(null),M=T.useRef(0),z=T.useRef(0),[S,C]=T.useState(!1),P=T.useCallback((e=>{i&&(w.current.innerText=`${n.current[(r+1)%3].toFixed(2)}, ${n.current[(r+2)%3].toFixed(2)}`,w.current.style.display="block"),e.stopPropagation();const t=e.point.clone(),o=(new R.Vector3).setFromMatrixPosition(b.current.matrixWorld),a=(new R.Vector3).setFromMatrixColumn(b.current.matrixWorld,0).normalize(),s=(new R.Vector3).setFromMatrixColumn(b.current.matrixWorld,1).normalize(),l=(new R.Vector3).setFromMatrixColumn(b.current.matrixWorld,2).normalize(),c=(new R.Plane).setFromNormalAndCoplanarPoint(l,o);E.current={clickPoint:t,e1:a,e2:s,plane:c},M.current=n.current[(r+1)%3],z.current=n.current[(r+2)%3],h({component:"Slider",axis:r,origin:o,directions:[a,s,l]}),g&&(g.enabled=!1),e.target.setPointerCapture(e.pointerId)}),[i,g,h,r]),D=T.useCallback((e=>{if(e.stopPropagation(),S||C(!0),E.current){const{clickPoint:t,e1:o,e2:s,plane:l}=E.current,[c,u]=(null==a?void 0:a[(r+1)%3])||[void 0,void 0],[m,d]=(null==a?void 0:a[(r+2)%3])||[void 0,void 0];Go.copy(e.ray),Go.intersectPlane(l,Ho),Go.direction.negate(),Go.intersectPlane(l,Ho),Ho.sub(t);let[f,p]=((e,t,r)=>{const n=Math.abs(e.x)>=Math.abs(e.y)&&Math.abs(e.x)>=Math.abs(e.z)?0:Math.abs(e.y)>=Math.abs(e.x)&&Math.abs(e.y)>=Math.abs(e.z)?1:2,o=[0,1,2].sort(((e,r)=>Math.abs(t.getComponent(r))-Math.abs(t.getComponent(e)))),a=n===o[0]?o[1]:o[0],i=e.getComponent(n),s=e.getComponent(a),l=t.getComponent(n),c=t.getComponent(a),u=r.getComponent(n),m=(r.getComponent(a)-u*(s/i))/(c-l*(s/i));return[(u-m*l)/i,m]})(o,s,Ho);void 0!==c&&(f=Math.max(f,c-M.current)),void 0!==u&&(f=Math.min(f,u-M.current)),void 0!==m&&(p=Math.max(p,m-z.current)),void 0!==d&&(p=Math.min(p,d-z.current)),n.current[(r+1)%3]=M.current+f,n.current[(r+2)%3]=z.current+p,i&&(w.current.innerText=`${n.current[(r+1)%3].toFixed(2)}, ${n.current[(r+2)%3].toFixed(2)}`),$o.makeTranslation(f*o.x+p*s.x,f*o.y+p*s.y,f*o.z+p*s.z),v($o)}}),[i,v,S,n,a,r]),k=T.useCallback((e=>{i&&(w.current.style.display="none"),e.stopPropagation(),E.current=null,x(),g&&(g.enabled=!0),e.target.releasePointerCapture(e.pointerId)}),[i,g,x]),F=T.useCallback((e=>{e.stopPropagation(),C(!1)}),[]),_=T.useMemo((()=>{const r=e.clone().normalize(),n=t.clone().normalize();return(new R.Matrix4).makeBasis(r,n,r.clone().cross(n))}),[e,t]),L=m?1/7:c/7,A=m?.225:.225*c,B=S?f:d[r],O=T.useMemo((()=>[new R.Vector3(0,0,0),new R.Vector3(0,A,0),new R.Vector3(A,A,0),new R.Vector3(A,0,0),new R.Vector3(0,0,0)]),[A]);return T.createElement("group",{ref:b,matrix:_,matrixAutoUpdate:!1},i&&T.createElement(Y,{position:[0,0,0]},T.createElement("div",{style:{display:"none",background:"#151520",color:"white",padding:"6px 8px",borderRadius:7,whiteSpace:"nowrap"},className:s,ref:w})),T.createElement("group",{position:[1.7*L,1.7*L,0]},T.createElement("mesh",{visible:!0,onPointerDown:P,onPointerMove:D,onPointerUp:k,onPointerOut:F,scale:A,userData:y},T.createElement("planeGeometry",null),T.createElement("meshBasicMaterial",{transparent:!0,depthTest:l,color:B,polygonOffset:!0,polygonOffsetFactor:-10,side:R.DoubleSide,fog:!1})),T.createElement(ce,{position:[-A/2,-A/2,0],transparent:!0,depthTest:l,points:O,lineWidth:u,color:B,opacity:p,polygonOffset:!0,polygonOffsetFactor:-10,userData:y,fog:!1})))},Xo=new R.Vector3,Yo=new R.Vector3,Ko=e=>180*e/Math.PI,Zo=e=>{let t=((e,t)=>{let r=Math.floor(e/t);return r=r<0?r+1:r,e-r*t})(e,2*Math.PI);return Math.abs(t)<1e-6?0:(t<0&&(t+=2*Math.PI),t)},Qo=new R.Matrix4,Jo=new R.Vector3,ea=new R.Ray,ta=new R.Vector3,ra=({dir1:e,dir2:t,axis:r})=>{const{rotationLimits:n,annotations:a,annotationsClass:i,depthTest:s,scale:l,lineWidth:c,fixed:u,axisColors:m,hoveredColor:d,opacity:f,onDragStart:p,onDrag:h,onDragEnd:v,userData:x}=T.useContext(Io),y=o.useThree((e=>e.controls)),g=T.useRef(null),w=T.useRef(null),b=T.useRef(0),E=T.useRef(0),M=T.useRef(null),[z,S]=T.useState(!1),C=T.useCallback((e=>{a&&(g.current.innerText=`${Ko(E.current).toFixed(0)}º`,g.current.style.display="block"),e.stopPropagation();const t=e.point.clone(),n=(new R.Vector3).setFromMatrixPosition(w.current.matrixWorld),o=(new R.Vector3).setFromMatrixColumn(w.current.matrixWorld,0).normalize(),i=(new R.Vector3).setFromMatrixColumn(w.current.matrixWorld,1).normalize(),s=(new R.Vector3).setFromMatrixColumn(w.current.matrixWorld,2).normalize(),l=(new R.Plane).setFromNormalAndCoplanarPoint(s,n);M.current={clickPoint:t,origin:n,e1:o,e2:i,normal:s,plane:l},p({component:"Rotator",axis:r,origin:n,directions:[o,i,s]}),y&&(y.enabled=!1),e.target.setPointerCapture(e.pointerId)}),[a,y,p,r]),P=T.useCallback((e=>{if(e.stopPropagation(),z||S(!0),M.current){const{clickPoint:t,origin:o,e1:i,e2:s,normal:l,plane:c}=M.current,[u,m]=(null==n?void 0:n[r])||[void 0,void 0];ea.copy(e.ray),ea.intersectPlane(c,ta),ea.direction.negate(),ea.intersectPlane(c,ta);let d=((e,t,r,n,o)=>{Xo.copy(e).sub(r),Yo.copy(t).sub(r);const a=n.dot(n),i=o.dot(o),s=Xo.dot(n)/a,l=Xo.dot(o)/i,c=Yo.dot(n)/a,u=Yo.dot(o)/i,m=Math.atan2(l,s);return Math.atan2(u,c)-m})(t,ta,o,i,s),f=Ko(d);e.shiftKey&&(f=10*Math.round(f/10),d=(e=>e*Math.PI/180)(f)),void 0!==u&&void 0!==m&&m-u<2*Math.PI?(d=Zo(d),d=d>Math.PI?d-2*Math.PI:d,d=U.default(d,u-b.current,m-b.current),E.current=b.current+d):(E.current=Zo(b.current+d),E.current=E.current>Math.PI?E.current-2*Math.PI:E.current),a&&(f=Ko(E.current),g.current.innerText=`${f.toFixed(0)}º`),Qo.makeRotationAxis(l,d),Jo.copy(o).applyMatrix4(Qo).sub(o).negate(),Qo.setPosition(Jo),h(Qo)}}),[a,h,z,n,r]),D=T.useCallback((e=>{a&&(g.current.style.display="none"),e.stopPropagation(),b.current=E.current,M.current=null,v(),y&&(y.enabled=!0),e.target.releasePointerCapture(e.pointerId)}),[a,y,v]),k=T.useCallback((e=>{e.stopPropagation(),S(!1)}),[]),F=T.useMemo((()=>{const r=e.clone().normalize(),n=t.clone().normalize();return(new R.Matrix4).makeBasis(r,n,r.clone().cross(n))}),[e,t]),_=u?.65:.65*l,L=T.useMemo((()=>{const e=[];for(let t=0;t<=32;t++){const r=t*(Math.PI/2)/32;e.push(new R.Vector3(Math.cos(r)*_,Math.sin(r)*_,0))}return e}),[_]);return T.createElement("group",{ref:w,onPointerDown:C,onPointerMove:P,onPointerUp:D,onPointerOut:k,matrix:F,matrixAutoUpdate:!1},a&&T.createElement(Y,{position:[_,_,0]},T.createElement("div",{style:{display:"none",background:"#151520",color:"white",padding:"6px 8px",borderRadius:7,whiteSpace:"nowrap"},className:i,ref:g})),T.createElement(ce,{points:L,lineWidth:4*c,visible:!1,userData:x}),T.createElement(ce,{transparent:!0,raycast:()=>null,depthTest:s,points:L,lineWidth:c,color:z?d:m[r],opacity:f,polygonOffset:!0,polygonOffsetFactor:-10,fog:!1}))},na=new R.Vector3,oa=new R.Vector3,aa=new R.Vector3,ia=(e,t,r,n=1)=>{const o=na.set(e.x/r.width*2-1,-e.y/r.height*2+1,n);return o.unproject(t),o},sa=(e,t,r,n)=>{const o=((e,t,r)=>{const n=r.width/2,o=r.height/2;t.updateMatrixWorld(!1);const a=e.project(t);return a.x=a.x*n+n,a.y=-a.y*o+o,a})(aa.copy(e),r,n);let a=0;for(let i=0;i<2;++i){const s=oa.copy(o).setComponent(i,o.getComponent(i)+t),l=ia(s,r,n,s.z);a=Math.max(a,e.distanceTo(l))}return a},la=new R.Matrix4,ca=new R.Matrix4,ua=new R.Matrix4,ma=new R.Matrix4,da=new R.Matrix4,fa=new R.Matrix4,pa=new R.Matrix4,ha=new R.Matrix4,va=new R.Box3,xa=new R.Box3,ya=new R.Vector3,ga=new R.Vector3,wa=new R.Vector3,ba=new R.Vector3,Ea=new R.Vector3(1,0,0),Ma=new R.Vector3(0,1,0),za=new R.Vector3(0,0,1),Sa=T.forwardRef((({matrix:e,onDragStart:t,onDrag:r,onDragEnd:n,autoTransform:a=!0,anchor:i,disableAxes:s=!1,disableSliders:l=!1,disableRotations:c=!1,activeAxes:u=[!0,!0,!0],offset:m=[0,0,0],rotation:d=[0,0,0],scale:f=1,lineWidth:p=4,fixed:h=!1,translationLimits:v,rotationLimits:x,depthTest:y=!0,axisColors:g=["#ff2060","#20df80","#2080ff"],hoveredColor:w="#ffff40",annotations:b=!1,annotationsClass:E,opacity:M=1,visible:z=!0,userData:S,children:P,...D},k)=>{const F=o.useThree((e=>e.invalidate)),_=T.useRef(null),L=T.useRef(null),A=T.useRef(null),B=T.useRef(null),O=T.useRef([0,0,0]);T.useLayoutEffect((()=>{i&&(B.current.updateWorldMatrix(!0,!0),ma.copy(B.current.matrixWorld).invert(),va.makeEmpty(),B.current.traverse((e=>{e.geometry&&(e.geometry.boundingBox||e.geometry.computeBoundingBox(),fa.copy(e.matrixWorld).premultiply(ma),xa.copy(e.geometry.boundingBox),xa.applyMatrix4(fa),va.union(xa))})),ya.copy(va.max).add(va.min).multiplyScalar(.5),ga.copy(va.max).sub(va.min).multiplyScalar(.5),wa.copy(ga).multiply(new R.Vector3(...i)).add(ya),ba.set(...m).add(wa),A.current.position.copy(ba),F())}));const I=T.useMemo((()=>({onDragStart:e=>{la.copy(L.current.matrix),ca.copy(L.current.matrixWorld),t&&t(e),F()},onDrag:e=>{ua.copy(_.current.matrixWorld),ma.copy(ua).invert(),da.copy(ca).premultiply(e),fa.copy(da).premultiply(ma),pa.copy(la).invert(),ha.copy(fa).multiply(pa),a&&L.current.matrix.copy(fa),r&&r(fa,ha,da,e),F()},onDragEnd:()=>{n&&n(),F()},translation:O,translationLimits:v,rotationLimits:x,axisColors:g,hoveredColor:w,opacity:M,scale:f,lineWidth:p,fixed:h,depthTest:y,userData:S,annotations:b,annotationsClass:E})),[t,r,n,O,v,x,y,f,p,h,...g,w,M,S,a,b,E]),U=new R.Vector3;return o.useFrame((e=>{if(h){const o=sa(A.current.getWorldPosition(U),f,e.camera,e.size);var t,r,n;if(A.current)(null==(t=A.current)?void 0:t.scale.x)===o&&(null==(r=A.current)?void 0:r.scale.y)===o&&(null==(n=A.current)?void 0:n.scale.z)===o||(A.current.scale.setScalar(o),e.invalidate())}})),T.useImperativeHandle(k,(()=>L.current),[]),T.useLayoutEffect((()=>{e&&e instanceof R.Matrix4&&(L.current.matrix=e)}),[e]),T.createElement(Io.Provider,{value:I},T.createElement("group",{ref:_},T.createElement("group",C.default({ref:L,matrix:e,matrixAutoUpdate:!1},D),T.createElement("group",{visible:z,ref:A,position:m,rotation:d},!s&&u[0]&&T.createElement(No,{axis:0,direction:Ea}),!s&&u[1]&&T.createElement(No,{axis:1,direction:Ma}),!s&&u[2]&&T.createElement(No,{axis:2,direction:za}),!l&&u[0]&&u[1]&&T.createElement(qo,{axis:2,dir1:Ea,dir2:Ma}),!l&&u[0]&&u[2]&&T.createElement(qo,{axis:1,dir1:za,dir2:Ea}),!l&&u[2]&&u[1]&&T.createElement(qo,{axis:0,dir1:Ma,dir2:za}),!c&&u[0]&&u[1]&&T.createElement(ra,{axis:2,dir1:Ea,dir2:Ma}),!c&&u[0]&&u[2]&&T.createElement(ra,{axis:1,dir1:za,dir2:Ea}),!c&&u[2]&&u[1]&&T.createElement(ra,{axis:0,dir1:Ma,dir2:za})),T.createElement("group",{ref:B},P))))}));exports.AccumulativeShadows=on,exports.AdaptiveDpr=function({pixelated:e}){const t=o.useThree((e=>e.gl)),r=o.useThree((e=>e.internal.active)),n=o.useThree((e=>e.performance.current)),a=o.useThree((e=>e.viewport.initialDpr)),i=o.useThree((e=>e.setDpr));return T.useEffect((()=>{const n=t.domElement;return()=>{r&&i(a),e&&n&&(n.style.imageRendering="auto")}}),[]),T.useEffect((()=>{i(n*a),e&&t.domElement&&(t.domElement.style.imageRendering=1===n?"auto":"pixelated")}),[n]),null},exports.AdaptiveEvents=function(){const e=o.useThree((e=>e.get)),t=o.useThree((e=>e.setEvents)),r=o.useThree((e=>e.performance.current));return T.useEffect((()=>{const r=e().events.enabled;return()=>t({enabled:r})}),[]),T.useEffect((()=>t({enabled:1===r})),[r]),null},exports.ArcballControls=nt,exports.AsciiRenderer=function({renderIndex:e=1,bgColor:t="black",fgColor:r="white",characters:n=" .:-+*=%@#",invert:a=!0,color:i=!1,resolution:s=.15}){const{size:l,gl:c,scene:u,camera:d}=o.useThree(),f=T.useMemo((()=>{const e=new m.AsciiEffect(c,n,{invert:a,color:i,resolution:s});return e.domElement.style.position="absolute",e.domElement.style.top="0px",e.domElement.style.left="0px",e.domElement.style.pointerEvents="none",e}),[n,a,i,s]);return T.useLayoutEffect((()=>{f.domElement.style.color=r,f.domElement.style.backgroundColor=t}),[r,t]),T.useEffect((()=>(c.domElement.style.opacity="0",c.domElement.parentNode.appendChild(f.domElement),()=>{c.domElement.style.opacity="1",c.domElement.parentNode.removeChild(f.domElement)})),[f]),T.useEffect((()=>{f.setSize(l.width,l.height)}),[f,l]),o.useFrame((e=>{f.render(u,d)}),e),T.createElement(T.Fragment,null)},exports.BBAnchor=({anchor:e,...t})=>{const r=T.useRef(null),n=T.useRef(null);return T.useEffect((()=>{var e,t;null!=(e=r.current)&&null!=(t=e.parent)&&t.parent&&(n.current=r.current.parent,r.current.parent.parent.add(r.current))}),[]),o.useFrame((()=>{n.current&&(Gt.setFromObject(n.current),Gt.getSize(Ht),r.current.position.set(n.current.position.x+Ht.x*e[0]/2,n.current.position.y+Ht.y*e[1]/2,n.current.position.z+Ht.z*e[2]/2))})),T.createElement("group",C.default({ref:r},t))},exports.Backdrop=function({children:e,floor:t=.25,segments:r=20,receiveShadow:n,...o}){const a=T.useRef(null);return T.useLayoutEffect((()=>{let e=0;const n=r/r/2,o=a.current.attributes.position;for(let a=0;a<r+1;a++)for(let i=0;i<r+1;i++)o.setXYZ(e++,a/r-n+(0===a?-t:0),i/r-n,un(a/r));o.needsUpdate=!0,a.current.computeVertexNormals()}),[r,t]),T.createElement("group",o,T.createElement("mesh",{receiveShadow:n,rotation:[-Math.PI/2,0,Math.PI/2]},T.createElement("planeGeometry",{ref:a,args:[1,1,r,r]}),e))},exports.BakeShadows=function(){const e=o.useThree((e=>e.gl));return t.useEffect((()=>(e.shadowMap.autoUpdate=!1,e.shadowMap.needsUpdate=!0,()=>{e.shadowMap.autoUpdate=e.shadowMap.needsUpdate=!0})),[e.shadowMap]),null},exports.Billboard=se,exports.Bounds=Nr,exports.Box=pr,exports.Bvh=Nt,exports.CameraControls=st,exports.CameraShake=Hr,exports.Capsule=kr,exports.CatmullRomLine=fe,exports.Caustics=xn,exports.Center=Xt,exports.Circle=hr,exports.Clone=Be,exports.Cloud=function({opacity:e=.5,speed:t=.4,width:r=10,depth:n=1.5,segments:a=20,texture:i="https://rawcdn.githack.com/pmndrs/drei-assets/9225a9f1fbd449d9411125c2f419b843d0308c9f/cloud.png",color:s="#ffffff",depthTest:l=!0,...c}){const u=T.useRef(),m=ze(i),d=T.useMemo((()=>[...new Array(a)].map(((e,n)=>({x:r/2-Math.random()*r,y:r/2-Math.random()*r,scale:.4+Math.sin((n+1)/a*Math.PI)*(10*(.2+Math.random())),density:Math.max(.2,Math.random()),rotation:Math.max(.002,.005*Math.random())*t})))),[r,a,t]);return o.useFrame((e=>{var t;return null==(t=u.current)?void 0:t.children.forEach(((t,r)=>{t.children[0].rotation.z+=d[r].rotation,t.children[0].scale.setScalar(d[r].scale+(1+Math.sin(e.clock.getElapsedTime()/10))/2*r/10)}))})),T.createElement("group",c,T.createElement("group",{position:[0,0,a/2*n],ref:u},d.map((({x:t,y:r,scale:o,density:a},i)=>T.createElement(se,{key:i,position:[t,r,-i*n]},T.createElement(gr,{scale:o,rotation:[0,0,0]},T.createElement("meshStandardMaterial",{map:m,transparent:!0,opacity:o/6*a*e,depthTest:l,color:s})))))))},exports.ComputedAttribute=({compute:e,name:t,...r})=>{const[o]=T.useState((()=>new n.BufferAttribute(new Float32Array(0),1))),a=T.useRef(null);return T.useLayoutEffect((()=>{if(a.current){var t;const r=null!==(t=a.current.parent)&&void 0!==t?t:a.current.__r3f.parent,n=e(r);a.current.copy(n)}}),[e]),T.createElement("primitive",C.default({ref:a,object:o,attach:`attributes-${t}`},r))},exports.Cone=vr,exports.ContactShadows=tn,exports.CubeCamera=function({children:e,frames:t=1/0,resolution:r,near:n,far:a,envMap:i,fog:s,...l}){const c=T.useRef(),{fbo:u,camera:m,update:d}=Ze({resolution:r,near:n,far:a,envMap:i,fog:s});let f=0;return o.useFrame((()=>{c.current&&(t===1/0||f<t)&&(c.current.visible=!1,d(),c.current.visible=!0,f++)})),T.createElement("group",l,T.createElement("primitive",{object:m}),T.createElement("group",{ref:c},e(u.texture)))},exports.CubicBezierLine=de,exports.CurveModifier=Kt,exports.CycleRaycast=function({onChanged:e,portal:t,preventDefault:r=!0,scroll:n=!0,keyCode:a=9}){const i=T.useRef(0),s=o.useThree((e=>e.setEvents)),l=o.useThree((e=>e.get)),c=o.useThree((e=>e.gl));return T.useEffect((()=>{var o;let u,m=[];const d=l().events.filter,f=null!==(o=null==t?void 0:t.current)&&void 0!==o?o:c.domElement.parentNode,p=()=>f&&e&&e(m,Math.round(i.current)%m.length);s({filter:(e,t)=>{let r=[...e];r.length===m.length&&m.every((e=>r.map((e=>e.object.uuid)).includes(e.object.uuid)))||(i.current=0,m=r,p()),d&&(r=d(r,t));for(let e=0;e<Math.round(i.current)%r.length;e++){const e=r.shift();r=[...r,e]}return r}});const h=e=>{var t,r;i.current=e(i.current),null==(t=l().events.handlers)||t.onPointerCancel(void 0),null==(r=l().events.handlers)||r.onPointerMove(u),p()},v=e=>{(e.keyCode||e.which===a)&&(r&&e.preventDefault(),m.length>1&&h((e=>e+1)))},x=e=>{r&&e.preventDefault();let t=0;e||(e=window.event),e.wheelDelta?t=e.wheelDelta/120:e.detail&&(t=-e.detail/3),m.length>1&&h((e=>Math.abs(e-t)))},y=e=>u=e;return document.addEventListener("pointermove",y,{passive:!0}),n&&document.addEventListener("wheel",x),void 0!==a&&document.addEventListener("keydown",v),()=>{s({filter:d}),void 0!==a&&document.removeEventListener("keydown",v),n&&document.removeEventListener("wheel",x),document.removeEventListener("pointermove",y)}}),[c,l,s,r,n,a]),null},exports.Cylinder=xr,exports.Decal=We,exports.Detailed=Co,exports.DeviceOrientationControls=Qe,exports.Dodecahedron=Pr,exports.Edges=De,exports.Effects=be,exports.Environment=en,exports.EnvironmentCube=Zr,exports.EnvironmentMap=Kr,exports.EnvironmentPortal=Qr,exports.Example=Yt,exports.Extrude=Rr,exports.Facemesh=Lr,exports.FacemeshDatas=Ar,exports.FirstPersonControls=it,exports.Float=$r,exports.FlyControls=Je,exports.GizmoHelper=({alignment:e="bottom-right",margin:t=[80,80],renderPriority:r=1,onUpdate:a,onTarget:i,children:s})=>{const l=o.useThree((e=>e.size)),c=o.useThree((e=>e.camera)),u=o.useThree((e=>e.controls)),m=o.useThree((e=>e.invalidate)),d=T.useRef(),f=T.useRef(null),p=T.useRef(!1),h=T.useRef(0),v=T.useRef(new n.Vector3(0,0,0)),x=T.useRef(new n.Vector3(0,0,0));T.useEffect((()=>{x.current.copy(c.up)}),[c]);const y=T.useCallback((e=>{p.current=!0,(u||i)&&(v.current=(null==u?void 0:u.target)||(null==i?void 0:i())),h.current=c.position.distanceTo(xt),ht.copy(c.quaternion),yt.copy(e).multiplyScalar(h.current).add(xt),ft.lookAt(yt),ft.up.copy(c.up),vt.copy(ft.quaternion),m()}),[u,c,i,m]);o.useFrame(((e,t)=>{if(f.current&&d.current){var r;if(p.current)if(ht.angleTo(vt)<.01)p.current=!1,"minPolarAngle"in u&&c.up.copy(x.current);else{const e=t*dt;ht.rotateTowards(vt,e),c.position.set(0,0,1).applyQuaternion(ht).multiplyScalar(h.current).add(v.current),c.up.set(0,1,0).applyQuaternion(ht).normalize(),c.quaternion.copy(ht),a?a():u&&u.update(),m()}pt.copy(c.matrix).invert(),null==(r=d.current)||r.quaternion.setFromRotationMatrix(pt)}}));const g=T.useMemo((()=>({tweenCamera:y})),[y]),[w,b]=t,E=e.endsWith("-center")?0:e.endsWith("-left")?-l.width/2+w:l.width/2-w,M=e.startsWith("center-")?0:e.startsWith("top-")?l.height/2-b:-l.height/2+b;return T.createElement(ct,{renderPriority:r},T.createElement(ut.Provider,{value:g},T.createElement(Ye,{makeDefault:!0,ref:f,position:[0,0,200]}),T.createElement("group",{ref:d,position:[E,M,0]},s)))},exports.GizmoViewcube=e=>T.createElement("group",{scale:[60,60,60]},T.createElement(Dt,e),Tt.map(((t,r)=>T.createElement(kt,C.default({key:r,position:t,dimensions:Pt[r]},e)))),St.map(((t,r)=>T.createElement(kt,C.default({key:r,position:t,dimensions:Ct},e)))),T.createElement("ambientLight",{intensity:.5}),T.createElement("pointLight",{position:[10,10,10],intensity:.5})),exports.GizmoViewport=({hideNegativeAxes:e,hideAxisHeads:t,disabled:r,font:n="18px Inter var, Arial, sans-serif",axisColors:o=["#ff2060","#20df80","#2080ff"],axisHeadScale:a=1,axisScale:i,labels:s=["X","Y","Z"],labelColor:l="#000",onClick:c,...u})=>{const[m,d,f]=o,{tweenCamera:p}=mt(),h={font:n,disabled:r,labelColor:l,onClick:c,axisHeadScale:a,onPointerDown:r?void 0:e=>{p(e.object.position),e.stopPropagation()}};return T.createElement("group",C.default({scale:40},u),T.createElement(Ft,{color:m,rotation:[0,0,0],scale:i}),T.createElement(Ft,{color:d,rotation:[0,0,Math.PI/2],scale:i}),T.createElement(Ft,{color:f,rotation:[0,-Math.PI/2,0],scale:i}),!t&&T.createElement(T.Fragment,null,T.createElement(_t,C.default({arcStyle:m,position:[1,0,0],label:s[0]},h)),T.createElement(_t,C.default({arcStyle:d,position:[0,1,0],label:s[1]},h)),T.createElement(_t,C.default({arcStyle:f,position:[0,0,1],label:s[2]},h)),!e&&T.createElement(T.Fragment,null,T.createElement(_t,C.default({arcStyle:m,position:[-1,0,0]},h)),T.createElement(_t,C.default({arcStyle:d,position:[0,-1,0]},h)),T.createElement(_t,C.default({arcStyle:f,position:[0,0,-1]},h)))),T.createElement("ambientLight",{intensity:.5}),T.createElement("pointLight",{position:[10,10,10],intensity:.5}))},exports.Gltf=qe,exports.GradientTexture=function({stops:e,colors:t,size:r=1024,...n}){const o=T.useMemo((()=>{const n=document.createElement("canvas"),o=n.getContext("2d");n.width=16,n.height=r;const a=o.createLinearGradient(0,0,0,r);let i=e.length;for(;i--;)a.addColorStop(e[i],t[i]);return o.fillStyle=a,o.fillRect(0,0,16,r),n}),[e]);return T.createElement("canvasTexture",C.default({args:[o],attach:"map"},n))},exports.Grid=At,exports.Html=Y,exports.Hud=ct,exports.Icosahedron=Cr,exports.Image=Re,exports.Instance=yo,exports.Instances=go,exports.IsObject=Me,exports.KeyboardControls=function({map:e,children:t,onChange:r,domElement:n}){const o=e.map((e=>e.name+e.keys)).join("-"),a=T.useMemo((()=>k.default(u.subscribeWithSelector((()=>e.reduce(((e,t)=>({...e,[t.name]:!1})),{}))))),[o]),i=T.useMemo((()=>[a.subscribe,a.getState,a]),[o]),s=a.setState;return T.useEffect((()=>{const t=e.map((({name:e,keys:t,up:n})=>({keys:t,up:n,fn:t=>{s({[e]:t}),r&&r(e,t,i[1]())}}))).reduce(((e,{keys:t,fn:r,up:n=!0})=>(t.forEach((t=>e[t]={fn:r,pressed:!1,up:n})),e)),{}),o=({key:e,code:r})=>{const n=t[e]||t[r];if(!n)return;const{fn:o,pressed:a,up:i}=n;n.pressed=!0,!i&&a||o(!0)},a=({key:e,code:r})=>{const n=t[e]||t[r];if(!n)return;const{fn:o,up:a}=n;n.pressed=!1,a&&o(!1)},l=n||window;return l.addEventListener("keydown",o,{passive:!0}),l.addEventListener("keyup",a,{passive:!0}),()=>{l.removeEventListener("keydown",o),l.removeEventListener("keyup",a)}}),[n,o]),T.createElement(ae.Provider,{value:i,children:t})},exports.Lathe=Dr,exports.Lightformer=Sn,exports.Line=ce,exports.Loader=function({containerStyles:e,innerStyles:t,barStyles:r,dataStyles:n,dataInterpolation:o=Q,initialState:a=(e=>e)}){const{active:i,progress:s}=Z(),l=T.useRef(0),c=T.useRef(0),u=T.useRef(null),[m,d]=T.useState(a(i));T.useEffect((()=>{let e;return i!==m&&(e=setTimeout((()=>d(i)),300)),()=>clearTimeout(e)}),[m,i]);const f=T.useCallback((()=>{u.current&&(l.current+=(s-l.current)/2,(l.current>.95*s||100===s)&&(l.current=s),u.current.innerText=o(l.current),l.current<s&&(c.current=requestAnimationFrame(f)))}),[o,s]);return T.useEffect((()=>(f(),()=>cancelAnimationFrame(c.current))),[f]),m?T.createElement("div",{style:{...J.container,opacity:i?1:0,...e}},T.createElement("div",null,T.createElement("div",{style:{...J.inner,...t}},T.createElement("div",{style:{...J.bar,transform:`scaleX(${s/100})`,...r}}),T.createElement("span",{ref:u,style:{...J.data,...n}})))):null},exports.MapControls=et,exports.MarchingCube=Ue,exports.MarchingCubes=Ie,exports.MarchingPlane=Ve,exports.Mask=Lo,exports.Merged=wo,exports.MeshDiscardMaterial=cr,exports.MeshDistortMaterial=Qt,exports.MeshReflectorMaterial=or,exports.MeshRefractionMaterial=function({aberrationStrength:e=0,fastChroma:r=!0,envMap:n,...a}){o.extend({MeshRefractionMaterial:ar});const i=t.useRef(),{size:s}=o.useThree(),l=t.useMemo((()=>{var t,o;const a={},i=(s=n)&&s.isCubeTexture;var s;const l=(null!==(t=i?null==(o=n.image[0])?void 0:o.width:n.image.width)&&void 0!==t?t:1024)/4,c=Math.floor(Math.log2(l)),u=Math.pow(2,c),m=3*Math.max(u,112),d=4*u;return i&&(a.ENVMAP_TYPE_CUBEM=""),a.CUBEUV_TEXEL_WIDTH=""+1/m,a.CUBEUV_TEXEL_HEIGHT=""+1/d,a.CUBEUV_MAX_MIP=`${c}.0`,e>0&&(a.CHROMATIC_ABERRATIONS=""),r&&(a.FAST_CHROMA=""),a}),[e,r]);return t.useLayoutEffect((()=>{var e,t,r;const n=null==(e=i.current)||null==(t=e.__r3f)||null==(r=t.parent)?void 0:r.geometry;n&&(i.current.bvh=new b.MeshBVHUniformStruct,i.current.bvh.updateFrom(new b.MeshBVH(n.clone().toNonIndexed(),{lazyGeneration:!1,strategy:b.SAH})))}),[]),o.useFrame((({camera:e})=>{i.current.viewMatrixInverse=e.matrixWorld,i.current.projectionMatrixInverse=e.projectionMatrixInverse})),T.createElement("meshRefractionMaterial",C.default({key:JSON.stringify(l),defines:l,ref:i,resolution:[s.width,s.height],aberrationStrength:e,envMap:n},a))},exports.MeshTransmissionMaterial=lr,exports.MeshWobbleMaterial=er,exports.Octahedron=Tr,exports.OrbitControls=tt,exports.OrthographicCamera=Ye,exports.PerformanceMonitor=function({iterations:e=10,ms:r=250,threshold:n=.75,step:a=.1,factor:i=.5,flipflops:s=1/0,bounds:l=(e=>e>100?[60,100]:[40,60]),onIncline:c,onDecline:u,onChange:m,onFallback:d,children:f}){const p=Math.pow(10,0),[h,v]=t.useState((()=>({fps:0,index:0,factor:i,flipped:0,refreshrate:0,fallback:!1,frames:[],averages:[],subscriptions:new Map,subscribe:e=>{const t=Symbol();return h.subscriptions.set(t,e.current),()=>{h.subscriptions.delete(t)}}})));let x=0;return o.useFrame((()=>{const{frames:t,averages:o}=h;if(!h.fallback&&o.length<e){t.push(performance.now());const i=t[t.length-1]-t[0];if(i>=r){if(h.fps=Math.round(t.length/i*1e3*p)/p,h.refreshrate=Math.max(h.refreshrate,h.fps),o[h.index++%e]=h.fps,o.length===e){const[t,r]=l(h.refreshrate),i=o.filter((e=>e>=r)),f=o.filter((e=>e<t));i.length>e*n&&(h.factor=Math.min(1,h.factor+a),h.flipped++,c&&c(h),h.subscriptions.forEach((e=>e.onIncline&&e.onIncline(h)))),f.length>e*n&&(h.factor=Math.max(0,h.factor-a),h.flipped++,u&&u(h),h.subscriptions.forEach((e=>e.onDecline&&e.onDecline(h)))),x!==h.factor&&(x=h.factor,m&&m(h),h.subscriptions.forEach((e=>e.onChange&&e.onChange(h)))),h.flipped>s&&!h.fallback&&(h.fallback=!0,d&&d(h),h.subscriptions.forEach((e=>e.onFallback&&e.onFallback(h)))),h.averages=[]}h.frames=[]}}})),T.createElement(ko.Provider,{value:h},f)},exports.PerspectiveCamera=Ke,exports.PivotControls=Sa,exports.Plane=gr,exports.Point=ro,exports.PointMaterial=mr,exports.PointMaterialImpl=ur,exports.PointerLockControls=at,exports.Points=oo,exports.PointsBuffer=no,exports.Polyhedron=Sr,exports.PositionPoint=Yn,exports.PositionalAudio=pe,exports.Preload=function({all:e,scene:t,camera:r}){const a=o.useThree((({gl:e})=>e)),i=o.useThree((({camera:e})=>e)),s=o.useThree((({scene:e})=>e));return T.useLayoutEffect((()=>{const o=[];e&&(t||s).traverse((e=>{!1===e.visible&&(o.push(e),e.visible=!0)})),a.compile(t||s,r||i);const l=new n.WebGLCubeRenderTarget(128);new n.CubeCamera(.01,1e5,l).update(a,t||s),l.dispose(),o.forEach((e=>e.visible=!1))}),[]),null},exports.PresentationControls=function({enabled:e=!0,snap:t,global:r,domElement:a,cursor:i=!0,children:s,speed:u=1,rotation:m=[0,0,0],zoom:d=1,polar:f=[0,Math.PI/2],azimuth:p=[-1/0,1/0],config:h={mass:1,tension:170,friction:26}}){const v=o.useThree((e=>e.events)),x=o.useThree((e=>e.gl)),y=a||v.connected||x.domElement,{size:g}=o.useThree(),w=T.useMemo((()=>[m[0]+f[0],m[0]+f[1]]),[m[0],f[0],f[1]]),b=T.useMemo((()=>[m[1]+p[0],m[1]+p[1]]),[m[1],p[0],p[1]]),E=T.useMemo((()=>[n.MathUtils.clamp(m[0],...w),n.MathUtils.clamp(m[1],...b),m[2]]),[m[0],m[1],m[2],w,b]),[M,z]=l.useSpring((()=>({scale:1,rotation:E,config:h})));T.useEffect((()=>{z.start({scale:1,rotation:E,config:h})}),[E]),T.useEffect((()=>{if(r&&i&&e)return y.style.cursor="grab",x.domElement.style.cursor="",()=>{y.style.cursor="default",x.domElement.style.cursor="default"}}),[r,i,y,e]);const S=c.useGesture({onHover:({last:t})=>{i&&!r&&e&&(y.style.cursor=t?"auto":"grab")},onDrag:({down:r,delta:[o,a],memo:[s,l]=M.rotation.animation.to||E})=>{if(!e)return[a,o];i&&(y.style.cursor=r?"grabbing":"grab"),o=n.MathUtils.clamp(l+o/g.width*Math.PI*u,...b),a=n.MathUtils.clamp(s+a/g.height*Math.PI*u,...w);const c=t&&!r&&"boolean"!=typeof t?t:h;return z.start({scale:r&&a>w[1]/2?d:1,rotation:t&&!r?E:[a,o,0],config:e=>"scale"===e?{...c,friction:3*c.friction}:c}),[a,o]}},{target:r?y:void 0});return T.createElement(l.a.group,C.default({},null==S?void 0:S(),M),s)},exports.QuadraticBezierLine=me,exports.RandomizedLight=an,exports.Reflector=yn,exports.RenderTexture=Fo,exports.Resize=Vr,exports.Ring=zr,exports.RoundedBox=Or,exports.Sampler=function({children:e,weight:t,transform:r,instances:n,mesh:o,count:a=16,...i}){const s=T.useRef(null),l=T.useRef(null),c=T.useRef(null);return T.useLayoutEffect((()=>{var e,t;l.current=null!==(e=null==n?void 0:n.current)&&void 0!==e?e:s.current.children.find((e=>e.hasOwnProperty("instanceMatrix"))),c.current=null!==(t=null==o?void 0:o.current)&&void 0!==t?t:s.current.children.find((e=>"Mesh"===e.type))}),[e,null==o?void 0:o.current,null==n?void 0:n.current]),Ae(c,a,r,t,l),T.createElement("group",C.default({ref:s},i),e)},exports.ScreenQuad=Ur,exports.ScreenSpace=le,exports.Scroll=oe,exports.ScrollControls=function({eps:e=1e-5,enabled:t=!0,infinite:r,horizontal:n,pages:a=1,distance:i=1,damping:l=.25,maxSpeed:c=1/0,style:u={},children:m}){const{get:d,setEvents:f,gl:p,size:h,invalidate:v,events:x}=o.useThree(),[y]=T.useState((()=>document.createElement("div"))),[g]=T.useState((()=>document.createElement("div"))),[w]=T.useState((()=>document.createElement("div"))),b=p.domElement.parentNode,E=T.useRef(0),M=T.useMemo((()=>{const t={el:y,eps:e,fill:g,fixed:w,horizontal:n,damping:l,offset:0,delta:0,scroll:E,pages:a,range(e,t,r=0){const n=e-r,o=n+t+2*r;return this.offset<n?0:this.offset>o?1:(this.offset-n)/(o-n)},curve(e,t,r=0){return Math.sin(this.range(e,t,r)*Math.PI)},visible(e,t,r=0){const n=e-r,o=n+t+2*r;return this.offset>=n&&this.offset<=o}};return t}),[e,l,n,a]);T.useEffect((()=>{y.style.position="absolute",y.style.width="100%",y.style.height="100%",y.style[n?"overflowX":"overflowY"]="auto",y.style[n?"overflowY":"overflowX"]="hidden",y.style.top="0px",y.style.left="0px";for(const e in u)y.style[e]=u[e];w.style.position="sticky",w.style.top="0px",w.style.left="0px",w.style.width="100%",w.style.height="100%",w.style.overflow="hidden",y.appendChild(w),g.style.height=n?"100%":a*i*100+"%",g.style.width=n?a*i*100+"%":"100%",g.style.pointerEvents="none",y.appendChild(g),b.appendChild(y),y[n?"scrollLeft":"scrollTop"]=1;const e=x.connected||p.domElement;requestAnimationFrame((()=>null==x.connect?void 0:x.connect(y)));const t=d().events.compute;return f({compute(e,t){const{left:r,top:n}=b.getBoundingClientRect(),o=e.clientX-r,a=e.clientY-n;t.pointer.set(o/t.size.width*2-1,-a/t.size.height*2+1),t.raycaster.setFromCamera(t.pointer,t.camera)}}),()=>{b.removeChild(y),f({compute:t}),null==x.connect||x.connect(e)}}),[a,i,n,y,g,w,b]),T.useEffect((()=>{if(x.connected===y){const e=h[n?"width":"height"],o=y[n?"scrollWidth":"scrollHeight"],a=o-e;let i=0,s=!0,l=!0;const c=()=>{if(t&&!l&&(v(),i=y[n?"scrollLeft":"scrollTop"],E.current=i/a,r)){if(!s)if(i>=a){const e=1-M.offset;y[n?"scrollLeft":"scrollTop"]=1,E.current=M.offset=-e,s=!0}else if(i<=0){const e=1+M.offset;y[n?"scrollLeft":"scrollTop"]=o,E.current=M.offset=e,s=!0}s&&setTimeout((()=>s=!1),40)}};y.addEventListener("scroll",c,{passive:!0}),requestAnimationFrame((()=>l=!1));const u=e=>y.scrollLeft+=e.deltaY/2;return n&&y.addEventListener("wheel",u,{passive:!0}),()=>{y.removeEventListener("scroll",c),n&&y.removeEventListener("wheel",u)}}}),[y,x,h,r,M,v,n,t]);let z=0;return o.useFrame(((t,r)=>{z=M.offset,s.easing.damp(M,"offset",E.current,l,r,c,void 0,e),s.easing.damp(M,"delta",Math.abs(z-M.offset),l,r,c,void 0,e),M.delta>e&&v()})),T.createElement(ee.Provider,{value:M},m)},exports.Segment=So,exports.SegmentObject=Mo,exports.Segments=Eo,exports.Select=function({box:e,multiple:t,children:r,onChange:n,onChangePointerUp:a,border:i="1px solid #55aaff",backgroundColor:s="rgba(75, 160, 255, 0.1)",filter:l=(e=>e),...c}){const[u,d]=T.useState(!1),{setEvents:f,camera:p,raycaster:h,gl:v,controls:x,size:y,get:g}=o.useThree(),[w,b]=T.useState(!1),[E,M]=T.useReducer(((e,{object:t,shift:r})=>void 0===t?[]:Array.isArray(t)?t:r?e.includes(t)?e.filter((e=>e!==t)):[t,...e]:e[0]===t?[]:[t]),[]);T.useEffect((()=>{u?null==n||n(E):null==a||a(E)}),[E,u]);const z=T.useCallback((e=>{e.stopPropagation(),M({object:l([e.object])[0],shift:t&&e.shiftKey})}),[]),S=T.useCallback((e=>!w&&M({})),[w]),P=T.useRef(null);return T.useEffect((()=>{if(!e||!t)return;const r=new m.SelectionBox(p,P.current),n=document.createElement("div");n.style.pointerEvents="none",n.style.border=i,n.style.backgroundColor=s,n.style.position="fixed";const o=new R.Vector2,a=new R.Vector2,c=new R.Vector2,u=g().events.enabled,h=null==x?void 0:x.enabled;let w=!1;function b(e,t){const{offsetX:r,offsetY:n}=e,{width:o,height:a}=y;t.set(r/o*2-1,-n/a*2+1)}function E(e){e.shiftKey&&(!function(e){var t;x&&(x.enabled=!1),f({enabled:!1}),d(w=!0),null==(t=v.domElement.parentElement)||t.appendChild(n),n.style.left=`${e.clientX}px`,n.style.top=`${e.clientY}px`,n.style.width="0px",n.style.height="0px",o.x=e.clientX,o.y=e.clientY}(e),b(e,r.startPoint))}let z=[];function S(e){if(w){!function(e){c.x=Math.max(o.x,e.clientX),c.y=Math.max(o.y,e.clientY),a.x=Math.min(o.x,e.clientX),a.y=Math.min(o.y,e.clientY),n.style.left=`${a.x}px`,n.style.top=`${a.y}px`,n.style.width=c.x-a.x+"px",n.style.height=c.y-a.y+"px"}(e),b(e,r.endPoint);const t=r.select().sort((e=>e.uuid)).filter((e=>e.isMesh));_.default(t,z)||(z=t,M({object:l(t)}))}}function C(e){var t;w&&w&&(x&&(x.enabled=h),f({enabled:u}),d(w=!1),null==(t=n.parentElement)||t.removeChild(n))}return document.addEventListener("pointerdown",E,{passive:!0}),document.addEventListener("pointermove",S,{passive:!0,capture:!0}),document.addEventListener("pointerup",C,{passive:!0}),()=>{document.removeEventListener("pointerdown",E),document.removeEventListener("pointermove",S),document.removeEventListener("pointerup",C)}}),[y.width,y.height,h,p,x,v]),T.createElement("group",C.default({ref:P,onClick:z,onPointerOver:()=>b(!0),onPointerOut:()=>b(!1),onPointerMissed:S},c),T.createElement(ie.Provider,{value:E},r))},exports.Shadow=mn,exports.Shape=Fr,exports.Sky=Tn,exports.SoftShadows=function({focus:e=0,samples:t=10,size:r=25}){const n=o.useThree((e=>e.gl)),a=o.useThree((e=>e.scene)),i=o.useThree((e=>e.camera));return T.useEffect((()=>{const o=R.ShaderChunk.shadowmap_pars_fragment;return R.ShaderChunk.shadowmap_pars_fragment=R.ShaderChunk.shadowmap_pars_fragment.replace("#ifdef USE_SHADOWMAP","#ifdef USE_SHADOWMAP\n"+(({focus:e=0,size:t=25,samples:r=10}={})=>`\n#define PENUMBRA_FILTER_SIZE float(${t})\n#define RGB_NOISE_FUNCTION(uv) (randRGB(uv))\nvec3 randRGB(vec2 uv) {\n  return vec3(\n    fract(sin(dot(uv, vec2(12.75613, 38.12123))) * 13234.76575),\n    fract(sin(dot(uv, vec2(19.45531, 58.46547))) * 43678.23431),\n    fract(sin(dot(uv, vec2(23.67817, 78.23121))) * 93567.23423)\n  );\n}\n\nvec3 lowPassRandRGB(vec2 uv) {\n  // 3x3 convolution (average)\n  // can be implemented as separable with an extra buffer for a total of 6 samples instead of 9\n  vec3 result = vec3(0);\n  result += RGB_NOISE_FUNCTION(uv + vec2(-1.0, -1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(-1.0,  0.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(-1.0, +1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2( 0.0, -1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2( 0.0,  0.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2( 0.0, +1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(+1.0, -1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(+1.0,  0.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(+1.0, +1.0));\n  result *= 0.111111111; // 1.0 / 9.0\n  return result;\n}\nvec3 highPassRandRGB(vec2 uv) {\n  // by subtracting the low-pass signal from the original signal, we're being left with the high-pass signal\n  // hp(x) = x - lp(x)\n  return RGB_NOISE_FUNCTION(uv) - lowPassRandRGB(uv) + 0.5;\n}\n\n\nvec2 vogelDiskSample(int sampleIndex, int sampleCount, float angle) {\n  const float goldenAngle = 2.399963f; // radians\n  float r = sqrt(float(sampleIndex) + 0.5f) / sqrt(float(sampleCount));\n  float theta = float(sampleIndex) * goldenAngle + angle;\n  float sine = sin(theta);\n  float cosine = cos(theta);\n  return vec2(cosine, sine) * r;\n}\nfloat penumbraSize( const in float zReceiver, const in float zBlocker ) { // Parallel plane estimation\n  return (zReceiver - zBlocker) / zBlocker;\n}\nfloat findBlocker(sampler2D shadowMap, vec2 uv, float compare, float angle) {\n  float texelSize = 1.0 / float(textureSize(shadowMap, 0).x);\n  float blockerDepthSum = float(${e});\n  float blockers = 0.0;\n\n  int j = 0;\n  vec2 offset = vec2(0.);\n  float depth = 0.;\n\n  #pragma unroll_loop_start\n  for(int i = 0; i < ${r}; i ++) {\n    offset = (vogelDiskSample(j, ${r}, angle) * texelSize) * 2.0 * PENUMBRA_FILTER_SIZE;\n    depth = unpackRGBAToDepth( texture2D( shadowMap, uv + offset));\n    if (depth < compare) {\n      blockerDepthSum += depth;\n      blockers++;\n    }\n    j++;\n  }\n  #pragma unroll_loop_end\n\n  if (blockers > 0.0) {\n    return blockerDepthSum / blockers;\n  }\n  return -1.0;\n}\n\n        \nfloat vogelFilter(sampler2D shadowMap, vec2 uv, float zReceiver, float filterRadius, float angle) {\n  float texelSize = 1.0 / float(textureSize(shadowMap, 0).x);\n  float shadow = 0.0f;\n  int j = 0;\n  vec2 vogelSample = vec2(0.0);\n  vec2 offset = vec2(0.0);\n  #pragma unroll_loop_start\n  for (int i = 0; i < ${r}; i++) {\n    vogelSample = vogelDiskSample(j, ${r}, angle) * texelSize;\n    offset = vogelSample * (1.0 + filterRadius * float(${t}));\n    shadow += step( zReceiver, unpackRGBAToDepth( texture2D( shadowMap, uv + offset ) ) );\n    j++;\n  }\n  #pragma unroll_loop_end\n  return shadow * 1.0 / ${r}.0;\n}\n\nfloat PCSS (sampler2D shadowMap, vec4 coords) {\n  vec2 uv = coords.xy;\n  float zReceiver = coords.z; // Assumed to be eye-space z in this code\n  float angle = highPassRandRGB(gl_FragCoord.xy).r * PI2;\n  float avgBlockerDepth = findBlocker(shadowMap, uv, zReceiver, angle);\n  if (avgBlockerDepth == -1.0) {\n    return 1.0;\n  }\n  float penumbraRatio = penumbraSize(zReceiver, avgBlockerDepth);\n  return vogelFilter(shadowMap, uv, zReceiver, 1.25 * penumbraRatio, angle);\n}`)({size:r,samples:t,focus:e})).replace("#if defined( SHADOWMAP_TYPE_PCF )","\nreturn PCSS(shadowMap, shadowCoord);\n#if defined( SHADOWMAP_TYPE_PCF )"),dr(n,a,i),()=>{R.ShaderChunk.shadowmap_pars_fragment=o,dr(n,a,i)}}),[e,r,t]),null},exports.Sparkles=Bn,exports.Sphere=yr,exports.SpotLight=zn,exports.SpotLightShadow=function(e){return e.shader?T.createElement(En,e):T.createElement(Mn,e)},exports.SpriteAnimator=({startFrame:e,endFrame:t,fps:r,frameName:n,textureDataURL:a,textureImageURL:i,loop:s,numberOfFrames:l,autoPlay:c,animationNames:u,onStart:m,onEnd:d,onLoopEnd:f,onFrame:p,play:h,pause:v,flipX:x,alphaTest:y,children:g,...w},b)=>{o.useThree((e=>e.viewport));const E=T.useRef(null),[M,z]=T.useState(!1),S=T.useRef(),C=T.useRef(),P=T.useRef(window.performance.now()),D=T.useRef(),k=T.useRef(e||0),F=T.useRef(n||""),_=1e3/(r||30),[L,A]=T.useState(new R.Texture),B=T.useRef(0),[O,I]=T.useState([1,1,1]),U=x?-1:1;const V=(e,t)=>{const r=t/e;return C.current.scale.set(1,r,1),[1,r,1]};T.useEffect((()=>{if(a&&i)!function(e,t,r){const n=new R.TextureLoader,o=fetch(e).then((e=>e.json())),a=new Promise((e=>{n.load(t,e)}));Promise.all([o,a]).then((e=>{r(e[0],e[1])}))}(a,i,j);else if(i){const e=new R.TextureLoader;new Promise((t=>{e.load(i,t)})).then((e=>{j(null,e)}))}}),[]),T.useLayoutEffect((()=>{N()}),[L]),T.useEffect((()=>{}),[v]),T.useEffect((()=>{F.current!==n&&n&&(k.current=0,F.current=n)}),[n]);const j=(e,t)=>{if(null===e){if(t&&l){const e=t.image.width,r=t.image.height,n=e/l,o=r;if(D.current=t,B.current=l,E.current={frames:[],meta:{version:"1.0",size:{w:e,h:r},scale:"1"}},parseInt(n.toString(),10)===n)for(let e=0;e<l;e++)E.current.frames.push({frame:{x:e*n,y:0,w:n,h:o},rotated:!1,trimmed:!1,spriteSourceSize:{x:0,y:0,w:n,h:o},sourceSize:{w:n,h:r}})}}else if(t){E.current=e,E.current.frames=Array.isArray(e.frames)?e.frames:W(),B.current=Array.isArray(e.frames)?e.frames.length:Object.keys(e.frames).length,D.current=t;const{w:r,h:n}=G(e.frames).sourceSize,o=V(r,n);I(o),S.current&&(S.current.map=t)}t.premultiplyAlpha=!1,A(t)},W=()=>{const e={},t=E.current,r=u;if(r)for(let n=0;n<r.length;n++){e[r[n]]=[];for(let o in t.frames){const a=t.frames[o],i=a.frame,s=i.x,l=i.y,c=i.w,u=i.h,m=a.sourceSize.w,d=a.sourceSize.h;"string"==typeof o&&-1!==o.toLowerCase().indexOf(r[n].toLowerCase())&&e[r[n]].push({x:s,y:l,w:c,h:u,frame:i,sourceSize:{w:m,h:d}})}}return e},N=()=>{if(!E.current)return;const{meta:{size:e},frames:t}=E.current,{w:r,h:o}=Array.isArray(t)?t[0].sourceSize:n&&t[n]?t[n][0].sourceSize:{w:0,h:0};S.current.map.wrapS=S.current.map.wrapT=R.RepeatWrapping,S.current.map.center.set(0,0),S.current.map.repeat.set(1*U/(e.w/r),1/(e.h/o));const a=1/((e.h-1)/o);S.current.map.offset.x=0,S.current.map.offset.y=1-a,z(!0),m&&m({currentFrameName:n,currentFrame:k.current})};o.useFrame(((r,o)=>{var a,i;null!=(a=E.current)&&a.frames&&null!=(i=S.current)&&i.map&&(v||(c||h)&&((()=>{const r=window.performance.now(),o=r-P.current,{meta:{size:a},frames:i}=E.current,{w:l,h:c}=G(i).sourceSize,u=Array.isArray(i)?i:n?i[n]:[];let m=0,p=0;const h=t||u.length-1;if(k.current>h&&(k.current=s&&null!=e?e:0,s?null==f||f({currentFrameName:n,currentFrame:k.current}):null==d||d({currentFrameName:n,currentFrame:k.current}),!s))return;if(o<=_)return;P.current=r-o%_,V(l,c);const v=(a.w-1)/l,x=(a.h-1)/c,{frame:{x:y,y:g},sourceSize:{w:w,h:b}}=u[k.current],M=1/v,z=1/x;m=U>0?M*(y/w):M*(y/w)-S.current.map.repeat.x,p=Math.abs(1-z)-z*(g/b),S.current.map.offset.x=m,S.current.map.offset.y=p,k.current+=1})(),p&&p({currentFrameName:F.current,currentFrame:k.current})))}));const G=e=>{if(Array.isArray(e))return e[0];if("object"==typeof e&&null!==e){return e[Object.keys(e)[0]][0]}return{w:0,h:0}};return T.createElement("group",w,T.createElement(T.Suspense,{fallback:null},T.createElement("sprite",{ref:C,scale:O},T.createElement("spriteMaterial",{toneMapped:!1,ref:S,map:L,transparent:!0,alphaTest:null!=y?y:0}))),g)},exports.Stage=function({children:e,center:t,adjustCamera:r=!0,intensity:n=.5,shadows:o="contact",environment:a="city",preset:i="rembrandt",...s}){var l,c,u,m,d,f,p,h;const v="string"==typeof i?ln[i]:i,[{radius:x,height:y},g]=T.useState({radius:0,width:0,height:0,depth:0}),w=null!==(l=null==o?void 0:o.bias)&&void 0!==l?l:-1e-4,b=null!==(c=null==o?void 0:o.normalBias)&&void 0!==c?c:0,E=null!==(u=null==o?void 0:o.size)&&void 0!==u?u:1024,M=null!==(m=null==o?void 0:o.offset)&&void 0!==m?m:0,z="contact"===o||"contact"===(null==o?void 0:o.type),S="accumulative"===o||"accumulative"===(null==o?void 0:o.type),P={..."object"==typeof o?o:{}},R=a?"string"==typeof a?{preset:a}:a:null,D=T.useCallback((e=>{const{width:r,height:n,depth:o,boundingSphere:a}=e;g({radius:a.radius,width:r,height:n,depth:o}),null!=t&&t.onCentered&&t.onCentered(e)}),[]);return T.createElement(T.Fragment,null,T.createElement("ambientLight",{intensity:n/3}),T.createElement("spotLight",{penumbra:1,position:[v.main[0]*x,v.main[1]*x,v.main[2]*x],intensity:2*n,castShadow:!!o,"shadow-bias":w,"shadow-normalBias":b,"shadow-mapSize":E}),T.createElement("pointLight",{position:[v.fill[0]*x,v.fill[1]*x,v.fill[2]*x],intensity:n}),T.createElement(Nr,C.default({fit:!!r,clip:!!r,margin:Number(r),observe:!0},s),T.createElement(cn,{radius:x,adjustCamera:r}),T.createElement(Xt,C.default({},t,{position:[0,M/2,0],onCentered:D}),e)),T.createElement("group",{position:[0,-y/2-M/2,0]},z&&T.createElement(tn,C.default({scale:4*x,far:x,blur:2},P)),S&&T.createElement(on,C.default({temporal:!0,frames:100,alphaTest:.9,toneMapped:!0,scale:4*x},P),T.createElement(an,{amount:null!==(d=P.amount)&&void 0!==d?d:8,radius:null!==(f=P.radius)&&void 0!==f?f:x,ambient:null!==(p=P.ambient)&&void 0!==p?p:.5,intensity:null!==(h=P.intensity)&&void 0!==h?h:1,position:[v.main[0]*x,v.main[1]*x,v.main[2]*x],size:4*x,bias:-w,mapSize:E}))),a&&T.createElement(en,R))},exports.Stars=Dn,exports.Stats=function({showPanel:e=0,className:t,parent:r}){const n=function(e,t=[],r){const[n,o]=T.useState();return T.useLayoutEffect((()=>{const t=e();return o(t),Vt(r,t),()=>Vt(r,null)}),t),n}((()=>new O.default),[]);return T.useEffect((()=>{if(n){const a=r&&r.current||document.body;n.showPanel(e),null==a||a.appendChild(n.dom),t&&n.dom.classList.add(...t.split(" ").filter((e=>e)));const i=o.addEffect((()=>n.begin())),s=o.addAfterEffect((()=>n.end()));return()=>{null==a||a.removeChild(n.dom),i(),s()}}}),[r,n,t,e]),null},exports.Svg=Ne,exports.Tetrahedron=Mr,exports.Text=he,exports.Text3D=we,exports.Torus=br,exports.TorusKnot=Er,exports.TrackballControls=rt,exports.Trail=Le,exports.TransformControls=ot,exports.Tube=wr,exports.View=({track:e,index:t=1,frames:r=1/0,children:n})=>{var a,i;const s=T.useRef(null),{size:l,scene:c}=o.useThree(),[u]=T.useState((()=>new R.Scene)),m=T.useCallback(((t,r)=>{if(s.current&&e.current&&t.target===e.current){const{width:e,height:n,left:o,top:a}=s.current,i=t.clientX-o,l=t.clientY-a;r.pointer.set(i/e*2-1,-l/n*2+1),r.raycaster.setFromCamera(r.pointer,r.camera)}}),[s,e]),[d,f]=T.useReducer((()=>!0),!1);return T.useEffect((()=>{var t;s.current=null==(t=e.current)?void 0:t.getBoundingClientRect(),f()}),[e]),T.createElement(T.Fragment,null,d&&o.createPortal(T.createElement(Oo,{canvasSize:l,frames:r,scene:c,track:e,rect:s,index:t},n),u,{events:{compute:m,priority:t},size:{width:null==(a=s.current)?void 0:a.width,height:null==(i=s.current)?void 0:i.height}}))},exports.Wireframe=function({geometry:e,...t}){return e?T.createElement(Nn,C.default({geometry:e},t)):T.createElement(Gn,t)},exports.accumulativeContext=rn,exports.calcPosFromAngles=Cn,exports.calculateScaleFactor=sa,exports.isWebGL2Available=()=>{try{var e=document.createElement("canvas");return!(!window.WebGL2RenderingContext||!e.getContext("webgl2"))}catch(e){return!1}},exports.meshBounds=function(e,t){const r=this.geometry,n=this.material,o=this.matrixWorld;void 0!==n&&(null===r.boundingSphere&&r.computeBoundingSphere(),Ro.copy(r.boundingSphere),Ro.applyMatrix4(o),!1!==e.ray.intersectsSphere(Ro)&&(To.copy(o).invert(),Po.copy(e.ray).applyMatrix4(To),null!==r.boundingBox&&null===Po.intersectBox(r.boundingBox,Do)||t.push({distance:Do.distanceTo(e.ray.origin),point:Do.clone(),object:this})))},exports.shaderMaterial=Ee,exports.useAnimations=function(e,t){const r=T.useRef(),[a]=T.useState((()=>t?t instanceof n.Object3D?{current:t}:t:r)),[i]=T.useState((()=>new n.AnimationMixer(void 0)));T.useLayoutEffect((()=>{i._root=a.current}),[i,t]);const s=T.useRef({}),[l]=T.useState((()=>{const t={};return e.forEach((e=>Object.defineProperty(t,e.name,{enumerable:!0,get(){if(a.current)return s.current[e.name]||(s.current[e.name]=i.clipAction(e,a.current))},configurable:!0}))),{ref:a,clips:e,actions:t,names:e.map((e=>e.name)),mixer:i}}));return o.useFrame(((e,t)=>i.update(t))),T.useEffect((()=>{const e=a.current;return()=>{s.current={},Object.values(l.actions).forEach((t=>{e&&i.uncacheAction(t,e)}))}}),[e]),T.useEffect((()=>()=>{i.stopAllAction()}),[i]),l},exports.useAspect=function(e,t,r=1){const n=o.useThree((e=>e.viewport)),a=t*(n.aspect>e/t?n.width/e:n.height/t);return[e*(n.aspect>e/t?n.width/e:n.height/t)*r,a*r,1]},exports.useBVH=function(e,t){t={strategy:b.SAH,verbose:!1,setBoundingBox:!0,maxDepth:40,maxLeafTris:10,...t},T.useEffect((()=>{if(e.current){e.current.raycast=b.acceleratedRaycast;const r=e.current.geometry;return r.computeBoundsTree=b.computeBoundsTree,r.disposeBoundsTree=b.disposeBoundsTree,r.computeBoundsTree(t),()=>{r.boundsTree&&r.disposeBoundsTree()}}}),[e,JSON.stringify(t)])},exports.useBounds=Gr,exports.useBoxProjectedEnv=function(e=new R.Vector3,t=new R.Vector3){const[r]=T.useState((()=>({position:new R.Vector3,size:new R.Vector3})));o.applyProps(r,{position:e,size:t});const n=T.useRef(null),a=T.useMemo((()=>({ref:n,onBeforeCompile:e=>function(e,t,r){e.defines.BOX_PROJECTED_ENV_MAP=!0,e.uniforms.envMapPosition={value:t},e.uniforms.envMapSize={value:r},e.vertexShader=`\n  varying vec3 vWorldPosition;\n  ${e.vertexShader.replace("#include <worldpos_vertex>","\n#if defined( USE_ENVMAP ) || defined( DISTANCE ) || defined ( USE_SHADOWMAP )\n  vec4 worldPosition = modelMatrix * vec4( transformed, 1.0 );\n  #ifdef BOX_PROJECTED_ENV_MAP\n    vWorldPosition = worldPosition.xyz;\n  #endif\n#endif\n")}`,e.fragmentShader=`\n    \n#ifdef BOX_PROJECTED_ENV_MAP\n  uniform vec3 envMapSize;\n  uniform vec3 envMapPosition;\n  varying vec3 vWorldPosition;\n    \n  vec3 parallaxCorrectNormal( vec3 v, vec3 cubeSize, vec3 cubePos ) {\n    vec3 nDir = normalize( v );\n    vec3 rbmax = ( .5 * cubeSize + cubePos - vWorldPosition ) / nDir;\n    vec3 rbmin = ( -.5 * cubeSize + cubePos - vWorldPosition ) / nDir;\n    vec3 rbminmax;\n    rbminmax.x = ( nDir.x > 0. ) ? rbmax.x : rbmin.x;\n    rbminmax.y = ( nDir.y > 0. ) ? rbmax.y : rbmin.y;\n    rbminmax.z = ( nDir.z > 0. ) ? rbmax.z : rbmin.z;\n    float correction = min( min( rbminmax.x, rbminmax.y ), rbminmax.z );\n    vec3 boxIntersection = vWorldPosition + nDir * correction;    \n    return boxIntersection - cubePos;\n  }\n#endif\n\n    ${e.fragmentShader.replace("#include <envmap_physical_pars_fragment>",R.ShaderChunk.envmap_physical_pars_fragment).replace("vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );","vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );\n         \n#ifdef BOX_PROJECTED_ENV_MAP\n  worldNormal = parallaxCorrectNormal( worldNormal, envMapSize, envMapPosition );\n#endif\n\n         ").replace("reflectVec = inverseTransformDirection( reflectVec, viewMatrix );","reflectVec = inverseTransformDirection( reflectVec, viewMatrix );\n         \n#ifdef BOX_PROJECTED_ENV_MAP\n  reflectVec = parallaxCorrectNormal( reflectVec, envMapSize, envMapPosition );\n#endif\n\n        ")}`}(e,r.position,r.size),customProgramCacheKey:()=>JSON.stringify(r.position.toArray())+JSON.stringify(r.size.toArray())})),[...r.position.toArray(),...r.size.toArray()]);return T.useLayoutEffect((()=>{n.current.needsUpdate=!0}),[r]),a},exports.useCamera=function(e,t){const r=o.useThree((e=>e.pointer)),[a]=T.useState((()=>{const a=new n.Raycaster;return t&&o.applyProps(a,t,{}),function(t,o){a.setFromCamera(r,e instanceof n.Camera?e:e.current);const i=this.constructor.prototype.raycast.bind(this);i&&i(a,o)}}));return a},exports.useContextBridge=function(...e){const t=T.useRef([]);return t.current=e.map((e=>T.useContext(e))),T.useMemo((()=>({children:r})=>e.reduceRight(((e,r,n)=>T.createElement(r.Provider,{value:t.current[n],children:e})),r)),[])},exports.useCubeCamera=Ze,exports.useCubeTexture=Bt,exports.useCursor=function(e,t="pointer",r="auto"){T.useEffect((()=>{if(e)return document.body.style.cursor=t,()=>{document.body.style.cursor=r}}),[e])},exports.useDepthBuffer=function({size:e=256,frames:t=1/0}={}){const r=o.useThree((e=>e.viewport.dpr)),{width:a,height:i}=o.useThree((e=>e.size)),s=e||a*r,l=e||i*r,c=T.useMemo((()=>{const e=new n.DepthTexture(s,l);return e.format=n.DepthFormat,e.type=n.UnsignedShortType,{depthTexture:e}}),[s,l]);let u=0;const m=Xe(s,l,c);return o.useFrame((e=>{(t===1/0||u<t)&&(e.gl.setRenderTarget(m),e.gl.render(e.scene,e.camera),e.gl.setRenderTarget(null),u++)})),m.depthTexture},exports.useDetectGPU=e=>p.suspend((()=>w.getGPUTier(e)),["useDetectGPU"]),exports.useEnvironment=Xr,exports.useFBO=Xe,exports.useFBX=Ot,exports.useFont=ye,exports.useGLTF=$e,exports.useGizmoContext=mt,exports.useHelper=jt,exports.useIntersect=function(e){const t=T.useRef(null),r=T.useRef(!1),n=T.useRef(!1),a=T.useRef(e);return T.useLayoutEffect((()=>{a.current=e}),[e]),T.useEffect((()=>{const e=t.current;if(e){const t=o.addEffect((()=>(r.current=!1,!0))),i=e.onBeforeRender;e.onBeforeRender=()=>r.current=!0;const s=o.addAfterEffect((()=>(r.current!==n.current&&(null==a.current||a.current(n.current=r.current)),!0)));return()=>{e.onBeforeRender=i,t(),s()}}}),[]),t},exports.useKTX2=Ut,exports.useKeyboardControls=function(e){const[t,r,n]=T.useContext(ae);return e?n(e):[t,r]},exports.useMask=function(e,t=!1){return{stencilWrite:!0,stencilRef:e,stencilFunc:t?R.NotEqualStencilFunc:R.EqualStencilFunc,stencilFail:R.KeepStencilOp,stencilZFail:R.KeepStencilOp,stencilZPass:R.KeepStencilOp}},exports.useMatcapTexture=function(e=0,t=1024,r){const n=p.suspend((()=>fetch("https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master/matcaps.json").then((e=>e.json()))),["matcapList"]),o=n[0],a=T.useMemo((()=>Object.keys(n).length),[]),i=`${T.useMemo((()=>"string"==typeof e?e:"number"==typeof e?n[e]:null),[e])||o}${function(e){switch(e){case 64:return"-64px";case 128:return"-128px";case 256:return"-256px";case 512:return"-512px";default:return""}}(t)}.png`,s=`https://rawcdn.githack.com/emmelleppi/matcaps/9b36ccaaf0a24881a39062d05566c9e92be4aa0d/${t}/${i}`;return[ze(s,r),s,a]},exports.useNormalTexture=function(e=0,t={},r){const{repeat:o=[1,1],anisotropy:a=1,offset:i=[0,0]}=t,s=p.suspend((()=>fetch("https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master/normals/normals.json").then((e=>e.json()))),["normalsList"]),l=T.useMemo((()=>Object.keys(s).length),[]),c=s[0],u=`https://rawcdn.githack.com/pmndrs/drei-assets/7a3104997e1576f83472829815b00880d88b32fb/normals/${s[e]||c}`,m=ze(u,r);return T.useLayoutEffect((()=>{m&&(m.wrapS=m.wrapT=n.RepeatWrapping,m.repeat=new n.Vector2(o[0],o[1]),m.offset=new n.Vector2(i[0],i[1]),m.anisotropy=a)}),[m,a,o,i]),[m,u,l]},exports.usePerformanceMonitor=function({onIncline:e,onDecline:r,onChange:n,onFallback:o}){const a=t.useContext(ko),i=t.useRef({onIncline:e,onDecline:r,onChange:n,onFallback:o});t.useLayoutEffect((()=>{i.current.onIncline=e,i.current.onDecline=r,i.current.onChange=n,i.current.onFallback=o}),[e,r,n,o]),t.useLayoutEffect((()=>a.subscribe(i)),[a])},exports.useProgress=Z,exports.useScroll=te,exports.useSelect=function(){return T.useContext(ie)},exports.useSurfaceSampler=Ae,exports.useTexture=ze,exports.useTrail=_e,exports.useTrailTexture=function(e={}){const{size:r,maxAge:n,radius:a,intensity:i,interpolate:s,smoothing:l,minForce:c,blend:u,ease:m}=e,d=t.useMemo((()=>new qt(e)),[r,n,a,i,s,l,c,u,m]);o.useFrame(((e,t)=>{d.update(t)}));const f=t.useCallback((e=>d.addTouch(e.uv)),[d]);return[d.texture,f]},exports.useVideoTexture=function(e,r){const{unsuspend:n,start:a,crossOrigin:i,muted:s,loop:l,...c}={unsuspend:"loadedmetadata",crossOrigin:"Anonymous",muted:!0,loop:!0,start:!0,playsInline:!0,...r},u=o.useThree((e=>e.gl)),m=p.suspend((()=>new Promise(((t,r)=>{const o=Object.assign(document.createElement("video"),{src:"string"==typeof e&&e||void 0,srcObject:e instanceof MediaStream&&e||void 0,crossOrigin:i,loop:l,muted:s,...c}),a=new R.VideoTexture(o);"colorSpace"in a?a.colorSpace=u.outputColorSpace:a.encoding=u.outputEncoding,o.addEventListener(n,(()=>t(a)))}))),[e]);return t.useEffect((()=>{a&&m.image.play()}),[m,a]),m};
