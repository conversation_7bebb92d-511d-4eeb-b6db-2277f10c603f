{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useFrame } from '@react-three/fiber';\nimport { useFBO } from './useFBO.js';\nimport { DiscardMaterial } from '../materials/DiscardMaterial.js';\nclass MeshTransmissionMaterialImpl extends THREE.MeshPhysicalMaterial {\n  constructor(samples = 6, transmissionSampler = false) {\n    super();\n    this.uniforms = {\n      chromaticAberration: {\n        value: 0.05\n      },\n      // Transmission must always be 0, unless transmissionSampler is being used\n      transmission: {\n        value: 0\n      },\n      // Instead a workaround is used, see below for reasons why\n      _transmission: {\n        value: 1\n      },\n      transmissionMap: {\n        value: null\n      },\n      // Roughness is 1 in THREE.MeshPhysicalMaterial but it makes little sense in a transmission material\n      roughness: {\n        value: 0\n      },\n      thickness: {\n        value: 0\n      },\n      thicknessMap: {\n        value: null\n      },\n      attenuationDistance: {\n        value: Infinity\n      },\n      attenuationColor: {\n        value: new THREE.Color('white')\n      },\n      anisotropy: {\n        value: 0.1\n      },\n      time: {\n        value: 0\n      },\n      distortion: {\n        value: 0.0\n      },\n      distortionScale: {\n        value: 0.5\n      },\n      temporalDistortion: {\n        value: 0.0\n      },\n      buffer: {\n        value: null\n      }\n    };\n    this.onBeforeCompile = shader => {\n      shader.uniforms = {\n        ...shader.uniforms,\n        ...this.uniforms\n      }; // If the transmission sampler is active inject a flag\n\n      if (transmissionSampler) shader.defines.USE_SAMPLER = ''; // Otherwise we do use use .transmission and must therefore force USE_TRANSMISSION\n      // because threejs won't inject it for us\n      else shader.defines.USE_TRANSMISSION = ''; // Head\n\n      shader.fragmentShader = /*glsl*/\n      `\n      uniform float chromaticAberration;         \n      uniform float anisotropy;      \n      uniform float time;\n      uniform float distortion;\n      uniform float distortionScale;\n      uniform float temporalDistortion;\n      uniform sampler2D buffer;\n\n      vec3 random3(vec3 c) {\n        float j = 4096.0*sin(dot(c,vec3(17.0, 59.4, 15.0)));\n        vec3 r;\n        r.z = fract(512.0*j);\n        j *= .125;\n        r.x = fract(512.0*j);\n        j *= .125;\n        r.y = fract(512.0*j);\n        return r-0.5;\n      }\n\n      float seed = 0.0;\n      uint hash( uint x ) {\n        x += ( x << 10u );\n        x ^= ( x >>  6u );\n        x += ( x <<  3u );\n        x ^= ( x >> 11u );\n        x += ( x << 15u );\n        return x;\n      }\n\n      // Compound versions of the hashing algorithm I whipped together.\n      uint hash( uvec2 v ) { return hash( v.x ^ hash(v.y)                         ); }\n      uint hash( uvec3 v ) { return hash( v.x ^ hash(v.y) ^ hash(v.z)             ); }\n      uint hash( uvec4 v ) { return hash( v.x ^ hash(v.y) ^ hash(v.z) ^ hash(v.w) ); }\n\n      // Construct a float with half-open range [0:1] using low 23 bits.\n      // All zeroes yields 0.0, all ones yields the next smallest representable value below 1.0.\n      float floatConstruct( uint m ) {\n        const uint ieeeMantissa = 0x007FFFFFu; // binary32 mantissa bitmask\n        const uint ieeeOne      = 0x3F800000u; // 1.0 in IEEE binary32\n        m &= ieeeMantissa;                     // Keep only mantissa bits (fractional part)\n        m |= ieeeOne;                          // Add fractional part to 1.0\n        float  f = uintBitsToFloat( m );       // Range [1:2]\n        return f - 1.0;                        // Range [0:1]\n      }\n\n      // Pseudo-random value in half-open range [0:1].\n      float random( float x ) { return floatConstruct(hash(floatBitsToUint(x))); }\n      float random( vec2  v ) { return floatConstruct(hash(floatBitsToUint(v))); }\n      float random( vec3  v ) { return floatConstruct(hash(floatBitsToUint(v))); }\n      float random( vec4  v ) { return floatConstruct(hash(floatBitsToUint(v))); }\n\n      float rand() {\n        float result = random(vec3(gl_FragCoord.xy, seed));\n        seed += 1.0;\n        return result;\n      }\n\n      const float F3 =  0.3333333;\n      const float G3 =  0.1666667;\n\n      float snoise(vec3 p) {\n        vec3 s = floor(p + dot(p, vec3(F3)));\n        vec3 x = p - s + dot(s, vec3(G3));\n        vec3 e = step(vec3(0.0), x - x.yzx);\n        vec3 i1 = e*(1.0 - e.zxy);\n        vec3 i2 = 1.0 - e.zxy*(1.0 - e);\n        vec3 x1 = x - i1 + G3;\n        vec3 x2 = x - i2 + 2.0*G3;\n        vec3 x3 = x - 1.0 + 3.0*G3;\n        vec4 w, d;\n        w.x = dot(x, x);\n        w.y = dot(x1, x1);\n        w.z = dot(x2, x2);\n        w.w = dot(x3, x3);\n        w = max(0.6 - w, 0.0);\n        d.x = dot(random3(s), x);\n        d.y = dot(random3(s + i1), x1);\n        d.z = dot(random3(s + i2), x2);\n        d.w = dot(random3(s + 1.0), x3);\n        w *= w;\n        w *= w;\n        d *= w;\n        return dot(d, vec4(52.0));\n      }\n\n      float snoiseFractal(vec3 m) {\n        return 0.5333333* snoise(m)\n              +0.2666667* snoise(2.0*m)\n              +0.1333333* snoise(4.0*m)\n              +0.0666667* snoise(8.0*m);\n      }\\n` + shader.fragmentShader; // Remove transmission\n\n      shader.fragmentShader = shader.fragmentShader.replace('#include <transmission_pars_fragment>', /*glsl*/\n      `\n        #ifdef USE_TRANSMISSION\n          // Transmission code is based on glTF-Sampler-Viewer\n          // https://github.com/KhronosGroup/glTF-Sample-Viewer\n          uniform float _transmission;\n          uniform float thickness;\n          uniform float attenuationDistance;\n          uniform vec3 attenuationColor;\n          #ifdef USE_TRANSMISSIONMAP\n            uniform sampler2D transmissionMap;\n          #endif\n          #ifdef USE_THICKNESSMAP\n            uniform sampler2D thicknessMap;\n          #endif\n          uniform vec2 transmissionSamplerSize;\n          uniform sampler2D transmissionSamplerMap;\n          uniform mat4 modelMatrix;\n          uniform mat4 projectionMatrix;\n          varying vec3 vWorldPosition;\n          vec3 getVolumeTransmissionRay( const in vec3 n, const in vec3 v, const in float thickness, const in float ior, const in mat4 modelMatrix ) {\n            // Direction of refracted light.\n            vec3 refractionVector = refract( - v, normalize( n ), 1.0 / ior );\n            // Compute rotation-independant scaling of the model matrix.\n            vec3 modelScale;\n            modelScale.x = length( vec3( modelMatrix[ 0 ].xyz ) );\n            modelScale.y = length( vec3( modelMatrix[ 1 ].xyz ) );\n            modelScale.z = length( vec3( modelMatrix[ 2 ].xyz ) );\n            // The thickness is specified in local space.\n            return normalize( refractionVector ) * thickness * modelScale;\n          }\n          float applyIorToRoughness( const in float roughness, const in float ior ) {\n            // Scale roughness with IOR so that an IOR of 1.0 results in no microfacet refraction and\n            // an IOR of 1.5 results in the default amount of microfacet refraction.\n            return roughness * clamp( ior * 2.0 - 2.0, 0.0, 1.0 );\n          }\n          vec4 getTransmissionSample( const in vec2 fragCoord, const in float roughness, const in float ior ) {\n            float framebufferLod = log2( transmissionSamplerSize.x ) * applyIorToRoughness( roughness, ior );            \n            #ifdef USE_SAMPLER\n              #ifdef texture2DLodEXT\n                return texture2DLodEXT(transmissionSamplerMap, fragCoord.xy, framebufferLod);\n              #else\n                return texture2D(transmissionSamplerMap, fragCoord.xy, framebufferLod);\n              #endif\n            #else\n              return texture2D(buffer, fragCoord.xy);\n            #endif\n          }\n          vec3 applyVolumeAttenuation( const in vec3 radiance, const in float transmissionDistance, const in vec3 attenuationColor, const in float attenuationDistance ) {\n            if ( isinf( attenuationDistance ) ) {\n              // Attenuation distance is +∞, i.e. the transmitted color is not attenuated at all.\n              return radiance;\n            } else {\n              // Compute light attenuation using Beer's law.\n              vec3 attenuationCoefficient = -log( attenuationColor ) / attenuationDistance;\n              vec3 transmittance = exp( - attenuationCoefficient * transmissionDistance ); // Beer's law\n              return transmittance * radiance;\n            }\n          }\n          vec4 getIBLVolumeRefraction( const in vec3 n, const in vec3 v, const in float roughness, const in vec3 diffuseColor,\n            const in vec3 specularColor, const in float specularF90, const in vec3 position, const in mat4 modelMatrix,\n            const in mat4 viewMatrix, const in mat4 projMatrix, const in float ior, const in float thickness,\n            const in vec3 attenuationColor, const in float attenuationDistance ) {\n            vec3 transmissionRay = getVolumeTransmissionRay( n, v, thickness, ior, modelMatrix );\n            vec3 refractedRayExit = position + transmissionRay;\n            // Project refracted vector on the framebuffer, while mapping to normalized device coordinates.\n            vec4 ndcPos = projMatrix * viewMatrix * vec4( refractedRayExit, 1.0 );\n            vec2 refractionCoords = ndcPos.xy / ndcPos.w;\n            refractionCoords += 1.0;\n            refractionCoords /= 2.0;\n            // Sample framebuffer to get pixel the refracted ray hits.\n            vec4 transmittedLight = getTransmissionSample( refractionCoords, roughness, ior );\n            vec3 attenuatedColor = applyVolumeAttenuation( transmittedLight.rgb, length( transmissionRay ), attenuationColor, attenuationDistance );\n            // Get the specular component.\n            vec3 F = EnvironmentBRDF( n, v, specularColor, specularF90, roughness );\n            return vec4( ( 1.0 - F ) * attenuatedColor * diffuseColor, transmittedLight.a );\n          }\n        #endif\\n`); // Add refraction\n\n      shader.fragmentShader = shader.fragmentShader.replace('#include <transmission_fragment>', /*glsl*/\n      `  \n        // Improve the refraction to use the world pos\n        material.transmission = _transmission;\n        material.transmissionAlpha = 1.0;\n        material.thickness = thickness;\n        material.attenuationDistance = attenuationDistance;\n        material.attenuationColor = attenuationColor;\n        #ifdef USE_TRANSMISSIONMAP\n          material.transmission *= texture2D( transmissionMap, vUv ).r;\n        #endif\n        #ifdef USE_THICKNESSMAP\n          material.thickness *= texture2D( thicknessMap, vUv ).g;\n        #endif\n        \n        vec3 pos = vWorldPosition;\n        vec3 v = normalize( cameraPosition - pos );\n        vec3 n = inverseTransformDirection( normal, viewMatrix );\n        vec3 transmission = vec3(0.0);\n        float transmissionR, transmissionB, transmissionG;\n        float randomCoords = rand();\n        float thickness_smear = thickness * max(pow(roughnessFactor, 0.33), anisotropy);\n        vec3 distortionNormal = vec3(0.0);\n        vec3 temporalOffset = vec3(time, -time, -time) * temporalDistortion;\n        if (distortion > 0.0) {\n          distortionNormal = distortion * vec3(snoiseFractal(vec3((pos * distortionScale + temporalOffset))), snoiseFractal(vec3(pos.zxy * distortionScale - temporalOffset)), snoiseFractal(vec3(pos.yxz * distortionScale + temporalOffset)));\n        }\n        for (float i = 0.0; i < ${samples}.0; i ++) {\n          vec3 sampleNorm = normalize(n + roughnessFactor * roughnessFactor * 2.0 * normalize(vec3(rand() - 0.5, rand() - 0.5, rand() - 0.5)) * pow(rand(), 0.33) + distortionNormal);\n          transmissionR = getIBLVolumeRefraction(\n            sampleNorm, v, material.roughness, material.diffuseColor, material.specularColor, material.specularF90,\n            pos, modelMatrix, viewMatrix, projectionMatrix, material.ior, material.thickness  + thickness_smear * (i + randomCoords) / float(${samples}),\n            material.attenuationColor, material.attenuationDistance\n          ).r;\n          transmissionG = getIBLVolumeRefraction(\n            sampleNorm, v, material.roughness, material.diffuseColor, material.specularColor, material.specularF90,\n            pos, modelMatrix, viewMatrix, projectionMatrix, material.ior  * (1.0 + chromaticAberration * (i + randomCoords) / float(${samples})) , material.thickness + thickness_smear * (i + randomCoords) / float(${samples}),\n            material.attenuationColor, material.attenuationDistance\n          ).g;\n          transmissionB = getIBLVolumeRefraction(\n            sampleNorm, v, material.roughness, material.diffuseColor, material.specularColor, material.specularF90,\n            pos, modelMatrix, viewMatrix, projectionMatrix, material.ior * (1.0 + 2.0 * chromaticAberration * (i + randomCoords) / float(${samples})), material.thickness + thickness_smear * (i + randomCoords) / float(${samples}),\n            material.attenuationColor, material.attenuationDistance\n          ).b;\n          transmission.r += transmissionR;\n          transmission.g += transmissionG;\n          transmission.b += transmissionB;\n        }\n        transmission /= ${samples}.0;\n        totalDiffuse = mix( totalDiffuse, transmission.rgb, material.transmission );\\n`);\n    };\n    Object.keys(this.uniforms).forEach(name => Object.defineProperty(this, name, {\n      get: () => this.uniforms[name].value,\n      set: v => this.uniforms[name].value = v\n    }));\n  }\n}\nconst MeshTransmissionMaterial = /*#__PURE__*/React.forwardRef(({\n  buffer,\n  transmissionSampler = false,\n  backside = false,\n  side = THREE.FrontSide,\n  transmission = 1,\n  thickness = 0,\n  backsideThickness = 0,\n  samples = 10,\n  resolution,\n  backsideResolution,\n  background,\n  ...props\n}, fref) => {\n  extend({\n    MeshTransmissionMaterial: MeshTransmissionMaterialImpl\n  });\n  const ref = React.useRef(null);\n  const [discardMaterial] = React.useState(() => new DiscardMaterial());\n  const fboBack = useFBO(backsideResolution || resolution);\n  const fboMain = useFBO(resolution);\n  let oldBg;\n  let oldTone;\n  let parent;\n  useFrame(state => {\n    ref.current.time = state.clock.getElapsedTime(); // Render only if the buffer matches the built-in and no transmission sampler is set\n\n    if (ref.current.buffer === fboMain.texture && !transmissionSampler) {\n      parent = ref.current.__r3f.parent;\n      if (parent) {\n        // Save defaults\n        oldTone = state.gl.toneMapping;\n        oldBg = state.scene.background; // Switch off tonemapping lest it double tone maps\n        // Save the current background and set the HDR as the new BG\n        // Use discardmaterial, the parent will be invisible, but it's shadows will still be cast\n\n        state.gl.toneMapping = THREE.NoToneMapping;\n        if (background) state.scene.background = background;\n        parent.material = discardMaterial;\n        if (backside) {\n          // Render into the backside buffer\n          state.gl.setRenderTarget(fboBack);\n          state.gl.render(state.scene, state.camera); // And now prepare the material for the main render using the backside buffer\n\n          parent.material = ref.current;\n          parent.material.buffer = fboBack.texture;\n          parent.material.thickness = backsideThickness;\n          parent.material.side = THREE.BackSide;\n        } // Render into the main buffer\n\n        state.gl.setRenderTarget(fboMain);\n        state.gl.render(state.scene, state.camera);\n        parent.material.thickness = thickness;\n        parent.material.side = side;\n        parent.material.buffer = fboMain.texture; // Set old state back\n\n        state.scene.background = oldBg;\n        state.gl.setRenderTarget(null);\n        parent.material = ref.current;\n        state.gl.toneMapping = oldTone;\n      }\n    }\n  }); // Forward ref\n\n  React.useImperativeHandle(fref, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"meshTransmissionMaterial\", _extends({\n    // Samples must re-compile the shader so we memoize it\n    args: [samples, transmissionSampler],\n    ref: ref\n  }, props, {\n    buffer: buffer || fboMain.texture // @ts-ignore\n    ,\n\n    _transmission: transmission // In order for this to not incur extra cost \"transmission\" must be set to 0 and treated as a reserved prop.\n    // This is because THREE.WebGLRenderer will check for transmission > 0 and execute extra renders.\n    // The exception is when transmissionSampler is set, in which case we are using three's built in sampler.\n    ,\n\n    transmission: transmissionSampler ? transmission : 0,\n    thickness: thickness,\n    side: side\n  }));\n});\nexport { MeshTransmissionMaterial };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "extend", "useFrame", "useFBO", "DiscardMaterial", "MeshTransmissionMaterialImpl", "MeshPhysicalMaterial", "constructor", "samples", "transmissionSampler", "uniforms", "chromaticAberration", "value", "transmission", "_transmission", "transmissionMap", "roughness", "thickness", "thicknessMap", "attenuationDistance", "Infinity", "attenuationColor", "Color", "anisotropy", "time", "distortion", "distortionScale", "temporalDistortion", "buffer", "onBeforeCompile", "shader", "defines", "USE_SAMPLER", "USE_TRANSMISSION", "fragmentShader", "replace", "Object", "keys", "for<PERSON>ach", "name", "defineProperty", "get", "set", "v", "MeshTransmissionMaterial", "forwardRef", "backside", "side", "FrontSide", "backsideThickness", "resolution", "backsideResolution", "background", "props", "fref", "ref", "useRef", "discardMaterial", "useState", "fboBack", "fboMain", "oldBg", "oldTone", "parent", "state", "current", "clock", "getElapsedTime", "texture", "__r3f", "gl", "toneMapping", "scene", "NoToneMapping", "material", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "render", "camera", "BackSide", "useImperativeHandle", "createElement", "args"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/MeshTransmissionMaterial.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { extend, useFrame } from '@react-three/fiber';\nimport { useFBO } from './useFBO.js';\nimport { DiscardMaterial } from '../materials/DiscardMaterial.js';\n\nclass MeshTransmissionMaterialImpl extends THREE.MeshPhysicalMaterial {\n  constructor(samples = 6, transmissionSampler = false) {\n    super();\n    this.uniforms = {\n      chromaticAberration: {\n        value: 0.05\n      },\n      // Transmission must always be 0, unless transmissionSampler is being used\n      transmission: {\n        value: 0\n      },\n      // Instead a workaround is used, see below for reasons why\n      _transmission: {\n        value: 1\n      },\n      transmissionMap: {\n        value: null\n      },\n      // Roughness is 1 in THREE.MeshPhysicalMaterial but it makes little sense in a transmission material\n      roughness: {\n        value: 0\n      },\n      thickness: {\n        value: 0\n      },\n      thicknessMap: {\n        value: null\n      },\n      attenuationDistance: {\n        value: Infinity\n      },\n      attenuationColor: {\n        value: new THREE.Color('white')\n      },\n      anisotropy: {\n        value: 0.1\n      },\n      time: {\n        value: 0\n      },\n      distortion: {\n        value: 0.0\n      },\n      distortionScale: {\n        value: 0.5\n      },\n      temporalDistortion: {\n        value: 0.0\n      },\n      buffer: {\n        value: null\n      }\n    };\n\n    this.onBeforeCompile = shader => {\n      shader.uniforms = { ...shader.uniforms,\n        ...this.uniforms\n      }; // If the transmission sampler is active inject a flag\n\n      if (transmissionSampler) shader.defines.USE_SAMPLER = ''; // Otherwise we do use use .transmission and must therefore force USE_TRANSMISSION\n      // because threejs won't inject it for us\n      else shader.defines.USE_TRANSMISSION = ''; // Head\n\n      shader.fragmentShader =\n      /*glsl*/\n      `\n      uniform float chromaticAberration;         \n      uniform float anisotropy;      \n      uniform float time;\n      uniform float distortion;\n      uniform float distortionScale;\n      uniform float temporalDistortion;\n      uniform sampler2D buffer;\n\n      vec3 random3(vec3 c) {\n        float j = 4096.0*sin(dot(c,vec3(17.0, 59.4, 15.0)));\n        vec3 r;\n        r.z = fract(512.0*j);\n        j *= .125;\n        r.x = fract(512.0*j);\n        j *= .125;\n        r.y = fract(512.0*j);\n        return r-0.5;\n      }\n\n      float seed = 0.0;\n      uint hash( uint x ) {\n        x += ( x << 10u );\n        x ^= ( x >>  6u );\n        x += ( x <<  3u );\n        x ^= ( x >> 11u );\n        x += ( x << 15u );\n        return x;\n      }\n\n      // Compound versions of the hashing algorithm I whipped together.\n      uint hash( uvec2 v ) { return hash( v.x ^ hash(v.y)                         ); }\n      uint hash( uvec3 v ) { return hash( v.x ^ hash(v.y) ^ hash(v.z)             ); }\n      uint hash( uvec4 v ) { return hash( v.x ^ hash(v.y) ^ hash(v.z) ^ hash(v.w) ); }\n\n      // Construct a float with half-open range [0:1] using low 23 bits.\n      // All zeroes yields 0.0, all ones yields the next smallest representable value below 1.0.\n      float floatConstruct( uint m ) {\n        const uint ieeeMantissa = 0x007FFFFFu; // binary32 mantissa bitmask\n        const uint ieeeOne      = 0x3F800000u; // 1.0 in IEEE binary32\n        m &= ieeeMantissa;                     // Keep only mantissa bits (fractional part)\n        m |= ieeeOne;                          // Add fractional part to 1.0\n        float  f = uintBitsToFloat( m );       // Range [1:2]\n        return f - 1.0;                        // Range [0:1]\n      }\n\n      // Pseudo-random value in half-open range [0:1].\n      float random( float x ) { return floatConstruct(hash(floatBitsToUint(x))); }\n      float random( vec2  v ) { return floatConstruct(hash(floatBitsToUint(v))); }\n      float random( vec3  v ) { return floatConstruct(hash(floatBitsToUint(v))); }\n      float random( vec4  v ) { return floatConstruct(hash(floatBitsToUint(v))); }\n\n      float rand() {\n        float result = random(vec3(gl_FragCoord.xy, seed));\n        seed += 1.0;\n        return result;\n      }\n\n      const float F3 =  0.3333333;\n      const float G3 =  0.1666667;\n\n      float snoise(vec3 p) {\n        vec3 s = floor(p + dot(p, vec3(F3)));\n        vec3 x = p - s + dot(s, vec3(G3));\n        vec3 e = step(vec3(0.0), x - x.yzx);\n        vec3 i1 = e*(1.0 - e.zxy);\n        vec3 i2 = 1.0 - e.zxy*(1.0 - e);\n        vec3 x1 = x - i1 + G3;\n        vec3 x2 = x - i2 + 2.0*G3;\n        vec3 x3 = x - 1.0 + 3.0*G3;\n        vec4 w, d;\n        w.x = dot(x, x);\n        w.y = dot(x1, x1);\n        w.z = dot(x2, x2);\n        w.w = dot(x3, x3);\n        w = max(0.6 - w, 0.0);\n        d.x = dot(random3(s), x);\n        d.y = dot(random3(s + i1), x1);\n        d.z = dot(random3(s + i2), x2);\n        d.w = dot(random3(s + 1.0), x3);\n        w *= w;\n        w *= w;\n        d *= w;\n        return dot(d, vec4(52.0));\n      }\n\n      float snoiseFractal(vec3 m) {\n        return 0.5333333* snoise(m)\n              +0.2666667* snoise(2.0*m)\n              +0.1333333* snoise(4.0*m)\n              +0.0666667* snoise(8.0*m);\n      }\\n` + shader.fragmentShader; // Remove transmission\n\n      shader.fragmentShader = shader.fragmentShader.replace('#include <transmission_pars_fragment>',\n      /*glsl*/\n      `\n        #ifdef USE_TRANSMISSION\n          // Transmission code is based on glTF-Sampler-Viewer\n          // https://github.com/KhronosGroup/glTF-Sample-Viewer\n          uniform float _transmission;\n          uniform float thickness;\n          uniform float attenuationDistance;\n          uniform vec3 attenuationColor;\n          #ifdef USE_TRANSMISSIONMAP\n            uniform sampler2D transmissionMap;\n          #endif\n          #ifdef USE_THICKNESSMAP\n            uniform sampler2D thicknessMap;\n          #endif\n          uniform vec2 transmissionSamplerSize;\n          uniform sampler2D transmissionSamplerMap;\n          uniform mat4 modelMatrix;\n          uniform mat4 projectionMatrix;\n          varying vec3 vWorldPosition;\n          vec3 getVolumeTransmissionRay( const in vec3 n, const in vec3 v, const in float thickness, const in float ior, const in mat4 modelMatrix ) {\n            // Direction of refracted light.\n            vec3 refractionVector = refract( - v, normalize( n ), 1.0 / ior );\n            // Compute rotation-independant scaling of the model matrix.\n            vec3 modelScale;\n            modelScale.x = length( vec3( modelMatrix[ 0 ].xyz ) );\n            modelScale.y = length( vec3( modelMatrix[ 1 ].xyz ) );\n            modelScale.z = length( vec3( modelMatrix[ 2 ].xyz ) );\n            // The thickness is specified in local space.\n            return normalize( refractionVector ) * thickness * modelScale;\n          }\n          float applyIorToRoughness( const in float roughness, const in float ior ) {\n            // Scale roughness with IOR so that an IOR of 1.0 results in no microfacet refraction and\n            // an IOR of 1.5 results in the default amount of microfacet refraction.\n            return roughness * clamp( ior * 2.0 - 2.0, 0.0, 1.0 );\n          }\n          vec4 getTransmissionSample( const in vec2 fragCoord, const in float roughness, const in float ior ) {\n            float framebufferLod = log2( transmissionSamplerSize.x ) * applyIorToRoughness( roughness, ior );            \n            #ifdef USE_SAMPLER\n              #ifdef texture2DLodEXT\n                return texture2DLodEXT(transmissionSamplerMap, fragCoord.xy, framebufferLod);\n              #else\n                return texture2D(transmissionSamplerMap, fragCoord.xy, framebufferLod);\n              #endif\n            #else\n              return texture2D(buffer, fragCoord.xy);\n            #endif\n          }\n          vec3 applyVolumeAttenuation( const in vec3 radiance, const in float transmissionDistance, const in vec3 attenuationColor, const in float attenuationDistance ) {\n            if ( isinf( attenuationDistance ) ) {\n              // Attenuation distance is +∞, i.e. the transmitted color is not attenuated at all.\n              return radiance;\n            } else {\n              // Compute light attenuation using Beer's law.\n              vec3 attenuationCoefficient = -log( attenuationColor ) / attenuationDistance;\n              vec3 transmittance = exp( - attenuationCoefficient * transmissionDistance ); // Beer's law\n              return transmittance * radiance;\n            }\n          }\n          vec4 getIBLVolumeRefraction( const in vec3 n, const in vec3 v, const in float roughness, const in vec3 diffuseColor,\n            const in vec3 specularColor, const in float specularF90, const in vec3 position, const in mat4 modelMatrix,\n            const in mat4 viewMatrix, const in mat4 projMatrix, const in float ior, const in float thickness,\n            const in vec3 attenuationColor, const in float attenuationDistance ) {\n            vec3 transmissionRay = getVolumeTransmissionRay( n, v, thickness, ior, modelMatrix );\n            vec3 refractedRayExit = position + transmissionRay;\n            // Project refracted vector on the framebuffer, while mapping to normalized device coordinates.\n            vec4 ndcPos = projMatrix * viewMatrix * vec4( refractedRayExit, 1.0 );\n            vec2 refractionCoords = ndcPos.xy / ndcPos.w;\n            refractionCoords += 1.0;\n            refractionCoords /= 2.0;\n            // Sample framebuffer to get pixel the refracted ray hits.\n            vec4 transmittedLight = getTransmissionSample( refractionCoords, roughness, ior );\n            vec3 attenuatedColor = applyVolumeAttenuation( transmittedLight.rgb, length( transmissionRay ), attenuationColor, attenuationDistance );\n            // Get the specular component.\n            vec3 F = EnvironmentBRDF( n, v, specularColor, specularF90, roughness );\n            return vec4( ( 1.0 - F ) * attenuatedColor * diffuseColor, transmittedLight.a );\n          }\n        #endif\\n`); // Add refraction\n\n      shader.fragmentShader = shader.fragmentShader.replace('#include <transmission_fragment>',\n      /*glsl*/\n      `  \n        // Improve the refraction to use the world pos\n        material.transmission = _transmission;\n        material.transmissionAlpha = 1.0;\n        material.thickness = thickness;\n        material.attenuationDistance = attenuationDistance;\n        material.attenuationColor = attenuationColor;\n        #ifdef USE_TRANSMISSIONMAP\n          material.transmission *= texture2D( transmissionMap, vUv ).r;\n        #endif\n        #ifdef USE_THICKNESSMAP\n          material.thickness *= texture2D( thicknessMap, vUv ).g;\n        #endif\n        \n        vec3 pos = vWorldPosition;\n        vec3 v = normalize( cameraPosition - pos );\n        vec3 n = inverseTransformDirection( normal, viewMatrix );\n        vec3 transmission = vec3(0.0);\n        float transmissionR, transmissionB, transmissionG;\n        float randomCoords = rand();\n        float thickness_smear = thickness * max(pow(roughnessFactor, 0.33), anisotropy);\n        vec3 distortionNormal = vec3(0.0);\n        vec3 temporalOffset = vec3(time, -time, -time) * temporalDistortion;\n        if (distortion > 0.0) {\n          distortionNormal = distortion * vec3(snoiseFractal(vec3((pos * distortionScale + temporalOffset))), snoiseFractal(vec3(pos.zxy * distortionScale - temporalOffset)), snoiseFractal(vec3(pos.yxz * distortionScale + temporalOffset)));\n        }\n        for (float i = 0.0; i < ${samples}.0; i ++) {\n          vec3 sampleNorm = normalize(n + roughnessFactor * roughnessFactor * 2.0 * normalize(vec3(rand() - 0.5, rand() - 0.5, rand() - 0.5)) * pow(rand(), 0.33) + distortionNormal);\n          transmissionR = getIBLVolumeRefraction(\n            sampleNorm, v, material.roughness, material.diffuseColor, material.specularColor, material.specularF90,\n            pos, modelMatrix, viewMatrix, projectionMatrix, material.ior, material.thickness  + thickness_smear * (i + randomCoords) / float(${samples}),\n            material.attenuationColor, material.attenuationDistance\n          ).r;\n          transmissionG = getIBLVolumeRefraction(\n            sampleNorm, v, material.roughness, material.diffuseColor, material.specularColor, material.specularF90,\n            pos, modelMatrix, viewMatrix, projectionMatrix, material.ior  * (1.0 + chromaticAberration * (i + randomCoords) / float(${samples})) , material.thickness + thickness_smear * (i + randomCoords) / float(${samples}),\n            material.attenuationColor, material.attenuationDistance\n          ).g;\n          transmissionB = getIBLVolumeRefraction(\n            sampleNorm, v, material.roughness, material.diffuseColor, material.specularColor, material.specularF90,\n            pos, modelMatrix, viewMatrix, projectionMatrix, material.ior * (1.0 + 2.0 * chromaticAberration * (i + randomCoords) / float(${samples})), material.thickness + thickness_smear * (i + randomCoords) / float(${samples}),\n            material.attenuationColor, material.attenuationDistance\n          ).b;\n          transmission.r += transmissionR;\n          transmission.g += transmissionG;\n          transmission.b += transmissionB;\n        }\n        transmission /= ${samples}.0;\n        totalDiffuse = mix( totalDiffuse, transmission.rgb, material.transmission );\\n`);\n    };\n\n    Object.keys(this.uniforms).forEach(name => Object.defineProperty(this, name, {\n      get: () => this.uniforms[name].value,\n      set: v => this.uniforms[name].value = v\n    }));\n  }\n\n}\n\nconst MeshTransmissionMaterial = /*#__PURE__*/React.forwardRef(({\n  buffer,\n  transmissionSampler = false,\n  backside = false,\n  side = THREE.FrontSide,\n  transmission = 1,\n  thickness = 0,\n  backsideThickness = 0,\n  samples = 10,\n  resolution,\n  backsideResolution,\n  background,\n  ...props\n}, fref) => {\n  extend({\n    MeshTransmissionMaterial: MeshTransmissionMaterialImpl\n  });\n  const ref = React.useRef(null);\n  const [discardMaterial] = React.useState(() => new DiscardMaterial());\n  const fboBack = useFBO(backsideResolution || resolution);\n  const fboMain = useFBO(resolution);\n  let oldBg;\n  let oldTone;\n  let parent;\n  useFrame(state => {\n    ref.current.time = state.clock.getElapsedTime(); // Render only if the buffer matches the built-in and no transmission sampler is set\n\n    if (ref.current.buffer === fboMain.texture && !transmissionSampler) {\n      parent = ref.current.__r3f.parent;\n\n      if (parent) {\n        // Save defaults\n        oldTone = state.gl.toneMapping;\n        oldBg = state.scene.background; // Switch off tonemapping lest it double tone maps\n        // Save the current background and set the HDR as the new BG\n        // Use discardmaterial, the parent will be invisible, but it's shadows will still be cast\n\n        state.gl.toneMapping = THREE.NoToneMapping;\n        if (background) state.scene.background = background;\n        parent.material = discardMaterial;\n\n        if (backside) {\n          // Render into the backside buffer\n          state.gl.setRenderTarget(fboBack);\n          state.gl.render(state.scene, state.camera); // And now prepare the material for the main render using the backside buffer\n\n          parent.material = ref.current;\n          parent.material.buffer = fboBack.texture;\n          parent.material.thickness = backsideThickness;\n          parent.material.side = THREE.BackSide;\n        } // Render into the main buffer\n\n\n        state.gl.setRenderTarget(fboMain);\n        state.gl.render(state.scene, state.camera);\n        parent.material.thickness = thickness;\n        parent.material.side = side;\n        parent.material.buffer = fboMain.texture; // Set old state back\n\n        state.scene.background = oldBg;\n        state.gl.setRenderTarget(null);\n        parent.material = ref.current;\n        state.gl.toneMapping = oldTone;\n      }\n    }\n  }); // Forward ref\n\n  React.useImperativeHandle(fref, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"meshTransmissionMaterial\", _extends({\n    // Samples must re-compile the shader so we memoize it\n    args: [samples, transmissionSampler],\n    ref: ref\n  }, props, {\n    buffer: buffer || fboMain.texture // @ts-ignore\n    ,\n    _transmission: transmission // In order for this to not incur extra cost \"transmission\" must be set to 0 and treated as a reserved prop.\n    // This is because THREE.WebGLRenderer will check for transmission > 0 and execute extra renders.\n    // The exception is when transmissionSampler is set, in which case we are using three's built in sampler.\n    ,\n    transmission: transmissionSampler ? transmission : 0,\n    thickness: thickness,\n    side: side\n  }));\n});\n\nexport { MeshTransmissionMaterial };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,QAAQ,oBAAoB;AACrD,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,eAAe,QAAQ,iCAAiC;AAEjE,MAAMC,4BAA4B,SAASN,KAAK,CAACO,oBAAoB,CAAC;EACpEC,WAAWA,CAACC,OAAO,GAAG,CAAC,EAAEC,mBAAmB,GAAG,KAAK,EAAE;IACpD,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,QAAQ,GAAG;MACdC,mBAAmB,EAAE;QACnBC,KAAK,EAAE;MACT,CAAC;MACD;MACAC,YAAY,EAAE;QACZD,KAAK,EAAE;MACT,CAAC;MACD;MACAE,aAAa,EAAE;QACbF,KAAK,EAAE;MACT,CAAC;MACDG,eAAe,EAAE;QACfH,KAAK,EAAE;MACT,CAAC;MACD;MACAI,SAAS,EAAE;QACTJ,KAAK,EAAE;MACT,CAAC;MACDK,SAAS,EAAE;QACTL,KAAK,EAAE;MACT,CAAC;MACDM,YAAY,EAAE;QACZN,KAAK,EAAE;MACT,CAAC;MACDO,mBAAmB,EAAE;QACnBP,KAAK,EAAEQ;MACT,CAAC;MACDC,gBAAgB,EAAE;QAChBT,KAAK,EAAE,IAAIb,KAAK,CAACuB,KAAK,CAAC,OAAO;MAChC,CAAC;MACDC,UAAU,EAAE;QACVX,KAAK,EAAE;MACT,CAAC;MACDY,IAAI,EAAE;QACJZ,KAAK,EAAE;MACT,CAAC;MACDa,UAAU,EAAE;QACVb,KAAK,EAAE;MACT,CAAC;MACDc,eAAe,EAAE;QACfd,KAAK,EAAE;MACT,CAAC;MACDe,kBAAkB,EAAE;QAClBf,KAAK,EAAE;MACT,CAAC;MACDgB,MAAM,EAAE;QACNhB,KAAK,EAAE;MACT;IACF,CAAC;IAED,IAAI,CAACiB,eAAe,GAAGC,MAAM,IAAI;MAC/BA,MAAM,CAACpB,QAAQ,GAAG;QAAE,GAAGoB,MAAM,CAACpB,QAAQ;QACpC,GAAG,IAAI,CAACA;MACV,CAAC,CAAC,CAAC;;MAEH,IAAID,mBAAmB,EAAEqB,MAAM,CAACC,OAAO,CAACC,WAAW,GAAG,EAAE,CAAC,CAAC;MAC1D;MAAA,KACKF,MAAM,CAACC,OAAO,CAACE,gBAAgB,GAAG,EAAE,CAAC,CAAC;;MAE3CH,MAAM,CAACI,cAAc,GACrB;MACA;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,GAAGJ,MAAM,CAACI,cAAc,CAAC,CAAC;;MAE9BJ,MAAM,CAACI,cAAc,GAAGJ,MAAM,CAACI,cAAc,CAACC,OAAO,CAAC,uCAAuC,EAC7F;MACA;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC,CAAC,CAAC;;MAEdL,MAAM,CAACI,cAAc,GAAGJ,MAAM,CAACI,cAAc,CAACC,OAAO,CAAC,kCAAkC,EACxF;MACA;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC3B,OAAO;AACzC;AACA;AACA;AACA,+IAA+IA,OAAO;AACtJ;AACA;AACA;AACA;AACA,sIAAsIA,OAAO,0EAA0EA,OAAO;AAC9N;AACA;AACA;AACA;AACA,2IAA2IA,OAAO,yEAAyEA,OAAO;AAClO;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0BA,OAAO;AACjC,uFAAuF,CAAC;IACpF,CAAC;IAED4B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3B,QAAQ,CAAC,CAAC4B,OAAO,CAACC,IAAI,IAAIH,MAAM,CAACI,cAAc,CAAC,IAAI,EAAED,IAAI,EAAE;MAC3EE,GAAG,EAAEA,CAAA,KAAM,IAAI,CAAC/B,QAAQ,CAAC6B,IAAI,CAAC,CAAC3B,KAAK;MACpC8B,GAAG,EAAEC,CAAC,IAAI,IAAI,CAACjC,QAAQ,CAAC6B,IAAI,CAAC,CAAC3B,KAAK,GAAG+B;IACxC,CAAC,CAAC,CAAC;EACL;AAEF;AAEA,MAAMC,wBAAwB,GAAG,aAAa5C,KAAK,CAAC6C,UAAU,CAAC,CAAC;EAC9DjB,MAAM;EACNnB,mBAAmB,GAAG,KAAK;EAC3BqC,QAAQ,GAAG,KAAK;EAChBC,IAAI,GAAGhD,KAAK,CAACiD,SAAS;EACtBnC,YAAY,GAAG,CAAC;EAChBI,SAAS,GAAG,CAAC;EACbgC,iBAAiB,GAAG,CAAC;EACrBzC,OAAO,GAAG,EAAE;EACZ0C,UAAU;EACVC,kBAAkB;EAClBC,UAAU;EACV,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACVrD,MAAM,CAAC;IACL2C,wBAAwB,EAAEvC;EAC5B,CAAC,CAAC;EACF,MAAMkD,GAAG,GAAGvD,KAAK,CAACwD,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAACC,eAAe,CAAC,GAAGzD,KAAK,CAAC0D,QAAQ,CAAC,MAAM,IAAItD,eAAe,CAAC,CAAC,CAAC;EACrE,MAAMuD,OAAO,GAAGxD,MAAM,CAACgD,kBAAkB,IAAID,UAAU,CAAC;EACxD,MAAMU,OAAO,GAAGzD,MAAM,CAAC+C,UAAU,CAAC;EAClC,IAAIW,KAAK;EACT,IAAIC,OAAO;EACX,IAAIC,MAAM;EACV7D,QAAQ,CAAC8D,KAAK,IAAI;IAChBT,GAAG,CAACU,OAAO,CAACzC,IAAI,GAAGwC,KAAK,CAACE,KAAK,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;;IAEjD,IAAIZ,GAAG,CAACU,OAAO,CAACrC,MAAM,KAAKgC,OAAO,CAACQ,OAAO,IAAI,CAAC3D,mBAAmB,EAAE;MAClEsD,MAAM,GAAGR,GAAG,CAACU,OAAO,CAACI,KAAK,CAACN,MAAM;MAEjC,IAAIA,MAAM,EAAE;QACV;QACAD,OAAO,GAAGE,KAAK,CAACM,EAAE,CAACC,WAAW;QAC9BV,KAAK,GAAGG,KAAK,CAACQ,KAAK,CAACpB,UAAU,CAAC,CAAC;QAChC;QACA;;QAEAY,KAAK,CAACM,EAAE,CAACC,WAAW,GAAGxE,KAAK,CAAC0E,aAAa;QAC1C,IAAIrB,UAAU,EAAEY,KAAK,CAACQ,KAAK,CAACpB,UAAU,GAAGA,UAAU;QACnDW,MAAM,CAACW,QAAQ,GAAGjB,eAAe;QAEjC,IAAIX,QAAQ,EAAE;UACZ;UACAkB,KAAK,CAACM,EAAE,CAACK,eAAe,CAAChB,OAAO,CAAC;UACjCK,KAAK,CAACM,EAAE,CAACM,MAAM,CAACZ,KAAK,CAACQ,KAAK,EAAER,KAAK,CAACa,MAAM,CAAC,CAAC,CAAC;;UAE5Cd,MAAM,CAACW,QAAQ,GAAGnB,GAAG,CAACU,OAAO;UAC7BF,MAAM,CAACW,QAAQ,CAAC9C,MAAM,GAAG+B,OAAO,CAACS,OAAO;UACxCL,MAAM,CAACW,QAAQ,CAACzD,SAAS,GAAGgC,iBAAiB;UAC7Cc,MAAM,CAACW,QAAQ,CAAC3B,IAAI,GAAGhD,KAAK,CAAC+E,QAAQ;QACvC,CAAC,CAAC;;QAGFd,KAAK,CAACM,EAAE,CAACK,eAAe,CAACf,OAAO,CAAC;QACjCI,KAAK,CAACM,EAAE,CAACM,MAAM,CAACZ,KAAK,CAACQ,KAAK,EAAER,KAAK,CAACa,MAAM,CAAC;QAC1Cd,MAAM,CAACW,QAAQ,CAACzD,SAAS,GAAGA,SAAS;QACrC8C,MAAM,CAACW,QAAQ,CAAC3B,IAAI,GAAGA,IAAI;QAC3BgB,MAAM,CAACW,QAAQ,CAAC9C,MAAM,GAAGgC,OAAO,CAACQ,OAAO,CAAC,CAAC;;QAE1CJ,KAAK,CAACQ,KAAK,CAACpB,UAAU,GAAGS,KAAK;QAC9BG,KAAK,CAACM,EAAE,CAACK,eAAe,CAAC,IAAI,CAAC;QAC9BZ,MAAM,CAACW,QAAQ,GAAGnB,GAAG,CAACU,OAAO;QAC7BD,KAAK,CAACM,EAAE,CAACC,WAAW,GAAGT,OAAO;MAChC;IACF;EACF,CAAC,CAAC,CAAC,CAAC;;EAEJ9D,KAAK,CAAC+E,mBAAmB,CAACzB,IAAI,EAAE,MAAMC,GAAG,CAACU,OAAO,EAAE,EAAE,CAAC;EACtD,OAAO,aAAajE,KAAK,CAACgF,aAAa,CAAC,0BAA0B,EAAElF,QAAQ,CAAC;IAC3E;IACAmF,IAAI,EAAE,CAACzE,OAAO,EAAEC,mBAAmB,CAAC;IACpC8C,GAAG,EAAEA;EACP,CAAC,EAAEF,KAAK,EAAE;IACRzB,MAAM,EAAEA,MAAM,IAAIgC,OAAO,CAACQ,OAAO,CAAC;IAAA;;IAElCtD,aAAa,EAAED,YAAY,CAAC;IAC5B;IACA;IAAA;;IAEAA,YAAY,EAAEJ,mBAAmB,GAAGI,YAAY,GAAG,CAAC;IACpDI,SAAS,EAAEA,SAAS;IACpB8B,IAAI,EAAEA;EACR,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASH,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}