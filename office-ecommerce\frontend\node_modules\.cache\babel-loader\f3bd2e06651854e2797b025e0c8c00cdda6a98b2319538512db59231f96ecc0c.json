{"ast": null, "code": "export function IS_LEAF(n16, uint16Array) {\n  return uint16Array[n16 + 15] === 0xFFFF;\n}\nexport function OFFSET(n32, uint32Array) {\n  return uint32Array[n32 + 6];\n}\nexport function COUNT(n16, uint16Array) {\n  return uint16Array[n16 + 14];\n}\nexport function LEFT_NODE(n32) {\n  return n32 + 8;\n}\nexport function RIGHT_NODE(n32, uint32Array) {\n  return uint32Array[n32 + 6];\n}\nexport function SPLIT_AXIS(n32, uint32Array) {\n  return uint32Array[n32 + 7];\n}\nexport function BOUNDING_DATA_INDEX(n32) {\n  return n32;\n}", "map": {"version": 3, "names": ["IS_LEAF", "n16", "uint16Array", "OFFSET", "n32", "uint32Array", "COUNT", "LEFT_NODE", "RIGHT_NODE", "SPLIT_AXIS", "BOUNDING_DATA_INDEX"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/three-mesh-bvh/src/core/nodeBufferFunctions.js"], "sourcesContent": ["export function IS_LEAF( n16, uint16Array ) {\n\n\treturn uint16Array[ n16 + 15 ] === 0xFFFF;\n\n}\n\nexport function OFFSET( n32, uint32Array ) {\n\n\treturn uint32Array[ n32 + 6 ];\n\n}\n\nexport function COUNT( n16, uint16Array ) {\n\n\treturn uint16Array[ n16 + 14 ];\n\n}\n\nexport function LEFT_NODE( n32 ) {\n\n\treturn n32 + 8;\n\n}\n\nexport function RIGHT_NODE( n32, uint32Array ) {\n\n\treturn uint32Array[ n32 + 6 ];\n\n}\n\nexport function SPLIT_AXIS( n32, uint32Array ) {\n\n\treturn uint32Array[ n32 + 7 ];\n\n}\n\nexport function BOUNDING_DATA_INDEX( n32 ) {\n\n\treturn n32;\n\n}\n"], "mappings": "AAAA,OAAO,SAASA,OAAOA,CAAEC,GAAG,EAAEC,WAAW,EAAG;EAE3C,OAAOA,WAAW,CAAED,GAAG,GAAG,EAAE,CAAE,KAAK,MAAM;AAE1C;AAEA,OAAO,SAASE,MAAMA,CAAEC,GAAG,EAAEC,WAAW,EAAG;EAE1C,OAAOA,WAAW,CAAED,GAAG,GAAG,CAAC,CAAE;AAE9B;AAEA,OAAO,SAASE,KAAKA,CAAEL,GAAG,EAAEC,WAAW,EAAG;EAEzC,OAAOA,WAAW,CAAED,GAAG,GAAG,EAAE,CAAE;AAE/B;AAEA,OAAO,SAASM,SAASA,CAAEH,GAAG,EAAG;EAEhC,OAAOA,GAAG,GAAG,CAAC;AAEf;AAEA,OAAO,SAASI,UAAUA,CAAEJ,GAAG,EAAEC,WAAW,EAAG;EAE9C,OAAOA,WAAW,CAAED,GAAG,GAAG,CAAC,CAAE;AAE9B;AAEA,OAAO,SAASK,UAAUA,CAAEL,GAAG,EAAEC,WAAW,EAAG;EAE9C,OAAOA,WAAW,CAAED,GAAG,GAAG,CAAC,CAAE;AAE9B;AAEA,OAAO,SAASM,mBAAmBA,CAAEN,GAAG,EAAG;EAE1C,OAAOA,GAAG;AAEX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}