{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Vector3, CylinderGeometry, Matrix4, WebGLRenderTarget, RGBAFormat, LinearEncoding, ShaderMaterial, DoubleSide, RepeatWrapping } from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { FullScreenQuad } from 'three-stdlib';\nimport mergeRefs from 'react-merge-refs';\nimport { SpotLightMaterial } from '../materials/SpotLightMaterial.js';\nimport SpotlightShadowShader from '../helpers/glsl/DefaultSpotlightShadowShadows.glsl.js';\nconst isSpotLight = child => {\n  return child == null ? void 0 : child.isSpotLight;\n};\nfunction VolumetricMesh({\n  opacity = 1,\n  radiusTop,\n  radiusBottom,\n  depthBuffer,\n  color = 'white',\n  distance = 5,\n  angle = 0.15,\n  attenuation = 5,\n  anglePower = 5\n}) {\n  const mesh = React.useRef(null);\n  const size = useThree(state => state.size);\n  const camera = useThree(state => state.camera);\n  const dpr = useThree(state => state.viewport.dpr);\n  const [material] = React.useState(() => new SpotLightMaterial());\n  const [vec] = React.useState(() => new Vector3());\n  radiusTop = radiusTop === undefined ? 0.1 : radiusTop;\n  radiusBottom = radiusBottom === undefined ? angle * 7 : radiusBottom;\n  useFrame(() => {\n    material.uniforms.spotPosition.value.copy(mesh.current.getWorldPosition(vec));\n    mesh.current.lookAt(mesh.current.parent.target.getWorldPosition(vec));\n  });\n  const geom = React.useMemo(() => {\n    const geometry = new CylinderGeometry(radiusTop, radiusBottom, distance, 128, 64, true);\n    geometry.applyMatrix4(new Matrix4().makeTranslation(0, -distance / 2, 0));\n    geometry.applyMatrix4(new Matrix4().makeRotationX(-Math.PI / 2));\n    return geometry;\n  }, [distance, radiusTop, radiusBottom]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: mesh,\n    geometry: geom,\n    raycast: () => null\n  }, /*#__PURE__*/React.createElement(\"primitive\", {\n    object: material,\n    attach: \"material\",\n    \"uniforms-opacity-value\": opacity,\n    \"uniforms-lightColor-value\": color,\n    \"uniforms-attenuation-value\": attenuation,\n    \"uniforms-anglePower-value\": anglePower,\n    \"uniforms-depth-value\": depthBuffer,\n    \"uniforms-cameraNear-value\": camera.near,\n    \"uniforms-cameraFar-value\": camera.far,\n    \"uniforms-resolution-value\": depthBuffer ? [size.width * dpr, size.height * dpr] : [0, 0]\n  })));\n}\nfunction useCommon(spotlight, mesh, width, height, distance) {\n  const [[pos, dir]] = React.useState(() => [new Vector3(), new Vector3()]);\n  React.useLayoutEffect(() => {\n    if (isSpotLight(spotlight.current)) {\n      spotlight.current.shadow.mapSize.set(width, height);\n      spotlight.current.shadow.needsUpdate = true;\n    } else {\n      throw new Error('SpotlightShadow must be a child of a SpotLight');\n    }\n  }, [spotlight, width, height]);\n  useFrame(() => {\n    if (!spotlight.current) return;\n    const A = spotlight.current.position;\n    const B = spotlight.current.target.position;\n    dir.copy(B).sub(A);\n    var len = dir.length();\n    dir.normalize().multiplyScalar(len * distance);\n    pos.copy(A).add(dir);\n    mesh.current.position.copy(pos);\n    mesh.current.lookAt(spotlight.current.target.position);\n  });\n}\nfunction SpotlightShadowWithShader({\n  distance = 0.4,\n  alphaTest = 0.5,\n  map,\n  shader = SpotlightShadowShader,\n  width = 512,\n  height = 512,\n  scale = 1,\n  children,\n  ...rest\n}) {\n  const mesh = React.useRef(null);\n  const spotlight = rest.spotlightRef;\n  const debug = rest.debug;\n  useCommon(spotlight, mesh, width, height, distance);\n  const renderTarget = React.useMemo(() => new WebGLRenderTarget(width, height, {\n    format: RGBAFormat,\n    encoding: LinearEncoding,\n    stencilBuffer: false // depthTexture: null!\n  }), [width, height]);\n  const uniforms = React.useRef({\n    uShadowMap: {\n      value: map\n    },\n    uTime: {\n      value: 0\n    }\n  });\n  React.useEffect(() => void (uniforms.current.uShadowMap.value = map), [map]);\n  const fsQuad = React.useMemo(() => new FullScreenQuad(new ShaderMaterial({\n    uniforms: uniforms.current,\n    vertexShader: /* glsl */\n    `\n          varying vec2 vUv;\n\n          void main() {\n            vUv = uv;\n            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n          }\n          `,\n    fragmentShader: shader\n  })), [shader]);\n  React.useEffect(() => () => {\n    fsQuad.material.dispose();\n    fsQuad.dispose();\n  }, [fsQuad]);\n  React.useEffect(() => () => renderTarget.dispose(), [renderTarget]);\n  useFrame(({\n    gl\n  }, dt) => {\n    uniforms.current.uTime.value += dt;\n    gl.setRenderTarget(renderTarget);\n    fsQuad.render(gl);\n    gl.setRenderTarget(null);\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: mesh,\n    scale: scale,\n    castShadow: true\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    side: DoubleSide,\n    alphaTest: alphaTest,\n    alphaMap: renderTarget.texture,\n    \"alphaMap-wrapS\": RepeatWrapping,\n    \"alphaMap-wrapT\": RepeatWrapping,\n    opacity: debug ? 1 : 0\n  }, children)));\n}\nfunction SpotlightShadowWithoutShader({\n  distance = 0.4,\n  alphaTest = 0.5,\n  map,\n  width = 512,\n  height = 512,\n  scale,\n  children,\n  ...rest\n}) {\n  const mesh = React.useRef(null);\n  const spotlight = rest.spotlightRef;\n  const debug = rest.debug;\n  useCommon(spotlight, mesh, width, height, distance);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: mesh,\n    scale: scale,\n    castShadow: true\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    side: DoubleSide,\n    alphaTest: alphaTest,\n    alphaMap: map,\n    \"alphaMap-wrapS\": RepeatWrapping,\n    \"alphaMap-wrapT\": RepeatWrapping,\n    opacity: debug ? 1 : 0\n  }, children)));\n}\nfunction SpotLightShadow(props) {\n  if (props.shader) return /*#__PURE__*/React.createElement(SpotlightShadowWithShader, props);\n  return /*#__PURE__*/React.createElement(SpotlightShadowWithoutShader, props);\n}\nconst SpotLight = /*#__PURE__*/React.forwardRef(({\n  // Volumetric\n  opacity = 1,\n  radiusTop,\n  radiusBottom,\n  depthBuffer,\n  color = 'white',\n  distance = 5,\n  angle = 0.15,\n  attenuation = 5,\n  anglePower = 5,\n  volumetric = true,\n  debug = false,\n  children,\n  ...props\n}, ref) => {\n  const spotlight = React.useRef(null);\n  return /*#__PURE__*/React.createElement(\"group\", null, debug && spotlight.current && /*#__PURE__*/React.createElement(\"spotLightHelper\", {\n    args: [spotlight.current]\n  }), /*#__PURE__*/React.createElement(\"spotLight\", _extends({\n    ref: mergeRefs([ref, spotlight]),\n    angle: angle,\n    color: color,\n    distance: distance,\n    castShadow: true\n  }, props), volumetric && /*#__PURE__*/React.createElement(VolumetricMesh, {\n    debug: debug,\n    opacity: opacity,\n    radiusTop: radiusTop,\n    radiusBottom: radiusBottom,\n    depthBuffer: depthBuffer,\n    color: color,\n    distance: distance,\n    angle: angle,\n    attenuation: attenuation,\n    anglePower: anglePower\n  })), children && /*#__PURE__*/React.cloneElement(children, {\n    spotlightRef: spotlight,\n    debug: debug\n  }));\n});\nexport { SpotLight, SpotLightShadow };", "map": {"version": 3, "names": ["_extends", "React", "Vector3", "CylinderGeometry", "Matrix4", "WebGLRenderTarget", "RGBAFormat", "LinearEncoding", "ShaderMaterial", "DoubleSide", "RepeatWrapping", "useThree", "useFrame", "FullScreenQuad", "mergeRefs", "SpotLightMaterial", "SpotlightShadowShader", "isSpotLight", "child", "VolumetricMesh", "opacity", "radiusTop", "radiusBottom", "depthBuffer", "color", "distance", "angle", "attenuation", "anglePower", "mesh", "useRef", "size", "state", "camera", "dpr", "viewport", "material", "useState", "vec", "undefined", "uniforms", "spotPosition", "value", "copy", "current", "getWorldPosition", "lookAt", "parent", "target", "geom", "useMemo", "geometry", "applyMatrix4", "makeTranslation", "makeRotationX", "Math", "PI", "createElement", "Fragment", "ref", "raycast", "object", "attach", "near", "far", "width", "height", "useCommon", "spotlight", "pos", "dir", "useLayoutEffect", "shadow", "mapSize", "set", "needsUpdate", "Error", "A", "position", "B", "sub", "len", "length", "normalize", "multiplyScalar", "add", "SpotlightShadowWithShader", "alphaTest", "map", "shader", "scale", "children", "rest", "spotlightRef", "debug", "renderTarget", "format", "encoding", "stencil<PERSON>uffer", "uShadowMap", "uTime", "useEffect", "fsQuad", "vertexShader", "fragmentShader", "dispose", "gl", "dt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "render", "<PERSON><PERSON><PERSON><PERSON>", "transparent", "side", "alphaMap", "texture", "SpotlightShadowWithoutShader", "SpotLightShadow", "props", "SpotLight", "forwardRef", "volumetric", "args", "cloneElement"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/SpotLight.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Vector3, CylinderGeometry, Matrix4, WebGLRenderTarget, RGBAFormat, LinearEncoding, ShaderMaterial, DoubleSide, RepeatWrapping } from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { FullScreenQuad } from 'three-stdlib';\nimport mergeRefs from 'react-merge-refs';\nimport { SpotLightMaterial } from '../materials/SpotLightMaterial.js';\nimport SpotlightShadowShader from '../helpers/glsl/DefaultSpotlightShadowShadows.glsl.js';\n\nconst isSpotLight = child => {\n  return child == null ? void 0 : child.isSpotLight;\n};\n\nfunction VolumetricMesh({\n  opacity = 1,\n  radiusTop,\n  radiusBottom,\n  depthBuffer,\n  color = 'white',\n  distance = 5,\n  angle = 0.15,\n  attenuation = 5,\n  anglePower = 5\n}) {\n  const mesh = React.useRef(null);\n  const size = useThree(state => state.size);\n  const camera = useThree(state => state.camera);\n  const dpr = useThree(state => state.viewport.dpr);\n  const [material] = React.useState(() => new SpotLightMaterial());\n  const [vec] = React.useState(() => new Vector3());\n  radiusTop = radiusTop === undefined ? 0.1 : radiusTop;\n  radiusBottom = radiusBottom === undefined ? angle * 7 : radiusBottom;\n  useFrame(() => {\n    material.uniforms.spotPosition.value.copy(mesh.current.getWorldPosition(vec));\n    mesh.current.lookAt(mesh.current.parent.target.getWorldPosition(vec));\n  });\n  const geom = React.useMemo(() => {\n    const geometry = new CylinderGeometry(radiusTop, radiusBottom, distance, 128, 64, true);\n    geometry.applyMatrix4(new Matrix4().makeTranslation(0, -distance / 2, 0));\n    geometry.applyMatrix4(new Matrix4().makeRotationX(-Math.PI / 2));\n    return geometry;\n  }, [distance, radiusTop, radiusBottom]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: mesh,\n    geometry: geom,\n    raycast: () => null\n  }, /*#__PURE__*/React.createElement(\"primitive\", {\n    object: material,\n    attach: \"material\",\n    \"uniforms-opacity-value\": opacity,\n    \"uniforms-lightColor-value\": color,\n    \"uniforms-attenuation-value\": attenuation,\n    \"uniforms-anglePower-value\": anglePower,\n    \"uniforms-depth-value\": depthBuffer,\n    \"uniforms-cameraNear-value\": camera.near,\n    \"uniforms-cameraFar-value\": camera.far,\n    \"uniforms-resolution-value\": depthBuffer ? [size.width * dpr, size.height * dpr] : [0, 0]\n  })));\n}\n\nfunction useCommon(spotlight, mesh, width, height, distance) {\n  const [[pos, dir]] = React.useState(() => [new Vector3(), new Vector3()]);\n  React.useLayoutEffect(() => {\n    if (isSpotLight(spotlight.current)) {\n      spotlight.current.shadow.mapSize.set(width, height);\n      spotlight.current.shadow.needsUpdate = true;\n    } else {\n      throw new Error('SpotlightShadow must be a child of a SpotLight');\n    }\n  }, [spotlight, width, height]);\n  useFrame(() => {\n    if (!spotlight.current) return;\n    const A = spotlight.current.position;\n    const B = spotlight.current.target.position;\n    dir.copy(B).sub(A);\n    var len = dir.length();\n    dir.normalize().multiplyScalar(len * distance);\n    pos.copy(A).add(dir);\n    mesh.current.position.copy(pos);\n    mesh.current.lookAt(spotlight.current.target.position);\n  });\n}\n\nfunction SpotlightShadowWithShader({\n  distance = 0.4,\n  alphaTest = 0.5,\n  map,\n  shader = SpotlightShadowShader,\n  width = 512,\n  height = 512,\n  scale = 1,\n  children,\n  ...rest\n}) {\n  const mesh = React.useRef(null);\n  const spotlight = rest.spotlightRef;\n  const debug = rest.debug;\n  useCommon(spotlight, mesh, width, height, distance);\n  const renderTarget = React.useMemo(() => new WebGLRenderTarget(width, height, {\n    format: RGBAFormat,\n    encoding: LinearEncoding,\n    stencilBuffer: false // depthTexture: null!\n\n  }), [width, height]);\n  const uniforms = React.useRef({\n    uShadowMap: {\n      value: map\n    },\n    uTime: {\n      value: 0\n    }\n  });\n  React.useEffect(() => void (uniforms.current.uShadowMap.value = map), [map]);\n  const fsQuad = React.useMemo(() => new FullScreenQuad(new ShaderMaterial({\n    uniforms: uniforms.current,\n    vertexShader:\n    /* glsl */\n    `\n          varying vec2 vUv;\n\n          void main() {\n            vUv = uv;\n            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n          }\n          `,\n    fragmentShader: shader\n  })), [shader]);\n  React.useEffect(() => () => {\n    fsQuad.material.dispose();\n    fsQuad.dispose();\n  }, [fsQuad]);\n  React.useEffect(() => () => renderTarget.dispose(), [renderTarget]);\n  useFrame(({\n    gl\n  }, dt) => {\n    uniforms.current.uTime.value += dt;\n    gl.setRenderTarget(renderTarget);\n    fsQuad.render(gl);\n    gl.setRenderTarget(null);\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: mesh,\n    scale: scale,\n    castShadow: true\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    side: DoubleSide,\n    alphaTest: alphaTest,\n    alphaMap: renderTarget.texture,\n    \"alphaMap-wrapS\": RepeatWrapping,\n    \"alphaMap-wrapT\": RepeatWrapping,\n    opacity: debug ? 1 : 0\n  }, children)));\n}\n\nfunction SpotlightShadowWithoutShader({\n  distance = 0.4,\n  alphaTest = 0.5,\n  map,\n  width = 512,\n  height = 512,\n  scale,\n  children,\n  ...rest\n}) {\n  const mesh = React.useRef(null);\n  const spotlight = rest.spotlightRef;\n  const debug = rest.debug;\n  useCommon(spotlight, mesh, width, height, distance);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: mesh,\n    scale: scale,\n    castShadow: true\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    side: DoubleSide,\n    alphaTest: alphaTest,\n    alphaMap: map,\n    \"alphaMap-wrapS\": RepeatWrapping,\n    \"alphaMap-wrapT\": RepeatWrapping,\n    opacity: debug ? 1 : 0\n  }, children)));\n}\n\nfunction SpotLightShadow(props) {\n  if (props.shader) return /*#__PURE__*/React.createElement(SpotlightShadowWithShader, props);\n  return /*#__PURE__*/React.createElement(SpotlightShadowWithoutShader, props);\n}\nconst SpotLight = /*#__PURE__*/React.forwardRef(({\n  // Volumetric\n  opacity = 1,\n  radiusTop,\n  radiusBottom,\n  depthBuffer,\n  color = 'white',\n  distance = 5,\n  angle = 0.15,\n  attenuation = 5,\n  anglePower = 5,\n  volumetric = true,\n  debug = false,\n  children,\n  ...props\n}, ref) => {\n  const spotlight = React.useRef(null);\n  return /*#__PURE__*/React.createElement(\"group\", null, debug && spotlight.current && /*#__PURE__*/React.createElement(\"spotLightHelper\", {\n    args: [spotlight.current]\n  }), /*#__PURE__*/React.createElement(\"spotLight\", _extends({\n    ref: mergeRefs([ref, spotlight]),\n    angle: angle,\n    color: color,\n    distance: distance,\n    castShadow: true\n  }, props), volumetric && /*#__PURE__*/React.createElement(VolumetricMesh, {\n    debug: debug,\n    opacity: opacity,\n    radiusTop: radiusTop,\n    radiusBottom: radiusBottom,\n    depthBuffer: depthBuffer,\n    color: color,\n    distance: distance,\n    angle: angle,\n    attenuation: attenuation,\n    anglePower: anglePower\n  })), children && /*#__PURE__*/React.cloneElement(children, {\n    spotlightRef: spotlight,\n    debug: debug\n  }));\n});\n\nexport { SpotLight, SpotLightShadow };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,cAAc,EAAEC,cAAc,EAAEC,UAAU,EAAEC,cAAc,QAAQ,OAAO;AACrJ,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,SAASC,cAAc,QAAQ,cAAc;AAC7C,OAAOC,SAAS,MAAM,kBAAkB;AACxC,SAASC,iBAAiB,QAAQ,mCAAmC;AACrE,OAAOC,qBAAqB,MAAM,uDAAuD;AAEzF,MAAMC,WAAW,GAAGC,KAAK,IAAI;EAC3B,OAAOA,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACD,WAAW;AACnD,CAAC;AAED,SAASE,cAAcA,CAAC;EACtBC,OAAO,GAAG,CAAC;EACXC,SAAS;EACTC,YAAY;EACZC,WAAW;EACXC,KAAK,GAAG,OAAO;EACfC,QAAQ,GAAG,CAAC;EACZC,KAAK,GAAG,IAAI;EACZC,WAAW,GAAG,CAAC;EACfC,UAAU,GAAG;AACf,CAAC,EAAE;EACD,MAAMC,IAAI,GAAG5B,KAAK,CAAC6B,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMC,IAAI,GAAGpB,QAAQ,CAACqB,KAAK,IAAIA,KAAK,CAACD,IAAI,CAAC;EAC1C,MAAME,MAAM,GAAGtB,QAAQ,CAACqB,KAAK,IAAIA,KAAK,CAACC,MAAM,CAAC;EAC9C,MAAMC,GAAG,GAAGvB,QAAQ,CAACqB,KAAK,IAAIA,KAAK,CAACG,QAAQ,CAACD,GAAG,CAAC;EACjD,MAAM,CAACE,QAAQ,CAAC,GAAGnC,KAAK,CAACoC,QAAQ,CAAC,MAAM,IAAItB,iBAAiB,CAAC,CAAC,CAAC;EAChE,MAAM,CAACuB,GAAG,CAAC,GAAGrC,KAAK,CAACoC,QAAQ,CAAC,MAAM,IAAInC,OAAO,CAAC,CAAC,CAAC;EACjDmB,SAAS,GAAGA,SAAS,KAAKkB,SAAS,GAAG,GAAG,GAAGlB,SAAS;EACrDC,YAAY,GAAGA,YAAY,KAAKiB,SAAS,GAAGb,KAAK,GAAG,CAAC,GAAGJ,YAAY;EACpEV,QAAQ,CAAC,MAAM;IACbwB,QAAQ,CAACI,QAAQ,CAACC,YAAY,CAACC,KAAK,CAACC,IAAI,CAACd,IAAI,CAACe,OAAO,CAACC,gBAAgB,CAACP,GAAG,CAAC,CAAC;IAC7ET,IAAI,CAACe,OAAO,CAACE,MAAM,CAACjB,IAAI,CAACe,OAAO,CAACG,MAAM,CAACC,MAAM,CAACH,gBAAgB,CAACP,GAAG,CAAC,CAAC;EACvE,CAAC,CAAC;EACF,MAAMW,IAAI,GAAGhD,KAAK,CAACiD,OAAO,CAAC,MAAM;IAC/B,MAAMC,QAAQ,GAAG,IAAIhD,gBAAgB,CAACkB,SAAS,EAAEC,YAAY,EAAEG,QAAQ,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC;IACvF0B,QAAQ,CAACC,YAAY,CAAC,IAAIhD,OAAO,CAAC,CAAC,CAACiD,eAAe,CAAC,CAAC,EAAE,CAAC5B,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IACzE0B,QAAQ,CAACC,YAAY,CAAC,IAAIhD,OAAO,CAAC,CAAC,CAACkD,aAAa,CAAC,CAACC,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,CAAC;IAChE,OAAOL,QAAQ;EACjB,CAAC,EAAE,CAAC1B,QAAQ,EAAEJ,SAAS,EAAEC,YAAY,CAAC,CAAC;EACvC,OAAO,aAAarB,KAAK,CAACwD,aAAa,CAACxD,KAAK,CAACyD,QAAQ,EAAE,IAAI,EAAE,aAAazD,KAAK,CAACwD,aAAa,CAAC,MAAM,EAAE;IACrGE,GAAG,EAAE9B,IAAI;IACTsB,QAAQ,EAAEF,IAAI;IACdW,OAAO,EAAEA,CAAA,KAAM;EACjB,CAAC,EAAE,aAAa3D,KAAK,CAACwD,aAAa,CAAC,WAAW,EAAE;IAC/CI,MAAM,EAAEzB,QAAQ;IAChB0B,MAAM,EAAE,UAAU;IAClB,wBAAwB,EAAE1C,OAAO;IACjC,2BAA2B,EAAEI,KAAK;IAClC,4BAA4B,EAAEG,WAAW;IACzC,2BAA2B,EAAEC,UAAU;IACvC,sBAAsB,EAAEL,WAAW;IACnC,2BAA2B,EAAEU,MAAM,CAAC8B,IAAI;IACxC,0BAA0B,EAAE9B,MAAM,CAAC+B,GAAG;IACtC,2BAA2B,EAAEzC,WAAW,GAAG,CAACQ,IAAI,CAACkC,KAAK,GAAG/B,GAAG,EAAEH,IAAI,CAACmC,MAAM,GAAGhC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;EAC1F,CAAC,CAAC,CAAC,CAAC;AACN;AAEA,SAASiC,SAASA,CAACC,SAAS,EAAEvC,IAAI,EAAEoC,KAAK,EAAEC,MAAM,EAAEzC,QAAQ,EAAE;EAC3D,MAAM,CAAC,CAAC4C,GAAG,EAAEC,GAAG,CAAC,CAAC,GAAGrE,KAAK,CAACoC,QAAQ,CAAC,MAAM,CAAC,IAAInC,OAAO,CAAC,CAAC,EAAE,IAAIA,OAAO,CAAC,CAAC,CAAC,CAAC;EACzED,KAAK,CAACsE,eAAe,CAAC,MAAM;IAC1B,IAAItD,WAAW,CAACmD,SAAS,CAACxB,OAAO,CAAC,EAAE;MAClCwB,SAAS,CAACxB,OAAO,CAAC4B,MAAM,CAACC,OAAO,CAACC,GAAG,CAACT,KAAK,EAAEC,MAAM,CAAC;MACnDE,SAAS,CAACxB,OAAO,CAAC4B,MAAM,CAACG,WAAW,GAAG,IAAI;IAC7C,CAAC,MAAM;MACL,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;IACnE;EACF,CAAC,EAAE,CAACR,SAAS,EAAEH,KAAK,EAAEC,MAAM,CAAC,CAAC;EAC9BtD,QAAQ,CAAC,MAAM;IACb,IAAI,CAACwD,SAAS,CAACxB,OAAO,EAAE;IACxB,MAAMiC,CAAC,GAAGT,SAAS,CAACxB,OAAO,CAACkC,QAAQ;IACpC,MAAMC,CAAC,GAAGX,SAAS,CAACxB,OAAO,CAACI,MAAM,CAAC8B,QAAQ;IAC3CR,GAAG,CAAC3B,IAAI,CAACoC,CAAC,CAAC,CAACC,GAAG,CAACH,CAAC,CAAC;IAClB,IAAII,GAAG,GAAGX,GAAG,CAACY,MAAM,CAAC,CAAC;IACtBZ,GAAG,CAACa,SAAS,CAAC,CAAC,CAACC,cAAc,CAACH,GAAG,GAAGxD,QAAQ,CAAC;IAC9C4C,GAAG,CAAC1B,IAAI,CAACkC,CAAC,CAAC,CAACQ,GAAG,CAACf,GAAG,CAAC;IACpBzC,IAAI,CAACe,OAAO,CAACkC,QAAQ,CAACnC,IAAI,CAAC0B,GAAG,CAAC;IAC/BxC,IAAI,CAACe,OAAO,CAACE,MAAM,CAACsB,SAAS,CAACxB,OAAO,CAACI,MAAM,CAAC8B,QAAQ,CAAC;EACxD,CAAC,CAAC;AACJ;AAEA,SAASQ,yBAAyBA,CAAC;EACjC7D,QAAQ,GAAG,GAAG;EACd8D,SAAS,GAAG,GAAG;EACfC,GAAG;EACHC,MAAM,GAAGzE,qBAAqB;EAC9BiD,KAAK,GAAG,GAAG;EACXC,MAAM,GAAG,GAAG;EACZwB,KAAK,GAAG,CAAC;EACTC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAE;EACD,MAAM/D,IAAI,GAAG5B,KAAK,CAAC6B,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMsC,SAAS,GAAGwB,IAAI,CAACC,YAAY;EACnC,MAAMC,KAAK,GAAGF,IAAI,CAACE,KAAK;EACxB3B,SAAS,CAACC,SAAS,EAAEvC,IAAI,EAAEoC,KAAK,EAAEC,MAAM,EAAEzC,QAAQ,CAAC;EACnD,MAAMsE,YAAY,GAAG9F,KAAK,CAACiD,OAAO,CAAC,MAAM,IAAI7C,iBAAiB,CAAC4D,KAAK,EAAEC,MAAM,EAAE;IAC5E8B,MAAM,EAAE1F,UAAU;IAClB2F,QAAQ,EAAE1F,cAAc;IACxB2F,aAAa,EAAE,KAAK,CAAC;EAEvB,CAAC,CAAC,EAAE,CAACjC,KAAK,EAAEC,MAAM,CAAC,CAAC;EACpB,MAAM1B,QAAQ,GAAGvC,KAAK,CAAC6B,MAAM,CAAC;IAC5BqE,UAAU,EAAE;MACVzD,KAAK,EAAE8C;IACT,CAAC;IACDY,KAAK,EAAE;MACL1D,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACFzC,KAAK,CAACoG,SAAS,CAAC,MAAM,MAAM7D,QAAQ,CAACI,OAAO,CAACuD,UAAU,CAACzD,KAAK,GAAG8C,GAAG,CAAC,EAAE,CAACA,GAAG,CAAC,CAAC;EAC5E,MAAMc,MAAM,GAAGrG,KAAK,CAACiD,OAAO,CAAC,MAAM,IAAIrC,cAAc,CAAC,IAAIL,cAAc,CAAC;IACvEgC,QAAQ,EAAEA,QAAQ,CAACI,OAAO;IAC1B2D,YAAY,EACZ;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;IACPC,cAAc,EAAEf;EAClB,CAAC,CAAC,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACdxF,KAAK,CAACoG,SAAS,CAAC,MAAM,MAAM;IAC1BC,MAAM,CAAClE,QAAQ,CAACqE,OAAO,CAAC,CAAC;IACzBH,MAAM,CAACG,OAAO,CAAC,CAAC;EAClB,CAAC,EAAE,CAACH,MAAM,CAAC,CAAC;EACZrG,KAAK,CAACoG,SAAS,CAAC,MAAM,MAAMN,YAAY,CAACU,OAAO,CAAC,CAAC,EAAE,CAACV,YAAY,CAAC,CAAC;EACnEnF,QAAQ,CAAC,CAAC;IACR8F;EACF,CAAC,EAAEC,EAAE,KAAK;IACRnE,QAAQ,CAACI,OAAO,CAACwD,KAAK,CAAC1D,KAAK,IAAIiE,EAAE;IAClCD,EAAE,CAACE,eAAe,CAACb,YAAY,CAAC;IAChCO,MAAM,CAACO,MAAM,CAACH,EAAE,CAAC;IACjBA,EAAE,CAACE,eAAe,CAAC,IAAI,CAAC;EAC1B,CAAC,CAAC;EACF,OAAO,aAAa3G,KAAK,CAACwD,aAAa,CAACxD,KAAK,CAACyD,QAAQ,EAAE,IAAI,EAAE,aAAazD,KAAK,CAACwD,aAAa,CAAC,MAAM,EAAE;IACrGE,GAAG,EAAE9B,IAAI;IACT6D,KAAK,EAAEA,KAAK;IACZoB,UAAU,EAAE;EACd,CAAC,EAAE,aAAa7G,KAAK,CAACwD,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,aAAaxD,KAAK,CAACwD,aAAa,CAAC,mBAAmB,EAAE;IAChHsD,WAAW,EAAE,IAAI;IACjBC,IAAI,EAAEvG,UAAU;IAChB8E,SAAS,EAAEA,SAAS;IACpB0B,QAAQ,EAAElB,YAAY,CAACmB,OAAO;IAC9B,gBAAgB,EAAExG,cAAc;IAChC,gBAAgB,EAAEA,cAAc;IAChCU,OAAO,EAAE0E,KAAK,GAAG,CAAC,GAAG;EACvB,CAAC,EAAEH,QAAQ,CAAC,CAAC,CAAC;AAChB;AAEA,SAASwB,4BAA4BA,CAAC;EACpC1F,QAAQ,GAAG,GAAG;EACd8D,SAAS,GAAG,GAAG;EACfC,GAAG;EACHvB,KAAK,GAAG,GAAG;EACXC,MAAM,GAAG,GAAG;EACZwB,KAAK;EACLC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAE;EACD,MAAM/D,IAAI,GAAG5B,KAAK,CAAC6B,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMsC,SAAS,GAAGwB,IAAI,CAACC,YAAY;EACnC,MAAMC,KAAK,GAAGF,IAAI,CAACE,KAAK;EACxB3B,SAAS,CAACC,SAAS,EAAEvC,IAAI,EAAEoC,KAAK,EAAEC,MAAM,EAAEzC,QAAQ,CAAC;EACnD,OAAO,aAAaxB,KAAK,CAACwD,aAAa,CAACxD,KAAK,CAACyD,QAAQ,EAAE,IAAI,EAAE,aAAazD,KAAK,CAACwD,aAAa,CAAC,MAAM,EAAE;IACrGE,GAAG,EAAE9B,IAAI;IACT6D,KAAK,EAAEA,KAAK;IACZoB,UAAU,EAAE;EACd,CAAC,EAAE,aAAa7G,KAAK,CAACwD,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,aAAaxD,KAAK,CAACwD,aAAa,CAAC,mBAAmB,EAAE;IAChHsD,WAAW,EAAE,IAAI;IACjBC,IAAI,EAAEvG,UAAU;IAChB8E,SAAS,EAAEA,SAAS;IACpB0B,QAAQ,EAAEzB,GAAG;IACb,gBAAgB,EAAE9E,cAAc;IAChC,gBAAgB,EAAEA,cAAc;IAChCU,OAAO,EAAE0E,KAAK,GAAG,CAAC,GAAG;EACvB,CAAC,EAAEH,QAAQ,CAAC,CAAC,CAAC;AAChB;AAEA,SAASyB,eAAeA,CAACC,KAAK,EAAE;EAC9B,IAAIA,KAAK,CAAC5B,MAAM,EAAE,OAAO,aAAaxF,KAAK,CAACwD,aAAa,CAAC6B,yBAAyB,EAAE+B,KAAK,CAAC;EAC3F,OAAO,aAAapH,KAAK,CAACwD,aAAa,CAAC0D,4BAA4B,EAAEE,KAAK,CAAC;AAC9E;AACA,MAAMC,SAAS,GAAG,aAAarH,KAAK,CAACsH,UAAU,CAAC,CAAC;EAC/C;EACAnG,OAAO,GAAG,CAAC;EACXC,SAAS;EACTC,YAAY;EACZC,WAAW;EACXC,KAAK,GAAG,OAAO;EACfC,QAAQ,GAAG,CAAC;EACZC,KAAK,GAAG,IAAI;EACZC,WAAW,GAAG,CAAC;EACfC,UAAU,GAAG,CAAC;EACd4F,UAAU,GAAG,IAAI;EACjB1B,KAAK,GAAG,KAAK;EACbH,QAAQ;EACR,GAAG0B;AACL,CAAC,EAAE1D,GAAG,KAAK;EACT,MAAMS,SAAS,GAAGnE,KAAK,CAAC6B,MAAM,CAAC,IAAI,CAAC;EACpC,OAAO,aAAa7B,KAAK,CAACwD,aAAa,CAAC,OAAO,EAAE,IAAI,EAAEqC,KAAK,IAAI1B,SAAS,CAACxB,OAAO,IAAI,aAAa3C,KAAK,CAACwD,aAAa,CAAC,iBAAiB,EAAE;IACvIgE,IAAI,EAAE,CAACrD,SAAS,CAACxB,OAAO;EAC1B,CAAC,CAAC,EAAE,aAAa3C,KAAK,CAACwD,aAAa,CAAC,WAAW,EAAEzD,QAAQ,CAAC;IACzD2D,GAAG,EAAE7C,SAAS,CAAC,CAAC6C,GAAG,EAAES,SAAS,CAAC,CAAC;IAChC1C,KAAK,EAAEA,KAAK;IACZF,KAAK,EAAEA,KAAK;IACZC,QAAQ,EAAEA,QAAQ;IAClBqF,UAAU,EAAE;EACd,CAAC,EAAEO,KAAK,CAAC,EAAEG,UAAU,IAAI,aAAavH,KAAK,CAACwD,aAAa,CAACtC,cAAc,EAAE;IACxE2E,KAAK,EAAEA,KAAK;IACZ1E,OAAO,EAAEA,OAAO;IAChBC,SAAS,EAAEA,SAAS;IACpBC,YAAY,EAAEA,YAAY;IAC1BC,WAAW,EAAEA,WAAW;IACxBC,KAAK,EAAEA,KAAK;IACZC,QAAQ,EAAEA,QAAQ;IAClBC,KAAK,EAAEA,KAAK;IACZC,WAAW,EAAEA,WAAW;IACxBC,UAAU,EAAEA;EACd,CAAC,CAAC,CAAC,EAAE+D,QAAQ,IAAI,aAAa1F,KAAK,CAACyH,YAAY,CAAC/B,QAAQ,EAAE;IACzDE,YAAY,EAAEzB,SAAS;IACvB0B,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASwB,SAAS,EAAEF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}