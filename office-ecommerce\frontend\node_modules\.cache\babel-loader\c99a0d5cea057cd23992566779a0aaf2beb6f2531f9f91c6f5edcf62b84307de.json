{"ast": null, "code": "import * as React from 'react';\nimport { useState, useContext, useRef, useLayoutEffect, createContext } from 'react';\nimport { useFrame } from '@react-three/fiber';\nconst context = /*#__PURE__*/createContext(null);\nfunction PerformanceMonitor({\n  iterations = 10,\n  ms = 250,\n  threshold = 0.75,\n  step = 0.1,\n  factor: _factor = 0.5,\n  flipflops = Infinity,\n  bounds = refreshrate => refreshrate > 100 ? [60, 100] : [40, 60],\n  onIncline,\n  onDecline,\n  onChange,\n  onFallback,\n  children\n}) {\n  const decimalPlacesRatio = Math.pow(10, 0);\n  const [api, _] = useState(() => ({\n    fps: 0,\n    index: 0,\n    factor: _factor,\n    flipped: 0,\n    refreshrate: 0,\n    fallback: false,\n    frames: [],\n    averages: [],\n    subscriptions: new Map(),\n    subscribe: ref => {\n      const key = Symbol();\n      api.subscriptions.set(key, ref.current);\n      return () => void api.subscriptions.delete(key);\n    }\n  }));\n  let lastFactor = 0;\n  useFrame(() => {\n    const {\n      frames,\n      averages\n    } = api; // If the fallback has been reached do not continue running samples\n\n    if (api.fallback) return;\n    if (averages.length < iterations) {\n      frames.push(performance.now());\n      const msPassed = frames[frames.length - 1] - frames[0];\n      if (msPassed >= ms) {\n        api.fps = Math.round(frames.length / msPassed * 1000 * decimalPlacesRatio) / decimalPlacesRatio;\n        api.refreshrate = Math.max(api.refreshrate, api.fps);\n        averages[api.index++ % iterations] = api.fps;\n        if (averages.length === iterations) {\n          const [lower, upper] = bounds(api.refreshrate);\n          const upperBounds = averages.filter(value => value >= upper);\n          const lowerBounds = averages.filter(value => value < lower); // Trigger incline when more than -threshold- avgs exceed the upper bound\n\n          if (upperBounds.length > iterations * threshold) {\n            api.factor = Math.min(1, api.factor + step);\n            api.flipped++;\n            if (onIncline) onIncline(api);\n            api.subscriptions.forEach(value => value.onIncline && value.onIncline(api));\n          } // Trigger decline when more than -threshold- avgs are below the lower bound\n\n          if (lowerBounds.length > iterations * threshold) {\n            api.factor = Math.max(0, api.factor - step);\n            api.flipped++;\n            if (onDecline) onDecline(api);\n            api.subscriptions.forEach(value => value.onDecline && value.onDecline(api));\n          }\n          if (lastFactor !== api.factor) {\n            lastFactor = api.factor;\n            if (onChange) onChange(api);\n            api.subscriptions.forEach(value => value.onChange && value.onChange(api));\n          }\n          if (api.flipped > flipflops && !api.fallback) {\n            api.fallback = true;\n            if (onFallback) onFallback(api);\n            api.subscriptions.forEach(value => value.onFallback && value.onFallback(api));\n          }\n          api.averages = []; // Resetting the refreshrate creates more problems than it solves atm\n          // api.refreshrate = 0\n        }\n        api.frames = [];\n      }\n    }\n  });\n  return /*#__PURE__*/React.createElement(context.Provider, {\n    value: api\n  }, children);\n}\nfunction usePerformanceMonitor({\n  onIncline,\n  onDecline,\n  onChange,\n  onFallback\n}) {\n  const api = useContext(context);\n  const ref = useRef({\n    onIncline,\n    onDecline,\n    onChange,\n    onFallback\n  });\n  useLayoutEffect(() => {\n    ref.current.onIncline = onIncline;\n    ref.current.onDecline = onDecline;\n    ref.current.onChange = onChange;\n    ref.current.onFallback = onFallback;\n  }, [onIncline, onDecline, onChange, onFallback]);\n  useLayoutEffect(() => api.subscribe(ref), [api]);\n}\nexport { PerformanceMonitor, usePerformanceMonitor };", "map": {"version": 3, "names": ["React", "useState", "useContext", "useRef", "useLayoutEffect", "createContext", "useFrame", "context", "PerformanceMonitor", "iterations", "ms", "threshold", "step", "factor", "_factor", "flipflops", "Infinity", "bounds", "refreshrate", "onIncline", "onDecline", "onChange", "onFallback", "children", "decimalPlacesRatio", "Math", "pow", "api", "_", "fps", "index", "flipped", "fallback", "frames", "averages", "subscriptions", "Map", "subscribe", "ref", "key", "Symbol", "set", "current", "delete", "lastFactor", "length", "push", "performance", "now", "msPassed", "round", "max", "lower", "upper", "upperBounds", "filter", "value", "lowerBounds", "min", "for<PERSON>ach", "createElement", "Provider", "usePerformanceMonitor"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/PerformanceMonitor.js"], "sourcesContent": ["import * as React from 'react';\nimport { useState, useContext, useRef, useLayoutEffect, createContext } from 'react';\nimport { useFrame } from '@react-three/fiber';\n\nconst context = /*#__PURE__*/createContext(null);\nfunction PerformanceMonitor({\n  iterations = 10,\n  ms = 250,\n  threshold = 0.75,\n  step = 0.1,\n  factor: _factor = 0.5,\n  flipflops = Infinity,\n  bounds = refreshrate => refreshrate > 100 ? [60, 100] : [40, 60],\n  onIncline,\n  onDecline,\n  onChange,\n  onFallback,\n  children\n}) {\n  const decimalPlacesRatio = Math.pow(10, 0);\n  const [api, _] = useState(() => ({\n    fps: 0,\n    index: 0,\n    factor: _factor,\n    flipped: 0,\n    refreshrate: 0,\n    fallback: false,\n    frames: [],\n    averages: [],\n    subscriptions: new Map(),\n    subscribe: ref => {\n      const key = Symbol();\n      api.subscriptions.set(key, ref.current);\n      return () => void api.subscriptions.delete(key);\n    }\n  }));\n  let lastFactor = 0;\n  useFrame(() => {\n    const {\n      frames,\n      averages\n    } = api; // If the fallback has been reached do not continue running samples\n\n    if (api.fallback) return;\n\n    if (averages.length < iterations) {\n      frames.push(performance.now());\n      const msPassed = frames[frames.length - 1] - frames[0];\n\n      if (msPassed >= ms) {\n        api.fps = Math.round(frames.length / msPassed * 1000 * decimalPlacesRatio) / decimalPlacesRatio;\n        api.refreshrate = Math.max(api.refreshrate, api.fps);\n        averages[api.index++ % iterations] = api.fps;\n\n        if (averages.length === iterations) {\n          const [lower, upper] = bounds(api.refreshrate);\n          const upperBounds = averages.filter(value => value >= upper);\n          const lowerBounds = averages.filter(value => value < lower); // Trigger incline when more than -threshold- avgs exceed the upper bound\n\n          if (upperBounds.length > iterations * threshold) {\n            api.factor = Math.min(1, api.factor + step);\n            api.flipped++;\n            if (onIncline) onIncline(api);\n            api.subscriptions.forEach(value => value.onIncline && value.onIncline(api));\n          } // Trigger decline when more than -threshold- avgs are below the lower bound\n\n\n          if (lowerBounds.length > iterations * threshold) {\n            api.factor = Math.max(0, api.factor - step);\n            api.flipped++;\n            if (onDecline) onDecline(api);\n            api.subscriptions.forEach(value => value.onDecline && value.onDecline(api));\n          }\n\n          if (lastFactor !== api.factor) {\n            lastFactor = api.factor;\n            if (onChange) onChange(api);\n            api.subscriptions.forEach(value => value.onChange && value.onChange(api));\n          }\n\n          if (api.flipped > flipflops && !api.fallback) {\n            api.fallback = true;\n            if (onFallback) onFallback(api);\n            api.subscriptions.forEach(value => value.onFallback && value.onFallback(api));\n          }\n\n          api.averages = []; // Resetting the refreshrate creates more problems than it solves atm\n          // api.refreshrate = 0\n        }\n\n        api.frames = [];\n      }\n    }\n  });\n  return /*#__PURE__*/React.createElement(context.Provider, {\n    value: api\n  }, children);\n}\nfunction usePerformanceMonitor({\n  onIncline,\n  onDecline,\n  onChange,\n  onFallback\n}) {\n  const api = useContext(context);\n  const ref = useRef({\n    onIncline,\n    onDecline,\n    onChange,\n    onFallback\n  });\n  useLayoutEffect(() => {\n    ref.current.onIncline = onIncline;\n    ref.current.onDecline = onDecline;\n    ref.current.onChange = onChange;\n    ref.current.onFallback = onFallback;\n  }, [onIncline, onDecline, onChange, onFallback]);\n  useLayoutEffect(() => api.subscribe(ref), [api]);\n}\n\nexport { PerformanceMonitor, usePerformanceMonitor };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,UAAU,EAAEC,MAAM,EAAEC,eAAe,EAAEC,aAAa,QAAQ,OAAO;AACpF,SAASC,QAAQ,QAAQ,oBAAoB;AAE7C,MAAMC,OAAO,GAAG,aAAaF,aAAa,CAAC,IAAI,CAAC;AAChD,SAASG,kBAAkBA,CAAC;EAC1BC,UAAU,GAAG,EAAE;EACfC,EAAE,GAAG,GAAG;EACRC,SAAS,GAAG,IAAI;EAChBC,IAAI,GAAG,GAAG;EACVC,MAAM,EAAEC,OAAO,GAAG,GAAG;EACrBC,SAAS,GAAGC,QAAQ;EACpBC,MAAM,GAAGC,WAAW,IAAIA,WAAW,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;EAChEC,SAAS;EACTC,SAAS;EACTC,QAAQ;EACRC,UAAU;EACVC;AACF,CAAC,EAAE;EACD,MAAMC,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;EAC1C,MAAM,CAACC,GAAG,EAAEC,CAAC,CAAC,GAAG3B,QAAQ,CAAC,OAAO;IAC/B4B,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRjB,MAAM,EAAEC,OAAO;IACfiB,OAAO,EAAE,CAAC;IACVb,WAAW,EAAE,CAAC;IACdc,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,IAAIC,GAAG,CAAC,CAAC;IACxBC,SAAS,EAAEC,GAAG,IAAI;MAChB,MAAMC,GAAG,GAAGC,MAAM,CAAC,CAAC;MACpBb,GAAG,CAACQ,aAAa,CAACM,GAAG,CAACF,GAAG,EAAED,GAAG,CAACI,OAAO,CAAC;MACvC,OAAO,MAAM,KAAKf,GAAG,CAACQ,aAAa,CAACQ,MAAM,CAACJ,GAAG,CAAC;IACjD;EACF,CAAC,CAAC,CAAC;EACH,IAAIK,UAAU,GAAG,CAAC;EAClBtC,QAAQ,CAAC,MAAM;IACb,MAAM;MACJ2B,MAAM;MACNC;IACF,CAAC,GAAGP,GAAG,CAAC,CAAC;;IAET,IAAIA,GAAG,CAACK,QAAQ,EAAE;IAElB,IAAIE,QAAQ,CAACW,MAAM,GAAGpC,UAAU,EAAE;MAChCwB,MAAM,CAACa,IAAI,CAACC,WAAW,CAACC,GAAG,CAAC,CAAC,CAAC;MAC9B,MAAMC,QAAQ,GAAGhB,MAAM,CAACA,MAAM,CAACY,MAAM,GAAG,CAAC,CAAC,GAAGZ,MAAM,CAAC,CAAC,CAAC;MAEtD,IAAIgB,QAAQ,IAAIvC,EAAE,EAAE;QAClBiB,GAAG,CAACE,GAAG,GAAGJ,IAAI,CAACyB,KAAK,CAACjB,MAAM,CAACY,MAAM,GAAGI,QAAQ,GAAG,IAAI,GAAGzB,kBAAkB,CAAC,GAAGA,kBAAkB;QAC/FG,GAAG,CAACT,WAAW,GAAGO,IAAI,CAAC0B,GAAG,CAACxB,GAAG,CAACT,WAAW,EAAES,GAAG,CAACE,GAAG,CAAC;QACpDK,QAAQ,CAACP,GAAG,CAACG,KAAK,EAAE,GAAGrB,UAAU,CAAC,GAAGkB,GAAG,CAACE,GAAG;QAE5C,IAAIK,QAAQ,CAACW,MAAM,KAAKpC,UAAU,EAAE;UAClC,MAAM,CAAC2C,KAAK,EAAEC,KAAK,CAAC,GAAGpC,MAAM,CAACU,GAAG,CAACT,WAAW,CAAC;UAC9C,MAAMoC,WAAW,GAAGpB,QAAQ,CAACqB,MAAM,CAACC,KAAK,IAAIA,KAAK,IAAIH,KAAK,CAAC;UAC5D,MAAMI,WAAW,GAAGvB,QAAQ,CAACqB,MAAM,CAACC,KAAK,IAAIA,KAAK,GAAGJ,KAAK,CAAC,CAAC,CAAC;;UAE7D,IAAIE,WAAW,CAACT,MAAM,GAAGpC,UAAU,GAAGE,SAAS,EAAE;YAC/CgB,GAAG,CAACd,MAAM,GAAGY,IAAI,CAACiC,GAAG,CAAC,CAAC,EAAE/B,GAAG,CAACd,MAAM,GAAGD,IAAI,CAAC;YAC3Ce,GAAG,CAACI,OAAO,EAAE;YACb,IAAIZ,SAAS,EAAEA,SAAS,CAACQ,GAAG,CAAC;YAC7BA,GAAG,CAACQ,aAAa,CAACwB,OAAO,CAACH,KAAK,IAAIA,KAAK,CAACrC,SAAS,IAAIqC,KAAK,CAACrC,SAAS,CAACQ,GAAG,CAAC,CAAC;UAC7E,CAAC,CAAC;;UAGF,IAAI8B,WAAW,CAACZ,MAAM,GAAGpC,UAAU,GAAGE,SAAS,EAAE;YAC/CgB,GAAG,CAACd,MAAM,GAAGY,IAAI,CAAC0B,GAAG,CAAC,CAAC,EAAExB,GAAG,CAACd,MAAM,GAAGD,IAAI,CAAC;YAC3Ce,GAAG,CAACI,OAAO,EAAE;YACb,IAAIX,SAAS,EAAEA,SAAS,CAACO,GAAG,CAAC;YAC7BA,GAAG,CAACQ,aAAa,CAACwB,OAAO,CAACH,KAAK,IAAIA,KAAK,CAACpC,SAAS,IAAIoC,KAAK,CAACpC,SAAS,CAACO,GAAG,CAAC,CAAC;UAC7E;UAEA,IAAIiB,UAAU,KAAKjB,GAAG,CAACd,MAAM,EAAE;YAC7B+B,UAAU,GAAGjB,GAAG,CAACd,MAAM;YACvB,IAAIQ,QAAQ,EAAEA,QAAQ,CAACM,GAAG,CAAC;YAC3BA,GAAG,CAACQ,aAAa,CAACwB,OAAO,CAACH,KAAK,IAAIA,KAAK,CAACnC,QAAQ,IAAImC,KAAK,CAACnC,QAAQ,CAACM,GAAG,CAAC,CAAC;UAC3E;UAEA,IAAIA,GAAG,CAACI,OAAO,GAAGhB,SAAS,IAAI,CAACY,GAAG,CAACK,QAAQ,EAAE;YAC5CL,GAAG,CAACK,QAAQ,GAAG,IAAI;YACnB,IAAIV,UAAU,EAAEA,UAAU,CAACK,GAAG,CAAC;YAC/BA,GAAG,CAACQ,aAAa,CAACwB,OAAO,CAACH,KAAK,IAAIA,KAAK,CAAClC,UAAU,IAAIkC,KAAK,CAAClC,UAAU,CAACK,GAAG,CAAC,CAAC;UAC/E;UAEAA,GAAG,CAACO,QAAQ,GAAG,EAAE,CAAC,CAAC;UACnB;QACF;QAEAP,GAAG,CAACM,MAAM,GAAG,EAAE;MACjB;IACF;EACF,CAAC,CAAC;EACF,OAAO,aAAajC,KAAK,CAAC4D,aAAa,CAACrD,OAAO,CAACsD,QAAQ,EAAE;IACxDL,KAAK,EAAE7B;EACT,CAAC,EAAEJ,QAAQ,CAAC;AACd;AACA,SAASuC,qBAAqBA,CAAC;EAC7B3C,SAAS;EACTC,SAAS;EACTC,QAAQ;EACRC;AACF,CAAC,EAAE;EACD,MAAMK,GAAG,GAAGzB,UAAU,CAACK,OAAO,CAAC;EAC/B,MAAM+B,GAAG,GAAGnC,MAAM,CAAC;IACjBgB,SAAS;IACTC,SAAS;IACTC,QAAQ;IACRC;EACF,CAAC,CAAC;EACFlB,eAAe,CAAC,MAAM;IACpBkC,GAAG,CAACI,OAAO,CAACvB,SAAS,GAAGA,SAAS;IACjCmB,GAAG,CAACI,OAAO,CAACtB,SAAS,GAAGA,SAAS;IACjCkB,GAAG,CAACI,OAAO,CAACrB,QAAQ,GAAGA,QAAQ;IAC/BiB,GAAG,CAACI,OAAO,CAACpB,UAAU,GAAGA,UAAU;EACrC,CAAC,EAAE,CAACH,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,CAAC,CAAC;EAChDlB,eAAe,CAAC,MAAMuB,GAAG,CAACU,SAAS,CAACC,GAAG,CAAC,EAAE,CAACX,GAAG,CAAC,CAAC;AAClD;AAEA,SAASnB,kBAAkB,EAAEsD,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}