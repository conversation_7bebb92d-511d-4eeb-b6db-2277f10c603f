"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("three"),o=require("@react-three/fiber");function r(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(o){if("default"!==o){var r=Object.getOwnPropertyDescriptor(e,o);Object.defineProperty(t,o,r.get?r:{enumerable:!0,get:function(){return e[o]}})}})),t.default=e,Object.freeze(t)}var a=r(e),n=r(t);const i=e=>e&&e.isOrthographicCamera,c=a.createContext(null);exports.Bounds=function({children:e,damping:t=6,fit:r,clip:s,observe:u,margin:m=1.2,eps:f=.01,onFit:p}){const l=a.useRef(null),{camera:x,invalidate:y,size:z,controls:d}=o.useThree(),h=d,g=a.useRef(p);function M(e,t){return Math.abs(e.x-t.x)<f&&Math.abs(e.y-t.y)<f&&Math.abs(e.z-t.z)<f}function b(e,t,o,r){e.x=n.MathUtils.damp(e.x,t.x,o,r),e.y=n.MathUtils.damp(e.y,t.y,o,r),e.z=n.MathUtils.damp(e.z,t.z,o,r)}g.current=p;const[w]=a.useState((()=>({animating:!1,focus:new n.Vector3,camera:new n.Vector3,zoom:1}))),[v]=a.useState((()=>({focus:new n.Vector3,camera:new n.Vector3,zoom:1}))),[V]=a.useState((()=>new n.Box3)),j=a.useMemo((()=>{function e(){const e=V.getSize(new n.Vector3),t=V.getCenter(new n.Vector3),o=Math.max(e.x,e.y,e.z),r=i(x)?4*o:o/(2*Math.atan(Math.PI*x.fov/360)),a=i(x)?4*o:r/x.aspect,c=m*Math.max(r,a);return{box:V,size:e,center:t,distance:c}}return{getSize:e,refresh(t){if((o=t)&&o.isBox3)V.copy(t);else{const e=t||l.current;e.updateWorldMatrix(!0,!0),V.setFromObject(e)}var o;if(V.isEmpty()){const e=x.position.length()||10;V.setFromCenterAndSize(new n.Vector3,new n.Vector3(e,e,e))}if("OrthographicTrackballControls"===(null==h?void 0:h.constructor.name)){const{distance:t}=e(),o=x.position.clone().sub(h.target).normalize().multiplyScalar(t),r=h.target.clone().add(o);x.position.copy(r)}return this},clip(){const{distance:t}=e();return h&&(h.maxDistance=10*t),x.near=t/100,x.far=100*t,x.updateProjectionMatrix(),h&&h.update(),y(),this},to({position:o,target:r}){w.camera.copy(x.position);const{center:a}=e();return v.camera.set(...o),r?v.focus.set(...r):v.focus.copy(a),t?w.animating=!0:x.position.set(...o),this},fit(){w.camera.copy(x.position),h&&w.focus.copy(h.target);const{center:o,distance:r}=e(),a=o.clone().sub(x.position).normalize().multiplyScalar(r);if(v.camera.copy(o).sub(a),v.focus.copy(o),i(x)){w.zoom=x.zoom;let e=0,r=0;const a=[new n.Vector3(V.min.x,V.min.y,V.min.z),new n.Vector3(V.min.x,V.max.y,V.min.z),new n.Vector3(V.min.x,V.min.y,V.max.z),new n.Vector3(V.min.x,V.max.y,V.max.z),new n.Vector3(V.max.x,V.max.y,V.max.z),new n.Vector3(V.max.x,V.max.y,V.min.z),new n.Vector3(V.max.x,V.min.y,V.max.z),new n.Vector3(V.max.x,V.min.y,V.min.z)];o.applyMatrix4(x.matrixWorldInverse);for(const t of a)t.applyMatrix4(x.matrixWorldInverse),e=Math.max(e,Math.abs(t.y-o.y)),r=Math.max(r,Math.abs(t.x-o.x));e*=2,r*=2;const i=(x.top-x.bottom)/e,c=(x.right-x.left)/r;v.zoom=Math.min(i,c)/m,t||(x.zoom=v.zoom,x.updateProjectionMatrix())}return t?w.animating=!0:(x.position.copy(v.camera),x.lookAt(v.focus),h&&(h.target.copy(v.focus),h.update())),g.current&&g.current(this.getSize()),y(),this}}}),[V,x,h,m,t,y]);a.useLayoutEffect((()=>{if(h){const e=()=>w.animating=!1;return h.addEventListener("start",e),()=>h.removeEventListener("start",e)}}),[h]);const O=a.useRef(0);return a.useLayoutEffect((()=>{(u||0==O.current++)&&(j.refresh(),r&&j.fit(),s&&j.clip())}),[z,s,r,u,x,h]),o.useFrame(((e,o)=>{if(w.animating){if(b(w.focus,v.focus,t,o),b(w.camera,v.camera,t,o),w.zoom=n.MathUtils.damp(w.zoom,v.zoom,t,o),x.position.copy(w.camera),i(x)&&(x.zoom=w.zoom,x.updateProjectionMatrix()),h?(h.target.copy(w.focus),h.update()):x.lookAt(w.focus),y(),i(x)&&!(Math.abs(w.zoom-v.zoom)<f))return;if(!i(x)&&!M(w.camera,v.camera))return;if(h&&!M(w.focus,v.focus))return;w.animating=!1}})),a.createElement("group",{ref:l},a.createElement(c.Provider,{value:j},e))},exports.useBounds=function(){return a.useContext(c)};
