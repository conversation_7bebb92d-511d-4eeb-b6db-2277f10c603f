{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\admin\\\\ProductManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { toast } from 'react-toastify';\nimport ProductFormModal from './modals/ProductFormModal';\nimport ProductDetailsModal from './modals/ProductDetailsModal';\nimport ConfirmationModal from '../modals/ConfirmationModal';\nimport { productsApi } from '../../services/api';\nimport websocketService from '../../services/websocketService';\nimport './ProductManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductManagement = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('');\n  const [sortBy, setSortBy] = useState('ProductName');\n  const [sortDirection, setSortDirection] = useState('ASC');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(20);\n  const [totalCount, setTotalCount] = useState(0);\n  const [totalPages, setTotalPages] = useState(0);\n\n  // Modal states\n  const [showProductForm, setShowProductForm] = useState(false);\n  const [showProductDetails, setShowProductDetails] = useState(false);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [editingProduct, setEditingProduct] = useState(null);\n\n  // Fetch products with filters and pagination\n  const fetchProducts = useCallback(async () => {\n    try {\n      setLoading(true);\n      const params = {\n        page: currentPage,\n        limit: pageSize,\n        search: searchTerm || undefined,\n        category: selectedCategory || undefined,\n        status: selectedStatus || undefined,\n        sortBy,\n        sortDirection\n      };\n      const response = await productsApi.getProducts(params);\n      if (response.success) {\n        var _response$data$pagina, _response$data$pagina2;\n        setProducts(response.data.products || []);\n        setTotalCount(((_response$data$pagina = response.data.pagination) === null || _response$data$pagina === void 0 ? void 0 : _response$data$pagina.totalItems) || 0);\n        setTotalPages(((_response$data$pagina2 = response.data.pagination) === null || _response$data$pagina2 === void 0 ? void 0 : _response$data$pagina2.totalPages) || 0);\n      } else {\n        toast.error('Failed to fetch products');\n      }\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      toast.error('Error loading products');\n    } finally {\n      setLoading(false);\n    }\n  }, [currentPage, pageSize, searchTerm, selectedCategory, selectedStatus, sortBy, sortDirection]);\n\n  // Fetch categories\n  const fetchCategories = useCallback(async () => {\n    try {\n      const response = await productsApi.getCategories();\n      if (response.success) {\n        setCategories(response.data);\n      }\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n    }\n  }, []);\n  useEffect(() => {\n    fetchProducts();\n  }, [fetchProducts]);\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n\n  // WebSocket integration for real-time updates\n  useEffect(() => {\n    // Subscribe to product updates\n    websocketService.subscribeToProducts();\n\n    // Set up event listeners\n    const handleProductCreated = data => {\n      toast.success(`New product \"${data.ProductName}\" has been created`);\n      fetchProducts(); // Refresh the product list\n    };\n    const handleProductUpdated = data => {\n      toast.info(`Product \"${data.ProductName}\" has been updated`);\n      fetchProducts(); // Refresh the product list\n    };\n    const handleProductDeleted = data => {\n      toast.warning(`Product has been deleted`);\n      fetchProducts(); // Refresh the product list\n    };\n    const handleProductFileUploaded = data => {\n      toast.success(`File uploaded for product ID: ${data.productId}`);\n      // Optionally refresh specific product or entire list\n      fetchProducts();\n    };\n\n    // Register event listeners\n    websocketService.on('productCreated', handleProductCreated);\n    websocketService.on('productUpdated', handleProductUpdated);\n    websocketService.on('productDeleted', handleProductDeleted);\n    websocketService.on('productFileUploaded', handleProductFileUploaded);\n\n    // Cleanup function\n    return () => {\n      websocketService.off('productCreated', handleProductCreated);\n      websocketService.off('productUpdated', handleProductUpdated);\n      websocketService.off('productDeleted', handleProductDeleted);\n      websocketService.off('productFileUploaded', handleProductFileUploaded);\n    };\n  }, [fetchProducts]);\n\n  // Handle search with debouncing\n  const handleSearch = useCallback(value => {\n    setSearchTerm(value);\n    setCurrentPage(1);\n  }, []);\n\n  // Handle sorting\n  const handleSort = field => {\n    if (sortBy === field) {\n      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');\n    } else {\n      setSortBy(field);\n      setSortDirection('ASC');\n    }\n    setCurrentPage(1);\n  };\n\n  // Handle product actions\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setShowProductForm(true);\n  };\n  const handleEditProduct = product => {\n    setEditingProduct(product);\n    setShowProductForm(true);\n  };\n  const handleViewProduct = async productId => {\n    try {\n      const response = await productsApi.getProductById(productId);\n      if (response.success) {\n        setSelectedProduct(response.data);\n        setShowProductDetails(true);\n      } else {\n        toast.error('Failed to load product details');\n      }\n    } catch (error) {\n      console.error('Error fetching product details:', error);\n      toast.error('Error loading product details');\n    }\n  };\n  const handleDeleteProduct = product => {\n    setSelectedProduct(product);\n    setShowDeleteConfirm(true);\n  };\n  const confirmDeleteProduct = async () => {\n    try {\n      const response = await productsApi.deleteProduct(selectedProduct.ProductID);\n      if (response.success) {\n        // Emit WebSocket event for real-time updates\n        websocketService.notifyProductDeleted(selectedProduct.ProductID);\n        toast.success('Product deleted successfully');\n        fetchProducts();\n      } else {\n        toast.error('Failed to delete product');\n      }\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      toast.error('Error deleting product');\n    } finally {\n      setShowDeleteConfirm(false);\n      setSelectedProduct(null);\n    }\n  };\n  const handleProductSaved = productData => {\n    setShowProductForm(false);\n    setEditingProduct(null);\n    fetchProducts();\n\n    // Emit WebSocket event for real-time updates\n    if (editingProduct) {\n      websocketService.notifyProductUpdated(productData);\n      toast.success('Product updated successfully');\n    } else {\n      websocketService.notifyProductCreated(productData);\n      toast.success('Product created successfully');\n    }\n  };\n\n  // Utility functions\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n  const getStatusBadgeColor = status => {\n    switch (status) {\n      case 'Active':\n        return '#27ae60';\n      case 'Draft':\n        return '#f39c12';\n      case 'Inactive':\n        return '#95a5a6';\n      case 'Discontinued':\n        return '#e74c3c';\n      case 'Pending Review':\n        return '#3498db';\n      default:\n        return '#95a5a6';\n    }\n  };\n  const getSortIcon = field => {\n    if (sortBy !== field) return '↕️';\n    return sortDirection === 'ASC' ? '↑' : '↓';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading products...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-card-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"admin-card-title\",\n        children: \"Product Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"admin-btn admin-btn-primary\",\n        onClick: handleAddProduct,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"btn-icon\",\n          children: \"+\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), \"Add New Product\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-filters\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"search\",\n              children: \"Search Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"search\",\n              type: \"text\",\n              placeholder: \"Search by name, code, or description...\",\n              value: searchTerm,\n              onChange: e => handleSearch(e.target.value),\n              className: \"admin-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"category\",\n              children: \"Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"category\",\n              value: selectedCategory,\n              onChange: e => setSelectedCategory(e.target.value),\n              className: \"admin-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.CategoryID,\n                children: category.CategoryName\n              }, category.CategoryID, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"status\",\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"status\",\n              value: selectedStatus,\n              onChange: e => setSelectedStatus(e.target.value),\n              className: \"admin-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All Statuses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Active\",\n                children: \"Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Draft\",\n                children: \"Draft\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Inactive\",\n                children: \"Inactive\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Discontinued\",\n                children: \"Discontinued\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Pending Review\",\n                children: \"Pending Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"pageSize\",\n              children: \"Items per page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"pageSize\",\n              value: pageSize,\n              onChange: e => {\n                setPageSize(parseInt(e.target.value));\n                setCurrentPage(1);\n              },\n              className: \"admin-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: 10,\n                children: \"10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 20,\n                children: \"20\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 50,\n                children: \"50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: 100,\n                children: \"100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"admin-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"sortable\",\n                onClick: () => handleSort('ProductName'),\n                children: [\"Product Name \", getSortIcon('ProductName')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"sortable\",\n                onClick: () => handleSort('BasePrice'),\n                children: [\"Price \", getSortIcon('BasePrice')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"sortable\",\n                onClick: () => handleSort('Status'),\n                children: [\"Status \", getSortIcon('Status')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Files\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"sortable\",\n                onClick: () => handleSort('UpdatedAt'),\n                children: [\"Last Updated \", getSortIcon('UpdatedAt')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: products.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"7\",\n                className: \"no-data\",\n                children: \"No products found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this) : products.map(product => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"product-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: product.ProductName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: product.ProductCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.CategoryName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatCurrency(product.BasePrice)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"status-badge\",\n                  style: {\n                    backgroundColor: getStatusBadgeColor(product.Status)\n                  },\n                  children: product.Status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-counts\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-count\",\n                    children: [\"\\uD83D\\uDCF7 \", product.ImageCount || 0]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 388,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"file-count\",\n                    children: [\"\\uD83C\\uDFAF \", product.ModelCount || 0]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.UpdatedAt ? new Date(product.UpdatedAt).toLocaleDateString() : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"action-buttons\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"admin-btn admin-btn-secondary btn-small\",\n                    onClick: () => handleViewProduct(product.ProductID),\n                    title: \"View Details\",\n                    children: \"\\uD83D\\uDC41\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"admin-btn admin-btn-secondary btn-small\",\n                    onClick: () => handleEditProduct(product),\n                    title: \"Edit Product\",\n                    children: \"\\u270F\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"admin-btn admin-btn-danger btn-small\",\n                    onClick: () => handleDeleteProduct(product),\n                    title: \"Delete Product\",\n                    children: \"\\uD83D\\uDDD1\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 21\n              }, this)]\n            }, product.ProductID, true, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pagination\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-info\",\n          children: [\"Showing \", (currentPage - 1) * pageSize + 1, \" to \", Math.min(currentPage * pageSize, totalCount), \" of \", totalCount, \" products\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn admin-btn-secondary btn-small\",\n            onClick: () => setCurrentPage(1),\n            disabled: currentPage === 1,\n            children: \"First\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn admin-btn-secondary btn-small\",\n            onClick: () => setCurrentPage(currentPage - 1),\n            disabled: currentPage === 1,\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"page-info\",\n            children: [\"Page \", currentPage, \" of \", totalPages]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn admin-btn-secondary btn-small\",\n            onClick: () => setCurrentPage(currentPage + 1),\n            disabled: currentPage === totalPages,\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"admin-btn admin-btn-secondary btn-small\",\n            onClick: () => setCurrentPage(totalPages),\n            disabled: currentPage === totalPages,\n            children: \"Last\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this), showProductForm && /*#__PURE__*/_jsxDEV(ProductFormModal, {\n      product: editingProduct,\n      categories: categories,\n      onSave: handleProductSaved,\n      onClose: () => {\n        setShowProductForm(false);\n        setEditingProduct(null);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 9\n    }, this), showProductDetails && selectedProduct && /*#__PURE__*/_jsxDEV(ProductDetailsModal, {\n      product: selectedProduct,\n      onClose: () => {\n        setShowProductDetails(false);\n        setSelectedProduct(null);\n      },\n      onEdit: () => {\n        setEditingProduct(selectedProduct.product);\n        setShowProductDetails(false);\n        setShowProductForm(true);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 9\n    }, this), showDeleteConfirm && selectedProduct && /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n      title: \"Delete Product\",\n      message: `Are you sure you want to delete \"${selectedProduct.ProductName}\"? This action cannot be undone.`,\n      confirmText: \"Delete\",\n      cancelText: \"Cancel\",\n      onConfirm: confirmDeleteProduct,\n      onCancel: () => {\n        setShowDeleteConfirm(false);\n        setSelectedProduct(null);\n      },\n      type: \"danger\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 241,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductManagement, \"TPNr0vrGcbdkLocBG9h82YZtOHA=\");\n_c = ProductManagement;\nexport default ProductManagement;\nvar _c;\n$RefreshReg$(_c, \"ProductManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "toast", "ProductFormModal", "ProductDetailsModal", "ConfirmationModal", "productsApi", "websocketService", "jsxDEV", "_jsxDEV", "ProductManagement", "_s", "products", "setProducts", "categories", "setCategories", "loading", "setLoading", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedStatus", "setSelectedStatus", "sortBy", "setSortBy", "sortDirection", "setSortDirection", "currentPage", "setCurrentPage", "pageSize", "setPageSize", "totalCount", "setTotalCount", "totalPages", "setTotalPages", "showProductForm", "setShowProductForm", "showProductDetails", "setShowProductDetails", "showDeleteConfirm", "setShowDeleteConfirm", "selectedProduct", "setSelectedProduct", "editingProduct", "setEditingProduct", "fetchProducts", "params", "page", "limit", "search", "undefined", "category", "status", "response", "getProducts", "success", "_response$data$pagina", "_response$data$pagina2", "data", "pagination", "totalItems", "error", "console", "fetchCategories", "getCategories", "subscribeToProducts", "handleProductCreated", "ProductName", "handleProductUpdated", "info", "handleProductDeleted", "warning", "handleProductFileUploaded", "productId", "on", "off", "handleSearch", "value", "handleSort", "field", "handleAddProduct", "handleEditProduct", "product", "handleViewProduct", "getProductById", "handleDeleteProduct", "confirmDeleteProduct", "deleteProduct", "ProductID", "notifyProductDeleted", "handleProductSaved", "productData", "notifyProductUpdated", "notifyProductCreated", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "getStatusBadgeColor", "getSortIcon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "htmlFor", "id", "type", "placeholder", "onChange", "e", "target", "map", "CategoryID", "CategoryName", "parseInt", "length", "colSpan", "ProductCode", "BasePrice", "backgroundColor", "Status", "ImageCount", "ModelCount", "UpdatedAt", "Date", "toLocaleDateString", "title", "Math", "min", "disabled", "onSave", "onClose", "onEdit", "message", "confirmText", "cancelText", "onConfirm", "onCancel", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/admin/ProductManagement.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { toast } from 'react-toastify';\nimport ProductFormModal from './modals/ProductFormModal';\nimport ProductDetailsModal from './modals/ProductDetailsModal';\nimport ConfirmationModal from '../modals/ConfirmationModal';\nimport { productsApi } from '../../services/api';\nimport websocketService from '../../services/websocketService';\nimport './ProductManagement.css';\n\nconst ProductManagement = () => {\n  const [products, setProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('');\n  const [sortBy, setSortBy] = useState('ProductName');\n  const [sortDirection, setSortDirection] = useState('ASC');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(20);\n  const [totalCount, setTotalCount] = useState(0);\n  const [totalPages, setTotalPages] = useState(0);\n\n  // Modal states\n  const [showProductForm, setShowProductForm] = useState(false);\n  const [showProductDetails, setShowProductDetails] = useState(false);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [editingProduct, setEditingProduct] = useState(null);\n\n  // Fetch products with filters and pagination\n  const fetchProducts = useCallback(async () => {\n    try {\n      setLoading(true);\n      const params = {\n        page: currentPage,\n        limit: pageSize,\n        search: searchTerm || undefined,\n        category: selectedCategory || undefined,\n        status: selectedStatus || undefined,\n        sortBy,\n        sortDirection\n      };\n\n      const response = await productsApi.getProducts(params);\n\n      if (response.success) {\n        setProducts(response.data.products || []);\n        setTotalCount(response.data.pagination?.totalItems || 0);\n        setTotalPages(response.data.pagination?.totalPages || 0);\n      } else {\n        toast.error('Failed to fetch products');\n      }\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      toast.error('Error loading products');\n    } finally {\n      setLoading(false);\n    }\n  }, [currentPage, pageSize, searchTerm, selectedCategory, selectedStatus, sortBy, sortDirection]);\n\n  // Fetch categories\n  const fetchCategories = useCallback(async () => {\n    try {\n      const response = await productsApi.getCategories();\n      if (response.success) {\n        setCategories(response.data);\n      }\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n    }\n  }, []);\n\n  useEffect(() => {\n    fetchProducts();\n  }, [fetchProducts]);\n\n  useEffect(() => {\n    fetchCategories();\n  }, [fetchCategories]);\n\n  // WebSocket integration for real-time updates\n  useEffect(() => {\n    // Subscribe to product updates\n    websocketService.subscribeToProducts();\n\n    // Set up event listeners\n    const handleProductCreated = (data) => {\n      toast.success(`New product \"${data.ProductName}\" has been created`);\n      fetchProducts(); // Refresh the product list\n    };\n\n    const handleProductUpdated = (data) => {\n      toast.info(`Product \"${data.ProductName}\" has been updated`);\n      fetchProducts(); // Refresh the product list\n    };\n\n    const handleProductDeleted = (data) => {\n      toast.warning(`Product has been deleted`);\n      fetchProducts(); // Refresh the product list\n    };\n\n    const handleProductFileUploaded = (data) => {\n      toast.success(`File uploaded for product ID: ${data.productId}`);\n      // Optionally refresh specific product or entire list\n      fetchProducts();\n    };\n\n    // Register event listeners\n    websocketService.on('productCreated', handleProductCreated);\n    websocketService.on('productUpdated', handleProductUpdated);\n    websocketService.on('productDeleted', handleProductDeleted);\n    websocketService.on('productFileUploaded', handleProductFileUploaded);\n\n    // Cleanup function\n    return () => {\n      websocketService.off('productCreated', handleProductCreated);\n      websocketService.off('productUpdated', handleProductUpdated);\n      websocketService.off('productDeleted', handleProductDeleted);\n      websocketService.off('productFileUploaded', handleProductFileUploaded);\n    };\n  }, [fetchProducts]);\n\n  // Handle search with debouncing\n  const handleSearch = useCallback((value) => {\n    setSearchTerm(value);\n    setCurrentPage(1);\n  }, []);\n\n  // Handle sorting\n  const handleSort = (field) => {\n    if (sortBy === field) {\n      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');\n    } else {\n      setSortBy(field);\n      setSortDirection('ASC');\n    }\n    setCurrentPage(1);\n  };\n\n  // Handle product actions\n  const handleAddProduct = () => {\n    setEditingProduct(null);\n    setShowProductForm(true);\n  };\n\n  const handleEditProduct = (product) => {\n    setEditingProduct(product);\n    setShowProductForm(true);\n  };\n\n  const handleViewProduct = async (productId) => {\n    try {\n      const response = await productsApi.getProductById(productId);\n      if (response.success) {\n        setSelectedProduct(response.data);\n        setShowProductDetails(true);\n      } else {\n        toast.error('Failed to load product details');\n      }\n    } catch (error) {\n      console.error('Error fetching product details:', error);\n      toast.error('Error loading product details');\n    }\n  };\n\n  const handleDeleteProduct = (product) => {\n    setSelectedProduct(product);\n    setShowDeleteConfirm(true);\n  };\n\n  const confirmDeleteProduct = async () => {\n    try {\n      const response = await productsApi.deleteProduct(selectedProduct.ProductID);\n      if (response.success) {\n        // Emit WebSocket event for real-time updates\n        websocketService.notifyProductDeleted(selectedProduct.ProductID);\n        toast.success('Product deleted successfully');\n        fetchProducts();\n      } else {\n        toast.error('Failed to delete product');\n      }\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      toast.error('Error deleting product');\n    } finally {\n      setShowDeleteConfirm(false);\n      setSelectedProduct(null);\n    }\n  };\n\n  const handleProductSaved = (productData) => {\n    setShowProductForm(false);\n    setEditingProduct(null);\n    fetchProducts();\n\n    // Emit WebSocket event for real-time updates\n    if (editingProduct) {\n      websocketService.notifyProductUpdated(productData);\n      toast.success('Product updated successfully');\n    } else {\n      websocketService.notifyProductCreated(productData);\n      toast.success('Product created successfully');\n    }\n  };\n\n  // Utility functions\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-PH', {\n      style: 'currency',\n      currency: 'PHP'\n    }).format(amount);\n  };\n\n  const getStatusBadgeColor = (status) => {\n    switch (status) {\n      case 'Active': return '#27ae60';\n      case 'Draft': return '#f39c12';\n      case 'Inactive': return '#95a5a6';\n      case 'Discontinued': return '#e74c3c';\n      case 'Pending Review': return '#3498db';\n      default: return '#95a5a6';\n    }\n  };\n\n  const getSortIcon = (field) => {\n    if (sortBy !== field) return '↕️';\n    return sortDirection === 'ASC' ? '↑' : '↓';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"admin-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading products...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"product-management\">\n      {/* Header */}\n      <div className=\"admin-card-header\">\n        <h1 className=\"admin-card-title\">Product Management</h1>\n        <button\n          className=\"admin-btn admin-btn-primary\"\n          onClick={handleAddProduct}\n        >\n          <span className=\"btn-icon\">+</span>\n          Add New Product\n        </button>\n      </div>\n\n      {/* Filters and Search */}\n      <div className=\"admin-card\">\n        <div className=\"product-filters\">\n          <div className=\"filter-row\">\n            <div className=\"filter-group\">\n              <label htmlFor=\"search\">Search Products</label>\n              <input\n                id=\"search\"\n                type=\"text\"\n                placeholder=\"Search by name, code, or description...\"\n                value={searchTerm}\n                onChange={(e) => handleSearch(e.target.value)}\n                className=\"admin-input\"\n              />\n            </div>\n\n            <div className=\"filter-group\">\n              <label htmlFor=\"category\">Category</label>\n              <select\n                id=\"category\"\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n                className=\"admin-select\"\n              >\n                <option value=\"\">All Categories</option>\n                {categories.map(category => (\n                  <option key={category.CategoryID} value={category.CategoryID}>\n                    {category.CategoryName}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div className=\"filter-group\">\n              <label htmlFor=\"status\">Status</label>\n              <select\n                id=\"status\"\n                value={selectedStatus}\n                onChange={(e) => setSelectedStatus(e.target.value)}\n                className=\"admin-select\"\n              >\n                <option value=\"\">All Statuses</option>\n                <option value=\"Active\">Active</option>\n                <option value=\"Draft\">Draft</option>\n                <option value=\"Inactive\">Inactive</option>\n                <option value=\"Discontinued\">Discontinued</option>\n                <option value=\"Pending Review\">Pending Review</option>\n              </select>\n            </div>\n\n            <div className=\"filter-group\">\n              <label htmlFor=\"pageSize\">Items per page</label>\n              <select\n                id=\"pageSize\"\n                value={pageSize}\n                onChange={(e) => {\n                  setPageSize(parseInt(e.target.value));\n                  setCurrentPage(1);\n                }}\n                className=\"admin-select\"\n              >\n                <option value={10}>10</option>\n                <option value={20}>20</option>\n                <option value={50}>50</option>\n                <option value={100}>100</option>\n              </select>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Products Table */}\n      <div className=\"admin-card\">\n        <div className=\"table-container\">\n          <table className=\"admin-table\">\n            <thead>\n              <tr>\n                <th\n                  className=\"sortable\"\n                  onClick={() => handleSort('ProductName')}\n                >\n                  Product Name {getSortIcon('ProductName')}\n                </th>\n                <th>Category</th>\n                <th\n                  className=\"sortable\"\n                  onClick={() => handleSort('BasePrice')}\n                >\n                  Price {getSortIcon('BasePrice')}\n                </th>\n                <th\n                  className=\"sortable\"\n                  onClick={() => handleSort('Status')}\n                >\n                  Status {getSortIcon('Status')}\n                </th>\n                <th>Files</th>\n                <th\n                  className=\"sortable\"\n                  onClick={() => handleSort('UpdatedAt')}\n                >\n                  Last Updated {getSortIcon('UpdatedAt')}\n                </th>\n                <th>Actions</th>\n              </tr>\n            </thead>\n            <tbody>\n              {products.length === 0 ? (\n                <tr>\n                  <td colSpan=\"7\" className=\"no-data\">\n                    No products found\n                  </td>\n                </tr>\n              ) : (\n                products.map(product => (\n                  <tr key={product.ProductID}>\n                    <td>\n                      <div className=\"product-info\">\n                        <strong>{product.ProductName}</strong>\n                        <small>{product.ProductCode}</small>\n                      </div>\n                    </td>\n                    <td>{product.CategoryName}</td>\n                    <td>{formatCurrency(product.BasePrice)}</td>\n                    <td>\n                      <span\n                        className=\"status-badge\"\n                        style={{ backgroundColor: getStatusBadgeColor(product.Status) }}\n                      >\n                        {product.Status}\n                      </span>\n                    </td>\n                    <td>\n                      <div className=\"file-counts\">\n                        <span className=\"file-count\">\n                          📷 {product.ImageCount || 0}\n                        </span>\n                        <span className=\"file-count\">\n                          🎯 {product.ModelCount || 0}\n                        </span>\n                      </div>\n                    </td>\n                    <td>\n                      {product.UpdatedAt ? new Date(product.UpdatedAt).toLocaleDateString() : '-'}\n                    </td>\n                    <td>\n                      <div className=\"action-buttons\">\n                        <button\n                          className=\"admin-btn admin-btn-secondary btn-small\"\n                          onClick={() => handleViewProduct(product.ProductID)}\n                          title=\"View Details\"\n                        >\n                          👁️\n                        </button>\n                        <button\n                          className=\"admin-btn admin-btn-secondary btn-small\"\n                          onClick={() => handleEditProduct(product)}\n                          title=\"Edit Product\"\n                        >\n                          ✏️\n                        </button>\n                        <button\n                          className=\"admin-btn admin-btn-danger btn-small\"\n                          onClick={() => handleDeleteProduct(product)}\n                          title=\"Delete Product\"\n                        >\n                          🗑️\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))\n              )}\n            </tbody>\n          </table>\n        </div>\n\n        {/* Pagination */}\n        {totalPages > 1 && (\n          <div className=\"pagination\">\n            <div className=\"pagination-info\">\n              Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} products\n            </div>\n            <div className=\"pagination-controls\">\n              <button\n                className=\"admin-btn admin-btn-secondary btn-small\"\n                onClick={() => setCurrentPage(1)}\n                disabled={currentPage === 1}\n              >\n                First\n              </button>\n              <button\n                className=\"admin-btn admin-btn-secondary btn-small\"\n                onClick={() => setCurrentPage(currentPage - 1)}\n                disabled={currentPage === 1}\n              >\n                Previous\n              </button>\n              <span className=\"page-info\">\n                Page {currentPage} of {totalPages}\n              </span>\n              <button\n                className=\"admin-btn admin-btn-secondary btn-small\"\n                onClick={() => setCurrentPage(currentPage + 1)}\n                disabled={currentPage === totalPages}\n              >\n                Next\n              </button>\n              <button\n                className=\"admin-btn admin-btn-secondary btn-small\"\n                onClick={() => setCurrentPage(totalPages)}\n                disabled={currentPage === totalPages}\n              >\n                Last\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Modals */}\n      {showProductForm && (\n        <ProductFormModal\n          product={editingProduct}\n          categories={categories}\n          onSave={handleProductSaved}\n          onClose={() => {\n            setShowProductForm(false);\n            setEditingProduct(null);\n          }}\n        />\n      )}\n\n      {showProductDetails && selectedProduct && (\n        <ProductDetailsModal\n          product={selectedProduct}\n          onClose={() => {\n            setShowProductDetails(false);\n            setSelectedProduct(null);\n          }}\n          onEdit={() => {\n            setEditingProduct(selectedProduct.product);\n            setShowProductDetails(false);\n            setShowProductForm(true);\n          }}\n        />\n      )}\n\n      {showDeleteConfirm && selectedProduct && (\n        <ConfirmationModal\n          title=\"Delete Product\"\n          message={`Are you sure you want to delete \"${selectedProduct.ProductName}\"? This action cannot be undone.`}\n          confirmText=\"Delete\"\n          cancelText=\"Cancel\"\n          onConfirm={confirmDeleteProduct}\n          onCancel={() => {\n            setShowDeleteConfirm(false);\n            setSelectedProduct(null);\n          }}\n          type=\"danger\"\n        />\n      )}\n    </div>\n  );\n};\n\nexport default ProductManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,OAAOC,iBAAiB,MAAM,6BAA6B;AAC3D,SAASC,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyB,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,aAAa,CAAC;EACnD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC6C,cAAc,EAAEC,iBAAiB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAM+C,aAAa,GAAG7C,WAAW,CAAC,YAAY;IAC5C,IAAI;MACFgB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM8B,MAAM,GAAG;QACbC,IAAI,EAAEpB,WAAW;QACjBqB,KAAK,EAAEnB,QAAQ;QACfoB,MAAM,EAAEhC,UAAU,IAAIiC,SAAS;QAC/BC,QAAQ,EAAEhC,gBAAgB,IAAI+B,SAAS;QACvCE,MAAM,EAAE/B,cAAc,IAAI6B,SAAS;QACnC3B,MAAM;QACNE;MACF,CAAC;MAED,MAAM4B,QAAQ,GAAG,MAAMhD,WAAW,CAACiD,WAAW,CAACR,MAAM,CAAC;MAEtD,IAAIO,QAAQ,CAACE,OAAO,EAAE;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;QACpB7C,WAAW,CAACyC,QAAQ,CAACK,IAAI,CAAC/C,QAAQ,IAAI,EAAE,CAAC;QACzCqB,aAAa,CAAC,EAAAwB,qBAAA,GAAAH,QAAQ,CAACK,IAAI,CAACC,UAAU,cAAAH,qBAAA,uBAAxBA,qBAAA,CAA0BI,UAAU,KAAI,CAAC,CAAC;QACxD1B,aAAa,CAAC,EAAAuB,sBAAA,GAAAJ,QAAQ,CAACK,IAAI,CAACC,UAAU,cAAAF,sBAAA,uBAAxBA,sBAAA,CAA0BxB,UAAU,KAAI,CAAC,CAAC;MAC1D,CAAC,MAAM;QACLhC,KAAK,CAAC4D,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD5D,KAAK,CAAC4D,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACR7C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACW,WAAW,EAAEE,QAAQ,EAAEZ,UAAU,EAAEE,gBAAgB,EAAEE,cAAc,EAAEE,MAAM,EAAEE,aAAa,CAAC,CAAC;;EAEhG;EACA,MAAMsC,eAAe,GAAG/D,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF,MAAMqD,QAAQ,GAAG,MAAMhD,WAAW,CAAC2D,aAAa,CAAC,CAAC;MAClD,IAAIX,QAAQ,CAACE,OAAO,EAAE;QACpBzC,aAAa,CAACuC,QAAQ,CAACK,IAAI,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN9D,SAAS,CAAC,MAAM;IACd8C,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB9C,SAAS,CAAC,MAAM;IACdgE,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;;EAErB;EACAhE,SAAS,CAAC,MAAM;IACd;IACAO,gBAAgB,CAAC2D,mBAAmB,CAAC,CAAC;;IAEtC;IACA,MAAMC,oBAAoB,GAAIR,IAAI,IAAK;MACrCzD,KAAK,CAACsD,OAAO,CAAC,gBAAgBG,IAAI,CAACS,WAAW,oBAAoB,CAAC;MACnEtB,aAAa,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IAED,MAAMuB,oBAAoB,GAAIV,IAAI,IAAK;MACrCzD,KAAK,CAACoE,IAAI,CAAC,YAAYX,IAAI,CAACS,WAAW,oBAAoB,CAAC;MAC5DtB,aAAa,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IAED,MAAMyB,oBAAoB,GAAIZ,IAAI,IAAK;MACrCzD,KAAK,CAACsE,OAAO,CAAC,0BAA0B,CAAC;MACzC1B,aAAa,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IAED,MAAM2B,yBAAyB,GAAId,IAAI,IAAK;MAC1CzD,KAAK,CAACsD,OAAO,CAAC,iCAAiCG,IAAI,CAACe,SAAS,EAAE,CAAC;MAChE;MACA5B,aAAa,CAAC,CAAC;IACjB,CAAC;;IAED;IACAvC,gBAAgB,CAACoE,EAAE,CAAC,gBAAgB,EAAER,oBAAoB,CAAC;IAC3D5D,gBAAgB,CAACoE,EAAE,CAAC,gBAAgB,EAAEN,oBAAoB,CAAC;IAC3D9D,gBAAgB,CAACoE,EAAE,CAAC,gBAAgB,EAAEJ,oBAAoB,CAAC;IAC3DhE,gBAAgB,CAACoE,EAAE,CAAC,qBAAqB,EAAEF,yBAAyB,CAAC;;IAErE;IACA,OAAO,MAAM;MACXlE,gBAAgB,CAACqE,GAAG,CAAC,gBAAgB,EAAET,oBAAoB,CAAC;MAC5D5D,gBAAgB,CAACqE,GAAG,CAAC,gBAAgB,EAAEP,oBAAoB,CAAC;MAC5D9D,gBAAgB,CAACqE,GAAG,CAAC,gBAAgB,EAAEL,oBAAoB,CAAC;MAC5DhE,gBAAgB,CAACqE,GAAG,CAAC,qBAAqB,EAAEH,yBAAyB,CAAC;IACxE,CAAC;EACH,CAAC,EAAE,CAAC3B,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM+B,YAAY,GAAG5E,WAAW,CAAE6E,KAAK,IAAK;IAC1C3D,aAAa,CAAC2D,KAAK,CAAC;IACpBjD,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkD,UAAU,GAAIC,KAAK,IAAK;IAC5B,IAAIxD,MAAM,KAAKwD,KAAK,EAAE;MACpBrD,gBAAgB,CAACD,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;IAC5D,CAAC,MAAM;MACLD,SAAS,CAACuD,KAAK,CAAC;MAChBrD,gBAAgB,CAAC,KAAK,CAAC;IACzB;IACAE,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMoD,gBAAgB,GAAGA,CAAA,KAAM;IAC7BpC,iBAAiB,CAAC,IAAI,CAAC;IACvBR,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM6C,iBAAiB,GAAIC,OAAO,IAAK;IACrCtC,iBAAiB,CAACsC,OAAO,CAAC;IAC1B9C,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM+C,iBAAiB,GAAG,MAAOV,SAAS,IAAK;IAC7C,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMhD,WAAW,CAAC+E,cAAc,CAACX,SAAS,CAAC;MAC5D,IAAIpB,QAAQ,CAACE,OAAO,EAAE;QACpBb,kBAAkB,CAACW,QAAQ,CAACK,IAAI,CAAC;QACjCpB,qBAAqB,CAAC,IAAI,CAAC;MAC7B,CAAC,MAAM;QACLrC,KAAK,CAAC4D,KAAK,CAAC,gCAAgC,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD5D,KAAK,CAAC4D,KAAK,CAAC,+BAA+B,CAAC;IAC9C;EACF,CAAC;EAED,MAAMwB,mBAAmB,GAAIH,OAAO,IAAK;IACvCxC,kBAAkB,CAACwC,OAAO,CAAC;IAC3B1C,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAM8C,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMjC,QAAQ,GAAG,MAAMhD,WAAW,CAACkF,aAAa,CAAC9C,eAAe,CAAC+C,SAAS,CAAC;MAC3E,IAAInC,QAAQ,CAACE,OAAO,EAAE;QACpB;QACAjD,gBAAgB,CAACmF,oBAAoB,CAAChD,eAAe,CAAC+C,SAAS,CAAC;QAChEvF,KAAK,CAACsD,OAAO,CAAC,8BAA8B,CAAC;QAC7CV,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACL5C,KAAK,CAAC4D,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C5D,KAAK,CAAC4D,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACRrB,oBAAoB,CAAC,KAAK,CAAC;MAC3BE,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC;EAED,MAAMgD,kBAAkB,GAAIC,WAAW,IAAK;IAC1CvD,kBAAkB,CAAC,KAAK,CAAC;IACzBQ,iBAAiB,CAAC,IAAI,CAAC;IACvBC,aAAa,CAAC,CAAC;;IAEf;IACA,IAAIF,cAAc,EAAE;MAClBrC,gBAAgB,CAACsF,oBAAoB,CAACD,WAAW,CAAC;MAClD1F,KAAK,CAACsD,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC,MAAM;MACLjD,gBAAgB,CAACuF,oBAAoB,CAACF,WAAW,CAAC;MAClD1F,KAAK,CAACsD,OAAO,CAAC,8BAA8B,CAAC;IAC/C;EACF,CAAC;;EAED;EACA,MAAMuC,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,mBAAmB,GAAIjD,MAAM,IAAK;IACtC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,cAAc;QAAE,OAAO,SAAS;MACrC,KAAK,gBAAgB;QAAE,OAAO,SAAS;MACvC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMkD,WAAW,GAAIvB,KAAK,IAAK;IAC7B,IAAIxD,MAAM,KAAKwD,KAAK,EAAE,OAAO,IAAI;IACjC,OAAOtD,aAAa,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG;EAC5C,CAAC;EAED,IAAIV,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK+F,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BhG,OAAA;QAAK+F,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCpG,OAAA;QAAAgG,QAAA,EAAG;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;EAEV;EAEA,oBACEpG,OAAA;IAAK+F,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBAEjChG,OAAA;MAAK+F,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChChG,OAAA;QAAI+F,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxDpG,OAAA;QACE+F,SAAS,EAAC,6BAA6B;QACvCM,OAAO,EAAE7B,gBAAiB;QAAAwB,QAAA,gBAE1BhG,OAAA;UAAM+F,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,mBAErC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNpG,OAAA;MAAK+F,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzBhG,OAAA;QAAK+F,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BhG,OAAA;UAAK+F,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBhG,OAAA;YAAK+F,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BhG,OAAA;cAAOsG,OAAO,EAAC,QAAQ;cAAAN,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/CpG,OAAA;cACEuG,EAAE,EAAC,QAAQ;cACXC,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,yCAAyC;cACrDpC,KAAK,EAAE5D,UAAW;cAClBiG,QAAQ,EAAGC,CAAC,IAAKvC,YAAY,CAACuC,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAE;cAC9C0B,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENpG,OAAA;YAAK+F,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BhG,OAAA;cAAOsG,OAAO,EAAC,UAAU;cAAAN,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CpG,OAAA;cACEuG,EAAE,EAAC,UAAU;cACblC,KAAK,EAAE1D,gBAAiB;cACxB+F,QAAQ,EAAGC,CAAC,IAAK/F,mBAAmB,CAAC+F,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAE;cACrD0B,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAExBhG,OAAA;gBAAQqE,KAAK,EAAC,EAAE;gBAAA2B,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACvC/F,UAAU,CAACwG,GAAG,CAAClE,QAAQ,iBACtB3C,OAAA;gBAAkCqE,KAAK,EAAE1B,QAAQ,CAACmE,UAAW;gBAAAd,QAAA,EAC1DrD,QAAQ,CAACoE;cAAY,GADXpE,QAAQ,CAACmE,UAAU;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAExB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENpG,OAAA;YAAK+F,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BhG,OAAA;cAAOsG,OAAO,EAAC,QAAQ;cAAAN,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCpG,OAAA;cACEuG,EAAE,EAAC,QAAQ;cACXlC,KAAK,EAAExD,cAAe;cACtB6F,QAAQ,EAAGC,CAAC,IAAK7F,iBAAiB,CAAC6F,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAE;cACnD0B,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAExBhG,OAAA;gBAAQqE,KAAK,EAAC,EAAE;gBAAA2B,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCpG,OAAA;gBAAQqE,KAAK,EAAC,QAAQ;gBAAA2B,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCpG,OAAA;gBAAQqE,KAAK,EAAC,OAAO;gBAAA2B,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCpG,OAAA;gBAAQqE,KAAK,EAAC,UAAU;gBAAA2B,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1CpG,OAAA;gBAAQqE,KAAK,EAAC,cAAc;gBAAA2B,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClDpG,OAAA;gBAAQqE,KAAK,EAAC,gBAAgB;gBAAA2B,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENpG,OAAA;YAAK+F,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BhG,OAAA;cAAOsG,OAAO,EAAC,UAAU;cAAAN,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChDpG,OAAA;cACEuG,EAAE,EAAC,UAAU;cACblC,KAAK,EAAEhD,QAAS;cAChBqF,QAAQ,EAAGC,CAAC,IAAK;gBACfrF,WAAW,CAAC0F,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAC,CAAC;gBACrCjD,cAAc,CAAC,CAAC,CAAC;cACnB,CAAE;cACF2E,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAExBhG,OAAA;gBAAQqE,KAAK,EAAE,EAAG;gBAAA2B,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BpG,OAAA;gBAAQqE,KAAK,EAAE,EAAG;gBAAA2B,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BpG,OAAA;gBAAQqE,KAAK,EAAE,EAAG;gBAAA2B,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BpG,OAAA;gBAAQqE,KAAK,EAAE,GAAI;gBAAA2B,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpG,OAAA;MAAK+F,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBhG,OAAA;QAAK+F,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BhG,OAAA;UAAO+F,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC5BhG,OAAA;YAAAgG,QAAA,eACEhG,OAAA;cAAAgG,QAAA,gBACEhG,OAAA;gBACE+F,SAAS,EAAC,UAAU;gBACpBM,OAAO,EAAEA,CAAA,KAAM/B,UAAU,CAAC,aAAa,CAAE;gBAAA0B,QAAA,GAC1C,eACc,EAACF,WAAW,CAAC,aAAa,CAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACLpG,OAAA;gBAAAgG,QAAA,EAAI;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBpG,OAAA;gBACE+F,SAAS,EAAC,UAAU;gBACpBM,OAAO,EAAEA,CAAA,KAAM/B,UAAU,CAAC,WAAW,CAAE;gBAAA0B,QAAA,GACxC,QACO,EAACF,WAAW,CAAC,WAAW,CAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACLpG,OAAA;gBACE+F,SAAS,EAAC,UAAU;gBACpBM,OAAO,EAAEA,CAAA,KAAM/B,UAAU,CAAC,QAAQ,CAAE;gBAAA0B,QAAA,GACrC,SACQ,EAACF,WAAW,CAAC,QAAQ,CAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACLpG,OAAA;gBAAAgG,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdpG,OAAA;gBACE+F,SAAS,EAAC,UAAU;gBACpBM,OAAO,EAAEA,CAAA,KAAM/B,UAAU,CAAC,WAAW,CAAE;gBAAA0B,QAAA,GACxC,eACc,EAACF,WAAW,CAAC,WAAW,CAAC;cAAA;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACLpG,OAAA;gBAAAgG,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRpG,OAAA;YAAAgG,QAAA,EACG7F,QAAQ,CAAC8G,MAAM,KAAK,CAAC,gBACpBjH,OAAA;cAAAgG,QAAA,eACEhG,OAAA;gBAAIkH,OAAO,EAAC,GAAG;gBAACnB,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAEpC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GAELjG,QAAQ,CAAC0G,GAAG,CAACnC,OAAO,iBAClB1E,OAAA;cAAAgG,QAAA,gBACEhG,OAAA;gBAAAgG,QAAA,eACEhG,OAAA;kBAAK+F,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BhG,OAAA;oBAAAgG,QAAA,EAAStB,OAAO,CAACf;kBAAW;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eACtCpG,OAAA;oBAAAgG,QAAA,EAAQtB,OAAO,CAACyC;kBAAW;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLpG,OAAA;gBAAAgG,QAAA,EAAKtB,OAAO,CAACqC;cAAY;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/BpG,OAAA;gBAAAgG,QAAA,EAAKV,cAAc,CAACZ,OAAO,CAAC0C,SAAS;cAAC;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5CpG,OAAA;gBAAAgG,QAAA,eACEhG,OAAA;kBACE+F,SAAS,EAAC,cAAc;kBACxBL,KAAK,EAAE;oBAAE2B,eAAe,EAAExB,mBAAmB,CAACnB,OAAO,CAAC4C,MAAM;kBAAE,CAAE;kBAAAtB,QAAA,EAE/DtB,OAAO,CAAC4C;gBAAM;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLpG,OAAA;gBAAAgG,QAAA,eACEhG,OAAA;kBAAK+F,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BhG,OAAA;oBAAM+F,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAC,eACxB,EAACtB,OAAO,CAAC6C,UAAU,IAAI,CAAC;kBAAA;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACPpG,OAAA;oBAAM+F,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAC,eACxB,EAACtB,OAAO,CAAC8C,UAAU,IAAI,CAAC;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLpG,OAAA;gBAAAgG,QAAA,EACGtB,OAAO,CAAC+C,SAAS,GAAG,IAAIC,IAAI,CAAChD,OAAO,CAAC+C,SAAS,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;cAAG;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACLpG,OAAA;gBAAAgG,QAAA,eACEhG,OAAA;kBAAK+F,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BhG,OAAA;oBACE+F,SAAS,EAAC,yCAAyC;oBACnDM,OAAO,EAAEA,CAAA,KAAM1B,iBAAiB,CAACD,OAAO,CAACM,SAAS,CAAE;oBACpD4C,KAAK,EAAC,cAAc;oBAAA5B,QAAA,EACrB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTpG,OAAA;oBACE+F,SAAS,EAAC,yCAAyC;oBACnDM,OAAO,EAAEA,CAAA,KAAM5B,iBAAiB,CAACC,OAAO,CAAE;oBAC1CkD,KAAK,EAAC,cAAc;oBAAA5B,QAAA,EACrB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTpG,OAAA;oBACE+F,SAAS,EAAC,sCAAsC;oBAChDM,OAAO,EAAEA,CAAA,KAAMxB,mBAAmB,CAACH,OAAO,CAAE;oBAC5CkD,KAAK,EAAC,gBAAgB;oBAAA5B,QAAA,EACvB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAtDE1B,OAAO,CAACM,SAAS;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuDtB,CACL;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGL3E,UAAU,GAAG,CAAC,iBACbzB,OAAA;QAAK+F,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBhG,OAAA;UAAK+F,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,UACvB,EAAE,CAAC7E,WAAW,GAAG,CAAC,IAAIE,QAAQ,GAAI,CAAC,EAAC,MAAI,EAACwG,IAAI,CAACC,GAAG,CAAC3G,WAAW,GAAGE,QAAQ,EAAEE,UAAU,CAAC,EAAC,MAAI,EAACA,UAAU,EAAC,WAChH;QAAA;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNpG,OAAA;UAAK+F,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClChG,OAAA;YACE+F,SAAS,EAAC,yCAAyC;YACnDM,OAAO,EAAEA,CAAA,KAAMjF,cAAc,CAAC,CAAC,CAAE;YACjC2G,QAAQ,EAAE5G,WAAW,KAAK,CAAE;YAAA6E,QAAA,EAC7B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpG,OAAA;YACE+F,SAAS,EAAC,yCAAyC;YACnDM,OAAO,EAAEA,CAAA,KAAMjF,cAAc,CAACD,WAAW,GAAG,CAAC,CAAE;YAC/C4G,QAAQ,EAAE5G,WAAW,KAAK,CAAE;YAAA6E,QAAA,EAC7B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpG,OAAA;YAAM+F,SAAS,EAAC,WAAW;YAAAC,QAAA,GAAC,OACrB,EAAC7E,WAAW,EAAC,MAAI,EAACM,UAAU;UAAA;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACPpG,OAAA;YACE+F,SAAS,EAAC,yCAAyC;YACnDM,OAAO,EAAEA,CAAA,KAAMjF,cAAc,CAACD,WAAW,GAAG,CAAC,CAAE;YAC/C4G,QAAQ,EAAE5G,WAAW,KAAKM,UAAW;YAAAuE,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpG,OAAA;YACE+F,SAAS,EAAC,yCAAyC;YACnDM,OAAO,EAAEA,CAAA,KAAMjF,cAAc,CAACK,UAAU,CAAE;YAC1CsG,QAAQ,EAAE5G,WAAW,KAAKM,UAAW;YAAAuE,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLzE,eAAe,iBACd3B,OAAA,CAACN,gBAAgB;MACfgF,OAAO,EAAEvC,cAAe;MACxB9B,UAAU,EAAEA,UAAW;MACvB2H,MAAM,EAAE9C,kBAAmB;MAC3B+C,OAAO,EAAEA,CAAA,KAAM;QACbrG,kBAAkB,CAAC,KAAK,CAAC;QACzBQ,iBAAiB,CAAC,IAAI,CAAC;MACzB;IAAE;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EAEAvE,kBAAkB,IAAII,eAAe,iBACpCjC,OAAA,CAACL,mBAAmB;MAClB+E,OAAO,EAAEzC,eAAgB;MACzBgG,OAAO,EAAEA,CAAA,KAAM;QACbnG,qBAAqB,CAAC,KAAK,CAAC;QAC5BI,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAE;MACFgG,MAAM,EAAEA,CAAA,KAAM;QACZ9F,iBAAiB,CAACH,eAAe,CAACyC,OAAO,CAAC;QAC1C5C,qBAAqB,CAAC,KAAK,CAAC;QAC5BF,kBAAkB,CAAC,IAAI,CAAC;MAC1B;IAAE;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,EAEArE,iBAAiB,IAAIE,eAAe,iBACnCjC,OAAA,CAACJ,iBAAiB;MAChBgI,KAAK,EAAC,gBAAgB;MACtBO,OAAO,EAAE,oCAAoClG,eAAe,CAAC0B,WAAW,kCAAmC;MAC3GyE,WAAW,EAAC,QAAQ;MACpBC,UAAU,EAAC,QAAQ;MACnBC,SAAS,EAAExD,oBAAqB;MAChCyD,QAAQ,EAAEA,CAAA,KAAM;QACdvG,oBAAoB,CAAC,KAAK,CAAC;QAC3BE,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAE;MACFsE,IAAI,EAAC;IAAQ;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClG,EAAA,CA5fID,iBAAiB;AAAAuI,EAAA,GAAjBvI,iBAAiB;AA8fvB,eAAeA,iBAAiB;AAAC,IAAAuI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}