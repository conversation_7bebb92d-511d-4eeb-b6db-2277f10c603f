'use strict';

var classCallCheck = require('./classCallCheck-839aeb3a.cjs.prod.js');
var isNativeReflectConstruct = require('./isNativeReflectConstruct-9acebf01.cjs.prod.js');
var THREE = require('three');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n["default"] = e;
  return Object.freeze(n);
}

var THREE__namespace = /*#__PURE__*/_interopNamespace(THREE);

function _inherits(subClass, superClass) {
  if (typeof superClass !== "function" && superClass !== null) {
    throw new TypeError("Super expression must either be null or a function");
  }

  subClass.prototype = Object.create(superClass && superClass.prototype, {
    constructor: {
      value: subClass,
      writable: true,
      configurable: true
    }
  });
  if (superClass) isNativeReflectConstruct._setPrototypeOf(subClass, superClass);
}

function _getPrototypeOf(o) {
  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {
    return o.__proto__ || Object.getPrototypeOf(o);
  };
  return _getPrototypeOf(o);
}

function _assertThisInitialized(self) {
  if (self === void 0) {
    throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  }

  return self;
}

function _possibleConstructorReturn(self, call) {
  if (call && (typeof call === "object" || typeof call === "function")) {
    return call;
  } else if (call !== void 0) {
    throw new TypeError("Derived constructors may only return object or undefined");
  }

  return _assertThisInitialized(self);
}

function _createSuper(Derived) {
  var hasNativeReflectConstruct = isNativeReflectConstruct._isNativeReflectConstruct();
  return function _createSuperInternal() {
    var Super = _getPrototypeOf(Derived),
        result;

    if (hasNativeReflectConstruct) {
      var NewTarget = _getPrototypeOf(this).constructor;
      result = Reflect.construct(Super, arguments, NewTarget);
    } else {
      result = Super.apply(this, arguments);
    }

    return _possibleConstructorReturn(this, result);
  };
}

var RoundedPlaneGeometry = /*#__PURE__*/function (_THREE$BufferGeometry) {
  _inherits(RoundedPlaneGeometry, _THREE$BufferGeometry);

  var _super = _createSuper(RoundedPlaneGeometry);

  function RoundedPlaneGeometry() {
    var _this;

    var width = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 2;
    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;
    var radius = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0.2;
    var segments = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 16;

    classCallCheck._classCallCheck(this, RoundedPlaneGeometry);

    _this = _super.call(this); // helper const's

    var wi = width / 2 - radius; // inner width

    var hi = height / 2 - radius; // inner height

    var ul = radius / width; // u left

    var ur = (width - radius) / width; // u right

    var vl = radius / height; // v low

    var vh = (height - radius) / height; // v high

    var positions = [wi, hi, 0, -wi, hi, 0, -wi, -hi, 0, wi, -hi, 0];
    var uvs = [ur, vh, ul, vh, ul, vl, ur, vl];
    var n = [3 * (segments + 1) + 3, 3 * (segments + 1) + 4, segments + 4, segments + 5, 2 * (segments + 1) + 4, 2, 1, 2 * (segments + 1) + 3, 3, 4 * (segments + 1) + 3, 4, 0];
    var indices = [n[0], n[1], n[2], n[0], n[2], n[3], n[4], n[5], n[6], n[4], n[6], n[7], n[8], n[9], n[10], n[8], n[10], n[11]];
    var phi, cos, sin, xc, yc, uc, vc, idx;

    for (var i = 0; i < 4; i++) {
      xc = i < 1 || i > 2 ? wi : -wi;
      yc = i < 2 ? hi : -hi;
      uc = i < 1 || i > 2 ? ur : ul;
      vc = i < 2 ? vh : vl;

      for (var j = 0; j <= segments; j++) {
        phi = Math.PI / 2 * (i + j / segments);
        cos = Math.cos(phi);
        sin = Math.sin(phi);
        positions.push(xc + radius * cos, yc + radius * sin, 0);
        uvs.push(uc + ul * cos, vc + vl * sin);

        if (j < segments) {
          idx = (segments + 1) * i + j + 4;
          indices.push(i, idx, idx + 1);
        }
      }
    }

    _this.setIndex(new THREE__namespace.BufferAttribute(new Uint32Array(indices), 1));

    _this.setAttribute("position", new THREE__namespace.BufferAttribute(new Float32Array(positions), 3));

    _this.setAttribute("uv", new THREE__namespace.BufferAttribute(new Float32Array(uvs), 2));

    return _this;
  }

  return RoundedPlaneGeometry;
}(THREE__namespace.BufferGeometry);

var geometry = /*#__PURE__*/Object.freeze({
  __proto__: null,
  RoundedPlaneGeometry: RoundedPlaneGeometry
});

exports.RoundedPlaneGeometry = RoundedPlaneGeometry;
exports.geometry = geometry;
