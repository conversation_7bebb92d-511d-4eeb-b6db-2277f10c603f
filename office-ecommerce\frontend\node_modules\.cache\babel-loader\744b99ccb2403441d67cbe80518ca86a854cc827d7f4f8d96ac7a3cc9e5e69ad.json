{"ast": null, "code": "import { Vector3, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>3, <PERSON><PERSON><PERSON>, Matrix4 } from 'three';\nimport { CENTER, BYTES_PER_NODE, IS_LEAFNODE_FLAG } from './Constants.js';\nimport { buildPackedTree } from './buildFunctions.js';\nimport { raycast, raycastFirst, shapecast, intersectsGeometry, setBuffer, clearBuffer } from './castFunctions.js';\nimport { OrientedBox } from '../math/OrientedBox.js';\nimport { ExtendedTriangle } from '../math/ExtendedTriangle.js';\nimport { PrimitivePool } from '../utils/PrimitivePool.js';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nimport { iterateOverTriangles, setTriangle } from '../utils/TriangleUtilities.js';\nconst SKIP_GENERATION = Symbol('skip tree generation');\nconst aabb = /* @__PURE__ */new Box3();\nconst aabb2 = /* @__PURE__ */new Box3();\nconst tempMatrix = /* @__PURE__ */new Matrix4();\nconst obb = /* @__PURE__ */new OrientedBox();\nconst obb2 = /* @__PURE__ */new OrientedBox();\nconst temp = /* @__PURE__ */new Vector3();\nconst temp1 = /* @__PURE__ */new Vector3();\nconst temp2 = /* @__PURE__ */new Vector3();\nconst temp3 = /* @__PURE__ */new Vector3();\nconst temp4 = /* @__PURE__ */new Vector3();\nconst tempBox = /* @__PURE__ */new Box3();\nconst trianglePool = /* @__PURE__ */new PrimitivePool(() => new ExtendedTriangle());\nexport class MeshBVH {\n  static serialize(bvh, options = {}) {\n    if (options.isBufferGeometry) {\n      console.warn('MeshBVH.serialize: The arguments for the function have changed. See documentation for new signature.');\n      return MeshBVH.serialize(arguments[0], {\n        cloneBuffers: arguments[2] === undefined ? true : arguments[2]\n      });\n    }\n    options = {\n      cloneBuffers: true,\n      ...options\n    };\n    const geometry = bvh.geometry;\n    const rootData = bvh._roots;\n    const indexAttribute = geometry.getIndex();\n    let result;\n    if (options.cloneBuffers) {\n      result = {\n        roots: rootData.map(root => root.slice()),\n        index: indexAttribute.array.slice()\n      };\n    } else {\n      result = {\n        roots: rootData,\n        index: indexAttribute.array\n      };\n    }\n    return result;\n  }\n  static deserialize(data, geometry, options = {}) {\n    if (typeof options === 'boolean') {\n      console.warn('MeshBVH.deserialize: The arguments for the function have changed. See documentation for new signature.');\n      return MeshBVH.deserialize(arguments[0], arguments[1], {\n        setIndex: arguments[2] === undefined ? true : arguments[2]\n      });\n    }\n    options = {\n      setIndex: true,\n      ...options\n    };\n    const {\n      index,\n      roots\n    } = data;\n    const bvh = new MeshBVH(geometry, {\n      ...options,\n      [SKIP_GENERATION]: true\n    });\n    bvh._roots = roots;\n    if (options.setIndex) {\n      const indexAttribute = geometry.getIndex();\n      if (indexAttribute === null) {\n        const newIndex = new BufferAttribute(data.index, 1, false);\n        geometry.setIndex(newIndex);\n      } else if (indexAttribute.array !== index) {\n        indexAttribute.array.set(index);\n        indexAttribute.needsUpdate = true;\n      }\n    }\n    return bvh;\n  }\n  constructor(geometry, options = {}) {\n    if (!geometry.isBufferGeometry) {\n      throw new Error('MeshBVH: Only BufferGeometries are supported.');\n    } else if (geometry.index && geometry.index.isInterleavedBufferAttribute) {\n      throw new Error('MeshBVH: InterleavedBufferAttribute is not supported for the index attribute.');\n    }\n\n    // default options\n    options = Object.assign({\n      strategy: CENTER,\n      maxDepth: 40,\n      maxLeafTris: 10,\n      verbose: true,\n      useSharedArrayBuffer: false,\n      setBoundingBox: true,\n      onProgress: null,\n      // undocumented options\n\n      // Whether to skip generating the tree. Used for deserialization.\n      [SKIP_GENERATION]: false\n    }, options);\n    if (options.useSharedArrayBuffer && typeof SharedArrayBuffer === 'undefined') {\n      throw new Error('MeshBVH: SharedArrayBuffer is not available.');\n    }\n    this._roots = null;\n    if (!options[SKIP_GENERATION]) {\n      this._roots = buildPackedTree(geometry, options);\n      if (!geometry.boundingBox && options.setBoundingBox) {\n        geometry.boundingBox = this.getBoundingBox(new Box3());\n      }\n    }\n\n    // retain references to the geometry so we can use them it without having to\n    // take a geometry reference in every function.\n    this.geometry = geometry;\n  }\n  refit(nodeIndices = null) {\n    if (nodeIndices && Array.isArray(nodeIndices)) {\n      nodeIndices = new Set(nodeIndices);\n    }\n    const geometry = this.geometry;\n    const indexArr = geometry.index.array;\n    const posAttr = geometry.attributes.position;\n    let buffer, uint32Array, uint16Array, float32Array;\n    let byteOffset = 0;\n    const roots = this._roots;\n    for (let i = 0, l = roots.length; i < l; i++) {\n      buffer = roots[i];\n      uint32Array = new Uint32Array(buffer);\n      uint16Array = new Uint16Array(buffer);\n      float32Array = new Float32Array(buffer);\n      _traverse(0, byteOffset);\n      byteOffset += buffer.byteLength;\n    }\n    function _traverse(node32Index, byteOffset, force = false) {\n      const node16Index = node32Index * 2;\n      const isLeaf = uint16Array[node16Index + 15] === IS_LEAFNODE_FLAG;\n      if (isLeaf) {\n        const offset = uint32Array[node32Index + 6];\n        const count = uint16Array[node16Index + 14];\n        let minx = Infinity;\n        let miny = Infinity;\n        let minz = Infinity;\n        let maxx = -Infinity;\n        let maxy = -Infinity;\n        let maxz = -Infinity;\n        for (let i = 3 * offset, l = 3 * (offset + count); i < l; i++) {\n          const index = indexArr[i];\n          const x = posAttr.getX(index);\n          const y = posAttr.getY(index);\n          const z = posAttr.getZ(index);\n          if (x < minx) minx = x;\n          if (x > maxx) maxx = x;\n          if (y < miny) miny = y;\n          if (y > maxy) maxy = y;\n          if (z < minz) minz = z;\n          if (z > maxz) maxz = z;\n        }\n        if (float32Array[node32Index + 0] !== minx || float32Array[node32Index + 1] !== miny || float32Array[node32Index + 2] !== minz || float32Array[node32Index + 3] !== maxx || float32Array[node32Index + 4] !== maxy || float32Array[node32Index + 5] !== maxz) {\n          float32Array[node32Index + 0] = minx;\n          float32Array[node32Index + 1] = miny;\n          float32Array[node32Index + 2] = minz;\n          float32Array[node32Index + 3] = maxx;\n          float32Array[node32Index + 4] = maxy;\n          float32Array[node32Index + 5] = maxz;\n          return true;\n        } else {\n          return false;\n        }\n      } else {\n        const left = node32Index + 8;\n        const right = uint32Array[node32Index + 6];\n\n        // the identifying node indices provided by the shapecast function include offsets of all\n        // root buffers to guarantee they're unique between roots so offset left and right indices here.\n        const offsetLeft = left + byteOffset;\n        const offsetRight = right + byteOffset;\n        let forceChildren = force;\n        let includesLeft = false;\n        let includesRight = false;\n        if (nodeIndices) {\n          // if we see that neither the left or right child are included in the set that need to be updated\n          // then we assume that all children need to be updated.\n          if (!forceChildren) {\n            includesLeft = nodeIndices.has(offsetLeft);\n            includesRight = nodeIndices.has(offsetRight);\n            forceChildren = !includesLeft && !includesRight;\n          }\n        } else {\n          includesLeft = true;\n          includesRight = true;\n        }\n        const traverseLeft = forceChildren || includesLeft;\n        const traverseRight = forceChildren || includesRight;\n        let leftChange = false;\n        if (traverseLeft) {\n          leftChange = _traverse(left, byteOffset, forceChildren);\n        }\n        let rightChange = false;\n        if (traverseRight) {\n          rightChange = _traverse(right, byteOffset, forceChildren);\n        }\n        const didChange = leftChange || rightChange;\n        if (didChange) {\n          for (let i = 0; i < 3; i++) {\n            const lefti = left + i;\n            const righti = right + i;\n            const minLeftValue = float32Array[lefti];\n            const maxLeftValue = float32Array[lefti + 3];\n            const minRightValue = float32Array[righti];\n            const maxRightValue = float32Array[righti + 3];\n            float32Array[node32Index + i] = minLeftValue < minRightValue ? minLeftValue : minRightValue;\n            float32Array[node32Index + i + 3] = maxLeftValue > maxRightValue ? maxLeftValue : maxRightValue;\n          }\n        }\n        return didChange;\n      }\n    }\n  }\n  traverse(callback, rootIndex = 0) {\n    const buffer = this._roots[rootIndex];\n    const uint32Array = new Uint32Array(buffer);\n    const uint16Array = new Uint16Array(buffer);\n    _traverse(0);\n    function _traverse(node32Index, depth = 0) {\n      const node16Index = node32Index * 2;\n      const isLeaf = uint16Array[node16Index + 15] === IS_LEAFNODE_FLAG;\n      if (isLeaf) {\n        const offset = uint32Array[node32Index + 6];\n        const count = uint16Array[node16Index + 14];\n        callback(depth, isLeaf, new Float32Array(buffer, node32Index * 4, 6), offset, count);\n      } else {\n        // TODO: use node functions here\n        const left = node32Index + BYTES_PER_NODE / 4;\n        const right = uint32Array[node32Index + 6];\n        const splitAxis = uint32Array[node32Index + 7];\n        const stopTraversal = callback(depth, isLeaf, new Float32Array(buffer, node32Index * 4, 6), splitAxis);\n        if (!stopTraversal) {\n          _traverse(left, depth + 1);\n          _traverse(right, depth + 1);\n        }\n      }\n    }\n  }\n\n  /* Core Cast Functions */\n  raycast(ray, materialOrSide = FrontSide) {\n    const roots = this._roots;\n    const geometry = this.geometry;\n    const intersects = [];\n    const isMaterial = materialOrSide.isMaterial;\n    const isArrayMaterial = Array.isArray(materialOrSide);\n    const groups = geometry.groups;\n    const side = isMaterial ? materialOrSide.side : materialOrSide;\n    for (let i = 0, l = roots.length; i < l; i++) {\n      const materialSide = isArrayMaterial ? materialOrSide[groups[i].materialIndex].side : side;\n      const startCount = intersects.length;\n      setBuffer(roots[i]);\n      raycast(0, geometry, materialSide, ray, intersects);\n      clearBuffer();\n      if (isArrayMaterial) {\n        const materialIndex = groups[i].materialIndex;\n        for (let j = startCount, jl = intersects.length; j < jl; j++) {\n          intersects[j].face.materialIndex = materialIndex;\n        }\n      }\n    }\n    return intersects;\n  }\n  raycastFirst(ray, materialOrSide = FrontSide) {\n    const roots = this._roots;\n    const geometry = this.geometry;\n    const isMaterial = materialOrSide.isMaterial;\n    const isArrayMaterial = Array.isArray(materialOrSide);\n    let closestResult = null;\n    const groups = geometry.groups;\n    const side = isMaterial ? materialOrSide.side : materialOrSide;\n    for (let i = 0, l = roots.length; i < l; i++) {\n      const materialSide = isArrayMaterial ? materialOrSide[groups[i].materialIndex].side : side;\n      setBuffer(roots[i]);\n      const result = raycastFirst(0, geometry, materialSide, ray);\n      clearBuffer();\n      if (result != null && (closestResult == null || result.distance < closestResult.distance)) {\n        closestResult = result;\n        if (isArrayMaterial) {\n          result.face.materialIndex = groups[i].materialIndex;\n        }\n      }\n    }\n    return closestResult;\n  }\n  intersectsGeometry(otherGeometry, geomToMesh) {\n    const geometry = this.geometry;\n    let result = false;\n    for (const root of this._roots) {\n      setBuffer(root);\n      result = intersectsGeometry(0, geometry, otherGeometry, geomToMesh);\n      clearBuffer();\n      if (result) {\n        break;\n      }\n    }\n    return result;\n  }\n  shapecast(callbacks, _intersectsTriangleFunc, _orderNodesFunc) {\n    const geometry = this.geometry;\n    if (callbacks instanceof Function) {\n      if (_intersectsTriangleFunc) {\n        // Support the previous function signature that provided three sequential index buffer\n        // indices here.\n        const originalTriangleFunc = _intersectsTriangleFunc;\n        _intersectsTriangleFunc = (tri, index, contained, depth) => {\n          const i3 = index * 3;\n          return originalTriangleFunc(tri, i3, i3 + 1, i3 + 2, contained, depth);\n        };\n      }\n      callbacks = {\n        boundsTraverseOrder: _orderNodesFunc,\n        intersectsBounds: callbacks,\n        intersectsTriangle: _intersectsTriangleFunc,\n        intersectsRange: null\n      };\n      console.warn('MeshBVH: Shapecast function signature has changed and now takes an object of callbacks as a second argument. See docs for new signature.');\n    }\n    const triangle = trianglePool.getPrimitive();\n    let {\n      boundsTraverseOrder,\n      intersectsBounds,\n      intersectsRange,\n      intersectsTriangle\n    } = callbacks;\n    if (intersectsRange && intersectsTriangle) {\n      const originalIntersectsRange = intersectsRange;\n      intersectsRange = (offset, count, contained, depth, nodeIndex) => {\n        if (!originalIntersectsRange(offset, count, contained, depth, nodeIndex)) {\n          return iterateOverTriangles(offset, count, geometry, intersectsTriangle, contained, depth, triangle);\n        }\n        return true;\n      };\n    } else if (!intersectsRange) {\n      if (intersectsTriangle) {\n        intersectsRange = (offset, count, contained, depth) => {\n          return iterateOverTriangles(offset, count, geometry, intersectsTriangle, contained, depth, triangle);\n        };\n      } else {\n        intersectsRange = (offset, count, contained) => {\n          return contained;\n        };\n      }\n    }\n    let result = false;\n    let byteOffset = 0;\n    for (const root of this._roots) {\n      setBuffer(root);\n      result = shapecast(0, geometry, intersectsBounds, intersectsRange, boundsTraverseOrder, byteOffset);\n      clearBuffer();\n      if (result) {\n        break;\n      }\n      byteOffset += root.byteLength;\n    }\n    trianglePool.releasePrimitive(triangle);\n    return result;\n  }\n  bvhcast(otherBvh, matrixToLocal, callbacks) {\n    // BVHCast function for intersecting two BVHs against each other. Ultimately just uses two recursive shapecast calls rather\n    // than an approach that walks down the tree (see bvhcast.js file for more info).\n\n    let {\n      intersectsRanges,\n      intersectsTriangles\n    } = callbacks;\n    const indexAttr = this.geometry.index;\n    const positionAttr = this.geometry.attributes.position;\n    const otherIndexAttr = otherBvh.geometry.index;\n    const otherPositionAttr = otherBvh.geometry.attributes.position;\n    tempMatrix.copy(matrixToLocal).invert();\n    const triangle = trianglePool.getPrimitive();\n    const triangle2 = trianglePool.getPrimitive();\n    if (intersectsTriangles) {\n      function iterateOverDoubleTriangles(offset1, count1, offset2, count2, depth1, index1, depth2, index2) {\n        for (let i2 = offset2, l2 = offset2 + count2; i2 < l2; i2++) {\n          setTriangle(triangle2, i2 * 3, otherIndexAttr, otherPositionAttr);\n          triangle2.a.applyMatrix4(matrixToLocal);\n          triangle2.b.applyMatrix4(matrixToLocal);\n          triangle2.c.applyMatrix4(matrixToLocal);\n          triangle2.needsUpdate = true;\n          for (let i1 = offset1, l1 = offset1 + count1; i1 < l1; i1++) {\n            setTriangle(triangle, i1 * 3, indexAttr, positionAttr);\n            triangle.needsUpdate = true;\n            if (intersectsTriangles(triangle, triangle2, i1, i2, depth1, index1, depth2, index2)) {\n              return true;\n            }\n          }\n        }\n        return false;\n      }\n      if (intersectsRanges) {\n        const originalIntersectsRanges = intersectsRanges;\n        intersectsRanges = function (offset1, count1, offset2, count2, depth1, index1, depth2, index2) {\n          if (!originalIntersectsRanges(offset1, count1, offset2, count2, depth1, index1, depth2, index2)) {\n            return iterateOverDoubleTriangles(offset1, count1, offset2, count2, depth1, index1, depth2, index2);\n          }\n          return true;\n        };\n      } else {\n        intersectsRanges = iterateOverDoubleTriangles;\n      }\n    }\n    otherBvh.getBoundingBox(aabb2);\n    aabb2.applyMatrix4(matrixToLocal);\n    const result = this.shapecast({\n      intersectsBounds: box => aabb2.intersectsBox(box),\n      intersectsRange: (offset1, count1, contained, depth1, nodeIndex1, box) => {\n        aabb.copy(box);\n        aabb.applyMatrix4(tempMatrix);\n        return otherBvh.shapecast({\n          intersectsBounds: box => aabb.intersectsBox(box),\n          intersectsRange: (offset2, count2, contained, depth2, nodeIndex2) => {\n            return intersectsRanges(offset1, count1, offset2, count2, depth1, nodeIndex1, depth2, nodeIndex2);\n          }\n        });\n      }\n    });\n    trianglePool.releasePrimitive(triangle);\n    trianglePool.releasePrimitive(triangle2);\n    return result;\n  }\n\n  /* Derived Cast Functions */\n  intersectsBox(box, boxToMesh) {\n    obb.set(box.min, box.max, boxToMesh);\n    obb.needsUpdate = true;\n    return this.shapecast({\n      intersectsBounds: box => obb.intersectsBox(box),\n      intersectsTriangle: tri => obb.intersectsTriangle(tri)\n    });\n  }\n  intersectsSphere(sphere) {\n    return this.shapecast({\n      intersectsBounds: box => sphere.intersectsBox(box),\n      intersectsTriangle: tri => tri.intersectsSphere(sphere)\n    });\n  }\n  closestPointToGeometry(otherGeometry, geometryToBvh, target1 = {}, target2 = {}, minThreshold = 0, maxThreshold = Infinity) {\n    if (!otherGeometry.boundingBox) {\n      otherGeometry.computeBoundingBox();\n    }\n    obb.set(otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh);\n    obb.needsUpdate = true;\n    const geometry = this.geometry;\n    const pos = geometry.attributes.position;\n    const index = geometry.index;\n    const otherPos = otherGeometry.attributes.position;\n    const otherIndex = otherGeometry.index;\n    const triangle = trianglePool.getPrimitive();\n    const triangle2 = trianglePool.getPrimitive();\n    let tempTarget1 = temp1;\n    let tempTargetDest1 = temp2;\n    let tempTarget2 = null;\n    let tempTargetDest2 = null;\n    if (target2) {\n      tempTarget2 = temp3;\n      tempTargetDest2 = temp4;\n    }\n    let closestDistance = Infinity;\n    let closestDistanceTriIndex = null;\n    let closestDistanceOtherTriIndex = null;\n    tempMatrix.copy(geometryToBvh).invert();\n    obb2.matrix.copy(tempMatrix);\n    this.shapecast({\n      boundsTraverseOrder: box => {\n        return obb.distanceToBox(box);\n      },\n      intersectsBounds: (box, isLeaf, score) => {\n        if (score < closestDistance && score < maxThreshold) {\n          // if we know the triangles of this bounds will be intersected next then\n          // save the bounds to use during triangle checks.\n          if (isLeaf) {\n            obb2.min.copy(box.min);\n            obb2.max.copy(box.max);\n            obb2.needsUpdate = true;\n          }\n          return true;\n        }\n        return false;\n      },\n      intersectsRange: (offset, count) => {\n        if (otherGeometry.boundsTree) {\n          // if the other geometry has a bvh then use the accelerated path where we use shapecast to find\n          // the closest bounds in the other geometry to check.\n          return otherGeometry.boundsTree.shapecast({\n            boundsTraverseOrder: box => {\n              return obb2.distanceToBox(box);\n            },\n            intersectsBounds: (box, isLeaf, score) => {\n              return score < closestDistance && score < maxThreshold;\n            },\n            intersectsRange: (otherOffset, otherCount) => {\n              for (let i2 = otherOffset * 3, l2 = (otherOffset + otherCount) * 3; i2 < l2; i2 += 3) {\n                setTriangle(triangle2, i2, otherIndex, otherPos);\n                triangle2.a.applyMatrix4(geometryToBvh);\n                triangle2.b.applyMatrix4(geometryToBvh);\n                triangle2.c.applyMatrix4(geometryToBvh);\n                triangle2.needsUpdate = true;\n                for (let i = offset * 3, l = (offset + count) * 3; i < l; i += 3) {\n                  setTriangle(triangle, i, index, pos);\n                  triangle.needsUpdate = true;\n                  const dist = triangle.distanceToTriangle(triangle2, tempTarget1, tempTarget2);\n                  if (dist < closestDistance) {\n                    tempTargetDest1.copy(tempTarget1);\n                    if (tempTargetDest2) {\n                      tempTargetDest2.copy(tempTarget2);\n                    }\n                    closestDistance = dist;\n                    closestDistanceTriIndex = i / 3;\n                    closestDistanceOtherTriIndex = i2 / 3;\n                  }\n\n                  // stop traversal if we find a point that's under the given threshold\n                  if (dist < minThreshold) {\n                    return true;\n                  }\n                }\n              }\n            }\n          });\n        } else {\n          // If no bounds tree then we'll just check every triangle.\n          const triCount = otherIndex ? otherIndex.count : otherPos.count;\n          for (let i2 = 0, l2 = triCount; i2 < l2; i2 += 3) {\n            setTriangle(triangle2, i2, otherIndex, otherPos);\n            triangle2.a.applyMatrix4(geometryToBvh);\n            triangle2.b.applyMatrix4(geometryToBvh);\n            triangle2.c.applyMatrix4(geometryToBvh);\n            triangle2.needsUpdate = true;\n            for (let i = offset * 3, l = (offset + count) * 3; i < l; i += 3) {\n              setTriangle(triangle, i, index, pos);\n              triangle.needsUpdate = true;\n              const dist = triangle.distanceToTriangle(triangle2, tempTarget1, tempTarget2);\n              if (dist < closestDistance) {\n                tempTargetDest1.copy(tempTarget1);\n                if (tempTargetDest2) {\n                  tempTargetDest2.copy(tempTarget2);\n                }\n                closestDistance = dist;\n                closestDistanceTriIndex = i / 3;\n                closestDistanceOtherTriIndex = i2 / 3;\n              }\n\n              // stop traversal if we find a point that's under the given threshold\n              if (dist < minThreshold) {\n                return true;\n              }\n            }\n          }\n        }\n      }\n    });\n    trianglePool.releasePrimitive(triangle);\n    trianglePool.releasePrimitive(triangle2);\n    if (closestDistance === Infinity) return null;\n    if (!target1.point) target1.point = tempTargetDest1.clone();else target1.point.copy(tempTargetDest1);\n    target1.distance = closestDistance, target1.faceIndex = closestDistanceTriIndex;\n    if (target2) {\n      if (!target2.point) target2.point = tempTargetDest2.clone();else target2.point.copy(tempTargetDest2);\n      target2.point.applyMatrix4(tempMatrix);\n      tempTargetDest1.applyMatrix4(tempMatrix);\n      target2.distance = tempTargetDest1.sub(target2.point).length();\n      target2.faceIndex = closestDistanceOtherTriIndex;\n    }\n    return target1;\n  }\n  closestPointToPoint(point, target = {}, minThreshold = 0, maxThreshold = Infinity) {\n    // early out if under minThreshold\n    // skip checking if over maxThreshold\n    // set minThreshold = maxThreshold to quickly check if a point is within a threshold\n    // returns Infinity if no value found\n    const minThresholdSq = minThreshold * minThreshold;\n    const maxThresholdSq = maxThreshold * maxThreshold;\n    let closestDistanceSq = Infinity;\n    let closestDistanceTriIndex = null;\n    this.shapecast({\n      boundsTraverseOrder: box => {\n        temp.copy(point).clamp(box.min, box.max);\n        return temp.distanceToSquared(point);\n      },\n      intersectsBounds: (box, isLeaf, score) => {\n        return score < closestDistanceSq && score < maxThresholdSq;\n      },\n      intersectsTriangle: (tri, triIndex) => {\n        tri.closestPointToPoint(point, temp);\n        const distSq = point.distanceToSquared(temp);\n        if (distSq < closestDistanceSq) {\n          temp1.copy(temp);\n          closestDistanceSq = distSq;\n          closestDistanceTriIndex = triIndex;\n        }\n        if (distSq < minThresholdSq) {\n          return true;\n        } else {\n          return false;\n        }\n      }\n    });\n    if (closestDistanceSq === Infinity) return null;\n    const closestDistance = Math.sqrt(closestDistanceSq);\n    if (!target.point) target.point = temp1.clone();else target.point.copy(temp1);\n    target.distance = closestDistance, target.faceIndex = closestDistanceTriIndex;\n    return target;\n  }\n  getBoundingBox(target) {\n    target.makeEmpty();\n    const roots = this._roots;\n    roots.forEach(buffer => {\n      arrayToBox(0, new Float32Array(buffer), tempBox);\n      target.union(tempBox);\n    });\n    return target;\n  }\n}", "map": {"version": 3, "names": ["Vector3", "BufferAttribute", "Box3", "FrontSide", "Matrix4", "CENTER", "BYTES_PER_NODE", "IS_LEAFNODE_FLAG", "buildPackedTree", "raycast", "raycastFirst", "shapecast", "intersectsGeometry", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "OrientedBox", "ExtendedTriangle", "PrimitivePool", "arrayToBox", "iterateOverTriangles", "set<PERSON>riangle", "SKIP_GENERATION", "Symbol", "aabb", "aabb2", "tempMatrix", "obb", "obb2", "temp", "temp1", "temp2", "temp3", "temp4", "tempBox", "trianglePool", "MeshBVH", "serialize", "bvh", "options", "isBufferGeometry", "console", "warn", "arguments", "cloneBuffers", "undefined", "geometry", "rootData", "_roots", "indexAttribute", "getIndex", "result", "roots", "map", "root", "slice", "index", "array", "deserialize", "data", "setIndex", "newIndex", "set", "needsUpdate", "constructor", "Error", "isInterleavedBufferAttribute", "Object", "assign", "strategy", "max<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "verbose", "useSharedArrayBuffer", "setBoundingBox", "onProgress", "SharedArrayBuffer", "boundingBox", "getBoundingBox", "refit", "nodeIndices", "Array", "isArray", "Set", "indexArr", "posAttr", "attributes", "position", "buffer", "uint32Array", "uint16Array", "float32Array", "byteOffset", "i", "l", "length", "Uint32Array", "Uint16Array", "Float32Array", "_traverse", "byteLength", "node32Index", "force", "node16Index", "<PERSON><PERSON><PERSON><PERSON>", "offset", "count", "minx", "Infinity", "miny", "minz", "maxx", "maxy", "maxz", "x", "getX", "y", "getY", "z", "getZ", "left", "right", "offsetLeft", "offsetRight", "forceChildren", "includesLeft", "includesRight", "has", "traverseLeft", "traverseRight", "leftChange", "rightChange", "<PERSON><PERSON><PERSON><PERSON>", "lefti", "righti", "minLeftValue", "maxLeftValue", "minRightValue", "maxRightValue", "traverse", "callback", "rootIndex", "depth", "splitAxis", "stopTraversal", "ray", "materialOrSide", "intersects", "isMaterial", "isArrayMaterial", "groups", "side", "materialSide", "materialIndex", "startCount", "j", "jl", "face", "closestResult", "distance", "otherGeometry", "geomToMesh", "callbacks", "_intersectsTriangleFunc", "_orderNodesFunc", "Function", "originalTriangleFunc", "tri", "contained", "i3", "boundsTraverseOrder", "intersectsBounds", "intersectsTriangle", "intersectsRange", "triangle", "getPrimitive", "originalIntersectsRange", "nodeIndex", "releasePrimitive", "bvhcast", "otherBvh", "matrixToLocal", "intersectsRanges", "intersects<PERSON><PERSON><PERSON>", "indexAttr", "positionAttr", "otherIndexAttr", "otherPositionAttr", "copy", "invert", "triangle2", "iterateOverDoubleTriangles", "offset1", "count1", "offset2", "count2", "depth1", "index1", "depth2", "index2", "i2", "l2", "a", "applyMatrix4", "b", "c", "i1", "l1", "originalIntersectsRanges", "box", "intersectsBox", "nodeIndex1", "nodeIndex2", "boxToMesh", "min", "max", "intersectsSphere", "sphere", "closestPointToGeometry", "geometryToBvh", "target1", "target2", "min<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON>", "computeBoundingBox", "pos", "otherPos", "otherIndex", "tempTarget1", "tempTargetDest1", "tempTarget2", "tempTargetDest2", "closestDistance", "closestDistanceTriIndex", "closestDistanceOtherTriIndex", "matrix", "distanceToBox", "score", "boundsTree", "otherOffset", "otherCount", "dist", "distanceToTriangle", "triCount", "point", "clone", "faceIndex", "sub", "closestPointToPoint", "target", "minThresholdSq", "maxThresholdSq", "closestDistanceSq", "clamp", "distanceToSquared", "triIndex", "distSq", "Math", "sqrt", "makeEmpty", "for<PERSON>ach", "union"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/three-mesh-bvh/src/core/MeshBVH.js"], "sourcesContent": ["import { Vector3, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Matrix4 } from 'three';\nimport { CENTER, BYTES_PER_NODE, IS_LEAFNODE_FLAG } from './Constants.js';\nimport { buildPackedTree } from './buildFunctions.js';\nimport {\n\traycast,\n\traycastFirst,\n\tshapecast,\n\tintersectsGeometry,\n\tsetBuffer,\n\tclearBuffer,\n} from './castFunctions.js';\nimport { OrientedBox } from '../math/OrientedBox.js';\nimport { ExtendedTriangle } from '../math/ExtendedTriangle.js';\nimport { PrimitivePool } from '../utils/PrimitivePool.js';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nimport { iterateOverTriangles, setTriangle } from '../utils/TriangleUtilities.js';\n\nconst SKIP_GENERATION = Symbol( 'skip tree generation' );\n\nconst aabb = /* @__PURE__ */ new Box3();\nconst aabb2 = /* @__PURE__ */ new Box3();\nconst tempMatrix = /* @__PURE__ */ new Matrix4();\nconst obb = /* @__PURE__ */ new OrientedBox();\nconst obb2 = /* @__PURE__ */ new OrientedBox();\nconst temp = /* @__PURE__ */ new Vector3();\nconst temp1 = /* @__PURE__ */ new Vector3();\nconst temp2 = /* @__PURE__ */ new Vector3();\nconst temp3 = /* @__PURE__ */ new Vector3();\nconst temp4 = /* @__PURE__ */ new Vector3();\nconst tempBox = /* @__PURE__ */ new Box3();\nconst trianglePool = /* @__PURE__ */ new PrimitivePool( () => new ExtendedTriangle() );\n\nexport class MeshBVH {\n\n\tstatic serialize( bvh, options = {} ) {\n\n\t\tif ( options.isBufferGeometry ) {\n\n\t\t\tconsole.warn( 'MeshBVH.serialize: The arguments for the function have changed. See documentation for new signature.' );\n\n\t\t\treturn MeshBVH.serialize(\n\t\t\t\targuments[ 0 ],\n\t\t\t\t{\n\t\t\t\t\tcloneBuffers: arguments[ 2 ] === undefined ? true : arguments[ 2 ],\n\t\t\t\t}\n\t\t\t);\n\n\t\t}\n\n\t\toptions = {\n\t\t\tcloneBuffers: true,\n\t\t\t...options,\n\t\t};\n\n\t\tconst geometry = bvh.geometry;\n\t\tconst rootData = bvh._roots;\n\t\tconst indexAttribute = geometry.getIndex();\n\t\tlet result;\n\t\tif ( options.cloneBuffers ) {\n\n\t\t\tresult = {\n\t\t\t\troots: rootData.map( root => root.slice() ),\n\t\t\t\tindex: indexAttribute.array.slice(),\n\t\t\t};\n\n\t\t} else {\n\n\t\t\tresult = {\n\t\t\t\troots: rootData,\n\t\t\t\tindex: indexAttribute.array,\n\t\t\t};\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n\tstatic deserialize( data, geometry, options = {} ) {\n\n\t\tif ( typeof options === 'boolean' ) {\n\n\t\t\tconsole.warn( 'MeshBVH.deserialize: The arguments for the function have changed. See documentation for new signature.' );\n\n\t\t\treturn MeshBVH.deserialize(\n\t\t\t\targuments[ 0 ],\n\t\t\t\targuments[ 1 ],\n\t\t\t\t{\n\t\t\t\t\tsetIndex: arguments[ 2 ] === undefined ? true : arguments[ 2 ],\n\t\t\t\t}\n\t\t\t);\n\n\t\t}\n\n\t\toptions = {\n\t\t\tsetIndex: true,\n\t\t\t...options,\n\t\t};\n\n\t\tconst { index, roots } = data;\n\t\tconst bvh = new MeshBVH( geometry, { ...options, [ SKIP_GENERATION ]: true } );\n\t\tbvh._roots = roots;\n\n\t\tif ( options.setIndex ) {\n\n\t\t\tconst indexAttribute = geometry.getIndex();\n\t\t\tif ( indexAttribute === null ) {\n\n\t\t\t\tconst newIndex = new BufferAttribute( data.index, 1, false );\n\t\t\t\tgeometry.setIndex( newIndex );\n\n\t\t\t} else if ( indexAttribute.array !== index ) {\n\n\t\t\t\tindexAttribute.array.set( index );\n\t\t\t\tindexAttribute.needsUpdate = true;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn bvh;\n\n\t}\n\n\tconstructor( geometry, options = {} ) {\n\n\t\tif ( ! geometry.isBufferGeometry ) {\n\n\t\t\tthrow new Error( 'MeshBVH: Only BufferGeometries are supported.' );\n\n\t\t} else if ( geometry.index && geometry.index.isInterleavedBufferAttribute ) {\n\n\t\t\tthrow new Error( 'MeshBVH: InterleavedBufferAttribute is not supported for the index attribute.' );\n\n\t\t}\n\n\t\t// default options\n\t\toptions = Object.assign( {\n\n\t\t\tstrategy: CENTER,\n\t\t\tmaxDepth: 40,\n\t\t\tmaxLeafTris: 10,\n\t\t\tverbose: true,\n\t\t\tuseSharedArrayBuffer: false,\n\t\t\tsetBoundingBox: true,\n\t\t\tonProgress: null,\n\n\t\t\t// undocumented options\n\n\t\t\t// Whether to skip generating the tree. Used for deserialization.\n\t\t\t[ SKIP_GENERATION ]: false,\n\n\t\t}, options );\n\n\t\tif ( options.useSharedArrayBuffer && typeof SharedArrayBuffer === 'undefined' ) {\n\n\t\t\tthrow new Error( 'MeshBVH: SharedArrayBuffer is not available.' );\n\n\t\t}\n\n\t\tthis._roots = null;\n\t\tif ( ! options[ SKIP_GENERATION ] ) {\n\n\t\t\tthis._roots = buildPackedTree( geometry, options );\n\n\t\t\tif ( ! geometry.boundingBox && options.setBoundingBox ) {\n\n\t\t\t\tgeometry.boundingBox = this.getBoundingBox( new Box3() );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// retain references to the geometry so we can use them it without having to\n\t\t// take a geometry reference in every function.\n\t\tthis.geometry = geometry;\n\n\t}\n\n\trefit( nodeIndices = null ) {\n\n\t\tif ( nodeIndices && Array.isArray( nodeIndices ) ) {\n\n\t\t\tnodeIndices = new Set( nodeIndices );\n\n\t\t}\n\n\t\tconst geometry = this.geometry;\n\t\tconst indexArr = geometry.index.array;\n\t\tconst posAttr = geometry.attributes.position;\n\n\t\tlet buffer, uint32Array, uint16Array, float32Array;\n\t\tlet byteOffset = 0;\n\t\tconst roots = this._roots;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tbuffer = roots[ i ];\n\t\t\tuint32Array = new Uint32Array( buffer );\n\t\t\tuint16Array = new Uint16Array( buffer );\n\t\t\tfloat32Array = new Float32Array( buffer );\n\n\t\t\t_traverse( 0, byteOffset );\n\t\t\tbyteOffset += buffer.byteLength;\n\n\t\t}\n\n\t\tfunction _traverse( node32Index, byteOffset, force = false ) {\n\n\t\t\tconst node16Index = node32Index * 2;\n\t\t\tconst isLeaf = uint16Array[ node16Index + 15 ] === IS_LEAFNODE_FLAG;\n\t\t\tif ( isLeaf ) {\n\n\t\t\t\tconst offset = uint32Array[ node32Index + 6 ];\n\t\t\t\tconst count = uint16Array[ node16Index + 14 ];\n\n\t\t\t\tlet minx = Infinity;\n\t\t\t\tlet miny = Infinity;\n\t\t\t\tlet minz = Infinity;\n\t\t\t\tlet maxx = - Infinity;\n\t\t\t\tlet maxy = - Infinity;\n\t\t\t\tlet maxz = - Infinity;\n\n\t\t\t\tfor ( let i = 3 * offset, l = 3 * ( offset + count ); i < l; i ++ ) {\n\n\t\t\t\t\tconst index = indexArr[ i ];\n\t\t\t\t\tconst x = posAttr.getX( index );\n\t\t\t\t\tconst y = posAttr.getY( index );\n\t\t\t\t\tconst z = posAttr.getZ( index );\n\n\t\t\t\t\tif ( x < minx ) minx = x;\n\t\t\t\t\tif ( x > maxx ) maxx = x;\n\n\t\t\t\t\tif ( y < miny ) miny = y;\n\t\t\t\t\tif ( y > maxy ) maxy = y;\n\n\t\t\t\t\tif ( z < minz ) minz = z;\n\t\t\t\t\tif ( z > maxz ) maxz = z;\n\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\tfloat32Array[ node32Index + 0 ] !== minx ||\n\t\t\t\t\tfloat32Array[ node32Index + 1 ] !== miny ||\n\t\t\t\t\tfloat32Array[ node32Index + 2 ] !== minz ||\n\n\t\t\t\t\tfloat32Array[ node32Index + 3 ] !== maxx ||\n\t\t\t\t\tfloat32Array[ node32Index + 4 ] !== maxy ||\n\t\t\t\t\tfloat32Array[ node32Index + 5 ] !== maxz\n\t\t\t\t) {\n\n\t\t\t\t\tfloat32Array[ node32Index + 0 ] = minx;\n\t\t\t\t\tfloat32Array[ node32Index + 1 ] = miny;\n\t\t\t\t\tfloat32Array[ node32Index + 2 ] = minz;\n\n\t\t\t\t\tfloat32Array[ node32Index + 3 ] = maxx;\n\t\t\t\t\tfloat32Array[ node32Index + 4 ] = maxy;\n\t\t\t\t\tfloat32Array[ node32Index + 5 ] = maxz;\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t} else {\n\n\t\t\t\t\treturn false;\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tconst left = node32Index + 8;\n\t\t\t\tconst right = uint32Array[ node32Index + 6 ];\n\n\t\t\t\t// the identifying node indices provided by the shapecast function include offsets of all\n\t\t\t\t// root buffers to guarantee they're unique between roots so offset left and right indices here.\n\t\t\t\tconst offsetLeft = left + byteOffset;\n\t\t\t\tconst offsetRight = right + byteOffset;\n\t\t\t\tlet forceChildren = force;\n\t\t\t\tlet includesLeft = false;\n\t\t\t\tlet includesRight = false;\n\n\t\t\t\tif ( nodeIndices ) {\n\n\t\t\t\t\t// if we see that neither the left or right child are included in the set that need to be updated\n\t\t\t\t\t// then we assume that all children need to be updated.\n\t\t\t\t\tif ( ! forceChildren ) {\n\n\t\t\t\t\t\tincludesLeft = nodeIndices.has( offsetLeft );\n\t\t\t\t\t\tincludesRight = nodeIndices.has( offsetRight );\n\t\t\t\t\t\tforceChildren = ! includesLeft && ! includesRight;\n\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\n\t\t\t\t\tincludesLeft = true;\n\t\t\t\t\tincludesRight = true;\n\n\t\t\t\t}\n\n\t\t\t\tconst traverseLeft = forceChildren || includesLeft;\n\t\t\t\tconst traverseRight = forceChildren || includesRight;\n\n\t\t\t\tlet leftChange = false;\n\t\t\t\tif ( traverseLeft ) {\n\n\t\t\t\t\tleftChange = _traverse( left, byteOffset, forceChildren );\n\n\t\t\t\t}\n\n\t\t\t\tlet rightChange = false;\n\t\t\t\tif ( traverseRight ) {\n\n\t\t\t\t\trightChange = _traverse( right, byteOffset, forceChildren );\n\n\t\t\t\t}\n\n\t\t\t\tconst didChange = leftChange || rightChange;\n\t\t\t\tif ( didChange ) {\n\n\t\t\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\t\t\tconst lefti = left + i;\n\t\t\t\t\t\tconst righti = right + i;\n\t\t\t\t\t\tconst minLeftValue = float32Array[ lefti ];\n\t\t\t\t\t\tconst maxLeftValue = float32Array[ lefti + 3 ];\n\t\t\t\t\t\tconst minRightValue = float32Array[ righti ];\n\t\t\t\t\t\tconst maxRightValue = float32Array[ righti + 3 ];\n\n\t\t\t\t\t\tfloat32Array[ node32Index + i ] = minLeftValue < minRightValue ? minLeftValue : minRightValue;\n\t\t\t\t\t\tfloat32Array[ node32Index + i + 3 ] = maxLeftValue > maxRightValue ? maxLeftValue : maxRightValue;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\treturn didChange;\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\ttraverse( callback, rootIndex = 0 ) {\n\n\t\tconst buffer = this._roots[ rootIndex ];\n\t\tconst uint32Array = new Uint32Array( buffer );\n\t\tconst uint16Array = new Uint16Array( buffer );\n\t\t_traverse( 0 );\n\n\t\tfunction _traverse( node32Index, depth = 0 ) {\n\n\t\t\tconst node16Index = node32Index * 2;\n\t\t\tconst isLeaf = uint16Array[ node16Index + 15 ] === IS_LEAFNODE_FLAG;\n\t\t\tif ( isLeaf ) {\n\n\t\t\t\tconst offset = uint32Array[ node32Index + 6 ];\n\t\t\t\tconst count = uint16Array[ node16Index + 14 ];\n\t\t\t\tcallback( depth, isLeaf, new Float32Array( buffer, node32Index * 4, 6 ), offset, count );\n\n\t\t\t} else {\n\n\t\t\t\t// TODO: use node functions here\n\t\t\t\tconst left = node32Index + BYTES_PER_NODE / 4;\n\t\t\t\tconst right = uint32Array[ node32Index + 6 ];\n\t\t\t\tconst splitAxis = uint32Array[ node32Index + 7 ];\n\t\t\t\tconst stopTraversal = callback( depth, isLeaf, new Float32Array( buffer, node32Index * 4, 6 ), splitAxis );\n\n\t\t\t\tif ( ! stopTraversal ) {\n\n\t\t\t\t\t_traverse( left, depth + 1 );\n\t\t\t\t\t_traverse( right, depth + 1 );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/* Core Cast Functions */\n\traycast( ray, materialOrSide = FrontSide ) {\n\n\t\tconst roots = this._roots;\n\t\tconst geometry = this.geometry;\n\t\tconst intersects = [];\n\t\tconst isMaterial = materialOrSide.isMaterial;\n\t\tconst isArrayMaterial = Array.isArray( materialOrSide );\n\n\t\tconst groups = geometry.groups;\n\t\tconst side = isMaterial ? materialOrSide.side : materialOrSide;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tconst materialSide = isArrayMaterial ? materialOrSide[ groups[ i ].materialIndex ].side : side;\n\t\t\tconst startCount = intersects.length;\n\n\t\t\tsetBuffer( roots[ i ] );\n\t\t\traycast( 0, geometry, materialSide, ray, intersects );\n\t\t\tclearBuffer();\n\n\t\t\tif ( isArrayMaterial ) {\n\n\t\t\t\tconst materialIndex = groups[ i ].materialIndex;\n\t\t\t\tfor ( let j = startCount, jl = intersects.length; j < jl; j ++ ) {\n\n\t\t\t\t\tintersects[ j ].face.materialIndex = materialIndex;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn intersects;\n\n\t}\n\n\traycastFirst( ray, materialOrSide = FrontSide ) {\n\n\t\tconst roots = this._roots;\n\t\tconst geometry = this.geometry;\n\t\tconst isMaterial = materialOrSide.isMaterial;\n\t\tconst isArrayMaterial = Array.isArray( materialOrSide );\n\n\t\tlet closestResult = null;\n\n\t\tconst groups = geometry.groups;\n\t\tconst side = isMaterial ? materialOrSide.side : materialOrSide;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tconst materialSide = isArrayMaterial ? materialOrSide[ groups[ i ].materialIndex ].side : side;\n\n\t\t\tsetBuffer( roots[ i ] );\n\t\t\tconst result = raycastFirst( 0, geometry, materialSide, ray );\n\t\t\tclearBuffer();\n\n\t\t\tif ( result != null && ( closestResult == null || result.distance < closestResult.distance ) ) {\n\n\t\t\t\tclosestResult = result;\n\t\t\t\tif ( isArrayMaterial ) {\n\n\t\t\t\t\tresult.face.materialIndex = groups[ i ].materialIndex;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn closestResult;\n\n\t}\n\n\tintersectsGeometry( otherGeometry, geomToMesh ) {\n\n\t\tconst geometry = this.geometry;\n\t\tlet result = false;\n\t\tfor ( const root of this._roots ) {\n\n\t\t\tsetBuffer( root );\n\t\t\tresult = intersectsGeometry( 0, geometry, otherGeometry, geomToMesh );\n\t\t\tclearBuffer();\n\n\t\t\tif ( result ) {\n\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n\tshapecast( callbacks, _intersectsTriangleFunc, _orderNodesFunc ) {\n\n\t\tconst geometry = this.geometry;\n\t\tif ( callbacks instanceof Function ) {\n\n\t\t\tif ( _intersectsTriangleFunc ) {\n\n\t\t\t\t// Support the previous function signature that provided three sequential index buffer\n\t\t\t\t// indices here.\n\t\t\t\tconst originalTriangleFunc = _intersectsTriangleFunc;\n\t\t\t\t_intersectsTriangleFunc = ( tri, index, contained, depth ) => {\n\n\t\t\t\t\tconst i3 = index * 3;\n\t\t\t\t\treturn originalTriangleFunc( tri, i3, i3 + 1, i3 + 2, contained, depth );\n\n\t\t\t\t};\n\n\n\t\t\t}\n\n\t\t\tcallbacks = {\n\n\t\t\t\tboundsTraverseOrder: _orderNodesFunc,\n\t\t\t\tintersectsBounds: callbacks,\n\t\t\t\tintersectsTriangle: _intersectsTriangleFunc,\n\t\t\t\tintersectsRange: null,\n\n\t\t\t};\n\n\t\t\tconsole.warn( 'MeshBVH: Shapecast function signature has changed and now takes an object of callbacks as a second argument. See docs for new signature.' );\n\n\t\t}\n\n\t\tconst triangle = trianglePool.getPrimitive();\n\t\tlet {\n\t\t\tboundsTraverseOrder,\n\t\t\tintersectsBounds,\n\t\t\tintersectsRange,\n\t\t\tintersectsTriangle,\n\t\t} = callbacks;\n\n\t\tif ( intersectsRange && intersectsTriangle ) {\n\n\t\t\tconst originalIntersectsRange = intersectsRange;\n\t\t\tintersectsRange = ( offset, count, contained, depth, nodeIndex ) => {\n\n\t\t\t\tif ( ! originalIntersectsRange( offset, count, contained, depth, nodeIndex ) ) {\n\n\t\t\t\t\treturn iterateOverTriangles( offset, count, geometry, intersectsTriangle, contained, depth, triangle );\n\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\n\t\t\t};\n\n\t\t} else if ( ! intersectsRange ) {\n\n\t\t\tif ( intersectsTriangle ) {\n\n\t\t\t\tintersectsRange = ( offset, count, contained, depth ) => {\n\n\t\t\t\t\treturn iterateOverTriangles( offset, count, geometry, intersectsTriangle, contained, depth, triangle );\n\n\t\t\t\t};\n\n\t\t\t} else {\n\n\t\t\t\tintersectsRange = ( offset, count, contained ) => {\n\n\t\t\t\t\treturn contained;\n\n\t\t\t\t};\n\n\t\t\t}\n\n\t\t}\n\n\t\tlet result = false;\n\t\tlet byteOffset = 0;\n\t\tfor ( const root of this._roots ) {\n\n\t\t\tsetBuffer( root );\n\t\t\tresult = shapecast( 0, geometry, intersectsBounds, intersectsRange, boundsTraverseOrder, byteOffset );\n\t\t\tclearBuffer();\n\n\t\t\tif ( result ) {\n\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tbyteOffset += root.byteLength;\n\n\t\t}\n\n\t\ttrianglePool.releasePrimitive( triangle );\n\n\t\treturn result;\n\n\t}\n\n\tbvhcast( otherBvh, matrixToLocal, callbacks ) {\n\n\t\t// BVHCast function for intersecting two BVHs against each other. Ultimately just uses two recursive shapecast calls rather\n\t\t// than an approach that walks down the tree (see bvhcast.js file for more info).\n\n\t\tlet {\n\t\t\tintersectsRanges,\n\t\t\tintersectsTriangles,\n\t\t} = callbacks;\n\n\t\tconst indexAttr = this.geometry.index;\n\t\tconst positionAttr = this.geometry.attributes.position;\n\n\t\tconst otherIndexAttr = otherBvh.geometry.index;\n\t\tconst otherPositionAttr = otherBvh.geometry.attributes.position;\n\n\t\ttempMatrix.copy( matrixToLocal ).invert();\n\n\t\tconst triangle = trianglePool.getPrimitive();\n\t\tconst triangle2 = trianglePool.getPrimitive();\n\n\t\tif ( intersectsTriangles ) {\n\n\t\t\tfunction iterateOverDoubleTriangles( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) {\n\n\t\t\t\tfor ( let i2 = offset2, l2 = offset2 + count2; i2 < l2; i2 ++ ) {\n\n\t\t\t\t\tsetTriangle( triangle2, i2 * 3, otherIndexAttr, otherPositionAttr );\n\t\t\t\t\ttriangle2.a.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.b.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.c.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\tfor ( let i1 = offset1, l1 = offset1 + count1; i1 < l1; i1 ++ ) {\n\n\t\t\t\t\t\tsetTriangle( triangle, i1 * 3, indexAttr, positionAttr );\n\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\tif ( intersectsTriangles( triangle, triangle2, i1, i2, depth1, index1, depth2, index2 ) ) {\n\n\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\tif ( intersectsRanges ) {\n\n\t\t\t\tconst originalIntersectsRanges = intersectsRanges;\n\t\t\t\tintersectsRanges = function ( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) {\n\n\t\t\t\t\tif ( ! originalIntersectsRanges( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) ) {\n\n\t\t\t\t\t\treturn iterateOverDoubleTriangles( offset1, count1, offset2, count2, depth1, index1, depth2, index2 );\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t};\n\n\t\t\t} else {\n\n\t\t\t\tintersectsRanges = iterateOverDoubleTriangles;\n\n\t\t\t}\n\n\t\t}\n\n\t\totherBvh.getBoundingBox( aabb2 );\n\t\taabb2.applyMatrix4( matrixToLocal );\n\t\tconst result = this.shapecast( {\n\n\t\t\tintersectsBounds: box => aabb2.intersectsBox( box ),\n\n\t\t\tintersectsRange: ( offset1, count1, contained, depth1, nodeIndex1, box ) => {\n\n\t\t\t\taabb.copy( box );\n\t\t\t\taabb.applyMatrix4( tempMatrix );\n\t\t\t\treturn otherBvh.shapecast( {\n\n\t\t\t\t\tintersectsBounds: box => aabb.intersectsBox( box ),\n\n\t\t\t\t\tintersectsRange: ( offset2, count2, contained, depth2, nodeIndex2 ) => {\n\n\t\t\t\t\t\treturn intersectsRanges( offset1, count1, offset2, count2, depth1, nodeIndex1, depth2, nodeIndex2 );\n\n\t\t\t\t\t},\n\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t} );\n\n\t\ttrianglePool.releasePrimitive( triangle );\n\t\ttrianglePool.releasePrimitive( triangle2 );\n\t\treturn result;\n\n\t}\n\n\t/* Derived Cast Functions */\n\tintersectsBox( box, boxToMesh ) {\n\n\t\tobb.set( box.min, box.max, boxToMesh );\n\t\tobb.needsUpdate = true;\n\n\t\treturn this.shapecast(\n\t\t\t{\n\t\t\t\tintersectsBounds: box => obb.intersectsBox( box ),\n\t\t\t\tintersectsTriangle: tri => obb.intersectsTriangle( tri )\n\t\t\t}\n\t\t);\n\n\t}\n\n\tintersectsSphere( sphere ) {\n\n\t\treturn this.shapecast(\n\t\t\t{\n\t\t\t\tintersectsBounds: box => sphere.intersectsBox( box ),\n\t\t\t\tintersectsTriangle: tri => tri.intersectsSphere( sphere )\n\t\t\t}\n\t\t);\n\n\t}\n\n\tclosestPointToGeometry( otherGeometry, geometryToBvh, target1 = { }, target2 = { }, minThreshold = 0, maxThreshold = Infinity ) {\n\n\t\tif ( ! otherGeometry.boundingBox ) {\n\n\t\t\totherGeometry.computeBoundingBox();\n\n\t\t}\n\n\t\tobb.set( otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh );\n\t\tobb.needsUpdate = true;\n\n\t\tconst geometry = this.geometry;\n\t\tconst pos = geometry.attributes.position;\n\t\tconst index = geometry.index;\n\t\tconst otherPos = otherGeometry.attributes.position;\n\t\tconst otherIndex = otherGeometry.index;\n\t\tconst triangle = trianglePool.getPrimitive();\n\t\tconst triangle2 = trianglePool.getPrimitive();\n\n\t\tlet tempTarget1 = temp1;\n\t\tlet tempTargetDest1 = temp2;\n\t\tlet tempTarget2 = null;\n\t\tlet tempTargetDest2 = null;\n\n\t\tif ( target2 ) {\n\n\t\t\ttempTarget2 = temp3;\n\t\t\ttempTargetDest2 = temp4;\n\n\t\t}\n\n\t\tlet closestDistance = Infinity;\n\t\tlet closestDistanceTriIndex = null;\n\t\tlet closestDistanceOtherTriIndex = null;\n\t\ttempMatrix.copy( geometryToBvh ).invert();\n\t\tobb2.matrix.copy( tempMatrix );\n\t\tthis.shapecast(\n\t\t\t{\n\n\t\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\t\treturn obb.distanceToBox( box );\n\n\t\t\t\t},\n\n\t\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\t\tif ( score < closestDistance && score < maxThreshold ) {\n\n\t\t\t\t\t\t// if we know the triangles of this bounds will be intersected next then\n\t\t\t\t\t\t// save the bounds to use during triangle checks.\n\t\t\t\t\t\tif ( isLeaf ) {\n\n\t\t\t\t\t\t\tobb2.min.copy( box.min );\n\t\t\t\t\t\t\tobb2.max.copy( box.max );\n\t\t\t\t\t\t\tobb2.needsUpdate = true;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn false;\n\n\t\t\t\t},\n\n\t\t\t\tintersectsRange: ( offset, count ) => {\n\n\t\t\t\t\tif ( otherGeometry.boundsTree ) {\n\n\t\t\t\t\t\t// if the other geometry has a bvh then use the accelerated path where we use shapecast to find\n\t\t\t\t\t\t// the closest bounds in the other geometry to check.\n\t\t\t\t\t\treturn otherGeometry.boundsTree.shapecast( {\n\t\t\t\t\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\t\t\t\t\treturn obb2.distanceToBox( box );\n\n\t\t\t\t\t\t\t},\n\n\t\t\t\t\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\t\t\t\t\treturn score < closestDistance && score < maxThreshold;\n\n\t\t\t\t\t\t\t},\n\n\t\t\t\t\t\t\tintersectsRange: ( otherOffset, otherCount ) => {\n\n\t\t\t\t\t\t\t\tfor ( let i2 = otherOffset * 3, l2 = ( otherOffset + otherCount ) * 3; i2 < l2; i2 += 3 ) {\n\n\t\t\t\t\t\t\t\t\tsetTriangle( triangle2, i2, otherIndex, otherPos );\n\t\t\t\t\t\t\t\t\ttriangle2.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\t\ttriangle2.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\t\ttriangle2.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\t\t\t\tfor ( let i = offset * 3, l = ( offset + count ) * 3; i < l; i += 3 ) {\n\n\t\t\t\t\t\t\t\t\t\tsetTriangle( triangle, i, index, pos );\n\t\t\t\t\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\t\t\t\t\tconst dist = triangle.distanceToTriangle( triangle2, tempTarget1, tempTarget2 );\n\t\t\t\t\t\t\t\t\t\tif ( dist < closestDistance ) {\n\n\t\t\t\t\t\t\t\t\t\t\ttempTargetDest1.copy( tempTarget1 );\n\n\t\t\t\t\t\t\t\t\t\t\tif ( tempTargetDest2 ) {\n\n\t\t\t\t\t\t\t\t\t\t\t\ttempTargetDest2.copy( tempTarget2 );\n\n\t\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t\tclosestDistance = dist;\n\t\t\t\t\t\t\t\t\t\t\tclosestDistanceTriIndex = i / 3;\n\t\t\t\t\t\t\t\t\t\t\tclosestDistanceOtherTriIndex = i2 / 3;\n\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t// stop traversal if we find a point that's under the given threshold\n\t\t\t\t\t\t\t\t\t\tif ( dist < minThreshold ) {\n\n\t\t\t\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t} );\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\t// If no bounds tree then we'll just check every triangle.\n\t\t\t\t\t\tconst triCount = otherIndex ? otherIndex.count : otherPos.count;\n\t\t\t\t\t\tfor ( let i2 = 0, l2 = triCount; i2 < l2; i2 += 3 ) {\n\n\t\t\t\t\t\t\tsetTriangle( triangle2, i2, otherIndex, otherPos );\n\t\t\t\t\t\t\ttriangle2.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\ttriangle2.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\ttriangle2.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\t\tfor ( let i = offset * 3, l = ( offset + count ) * 3; i < l; i += 3 ) {\n\n\t\t\t\t\t\t\t\tsetTriangle( triangle, i, index, pos );\n\t\t\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\t\t\tconst dist = triangle.distanceToTriangle( triangle2, tempTarget1, tempTarget2 );\n\t\t\t\t\t\t\t\tif ( dist < closestDistance ) {\n\n\t\t\t\t\t\t\t\t\ttempTargetDest1.copy( tempTarget1 );\n\n\t\t\t\t\t\t\t\t\tif ( tempTargetDest2 ) {\n\n\t\t\t\t\t\t\t\t\t\ttempTargetDest2.copy( tempTarget2 );\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\tclosestDistance = dist;\n\t\t\t\t\t\t\t\t\tclosestDistanceTriIndex = i / 3;\n\t\t\t\t\t\t\t\t\tclosestDistanceOtherTriIndex = i2 / 3;\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t// stop traversal if we find a point that's under the given threshold\n\t\t\t\t\t\t\t\tif ( dist < minThreshold ) {\n\n\t\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t},\n\n\t\t\t}\n\n\t\t);\n\n\t\ttrianglePool.releasePrimitive( triangle );\n\t\ttrianglePool.releasePrimitive( triangle2 );\n\n\t\tif ( closestDistance === Infinity ) return null;\n\n\t\tif ( ! target1.point ) target1.point = tempTargetDest1.clone();\n\t\telse target1.point.copy( tempTargetDest1 );\n\t\ttarget1.distance = closestDistance,\n\t\ttarget1.faceIndex = closestDistanceTriIndex;\n\n\t\tif ( target2 ) {\n\n\t\t\tif ( ! target2.point ) target2.point = tempTargetDest2.clone();\n\t\t\telse target2.point.copy( tempTargetDest2 );\n\t\t\ttarget2.point.applyMatrix4( tempMatrix );\n\t\t\ttempTargetDest1.applyMatrix4( tempMatrix );\n\t\t\ttarget2.distance = tempTargetDest1.sub( target2.point ).length();\n\t\t\ttarget2.faceIndex = closestDistanceOtherTriIndex;\n\n\t\t}\n\n\t\treturn target1;\n\n\t}\n\n\tclosestPointToPoint( point, target = { }, minThreshold = 0, maxThreshold = Infinity ) {\n\n\t\t// early out if under minThreshold\n\t\t// skip checking if over maxThreshold\n\t\t// set minThreshold = maxThreshold to quickly check if a point is within a threshold\n\t\t// returns Infinity if no value found\n\t\tconst minThresholdSq = minThreshold * minThreshold;\n\t\tconst maxThresholdSq = maxThreshold * maxThreshold;\n\t\tlet closestDistanceSq = Infinity;\n\t\tlet closestDistanceTriIndex = null;\n\t\tthis.shapecast(\n\n\t\t\t{\n\n\t\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\t\ttemp.copy( point ).clamp( box.min, box.max );\n\t\t\t\t\treturn temp.distanceToSquared( point );\n\n\t\t\t\t},\n\n\t\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\t\treturn score < closestDistanceSq && score < maxThresholdSq;\n\n\t\t\t\t},\n\n\t\t\t\tintersectsTriangle: ( tri, triIndex ) => {\n\n\t\t\t\t\ttri.closestPointToPoint( point, temp );\n\t\t\t\t\tconst distSq = point.distanceToSquared( temp );\n\t\t\t\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\t\t\t\ttemp1.copy( temp );\n\t\t\t\t\t\tclosestDistanceSq = distSq;\n\t\t\t\t\t\tclosestDistanceTriIndex = triIndex;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( distSq < minThresholdSq ) {\n\n\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\treturn false;\n\n\t\t\t\t\t}\n\n\t\t\t\t},\n\n\t\t\t}\n\n\t\t);\n\n\t\tif ( closestDistanceSq === Infinity ) return null;\n\n\t\tconst closestDistance = Math.sqrt( closestDistanceSq );\n\n\t\tif ( ! target.point ) target.point = temp1.clone();\n\t\telse target.point.copy( temp1 );\n\t\ttarget.distance = closestDistance,\n\t\ttarget.faceIndex = closestDistanceTriIndex;\n\n\t\treturn target;\n\n\t}\n\n\tgetBoundingBox( target ) {\n\n\t\ttarget.makeEmpty();\n\n\t\tconst roots = this._roots;\n\t\troots.forEach( buffer => {\n\n\t\t\tarrayToBox( 0, new Float32Array( buffer ), tempBox );\n\t\t\ttarget.union( tempBox );\n\n\t\t} );\n\n\t\treturn target;\n\n\t}\n\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,eAAe,EAAEC,IAAI,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AAC1E,SAASC,MAAM,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,gBAAgB;AACzE,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SACCC,OAAO,EACPC,YAAY,EACZC,SAAS,EACTC,kBAAkB,EAClBC,SAAS,EACTC,WAAW,QACL,oBAAoB;AAC3B,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,oBAAoB,EAAEC,WAAW,QAAQ,+BAA+B;AAEjF,MAAMC,eAAe,GAAGC,MAAM,CAAE,sBAAuB,CAAC;AAExD,MAAMC,IAAI,GAAG,eAAgB,IAAIrB,IAAI,CAAC,CAAC;AACvC,MAAMsB,KAAK,GAAG,eAAgB,IAAItB,IAAI,CAAC,CAAC;AACxC,MAAMuB,UAAU,GAAG,eAAgB,IAAIrB,OAAO,CAAC,CAAC;AAChD,MAAMsB,GAAG,GAAG,eAAgB,IAAIX,WAAW,CAAC,CAAC;AAC7C,MAAMY,IAAI,GAAG,eAAgB,IAAIZ,WAAW,CAAC,CAAC;AAC9C,MAAMa,IAAI,GAAG,eAAgB,IAAI5B,OAAO,CAAC,CAAC;AAC1C,MAAM6B,KAAK,GAAG,eAAgB,IAAI7B,OAAO,CAAC,CAAC;AAC3C,MAAM8B,KAAK,GAAG,eAAgB,IAAI9B,OAAO,CAAC,CAAC;AAC3C,MAAM+B,KAAK,GAAG,eAAgB,IAAI/B,OAAO,CAAC,CAAC;AAC3C,MAAMgC,KAAK,GAAG,eAAgB,IAAIhC,OAAO,CAAC,CAAC;AAC3C,MAAMiC,OAAO,GAAG,eAAgB,IAAI/B,IAAI,CAAC,CAAC;AAC1C,MAAMgC,YAAY,GAAG,eAAgB,IAAIjB,aAAa,CAAE,MAAM,IAAID,gBAAgB,CAAC,CAAE,CAAC;AAEtF,OAAO,MAAMmB,OAAO,CAAC;EAEpB,OAAOC,SAASA,CAAEC,GAAG,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAG;IAErC,IAAKA,OAAO,CAACC,gBAAgB,EAAG;MAE/BC,OAAO,CAACC,IAAI,CAAE,sGAAuG,CAAC;MAEtH,OAAON,OAAO,CAACC,SAAS,CACvBM,SAAS,CAAE,CAAC,CAAE,EACd;QACCC,YAAY,EAAED,SAAS,CAAE,CAAC,CAAE,KAAKE,SAAS,GAAG,IAAI,GAAGF,SAAS,CAAE,CAAC;MACjE,CACD,CAAC;IAEF;IAEAJ,OAAO,GAAG;MACTK,YAAY,EAAE,IAAI;MAClB,GAAGL;IACJ,CAAC;IAED,MAAMO,QAAQ,GAAGR,GAAG,CAACQ,QAAQ;IAC7B,MAAMC,QAAQ,GAAGT,GAAG,CAACU,MAAM;IAC3B,MAAMC,cAAc,GAAGH,QAAQ,CAACI,QAAQ,CAAC,CAAC;IAC1C,IAAIC,MAAM;IACV,IAAKZ,OAAO,CAACK,YAAY,EAAG;MAE3BO,MAAM,GAAG;QACRC,KAAK,EAAEL,QAAQ,CAACM,GAAG,CAAEC,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC,CAAE,CAAC;QAC3CC,KAAK,EAAEP,cAAc,CAACQ,KAAK,CAACF,KAAK,CAAC;MACnC,CAAC;IAEF,CAAC,MAAM;MAENJ,MAAM,GAAG;QACRC,KAAK,EAAEL,QAAQ;QACfS,KAAK,EAAEP,cAAc,CAACQ;MACvB,CAAC;IAEF;IAEA,OAAON,MAAM;EAEd;EAEA,OAAOO,WAAWA,CAAEC,IAAI,EAAEb,QAAQ,EAAEP,OAAO,GAAG,CAAC,CAAC,EAAG;IAElD,IAAK,OAAOA,OAAO,KAAK,SAAS,EAAG;MAEnCE,OAAO,CAACC,IAAI,CAAE,wGAAyG,CAAC;MAExH,OAAON,OAAO,CAACsB,WAAW,CACzBf,SAAS,CAAE,CAAC,CAAE,EACdA,SAAS,CAAE,CAAC,CAAE,EACd;QACCiB,QAAQ,EAAEjB,SAAS,CAAE,CAAC,CAAE,KAAKE,SAAS,GAAG,IAAI,GAAGF,SAAS,CAAE,CAAC;MAC7D,CACD,CAAC;IAEF;IAEAJ,OAAO,GAAG;MACTqB,QAAQ,EAAE,IAAI;MACd,GAAGrB;IACJ,CAAC;IAED,MAAM;MAAEiB,KAAK;MAAEJ;IAAM,CAAC,GAAGO,IAAI;IAC7B,MAAMrB,GAAG,GAAG,IAAIF,OAAO,CAAEU,QAAQ,EAAE;MAAE,GAAGP,OAAO;MAAE,CAAEjB,eAAe,GAAI;IAAK,CAAE,CAAC;IAC9EgB,GAAG,CAACU,MAAM,GAAGI,KAAK;IAElB,IAAKb,OAAO,CAACqB,QAAQ,EAAG;MAEvB,MAAMX,cAAc,GAAGH,QAAQ,CAACI,QAAQ,CAAC,CAAC;MAC1C,IAAKD,cAAc,KAAK,IAAI,EAAG;QAE9B,MAAMY,QAAQ,GAAG,IAAI3D,eAAe,CAAEyD,IAAI,CAACH,KAAK,EAAE,CAAC,EAAE,KAAM,CAAC;QAC5DV,QAAQ,CAACc,QAAQ,CAAEC,QAAS,CAAC;MAE9B,CAAC,MAAM,IAAKZ,cAAc,CAACQ,KAAK,KAAKD,KAAK,EAAG;QAE5CP,cAAc,CAACQ,KAAK,CAACK,GAAG,CAAEN,KAAM,CAAC;QACjCP,cAAc,CAACc,WAAW,GAAG,IAAI;MAElC;IAED;IAEA,OAAOzB,GAAG;EAEX;EAEA0B,WAAWA,CAAElB,QAAQ,EAAEP,OAAO,GAAG,CAAC,CAAC,EAAG;IAErC,IAAK,CAAEO,QAAQ,CAACN,gBAAgB,EAAG;MAElC,MAAM,IAAIyB,KAAK,CAAE,+CAAgD,CAAC;IAEnE,CAAC,MAAM,IAAKnB,QAAQ,CAACU,KAAK,IAAIV,QAAQ,CAACU,KAAK,CAACU,4BAA4B,EAAG;MAE3E,MAAM,IAAID,KAAK,CAAE,+EAAgF,CAAC;IAEnG;;IAEA;IACA1B,OAAO,GAAG4B,MAAM,CAACC,MAAM,CAAE;MAExBC,QAAQ,EAAE/D,MAAM;MAChBgE,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,EAAE;MACfC,OAAO,EAAE,IAAI;MACbC,oBAAoB,EAAE,KAAK;MAC3BC,cAAc,EAAE,IAAI;MACpBC,UAAU,EAAE,IAAI;MAEhB;;MAEA;MACA,CAAErD,eAAe,GAAI;IAEtB,CAAC,EAAEiB,OAAQ,CAAC;IAEZ,IAAKA,OAAO,CAACkC,oBAAoB,IAAI,OAAOG,iBAAiB,KAAK,WAAW,EAAG;MAE/E,MAAM,IAAIX,KAAK,CAAE,8CAA+C,CAAC;IAElE;IAEA,IAAI,CAACjB,MAAM,GAAG,IAAI;IAClB,IAAK,CAAET,OAAO,CAAEjB,eAAe,CAAE,EAAG;MAEnC,IAAI,CAAC0B,MAAM,GAAGvC,eAAe,CAAEqC,QAAQ,EAAEP,OAAQ,CAAC;MAElD,IAAK,CAAEO,QAAQ,CAAC+B,WAAW,IAAItC,OAAO,CAACmC,cAAc,EAAG;QAEvD5B,QAAQ,CAAC+B,WAAW,GAAG,IAAI,CAACC,cAAc,CAAE,IAAI3E,IAAI,CAAC,CAAE,CAAC;MAEzD;IAED;;IAEA;IACA;IACA,IAAI,CAAC2C,QAAQ,GAAGA,QAAQ;EAEzB;EAEAiC,KAAKA,CAAEC,WAAW,GAAG,IAAI,EAAG;IAE3B,IAAKA,WAAW,IAAIC,KAAK,CAACC,OAAO,CAAEF,WAAY,CAAC,EAAG;MAElDA,WAAW,GAAG,IAAIG,GAAG,CAAEH,WAAY,CAAC;IAErC;IAEA,MAAMlC,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMsC,QAAQ,GAAGtC,QAAQ,CAACU,KAAK,CAACC,KAAK;IACrC,MAAM4B,OAAO,GAAGvC,QAAQ,CAACwC,UAAU,CAACC,QAAQ;IAE5C,IAAIC,MAAM,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY;IAClD,IAAIC,UAAU,GAAG,CAAC;IAClB,MAAMxC,KAAK,GAAG,IAAI,CAACJ,MAAM;IACzB,KAAM,IAAI6C,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG1C,KAAK,CAAC2C,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;MAEhDL,MAAM,GAAGpC,KAAK,CAAEyC,CAAC,CAAE;MACnBJ,WAAW,GAAG,IAAIO,WAAW,CAAER,MAAO,CAAC;MACvCE,WAAW,GAAG,IAAIO,WAAW,CAAET,MAAO,CAAC;MACvCG,YAAY,GAAG,IAAIO,YAAY,CAAEV,MAAO,CAAC;MAEzCW,SAAS,CAAE,CAAC,EAAEP,UAAW,CAAC;MAC1BA,UAAU,IAAIJ,MAAM,CAACY,UAAU;IAEhC;IAEA,SAASD,SAASA,CAAEE,WAAW,EAAET,UAAU,EAAEU,KAAK,GAAG,KAAK,EAAG;MAE5D,MAAMC,WAAW,GAAGF,WAAW,GAAG,CAAC;MACnC,MAAMG,MAAM,GAAGd,WAAW,CAAEa,WAAW,GAAG,EAAE,CAAE,KAAK/F,gBAAgB;MACnE,IAAKgG,MAAM,EAAG;QAEb,MAAMC,MAAM,GAAGhB,WAAW,CAAEY,WAAW,GAAG,CAAC,CAAE;QAC7C,MAAMK,KAAK,GAAGhB,WAAW,CAAEa,WAAW,GAAG,EAAE,CAAE;QAE7C,IAAII,IAAI,GAAGC,QAAQ;QACnB,IAAIC,IAAI,GAAGD,QAAQ;QACnB,IAAIE,IAAI,GAAGF,QAAQ;QACnB,IAAIG,IAAI,GAAG,CAAEH,QAAQ;QACrB,IAAII,IAAI,GAAG,CAAEJ,QAAQ;QACrB,IAAIK,IAAI,GAAG,CAAEL,QAAQ;QAErB,KAAM,IAAIf,CAAC,GAAG,CAAC,GAAGY,MAAM,EAAEX,CAAC,GAAG,CAAC,IAAKW,MAAM,GAAGC,KAAK,CAAE,EAAEb,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;UAEnE,MAAMrC,KAAK,GAAG4B,QAAQ,CAAES,CAAC,CAAE;UAC3B,MAAMqB,CAAC,GAAG7B,OAAO,CAAC8B,IAAI,CAAE3D,KAAM,CAAC;UAC/B,MAAM4D,CAAC,GAAG/B,OAAO,CAACgC,IAAI,CAAE7D,KAAM,CAAC;UAC/B,MAAM8D,CAAC,GAAGjC,OAAO,CAACkC,IAAI,CAAE/D,KAAM,CAAC;UAE/B,IAAK0D,CAAC,GAAGP,IAAI,EAAGA,IAAI,GAAGO,CAAC;UACxB,IAAKA,CAAC,GAAGH,IAAI,EAAGA,IAAI,GAAGG,CAAC;UAExB,IAAKE,CAAC,GAAGP,IAAI,EAAGA,IAAI,GAAGO,CAAC;UACxB,IAAKA,CAAC,GAAGJ,IAAI,EAAGA,IAAI,GAAGI,CAAC;UAExB,IAAKE,CAAC,GAAGR,IAAI,EAAGA,IAAI,GAAGQ,CAAC;UACxB,IAAKA,CAAC,GAAGL,IAAI,EAAGA,IAAI,GAAGK,CAAC;QAEzB;QAEA,IACC3B,YAAY,CAAEU,WAAW,GAAG,CAAC,CAAE,KAAKM,IAAI,IACxChB,YAAY,CAAEU,WAAW,GAAG,CAAC,CAAE,KAAKQ,IAAI,IACxClB,YAAY,CAAEU,WAAW,GAAG,CAAC,CAAE,KAAKS,IAAI,IAExCnB,YAAY,CAAEU,WAAW,GAAG,CAAC,CAAE,KAAKU,IAAI,IACxCpB,YAAY,CAAEU,WAAW,GAAG,CAAC,CAAE,KAAKW,IAAI,IACxCrB,YAAY,CAAEU,WAAW,GAAG,CAAC,CAAE,KAAKY,IAAI,EACvC;UAEDtB,YAAY,CAAEU,WAAW,GAAG,CAAC,CAAE,GAAGM,IAAI;UACtChB,YAAY,CAAEU,WAAW,GAAG,CAAC,CAAE,GAAGQ,IAAI;UACtClB,YAAY,CAAEU,WAAW,GAAG,CAAC,CAAE,GAAGS,IAAI;UAEtCnB,YAAY,CAAEU,WAAW,GAAG,CAAC,CAAE,GAAGU,IAAI;UACtCpB,YAAY,CAAEU,WAAW,GAAG,CAAC,CAAE,GAAGW,IAAI;UACtCrB,YAAY,CAAEU,WAAW,GAAG,CAAC,CAAE,GAAGY,IAAI;UAEtC,OAAO,IAAI;QAEZ,CAAC,MAAM;UAEN,OAAO,KAAK;QAEb;MAED,CAAC,MAAM;QAEN,MAAMO,IAAI,GAAGnB,WAAW,GAAG,CAAC;QAC5B,MAAMoB,KAAK,GAAGhC,WAAW,CAAEY,WAAW,GAAG,CAAC,CAAE;;QAE5C;QACA;QACA,MAAMqB,UAAU,GAAGF,IAAI,GAAG5B,UAAU;QACpC,MAAM+B,WAAW,GAAGF,KAAK,GAAG7B,UAAU;QACtC,IAAIgC,aAAa,GAAGtB,KAAK;QACzB,IAAIuB,YAAY,GAAG,KAAK;QACxB,IAAIC,aAAa,GAAG,KAAK;QAEzB,IAAK9C,WAAW,EAAG;UAElB;UACA;UACA,IAAK,CAAE4C,aAAa,EAAG;YAEtBC,YAAY,GAAG7C,WAAW,CAAC+C,GAAG,CAAEL,UAAW,CAAC;YAC5CI,aAAa,GAAG9C,WAAW,CAAC+C,GAAG,CAAEJ,WAAY,CAAC;YAC9CC,aAAa,GAAG,CAAEC,YAAY,IAAI,CAAEC,aAAa;UAElD;QAED,CAAC,MAAM;UAEND,YAAY,GAAG,IAAI;UACnBC,aAAa,GAAG,IAAI;QAErB;QAEA,MAAME,YAAY,GAAGJ,aAAa,IAAIC,YAAY;QAClD,MAAMI,aAAa,GAAGL,aAAa,IAAIE,aAAa;QAEpD,IAAII,UAAU,GAAG,KAAK;QACtB,IAAKF,YAAY,EAAG;UAEnBE,UAAU,GAAG/B,SAAS,CAAEqB,IAAI,EAAE5B,UAAU,EAAEgC,aAAc,CAAC;QAE1D;QAEA,IAAIO,WAAW,GAAG,KAAK;QACvB,IAAKF,aAAa,EAAG;UAEpBE,WAAW,GAAGhC,SAAS,CAAEsB,KAAK,EAAE7B,UAAU,EAAEgC,aAAc,CAAC;QAE5D;QAEA,MAAMQ,SAAS,GAAGF,UAAU,IAAIC,WAAW;QAC3C,IAAKC,SAAS,EAAG;UAEhB,KAAM,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;YAE9B,MAAMwC,KAAK,GAAGb,IAAI,GAAG3B,CAAC;YACtB,MAAMyC,MAAM,GAAGb,KAAK,GAAG5B,CAAC;YACxB,MAAM0C,YAAY,GAAG5C,YAAY,CAAE0C,KAAK,CAAE;YAC1C,MAAMG,YAAY,GAAG7C,YAAY,CAAE0C,KAAK,GAAG,CAAC,CAAE;YAC9C,MAAMI,aAAa,GAAG9C,YAAY,CAAE2C,MAAM,CAAE;YAC5C,MAAMI,aAAa,GAAG/C,YAAY,CAAE2C,MAAM,GAAG,CAAC,CAAE;YAEhD3C,YAAY,CAAEU,WAAW,GAAGR,CAAC,CAAE,GAAG0C,YAAY,GAAGE,aAAa,GAAGF,YAAY,GAAGE,aAAa;YAC7F9C,YAAY,CAAEU,WAAW,GAAGR,CAAC,GAAG,CAAC,CAAE,GAAG2C,YAAY,GAAGE,aAAa,GAAGF,YAAY,GAAGE,aAAa;UAElG;QAED;QAEA,OAAON,SAAS;MAEjB;IAED;EAED;EAEAO,QAAQA,CAAEC,QAAQ,EAAEC,SAAS,GAAG,CAAC,EAAG;IAEnC,MAAMrD,MAAM,GAAG,IAAI,CAACxC,MAAM,CAAE6F,SAAS,CAAE;IACvC,MAAMpD,WAAW,GAAG,IAAIO,WAAW,CAAER,MAAO,CAAC;IAC7C,MAAME,WAAW,GAAG,IAAIO,WAAW,CAAET,MAAO,CAAC;IAC7CW,SAAS,CAAE,CAAE,CAAC;IAEd,SAASA,SAASA,CAAEE,WAAW,EAAEyC,KAAK,GAAG,CAAC,EAAG;MAE5C,MAAMvC,WAAW,GAAGF,WAAW,GAAG,CAAC;MACnC,MAAMG,MAAM,GAAGd,WAAW,CAAEa,WAAW,GAAG,EAAE,CAAE,KAAK/F,gBAAgB;MACnE,IAAKgG,MAAM,EAAG;QAEb,MAAMC,MAAM,GAAGhB,WAAW,CAAEY,WAAW,GAAG,CAAC,CAAE;QAC7C,MAAMK,KAAK,GAAGhB,WAAW,CAAEa,WAAW,GAAG,EAAE,CAAE;QAC7CqC,QAAQ,CAAEE,KAAK,EAAEtC,MAAM,EAAE,IAAIN,YAAY,CAAEV,MAAM,EAAEa,WAAW,GAAG,CAAC,EAAE,CAAE,CAAC,EAAEI,MAAM,EAAEC,KAAM,CAAC;MAEzF,CAAC,MAAM;QAEN;QACA,MAAMc,IAAI,GAAGnB,WAAW,GAAG9F,cAAc,GAAG,CAAC;QAC7C,MAAMkH,KAAK,GAAGhC,WAAW,CAAEY,WAAW,GAAG,CAAC,CAAE;QAC5C,MAAM0C,SAAS,GAAGtD,WAAW,CAAEY,WAAW,GAAG,CAAC,CAAE;QAChD,MAAM2C,aAAa,GAAGJ,QAAQ,CAAEE,KAAK,EAAEtC,MAAM,EAAE,IAAIN,YAAY,CAAEV,MAAM,EAAEa,WAAW,GAAG,CAAC,EAAE,CAAE,CAAC,EAAE0C,SAAU,CAAC;QAE1G,IAAK,CAAEC,aAAa,EAAG;UAEtB7C,SAAS,CAAEqB,IAAI,EAAEsB,KAAK,GAAG,CAAE,CAAC;UAC5B3C,SAAS,CAAEsB,KAAK,EAAEqB,KAAK,GAAG,CAAE,CAAC;QAE9B;MAED;IAED;EAED;;EAEA;EACApI,OAAOA,CAAEuI,GAAG,EAAEC,cAAc,GAAG9I,SAAS,EAAG;IAE1C,MAAMgD,KAAK,GAAG,IAAI,CAACJ,MAAM;IACzB,MAAMF,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMqG,UAAU,GAAG,EAAE;IACrB,MAAMC,UAAU,GAAGF,cAAc,CAACE,UAAU;IAC5C,MAAMC,eAAe,GAAGpE,KAAK,CAACC,OAAO,CAAEgE,cAAe,CAAC;IAEvD,MAAMI,MAAM,GAAGxG,QAAQ,CAACwG,MAAM;IAC9B,MAAMC,IAAI,GAAGH,UAAU,GAAGF,cAAc,CAACK,IAAI,GAAGL,cAAc;IAC9D,KAAM,IAAIrD,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG1C,KAAK,CAAC2C,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;MAEhD,MAAM2D,YAAY,GAAGH,eAAe,GAAGH,cAAc,CAAEI,MAAM,CAAEzD,CAAC,CAAE,CAAC4D,aAAa,CAAE,CAACF,IAAI,GAAGA,IAAI;MAC9F,MAAMG,UAAU,GAAGP,UAAU,CAACpD,MAAM;MAEpCjF,SAAS,CAAEsC,KAAK,CAAEyC,CAAC,CAAG,CAAC;MACvBnF,OAAO,CAAE,CAAC,EAAEoC,QAAQ,EAAE0G,YAAY,EAAEP,GAAG,EAAEE,UAAW,CAAC;MACrDpI,WAAW,CAAC,CAAC;MAEb,IAAKsI,eAAe,EAAG;QAEtB,MAAMI,aAAa,GAAGH,MAAM,CAAEzD,CAAC,CAAE,CAAC4D,aAAa;QAC/C,KAAM,IAAIE,CAAC,GAAGD,UAAU,EAAEE,EAAE,GAAGT,UAAU,CAACpD,MAAM,EAAE4D,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAG,EAAG;UAEhER,UAAU,CAAEQ,CAAC,CAAE,CAACE,IAAI,CAACJ,aAAa,GAAGA,aAAa;QAEnD;MAED;IAED;IAEA,OAAON,UAAU;EAElB;EAEAxI,YAAYA,CAAEsI,GAAG,EAAEC,cAAc,GAAG9I,SAAS,EAAG;IAE/C,MAAMgD,KAAK,GAAG,IAAI,CAACJ,MAAM;IACzB,MAAMF,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMsG,UAAU,GAAGF,cAAc,CAACE,UAAU;IAC5C,MAAMC,eAAe,GAAGpE,KAAK,CAACC,OAAO,CAAEgE,cAAe,CAAC;IAEvD,IAAIY,aAAa,GAAG,IAAI;IAExB,MAAMR,MAAM,GAAGxG,QAAQ,CAACwG,MAAM;IAC9B,MAAMC,IAAI,GAAGH,UAAU,GAAGF,cAAc,CAACK,IAAI,GAAGL,cAAc;IAC9D,KAAM,IAAIrD,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG1C,KAAK,CAAC2C,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAG,EAAG;MAEhD,MAAM2D,YAAY,GAAGH,eAAe,GAAGH,cAAc,CAAEI,MAAM,CAAEzD,CAAC,CAAE,CAAC4D,aAAa,CAAE,CAACF,IAAI,GAAGA,IAAI;MAE9FzI,SAAS,CAAEsC,KAAK,CAAEyC,CAAC,CAAG,CAAC;MACvB,MAAM1C,MAAM,GAAGxC,YAAY,CAAE,CAAC,EAAEmC,QAAQ,EAAE0G,YAAY,EAAEP,GAAI,CAAC;MAC7DlI,WAAW,CAAC,CAAC;MAEb,IAAKoC,MAAM,IAAI,IAAI,KAAM2G,aAAa,IAAI,IAAI,IAAI3G,MAAM,CAAC4G,QAAQ,GAAGD,aAAa,CAACC,QAAQ,CAAE,EAAG;QAE9FD,aAAa,GAAG3G,MAAM;QACtB,IAAKkG,eAAe,EAAG;UAEtBlG,MAAM,CAAC0G,IAAI,CAACJ,aAAa,GAAGH,MAAM,CAAEzD,CAAC,CAAE,CAAC4D,aAAa;QAEtD;MAED;IAED;IAEA,OAAOK,aAAa;EAErB;EAEAjJ,kBAAkBA,CAAEmJ,aAAa,EAAEC,UAAU,EAAG;IAE/C,MAAMnH,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAIK,MAAM,GAAG,KAAK;IAClB,KAAM,MAAMG,IAAI,IAAI,IAAI,CAACN,MAAM,EAAG;MAEjClC,SAAS,CAAEwC,IAAK,CAAC;MACjBH,MAAM,GAAGtC,kBAAkB,CAAE,CAAC,EAAEiC,QAAQ,EAAEkH,aAAa,EAAEC,UAAW,CAAC;MACrElJ,WAAW,CAAC,CAAC;MAEb,IAAKoC,MAAM,EAAG;QAEb;MAED;IAED;IAEA,OAAOA,MAAM;EAEd;EAEAvC,SAASA,CAAEsJ,SAAS,EAAEC,uBAAuB,EAAEC,eAAe,EAAG;IAEhE,MAAMtH,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAKoH,SAAS,YAAYG,QAAQ,EAAG;MAEpC,IAAKF,uBAAuB,EAAG;QAE9B;QACA;QACA,MAAMG,oBAAoB,GAAGH,uBAAuB;QACpDA,uBAAuB,GAAGA,CAAEI,GAAG,EAAE/G,KAAK,EAAEgH,SAAS,EAAE1B,KAAK,KAAM;UAE7D,MAAM2B,EAAE,GAAGjH,KAAK,GAAG,CAAC;UACpB,OAAO8G,oBAAoB,CAAEC,GAAG,EAAEE,EAAE,EAAEA,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,EAAED,SAAS,EAAE1B,KAAM,CAAC;QAEzE,CAAC;MAGF;MAEAoB,SAAS,GAAG;QAEXQ,mBAAmB,EAAEN,eAAe;QACpCO,gBAAgB,EAAET,SAAS;QAC3BU,kBAAkB,EAAET,uBAAuB;QAC3CU,eAAe,EAAE;MAElB,CAAC;MAEDpI,OAAO,CAACC,IAAI,CAAE,0IAA2I,CAAC;IAE3J;IAEA,MAAMoI,QAAQ,GAAG3I,YAAY,CAAC4I,YAAY,CAAC,CAAC;IAC5C,IAAI;MACHL,mBAAmB;MACnBC,gBAAgB;MAChBE,eAAe;MACfD;IACD,CAAC,GAAGV,SAAS;IAEb,IAAKW,eAAe,IAAID,kBAAkB,EAAG;MAE5C,MAAMI,uBAAuB,GAAGH,eAAe;MAC/CA,eAAe,GAAGA,CAAEpE,MAAM,EAAEC,KAAK,EAAE8D,SAAS,EAAE1B,KAAK,EAAEmC,SAAS,KAAM;QAEnE,IAAK,CAAED,uBAAuB,CAAEvE,MAAM,EAAEC,KAAK,EAAE8D,SAAS,EAAE1B,KAAK,EAAEmC,SAAU,CAAC,EAAG;UAE9E,OAAO7J,oBAAoB,CAAEqF,MAAM,EAAEC,KAAK,EAAE5D,QAAQ,EAAE8H,kBAAkB,EAAEJ,SAAS,EAAE1B,KAAK,EAAEgC,QAAS,CAAC;QAEvG;QAEA,OAAO,IAAI;MAEZ,CAAC;IAEF,CAAC,MAAM,IAAK,CAAED,eAAe,EAAG;MAE/B,IAAKD,kBAAkB,EAAG;QAEzBC,eAAe,GAAGA,CAAEpE,MAAM,EAAEC,KAAK,EAAE8D,SAAS,EAAE1B,KAAK,KAAM;UAExD,OAAO1H,oBAAoB,CAAEqF,MAAM,EAAEC,KAAK,EAAE5D,QAAQ,EAAE8H,kBAAkB,EAAEJ,SAAS,EAAE1B,KAAK,EAAEgC,QAAS,CAAC;QAEvG,CAAC;MAEF,CAAC,MAAM;QAEND,eAAe,GAAGA,CAAEpE,MAAM,EAAEC,KAAK,EAAE8D,SAAS,KAAM;UAEjD,OAAOA,SAAS;QAEjB,CAAC;MAEF;IAED;IAEA,IAAIrH,MAAM,GAAG,KAAK;IAClB,IAAIyC,UAAU,GAAG,CAAC;IAClB,KAAM,MAAMtC,IAAI,IAAI,IAAI,CAACN,MAAM,EAAG;MAEjClC,SAAS,CAAEwC,IAAK,CAAC;MACjBH,MAAM,GAAGvC,SAAS,CAAE,CAAC,EAAEkC,QAAQ,EAAE6H,gBAAgB,EAAEE,eAAe,EAAEH,mBAAmB,EAAE9E,UAAW,CAAC;MACrG7E,WAAW,CAAC,CAAC;MAEb,IAAKoC,MAAM,EAAG;QAEb;MAED;MAEAyC,UAAU,IAAItC,IAAI,CAAC8C,UAAU;IAE9B;IAEAjE,YAAY,CAAC+I,gBAAgB,CAAEJ,QAAS,CAAC;IAEzC,OAAO3H,MAAM;EAEd;EAEAgI,OAAOA,CAAEC,QAAQ,EAAEC,aAAa,EAAEnB,SAAS,EAAG;IAE7C;IACA;;IAEA,IAAI;MACHoB,gBAAgB;MAChBC;IACD,CAAC,GAAGrB,SAAS;IAEb,MAAMsB,SAAS,GAAG,IAAI,CAAC1I,QAAQ,CAACU,KAAK;IACrC,MAAMiI,YAAY,GAAG,IAAI,CAAC3I,QAAQ,CAACwC,UAAU,CAACC,QAAQ;IAEtD,MAAMmG,cAAc,GAAGN,QAAQ,CAACtI,QAAQ,CAACU,KAAK;IAC9C,MAAMmI,iBAAiB,GAAGP,QAAQ,CAACtI,QAAQ,CAACwC,UAAU,CAACC,QAAQ;IAE/D7D,UAAU,CAACkK,IAAI,CAAEP,aAAc,CAAC,CAACQ,MAAM,CAAC,CAAC;IAEzC,MAAMf,QAAQ,GAAG3I,YAAY,CAAC4I,YAAY,CAAC,CAAC;IAC5C,MAAMe,SAAS,GAAG3J,YAAY,CAAC4I,YAAY,CAAC,CAAC;IAE7C,IAAKQ,mBAAmB,EAAG;MAE1B,SAASQ,0BAA0BA,CAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAG;QAEvG,KAAM,IAAIC,EAAE,GAAGN,OAAO,EAAEO,EAAE,GAAGP,OAAO,GAAGC,MAAM,EAAEK,EAAE,GAAGC,EAAE,EAAED,EAAE,EAAG,EAAG;UAE/DnL,WAAW,CAAEyK,SAAS,EAAEU,EAAE,GAAG,CAAC,EAAEd,cAAc,EAAEC,iBAAkB,CAAC;UACnEG,SAAS,CAACY,CAAC,CAACC,YAAY,CAAEtB,aAAc,CAAC;UACzCS,SAAS,CAACc,CAAC,CAACD,YAAY,CAAEtB,aAAc,CAAC;UACzCS,SAAS,CAACe,CAAC,CAACF,YAAY,CAAEtB,aAAc,CAAC;UACzCS,SAAS,CAAC/H,WAAW,GAAG,IAAI;UAE5B,KAAM,IAAI+I,EAAE,GAAGd,OAAO,EAAEe,EAAE,GAAGf,OAAO,GAAGC,MAAM,EAAEa,EAAE,GAAGC,EAAE,EAAED,EAAE,EAAG,EAAG;YAE/DzL,WAAW,CAAEyJ,QAAQ,EAAEgC,EAAE,GAAG,CAAC,EAAEtB,SAAS,EAAEC,YAAa,CAAC;YACxDX,QAAQ,CAAC/G,WAAW,GAAG,IAAI;YAE3B,IAAKwH,mBAAmB,CAAET,QAAQ,EAAEgB,SAAS,EAAEgB,EAAE,EAAEN,EAAE,EAAEJ,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAO,CAAC,EAAG;cAEzF,OAAO,IAAI;YAEZ;UAED;QAED;QAEA,OAAO,KAAK;MAEb;MAEA,IAAKjB,gBAAgB,EAAG;QAEvB,MAAM0B,wBAAwB,GAAG1B,gBAAgB;QACjDA,gBAAgB,GAAG,SAAAA,CAAWU,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAG;UAEhG,IAAK,CAAES,wBAAwB,CAAEhB,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAO,CAAC,EAAG;YAErG,OAAOR,0BAA0B,CAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAO,CAAC;UAEtG;UAEA,OAAO,IAAI;QAEZ,CAAC;MAEF,CAAC,MAAM;QAENjB,gBAAgB,GAAGS,0BAA0B;MAE9C;IAED;IAEAX,QAAQ,CAACtG,cAAc,CAAErD,KAAM,CAAC;IAChCA,KAAK,CAACkL,YAAY,CAAEtB,aAAc,CAAC;IACnC,MAAMlI,MAAM,GAAG,IAAI,CAACvC,SAAS,CAAE;MAE9B+J,gBAAgB,EAAEsC,GAAG,IAAIxL,KAAK,CAACyL,aAAa,CAAED,GAAI,CAAC;MAEnDpC,eAAe,EAAEA,CAAEmB,OAAO,EAAEC,MAAM,EAAEzB,SAAS,EAAE4B,MAAM,EAAEe,UAAU,EAAEF,GAAG,KAAM;QAE3EzL,IAAI,CAACoK,IAAI,CAAEqB,GAAI,CAAC;QAChBzL,IAAI,CAACmL,YAAY,CAAEjL,UAAW,CAAC;QAC/B,OAAO0J,QAAQ,CAACxK,SAAS,CAAE;UAE1B+J,gBAAgB,EAAEsC,GAAG,IAAIzL,IAAI,CAAC0L,aAAa,CAAED,GAAI,CAAC;UAElDpC,eAAe,EAAEA,CAAEqB,OAAO,EAAEC,MAAM,EAAE3B,SAAS,EAAE8B,MAAM,EAAEc,UAAU,KAAM;YAEtE,OAAO9B,gBAAgB,CAAEU,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEe,UAAU,EAAEb,MAAM,EAAEc,UAAW,CAAC;UAEpG;QAED,CAAE,CAAC;MAEJ;IAED,CAAE,CAAC;IAEHjL,YAAY,CAAC+I,gBAAgB,CAAEJ,QAAS,CAAC;IACzC3I,YAAY,CAAC+I,gBAAgB,CAAEY,SAAU,CAAC;IAC1C,OAAO3I,MAAM;EAEd;;EAEA;EACA+J,aAAaA,CAAED,GAAG,EAAEI,SAAS,EAAG;IAE/B1L,GAAG,CAACmC,GAAG,CAAEmJ,GAAG,CAACK,GAAG,EAAEL,GAAG,CAACM,GAAG,EAAEF,SAAU,CAAC;IACtC1L,GAAG,CAACoC,WAAW,GAAG,IAAI;IAEtB,OAAO,IAAI,CAACnD,SAAS,CACpB;MACC+J,gBAAgB,EAAEsC,GAAG,IAAItL,GAAG,CAACuL,aAAa,CAAED,GAAI,CAAC;MACjDrC,kBAAkB,EAAEL,GAAG,IAAI5I,GAAG,CAACiJ,kBAAkB,CAAEL,GAAI;IACxD,CACD,CAAC;EAEF;EAEAiD,gBAAgBA,CAAEC,MAAM,EAAG;IAE1B,OAAO,IAAI,CAAC7M,SAAS,CACpB;MACC+J,gBAAgB,EAAEsC,GAAG,IAAIQ,MAAM,CAACP,aAAa,CAAED,GAAI,CAAC;MACpDrC,kBAAkB,EAAEL,GAAG,IAAIA,GAAG,CAACiD,gBAAgB,CAAEC,MAAO;IACzD,CACD,CAAC;EAEF;EAEAC,sBAAsBA,CAAE1D,aAAa,EAAE2D,aAAa,EAAEC,OAAO,GAAG,CAAE,CAAC,EAAEC,OAAO,GAAG,CAAE,CAAC,EAAEC,YAAY,GAAG,CAAC,EAAEC,YAAY,GAAGnH,QAAQ,EAAG;IAE/H,IAAK,CAAEoD,aAAa,CAACnF,WAAW,EAAG;MAElCmF,aAAa,CAACgE,kBAAkB,CAAC,CAAC;IAEnC;IAEArM,GAAG,CAACmC,GAAG,CAAEkG,aAAa,CAACnF,WAAW,CAACyI,GAAG,EAAEtD,aAAa,CAACnF,WAAW,CAAC0I,GAAG,EAAEI,aAAc,CAAC;IACtFhM,GAAG,CAACoC,WAAW,GAAG,IAAI;IAEtB,MAAMjB,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMmL,GAAG,GAAGnL,QAAQ,CAACwC,UAAU,CAACC,QAAQ;IACxC,MAAM/B,KAAK,GAAGV,QAAQ,CAACU,KAAK;IAC5B,MAAM0K,QAAQ,GAAGlE,aAAa,CAAC1E,UAAU,CAACC,QAAQ;IAClD,MAAM4I,UAAU,GAAGnE,aAAa,CAACxG,KAAK;IACtC,MAAMsH,QAAQ,GAAG3I,YAAY,CAAC4I,YAAY,CAAC,CAAC;IAC5C,MAAMe,SAAS,GAAG3J,YAAY,CAAC4I,YAAY,CAAC,CAAC;IAE7C,IAAIqD,WAAW,GAAGtM,KAAK;IACvB,IAAIuM,eAAe,GAAGtM,KAAK;IAC3B,IAAIuM,WAAW,GAAG,IAAI;IACtB,IAAIC,eAAe,GAAG,IAAI;IAE1B,IAAKV,OAAO,EAAG;MAEdS,WAAW,GAAGtM,KAAK;MACnBuM,eAAe,GAAGtM,KAAK;IAExB;IAEA,IAAIuM,eAAe,GAAG5H,QAAQ;IAC9B,IAAI6H,uBAAuB,GAAG,IAAI;IAClC,IAAIC,4BAA4B,GAAG,IAAI;IACvChN,UAAU,CAACkK,IAAI,CAAE+B,aAAc,CAAC,CAAC9B,MAAM,CAAC,CAAC;IACzCjK,IAAI,CAAC+M,MAAM,CAAC/C,IAAI,CAAElK,UAAW,CAAC;IAC9B,IAAI,CAACd,SAAS,CACb;MAEC8J,mBAAmB,EAAEuC,GAAG,IAAI;QAE3B,OAAOtL,GAAG,CAACiN,aAAa,CAAE3B,GAAI,CAAC;MAEhC,CAAC;MAEDtC,gBAAgB,EAAEA,CAAEsC,GAAG,EAAEzG,MAAM,EAAEqI,KAAK,KAAM;QAE3C,IAAKA,KAAK,GAAGL,eAAe,IAAIK,KAAK,GAAGd,YAAY,EAAG;UAEtD;UACA;UACA,IAAKvH,MAAM,EAAG;YAEb5E,IAAI,CAAC0L,GAAG,CAAC1B,IAAI,CAAEqB,GAAG,CAACK,GAAI,CAAC;YACxB1L,IAAI,CAAC2L,GAAG,CAAC3B,IAAI,CAAEqB,GAAG,CAACM,GAAI,CAAC;YACxB3L,IAAI,CAACmC,WAAW,GAAG,IAAI;UAExB;UAEA,OAAO,IAAI;QAEZ;QAEA,OAAO,KAAK;MAEb,CAAC;MAED8G,eAAe,EAAEA,CAAEpE,MAAM,EAAEC,KAAK,KAAM;QAErC,IAAKsD,aAAa,CAAC8E,UAAU,EAAG;UAE/B;UACA;UACA,OAAO9E,aAAa,CAAC8E,UAAU,CAAClO,SAAS,CAAE;YAC1C8J,mBAAmB,EAAEuC,GAAG,IAAI;cAE3B,OAAOrL,IAAI,CAACgN,aAAa,CAAE3B,GAAI,CAAC;YAEjC,CAAC;YAEDtC,gBAAgB,EAAEA,CAAEsC,GAAG,EAAEzG,MAAM,EAAEqI,KAAK,KAAM;cAE3C,OAAOA,KAAK,GAAGL,eAAe,IAAIK,KAAK,GAAGd,YAAY;YAEvD,CAAC;YAEDlD,eAAe,EAAEA,CAAEkE,WAAW,EAAEC,UAAU,KAAM;cAE/C,KAAM,IAAIxC,EAAE,GAAGuC,WAAW,GAAG,CAAC,EAAEtC,EAAE,GAAG,CAAEsC,WAAW,GAAGC,UAAU,IAAK,CAAC,EAAExC,EAAE,GAAGC,EAAE,EAAED,EAAE,IAAI,CAAC,EAAG;gBAEzFnL,WAAW,CAAEyK,SAAS,EAAEU,EAAE,EAAE2B,UAAU,EAAED,QAAS,CAAC;gBAClDpC,SAAS,CAACY,CAAC,CAACC,YAAY,CAAEgB,aAAc,CAAC;gBACzC7B,SAAS,CAACc,CAAC,CAACD,YAAY,CAAEgB,aAAc,CAAC;gBACzC7B,SAAS,CAACe,CAAC,CAACF,YAAY,CAAEgB,aAAc,CAAC;gBACzC7B,SAAS,CAAC/H,WAAW,GAAG,IAAI;gBAE5B,KAAM,IAAI8B,CAAC,GAAGY,MAAM,GAAG,CAAC,EAAEX,CAAC,GAAG,CAAEW,MAAM,GAAGC,KAAK,IAAK,CAAC,EAAEb,CAAC,GAAGC,CAAC,EAAED,CAAC,IAAI,CAAC,EAAG;kBAErExE,WAAW,CAAEyJ,QAAQ,EAAEjF,CAAC,EAAErC,KAAK,EAAEyK,GAAI,CAAC;kBACtCnD,QAAQ,CAAC/G,WAAW,GAAG,IAAI;kBAE3B,MAAMkL,IAAI,GAAGnE,QAAQ,CAACoE,kBAAkB,CAAEpD,SAAS,EAAEsC,WAAW,EAAEE,WAAY,CAAC;kBAC/E,IAAKW,IAAI,GAAGT,eAAe,EAAG;oBAE7BH,eAAe,CAACzC,IAAI,CAAEwC,WAAY,CAAC;oBAEnC,IAAKG,eAAe,EAAG;sBAEtBA,eAAe,CAAC3C,IAAI,CAAE0C,WAAY,CAAC;oBAEpC;oBAEAE,eAAe,GAAGS,IAAI;oBACtBR,uBAAuB,GAAG5I,CAAC,GAAG,CAAC;oBAC/B6I,4BAA4B,GAAGlC,EAAE,GAAG,CAAC;kBAEtC;;kBAEA;kBACA,IAAKyC,IAAI,GAAGnB,YAAY,EAAG;oBAE1B,OAAO,IAAI;kBAEZ;gBAED;cAED;YAED;UACD,CAAE,CAAC;QAEJ,CAAC,MAAM;UAEN;UACA,MAAMqB,QAAQ,GAAGhB,UAAU,GAAGA,UAAU,CAACzH,KAAK,GAAGwH,QAAQ,CAACxH,KAAK;UAC/D,KAAM,IAAI8F,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG0C,QAAQ,EAAE3C,EAAE,GAAGC,EAAE,EAAED,EAAE,IAAI,CAAC,EAAG;YAEnDnL,WAAW,CAAEyK,SAAS,EAAEU,EAAE,EAAE2B,UAAU,EAAED,QAAS,CAAC;YAClDpC,SAAS,CAACY,CAAC,CAACC,YAAY,CAAEgB,aAAc,CAAC;YACzC7B,SAAS,CAACc,CAAC,CAACD,YAAY,CAAEgB,aAAc,CAAC;YACzC7B,SAAS,CAACe,CAAC,CAACF,YAAY,CAAEgB,aAAc,CAAC;YACzC7B,SAAS,CAAC/H,WAAW,GAAG,IAAI;YAE5B,KAAM,IAAI8B,CAAC,GAAGY,MAAM,GAAG,CAAC,EAAEX,CAAC,GAAG,CAAEW,MAAM,GAAGC,KAAK,IAAK,CAAC,EAAEb,CAAC,GAAGC,CAAC,EAAED,CAAC,IAAI,CAAC,EAAG;cAErExE,WAAW,CAAEyJ,QAAQ,EAAEjF,CAAC,EAAErC,KAAK,EAAEyK,GAAI,CAAC;cACtCnD,QAAQ,CAAC/G,WAAW,GAAG,IAAI;cAE3B,MAAMkL,IAAI,GAAGnE,QAAQ,CAACoE,kBAAkB,CAAEpD,SAAS,EAAEsC,WAAW,EAAEE,WAAY,CAAC;cAC/E,IAAKW,IAAI,GAAGT,eAAe,EAAG;gBAE7BH,eAAe,CAACzC,IAAI,CAAEwC,WAAY,CAAC;gBAEnC,IAAKG,eAAe,EAAG;kBAEtBA,eAAe,CAAC3C,IAAI,CAAE0C,WAAY,CAAC;gBAEpC;gBAEAE,eAAe,GAAGS,IAAI;gBACtBR,uBAAuB,GAAG5I,CAAC,GAAG,CAAC;gBAC/B6I,4BAA4B,GAAGlC,EAAE,GAAG,CAAC;cAEtC;;cAEA;cACA,IAAKyC,IAAI,GAAGnB,YAAY,EAAG;gBAE1B,OAAO,IAAI;cAEZ;YAED;UAED;QAED;MAED;IAED,CAED,CAAC;IAED3L,YAAY,CAAC+I,gBAAgB,CAAEJ,QAAS,CAAC;IACzC3I,YAAY,CAAC+I,gBAAgB,CAAEY,SAAU,CAAC;IAE1C,IAAK0C,eAAe,KAAK5H,QAAQ,EAAG,OAAO,IAAI;IAE/C,IAAK,CAAEgH,OAAO,CAACwB,KAAK,EAAGxB,OAAO,CAACwB,KAAK,GAAGf,eAAe,CAACgB,KAAK,CAAC,CAAC,CAAC,KAC1DzB,OAAO,CAACwB,KAAK,CAACxD,IAAI,CAAEyC,eAAgB,CAAC;IAC1CT,OAAO,CAAC7D,QAAQ,GAAGyE,eAAe,EAClCZ,OAAO,CAAC0B,SAAS,GAAGb,uBAAuB;IAE3C,IAAKZ,OAAO,EAAG;MAEd,IAAK,CAAEA,OAAO,CAACuB,KAAK,EAAGvB,OAAO,CAACuB,KAAK,GAAGb,eAAe,CAACc,KAAK,CAAC,CAAC,CAAC,KAC1DxB,OAAO,CAACuB,KAAK,CAACxD,IAAI,CAAE2C,eAAgB,CAAC;MAC1CV,OAAO,CAACuB,KAAK,CAACzC,YAAY,CAAEjL,UAAW,CAAC;MACxC2M,eAAe,CAAC1B,YAAY,CAAEjL,UAAW,CAAC;MAC1CmM,OAAO,CAAC9D,QAAQ,GAAGsE,eAAe,CAACkB,GAAG,CAAE1B,OAAO,CAACuB,KAAM,CAAC,CAACrJ,MAAM,CAAC,CAAC;MAChE8H,OAAO,CAACyB,SAAS,GAAGZ,4BAA4B;IAEjD;IAEA,OAAOd,OAAO;EAEf;EAEA4B,mBAAmBA,CAAEJ,KAAK,EAAEK,MAAM,GAAG,CAAE,CAAC,EAAE3B,YAAY,GAAG,CAAC,EAAEC,YAAY,GAAGnH,QAAQ,EAAG;IAErF;IACA;IACA;IACA;IACA,MAAM8I,cAAc,GAAG5B,YAAY,GAAGA,YAAY;IAClD,MAAM6B,cAAc,GAAG5B,YAAY,GAAGA,YAAY;IAClD,IAAI6B,iBAAiB,GAAGhJ,QAAQ;IAChC,IAAI6H,uBAAuB,GAAG,IAAI;IAClC,IAAI,CAAC7N,SAAS,CAEb;MAEC8J,mBAAmB,EAAEuC,GAAG,IAAI;QAE3BpL,IAAI,CAAC+J,IAAI,CAAEwD,KAAM,CAAC,CAACS,KAAK,CAAE5C,GAAG,CAACK,GAAG,EAAEL,GAAG,CAACM,GAAI,CAAC;QAC5C,OAAO1L,IAAI,CAACiO,iBAAiB,CAAEV,KAAM,CAAC;MAEvC,CAAC;MAEDzE,gBAAgB,EAAEA,CAAEsC,GAAG,EAAEzG,MAAM,EAAEqI,KAAK,KAAM;QAE3C,OAAOA,KAAK,GAAGe,iBAAiB,IAAIf,KAAK,GAAGc,cAAc;MAE3D,CAAC;MAED/E,kBAAkB,EAAEA,CAAEL,GAAG,EAAEwF,QAAQ,KAAM;QAExCxF,GAAG,CAACiF,mBAAmB,CAAEJ,KAAK,EAAEvN,IAAK,CAAC;QACtC,MAAMmO,MAAM,GAAGZ,KAAK,CAACU,iBAAiB,CAAEjO,IAAK,CAAC;QAC9C,IAAKmO,MAAM,GAAGJ,iBAAiB,EAAG;UAEjC9N,KAAK,CAAC8J,IAAI,CAAE/J,IAAK,CAAC;UAClB+N,iBAAiB,GAAGI,MAAM;UAC1BvB,uBAAuB,GAAGsB,QAAQ;QAEnC;QAEA,IAAKC,MAAM,GAAGN,cAAc,EAAG;UAE9B,OAAO,IAAI;QAEZ,CAAC,MAAM;UAEN,OAAO,KAAK;QAEb;MAED;IAED,CAED,CAAC;IAED,IAAKE,iBAAiB,KAAKhJ,QAAQ,EAAG,OAAO,IAAI;IAEjD,MAAM4H,eAAe,GAAGyB,IAAI,CAACC,IAAI,CAAEN,iBAAkB,CAAC;IAEtD,IAAK,CAAEH,MAAM,CAACL,KAAK,EAAGK,MAAM,CAACL,KAAK,GAAGtN,KAAK,CAACuN,KAAK,CAAC,CAAC,CAAC,KAC9CI,MAAM,CAACL,KAAK,CAACxD,IAAI,CAAE9J,KAAM,CAAC;IAC/B2N,MAAM,CAAC1F,QAAQ,GAAGyE,eAAe,EACjCiB,MAAM,CAACH,SAAS,GAAGb,uBAAuB;IAE1C,OAAOgB,MAAM;EAEd;EAEA3K,cAAcA,CAAE2K,MAAM,EAAG;IAExBA,MAAM,CAACU,SAAS,CAAC,CAAC;IAElB,MAAM/M,KAAK,GAAG,IAAI,CAACJ,MAAM;IACzBI,KAAK,CAACgN,OAAO,CAAE5K,MAAM,IAAI;MAExBrE,UAAU,CAAE,CAAC,EAAE,IAAI+E,YAAY,CAAEV,MAAO,CAAC,EAAEtD,OAAQ,CAAC;MACpDuN,MAAM,CAACY,KAAK,CAAEnO,OAAQ,CAAC;IAExB,CAAE,CAAC;IAEH,OAAOuN,MAAM;EAEd;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}