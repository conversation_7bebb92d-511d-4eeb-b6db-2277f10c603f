{"ast": null, "code": "export { b as buffer } from './buffer-d2a4726c.esm.js';\nexport { i as random } from './index-43782085.esm.js';\nexport { e as easing } from './easing-3be59c6d.esm.js';\nexport { g as geometry } from './geometry-217d0c0b.esm.js';\nexport { m as matrix } from './matrix-baa530bf.esm.js';\nexport { m as misc } from './misc-7d870b3c.esm.js';\nexport { t as three } from './three-eb2ad8c0.esm.js';\nexport { t as triangle } from './triangle-b62b9067.esm.js';\nexport { v as vector2 } from './vector2-d2bf51f1.esm.js';\nexport { v as vector3 } from './vector3-0a088b7f.esm.js';\nimport './objectSpread2-284232a6.esm.js';\nimport 'three';\nimport './classCallCheck-9098b006.esm.js';\nimport './isNativeReflectConstruct-5594d075.esm.js';", "map": {"version": 3, "names": ["b", "buffer", "i", "random", "e", "easing", "g", "geometry", "m", "matrix", "misc", "t", "three", "triangle", "v", "vector2", "vector3"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/maath/dist/maath.esm.js"], "sourcesContent": ["export { b as buffer } from './buffer-d2a4726c.esm.js';\nexport { i as random } from './index-43782085.esm.js';\nexport { e as easing } from './easing-3be59c6d.esm.js';\nexport { g as geometry } from './geometry-217d0c0b.esm.js';\nexport { m as matrix } from './matrix-baa530bf.esm.js';\nexport { m as misc } from './misc-7d870b3c.esm.js';\nexport { t as three } from './three-eb2ad8c0.esm.js';\nexport { t as triangle } from './triangle-b62b9067.esm.js';\nexport { v as vector2 } from './vector2-d2bf51f1.esm.js';\nexport { v as vector3 } from './vector3-0a088b7f.esm.js';\nimport './objectSpread2-284232a6.esm.js';\nimport 'three';\nimport './classCallCheck-9098b006.esm.js';\nimport './isNativeReflectConstruct-5594d075.esm.js';\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,MAAM,QAAQ,0BAA0B;AACtD,SAASC,CAAC,IAAIC,MAAM,QAAQ,yBAAyB;AACrD,SAASC,CAAC,IAAIC,MAAM,QAAQ,0BAA0B;AACtD,SAASC,CAAC,IAAIC,QAAQ,QAAQ,4BAA4B;AAC1D,SAASC,CAAC,IAAIC,MAAM,QAAQ,0BAA0B;AACtD,SAASD,CAAC,IAAIE,IAAI,QAAQ,wBAAwB;AAClD,SAASC,CAAC,IAAIC,KAAK,QAAQ,yBAAyB;AACpD,SAASD,CAAC,IAAIE,QAAQ,QAAQ,4BAA4B;AAC1D,SAASC,CAAC,IAAIC,OAAO,QAAQ,2BAA2B;AACxD,SAASD,CAAC,IAAIE,OAAO,QAAQ,2BAA2B;AACxD,OAAO,iCAAiC;AACxC,OAAO,OAAO;AACd,OAAO,kCAAkC;AACzC,OAAO,4CAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}