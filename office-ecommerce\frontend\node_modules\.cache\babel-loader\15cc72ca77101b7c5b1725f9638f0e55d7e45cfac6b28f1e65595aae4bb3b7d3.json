{"ast": null, "code": "// API service for frontend application\n// This handles communication with the backend API\nimport apiClient from './apiClient';\n\n// Mock data for products\nconst mockProducts = [{\n  id: 1,\n  name: \"Executive Office Table\",\n  description: \"Premium executive office table with modern design and customizable features.\",\n  basePrice: 500,\n  category: \"Tables\",\n  image: \"/images/table-1.jpg\",\n  features: [\"Adjustable height\", \"Premium materials\", \"Modern design\", \"Customizable\"]\n}, {\n  id: 2,\n  name: \"Ergonomic Office Chair\",\n  description: \"Comfortable ergonomic office chair with lumbar support and adjustable features.\",\n  basePrice: 200,\n  category: \"Chairs\",\n  image: \"/images/chair-1.jpg\",\n  features: [\"Ergonomic design\", \"Lumbar support\", \"Adjustable height\", \"Breathable mesh\"]\n}, {\n  id: 3,\n  name: \"Storage Cabinet\",\n  description: \"Modern storage cabinet with multiple compartments and secure locking system.\",\n  basePrice: 800,\n  category: \"Storage\",\n  image: \"/images/cabinet-1.jpg\",\n  features: [\"Multiple compartments\", \"Secure locks\", \"Modern design\", \"Durable materials\"]\n}];\n\n// Mock categories\nconst mockCategories = [{\n  id: 1,\n  name: \"Tables\",\n  description: \"Office tables and desks\"\n}, {\n  id: 2,\n  name: \"Chairs\",\n  description: \"Office chairs and seating\"\n}, {\n  id: 3,\n  name: \"Storage\",\n  description: \"Storage solutions and cabinets\"\n}, {\n  id: 4,\n  name: \"Workstations\",\n  description: \"Complete workstation setups\"\n}];\n\n// Mock API functions\nconst api = {\n  // Get all products\n  getProducts: () => {\n    return Promise.resolve({\n      success: true,\n      data: mockProducts\n    });\n  },\n  // Get product by ID\n  getProduct: id => {\n    const product = mockProducts.find(p => p.id === parseInt(id));\n    return Promise.resolve({\n      success: true,\n      data: product || null\n    });\n  },\n  // Get categories\n  getCategories: () => {\n    return Promise.resolve({\n      success: true,\n      data: mockCategories\n    });\n  }\n};\n\n// Enhanced Products API for admin functionality\nconst productsApi = {\n  // Get products with pagination and filtering\n  getProducts: async (params = {}) => {\n    try {\n      const response = await apiClient.get('/api/products', {\n        params\n      });\n      return response;\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      // Fallback to mock data if backend is not available\n      return {\n        success: true,\n        data: [],\n        pagination: {\n          currentPage: 1,\n          totalPages: 0,\n          totalCount: 0,\n          pageSize: 20\n        }\n      };\n    }\n  },\n  // Get product by ID with full details\n  getProductById: async id => {\n    try {\n      const response = await apiClient.get(`/api/products/${id}`);\n      return response;\n    } catch (error) {\n      console.error('Error fetching product by ID:', error);\n      return {\n        success: false,\n        message: 'Product not found'\n      };\n    }\n  },\n  // Create or update product\n  createOrUpdateProduct: async productData => {\n    try {\n      const url = productData.ProductID ? `/api/products/${productData.ProductID}` : '/api/products';\n      const method = productData.ProductID ? 'PUT' : 'POST';\n      const response = await apiClient[method.toLowerCase()](url, productData);\n      return response;\n    } catch (error) {\n      console.error('Error creating/updating product:', error);\n      throw error;\n    }\n  },\n  // Delete product\n  deleteProduct: async productId => {\n    try {\n      const response = await apiClient.delete(`/api/products/${productId}`);\n      return response;\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      throw error;\n    }\n  },\n  // Upload 3D model\n  uploadModel: async (productId, formData) => {\n    try {\n      const response = await apiClient.client.post(`/api/products/${productId}/models`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error uploading 3D model:', error);\n      throw error;\n    }\n  },\n  // Upload images\n  uploadImages: async (productId, formData) => {\n    try {\n      const response = await apiClient.client.post(`/api/products/${productId}/images`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error uploading images:', error);\n      throw error;\n    }\n  },\n  // Get categories\n  getCategories: async () => {\n    try {\n      const response = await apiClient.get('/api/products/categories');\n      return response;\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n      // Fallback to mock data if backend is not available\n      return {\n        success: true,\n        data: mockCategories\n      };\n    }\n  }\n};\n\n// Original API with authentication\nconst authApi = {\n  // Mock authentication with admin support\n  login: credentials => {\n    // Check for admin credentials\n    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {\n      return Promise.resolve({\n        success: true,\n        data: {\n          token: 'mock-admin-jwt-token',\n          user: {\n            id: 1,\n            firstName: 'Admin',\n            lastName: 'User',\n            email: credentials.email,\n            role: 'Admin'\n          }\n        }\n      });\n    }\n\n    // Check for employee credentials\n    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {\n      return Promise.resolve({\n        success: true,\n        data: {\n          token: 'mock-employee-jwt-token',\n          user: {\n            id: 2,\n            firstName: 'Manager',\n            lastName: 'User',\n            email: credentials.email,\n            role: 'Employee'\n          }\n        }\n      });\n    }\n\n    // Regular customer login\n    return Promise.resolve({\n      success: true,\n      data: {\n        token: 'mock-jwt-token',\n        user: {\n          id: 3,\n          firstName: 'Demo',\n          lastName: 'User',\n          email: credentials.email,\n          role: 'Customer'\n        }\n      }\n    });\n  },\n  // Mock registration (always succeeds)\n  register: userData => {\n    return Promise.resolve({\n      success: true,\n      data: {\n        token: 'mock-jwt-token',\n        user: {\n          id: 1,\n          name: userData.name,\n          email: userData.email\n        }\n      }\n    });\n  }\n};\n\n// Combine APIs\nconst combinedApi = {\n  ...api,\n  ...authApi\n};\nexport default combinedApi;\nexport { productsApi };", "map": {"version": 3, "names": ["apiClient", "mockProducts", "id", "name", "description", "basePrice", "category", "image", "features", "mockCategories", "api", "getProducts", "Promise", "resolve", "success", "data", "getProduct", "product", "find", "p", "parseInt", "getCategories", "productsApi", "params", "response", "get", "error", "console", "pagination", "currentPage", "totalPages", "totalCount", "pageSize", "getProductById", "message", "createOrUpdateProduct", "productData", "url", "ProductID", "method", "toLowerCase", "deleteProduct", "productId", "delete", "uploadModel", "formData", "client", "post", "headers", "uploadImages", "authApi", "login", "credentials", "email", "password", "token", "user", "firstName", "lastName", "role", "register", "userData", "combinedApi"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/services/api.js"], "sourcesContent": ["// API service for frontend application\n// This handles communication with the backend API\nimport apiClient from './apiClient';\n\n// Mock data for products\nconst mockProducts = [\n  {\n    id: 1,\n    name: \"Executive Office Table\",\n    description: \"Premium executive office table with modern design and customizable features.\",\n    basePrice: 500,\n    category: \"Tables\",\n    image: \"/images/table-1.jpg\",\n    features: [\"Adjustable height\", \"Premium materials\", \"Modern design\", \"Customizable\"]\n  },\n  {\n    id: 2,\n    name: \"Ergonomic Office Chair\",\n    description: \"Comfortable ergonomic office chair with lumbar support and adjustable features.\",\n    basePrice: 200,\n    category: \"Chairs\",\n    image: \"/images/chair-1.jpg\",\n    features: [\"Ergonomic design\", \"Lumbar support\", \"Adjustable height\", \"Breathable mesh\"]\n  },\n  {\n    id: 3,\n    name: \"Storage Cabinet\",\n    description: \"Modern storage cabinet with multiple compartments and secure locking system.\",\n    basePrice: 800,\n    category: \"Storage\",\n    image: \"/images/cabinet-1.jpg\",\n    features: [\"Multiple compartments\", \"Secure locks\", \"Modern design\", \"Durable materials\"]\n  }\n];\n\n// Mock categories\nconst mockCategories = [\n  { id: 1, name: \"Tables\", description: \"Office tables and desks\" },\n  { id: 2, name: \"Chairs\", description: \"Office chairs and seating\" },\n  { id: 3, name: \"Storage\", description: \"Storage solutions and cabinets\" },\n  { id: 4, name: \"Workstations\", description: \"Complete workstation setups\" }\n];\n\n// Mock API functions\nconst api = {\n  // Get all products\n  getProducts: () => {\n    return Promise.resolve({\n      success: true,\n      data: mockProducts\n    });\n  },\n\n  // Get product by ID\n  getProduct: (id) => {\n    const product = mockProducts.find(p => p.id === parseInt(id));\n    return Promise.resolve({\n      success: true,\n      data: product || null\n    });\n  },\n\n  // Get categories\n  getCategories: () => {\n    return Promise.resolve({\n      success: true,\n      data: mockCategories\n    });\n  }\n};\n\n// Enhanced Products API for admin functionality\nconst productsApi = {\n  // Get products with pagination and filtering\n  getProducts: async (params = {}) => {\n    try {\n      const response = await apiClient.get('/api/products', { params });\n      return response;\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      // Fallback to mock data if backend is not available\n      return {\n        success: true,\n        data: [],\n        pagination: {\n          currentPage: 1,\n          totalPages: 0,\n          totalCount: 0,\n          pageSize: 20\n        }\n      };\n    }\n  },\n\n  // Get product by ID with full details\n  getProductById: async (id) => {\n    try {\n      const response = await apiClient.get(`/api/products/${id}`);\n      return response;\n    } catch (error) {\n      console.error('Error fetching product by ID:', error);\n      return {\n        success: false,\n        message: 'Product not found'\n      };\n    }\n  },\n\n  // Create or update product\n  createOrUpdateProduct: async (productData) => {\n    try {\n      const url = productData.ProductID\n        ? `/api/products/${productData.ProductID}`\n        : '/api/products';\n\n      const method = productData.ProductID ? 'PUT' : 'POST';\n\n      const response = await apiClient[method.toLowerCase()](url, productData);\n      return response;\n    } catch (error) {\n      console.error('Error creating/updating product:', error);\n      throw error;\n    }\n  },\n\n  // Delete product\n  deleteProduct: async (productId) => {\n    try {\n      const response = await apiClient.delete(`/api/products/${productId}`);\n      return response;\n    } catch (error) {\n      console.error('Error deleting product:', error);\n      throw error;\n    }\n  },\n\n  // Upload 3D model\n  uploadModel: async (productId, formData) => {\n    try {\n      const response = await apiClient.client.post(`/api/products/${productId}/models`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error uploading 3D model:', error);\n      throw error;\n    }\n  },\n\n  // Upload images\n  uploadImages: async (productId, formData) => {\n    try {\n      const response = await apiClient.client.post(`/api/products/${productId}/images`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error uploading images:', error);\n      throw error;\n    }\n  },\n\n  // Get categories\n  getCategories: async () => {\n    try {\n      const response = await apiClient.get('/api/products/categories');\n      return response;\n    } catch (error) {\n      console.error('Error fetching categories:', error);\n      // Fallback to mock data if backend is not available\n      return {\n        success: true,\n        data: mockCategories\n      };\n    }\n  }\n};\n\n// Original API with authentication\nconst authApi = {\n  // Mock authentication with admin support\n  login: (credentials) => {\n    // Check for admin credentials\n    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {\n      return Promise.resolve({\n        success: true,\n        data: {\n          token: 'mock-admin-jwt-token',\n          user: {\n            id: 1,\n            firstName: 'Admin',\n            lastName: 'User',\n            email: credentials.email,\n            role: 'Admin'\n          }\n        }\n      });\n    }\n\n    // Check for employee credentials\n    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {\n      return Promise.resolve({\n        success: true,\n        data: {\n          token: 'mock-employee-jwt-token',\n          user: {\n            id: 2,\n            firstName: 'Manager',\n            lastName: 'User',\n            email: credentials.email,\n            role: 'Employee'\n          }\n        }\n      });\n    }\n\n    // Regular customer login\n    return Promise.resolve({\n      success: true,\n      data: {\n        token: 'mock-jwt-token',\n        user: {\n          id: 3,\n          firstName: 'Demo',\n          lastName: 'User',\n          email: credentials.email,\n          role: 'Customer'\n        }\n      }\n    });\n  },\n\n  // Mock registration (always succeeds)\n  register: (userData) => {\n    return Promise.resolve({\n      success: true,\n      data: {\n        token: 'mock-jwt-token',\n        user: {\n          id: 1,\n          name: userData.name,\n          email: userData.email\n        }\n      }\n    });\n  }\n};\n\n// Combine APIs\nconst combinedApi = {\n  ...api,\n  ...authApi\n};\n\nexport default combinedApi;\nexport { productsApi };\n"], "mappings": "AAAA;AACA;AACA,OAAOA,SAAS,MAAM,aAAa;;AAEnC;AACA,MAAMC,YAAY,GAAG,CACnB;EACEC,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,wBAAwB;EAC9BC,WAAW,EAAE,8EAA8E;EAC3FC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,QAAQ;EAClBC,KAAK,EAAE,qBAAqB;EAC5BC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,eAAe,EAAE,cAAc;AACtF,CAAC,EACD;EACEN,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,wBAAwB;EAC9BC,WAAW,EAAE,iFAAiF;EAC9FC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,QAAQ;EAClBC,KAAK,EAAE,qBAAqB;EAC5BC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,iBAAiB;AACzF,CAAC,EACD;EACEN,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,iBAAiB;EACvBC,WAAW,EAAE,8EAA8E;EAC3FC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,uBAAuB;EAC9BC,QAAQ,EAAE,CAAC,uBAAuB,EAAE,cAAc,EAAE,eAAe,EAAE,mBAAmB;AAC1F,CAAC,CACF;;AAED;AACA,MAAMC,cAAc,GAAG,CACrB;EAAEP,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,QAAQ;EAAEC,WAAW,EAAE;AAA0B,CAAC,EACjE;EAAEF,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,QAAQ;EAAEC,WAAW,EAAE;AAA4B,CAAC,EACnE;EAAEF,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,SAAS;EAAEC,WAAW,EAAE;AAAiC,CAAC,EACzE;EAAEF,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,cAAc;EAAEC,WAAW,EAAE;AAA8B,CAAC,CAC5E;;AAED;AACA,MAAMM,GAAG,GAAG;EACV;EACAC,WAAW,EAAEA,CAAA,KAAM;IACjB,OAAOC,OAAO,CAACC,OAAO,CAAC;MACrBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEd;IACR,CAAC,CAAC;EACJ,CAAC;EAED;EACAe,UAAU,EAAGd,EAAE,IAAK;IAClB,MAAMe,OAAO,GAAGhB,YAAY,CAACiB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjB,EAAE,KAAKkB,QAAQ,CAAClB,EAAE,CAAC,CAAC;IAC7D,OAAOU,OAAO,CAACC,OAAO,CAAC;MACrBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEE,OAAO,IAAI;IACnB,CAAC,CAAC;EACJ,CAAC;EAED;EACAI,aAAa,EAAEA,CAAA,KAAM;IACnB,OAAOT,OAAO,CAACC,OAAO,CAAC;MACrBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEN;IACR,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,MAAMa,WAAW,GAAG;EAClB;EACAX,WAAW,EAAE,MAAAA,CAAOY,MAAM,GAAG,CAAC,CAAC,KAAK;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMxB,SAAS,CAACyB,GAAG,CAAC,eAAe,EAAE;QAAEF;MAAO,CAAC,CAAC;MACjE,OAAOC,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD;MACA,OAAO;QACLZ,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,EAAE;QACRa,UAAU,EAAE;UACVC,WAAW,EAAE,CAAC;UACdC,UAAU,EAAE,CAAC;UACbC,UAAU,EAAE,CAAC;UACbC,QAAQ,EAAE;QACZ;MACF,CAAC;IACH;EACF,CAAC;EAED;EACAC,cAAc,EAAE,MAAO/B,EAAE,IAAK;IAC5B,IAAI;MACF,MAAMsB,QAAQ,GAAG,MAAMxB,SAAS,CAACyB,GAAG,CAAC,iBAAiBvB,EAAE,EAAE,CAAC;MAC3D,OAAOsB,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO;QACLZ,OAAO,EAAE,KAAK;QACdoB,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAC,qBAAqB,EAAE,MAAOC,WAAW,IAAK;IAC5C,IAAI;MACF,MAAMC,GAAG,GAAGD,WAAW,CAACE,SAAS,GAC7B,iBAAiBF,WAAW,CAACE,SAAS,EAAE,GACxC,eAAe;MAEnB,MAAMC,MAAM,GAAGH,WAAW,CAACE,SAAS,GAAG,KAAK,GAAG,MAAM;MAErD,MAAMd,QAAQ,GAAG,MAAMxB,SAAS,CAACuC,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC,CAACH,GAAG,EAAED,WAAW,CAAC;MACxE,OAAOZ,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAe,aAAa,EAAE,MAAOC,SAAS,IAAK;IAClC,IAAI;MACF,MAAMlB,QAAQ,GAAG,MAAMxB,SAAS,CAAC2C,MAAM,CAAC,iBAAiBD,SAAS,EAAE,CAAC;MACrE,OAAOlB,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAkB,WAAW,EAAE,MAAAA,CAAOF,SAAS,EAAEG,QAAQ,KAAK;IAC1C,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMxB,SAAS,CAAC8C,MAAM,CAACC,IAAI,CAAC,iBAAiBL,SAAS,SAAS,EAAEG,QAAQ,EAAE;QAC1FG,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAOxB,QAAQ,CAACT,IAAI;IACtB,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAuB,YAAY,EAAE,MAAAA,CAAOP,SAAS,EAAEG,QAAQ,KAAK;IAC3C,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMxB,SAAS,CAAC8C,MAAM,CAACC,IAAI,CAAC,iBAAiBL,SAAS,SAAS,EAAEG,QAAQ,EAAE;QAC1FG,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAOxB,QAAQ,CAACT,IAAI;IACtB,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAL,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMxB,SAAS,CAACyB,GAAG,CAAC,0BAA0B,CAAC;MAChE,OAAOD,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACA,OAAO;QACLZ,OAAO,EAAE,IAAI;QACbC,IAAI,EAAEN;MACR,CAAC;IACH;EACF;AACF,CAAC;;AAED;AACA,MAAMyC,OAAO,GAAG;EACd;EACAC,KAAK,EAAGC,WAAW,IAAK;IACtB;IACA,IAAIA,WAAW,CAACC,KAAK,KAAK,sBAAsB,IAAID,WAAW,CAACE,QAAQ,KAAK,UAAU,EAAE;MACvF,OAAO1C,OAAO,CAACC,OAAO,CAAC;QACrBC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE;UACJwC,KAAK,EAAE,sBAAsB;UAC7BC,IAAI,EAAE;YACJtD,EAAE,EAAE,CAAC;YACLuD,SAAS,EAAE,OAAO;YAClBC,QAAQ,EAAE,MAAM;YAChBL,KAAK,EAAED,WAAW,CAACC,KAAK;YACxBM,IAAI,EAAE;UACR;QACF;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIP,WAAW,CAACC,KAAK,KAAK,wBAAwB,IAAID,WAAW,CAACE,QAAQ,KAAK,UAAU,EAAE;MACzF,OAAO1C,OAAO,CAACC,OAAO,CAAC;QACrBC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE;UACJwC,KAAK,EAAE,yBAAyB;UAChCC,IAAI,EAAE;YACJtD,EAAE,EAAE,CAAC;YACLuD,SAAS,EAAE,SAAS;YACpBC,QAAQ,EAAE,MAAM;YAChBL,KAAK,EAAED,WAAW,CAACC,KAAK;YACxBM,IAAI,EAAE;UACR;QACF;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,OAAO/C,OAAO,CAACC,OAAO,CAAC;MACrBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJwC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE;UACJtD,EAAE,EAAE,CAAC;UACLuD,SAAS,EAAE,MAAM;UACjBC,QAAQ,EAAE,MAAM;UAChBL,KAAK,EAAED,WAAW,CAACC,KAAK;UACxBM,IAAI,EAAE;QACR;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,QAAQ,EAAGC,QAAQ,IAAK;IACtB,OAAOjD,OAAO,CAACC,OAAO,CAAC;MACrBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJwC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE;UACJtD,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE0D,QAAQ,CAAC1D,IAAI;UACnBkD,KAAK,EAAEQ,QAAQ,CAACR;QAClB;MACF;IACF,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,MAAMS,WAAW,GAAG;EAClB,GAAGpD,GAAG;EACN,GAAGwC;AACL,CAAC;AAED,eAAeY,WAAW;AAC1B,SAASxC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}