const logger = require('../utils/logger');

// Role hierarchy and permissions
const roleHierarchy = {
  'Admin': ['Admin', 'Employee', 'Customer'],
  'Employee': ['Employee', 'Customer'],
  'Customer': ['Customer']
};

const permissions = {
  'Admin': [
    'products:create',
    'products:read',
    'products:update',
    'products:delete',
    'products:upload',
    'inventory:create',
    'inventory:read',
    'inventory:update',
    'inventory:delete',
    'orders:create',
    'orders:read',
    'orders:update',
    'orders:delete',
    'users:create',
    'users:read',
    'users:update',
    'users:delete',
    'analytics:read',
    'settings:update'
  ],
  'Employee': [
    'products:create',
    'products:read',
    'products:update',
    'products:upload',
    'inventory:create',
    'inventory:read',
    'inventory:update',
    'orders:read',
    'orders:update',
    'analytics:read'
  ],
  'Customer': [
    'products:read',
    'orders:create',
    'orders:read'
  ]
};

/**
 * Role-based access control middleware
 * @param {string|string[]} allowedRoles - Single role or array of roles that can access the route
 * @param {string} permission - Optional specific permission to check
 * @returns {Function} Express middleware function
 */
const rbac = (allowedRoles, permission = null) => {
  return (req, res, next) => {
    try {
      // Ensure user is authenticated
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required',
          error: 'User not authenticated'
        });
      }

      const userRole = req.user.role;
      const userId = req.user.userId;

      // Convert single role to array for consistent processing
      const rolesArray = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];

      // Check if user's role is in the allowed roles
      let hasRoleAccess = false;
      
      for (const allowedRole of rolesArray) {
        if (roleHierarchy[userRole] && roleHierarchy[userRole].includes(allowedRole)) {
          hasRoleAccess = true;
          break;
        }
      }

      if (!hasRoleAccess) {
        logger.warn(`Access denied for user ${userId} with role ${userRole}. Required roles: ${rolesArray.join(', ')}`);
        return res.status(403).json({
          success: false,
          message: 'Access denied',
          error: `Insufficient permissions. Required role: ${rolesArray.join(' or ')}`
        });
      }

      // Check specific permission if provided
      if (permission) {
        const userPermissions = permissions[userRole] || [];
        
        if (!userPermissions.includes(permission)) {
          logger.warn(`Permission denied for user ${userId} with role ${userRole}. Required permission: ${permission}`);
          return res.status(403).json({
            success: false,
            message: 'Permission denied',
            error: `Insufficient permissions. Required permission: ${permission}`
          });
        }
      }

      // Add role and permissions to request for use in route handlers
      req.userRole = userRole;
      req.userPermissions = permissions[userRole] || [];

      logger.info(`Access granted for user ${userId} with role ${userRole}`);
      next();
    } catch (error) {
      logger.error('RBAC middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Authorization error',
        error: 'Internal server error during authorization'
      });
    }
  };
};

/**
 * Check if user has specific permission
 * @param {string} userRole - User's role
 * @param {string} permission - Permission to check
 * @returns {boolean} True if user has permission
 */
const hasPermission = (userRole, permission) => {
  const userPermissions = permissions[userRole] || [];
  return userPermissions.includes(permission);
};

/**
 * Check if user role can access another role's resources
 * @param {string} userRole - User's role
 * @param {string} targetRole - Target role to check access for
 * @returns {boolean} True if user can access target role's resources
 */
const canAccessRole = (userRole, targetRole) => {
  return roleHierarchy[userRole] && roleHierarchy[userRole].includes(targetRole);
};

/**
 * Get all permissions for a role
 * @param {string} role - Role to get permissions for
 * @returns {string[]} Array of permissions
 */
const getRolePermissions = (role) => {
  return permissions[role] || [];
};

/**
 * Middleware to check resource ownership or admin access
 * @param {string} resourceUserIdField - Field name in req.params or req.body that contains the resource owner's user ID
 * @returns {Function} Express middleware function
 */
const checkOwnershipOrAdmin = (resourceUserIdField = 'userId') => {
  return (req, res, next) => {
    try {
      const currentUserId = req.user.userId;
      const currentUserRole = req.user.role;
      const resourceUserId = req.params[resourceUserIdField] || req.body[resourceUserIdField];

      // Admin can access any resource
      if (currentUserRole === 'Admin') {
        return next();
      }

      // Employee can access customer resources but not other employee/admin resources
      if (currentUserRole === 'Employee') {
        // You might want to add additional logic here to check the target user's role
        return next();
      }

      // Users can only access their own resources
      if (currentUserId === resourceUserId) {
        return next();
      }

      logger.warn(`Ownership check failed for user ${currentUserId} accessing resource owned by ${resourceUserId}`);
      return res.status(403).json({
        success: false,
        message: 'Access denied',
        error: 'You can only access your own resources'
      });
    } catch (error) {
      logger.error('Ownership check error:', error);
      res.status(500).json({
        success: false,
        message: 'Authorization error',
        error: 'Internal server error during ownership check'
      });
    }
  };
};

/**
 * Middleware to check if user can modify orders based on status
 * @param {string[]} allowedStatuses - Order statuses that can be modified
 * @returns {Function} Express middleware function
 */
const checkOrderModificationPermission = (allowedStatuses = ['Pending', 'Confirmed']) => {
  return async (req, res, next) => {
    try {
      // This would require fetching the order from database
      // For now, we'll assume the order status is passed in the request
      const orderStatus = req.body.orderStatus || req.query.orderStatus;
      
      if (orderStatus && !allowedStatuses.includes(orderStatus)) {
        return res.status(403).json({
          success: false,
          message: 'Order modification not allowed',
          error: `Orders with status '${orderStatus}' cannot be modified`
        });
      }
      
      next();
    } catch (error) {
      logger.error('Order modification permission check error:', error);
      res.status(500).json({
        success: false,
        message: 'Authorization error',
        error: 'Internal server error during order modification check'
      });
    }
  };
};

module.exports = {
  rbac,
  hasPermission,
  canAccessRole,
  getRolePermissions,
  checkOwnershipOrAdmin,
  checkOrderModificationPermission,
  roleHierarchy,
  permissions
};
