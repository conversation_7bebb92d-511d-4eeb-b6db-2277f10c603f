{"ast": null, "code": "const presetsObj = {\n  sunset: 'venice/venice_sunset_1k.hdr',\n  dawn: 'kiara/kiara_1_dawn_1k.hdr',\n  night: 'dikhololo/dikhololo_night_1k.hdr',\n  warehouse: 'empty-wharehouse/empty_warehouse_01_1k.hdr',\n  forest: 'forrest-slope/forest_slope_1k.hdr',\n  apartment: 'lebombo/lebombo_1k.hdr',\n  studio: 'studio-small-3/studio_small_03_1k.hdr',\n  city: 'potsdamer-platz/potsdamer_platz_1k.hdr',\n  park: 'rooitou/rooitou_park_1k.hdr',\n  lobby: 'st-fagans/st_fagans_interior_1k.hdr'\n};\nexport { presetsObj };", "map": {"version": 3, "names": ["presetsObj", "sunset", "dawn", "night", "warehouse", "forest", "apartment", "studio", "city", "park", "lobby"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/helpers/environment-assets.js"], "sourcesContent": ["const presetsObj = {\n  sunset: 'venice/venice_sunset_1k.hdr',\n  dawn: 'kiara/kiara_1_dawn_1k.hdr',\n  night: 'dikhololo/dikhololo_night_1k.hdr',\n  warehouse: 'empty-wharehouse/empty_warehouse_01_1k.hdr',\n  forest: 'forrest-slope/forest_slope_1k.hdr',\n  apartment: 'lebombo/lebombo_1k.hdr',\n  studio: 'studio-small-3/studio_small_03_1k.hdr',\n  city: 'potsdamer-platz/potsdamer_platz_1k.hdr',\n  park: 'rooitou/rooitou_park_1k.hdr',\n  lobby: 'st-fagans/st_fagans_interior_1k.hdr'\n};\n\nexport { presetsObj };\n"], "mappings": "AAAA,MAAMA,UAAU,GAAG;EACjBC,MAAM,EAAE,6BAA6B;EACrCC,IAAI,EAAE,2BAA2B;EACjCC,KAAK,EAAE,kCAAkC;EACzCC,SAAS,EAAE,4CAA4C;EACvDC,MAAM,EAAE,mCAAmC;EAC3CC,SAAS,EAAE,wBAAwB;EACnCC,MAAM,EAAE,uCAAuC;EAC/CC,IAAI,EAAE,wCAAwC;EAC9CC,IAAI,EAAE,6BAA6B;EACnCC,KAAK,EAAE;AACT,CAAC;AAED,SAASV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}