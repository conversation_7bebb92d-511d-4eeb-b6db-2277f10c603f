/* ThreeJS Preview Styles */
.threejs-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e1e8ed;
}

.preview-container {
  flex: 1;
  position: relative;
  min-height: 400px;
  background: #f8f9fa;
}

/* Placeholder State */
.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
  color: #6c757d;
  text-align: center;
  padding: 40px 20px;
}

.placeholder-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.preview-placeholder h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.preview-placeholder p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

/* Loading State */
.preview-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(248, 249, 250, 0.9);
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e1e8ed;
  border-top: 4px solid #F0B21B;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.preview-loading p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

/* Error State */
.preview-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
  color: #dc3545;
  text-align: center;
  padding: 40px 20px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.preview-error h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #dc3545;
}

.preview-error p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

/* Model Information Panel */
.model-info-panel {
  padding: 16px;
  background: #f8f9fa;
  border-top: 1px solid #e1e8ed;
}

.model-info-panel h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.info-label {
  color: #6c757d;
  font-weight: 500;
}

.info-value {
  color: #2c3e50;
  font-weight: 600;
  text-align: right;
}

/* Preview Controls */
.preview-controls {
  padding: 12px 16px;
  background: #ffffff;
  border-top: 1px solid #e1e8ed;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-label {
  font-size: 12px;
  font-weight: 600;
  color: #2c3e50;
  min-width: 60px;
}

.control-hints {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.control-hints span {
  font-size: 11px;
  color: #6c757d;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Canvas Styling */
.threejs-preview canvas {
  display: block !important;
  border-radius: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .preview-container {
    min-height: 300px;
  }

  .preview-placeholder {
    min-height: 300px;
    padding: 30px 15px;
  }

  .placeholder-icon {
    font-size: 48px;
  }

  .preview-placeholder h3 {
    font-size: 18px;
  }

  .preview-placeholder p {
    font-size: 13px;
  }

  .preview-error {
    min-height: 300px;
    padding: 30px 15px;
  }

  .error-icon {
    font-size: 36px;
  }

  .preview-error h3 {
    font-size: 16px;
  }

  .model-info-panel {
    padding: 12px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }

  .info-value {
    text-align: left;
  }

  .preview-controls {
    padding: 10px 12px;
  }

  .control-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .control-hints {
    gap: 12px;
  }

  .control-hints span {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .preview-container {
    min-height: 250px;
  }

  .preview-placeholder {
    min-height: 250px;
    padding: 20px 10px;
  }

  .placeholder-icon {
    font-size: 40px;
  }

  .preview-placeholder h3 {
    font-size: 16px;
  }

  .preview-placeholder p {
    font-size: 12px;
  }

  .preview-error {
    min-height: 250px;
    padding: 20px 10px;
  }

  .error-icon {
    font-size: 32px;
  }

  .preview-error h3 {
    font-size: 14px;
  }

  .model-info-panel {
    padding: 10px;
  }

  .model-info-panel h4 {
    font-size: 13px;
  }

  .info-item {
    font-size: 11px;
  }

  .preview-controls {
    padding: 8px 10px;
  }

  .control-label {
    font-size: 11px;
    min-width: 50px;
  }

  .control-hints span {
    font-size: 9px;
  }
}

/* Animation for smooth transitions */
.threejs-preview {
  transition: all 0.3s ease;
}

.preview-container {
  transition: all 0.3s ease;
}

/* Focus states for accessibility */
.threejs-preview:focus-within {
  outline: 2px solid #F0B21B;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .preview-placeholder,
  .preview-error {
    border: 2px solid currentColor;
  }
  
  .model-info-panel,
  .preview-controls {
    border-top: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    animation: none;
  }
  
  .threejs-preview,
  .preview-container {
    transition: none;
  }
}
