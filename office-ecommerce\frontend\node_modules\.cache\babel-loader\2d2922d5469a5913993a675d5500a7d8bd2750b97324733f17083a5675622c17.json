{"ast": null, "code": "import { BufferAttribute } from 'three';\nimport { MeshBVHNode } from './MeshBVHNode.js';\nimport { getLongestEdgeIndex, computeSurfaceArea, copyBounds, unionBounds, expandByTriangleBounds } from '../utils/ArrayBoxUtilities.js';\nimport { CENTER, AVERAGE, SAH, TRIANGLE_INTERSECT_COST, TRAVERSAL_COST, BYTES_PER_NODE, FLOAT32_EPSILON, IS_LEAFNODE_FLAG } from './Constants.js';\nfunction ensureIndex(geo, options) {\n  if (!geo.index) {\n    const vertexCount = geo.attributes.position.count;\n    const BufferConstructor = options.useSharedArrayBuffer ? SharedArrayBuffer : ArrayBuffer;\n    let index;\n    if (vertexCount > 65535) {\n      index = new Uint32Array(new BufferConstructor(4 * vertexCount));\n    } else {\n      index = new Uint16Array(new BufferConstructor(2 * vertexCount));\n    }\n    geo.setIndex(new BufferAttribute(index, 1));\n    for (let i = 0; i < vertexCount; i++) {\n      index[i] = i;\n    }\n  }\n}\n\n// Computes the set of { offset, count } ranges which need independent BVH roots. Each\n// region in the geometry index that belongs to a different set of material groups requires\n// a separate BVH root, so that triangles indices belonging to one group never get swapped\n// with triangle indices belongs to another group. For example, if the groups were like this:\n//\n// [-------------------------------------------------------------]\n// |__________________|\n//   g0 = [0, 20]  |______________________||_____________________|\n//                      g1 = [16, 40]           g2 = [41, 60]\n//\n// we would need four BVH roots: [0, 15], [16, 20], [21, 40], [41, 60].\nfunction getRootIndexRanges(geo) {\n  if (!geo.groups || !geo.groups.length) {\n    return [{\n      offset: 0,\n      count: geo.index.count / 3\n    }];\n  }\n  const ranges = [];\n  const rangeBoundaries = new Set();\n  for (const group of geo.groups) {\n    rangeBoundaries.add(group.start);\n    rangeBoundaries.add(group.start + group.count);\n  }\n\n  // note that if you don't pass in a comparator, it sorts them lexicographically as strings :-(\n  const sortedBoundaries = Array.from(rangeBoundaries.values()).sort((a, b) => a - b);\n  for (let i = 0; i < sortedBoundaries.length - 1; i++) {\n    const start = sortedBoundaries[i],\n      end = sortedBoundaries[i + 1];\n    ranges.push({\n      offset: start / 3,\n      count: (end - start) / 3\n    });\n  }\n  return ranges;\n}\n\n// computes the union of the bounds of all of the given triangles and puts the resulting box in target. If\n// centroidTarget is provided then a bounding box is computed for the centroids of the triangles, as well.\n// These are computed together to avoid redundant accesses to bounds array.\nfunction getBounds(triangleBounds, offset, count, target, centroidTarget = null) {\n  let minx = Infinity;\n  let miny = Infinity;\n  let minz = Infinity;\n  let maxx = -Infinity;\n  let maxy = -Infinity;\n  let maxz = -Infinity;\n  let cminx = Infinity;\n  let cminy = Infinity;\n  let cminz = Infinity;\n  let cmaxx = -Infinity;\n  let cmaxy = -Infinity;\n  let cmaxz = -Infinity;\n  const includeCentroid = centroidTarget !== null;\n  for (let i = offset * 6, end = (offset + count) * 6; i < end; i += 6) {\n    const cx = triangleBounds[i + 0];\n    const hx = triangleBounds[i + 1];\n    const lx = cx - hx;\n    const rx = cx + hx;\n    if (lx < minx) minx = lx;\n    if (rx > maxx) maxx = rx;\n    if (includeCentroid && cx < cminx) cminx = cx;\n    if (includeCentroid && cx > cmaxx) cmaxx = cx;\n    const cy = triangleBounds[i + 2];\n    const hy = triangleBounds[i + 3];\n    const ly = cy - hy;\n    const ry = cy + hy;\n    if (ly < miny) miny = ly;\n    if (ry > maxy) maxy = ry;\n    if (includeCentroid && cy < cminy) cminy = cy;\n    if (includeCentroid && cy > cmaxy) cmaxy = cy;\n    const cz = triangleBounds[i + 4];\n    const hz = triangleBounds[i + 5];\n    const lz = cz - hz;\n    const rz = cz + hz;\n    if (lz < minz) minz = lz;\n    if (rz > maxz) maxz = rz;\n    if (includeCentroid && cz < cminz) cminz = cz;\n    if (includeCentroid && cz > cmaxz) cmaxz = cz;\n  }\n  target[0] = minx;\n  target[1] = miny;\n  target[2] = minz;\n  target[3] = maxx;\n  target[4] = maxy;\n  target[5] = maxz;\n  if (includeCentroid) {\n    centroidTarget[0] = cminx;\n    centroidTarget[1] = cminy;\n    centroidTarget[2] = cminz;\n    centroidTarget[3] = cmaxx;\n    centroidTarget[4] = cmaxy;\n    centroidTarget[5] = cmaxz;\n  }\n}\n\n// A stand alone function for retrieving the centroid bounds.\nfunction getCentroidBounds(triangleBounds, offset, count, centroidTarget) {\n  let cminx = Infinity;\n  let cminy = Infinity;\n  let cminz = Infinity;\n  let cmaxx = -Infinity;\n  let cmaxy = -Infinity;\n  let cmaxz = -Infinity;\n  for (let i = offset * 6, end = (offset + count) * 6; i < end; i += 6) {\n    const cx = triangleBounds[i + 0];\n    if (cx < cminx) cminx = cx;\n    if (cx > cmaxx) cmaxx = cx;\n    const cy = triangleBounds[i + 2];\n    if (cy < cminy) cminy = cy;\n    if (cy > cmaxy) cmaxy = cy;\n    const cz = triangleBounds[i + 4];\n    if (cz < cminz) cminz = cz;\n    if (cz > cmaxz) cmaxz = cz;\n  }\n  centroidTarget[0] = cminx;\n  centroidTarget[1] = cminy;\n  centroidTarget[2] = cminz;\n  centroidTarget[3] = cmaxx;\n  centroidTarget[4] = cmaxy;\n  centroidTarget[5] = cmaxz;\n}\n\n// reorders `tris` such that for `count` elements after `offset`, elements on the left side of the split\n// will be on the left and elements on the right side of the split will be on the right. returns the index\n// of the first element on the right side, or offset + count if there are no elements on the right side.\nfunction partition(index, triangleBounds, offset, count, split) {\n  let left = offset;\n  let right = offset + count - 1;\n  const pos = split.pos;\n  const axisOffset = split.axis * 2;\n\n  // hoare partitioning, see e.g. https://en.wikipedia.org/wiki/Quicksort#Hoare_partition_scheme\n  while (true) {\n    while (left <= right && triangleBounds[left * 6 + axisOffset] < pos) {\n      left++;\n    }\n\n    // if a triangle center lies on the partition plane it is considered to be on the right side\n    while (left <= right && triangleBounds[right * 6 + axisOffset] >= pos) {\n      right--;\n    }\n    if (left < right) {\n      // we need to swap all of the information associated with the triangles at index\n      // left and right; that's the verts in the geometry index, the bounds,\n      // and perhaps the SAH planes\n\n      for (let i = 0; i < 3; i++) {\n        let t0 = index[left * 3 + i];\n        index[left * 3 + i] = index[right * 3 + i];\n        index[right * 3 + i] = t0;\n        let t1 = triangleBounds[left * 6 + i * 2 + 0];\n        triangleBounds[left * 6 + i * 2 + 0] = triangleBounds[right * 6 + i * 2 + 0];\n        triangleBounds[right * 6 + i * 2 + 0] = t1;\n        let t2 = triangleBounds[left * 6 + i * 2 + 1];\n        triangleBounds[left * 6 + i * 2 + 1] = triangleBounds[right * 6 + i * 2 + 1];\n        triangleBounds[right * 6 + i * 2 + 1] = t2;\n      }\n      left++;\n      right--;\n    } else {\n      return left;\n    }\n  }\n}\nconst BIN_COUNT = 32;\nconst binsSort = (a, b) => a.candidate - b.candidate;\nconst sahBins = new Array(BIN_COUNT).fill().map(() => {\n  return {\n    count: 0,\n    bounds: new Float32Array(6),\n    rightCacheBounds: new Float32Array(6),\n    leftCacheBounds: new Float32Array(6),\n    candidate: 0\n  };\n});\nconst leftBounds = new Float32Array(6);\nfunction getOptimalSplit(nodeBoundingData, centroidBoundingData, triangleBounds, offset, count, strategy) {\n  let axis = -1;\n  let pos = 0;\n\n  // Center\n  if (strategy === CENTER) {\n    axis = getLongestEdgeIndex(centroidBoundingData);\n    if (axis !== -1) {\n      pos = (centroidBoundingData[axis] + centroidBoundingData[axis + 3]) / 2;\n    }\n  } else if (strategy === AVERAGE) {\n    axis = getLongestEdgeIndex(nodeBoundingData);\n    if (axis !== -1) {\n      pos = getAverage(triangleBounds, offset, count, axis);\n    }\n  } else if (strategy === SAH) {\n    const rootSurfaceArea = computeSurfaceArea(nodeBoundingData);\n    let bestCost = TRIANGLE_INTERSECT_COST * count;\n\n    // iterate over all axes\n    const cStart = offset * 6;\n    const cEnd = (offset + count) * 6;\n    for (let a = 0; a < 3; a++) {\n      const axisLeft = centroidBoundingData[a];\n      const axisRight = centroidBoundingData[a + 3];\n      const axisLength = axisRight - axisLeft;\n      const binWidth = axisLength / BIN_COUNT;\n\n      // If we have fewer triangles than we're planning to split then just check all\n      // the triangle positions because it will be faster.\n      if (count < BIN_COUNT / 4) {\n        // initialize the bin candidates\n        const truncatedBins = [...sahBins];\n        truncatedBins.length = count;\n\n        // set the candidates\n        let b = 0;\n        for (let c = cStart; c < cEnd; c += 6, b++) {\n          const bin = truncatedBins[b];\n          bin.candidate = triangleBounds[c + 2 * a];\n          bin.count = 0;\n          const {\n            bounds,\n            leftCacheBounds,\n            rightCacheBounds\n          } = bin;\n          for (let d = 0; d < 3; d++) {\n            rightCacheBounds[d] = Infinity;\n            rightCacheBounds[d + 3] = -Infinity;\n            leftCacheBounds[d] = Infinity;\n            leftCacheBounds[d + 3] = -Infinity;\n            bounds[d] = Infinity;\n            bounds[d + 3] = -Infinity;\n          }\n          expandByTriangleBounds(c, triangleBounds, bounds);\n        }\n        truncatedBins.sort(binsSort);\n\n        // remove redundant splits\n        let splitCount = count;\n        for (let bi = 0; bi < splitCount; bi++) {\n          const bin = truncatedBins[bi];\n          while (bi + 1 < splitCount && truncatedBins[bi + 1].candidate === bin.candidate) {\n            truncatedBins.splice(bi + 1, 1);\n            splitCount--;\n          }\n        }\n\n        // find the appropriate bin for each triangle and expand the bounds.\n        for (let c = cStart; c < cEnd; c += 6) {\n          const center = triangleBounds[c + 2 * a];\n          for (let bi = 0; bi < splitCount; bi++) {\n            const bin = truncatedBins[bi];\n            if (center >= bin.candidate) {\n              expandByTriangleBounds(c, triangleBounds, bin.rightCacheBounds);\n            } else {\n              expandByTriangleBounds(c, triangleBounds, bin.leftCacheBounds);\n              bin.count++;\n            }\n          }\n        }\n\n        // expand all the bounds\n        for (let bi = 0; bi < splitCount; bi++) {\n          const bin = truncatedBins[bi];\n          const leftCount = bin.count;\n          const rightCount = count - bin.count;\n\n          // check the cost of this split\n          const leftBounds = bin.leftCacheBounds;\n          const rightBounds = bin.rightCacheBounds;\n          let leftProb = 0;\n          if (leftCount !== 0) {\n            leftProb = computeSurfaceArea(leftBounds) / rootSurfaceArea;\n          }\n          let rightProb = 0;\n          if (rightCount !== 0) {\n            rightProb = computeSurfaceArea(rightBounds) / rootSurfaceArea;\n          }\n          const cost = TRAVERSAL_COST + TRIANGLE_INTERSECT_COST * (leftProb * leftCount + rightProb * rightCount);\n          if (cost < bestCost) {\n            axis = a;\n            bestCost = cost;\n            pos = bin.candidate;\n          }\n        }\n      } else {\n        // reset the bins\n        for (let i = 0; i < BIN_COUNT; i++) {\n          const bin = sahBins[i];\n          bin.count = 0;\n          bin.candidate = axisLeft + binWidth + i * binWidth;\n          const bounds = bin.bounds;\n          for (let d = 0; d < 3; d++) {\n            bounds[d] = Infinity;\n            bounds[d + 3] = -Infinity;\n          }\n        }\n\n        // iterate over all center positions\n        for (let c = cStart; c < cEnd; c += 6) {\n          const triCenter = triangleBounds[c + 2 * a];\n          const relativeCenter = triCenter - axisLeft;\n\n          // in the partition function if the centroid lies on the split plane then it is\n          // considered to be on the right side of the split\n          let binIndex = ~~(relativeCenter / binWidth);\n          if (binIndex >= BIN_COUNT) binIndex = BIN_COUNT - 1;\n          const bin = sahBins[binIndex];\n          bin.count++;\n          expandByTriangleBounds(c, triangleBounds, bin.bounds);\n        }\n\n        // cache the unioned bounds from right to left so we don't have to regenerate them each time\n        const lastBin = sahBins[BIN_COUNT - 1];\n        copyBounds(lastBin.bounds, lastBin.rightCacheBounds);\n        for (let i = BIN_COUNT - 2; i >= 0; i--) {\n          const bin = sahBins[i];\n          const nextBin = sahBins[i + 1];\n          unionBounds(bin.bounds, nextBin.rightCacheBounds, bin.rightCacheBounds);\n        }\n        let leftCount = 0;\n        for (let i = 0; i < BIN_COUNT - 1; i++) {\n          const bin = sahBins[i];\n          const binCount = bin.count;\n          const bounds = bin.bounds;\n          const nextBin = sahBins[i + 1];\n          const rightBounds = nextBin.rightCacheBounds;\n\n          // don't do anything with the bounds if the new bounds have no triangles\n          if (binCount !== 0) {\n            if (leftCount === 0) {\n              copyBounds(bounds, leftBounds);\n            } else {\n              unionBounds(bounds, leftBounds, leftBounds);\n            }\n          }\n          leftCount += binCount;\n\n          // check the cost of this split\n          let leftProb = 0;\n          let rightProb = 0;\n          if (leftCount !== 0) {\n            leftProb = computeSurfaceArea(leftBounds) / rootSurfaceArea;\n          }\n          const rightCount = count - leftCount;\n          if (rightCount !== 0) {\n            rightProb = computeSurfaceArea(rightBounds) / rootSurfaceArea;\n          }\n          const cost = TRAVERSAL_COST + TRIANGLE_INTERSECT_COST * (leftProb * leftCount + rightProb * rightCount);\n          if (cost < bestCost) {\n            axis = a;\n            bestCost = cost;\n            pos = bin.candidate;\n          }\n        }\n      }\n    }\n  } else {\n    console.warn(`MeshBVH: Invalid build strategy value ${strategy} used.`);\n  }\n  return {\n    axis,\n    pos\n  };\n}\n\n// returns the average coordinate on the specified axis of the all the provided triangles\nfunction getAverage(triangleBounds, offset, count, axis) {\n  let avg = 0;\n  for (let i = offset, end = offset + count; i < end; i++) {\n    avg += triangleBounds[i * 6 + axis * 2];\n  }\n  return avg / count;\n}\n\n// precomputes the bounding box for each triangle; required for quickly calculating tree splits.\n// result is an array of size tris.length * 6 where triangle i maps to a\n// [x_center, x_delta, y_center, y_delta, z_center, z_delta] tuple starting at index i * 6,\n// representing the center and half-extent in each dimension of triangle i\nfunction computeTriangleBounds(geo, fullBounds) {\n  const posAttr = geo.attributes.position;\n  const index = geo.index.array;\n  const triCount = index.length / 3;\n  const triangleBounds = new Float32Array(triCount * 6);\n  const normalized = posAttr.normalized;\n\n  // used for non-normalized positions\n  const posArr = posAttr.array;\n\n  // support for an interleaved position buffer\n  const bufferOffset = posAttr.offset || 0;\n  let stride = 3;\n  if (posAttr.isInterleavedBufferAttribute) {\n    stride = posAttr.data.stride;\n  }\n\n  // used for normalized positions\n  const getters = ['getX', 'getY', 'getZ'];\n  for (let tri = 0; tri < triCount; tri++) {\n    const tri3 = tri * 3;\n    const tri6 = tri * 6;\n    let ai, bi, ci;\n    if (normalized) {\n      ai = index[tri3 + 0];\n      bi = index[tri3 + 1];\n      ci = index[tri3 + 2];\n    } else {\n      ai = index[tri3 + 0] * stride + bufferOffset;\n      bi = index[tri3 + 1] * stride + bufferOffset;\n      ci = index[tri3 + 2] * stride + bufferOffset;\n    }\n    for (let el = 0; el < 3; el++) {\n      let a, b, c;\n      if (normalized) {\n        a = posAttr[getters[el]](ai);\n        b = posAttr[getters[el]](bi);\n        c = posAttr[getters[el]](ci);\n      } else {\n        a = posArr[ai + el];\n        b = posArr[bi + el];\n        c = posArr[ci + el];\n      }\n      let min = a;\n      if (b < min) min = b;\n      if (c < min) min = c;\n      let max = a;\n      if (b > max) max = b;\n      if (c > max) max = c;\n\n      // Increase the bounds size by float32 epsilon to avoid precision errors when\n      // converting to 32 bit float. Scale the epsilon by the size of the numbers being\n      // worked with.\n      const halfExtents = (max - min) / 2;\n      const el2 = el * 2;\n      triangleBounds[tri6 + el2 + 0] = min + halfExtents;\n      triangleBounds[tri6 + el2 + 1] = halfExtents + (Math.abs(min) + halfExtents) * FLOAT32_EPSILON;\n      if (min < fullBounds[el]) fullBounds[el] = min;\n      if (max > fullBounds[el + 3]) fullBounds[el + 3] = max;\n    }\n  }\n  return triangleBounds;\n}\nexport function buildTree(geo, options) {\n  function triggerProgress(trianglesProcessed) {\n    if (onProgress) {\n      onProgress(trianglesProcessed / totalTriangles);\n    }\n  }\n\n  // either recursively splits the given node, creating left and right subtrees for it, or makes it a leaf node,\n  // recording the offset and count of its triangles and writing them into the reordered geometry index.\n  function splitNode(node, offset, count, centroidBoundingData = null, depth = 0) {\n    if (!reachedMaxDepth && depth >= maxDepth) {\n      reachedMaxDepth = true;\n      if (verbose) {\n        console.warn(`MeshBVH: Max depth of ${maxDepth} reached when generating BVH. Consider increasing maxDepth.`);\n        console.warn(geo);\n      }\n    }\n\n    // early out if we've met our capacity\n    if (count <= maxLeafTris || depth >= maxDepth) {\n      triggerProgress(offset + count);\n      node.offset = offset;\n      node.count = count;\n      return node;\n    }\n\n    // Find where to split the volume\n    const split = getOptimalSplit(node.boundingData, centroidBoundingData, triangleBounds, offset, count, strategy);\n    if (split.axis === -1) {\n      triggerProgress(offset + count);\n      node.offset = offset;\n      node.count = count;\n      return node;\n    }\n    const splitOffset = partition(indexArray, triangleBounds, offset, count, split);\n\n    // create the two new child nodes\n    if (splitOffset === offset || splitOffset === offset + count) {\n      triggerProgress(offset + count);\n      node.offset = offset;\n      node.count = count;\n    } else {\n      node.splitAxis = split.axis;\n\n      // create the left child and compute its bounding box\n      const left = new MeshBVHNode();\n      const lstart = offset;\n      const lcount = splitOffset - offset;\n      node.left = left;\n      left.boundingData = new Float32Array(6);\n      getBounds(triangleBounds, lstart, lcount, left.boundingData, cacheCentroidBoundingData);\n      splitNode(left, lstart, lcount, cacheCentroidBoundingData, depth + 1);\n\n      // repeat for right\n      const right = new MeshBVHNode();\n      const rstart = splitOffset;\n      const rcount = count - lcount;\n      node.right = right;\n      right.boundingData = new Float32Array(6);\n      getBounds(triangleBounds, rstart, rcount, right.boundingData, cacheCentroidBoundingData);\n      splitNode(right, rstart, rcount, cacheCentroidBoundingData, depth + 1);\n    }\n    return node;\n  }\n  ensureIndex(geo, options);\n\n  // Compute the full bounds of the geometry at the same time as triangle bounds because\n  // we'll need it for the root bounds in the case with no groups and it should be fast here.\n  // We can't use the geometrying bounding box if it's available because it may be out of date.\n  const fullBounds = new Float32Array(6);\n  const cacheCentroidBoundingData = new Float32Array(6);\n  const triangleBounds = computeTriangleBounds(geo, fullBounds);\n  const indexArray = geo.index.array;\n  const maxDepth = options.maxDepth;\n  const verbose = options.verbose;\n  const maxLeafTris = options.maxLeafTris;\n  const strategy = options.strategy;\n  const onProgress = options.onProgress;\n  const totalTriangles = geo.index.count / 3;\n  let reachedMaxDepth = false;\n  const roots = [];\n  const ranges = getRootIndexRanges(geo);\n  if (ranges.length === 1) {\n    const range = ranges[0];\n    const root = new MeshBVHNode();\n    root.boundingData = fullBounds;\n    getCentroidBounds(triangleBounds, range.offset, range.count, cacheCentroidBoundingData);\n    splitNode(root, range.offset, range.count, cacheCentroidBoundingData);\n    roots.push(root);\n  } else {\n    for (let range of ranges) {\n      const root = new MeshBVHNode();\n      root.boundingData = new Float32Array(6);\n      getBounds(triangleBounds, range.offset, range.count, root.boundingData, cacheCentroidBoundingData);\n      splitNode(root, range.offset, range.count, cacheCentroidBoundingData);\n      roots.push(root);\n    }\n  }\n  return roots;\n}\nexport function buildPackedTree(geo, options) {\n  // boundingData  \t\t\t\t: 6 float32\n  // right / offset \t\t\t\t: 1 uint32\n  // splitAxis / isLeaf + count \t: 1 uint32 / 2 uint16\n  const roots = buildTree(geo, options);\n  let float32Array;\n  let uint32Array;\n  let uint16Array;\n  const packedRoots = [];\n  const BufferConstructor = options.useSharedArrayBuffer ? SharedArrayBuffer : ArrayBuffer;\n  for (let i = 0; i < roots.length; i++) {\n    const root = roots[i];\n    let nodeCount = countNodes(root);\n    const buffer = new BufferConstructor(BYTES_PER_NODE * nodeCount);\n    float32Array = new Float32Array(buffer);\n    uint32Array = new Uint32Array(buffer);\n    uint16Array = new Uint16Array(buffer);\n    populateBuffer(0, root);\n    packedRoots.push(buffer);\n  }\n  return packedRoots;\n  function countNodes(node) {\n    if (node.count) {\n      return 1;\n    } else {\n      return 1 + countNodes(node.left) + countNodes(node.right);\n    }\n  }\n  function populateBuffer(byteOffset, node) {\n    const stride4Offset = byteOffset / 4;\n    const stride2Offset = byteOffset / 2;\n    const isLeaf = !!node.count;\n    const boundingData = node.boundingData;\n    for (let i = 0; i < 6; i++) {\n      float32Array[stride4Offset + i] = boundingData[i];\n    }\n    if (isLeaf) {\n      const offset = node.offset;\n      const count = node.count;\n      uint32Array[stride4Offset + 6] = offset;\n      uint16Array[stride2Offset + 14] = count;\n      uint16Array[stride2Offset + 15] = IS_LEAFNODE_FLAG;\n      return byteOffset + BYTES_PER_NODE;\n    } else {\n      const left = node.left;\n      const right = node.right;\n      const splitAxis = node.splitAxis;\n      let nextUnusedPointer;\n      nextUnusedPointer = populateBuffer(byteOffset + BYTES_PER_NODE, left);\n      if (nextUnusedPointer / 4 > Math.pow(2, 32)) {\n        throw new Error('MeshBVH: Cannot store child pointer greater than 32 bits.');\n      }\n      uint32Array[stride4Offset + 6] = nextUnusedPointer / 4;\n      nextUnusedPointer = populateBuffer(nextUnusedPointer, right);\n      uint32Array[stride4Offset + 7] = splitAxis;\n      return nextUnusedPointer;\n    }\n  }\n}", "map": {"version": 3, "names": ["BufferAttribute", "MeshBVHNode", "getLongestEdgeIndex", "computeSurfaceArea", "copyBounds", "unionBounds", "expandByTriangleBounds", "CENTER", "AVERAGE", "SAH", "TRIANGLE_INTERSECT_COST", "TRAVERSAL_COST", "BYTES_PER_NODE", "FLOAT32_EPSILON", "IS_LEAFNODE_FLAG", "ensureIndex", "geo", "options", "index", "vertexCount", "attributes", "position", "count", "BufferConstructor", "useSharedArrayBuffer", "SharedArrayBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Uint32Array", "Uint16Array", "setIndex", "i", "getRootIndexRanges", "groups", "length", "offset", "ranges", "rangeBoundaries", "Set", "group", "add", "start", "sortedBoundaries", "Array", "from", "values", "sort", "a", "b", "end", "push", "getBounds", "triangleBounds", "target", "centroid<PERSON>arget", "minx", "Infinity", "miny", "minz", "maxx", "maxy", "maxz", "cminx", "cminy", "cminz", "cmaxx", "cmaxy", "cmaxz", "includeCentroid", "cx", "hx", "lx", "rx", "cy", "hy", "ly", "ry", "cz", "hz", "lz", "rz", "getCentroidBounds", "partition", "split", "left", "right", "pos", "axisOffset", "axis", "t0", "t1", "t2", "BIN_COUNT", "binsSort", "candidate", "sahBins", "fill", "map", "bounds", "Float32Array", "rightCacheBounds", "leftCacheBounds", "leftBounds", "getOptimalSplit", "nodeBoundingData", "centroidBoundingData", "strategy", "getAverage", "rootSurfaceArea", "bestCost", "cStart", "cEnd", "axisLeft", "axisRight", "axisLength", "<PERSON><PERSON><PERSON><PERSON>", "truncatedBins", "c", "bin", "d", "splitCount", "bi", "splice", "center", "leftCount", "rightCount", "rightBounds", "leftProb", "rightProb", "cost", "triCenter", "relativeCenter", "binIndex", "lastBin", "nextBin", "binCount", "console", "warn", "avg", "computeTriangleBounds", "fullBounds", "posAttr", "array", "triCount", "normalized", "posArr", "bufferOffset", "stride", "isInterleavedBufferAttribute", "data", "getters", "tri", "tri3", "tri6", "ai", "ci", "el", "min", "max", "halfExtents", "el2", "Math", "abs", "buildTree", "triggerProgress", "trianglesProcessed", "onProgress", "totalTriangles", "splitNode", "node", "depth", "reachedMaxDepth", "max<PERSON><PERSON><PERSON>", "verbose", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "boundingData", "splitOffset", "indexArray", "splitAxis", "lstart", "lcount", "cacheCentroidBoundingData", "rstart", "rcount", "roots", "range", "root", "buildPackedTree", "float32Array", "uint32Array", "uint16Array", "packedRoots", "nodeCount", "countNodes", "buffer", "populate<PERSON><PERSON>er", "byteOffset", "stride4Offset", "stride2Offset", "<PERSON><PERSON><PERSON><PERSON>", "nextUnusedPointer", "pow", "Error"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/three-mesh-bvh/src/core/buildFunctions.js"], "sourcesContent": ["import { BufferAttribute } from 'three';\nimport { MeshBVHNode } from './MeshBVHNode.js';\nimport { getLongestEdgeIndex, computeSurfaceArea, copyBounds, unionBounds, expandByTriangleBounds } from '../utils/ArrayBoxUtilities.js';\nimport {\n\tCENTER, AVERAGE, SAH, TRIANGLE_INTERSECT_COST, TRAVERSAL_COST,\n\tBYTES_PER_NODE, FLOAT32_EPSILON, IS_LEAFNODE_FLAG,\n} from './Constants.js';\n\nfunction ensureIndex( geo, options ) {\n\n\tif ( ! geo.index ) {\n\n\t\tconst vertexCount = geo.attributes.position.count;\n\t\tconst BufferConstructor = options.useSharedArrayBuffer ? SharedArrayBuffer : ArrayBuffer;\n\t\tlet index;\n\t\tif ( vertexCount > 65535 ) {\n\n\t\t\tindex = new Uint32Array( new BufferConstructor( 4 * vertexCount ) );\n\n\t\t} else {\n\n\t\t\tindex = new Uint16Array( new BufferConstructor( 2 * vertexCount ) );\n\n\t\t}\n\n\t\tgeo.setIndex( new BufferAttribute( index, 1 ) );\n\n\t\tfor ( let i = 0; i < vertexCount; i ++ ) {\n\n\t\t\tindex[ i ] = i;\n\n\t\t}\n\n\t}\n\n}\n\n// Computes the set of { offset, count } ranges which need independent BVH roots. Each\n// region in the geometry index that belongs to a different set of material groups requires\n// a separate BVH root, so that triangles indices belonging to one group never get swapped\n// with triangle indices belongs to another group. For example, if the groups were like this:\n//\n// [-------------------------------------------------------------]\n// |__________________|\n//   g0 = [0, 20]  |______________________||_____________________|\n//                      g1 = [16, 40]           g2 = [41, 60]\n//\n// we would need four BVH roots: [0, 15], [16, 20], [21, 40], [41, 60].\nfunction getRootIndexRanges( geo ) {\n\n\tif ( ! geo.groups || ! geo.groups.length ) {\n\n\t\treturn [ { offset: 0, count: geo.index.count / 3 } ];\n\n\t}\n\n\tconst ranges = [];\n\tconst rangeBoundaries = new Set();\n\tfor ( const group of geo.groups ) {\n\n\t\trangeBoundaries.add( group.start );\n\t\trangeBoundaries.add( group.start + group.count );\n\n\t}\n\n\t// note that if you don't pass in a comparator, it sorts them lexicographically as strings :-(\n\tconst sortedBoundaries = Array.from( rangeBoundaries.values() ).sort( ( a, b ) => a - b );\n\tfor ( let i = 0; i < sortedBoundaries.length - 1; i ++ ) {\n\n\t\tconst start = sortedBoundaries[ i ], end = sortedBoundaries[ i + 1 ];\n\t\tranges.push( { offset: ( start / 3 ), count: ( end - start ) / 3 } );\n\n\t}\n\n\treturn ranges;\n\n}\n\n// computes the union of the bounds of all of the given triangles and puts the resulting box in target. If\n// centroidTarget is provided then a bounding box is computed for the centroids of the triangles, as well.\n// These are computed together to avoid redundant accesses to bounds array.\nfunction getBounds( triangleBounds, offset, count, target, centroidTarget = null ) {\n\n\tlet minx = Infinity;\n\tlet miny = Infinity;\n\tlet minz = Infinity;\n\tlet maxx = - Infinity;\n\tlet maxy = - Infinity;\n\tlet maxz = - Infinity;\n\n\tlet cminx = Infinity;\n\tlet cminy = Infinity;\n\tlet cminz = Infinity;\n\tlet cmaxx = - Infinity;\n\tlet cmaxy = - Infinity;\n\tlet cmaxz = - Infinity;\n\n\tconst includeCentroid = centroidTarget !== null;\n\tfor ( let i = offset * 6, end = ( offset + count ) * 6; i < end; i += 6 ) {\n\n\t\tconst cx = triangleBounds[ i + 0 ];\n\t\tconst hx = triangleBounds[ i + 1 ];\n\t\tconst lx = cx - hx;\n\t\tconst rx = cx + hx;\n\t\tif ( lx < minx ) minx = lx;\n\t\tif ( rx > maxx ) maxx = rx;\n\t\tif ( includeCentroid && cx < cminx ) cminx = cx;\n\t\tif ( includeCentroid && cx > cmaxx ) cmaxx = cx;\n\n\t\tconst cy = triangleBounds[ i + 2 ];\n\t\tconst hy = triangleBounds[ i + 3 ];\n\t\tconst ly = cy - hy;\n\t\tconst ry = cy + hy;\n\t\tif ( ly < miny ) miny = ly;\n\t\tif ( ry > maxy ) maxy = ry;\n\t\tif ( includeCentroid && cy < cminy ) cminy = cy;\n\t\tif ( includeCentroid && cy > cmaxy ) cmaxy = cy;\n\n\t\tconst cz = triangleBounds[ i + 4 ];\n\t\tconst hz = triangleBounds[ i + 5 ];\n\t\tconst lz = cz - hz;\n\t\tconst rz = cz + hz;\n\t\tif ( lz < minz ) minz = lz;\n\t\tif ( rz > maxz ) maxz = rz;\n\t\tif ( includeCentroid && cz < cminz ) cminz = cz;\n\t\tif ( includeCentroid && cz > cmaxz ) cmaxz = cz;\n\n\t}\n\n\ttarget[ 0 ] = minx;\n\ttarget[ 1 ] = miny;\n\ttarget[ 2 ] = minz;\n\n\ttarget[ 3 ] = maxx;\n\ttarget[ 4 ] = maxy;\n\ttarget[ 5 ] = maxz;\n\n\tif ( includeCentroid ) {\n\n\t\tcentroidTarget[ 0 ] = cminx;\n\t\tcentroidTarget[ 1 ] = cminy;\n\t\tcentroidTarget[ 2 ] = cminz;\n\n\t\tcentroidTarget[ 3 ] = cmaxx;\n\t\tcentroidTarget[ 4 ] = cmaxy;\n\t\tcentroidTarget[ 5 ] = cmaxz;\n\n\t}\n\n}\n\n// A stand alone function for retrieving the centroid bounds.\nfunction getCentroidBounds( triangleBounds, offset, count, centroidTarget ) {\n\n\tlet cminx = Infinity;\n\tlet cminy = Infinity;\n\tlet cminz = Infinity;\n\tlet cmaxx = - Infinity;\n\tlet cmaxy = - Infinity;\n\tlet cmaxz = - Infinity;\n\n\tfor ( let i = offset * 6, end = ( offset + count ) * 6; i < end; i += 6 ) {\n\n\t\tconst cx = triangleBounds[ i + 0 ];\n\t\tif ( cx < cminx ) cminx = cx;\n\t\tif ( cx > cmaxx ) cmaxx = cx;\n\n\t\tconst cy = triangleBounds[ i + 2 ];\n\t\tif ( cy < cminy ) cminy = cy;\n\t\tif ( cy > cmaxy ) cmaxy = cy;\n\n\t\tconst cz = triangleBounds[ i + 4 ];\n\t\tif ( cz < cminz ) cminz = cz;\n\t\tif ( cz > cmaxz ) cmaxz = cz;\n\n\t}\n\n\tcentroidTarget[ 0 ] = cminx;\n\tcentroidTarget[ 1 ] = cminy;\n\tcentroidTarget[ 2 ] = cminz;\n\n\tcentroidTarget[ 3 ] = cmaxx;\n\tcentroidTarget[ 4 ] = cmaxy;\n\tcentroidTarget[ 5 ] = cmaxz;\n\n}\n\n\n// reorders `tris` such that for `count` elements after `offset`, elements on the left side of the split\n// will be on the left and elements on the right side of the split will be on the right. returns the index\n// of the first element on the right side, or offset + count if there are no elements on the right side.\nfunction partition( index, triangleBounds, offset, count, split ) {\n\n\tlet left = offset;\n\tlet right = offset + count - 1;\n\tconst pos = split.pos;\n\tconst axisOffset = split.axis * 2;\n\n\t// hoare partitioning, see e.g. https://en.wikipedia.org/wiki/Quicksort#Hoare_partition_scheme\n\twhile ( true ) {\n\n\t\twhile ( left <= right && triangleBounds[ left * 6 + axisOffset ] < pos ) {\n\n\t\t\tleft ++;\n\n\t\t}\n\n\n\t\t// if a triangle center lies on the partition plane it is considered to be on the right side\n\t\twhile ( left <= right && triangleBounds[ right * 6 + axisOffset ] >= pos ) {\n\n\t\t\tright --;\n\n\t\t}\n\n\t\tif ( left < right ) {\n\n\t\t\t// we need to swap all of the information associated with the triangles at index\n\t\t\t// left and right; that's the verts in the geometry index, the bounds,\n\t\t\t// and perhaps the SAH planes\n\n\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\tlet t0 = index[ left * 3 + i ];\n\t\t\t\tindex[ left * 3 + i ] = index[ right * 3 + i ];\n\t\t\t\tindex[ right * 3 + i ] = t0;\n\n\t\t\t\tlet t1 = triangleBounds[ left * 6 + i * 2 + 0 ];\n\t\t\t\ttriangleBounds[ left * 6 + i * 2 + 0 ] = triangleBounds[ right * 6 + i * 2 + 0 ];\n\t\t\t\ttriangleBounds[ right * 6 + i * 2 + 0 ] = t1;\n\n\t\t\t\tlet t2 = triangleBounds[ left * 6 + i * 2 + 1 ];\n\t\t\t\ttriangleBounds[ left * 6 + i * 2 + 1 ] = triangleBounds[ right * 6 + i * 2 + 1 ];\n\t\t\t\ttriangleBounds[ right * 6 + i * 2 + 1 ] = t2;\n\n\t\t\t}\n\n\t\t\tleft ++;\n\t\t\tright --;\n\n\t\t} else {\n\n\t\t\treturn left;\n\n\t\t}\n\n\t}\n\n}\n\nconst BIN_COUNT = 32;\nconst binsSort = ( a, b ) => a.candidate - b.candidate;\nconst sahBins = new Array( BIN_COUNT ).fill().map( () => {\n\n\treturn {\n\n\t\tcount: 0,\n\t\tbounds: new Float32Array( 6 ),\n\t\trightCacheBounds: new Float32Array( 6 ),\n\t\tleftCacheBounds: new Float32Array( 6 ),\n\t\tcandidate: 0,\n\n\t};\n\n} );\nconst leftBounds = new Float32Array( 6 );\n\nfunction getOptimalSplit( nodeBoundingData, centroidBoundingData, triangleBounds, offset, count, strategy ) {\n\n\tlet axis = - 1;\n\tlet pos = 0;\n\n\t// Center\n\tif ( strategy === CENTER ) {\n\n\t\taxis = getLongestEdgeIndex( centroidBoundingData );\n\t\tif ( axis !== - 1 ) {\n\n\t\t\tpos = ( centroidBoundingData[ axis ] + centroidBoundingData[ axis + 3 ] ) / 2;\n\n\t\t}\n\n\t} else if ( strategy === AVERAGE ) {\n\n\t\taxis = getLongestEdgeIndex( nodeBoundingData );\n\t\tif ( axis !== - 1 ) {\n\n\t\t\tpos = getAverage( triangleBounds, offset, count, axis );\n\n\t\t}\n\n\t} else if ( strategy === SAH ) {\n\n\t\tconst rootSurfaceArea = computeSurfaceArea( nodeBoundingData );\n\t\tlet bestCost = TRIANGLE_INTERSECT_COST * count;\n\n\t\t// iterate over all axes\n\t\tconst cStart = offset * 6;\n\t\tconst cEnd = ( offset + count ) * 6;\n\t\tfor ( let a = 0; a < 3; a ++ ) {\n\n\t\t\tconst axisLeft = centroidBoundingData[ a ];\n\t\t\tconst axisRight = centroidBoundingData[ a + 3 ];\n\t\t\tconst axisLength = axisRight - axisLeft;\n\t\t\tconst binWidth = axisLength / BIN_COUNT;\n\n\t\t\t// If we have fewer triangles than we're planning to split then just check all\n\t\t\t// the triangle positions because it will be faster.\n\t\t\tif ( count < BIN_COUNT / 4 ) {\n\n\t\t\t\t// initialize the bin candidates\n\t\t\t\tconst truncatedBins = [ ...sahBins ];\n\t\t\t\ttruncatedBins.length = count;\n\n\t\t\t\t// set the candidates\n\t\t\t\tlet b = 0;\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6, b ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ b ];\n\t\t\t\t\tbin.candidate = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tbin.count = 0;\n\n\t\t\t\t\tconst {\n\t\t\t\t\t\tbounds,\n\t\t\t\t\t\tleftCacheBounds,\n\t\t\t\t\t\trightCacheBounds,\n\t\t\t\t\t} = bin;\n\t\t\t\t\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\t\t\t\t\trightCacheBounds[ d ] = Infinity;\n\t\t\t\t\t\trightCacheBounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t\tleftCacheBounds[ d ] = Infinity;\n\t\t\t\t\t\tleftCacheBounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t\tbounds[ d ] = Infinity;\n\t\t\t\t\t\tbounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t}\n\n\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bounds );\n\n\t\t\t\t}\n\n\t\t\t\ttruncatedBins.sort( binsSort );\n\n\t\t\t\t// remove redundant splits\n\t\t\t\tlet splitCount = count;\n\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\twhile ( bi + 1 < splitCount && truncatedBins[ bi + 1 ].candidate === bin.candidate ) {\n\n\t\t\t\t\t\ttruncatedBins.splice( bi + 1, 1 );\n\t\t\t\t\t\tsplitCount --;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// find the appropriate bin for each triangle and expand the bounds.\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6 ) {\n\n\t\t\t\t\tconst center = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\t\tif ( center >= bin.candidate ) {\n\n\t\t\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.rightCacheBounds );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.leftCacheBounds );\n\t\t\t\t\t\t\tbin.count ++;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// expand all the bounds\n\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\tconst leftCount = bin.count;\n\t\t\t\t\tconst rightCount = count - bin.count;\n\n\t\t\t\t\t// check the cost of this split\n\t\t\t\t\tconst leftBounds = bin.leftCacheBounds;\n\t\t\t\t\tconst rightBounds = bin.rightCacheBounds;\n\n\t\t\t\t\tlet leftProb = 0;\n\t\t\t\t\tif ( leftCount !== 0 ) {\n\n\t\t\t\t\t\tleftProb = computeSurfaceArea( leftBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tlet rightProb = 0;\n\t\t\t\t\tif ( rightCount !== 0 ) {\n\n\t\t\t\t\t\trightProb = computeSurfaceArea( rightBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst cost = TRAVERSAL_COST + TRIANGLE_INTERSECT_COST * (\n\t\t\t\t\t\tleftProb * leftCount + rightProb * rightCount\n\t\t\t\t\t);\n\n\t\t\t\t\tif ( cost < bestCost ) {\n\n\t\t\t\t\t\taxis = a;\n\t\t\t\t\t\tbestCost = cost;\n\t\t\t\t\t\tpos = bin.candidate;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\t// reset the bins\n\t\t\t\tfor ( let i = 0; i < BIN_COUNT; i ++ ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tbin.count = 0;\n\t\t\t\t\tbin.candidate = axisLeft + binWidth + i * binWidth;\n\n\t\t\t\t\tconst bounds = bin.bounds;\n\t\t\t\t\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\t\t\t\t\tbounds[ d ] = Infinity;\n\t\t\t\t\t\tbounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// iterate over all center positions\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6 ) {\n\n\t\t\t\t\tconst triCenter = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tconst relativeCenter = triCenter - axisLeft;\n\n\t\t\t\t\t// in the partition function if the centroid lies on the split plane then it is\n\t\t\t\t\t// considered to be on the right side of the split\n\t\t\t\t\tlet binIndex = ~ ~ ( relativeCenter / binWidth );\n\t\t\t\t\tif ( binIndex >= BIN_COUNT ) binIndex = BIN_COUNT - 1;\n\n\t\t\t\t\tconst bin = sahBins[ binIndex ];\n\t\t\t\t\tbin.count ++;\n\n\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.bounds );\n\n\t\t\t\t}\n\n\t\t\t\t// cache the unioned bounds from right to left so we don't have to regenerate them each time\n\t\t\t\tconst lastBin = sahBins[ BIN_COUNT - 1 ];\n\t\t\t\tcopyBounds( lastBin.bounds, lastBin.rightCacheBounds );\n\t\t\t\tfor ( let i = BIN_COUNT - 2; i >= 0; i -- ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tconst nextBin = sahBins[ i + 1 ];\n\t\t\t\t\tunionBounds( bin.bounds, nextBin.rightCacheBounds, bin.rightCacheBounds );\n\n\t\t\t\t}\n\n\t\t\t\tlet leftCount = 0;\n\t\t\t\tfor ( let i = 0; i < BIN_COUNT - 1; i ++ ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tconst binCount = bin.count;\n\t\t\t\t\tconst bounds = bin.bounds;\n\n\t\t\t\t\tconst nextBin = sahBins[ i + 1 ];\n\t\t\t\t\tconst rightBounds = nextBin.rightCacheBounds;\n\n\t\t\t\t\t// don't do anything with the bounds if the new bounds have no triangles\n\t\t\t\t\tif ( binCount !== 0 ) {\n\n\t\t\t\t\t\tif ( leftCount === 0 ) {\n\n\t\t\t\t\t\t\tcopyBounds( bounds, leftBounds );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\tunionBounds( bounds, leftBounds, leftBounds );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\tleftCount += binCount;\n\n\t\t\t\t\t// check the cost of this split\n\t\t\t\t\tlet leftProb = 0;\n\t\t\t\t\tlet rightProb = 0;\n\n\t\t\t\t\tif ( leftCount !== 0 ) {\n\n\t\t\t\t\t\tleftProb = computeSurfaceArea( leftBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst rightCount = count - leftCount;\n\t\t\t\t\tif ( rightCount !== 0 ) {\n\n\t\t\t\t\t\trightProb = computeSurfaceArea( rightBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst cost = TRAVERSAL_COST + TRIANGLE_INTERSECT_COST * (\n\t\t\t\t\t\tleftProb * leftCount + rightProb * rightCount\n\t\t\t\t\t);\n\n\t\t\t\t\tif ( cost < bestCost ) {\n\n\t\t\t\t\t\taxis = a;\n\t\t\t\t\t\tbestCost = cost;\n\t\t\t\t\t\tpos = bin.candidate;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t} else {\n\n\t\tconsole.warn( `MeshBVH: Invalid build strategy value ${ strategy } used.` );\n\n\t}\n\n\treturn { axis, pos };\n\n}\n\n// returns the average coordinate on the specified axis of the all the provided triangles\nfunction getAverage( triangleBounds, offset, count, axis ) {\n\n\tlet avg = 0;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tavg += triangleBounds[ i * 6 + axis * 2 ];\n\n\t}\n\n\treturn avg / count;\n\n}\n\n// precomputes the bounding box for each triangle; required for quickly calculating tree splits.\n// result is an array of size tris.length * 6 where triangle i maps to a\n// [x_center, x_delta, y_center, y_delta, z_center, z_delta] tuple starting at index i * 6,\n// representing the center and half-extent in each dimension of triangle i\nfunction computeTriangleBounds( geo, fullBounds ) {\n\n\tconst posAttr = geo.attributes.position;\n\tconst index = geo.index.array;\n\tconst triCount = index.length / 3;\n\tconst triangleBounds = new Float32Array( triCount * 6 );\n\tconst normalized = posAttr.normalized;\n\n\t// used for non-normalized positions\n\tconst posArr = posAttr.array;\n\n\t// support for an interleaved position buffer\n\tconst bufferOffset = posAttr.offset || 0;\n\tlet stride = 3;\n\tif ( posAttr.isInterleavedBufferAttribute ) {\n\n\t\tstride = posAttr.data.stride;\n\n\t}\n\n\t// used for normalized positions\n\tconst getters = [ 'getX', 'getY', 'getZ' ];\n\n\tfor ( let tri = 0; tri < triCount; tri ++ ) {\n\n\t\tconst tri3 = tri * 3;\n\t\tconst tri6 = tri * 6;\n\n\t\tlet ai, bi, ci;\n\n\t\tif ( normalized ) {\n\n\t\t\tai = index[ tri3 + 0 ];\n\t\t\tbi = index[ tri3 + 1 ];\n\t\t\tci = index[ tri3 + 2 ];\n\n\t\t} else {\n\n\t\t\tai = index[ tri3 + 0 ] * stride + bufferOffset;\n\t\t\tbi = index[ tri3 + 1 ] * stride + bufferOffset;\n\t\t\tci = index[ tri3 + 2 ] * stride + bufferOffset;\n\n\t\t}\n\n\t\tfor ( let el = 0; el < 3; el ++ ) {\n\n\t\t\tlet a, b, c;\n\n\t\t\tif ( normalized ) {\n\n\t\t\t\ta = posAttr[ getters[ el ] ]( ai );\n\t\t\t\tb = posAttr[ getters[ el ] ]( bi );\n\t\t\t\tc = posAttr[ getters[ el ] ]( ci );\n\n\t\t\t} else {\n\n\t\t\t\ta = posArr[ ai + el ];\n\t\t\t\tb = posArr[ bi + el ];\n\t\t\t\tc = posArr[ ci + el ];\n\n\t\t\t}\n\n\t\t\tlet min = a;\n\t\t\tif ( b < min ) min = b;\n\t\t\tif ( c < min ) min = c;\n\n\t\t\tlet max = a;\n\t\t\tif ( b > max ) max = b;\n\t\t\tif ( c > max ) max = c;\n\n\t\t\t// Increase the bounds size by float32 epsilon to avoid precision errors when\n\t\t\t// converting to 32 bit float. Scale the epsilon by the size of the numbers being\n\t\t\t// worked with.\n\t\t\tconst halfExtents = ( max - min ) / 2;\n\t\t\tconst el2 = el * 2;\n\t\t\ttriangleBounds[ tri6 + el2 + 0 ] = min + halfExtents;\n\t\t\ttriangleBounds[ tri6 + el2 + 1 ] = halfExtents + ( Math.abs( min ) + halfExtents ) * FLOAT32_EPSILON;\n\n\t\t\tif ( min < fullBounds[ el ] ) fullBounds[ el ] = min;\n\t\t\tif ( max > fullBounds[ el + 3 ] ) fullBounds[ el + 3 ] = max;\n\n\t\t}\n\n\t}\n\n\treturn triangleBounds;\n\n}\n\nexport function buildTree( geo, options ) {\n\n\tfunction triggerProgress( trianglesProcessed ) {\n\n\t\tif ( onProgress ) {\n\n\t\t\tonProgress( trianglesProcessed / totalTriangles );\n\n\t\t}\n\n\t}\n\n\t// either recursively splits the given node, creating left and right subtrees for it, or makes it a leaf node,\n\t// recording the offset and count of its triangles and writing them into the reordered geometry index.\n\tfunction splitNode( node, offset, count, centroidBoundingData = null, depth = 0 ) {\n\n\t\tif ( ! reachedMaxDepth && depth >= maxDepth ) {\n\n\t\t\treachedMaxDepth = true;\n\t\t\tif ( verbose ) {\n\n\t\t\t\tconsole.warn( `MeshBVH: Max depth of ${ maxDepth } reached when generating BVH. Consider increasing maxDepth.` );\n\t\t\t\tconsole.warn( geo );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// early out if we've met our capacity\n\t\tif ( count <= maxLeafTris || depth >= maxDepth ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\t\t\treturn node;\n\n\t\t}\n\n\t\t// Find where to split the volume\n\t\tconst split = getOptimalSplit( node.boundingData, centroidBoundingData, triangleBounds, offset, count, strategy );\n\t\tif ( split.axis === - 1 ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\t\t\treturn node;\n\n\t\t}\n\n\t\tconst splitOffset = partition( indexArray, triangleBounds, offset, count, split );\n\n\t\t// create the two new child nodes\n\t\tif ( splitOffset === offset || splitOffset === offset + count ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\n\t\t} else {\n\n\t\t\tnode.splitAxis = split.axis;\n\n\t\t\t// create the left child and compute its bounding box\n\t\t\tconst left = new MeshBVHNode();\n\t\t\tconst lstart = offset;\n\t\t\tconst lcount = splitOffset - offset;\n\t\t\tnode.left = left;\n\t\t\tleft.boundingData = new Float32Array( 6 );\n\n\t\t\tgetBounds( triangleBounds, lstart, lcount, left.boundingData, cacheCentroidBoundingData );\n\t\t\tsplitNode( left, lstart, lcount, cacheCentroidBoundingData, depth + 1 );\n\n\t\t\t// repeat for right\n\t\t\tconst right = new MeshBVHNode();\n\t\t\tconst rstart = splitOffset;\n\t\t\tconst rcount = count - lcount;\n\t\t\tnode.right = right;\n\t\t\tright.boundingData = new Float32Array( 6 );\n\n\t\t\tgetBounds( triangleBounds, rstart, rcount, right.boundingData, cacheCentroidBoundingData );\n\t\t\tsplitNode( right, rstart, rcount, cacheCentroidBoundingData, depth + 1 );\n\n\t\t}\n\n\t\treturn node;\n\n\t}\n\n\tensureIndex( geo, options );\n\n\t// Compute the full bounds of the geometry at the same time as triangle bounds because\n\t// we'll need it for the root bounds in the case with no groups and it should be fast here.\n\t// We can't use the geometrying bounding box if it's available because it may be out of date.\n\tconst fullBounds = new Float32Array( 6 );\n\tconst cacheCentroidBoundingData = new Float32Array( 6 );\n\tconst triangleBounds = computeTriangleBounds( geo, fullBounds );\n\tconst indexArray = geo.index.array;\n\tconst maxDepth = options.maxDepth;\n\tconst verbose = options.verbose;\n\tconst maxLeafTris = options.maxLeafTris;\n\tconst strategy = options.strategy;\n\tconst onProgress = options.onProgress;\n\tconst totalTriangles = geo.index.count / 3;\n\tlet reachedMaxDepth = false;\n\n\tconst roots = [];\n\tconst ranges = getRootIndexRanges( geo );\n\n\tif ( ranges.length === 1 ) {\n\n\t\tconst range = ranges[ 0 ];\n\t\tconst root = new MeshBVHNode();\n\t\troot.boundingData = fullBounds;\n\t\tgetCentroidBounds( triangleBounds, range.offset, range.count, cacheCentroidBoundingData );\n\n\t\tsplitNode( root, range.offset, range.count, cacheCentroidBoundingData );\n\t\troots.push( root );\n\n\t} else {\n\n\t\tfor ( let range of ranges ) {\n\n\t\t\tconst root = new MeshBVHNode();\n\t\t\troot.boundingData = new Float32Array( 6 );\n\t\t\tgetBounds( triangleBounds, range.offset, range.count, root.boundingData, cacheCentroidBoundingData );\n\n\t\t\tsplitNode( root, range.offset, range.count, cacheCentroidBoundingData );\n\t\t\troots.push( root );\n\n\t\t}\n\n\t}\n\n\treturn roots;\n\n}\n\nexport function buildPackedTree( geo, options ) {\n\n\t// boundingData  \t\t\t\t: 6 float32\n\t// right / offset \t\t\t\t: 1 uint32\n\t// splitAxis / isLeaf + count \t: 1 uint32 / 2 uint16\n\tconst roots = buildTree( geo, options );\n\n\tlet float32Array;\n\tlet uint32Array;\n\tlet uint16Array;\n\tconst packedRoots = [];\n\tconst BufferConstructor = options.useSharedArrayBuffer ? SharedArrayBuffer : ArrayBuffer;\n\tfor ( let i = 0; i < roots.length; i ++ ) {\n\n\t\tconst root = roots[ i ];\n\t\tlet nodeCount = countNodes( root );\n\n\t\tconst buffer = new BufferConstructor( BYTES_PER_NODE * nodeCount );\n\t\tfloat32Array = new Float32Array( buffer );\n\t\tuint32Array = new Uint32Array( buffer );\n\t\tuint16Array = new Uint16Array( buffer );\n\t\tpopulateBuffer( 0, root );\n\t\tpackedRoots.push( buffer );\n\n\t}\n\n\treturn packedRoots;\n\n\tfunction countNodes( node ) {\n\n\t\tif ( node.count ) {\n\n\t\t\treturn 1;\n\n\t\t} else {\n\n\t\t\treturn 1 + countNodes( node.left ) + countNodes( node.right );\n\n\t\t}\n\n\t}\n\n\tfunction populateBuffer( byteOffset, node ) {\n\n\t\tconst stride4Offset = byteOffset / 4;\n\t\tconst stride2Offset = byteOffset / 2;\n\t\tconst isLeaf = ! ! node.count;\n\t\tconst boundingData = node.boundingData;\n\t\tfor ( let i = 0; i < 6; i ++ ) {\n\n\t\t\tfloat32Array[ stride4Offset + i ] = boundingData[ i ];\n\n\t\t}\n\n\t\tif ( isLeaf ) {\n\n\t\t\tconst offset = node.offset;\n\t\t\tconst count = node.count;\n\t\t\tuint32Array[ stride4Offset + 6 ] = offset;\n\t\t\tuint16Array[ stride2Offset + 14 ] = count;\n\t\t\tuint16Array[ stride2Offset + 15 ] = IS_LEAFNODE_FLAG;\n\t\t\treturn byteOffset + BYTES_PER_NODE;\n\n\t\t} else {\n\n\t\t\tconst left = node.left;\n\t\t\tconst right = node.right;\n\t\t\tconst splitAxis = node.splitAxis;\n\n\t\t\tlet nextUnusedPointer;\n\t\t\tnextUnusedPointer = populateBuffer( byteOffset + BYTES_PER_NODE, left );\n\n\t\t\tif ( ( nextUnusedPointer / 4 ) > Math.pow( 2, 32 ) ) {\n\n\t\t\t\tthrow new Error( 'MeshBVH: Cannot store child pointer greater than 32 bits.' );\n\n\t\t\t}\n\n\t\t\tuint32Array[ stride4Offset + 6 ] = nextUnusedPointer / 4;\n\t\t\tnextUnusedPointer = populateBuffer( nextUnusedPointer, right );\n\n\t\t\tuint32Array[ stride4Offset + 7 ] = splitAxis;\n\t\t\treturn nextUnusedPointer;\n\n\t\t}\n\n\t}\n\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,mBAAmB,EAAEC,kBAAkB,EAAEC,UAAU,EAAEC,WAAW,EAAEC,sBAAsB,QAAQ,+BAA+B;AACxI,SACCC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,uBAAuB,EAAEC,cAAc,EAC7DC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,QAC3C,gBAAgB;AAEvB,SAASC,WAAWA,CAAEC,GAAG,EAAEC,OAAO,EAAG;EAEpC,IAAK,CAAED,GAAG,CAACE,KAAK,EAAG;IAElB,MAAMC,WAAW,GAAGH,GAAG,CAACI,UAAU,CAACC,QAAQ,CAACC,KAAK;IACjD,MAAMC,iBAAiB,GAAGN,OAAO,CAACO,oBAAoB,GAAGC,iBAAiB,GAAGC,WAAW;IACxF,IAAIR,KAAK;IACT,IAAKC,WAAW,GAAG,KAAK,EAAG;MAE1BD,KAAK,GAAG,IAAIS,WAAW,CAAE,IAAIJ,iBAAiB,CAAE,CAAC,GAAGJ,WAAY,CAAE,CAAC;IAEpE,CAAC,MAAM;MAEND,KAAK,GAAG,IAAIU,WAAW,CAAE,IAAIL,iBAAiB,CAAE,CAAC,GAAGJ,WAAY,CAAE,CAAC;IAEpE;IAEAH,GAAG,CAACa,QAAQ,CAAE,IAAI7B,eAAe,CAAEkB,KAAK,EAAE,CAAE,CAAE,CAAC;IAE/C,KAAM,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,WAAW,EAAEW,CAAC,EAAG,EAAG;MAExCZ,KAAK,CAAEY,CAAC,CAAE,GAAGA,CAAC;IAEf;EAED;AAED;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAAEf,GAAG,EAAG;EAElC,IAAK,CAAEA,GAAG,CAACgB,MAAM,IAAI,CAAEhB,GAAG,CAACgB,MAAM,CAACC,MAAM,EAAG;IAE1C,OAAO,CAAE;MAAEC,MAAM,EAAE,CAAC;MAAEZ,KAAK,EAAEN,GAAG,CAACE,KAAK,CAACI,KAAK,GAAG;IAAE,CAAC,CAAE;EAErD;EAEA,MAAMa,MAAM,GAAG,EAAE;EACjB,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;EACjC,KAAM,MAAMC,KAAK,IAAItB,GAAG,CAACgB,MAAM,EAAG;IAEjCI,eAAe,CAACG,GAAG,CAAED,KAAK,CAACE,KAAM,CAAC;IAClCJ,eAAe,CAACG,GAAG,CAAED,KAAK,CAACE,KAAK,GAAGF,KAAK,CAAChB,KAAM,CAAC;EAEjD;;EAEA;EACA,MAAMmB,gBAAgB,GAAGC,KAAK,CAACC,IAAI,CAAEP,eAAe,CAACQ,MAAM,CAAC,CAAE,CAAC,CAACC,IAAI,CAAE,CAAEC,CAAC,EAAEC,CAAC,KAAMD,CAAC,GAAGC,CAAE,CAAC;EACzF,KAAM,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,gBAAgB,CAACR,MAAM,GAAG,CAAC,EAAEH,CAAC,EAAG,EAAG;IAExD,MAAMU,KAAK,GAAGC,gBAAgB,CAAEX,CAAC,CAAE;MAAEkB,GAAG,GAAGP,gBAAgB,CAAEX,CAAC,GAAG,CAAC,CAAE;IACpEK,MAAM,CAACc,IAAI,CAAE;MAAEf,MAAM,EAAIM,KAAK,GAAG,CAAG;MAAElB,KAAK,EAAE,CAAE0B,GAAG,GAAGR,KAAK,IAAK;IAAE,CAAE,CAAC;EAErE;EAEA,OAAOL,MAAM;AAEd;;AAEA;AACA;AACA;AACA,SAASe,SAASA,CAAEC,cAAc,EAAEjB,MAAM,EAAEZ,KAAK,EAAE8B,MAAM,EAAEC,cAAc,GAAG,IAAI,EAAG;EAElF,IAAIC,IAAI,GAAGC,QAAQ;EACnB,IAAIC,IAAI,GAAGD,QAAQ;EACnB,IAAIE,IAAI,GAAGF,QAAQ;EACnB,IAAIG,IAAI,GAAG,CAAEH,QAAQ;EACrB,IAAII,IAAI,GAAG,CAAEJ,QAAQ;EACrB,IAAIK,IAAI,GAAG,CAAEL,QAAQ;EAErB,IAAIM,KAAK,GAAGN,QAAQ;EACpB,IAAIO,KAAK,GAAGP,QAAQ;EACpB,IAAIQ,KAAK,GAAGR,QAAQ;EACpB,IAAIS,KAAK,GAAG,CAAET,QAAQ;EACtB,IAAIU,KAAK,GAAG,CAAEV,QAAQ;EACtB,IAAIW,KAAK,GAAG,CAAEX,QAAQ;EAEtB,MAAMY,eAAe,GAAGd,cAAc,KAAK,IAAI;EAC/C,KAAM,IAAIvB,CAAC,GAAGI,MAAM,GAAG,CAAC,EAAEc,GAAG,GAAG,CAAEd,MAAM,GAAGZ,KAAK,IAAK,CAAC,EAAEQ,CAAC,GAAGkB,GAAG,EAAElB,CAAC,IAAI,CAAC,EAAG;IAEzE,MAAMsC,EAAE,GAAGjB,cAAc,CAAErB,CAAC,GAAG,CAAC,CAAE;IAClC,MAAMuC,EAAE,GAAGlB,cAAc,CAAErB,CAAC,GAAG,CAAC,CAAE;IAClC,MAAMwC,EAAE,GAAGF,EAAE,GAAGC,EAAE;IAClB,MAAME,EAAE,GAAGH,EAAE,GAAGC,EAAE;IAClB,IAAKC,EAAE,GAAGhB,IAAI,EAAGA,IAAI,GAAGgB,EAAE;IAC1B,IAAKC,EAAE,GAAGb,IAAI,EAAGA,IAAI,GAAGa,EAAE;IAC1B,IAAKJ,eAAe,IAAIC,EAAE,GAAGP,KAAK,EAAGA,KAAK,GAAGO,EAAE;IAC/C,IAAKD,eAAe,IAAIC,EAAE,GAAGJ,KAAK,EAAGA,KAAK,GAAGI,EAAE;IAE/C,MAAMI,EAAE,GAAGrB,cAAc,CAAErB,CAAC,GAAG,CAAC,CAAE;IAClC,MAAM2C,EAAE,GAAGtB,cAAc,CAAErB,CAAC,GAAG,CAAC,CAAE;IAClC,MAAM4C,EAAE,GAAGF,EAAE,GAAGC,EAAE;IAClB,MAAME,EAAE,GAAGH,EAAE,GAAGC,EAAE;IAClB,IAAKC,EAAE,GAAGlB,IAAI,EAAGA,IAAI,GAAGkB,EAAE;IAC1B,IAAKC,EAAE,GAAGhB,IAAI,EAAGA,IAAI,GAAGgB,EAAE;IAC1B,IAAKR,eAAe,IAAIK,EAAE,GAAGV,KAAK,EAAGA,KAAK,GAAGU,EAAE;IAC/C,IAAKL,eAAe,IAAIK,EAAE,GAAGP,KAAK,EAAGA,KAAK,GAAGO,EAAE;IAE/C,MAAMI,EAAE,GAAGzB,cAAc,CAAErB,CAAC,GAAG,CAAC,CAAE;IAClC,MAAM+C,EAAE,GAAG1B,cAAc,CAAErB,CAAC,GAAG,CAAC,CAAE;IAClC,MAAMgD,EAAE,GAAGF,EAAE,GAAGC,EAAE;IAClB,MAAME,EAAE,GAAGH,EAAE,GAAGC,EAAE;IAClB,IAAKC,EAAE,GAAGrB,IAAI,EAAGA,IAAI,GAAGqB,EAAE;IAC1B,IAAKC,EAAE,GAAGnB,IAAI,EAAGA,IAAI,GAAGmB,EAAE;IAC1B,IAAKZ,eAAe,IAAIS,EAAE,GAAGb,KAAK,EAAGA,KAAK,GAAGa,EAAE;IAC/C,IAAKT,eAAe,IAAIS,EAAE,GAAGV,KAAK,EAAGA,KAAK,GAAGU,EAAE;EAEhD;EAEAxB,MAAM,CAAE,CAAC,CAAE,GAAGE,IAAI;EAClBF,MAAM,CAAE,CAAC,CAAE,GAAGI,IAAI;EAClBJ,MAAM,CAAE,CAAC,CAAE,GAAGK,IAAI;EAElBL,MAAM,CAAE,CAAC,CAAE,GAAGM,IAAI;EAClBN,MAAM,CAAE,CAAC,CAAE,GAAGO,IAAI;EAClBP,MAAM,CAAE,CAAC,CAAE,GAAGQ,IAAI;EAElB,IAAKO,eAAe,EAAG;IAEtBd,cAAc,CAAE,CAAC,CAAE,GAAGQ,KAAK;IAC3BR,cAAc,CAAE,CAAC,CAAE,GAAGS,KAAK;IAC3BT,cAAc,CAAE,CAAC,CAAE,GAAGU,KAAK;IAE3BV,cAAc,CAAE,CAAC,CAAE,GAAGW,KAAK;IAC3BX,cAAc,CAAE,CAAC,CAAE,GAAGY,KAAK;IAC3BZ,cAAc,CAAE,CAAC,CAAE,GAAGa,KAAK;EAE5B;AAED;;AAEA;AACA,SAASc,iBAAiBA,CAAE7B,cAAc,EAAEjB,MAAM,EAAEZ,KAAK,EAAE+B,cAAc,EAAG;EAE3E,IAAIQ,KAAK,GAAGN,QAAQ;EACpB,IAAIO,KAAK,GAAGP,QAAQ;EACpB,IAAIQ,KAAK,GAAGR,QAAQ;EACpB,IAAIS,KAAK,GAAG,CAAET,QAAQ;EACtB,IAAIU,KAAK,GAAG,CAAEV,QAAQ;EACtB,IAAIW,KAAK,GAAG,CAAEX,QAAQ;EAEtB,KAAM,IAAIzB,CAAC,GAAGI,MAAM,GAAG,CAAC,EAAEc,GAAG,GAAG,CAAEd,MAAM,GAAGZ,KAAK,IAAK,CAAC,EAAEQ,CAAC,GAAGkB,GAAG,EAAElB,CAAC,IAAI,CAAC,EAAG;IAEzE,MAAMsC,EAAE,GAAGjB,cAAc,CAAErB,CAAC,GAAG,CAAC,CAAE;IAClC,IAAKsC,EAAE,GAAGP,KAAK,EAAGA,KAAK,GAAGO,EAAE;IAC5B,IAAKA,EAAE,GAAGJ,KAAK,EAAGA,KAAK,GAAGI,EAAE;IAE5B,MAAMI,EAAE,GAAGrB,cAAc,CAAErB,CAAC,GAAG,CAAC,CAAE;IAClC,IAAK0C,EAAE,GAAGV,KAAK,EAAGA,KAAK,GAAGU,EAAE;IAC5B,IAAKA,EAAE,GAAGP,KAAK,EAAGA,KAAK,GAAGO,EAAE;IAE5B,MAAMI,EAAE,GAAGzB,cAAc,CAAErB,CAAC,GAAG,CAAC,CAAE;IAClC,IAAK8C,EAAE,GAAGb,KAAK,EAAGA,KAAK,GAAGa,EAAE;IAC5B,IAAKA,EAAE,GAAGV,KAAK,EAAGA,KAAK,GAAGU,EAAE;EAE7B;EAEAvB,cAAc,CAAE,CAAC,CAAE,GAAGQ,KAAK;EAC3BR,cAAc,CAAE,CAAC,CAAE,GAAGS,KAAK;EAC3BT,cAAc,CAAE,CAAC,CAAE,GAAGU,KAAK;EAE3BV,cAAc,CAAE,CAAC,CAAE,GAAGW,KAAK;EAC3BX,cAAc,CAAE,CAAC,CAAE,GAAGY,KAAK;EAC3BZ,cAAc,CAAE,CAAC,CAAE,GAAGa,KAAK;AAE5B;;AAGA;AACA;AACA;AACA,SAASe,SAASA,CAAE/D,KAAK,EAAEiC,cAAc,EAAEjB,MAAM,EAAEZ,KAAK,EAAE4D,KAAK,EAAG;EAEjE,IAAIC,IAAI,GAAGjD,MAAM;EACjB,IAAIkD,KAAK,GAAGlD,MAAM,GAAGZ,KAAK,GAAG,CAAC;EAC9B,MAAM+D,GAAG,GAAGH,KAAK,CAACG,GAAG;EACrB,MAAMC,UAAU,GAAGJ,KAAK,CAACK,IAAI,GAAG,CAAC;;EAEjC;EACA,OAAQ,IAAI,EAAG;IAEd,OAAQJ,IAAI,IAAIC,KAAK,IAAIjC,cAAc,CAAEgC,IAAI,GAAG,CAAC,GAAGG,UAAU,CAAE,GAAGD,GAAG,EAAG;MAExEF,IAAI,EAAG;IAER;;IAGA;IACA,OAAQA,IAAI,IAAIC,KAAK,IAAIjC,cAAc,CAAEiC,KAAK,GAAG,CAAC,GAAGE,UAAU,CAAE,IAAID,GAAG,EAAG;MAE1ED,KAAK,EAAG;IAET;IAEA,IAAKD,IAAI,GAAGC,KAAK,EAAG;MAEnB;MACA;MACA;;MAEA,KAAM,IAAItD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;QAE9B,IAAI0D,EAAE,GAAGtE,KAAK,CAAEiE,IAAI,GAAG,CAAC,GAAGrD,CAAC,CAAE;QAC9BZ,KAAK,CAAEiE,IAAI,GAAG,CAAC,GAAGrD,CAAC,CAAE,GAAGZ,KAAK,CAAEkE,KAAK,GAAG,CAAC,GAAGtD,CAAC,CAAE;QAC9CZ,KAAK,CAAEkE,KAAK,GAAG,CAAC,GAAGtD,CAAC,CAAE,GAAG0D,EAAE;QAE3B,IAAIC,EAAE,GAAGtC,cAAc,CAAEgC,IAAI,GAAG,CAAC,GAAGrD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE;QAC/CqB,cAAc,CAAEgC,IAAI,GAAG,CAAC,GAAGrD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAGqB,cAAc,CAAEiC,KAAK,GAAG,CAAC,GAAGtD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE;QAChFqB,cAAc,CAAEiC,KAAK,GAAG,CAAC,GAAGtD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAG2D,EAAE;QAE5C,IAAIC,EAAE,GAAGvC,cAAc,CAAEgC,IAAI,GAAG,CAAC,GAAGrD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE;QAC/CqB,cAAc,CAAEgC,IAAI,GAAG,CAAC,GAAGrD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAGqB,cAAc,CAAEiC,KAAK,GAAG,CAAC,GAAGtD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE;QAChFqB,cAAc,CAAEiC,KAAK,GAAG,CAAC,GAAGtD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAG4D,EAAE;MAE7C;MAEAP,IAAI,EAAG;MACPC,KAAK,EAAG;IAET,CAAC,MAAM;MAEN,OAAOD,IAAI;IAEZ;EAED;AAED;AAEA,MAAMQ,SAAS,GAAG,EAAE;AACpB,MAAMC,QAAQ,GAAGA,CAAE9C,CAAC,EAAEC,CAAC,KAAMD,CAAC,CAAC+C,SAAS,GAAG9C,CAAC,CAAC8C,SAAS;AACtD,MAAMC,OAAO,GAAG,IAAIpD,KAAK,CAAEiD,SAAU,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,GAAG,CAAE,MAAM;EAExD,OAAO;IAEN1E,KAAK,EAAE,CAAC;IACR2E,MAAM,EAAE,IAAIC,YAAY,CAAE,CAAE,CAAC;IAC7BC,gBAAgB,EAAE,IAAID,YAAY,CAAE,CAAE,CAAC;IACvCE,eAAe,EAAE,IAAIF,YAAY,CAAE,CAAE,CAAC;IACtCL,SAAS,EAAE;EAEZ,CAAC;AAEF,CAAE,CAAC;AACH,MAAMQ,UAAU,GAAG,IAAIH,YAAY,CAAE,CAAE,CAAC;AAExC,SAASI,eAAeA,CAAEC,gBAAgB,EAAEC,oBAAoB,EAAErD,cAAc,EAAEjB,MAAM,EAAEZ,KAAK,EAAEmF,QAAQ,EAAG;EAE3G,IAAIlB,IAAI,GAAG,CAAE,CAAC;EACd,IAAIF,GAAG,GAAG,CAAC;;EAEX;EACA,IAAKoB,QAAQ,KAAKlG,MAAM,EAAG;IAE1BgF,IAAI,GAAGrF,mBAAmB,CAAEsG,oBAAqB,CAAC;IAClD,IAAKjB,IAAI,KAAK,CAAE,CAAC,EAAG;MAEnBF,GAAG,GAAG,CAAEmB,oBAAoB,CAAEjB,IAAI,CAAE,GAAGiB,oBAAoB,CAAEjB,IAAI,GAAG,CAAC,CAAE,IAAK,CAAC;IAE9E;EAED,CAAC,MAAM,IAAKkB,QAAQ,KAAKjG,OAAO,EAAG;IAElC+E,IAAI,GAAGrF,mBAAmB,CAAEqG,gBAAiB,CAAC;IAC9C,IAAKhB,IAAI,KAAK,CAAE,CAAC,EAAG;MAEnBF,GAAG,GAAGqB,UAAU,CAAEvD,cAAc,EAAEjB,MAAM,EAAEZ,KAAK,EAAEiE,IAAK,CAAC;IAExD;EAED,CAAC,MAAM,IAAKkB,QAAQ,KAAKhG,GAAG,EAAG;IAE9B,MAAMkG,eAAe,GAAGxG,kBAAkB,CAAEoG,gBAAiB,CAAC;IAC9D,IAAIK,QAAQ,GAAGlG,uBAAuB,GAAGY,KAAK;;IAE9C;IACA,MAAMuF,MAAM,GAAG3E,MAAM,GAAG,CAAC;IACzB,MAAM4E,IAAI,GAAG,CAAE5E,MAAM,GAAGZ,KAAK,IAAK,CAAC;IACnC,KAAM,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B,MAAMiE,QAAQ,GAAGP,oBAAoB,CAAE1D,CAAC,CAAE;MAC1C,MAAMkE,SAAS,GAAGR,oBAAoB,CAAE1D,CAAC,GAAG,CAAC,CAAE;MAC/C,MAAMmE,UAAU,GAAGD,SAAS,GAAGD,QAAQ;MACvC,MAAMG,QAAQ,GAAGD,UAAU,GAAGtB,SAAS;;MAEvC;MACA;MACA,IAAKrE,KAAK,GAAGqE,SAAS,GAAG,CAAC,EAAG;QAE5B;QACA,MAAMwB,aAAa,GAAG,CAAE,GAAGrB,OAAO,CAAE;QACpCqB,aAAa,CAAClF,MAAM,GAAGX,KAAK;;QAE5B;QACA,IAAIyB,CAAC,GAAG,CAAC;QACT,KAAM,IAAIqE,CAAC,GAAGP,MAAM,EAAEO,CAAC,GAAGN,IAAI,EAAEM,CAAC,IAAI,CAAC,EAAErE,CAAC,EAAG,EAAG;UAE9C,MAAMsE,GAAG,GAAGF,aAAa,CAAEpE,CAAC,CAAE;UAC9BsE,GAAG,CAACxB,SAAS,GAAG1C,cAAc,CAAEiE,CAAC,GAAG,CAAC,GAAGtE,CAAC,CAAE;UAC3CuE,GAAG,CAAC/F,KAAK,GAAG,CAAC;UAEb,MAAM;YACL2E,MAAM;YACNG,eAAe;YACfD;UACD,CAAC,GAAGkB,GAAG;UACP,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;YAE9BnB,gBAAgB,CAAEmB,CAAC,CAAE,GAAG/D,QAAQ;YAChC4C,gBAAgB,CAAEmB,CAAC,GAAG,CAAC,CAAE,GAAG,CAAE/D,QAAQ;YAEtC6C,eAAe,CAAEkB,CAAC,CAAE,GAAG/D,QAAQ;YAC/B6C,eAAe,CAAEkB,CAAC,GAAG,CAAC,CAAE,GAAG,CAAE/D,QAAQ;YAErC0C,MAAM,CAAEqB,CAAC,CAAE,GAAG/D,QAAQ;YACtB0C,MAAM,CAAEqB,CAAC,GAAG,CAAC,CAAE,GAAG,CAAE/D,QAAQ;UAE7B;UAEAjD,sBAAsB,CAAE8G,CAAC,EAAEjE,cAAc,EAAE8C,MAAO,CAAC;QAEpD;QAEAkB,aAAa,CAACtE,IAAI,CAAE+C,QAAS,CAAC;;QAE9B;QACA,IAAI2B,UAAU,GAAGjG,KAAK;QACtB,KAAM,IAAIkG,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGD,UAAU,EAAEC,EAAE,EAAG,EAAG;UAE1C,MAAMH,GAAG,GAAGF,aAAa,CAAEK,EAAE,CAAE;UAC/B,OAAQA,EAAE,GAAG,CAAC,GAAGD,UAAU,IAAIJ,aAAa,CAAEK,EAAE,GAAG,CAAC,CAAE,CAAC3B,SAAS,KAAKwB,GAAG,CAACxB,SAAS,EAAG;YAEpFsB,aAAa,CAACM,MAAM,CAAED,EAAE,GAAG,CAAC,EAAE,CAAE,CAAC;YACjCD,UAAU,EAAG;UAEd;QAED;;QAEA;QACA,KAAM,IAAIH,CAAC,GAAGP,MAAM,EAAEO,CAAC,GAAGN,IAAI,EAAEM,CAAC,IAAI,CAAC,EAAG;UAExC,MAAMM,MAAM,GAAGvE,cAAc,CAAEiE,CAAC,GAAG,CAAC,GAAGtE,CAAC,CAAE;UAC1C,KAAM,IAAI0E,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGD,UAAU,EAAEC,EAAE,EAAG,EAAG;YAE1C,MAAMH,GAAG,GAAGF,aAAa,CAAEK,EAAE,CAAE;YAC/B,IAAKE,MAAM,IAAIL,GAAG,CAACxB,SAAS,EAAG;cAE9BvF,sBAAsB,CAAE8G,CAAC,EAAEjE,cAAc,EAAEkE,GAAG,CAAClB,gBAAiB,CAAC;YAElE,CAAC,MAAM;cAEN7F,sBAAsB,CAAE8G,CAAC,EAAEjE,cAAc,EAAEkE,GAAG,CAACjB,eAAgB,CAAC;cAChEiB,GAAG,CAAC/F,KAAK,EAAG;YAEb;UAED;QAED;;QAEA;QACA,KAAM,IAAIkG,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGD,UAAU,EAAEC,EAAE,EAAG,EAAG;UAE1C,MAAMH,GAAG,GAAGF,aAAa,CAAEK,EAAE,CAAE;UAC/B,MAAMG,SAAS,GAAGN,GAAG,CAAC/F,KAAK;UAC3B,MAAMsG,UAAU,GAAGtG,KAAK,GAAG+F,GAAG,CAAC/F,KAAK;;UAEpC;UACA,MAAM+E,UAAU,GAAGgB,GAAG,CAACjB,eAAe;UACtC,MAAMyB,WAAW,GAAGR,GAAG,CAAClB,gBAAgB;UAExC,IAAI2B,QAAQ,GAAG,CAAC;UAChB,IAAKH,SAAS,KAAK,CAAC,EAAG;YAEtBG,QAAQ,GAAG3H,kBAAkB,CAAEkG,UAAW,CAAC,GAAGM,eAAe;UAE9D;UAEA,IAAIoB,SAAS,GAAG,CAAC;UACjB,IAAKH,UAAU,KAAK,CAAC,EAAG;YAEvBG,SAAS,GAAG5H,kBAAkB,CAAE0H,WAAY,CAAC,GAAGlB,eAAe;UAEhE;UAEA,MAAMqB,IAAI,GAAGrH,cAAc,GAAGD,uBAAuB,IACpDoH,QAAQ,GAAGH,SAAS,GAAGI,SAAS,GAAGH,UAAU,CAC7C;UAED,IAAKI,IAAI,GAAGpB,QAAQ,EAAG;YAEtBrB,IAAI,GAAGzC,CAAC;YACR8D,QAAQ,GAAGoB,IAAI;YACf3C,GAAG,GAAGgC,GAAG,CAACxB,SAAS;UAEpB;QAED;MAED,CAAC,MAAM;QAEN;QACA,KAAM,IAAI/D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6D,SAAS,EAAE7D,CAAC,EAAG,EAAG;UAEtC,MAAMuF,GAAG,GAAGvB,OAAO,CAAEhE,CAAC,CAAE;UACxBuF,GAAG,CAAC/F,KAAK,GAAG,CAAC;UACb+F,GAAG,CAACxB,SAAS,GAAGkB,QAAQ,GAAGG,QAAQ,GAAGpF,CAAC,GAAGoF,QAAQ;UAElD,MAAMjB,MAAM,GAAGoB,GAAG,CAACpB,MAAM;UACzB,KAAM,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;YAE9BrB,MAAM,CAAEqB,CAAC,CAAE,GAAG/D,QAAQ;YACtB0C,MAAM,CAAEqB,CAAC,GAAG,CAAC,CAAE,GAAG,CAAE/D,QAAQ;UAE7B;QAED;;QAEA;QACA,KAAM,IAAI6D,CAAC,GAAGP,MAAM,EAAEO,CAAC,GAAGN,IAAI,EAAEM,CAAC,IAAI,CAAC,EAAG;UAExC,MAAMa,SAAS,GAAG9E,cAAc,CAAEiE,CAAC,GAAG,CAAC,GAAGtE,CAAC,CAAE;UAC7C,MAAMoF,cAAc,GAAGD,SAAS,GAAGlB,QAAQ;;UAE3C;UACA;UACA,IAAIoB,QAAQ,GAAG,CAAE,EAAID,cAAc,GAAGhB,QAAQ,CAAE;UAChD,IAAKiB,QAAQ,IAAIxC,SAAS,EAAGwC,QAAQ,GAAGxC,SAAS,GAAG,CAAC;UAErD,MAAM0B,GAAG,GAAGvB,OAAO,CAAEqC,QAAQ,CAAE;UAC/Bd,GAAG,CAAC/F,KAAK,EAAG;UAEZhB,sBAAsB,CAAE8G,CAAC,EAAEjE,cAAc,EAAEkE,GAAG,CAACpB,MAAO,CAAC;QAExD;;QAEA;QACA,MAAMmC,OAAO,GAAGtC,OAAO,CAAEH,SAAS,GAAG,CAAC,CAAE;QACxCvF,UAAU,CAAEgI,OAAO,CAACnC,MAAM,EAAEmC,OAAO,CAACjC,gBAAiB,CAAC;QACtD,KAAM,IAAIrE,CAAC,GAAG6D,SAAS,GAAG,CAAC,EAAE7D,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAG,EAAG;UAE3C,MAAMuF,GAAG,GAAGvB,OAAO,CAAEhE,CAAC,CAAE;UACxB,MAAMuG,OAAO,GAAGvC,OAAO,CAAEhE,CAAC,GAAG,CAAC,CAAE;UAChCzB,WAAW,CAAEgH,GAAG,CAACpB,MAAM,EAAEoC,OAAO,CAAClC,gBAAgB,EAAEkB,GAAG,CAAClB,gBAAiB,CAAC;QAE1E;QAEA,IAAIwB,SAAS,GAAG,CAAC;QACjB,KAAM,IAAI7F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6D,SAAS,GAAG,CAAC,EAAE7D,CAAC,EAAG,EAAG;UAE1C,MAAMuF,GAAG,GAAGvB,OAAO,CAAEhE,CAAC,CAAE;UACxB,MAAMwG,QAAQ,GAAGjB,GAAG,CAAC/F,KAAK;UAC1B,MAAM2E,MAAM,GAAGoB,GAAG,CAACpB,MAAM;UAEzB,MAAMoC,OAAO,GAAGvC,OAAO,CAAEhE,CAAC,GAAG,CAAC,CAAE;UAChC,MAAM+F,WAAW,GAAGQ,OAAO,CAAClC,gBAAgB;;UAE5C;UACA,IAAKmC,QAAQ,KAAK,CAAC,EAAG;YAErB,IAAKX,SAAS,KAAK,CAAC,EAAG;cAEtBvH,UAAU,CAAE6F,MAAM,EAAEI,UAAW,CAAC;YAEjC,CAAC,MAAM;cAENhG,WAAW,CAAE4F,MAAM,EAAEI,UAAU,EAAEA,UAAW,CAAC;YAE9C;UAED;UAEAsB,SAAS,IAAIW,QAAQ;;UAErB;UACA,IAAIR,QAAQ,GAAG,CAAC;UAChB,IAAIC,SAAS,GAAG,CAAC;UAEjB,IAAKJ,SAAS,KAAK,CAAC,EAAG;YAEtBG,QAAQ,GAAG3H,kBAAkB,CAAEkG,UAAW,CAAC,GAAGM,eAAe;UAE9D;UAEA,MAAMiB,UAAU,GAAGtG,KAAK,GAAGqG,SAAS;UACpC,IAAKC,UAAU,KAAK,CAAC,EAAG;YAEvBG,SAAS,GAAG5H,kBAAkB,CAAE0H,WAAY,CAAC,GAAGlB,eAAe;UAEhE;UAEA,MAAMqB,IAAI,GAAGrH,cAAc,GAAGD,uBAAuB,IACpDoH,QAAQ,GAAGH,SAAS,GAAGI,SAAS,GAAGH,UAAU,CAC7C;UAED,IAAKI,IAAI,GAAGpB,QAAQ,EAAG;YAEtBrB,IAAI,GAAGzC,CAAC;YACR8D,QAAQ,GAAGoB,IAAI;YACf3C,GAAG,GAAGgC,GAAG,CAACxB,SAAS;UAEpB;QAED;MAED;IAED;EAED,CAAC,MAAM;IAEN0C,OAAO,CAACC,IAAI,CAAE,yCAA0C/B,QAAQ,QAAU,CAAC;EAE5E;EAEA,OAAO;IAAElB,IAAI;IAAEF;EAAI,CAAC;AAErB;;AAEA;AACA,SAASqB,UAAUA,CAAEvD,cAAc,EAAEjB,MAAM,EAAEZ,KAAK,EAAEiE,IAAI,EAAG;EAE1D,IAAIkD,GAAG,GAAG,CAAC;EACX,KAAM,IAAI3G,CAAC,GAAGI,MAAM,EAAEc,GAAG,GAAGd,MAAM,GAAGZ,KAAK,EAAEQ,CAAC,GAAGkB,GAAG,EAAElB,CAAC,EAAG,EAAG;IAE3D2G,GAAG,IAAItF,cAAc,CAAErB,CAAC,GAAG,CAAC,GAAGyD,IAAI,GAAG,CAAC,CAAE;EAE1C;EAEA,OAAOkD,GAAG,GAAGnH,KAAK;AAEnB;;AAEA;AACA;AACA;AACA;AACA,SAASoH,qBAAqBA,CAAE1H,GAAG,EAAE2H,UAAU,EAAG;EAEjD,MAAMC,OAAO,GAAG5H,GAAG,CAACI,UAAU,CAACC,QAAQ;EACvC,MAAMH,KAAK,GAAGF,GAAG,CAACE,KAAK,CAAC2H,KAAK;EAC7B,MAAMC,QAAQ,GAAG5H,KAAK,CAACe,MAAM,GAAG,CAAC;EACjC,MAAMkB,cAAc,GAAG,IAAI+C,YAAY,CAAE4C,QAAQ,GAAG,CAAE,CAAC;EACvD,MAAMC,UAAU,GAAGH,OAAO,CAACG,UAAU;;EAErC;EACA,MAAMC,MAAM,GAAGJ,OAAO,CAACC,KAAK;;EAE5B;EACA,MAAMI,YAAY,GAAGL,OAAO,CAAC1G,MAAM,IAAI,CAAC;EACxC,IAAIgH,MAAM,GAAG,CAAC;EACd,IAAKN,OAAO,CAACO,4BAA4B,EAAG;IAE3CD,MAAM,GAAGN,OAAO,CAACQ,IAAI,CAACF,MAAM;EAE7B;;EAEA;EACA,MAAMG,OAAO,GAAG,CAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAE;EAE1C,KAAM,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGR,QAAQ,EAAEQ,GAAG,EAAG,EAAG;IAE3C,MAAMC,IAAI,GAAGD,GAAG,GAAG,CAAC;IACpB,MAAME,IAAI,GAAGF,GAAG,GAAG,CAAC;IAEpB,IAAIG,EAAE,EAAEjC,EAAE,EAAEkC,EAAE;IAEd,IAAKX,UAAU,EAAG;MAEjBU,EAAE,GAAGvI,KAAK,CAAEqI,IAAI,GAAG,CAAC,CAAE;MACtB/B,EAAE,GAAGtG,KAAK,CAAEqI,IAAI,GAAG,CAAC,CAAE;MACtBG,EAAE,GAAGxI,KAAK,CAAEqI,IAAI,GAAG,CAAC,CAAE;IAEvB,CAAC,MAAM;MAENE,EAAE,GAAGvI,KAAK,CAAEqI,IAAI,GAAG,CAAC,CAAE,GAAGL,MAAM,GAAGD,YAAY;MAC9CzB,EAAE,GAAGtG,KAAK,CAAEqI,IAAI,GAAG,CAAC,CAAE,GAAGL,MAAM,GAAGD,YAAY;MAC9CS,EAAE,GAAGxI,KAAK,CAAEqI,IAAI,GAAG,CAAC,CAAE,GAAGL,MAAM,GAAGD,YAAY;IAE/C;IAEA,KAAM,IAAIU,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,EAAEA,EAAE,EAAG,EAAG;MAEjC,IAAI7G,CAAC,EAAEC,CAAC,EAAEqE,CAAC;MAEX,IAAK2B,UAAU,EAAG;QAEjBjG,CAAC,GAAG8F,OAAO,CAAES,OAAO,CAAEM,EAAE,CAAE,CAAE,CAAEF,EAAG,CAAC;QAClC1G,CAAC,GAAG6F,OAAO,CAAES,OAAO,CAAEM,EAAE,CAAE,CAAE,CAAEnC,EAAG,CAAC;QAClCJ,CAAC,GAAGwB,OAAO,CAAES,OAAO,CAAEM,EAAE,CAAE,CAAE,CAAED,EAAG,CAAC;MAEnC,CAAC,MAAM;QAEN5G,CAAC,GAAGkG,MAAM,CAAES,EAAE,GAAGE,EAAE,CAAE;QACrB5G,CAAC,GAAGiG,MAAM,CAAExB,EAAE,GAAGmC,EAAE,CAAE;QACrBvC,CAAC,GAAG4B,MAAM,CAAEU,EAAE,GAAGC,EAAE,CAAE;MAEtB;MAEA,IAAIC,GAAG,GAAG9G,CAAC;MACX,IAAKC,CAAC,GAAG6G,GAAG,EAAGA,GAAG,GAAG7G,CAAC;MACtB,IAAKqE,CAAC,GAAGwC,GAAG,EAAGA,GAAG,GAAGxC,CAAC;MAEtB,IAAIyC,GAAG,GAAG/G,CAAC;MACX,IAAKC,CAAC,GAAG8G,GAAG,EAAGA,GAAG,GAAG9G,CAAC;MACtB,IAAKqE,CAAC,GAAGyC,GAAG,EAAGA,GAAG,GAAGzC,CAAC;;MAEtB;MACA;MACA;MACA,MAAM0C,WAAW,GAAG,CAAED,GAAG,GAAGD,GAAG,IAAK,CAAC;MACrC,MAAMG,GAAG,GAAGJ,EAAE,GAAG,CAAC;MAClBxG,cAAc,CAAEqG,IAAI,GAAGO,GAAG,GAAG,CAAC,CAAE,GAAGH,GAAG,GAAGE,WAAW;MACpD3G,cAAc,CAAEqG,IAAI,GAAGO,GAAG,GAAG,CAAC,CAAE,GAAGD,WAAW,GAAG,CAAEE,IAAI,CAACC,GAAG,CAAEL,GAAI,CAAC,GAAGE,WAAW,IAAKjJ,eAAe;MAEpG,IAAK+I,GAAG,GAAGjB,UAAU,CAAEgB,EAAE,CAAE,EAAGhB,UAAU,CAAEgB,EAAE,CAAE,GAAGC,GAAG;MACpD,IAAKC,GAAG,GAAGlB,UAAU,CAAEgB,EAAE,GAAG,CAAC,CAAE,EAAGhB,UAAU,CAAEgB,EAAE,GAAG,CAAC,CAAE,GAAGE,GAAG;IAE7D;EAED;EAEA,OAAO1G,cAAc;AAEtB;AAEA,OAAO,SAAS+G,SAASA,CAAElJ,GAAG,EAAEC,OAAO,EAAG;EAEzC,SAASkJ,eAAeA,CAAEC,kBAAkB,EAAG;IAE9C,IAAKC,UAAU,EAAG;MAEjBA,UAAU,CAAED,kBAAkB,GAAGE,cAAe,CAAC;IAElD;EAED;;EAEA;EACA;EACA,SAASC,SAASA,CAAEC,IAAI,EAAEtI,MAAM,EAAEZ,KAAK,EAAEkF,oBAAoB,GAAG,IAAI,EAAEiE,KAAK,GAAG,CAAC,EAAG;IAEjF,IAAK,CAAEC,eAAe,IAAID,KAAK,IAAIE,QAAQ,EAAG;MAE7CD,eAAe,GAAG,IAAI;MACtB,IAAKE,OAAO,EAAG;QAEdrC,OAAO,CAACC,IAAI,CAAE,yBAA0BmC,QAAQ,6DAA+D,CAAC;QAChHpC,OAAO,CAACC,IAAI,CAAExH,GAAI,CAAC;MAEpB;IAED;;IAEA;IACA,IAAKM,KAAK,IAAIuJ,WAAW,IAAIJ,KAAK,IAAIE,QAAQ,EAAG;MAEhDR,eAAe,CAAEjI,MAAM,GAAGZ,KAAM,CAAC;MACjCkJ,IAAI,CAACtI,MAAM,GAAGA,MAAM;MACpBsI,IAAI,CAAClJ,KAAK,GAAGA,KAAK;MAClB,OAAOkJ,IAAI;IAEZ;;IAEA;IACA,MAAMtF,KAAK,GAAGoB,eAAe,CAAEkE,IAAI,CAACM,YAAY,EAAEtE,oBAAoB,EAAErD,cAAc,EAAEjB,MAAM,EAAEZ,KAAK,EAAEmF,QAAS,CAAC;IACjH,IAAKvB,KAAK,CAACK,IAAI,KAAK,CAAE,CAAC,EAAG;MAEzB4E,eAAe,CAAEjI,MAAM,GAAGZ,KAAM,CAAC;MACjCkJ,IAAI,CAACtI,MAAM,GAAGA,MAAM;MACpBsI,IAAI,CAAClJ,KAAK,GAAGA,KAAK;MAClB,OAAOkJ,IAAI;IAEZ;IAEA,MAAMO,WAAW,GAAG9F,SAAS,CAAE+F,UAAU,EAAE7H,cAAc,EAAEjB,MAAM,EAAEZ,KAAK,EAAE4D,KAAM,CAAC;;IAEjF;IACA,IAAK6F,WAAW,KAAK7I,MAAM,IAAI6I,WAAW,KAAK7I,MAAM,GAAGZ,KAAK,EAAG;MAE/D6I,eAAe,CAAEjI,MAAM,GAAGZ,KAAM,CAAC;MACjCkJ,IAAI,CAACtI,MAAM,GAAGA,MAAM;MACpBsI,IAAI,CAAClJ,KAAK,GAAGA,KAAK;IAEnB,CAAC,MAAM;MAENkJ,IAAI,CAACS,SAAS,GAAG/F,KAAK,CAACK,IAAI;;MAE3B;MACA,MAAMJ,IAAI,GAAG,IAAIlF,WAAW,CAAC,CAAC;MAC9B,MAAMiL,MAAM,GAAGhJ,MAAM;MACrB,MAAMiJ,MAAM,GAAGJ,WAAW,GAAG7I,MAAM;MACnCsI,IAAI,CAACrF,IAAI,GAAGA,IAAI;MAChBA,IAAI,CAAC2F,YAAY,GAAG,IAAI5E,YAAY,CAAE,CAAE,CAAC;MAEzChD,SAAS,CAAEC,cAAc,EAAE+H,MAAM,EAAEC,MAAM,EAAEhG,IAAI,CAAC2F,YAAY,EAAEM,yBAA0B,CAAC;MACzFb,SAAS,CAAEpF,IAAI,EAAE+F,MAAM,EAAEC,MAAM,EAAEC,yBAAyB,EAAEX,KAAK,GAAG,CAAE,CAAC;;MAEvE;MACA,MAAMrF,KAAK,GAAG,IAAInF,WAAW,CAAC,CAAC;MAC/B,MAAMoL,MAAM,GAAGN,WAAW;MAC1B,MAAMO,MAAM,GAAGhK,KAAK,GAAG6J,MAAM;MAC7BX,IAAI,CAACpF,KAAK,GAAGA,KAAK;MAClBA,KAAK,CAAC0F,YAAY,GAAG,IAAI5E,YAAY,CAAE,CAAE,CAAC;MAE1ChD,SAAS,CAAEC,cAAc,EAAEkI,MAAM,EAAEC,MAAM,EAAElG,KAAK,CAAC0F,YAAY,EAAEM,yBAA0B,CAAC;MAC1Fb,SAAS,CAAEnF,KAAK,EAAEiG,MAAM,EAAEC,MAAM,EAAEF,yBAAyB,EAAEX,KAAK,GAAG,CAAE,CAAC;IAEzE;IAEA,OAAOD,IAAI;EAEZ;EAEAzJ,WAAW,CAAEC,GAAG,EAAEC,OAAQ,CAAC;;EAE3B;EACA;EACA;EACA,MAAM0H,UAAU,GAAG,IAAIzC,YAAY,CAAE,CAAE,CAAC;EACxC,MAAMkF,yBAAyB,GAAG,IAAIlF,YAAY,CAAE,CAAE,CAAC;EACvD,MAAM/C,cAAc,GAAGuF,qBAAqB,CAAE1H,GAAG,EAAE2H,UAAW,CAAC;EAC/D,MAAMqC,UAAU,GAAGhK,GAAG,CAACE,KAAK,CAAC2H,KAAK;EAClC,MAAM8B,QAAQ,GAAG1J,OAAO,CAAC0J,QAAQ;EACjC,MAAMC,OAAO,GAAG3J,OAAO,CAAC2J,OAAO;EAC/B,MAAMC,WAAW,GAAG5J,OAAO,CAAC4J,WAAW;EACvC,MAAMpE,QAAQ,GAAGxF,OAAO,CAACwF,QAAQ;EACjC,MAAM4D,UAAU,GAAGpJ,OAAO,CAACoJ,UAAU;EACrC,MAAMC,cAAc,GAAGtJ,GAAG,CAACE,KAAK,CAACI,KAAK,GAAG,CAAC;EAC1C,IAAIoJ,eAAe,GAAG,KAAK;EAE3B,MAAMa,KAAK,GAAG,EAAE;EAChB,MAAMpJ,MAAM,GAAGJ,kBAAkB,CAAEf,GAAI,CAAC;EAExC,IAAKmB,MAAM,CAACF,MAAM,KAAK,CAAC,EAAG;IAE1B,MAAMuJ,KAAK,GAAGrJ,MAAM,CAAE,CAAC,CAAE;IACzB,MAAMsJ,IAAI,GAAG,IAAIxL,WAAW,CAAC,CAAC;IAC9BwL,IAAI,CAACX,YAAY,GAAGnC,UAAU;IAC9B3D,iBAAiB,CAAE7B,cAAc,EAAEqI,KAAK,CAACtJ,MAAM,EAAEsJ,KAAK,CAAClK,KAAK,EAAE8J,yBAA0B,CAAC;IAEzFb,SAAS,CAAEkB,IAAI,EAAED,KAAK,CAACtJ,MAAM,EAAEsJ,KAAK,CAAClK,KAAK,EAAE8J,yBAA0B,CAAC;IACvEG,KAAK,CAACtI,IAAI,CAAEwI,IAAK,CAAC;EAEnB,CAAC,MAAM;IAEN,KAAM,IAAID,KAAK,IAAIrJ,MAAM,EAAG;MAE3B,MAAMsJ,IAAI,GAAG,IAAIxL,WAAW,CAAC,CAAC;MAC9BwL,IAAI,CAACX,YAAY,GAAG,IAAI5E,YAAY,CAAE,CAAE,CAAC;MACzChD,SAAS,CAAEC,cAAc,EAAEqI,KAAK,CAACtJ,MAAM,EAAEsJ,KAAK,CAAClK,KAAK,EAAEmK,IAAI,CAACX,YAAY,EAAEM,yBAA0B,CAAC;MAEpGb,SAAS,CAAEkB,IAAI,EAAED,KAAK,CAACtJ,MAAM,EAAEsJ,KAAK,CAAClK,KAAK,EAAE8J,yBAA0B,CAAC;MACvEG,KAAK,CAACtI,IAAI,CAAEwI,IAAK,CAAC;IAEnB;EAED;EAEA,OAAOF,KAAK;AAEb;AAEA,OAAO,SAASG,eAAeA,CAAE1K,GAAG,EAAEC,OAAO,EAAG;EAE/C;EACA;EACA;EACA,MAAMsK,KAAK,GAAGrB,SAAS,CAAElJ,GAAG,EAAEC,OAAQ,CAAC;EAEvC,IAAI0K,YAAY;EAChB,IAAIC,WAAW;EACf,IAAIC,WAAW;EACf,MAAMC,WAAW,GAAG,EAAE;EACtB,MAAMvK,iBAAiB,GAAGN,OAAO,CAACO,oBAAoB,GAAGC,iBAAiB,GAAGC,WAAW;EACxF,KAAM,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyJ,KAAK,CAACtJ,MAAM,EAAEH,CAAC,EAAG,EAAG;IAEzC,MAAM2J,IAAI,GAAGF,KAAK,CAAEzJ,CAAC,CAAE;IACvB,IAAIiK,SAAS,GAAGC,UAAU,CAAEP,IAAK,CAAC;IAElC,MAAMQ,MAAM,GAAG,IAAI1K,iBAAiB,CAAEX,cAAc,GAAGmL,SAAU,CAAC;IAClEJ,YAAY,GAAG,IAAIzF,YAAY,CAAE+F,MAAO,CAAC;IACzCL,WAAW,GAAG,IAAIjK,WAAW,CAAEsK,MAAO,CAAC;IACvCJ,WAAW,GAAG,IAAIjK,WAAW,CAAEqK,MAAO,CAAC;IACvCC,cAAc,CAAE,CAAC,EAAET,IAAK,CAAC;IACzBK,WAAW,CAAC7I,IAAI,CAAEgJ,MAAO,CAAC;EAE3B;EAEA,OAAOH,WAAW;EAElB,SAASE,UAAUA,CAAExB,IAAI,EAAG;IAE3B,IAAKA,IAAI,CAAClJ,KAAK,EAAG;MAEjB,OAAO,CAAC;IAET,CAAC,MAAM;MAEN,OAAO,CAAC,GAAG0K,UAAU,CAAExB,IAAI,CAACrF,IAAK,CAAC,GAAG6G,UAAU,CAAExB,IAAI,CAACpF,KAAM,CAAC;IAE9D;EAED;EAEA,SAAS8G,cAAcA,CAAEC,UAAU,EAAE3B,IAAI,EAAG;IAE3C,MAAM4B,aAAa,GAAGD,UAAU,GAAG,CAAC;IACpC,MAAME,aAAa,GAAGF,UAAU,GAAG,CAAC;IACpC,MAAMG,MAAM,GAAG,CAAE,CAAE9B,IAAI,CAAClJ,KAAK;IAC7B,MAAMwJ,YAAY,GAAGN,IAAI,CAACM,YAAY;IACtC,KAAM,IAAIhJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9B6J,YAAY,CAAES,aAAa,GAAGtK,CAAC,CAAE,GAAGgJ,YAAY,CAAEhJ,CAAC,CAAE;IAEtD;IAEA,IAAKwK,MAAM,EAAG;MAEb,MAAMpK,MAAM,GAAGsI,IAAI,CAACtI,MAAM;MAC1B,MAAMZ,KAAK,GAAGkJ,IAAI,CAAClJ,KAAK;MACxBsK,WAAW,CAAEQ,aAAa,GAAG,CAAC,CAAE,GAAGlK,MAAM;MACzC2J,WAAW,CAAEQ,aAAa,GAAG,EAAE,CAAE,GAAG/K,KAAK;MACzCuK,WAAW,CAAEQ,aAAa,GAAG,EAAE,CAAE,GAAGvL,gBAAgB;MACpD,OAAOqL,UAAU,GAAGvL,cAAc;IAEnC,CAAC,MAAM;MAEN,MAAMuE,IAAI,GAAGqF,IAAI,CAACrF,IAAI;MACtB,MAAMC,KAAK,GAAGoF,IAAI,CAACpF,KAAK;MACxB,MAAM6F,SAAS,GAAGT,IAAI,CAACS,SAAS;MAEhC,IAAIsB,iBAAiB;MACrBA,iBAAiB,GAAGL,cAAc,CAAEC,UAAU,GAAGvL,cAAc,EAAEuE,IAAK,CAAC;MAEvE,IAAOoH,iBAAiB,GAAG,CAAC,GAAKvC,IAAI,CAACwC,GAAG,CAAE,CAAC,EAAE,EAAG,CAAC,EAAG;QAEpD,MAAM,IAAIC,KAAK,CAAE,2DAA4D,CAAC;MAE/E;MAEAb,WAAW,CAAEQ,aAAa,GAAG,CAAC,CAAE,GAAGG,iBAAiB,GAAG,CAAC;MACxDA,iBAAiB,GAAGL,cAAc,CAAEK,iBAAiB,EAAEnH,KAAM,CAAC;MAE9DwG,WAAW,CAAEQ,aAAa,GAAG,CAAC,CAAE,GAAGnB,SAAS;MAC5C,OAAOsB,iBAAiB;IAEzB;EAED;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}