/* Product Form Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.product-form-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e1e8ed;
}

.modal-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

/* Modal Tabs */
.modal-tabs {
  display: flex;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.tab-button {
  flex: 1;
  padding: 16px 20px;
  border: none;
  background: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.tab-button:hover {
  background: #e9ecef;
  color: #2c3e50;
}

.tab-button.active {
  background: white;
  color: #F0B21B;
  border-bottom-color: #F0B21B;
}

.tab-icon {
  font-size: 16px;
}

/* Modal Body */
.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.tab-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Form Styles */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.form-input,
.form-select,
.form-textarea {
  padding: 12px;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #F0B21B;
  box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

/* Checkboxes */
.form-checkboxes {
  display: flex;
  gap: 24px;
  margin-top: 16px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #2c3e50;
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #F0B21B;
}

/* File Upload Section */
.file-upload-section {
  margin-bottom: 32px;
}

.file-upload-section h3 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.uploaded-files {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.uploaded-file {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e1e8ed;
}

.uploaded-file span {
  font-size: 14px;
  color: #2c3e50;
}

.remove-file {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.2s ease;
}

.remove-file:hover {
  background: #c82333;
  transform: scale(1.1);
}

/* Preview Section */
.preview-section {
  height: 400px;
  border: 2px dashed #e1e8ed;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.no-preview {
  text-align: center;
  color: #6c757d;
}

.no-preview p {
  margin: 0;
  font-size: 16px;
}

/* Modal Footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.btn-primary {
  background: #F0B21B;
  color: white;
}

.btn-primary:hover {
  background: #d4a017;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(240, 178, 27, 0.3);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }

  .product-form-modal {
    max-height: 95vh;
  }

  .modal-header {
    padding: 16px;
  }

  .modal-header h2 {
    font-size: 20px;
  }

  .modal-tabs {
    flex-direction: column;
  }

  .tab-button {
    padding: 12px 16px;
    justify-content: flex-start;
  }

  .modal-body {
    padding: 16px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .form-checkboxes {
    flex-direction: column;
    gap: 12px;
  }

  .modal-footer {
    padding: 16px;
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .modal-header {
    padding: 12px;
  }

  .modal-header h2 {
    font-size: 18px;
  }

  .tab-button {
    padding: 10px 12px;
    font-size: 13px;
  }

  .modal-body {
    padding: 12px;
  }

  .form-input,
  .form-select,
  .form-textarea {
    padding: 10px;
    font-size: 13px;
  }

  .preview-section {
    height: 300px;
  }
}
