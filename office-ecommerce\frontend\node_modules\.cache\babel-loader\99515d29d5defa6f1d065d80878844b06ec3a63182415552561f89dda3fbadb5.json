{"ast": null, "code": "import * as React from 'react';\nfunction call(ref, value) {\n  if (typeof ref === 'function') ref(value);else if (ref != null) ref.current = value;\n}\nfunction useEffectfulState(fn, deps = [], cb) {\n  const [state, set] = React.useState();\n  React.useLayoutEffect(() => {\n    const value = fn();\n    set(value);\n    call(cb, value);\n    return () => call(cb, null);\n  }, deps);\n  return state;\n}\nexport { useEffectfulState };", "map": {"version": 3, "names": ["React", "call", "ref", "value", "current", "useEffectfulState", "fn", "deps", "cb", "state", "set", "useState", "useLayoutEffect"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/helpers/useEffectfulState.js"], "sourcesContent": ["import * as React from 'react';\n\nfunction call(ref, value) {\n  if (typeof ref === 'function') ref(value);else if (ref != null) ref.current = value;\n}\n\nfunction useEffectfulState(fn, deps = [], cb) {\n  const [state, set] = React.useState();\n  React.useLayoutEffect(() => {\n    const value = fn();\n    set(value);\n    call(cb, value);\n    return () => call(cb, null);\n  }, deps);\n  return state;\n}\n\nexport { useEffectfulState };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,IAAIA,CAACC,GAAG,EAAEC,KAAK,EAAE;EACxB,IAAI,OAAOD,GAAG,KAAK,UAAU,EAAEA,GAAG,CAACC,KAAK,CAAC,CAAC,KAAK,IAAID,GAAG,IAAI,IAAI,EAAEA,GAAG,CAACE,OAAO,GAAGD,KAAK;AACrF;AAEA,SAASE,iBAAiBA,CAACC,EAAE,EAAEC,IAAI,GAAG,EAAE,EAAEC,EAAE,EAAE;EAC5C,MAAM,CAACC,KAAK,EAAEC,GAAG,CAAC,GAAGV,KAAK,CAACW,QAAQ,CAAC,CAAC;EACrCX,KAAK,CAACY,eAAe,CAAC,MAAM;IAC1B,MAAMT,KAAK,GAAGG,EAAE,CAAC,CAAC;IAClBI,GAAG,CAACP,KAAK,CAAC;IACVF,IAAI,CAACO,EAAE,EAAEL,KAAK,CAAC;IACf,OAAO,MAAMF,IAAI,CAACO,EAAE,EAAE,IAAI,CAAC;EAC7B,CAAC,EAAED,IAAI,CAAC;EACR,OAAOE,KAAK;AACd;AAEA,SAASJ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}