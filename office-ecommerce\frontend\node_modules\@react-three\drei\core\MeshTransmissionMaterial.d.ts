import * as THREE from 'three';
import * as React from 'react';
declare type MeshTransmissionMaterialType = Omit<JSX.IntrinsicElements['meshPhysicalMaterial'], 'args' | 'roughness' | 'thickness' | 'transmission'> & {
    transmission?: number;
    thickness?: number;
    roughness?: number;
    chromaticAberration?: number;
    anisotropy?: number;
    distortion?: number;
    distortionScale: number;
    temporalDistortion: number;
    buffer?: THREE.Texture;
    time?: number;
    args?: [samples: number, transmissionSampler: boolean];
};
declare global {
    namespace JSX {
        interface IntrinsicElements {
            meshTransmissionMaterial: MeshTransmissionMaterialType;
        }
    }
}
export declare const MeshTransmissionMaterial: React.ForwardRefExoticComponent<Pick<Omit<MeshTransmissionMaterialType, "args"> & {
    transmissionSampler?: boolean | undefined;
    backside?: boolean | undefined;
    backsideThickness?: number | undefined;
    resolution?: number | undefined;
    backsideResolution?: number | undefined;
    samples?: number | undefined;
    background?: THREE.Color | THREE.Texture | undefined;
}, "visible" | "attach" | "children" | "key" | "onUpdate" | "dispose" | "type" | "id" | "uuid" | "name" | "userData" | "toJSON" | "clone" | "copy" | "addEventListener" | "hasEventListener" | "removeEventListener" | "dispatchEvent" | "color" | "blending" | "map" | "time" | "transparent" | "fog" | "alphaTest" | "alphaToCoverage" | "blendDst" | "blendDstAlpha" | "blendEquation" | "blendEquationAlpha" | "blendSrc" | "blendSrcAlpha" | "clipIntersection" | "clippingPlanes" | "clipShadows" | "colorWrite" | "defines" | "depthFunc" | "depthTest" | "depthWrite" | "stencilWrite" | "stencilFunc" | "stencilRef" | "stencilWriteMask" | "stencilFuncMask" | "stencilFail" | "stencilZFail" | "stencilZPass" | "isMaterial" | "needsUpdate" | "opacity" | "polygonOffset" | "polygonOffsetFactor" | "polygonOffsetUnits" | "precision" | "premultipliedAlpha" | "forceSinglePass" | "dithering" | "side" | "shadowSide" | "toneMapped" | "vertexColors" | "version" | "onBeforeCompile" | "customProgramCacheKey" | "setValues" | "background" | "buffer" | "alphaMap" | "wireframe" | "wireframeLinewidth" | "clearcoat" | "clearcoatMap" | "clearcoatRoughness" | "clearcoatRoughnessMap" | "clearcoatNormalScale" | "clearcoatNormalMap" | "reflectivity" | "ior" | "sheen" | "sheenColor" | "sheenColorMap" | "sheenRoughness" | "sheenRoughnessMap" | "transmission" | "transmissionMap" | "thickness" | "thicknessMap" | "attenuationDistance" | "attenuationColor" | "specularIntensity" | "specularColor" | "specularIntensityMap" | "specularColorMap" | "iridescenceMap" | "iridescenceIOR" | "iridescence" | "iridescenceThicknessRange" | "iridescenceThicknessMap" | "roughness" | "metalness" | "lightMap" | "lightMapIntensity" | "aoMap" | "aoMapIntensity" | "emissive" | "emissiveIntensity" | "emissiveMap" | "bumpMap" | "bumpScale" | "normalMap" | "normalMapType" | "normalScale" | "displacementMap" | "displacementScale" | "displacementBias" | "roughnessMap" | "metalnessMap" | "envMap" | "envMapIntensity" | "wireframeLinecap" | "wireframeLinejoin" | "flatShading" | "isMeshStandardMaterial" | "anisotropy" | "resolution" | "samples" | "distortion" | "chromaticAberration" | "distortionScale" | "temporalDistortion" | "transmissionSampler" | "backside" | "backsideThickness" | "backsideResolution"> & React.RefAttributes<unknown>>;
export {};
