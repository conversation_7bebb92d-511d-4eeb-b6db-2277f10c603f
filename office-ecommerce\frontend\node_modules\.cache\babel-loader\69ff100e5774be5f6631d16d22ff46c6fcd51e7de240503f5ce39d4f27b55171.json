{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Plane, Vector3, Matrix4, Vector4, Perspective<PERSON>amera, WebGLRenderTarget, DepthTexture, DepthFormat, UnsignedShortType, HalfFloatType, LinearFilter } from 'three';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\nimport { BlurPass } from '../materials/BlurPass.js';\nimport { MeshReflectorMaterial } from '../materials/MeshReflectorMaterial.js';\nextend({\n  MeshReflectorMaterial\n});\n/**\n * @deprecated Use MeshReflectorMaterial instead\n */\n\nconst Reflector = /*#__PURE__*/React.forwardRef(({\n  mixBlur = 0,\n  mixStrength = 0.5,\n  resolution = 256,\n  blur = [0, 0],\n  args = [1, 1],\n  minDepthThreshold = 0.9,\n  maxDepthThreshold = 1,\n  depthScale = 0,\n  depthToBlurRatioBias = 0.25,\n  mirror = 0,\n  children,\n  debug = 0,\n  distortion = 1,\n  mixContrast = 1,\n  distortionMap,\n  ...props\n}, ref) => {\n  React.useEffect(() => {\n    console.warn('Reflector has been deprecated and will be removed next major. Replace it with <MeshReflectorMaterial />!');\n  }, []);\n  const gl = useThree(({\n    gl\n  }) => gl);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const scene = useThree(({\n    scene\n  }) => scene);\n  blur = Array.isArray(blur) ? blur : [blur, blur];\n  const hasBlur = blur[0] + blur[1] > 0;\n  const meshRef = React.useRef(null);\n  const [reflectorPlane] = React.useState(() => new Plane());\n  const [normal] = React.useState(() => new Vector3());\n  const [reflectorWorldPosition] = React.useState(() => new Vector3());\n  const [cameraWorldPosition] = React.useState(() => new Vector3());\n  const [rotationMatrix] = React.useState(() => new Matrix4());\n  const [lookAtPosition] = React.useState(() => new Vector3(0, 0, -1));\n  const [clipPlane] = React.useState(() => new Vector4());\n  const [view] = React.useState(() => new Vector3());\n  const [target] = React.useState(() => new Vector3());\n  const [q] = React.useState(() => new Vector4());\n  const [textureMatrix] = React.useState(() => new Matrix4());\n  const [virtualCamera] = React.useState(() => new PerspectiveCamera());\n  const beforeRender = React.useCallback(() => {\n    reflectorWorldPosition.setFromMatrixPosition(meshRef.current.matrixWorld);\n    cameraWorldPosition.setFromMatrixPosition(camera.matrixWorld);\n    rotationMatrix.extractRotation(meshRef.current.matrixWorld);\n    normal.set(0, 0, 1);\n    normal.applyMatrix4(rotationMatrix);\n    view.subVectors(reflectorWorldPosition, cameraWorldPosition); // Avoid rendering when reflector is facing away\n\n    if (view.dot(normal) > 0) return;\n    view.reflect(normal).negate();\n    view.add(reflectorWorldPosition);\n    rotationMatrix.extractRotation(camera.matrixWorld);\n    lookAtPosition.set(0, 0, -1);\n    lookAtPosition.applyMatrix4(rotationMatrix);\n    lookAtPosition.add(cameraWorldPosition);\n    target.subVectors(reflectorWorldPosition, lookAtPosition);\n    target.reflect(normal).negate();\n    target.add(reflectorWorldPosition);\n    virtualCamera.position.copy(view);\n    virtualCamera.up.set(0, 1, 0);\n    virtualCamera.up.applyMatrix4(rotationMatrix);\n    virtualCamera.up.reflect(normal);\n    virtualCamera.lookAt(target);\n    virtualCamera.far = camera.far; // Used in WebGLBackground\n\n    virtualCamera.updateMatrixWorld();\n    virtualCamera.projectionMatrix.copy(camera.projectionMatrix); // Update the texture matrix\n\n    textureMatrix.set(0.5, 0.0, 0.0, 0.5, 0.0, 0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0);\n    textureMatrix.multiply(virtualCamera.projectionMatrix);\n    textureMatrix.multiply(virtualCamera.matrixWorldInverse);\n    textureMatrix.multiply(meshRef.current.matrixWorld); // Now update projection matrix with new clip plane, implementing code from: http://www.terathon.com/code/oblique.html\n    // Paper explaining this technique: http://www.terathon.com/lengyel/Lengyel-Oblique.pdf\n\n    reflectorPlane.setFromNormalAndCoplanarPoint(normal, reflectorWorldPosition);\n    reflectorPlane.applyMatrix4(virtualCamera.matrixWorldInverse);\n    clipPlane.set(reflectorPlane.normal.x, reflectorPlane.normal.y, reflectorPlane.normal.z, reflectorPlane.constant);\n    const projectionMatrix = virtualCamera.projectionMatrix;\n    q.x = (Math.sign(clipPlane.x) + projectionMatrix.elements[8]) / projectionMatrix.elements[0];\n    q.y = (Math.sign(clipPlane.y) + projectionMatrix.elements[9]) / projectionMatrix.elements[5];\n    q.z = -1.0;\n    q.w = (1.0 + projectionMatrix.elements[10]) / projectionMatrix.elements[14]; // Calculate the scaled plane vector\n\n    clipPlane.multiplyScalar(2.0 / clipPlane.dot(q)); // Replacing the third row of the projection matrix\n\n    projectionMatrix.elements[2] = clipPlane.x;\n    projectionMatrix.elements[6] = clipPlane.y;\n    projectionMatrix.elements[10] = clipPlane.z + 1.0;\n    projectionMatrix.elements[14] = clipPlane.w;\n  }, []);\n  const [fbo1, fbo2, blurpass, reflectorProps] = React.useMemo(() => {\n    const parameters = {\n      type: HalfFloatType,\n      minFilter: LinearFilter,\n      magFilter: LinearFilter\n    };\n    const fbo1 = new WebGLRenderTarget(resolution, resolution, parameters);\n    fbo1.depthBuffer = true;\n    fbo1.depthTexture = new DepthTexture(resolution, resolution);\n    fbo1.depthTexture.format = DepthFormat;\n    fbo1.depthTexture.type = UnsignedShortType;\n    const fbo2 = new WebGLRenderTarget(resolution, resolution, parameters);\n    const blurpass = new BlurPass({\n      gl,\n      resolution,\n      width: blur[0],\n      height: blur[1],\n      minDepthThreshold,\n      maxDepthThreshold,\n      depthScale,\n      depthToBlurRatioBias\n    });\n    const reflectorProps = {\n      mirror,\n      textureMatrix,\n      mixBlur,\n      tDiffuse: fbo1.texture,\n      tDepth: fbo1.depthTexture,\n      tDiffuseBlur: fbo2.texture,\n      hasBlur,\n      mixStrength,\n      minDepthThreshold,\n      maxDepthThreshold,\n      depthScale,\n      depthToBlurRatioBias,\n      transparent: true,\n      debug,\n      distortion,\n      distortionMap,\n      mixContrast,\n      'defines-USE_BLUR': hasBlur ? '' : undefined,\n      'defines-USE_DEPTH': depthScale > 0 ? '' : undefined,\n      'defines-USE_DISTORTION': distortionMap ? '' : undefined\n    };\n    return [fbo1, fbo2, blurpass, reflectorProps];\n  }, [gl, blur, textureMatrix, resolution, mirror, hasBlur, mixBlur, mixStrength, minDepthThreshold, maxDepthThreshold, depthScale, depthToBlurRatioBias, debug, distortion, distortionMap, mixContrast]);\n  useFrame(() => {\n    if (!(meshRef != null && meshRef.current)) return;\n    meshRef.current.visible = false;\n    const currentXrEnabled = gl.xr.enabled;\n    const currentShadowAutoUpdate = gl.shadowMap.autoUpdate;\n    beforeRender();\n    gl.xr.enabled = false;\n    gl.shadowMap.autoUpdate = false;\n    gl.setRenderTarget(fbo1);\n    gl.state.buffers.depth.setMask(true);\n    if (!gl.autoClear) gl.clear();\n    gl.render(scene, virtualCamera);\n    if (hasBlur) blurpass.render(gl, fbo1, fbo2);\n    gl.xr.enabled = currentXrEnabled;\n    gl.shadowMap.autoUpdate = currentShadowAutoUpdate;\n    meshRef.current.visible = true;\n    gl.setRenderTarget(null);\n  });\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: mergeRefs([meshRef, ref])\n  }, props), /*#__PURE__*/React.createElement(\"planeGeometry\", {\n    args: args\n  }), children ? children('meshReflectorMaterial', reflectorProps) : /*#__PURE__*/React.createElement(\"meshReflectorMaterial\", reflectorProps));\n});\nexport { Reflector };", "map": {"version": 3, "names": ["_extends", "React", "Plane", "Vector3", "Matrix4", "Vector4", "PerspectiveCamera", "WebGLRenderTarget", "DepthTexture", "DepthFormat", "UnsignedShortType", "HalfFloatType", "LinearFilter", "extend", "useThree", "useFrame", "mergeRefs", "BlurPass", "MeshReflectorMaterial", "Reflector", "forwardRef", "mixBlur", "mixStrength", "resolution", "blur", "args", "minDepthThr<PERSON>old", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "depthScale", "depthToBlurRatioBias", "mirror", "children", "debug", "distortion", "mixContrast", "distortionMap", "props", "ref", "useEffect", "console", "warn", "gl", "camera", "scene", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "meshRef", "useRef", "reflectorPlane", "useState", "normal", "reflectorWorldPosition", "cameraWorldPosition", "rotationMatrix", "lookAtPosition", "clipPlane", "view", "target", "q", "textureMatrix", "virtualCamera", "beforeRender", "useCallback", "setFromMatrixPosition", "current", "matrixWorld", "extractRotation", "set", "applyMatrix4", "subVectors", "dot", "reflect", "negate", "add", "position", "copy", "up", "lookAt", "far", "updateMatrixWorld", "projectionMatrix", "multiply", "matrixWorldInverse", "setFromNormalAndCoplanarPoint", "x", "y", "z", "constant", "Math", "sign", "elements", "w", "multiplyScalar", "fbo1", "fbo2", "blurpass", "reflectorProps", "useMemo", "parameters", "type", "minFilter", "magFilter", "depthBuffer", "depthTexture", "format", "width", "height", "tDiffuse", "texture", "tD<PERSON>h", "tDiffuseBlur", "transparent", "undefined", "visible", "currentXrEnabled", "xr", "enabled", "currentShadowAutoUpdate", "shadowMap", "autoUpdate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "buffers", "depth", "setMask", "autoClear", "clear", "render", "createElement"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Reflector.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Plane, Vector3, Matrix4, Vector4, Perspective<PERSON>amera, WebGLRenderTarget, DepthTexture, DepthFormat, UnsignedShortType, HalfFloatType, LinearFilter } from 'three';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\nimport { BlurPass } from '../materials/BlurPass.js';\nimport { MeshReflectorMaterial } from '../materials/MeshReflectorMaterial.js';\n\nextend({\n  MeshReflectorMaterial\n});\n/**\n * @deprecated Use MeshReflectorMaterial instead\n */\n\nconst Reflector = /*#__PURE__*/React.forwardRef(({\n  mixBlur = 0,\n  mixStrength = 0.5,\n  resolution = 256,\n  blur = [0, 0],\n  args = [1, 1],\n  minDepthThreshold = 0.9,\n  maxDepthThreshold = 1,\n  depthScale = 0,\n  depthToBlurRatioBias = 0.25,\n  mirror = 0,\n  children,\n  debug = 0,\n  distortion = 1,\n  mixContrast = 1,\n  distortionMap,\n  ...props\n}, ref) => {\n  React.useEffect(() => {\n    console.warn('Reflector has been deprecated and will be removed next major. Replace it with <MeshReflectorMaterial />!');\n  }, []);\n  const gl = useThree(({\n    gl\n  }) => gl);\n  const camera = useThree(({\n    camera\n  }) => camera);\n  const scene = useThree(({\n    scene\n  }) => scene);\n  blur = Array.isArray(blur) ? blur : [blur, blur];\n  const hasBlur = blur[0] + blur[1] > 0;\n  const meshRef = React.useRef(null);\n  const [reflectorPlane] = React.useState(() => new Plane());\n  const [normal] = React.useState(() => new Vector3());\n  const [reflectorWorldPosition] = React.useState(() => new Vector3());\n  const [cameraWorldPosition] = React.useState(() => new Vector3());\n  const [rotationMatrix] = React.useState(() => new Matrix4());\n  const [lookAtPosition] = React.useState(() => new Vector3(0, 0, -1));\n  const [clipPlane] = React.useState(() => new Vector4());\n  const [view] = React.useState(() => new Vector3());\n  const [target] = React.useState(() => new Vector3());\n  const [q] = React.useState(() => new Vector4());\n  const [textureMatrix] = React.useState(() => new Matrix4());\n  const [virtualCamera] = React.useState(() => new PerspectiveCamera());\n  const beforeRender = React.useCallback(() => {\n    reflectorWorldPosition.setFromMatrixPosition(meshRef.current.matrixWorld);\n    cameraWorldPosition.setFromMatrixPosition(camera.matrixWorld);\n    rotationMatrix.extractRotation(meshRef.current.matrixWorld);\n    normal.set(0, 0, 1);\n    normal.applyMatrix4(rotationMatrix);\n    view.subVectors(reflectorWorldPosition, cameraWorldPosition); // Avoid rendering when reflector is facing away\n\n    if (view.dot(normal) > 0) return;\n    view.reflect(normal).negate();\n    view.add(reflectorWorldPosition);\n    rotationMatrix.extractRotation(camera.matrixWorld);\n    lookAtPosition.set(0, 0, -1);\n    lookAtPosition.applyMatrix4(rotationMatrix);\n    lookAtPosition.add(cameraWorldPosition);\n    target.subVectors(reflectorWorldPosition, lookAtPosition);\n    target.reflect(normal).negate();\n    target.add(reflectorWorldPosition);\n    virtualCamera.position.copy(view);\n    virtualCamera.up.set(0, 1, 0);\n    virtualCamera.up.applyMatrix4(rotationMatrix);\n    virtualCamera.up.reflect(normal);\n    virtualCamera.lookAt(target);\n    virtualCamera.far = camera.far; // Used in WebGLBackground\n\n    virtualCamera.updateMatrixWorld();\n    virtualCamera.projectionMatrix.copy(camera.projectionMatrix); // Update the texture matrix\n\n    textureMatrix.set(0.5, 0.0, 0.0, 0.5, 0.0, 0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0);\n    textureMatrix.multiply(virtualCamera.projectionMatrix);\n    textureMatrix.multiply(virtualCamera.matrixWorldInverse);\n    textureMatrix.multiply(meshRef.current.matrixWorld); // Now update projection matrix with new clip plane, implementing code from: http://www.terathon.com/code/oblique.html\n    // Paper explaining this technique: http://www.terathon.com/lengyel/Lengyel-Oblique.pdf\n\n    reflectorPlane.setFromNormalAndCoplanarPoint(normal, reflectorWorldPosition);\n    reflectorPlane.applyMatrix4(virtualCamera.matrixWorldInverse);\n    clipPlane.set(reflectorPlane.normal.x, reflectorPlane.normal.y, reflectorPlane.normal.z, reflectorPlane.constant);\n    const projectionMatrix = virtualCamera.projectionMatrix;\n    q.x = (Math.sign(clipPlane.x) + projectionMatrix.elements[8]) / projectionMatrix.elements[0];\n    q.y = (Math.sign(clipPlane.y) + projectionMatrix.elements[9]) / projectionMatrix.elements[5];\n    q.z = -1.0;\n    q.w = (1.0 + projectionMatrix.elements[10]) / projectionMatrix.elements[14]; // Calculate the scaled plane vector\n\n    clipPlane.multiplyScalar(2.0 / clipPlane.dot(q)); // Replacing the third row of the projection matrix\n\n    projectionMatrix.elements[2] = clipPlane.x;\n    projectionMatrix.elements[6] = clipPlane.y;\n    projectionMatrix.elements[10] = clipPlane.z + 1.0;\n    projectionMatrix.elements[14] = clipPlane.w;\n  }, []);\n  const [fbo1, fbo2, blurpass, reflectorProps] = React.useMemo(() => {\n    const parameters = {\n      type: HalfFloatType,\n      minFilter: LinearFilter,\n      magFilter: LinearFilter\n    };\n    const fbo1 = new WebGLRenderTarget(resolution, resolution, parameters);\n    fbo1.depthBuffer = true;\n    fbo1.depthTexture = new DepthTexture(resolution, resolution);\n    fbo1.depthTexture.format = DepthFormat;\n    fbo1.depthTexture.type = UnsignedShortType;\n    const fbo2 = new WebGLRenderTarget(resolution, resolution, parameters);\n    const blurpass = new BlurPass({\n      gl,\n      resolution,\n      width: blur[0],\n      height: blur[1],\n      minDepthThreshold,\n      maxDepthThreshold,\n      depthScale,\n      depthToBlurRatioBias\n    });\n    const reflectorProps = {\n      mirror,\n      textureMatrix,\n      mixBlur,\n      tDiffuse: fbo1.texture,\n      tDepth: fbo1.depthTexture,\n      tDiffuseBlur: fbo2.texture,\n      hasBlur,\n      mixStrength,\n      minDepthThreshold,\n      maxDepthThreshold,\n      depthScale,\n      depthToBlurRatioBias,\n      transparent: true,\n      debug,\n      distortion,\n      distortionMap,\n      mixContrast,\n      'defines-USE_BLUR': hasBlur ? '' : undefined,\n      'defines-USE_DEPTH': depthScale > 0 ? '' : undefined,\n      'defines-USE_DISTORTION': distortionMap ? '' : undefined\n    };\n    return [fbo1, fbo2, blurpass, reflectorProps];\n  }, [gl, blur, textureMatrix, resolution, mirror, hasBlur, mixBlur, mixStrength, minDepthThreshold, maxDepthThreshold, depthScale, depthToBlurRatioBias, debug, distortion, distortionMap, mixContrast]);\n  useFrame(() => {\n    if (!(meshRef != null && meshRef.current)) return;\n    meshRef.current.visible = false;\n    const currentXrEnabled = gl.xr.enabled;\n    const currentShadowAutoUpdate = gl.shadowMap.autoUpdate;\n    beforeRender();\n    gl.xr.enabled = false;\n    gl.shadowMap.autoUpdate = false;\n    gl.setRenderTarget(fbo1);\n    gl.state.buffers.depth.setMask(true);\n    if (!gl.autoClear) gl.clear();\n    gl.render(scene, virtualCamera);\n    if (hasBlur) blurpass.render(gl, fbo1, fbo2);\n    gl.xr.enabled = currentXrEnabled;\n    gl.shadowMap.autoUpdate = currentShadowAutoUpdate;\n    meshRef.current.visible = true;\n    gl.setRenderTarget(null);\n  });\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: mergeRefs([meshRef, ref])\n  }, props), /*#__PURE__*/React.createElement(\"planeGeometry\", {\n    args: args\n  }), children ? children('meshReflectorMaterial', reflectorProps) : /*#__PURE__*/React.createElement(\"meshReflectorMaterial\", reflectorProps));\n});\n\nexport { Reflector };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,YAAY,QAAQ,OAAO;AACzK,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAC/D,OAAOC,SAAS,MAAM,kBAAkB;AACxC,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,qBAAqB,QAAQ,uCAAuC;AAE7EL,MAAM,CAAC;EACLK;AACF,CAAC,CAAC;AACF;AACA;AACA;;AAEA,MAAMC,SAAS,GAAG,aAAalB,KAAK,CAACmB,UAAU,CAAC,CAAC;EAC/CC,OAAO,GAAG,CAAC;EACXC,WAAW,GAAG,GAAG;EACjBC,UAAU,GAAG,GAAG;EAChBC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACbC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACbC,iBAAiB,GAAG,GAAG;EACvBC,iBAAiB,GAAG,CAAC;EACrBC,UAAU,GAAG,CAAC;EACdC,oBAAoB,GAAG,IAAI;EAC3BC,MAAM,GAAG,CAAC;EACVC,QAAQ;EACRC,KAAK,GAAG,CAAC;EACTC,UAAU,GAAG,CAAC;EACdC,WAAW,GAAG,CAAC;EACfC,aAAa;EACb,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTpC,KAAK,CAACqC,SAAS,CAAC,MAAM;IACpBC,OAAO,CAACC,IAAI,CAAC,0GAA0G,CAAC;EAC1H,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,EAAE,GAAG3B,QAAQ,CAAC,CAAC;IACnB2B;EACF,CAAC,KAAKA,EAAE,CAAC;EACT,MAAMC,MAAM,GAAG5B,QAAQ,CAAC,CAAC;IACvB4B;EACF,CAAC,KAAKA,MAAM,CAAC;EACb,MAAMC,KAAK,GAAG7B,QAAQ,CAAC,CAAC;IACtB6B;EACF,CAAC,KAAKA,KAAK,CAAC;EACZnB,IAAI,GAAGoB,KAAK,CAACC,OAAO,CAACrB,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,EAAEA,IAAI,CAAC;EAChD,MAAMsB,OAAO,GAAGtB,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;EACrC,MAAMuB,OAAO,GAAG9C,KAAK,CAAC+C,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAACC,cAAc,CAAC,GAAGhD,KAAK,CAACiD,QAAQ,CAAC,MAAM,IAAIhD,KAAK,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACiD,MAAM,CAAC,GAAGlD,KAAK,CAACiD,QAAQ,CAAC,MAAM,IAAI/C,OAAO,CAAC,CAAC,CAAC;EACpD,MAAM,CAACiD,sBAAsB,CAAC,GAAGnD,KAAK,CAACiD,QAAQ,CAAC,MAAM,IAAI/C,OAAO,CAAC,CAAC,CAAC;EACpE,MAAM,CAACkD,mBAAmB,CAAC,GAAGpD,KAAK,CAACiD,QAAQ,CAAC,MAAM,IAAI/C,OAAO,CAAC,CAAC,CAAC;EACjE,MAAM,CAACmD,cAAc,CAAC,GAAGrD,KAAK,CAACiD,QAAQ,CAAC,MAAM,IAAI9C,OAAO,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACmD,cAAc,CAAC,GAAGtD,KAAK,CAACiD,QAAQ,CAAC,MAAM,IAAI/C,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpE,MAAM,CAACqD,SAAS,CAAC,GAAGvD,KAAK,CAACiD,QAAQ,CAAC,MAAM,IAAI7C,OAAO,CAAC,CAAC,CAAC;EACvD,MAAM,CAACoD,IAAI,CAAC,GAAGxD,KAAK,CAACiD,QAAQ,CAAC,MAAM,IAAI/C,OAAO,CAAC,CAAC,CAAC;EAClD,MAAM,CAACuD,MAAM,CAAC,GAAGzD,KAAK,CAACiD,QAAQ,CAAC,MAAM,IAAI/C,OAAO,CAAC,CAAC,CAAC;EACpD,MAAM,CAACwD,CAAC,CAAC,GAAG1D,KAAK,CAACiD,QAAQ,CAAC,MAAM,IAAI7C,OAAO,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACuD,aAAa,CAAC,GAAG3D,KAAK,CAACiD,QAAQ,CAAC,MAAM,IAAI9C,OAAO,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACyD,aAAa,CAAC,GAAG5D,KAAK,CAACiD,QAAQ,CAAC,MAAM,IAAI5C,iBAAiB,CAAC,CAAC,CAAC;EACrE,MAAMwD,YAAY,GAAG7D,KAAK,CAAC8D,WAAW,CAAC,MAAM;IAC3CX,sBAAsB,CAACY,qBAAqB,CAACjB,OAAO,CAACkB,OAAO,CAACC,WAAW,CAAC;IACzEb,mBAAmB,CAACW,qBAAqB,CAACtB,MAAM,CAACwB,WAAW,CAAC;IAC7DZ,cAAc,CAACa,eAAe,CAACpB,OAAO,CAACkB,OAAO,CAACC,WAAW,CAAC;IAC3Df,MAAM,CAACiB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACnBjB,MAAM,CAACkB,YAAY,CAACf,cAAc,CAAC;IACnCG,IAAI,CAACa,UAAU,CAAClB,sBAAsB,EAAEC,mBAAmB,CAAC,CAAC,CAAC;;IAE9D,IAAII,IAAI,CAACc,GAAG,CAACpB,MAAM,CAAC,GAAG,CAAC,EAAE;IAC1BM,IAAI,CAACe,OAAO,CAACrB,MAAM,CAAC,CAACsB,MAAM,CAAC,CAAC;IAC7BhB,IAAI,CAACiB,GAAG,CAACtB,sBAAsB,CAAC;IAChCE,cAAc,CAACa,eAAe,CAACzB,MAAM,CAACwB,WAAW,CAAC;IAClDX,cAAc,CAACa,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5Bb,cAAc,CAACc,YAAY,CAACf,cAAc,CAAC;IAC3CC,cAAc,CAACmB,GAAG,CAACrB,mBAAmB,CAAC;IACvCK,MAAM,CAACY,UAAU,CAAClB,sBAAsB,EAAEG,cAAc,CAAC;IACzDG,MAAM,CAACc,OAAO,CAACrB,MAAM,CAAC,CAACsB,MAAM,CAAC,CAAC;IAC/Bf,MAAM,CAACgB,GAAG,CAACtB,sBAAsB,CAAC;IAClCS,aAAa,CAACc,QAAQ,CAACC,IAAI,CAACnB,IAAI,CAAC;IACjCI,aAAa,CAACgB,EAAE,CAACT,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7BP,aAAa,CAACgB,EAAE,CAACR,YAAY,CAACf,cAAc,CAAC;IAC7CO,aAAa,CAACgB,EAAE,CAACL,OAAO,CAACrB,MAAM,CAAC;IAChCU,aAAa,CAACiB,MAAM,CAACpB,MAAM,CAAC;IAC5BG,aAAa,CAACkB,GAAG,GAAGrC,MAAM,CAACqC,GAAG,CAAC,CAAC;;IAEhClB,aAAa,CAACmB,iBAAiB,CAAC,CAAC;IACjCnB,aAAa,CAACoB,gBAAgB,CAACL,IAAI,CAAClC,MAAM,CAACuC,gBAAgB,CAAC,CAAC,CAAC;;IAE9DrB,aAAa,CAACQ,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACjGR,aAAa,CAACsB,QAAQ,CAACrB,aAAa,CAACoB,gBAAgB,CAAC;IACtDrB,aAAa,CAACsB,QAAQ,CAACrB,aAAa,CAACsB,kBAAkB,CAAC;IACxDvB,aAAa,CAACsB,QAAQ,CAACnC,OAAO,CAACkB,OAAO,CAACC,WAAW,CAAC,CAAC,CAAC;IACrD;;IAEAjB,cAAc,CAACmC,6BAA6B,CAACjC,MAAM,EAAEC,sBAAsB,CAAC;IAC5EH,cAAc,CAACoB,YAAY,CAACR,aAAa,CAACsB,kBAAkB,CAAC;IAC7D3B,SAAS,CAACY,GAAG,CAACnB,cAAc,CAACE,MAAM,CAACkC,CAAC,EAAEpC,cAAc,CAACE,MAAM,CAACmC,CAAC,EAAErC,cAAc,CAACE,MAAM,CAACoC,CAAC,EAAEtC,cAAc,CAACuC,QAAQ,CAAC;IACjH,MAAMP,gBAAgB,GAAGpB,aAAa,CAACoB,gBAAgB;IACvDtB,CAAC,CAAC0B,CAAC,GAAG,CAACI,IAAI,CAACC,IAAI,CAAClC,SAAS,CAAC6B,CAAC,CAAC,GAAGJ,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC,IAAIV,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC;IAC5FhC,CAAC,CAAC2B,CAAC,GAAG,CAACG,IAAI,CAACC,IAAI,CAAClC,SAAS,CAAC8B,CAAC,CAAC,GAAGL,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC,IAAIV,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC;IAC5FhC,CAAC,CAAC4B,CAAC,GAAG,CAAC,GAAG;IACV5B,CAAC,CAACiC,CAAC,GAAG,CAAC,GAAG,GAAGX,gBAAgB,CAACU,QAAQ,CAAC,EAAE,CAAC,IAAIV,gBAAgB,CAACU,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;IAE7EnC,SAAS,CAACqC,cAAc,CAAC,GAAG,GAAGrC,SAAS,CAACe,GAAG,CAACZ,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElDsB,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC,GAAGnC,SAAS,CAAC6B,CAAC;IAC1CJ,gBAAgB,CAACU,QAAQ,CAAC,CAAC,CAAC,GAAGnC,SAAS,CAAC8B,CAAC;IAC1CL,gBAAgB,CAACU,QAAQ,CAAC,EAAE,CAAC,GAAGnC,SAAS,CAAC+B,CAAC,GAAG,GAAG;IACjDN,gBAAgB,CAACU,QAAQ,CAAC,EAAE,CAAC,GAAGnC,SAAS,CAACoC,CAAC;EAC7C,CAAC,EAAE,EAAE,CAAC;EACN,MAAM,CAACE,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,cAAc,CAAC,GAAGhG,KAAK,CAACiG,OAAO,CAAC,MAAM;IACjE,MAAMC,UAAU,GAAG;MACjBC,IAAI,EAAEzF,aAAa;MACnB0F,SAAS,EAAEzF,YAAY;MACvB0F,SAAS,EAAE1F;IACb,CAAC;IACD,MAAMkF,IAAI,GAAG,IAAIvF,iBAAiB,CAACgB,UAAU,EAAEA,UAAU,EAAE4E,UAAU,CAAC;IACtEL,IAAI,CAACS,WAAW,GAAG,IAAI;IACvBT,IAAI,CAACU,YAAY,GAAG,IAAIhG,YAAY,CAACe,UAAU,EAAEA,UAAU,CAAC;IAC5DuE,IAAI,CAACU,YAAY,CAACC,MAAM,GAAGhG,WAAW;IACtCqF,IAAI,CAACU,YAAY,CAACJ,IAAI,GAAG1F,iBAAiB;IAC1C,MAAMqF,IAAI,GAAG,IAAIxF,iBAAiB,CAACgB,UAAU,EAAEA,UAAU,EAAE4E,UAAU,CAAC;IACtE,MAAMH,QAAQ,GAAG,IAAI/E,QAAQ,CAAC;MAC5BwB,EAAE;MACFlB,UAAU;MACVmF,KAAK,EAAElF,IAAI,CAAC,CAAC,CAAC;MACdmF,MAAM,EAAEnF,IAAI,CAAC,CAAC,CAAC;MACfE,iBAAiB;MACjBC,iBAAiB;MACjBC,UAAU;MACVC;IACF,CAAC,CAAC;IACF,MAAMoE,cAAc,GAAG;MACrBnE,MAAM;MACN8B,aAAa;MACbvC,OAAO;MACPuF,QAAQ,EAAEd,IAAI,CAACe,OAAO;MACtBC,MAAM,EAAEhB,IAAI,CAACU,YAAY;MACzBO,YAAY,EAAEhB,IAAI,CAACc,OAAO;MAC1B/D,OAAO;MACPxB,WAAW;MACXI,iBAAiB;MACjBC,iBAAiB;MACjBC,UAAU;MACVC,oBAAoB;MACpBmF,WAAW,EAAE,IAAI;MACjBhF,KAAK;MACLC,UAAU;MACVE,aAAa;MACbD,WAAW;MACX,kBAAkB,EAAEY,OAAO,GAAG,EAAE,GAAGmE,SAAS;MAC5C,mBAAmB,EAAErF,UAAU,GAAG,CAAC,GAAG,EAAE,GAAGqF,SAAS;MACpD,wBAAwB,EAAE9E,aAAa,GAAG,EAAE,GAAG8E;IACjD,CAAC;IACD,OAAO,CAACnB,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,cAAc,CAAC;EAC/C,CAAC,EAAE,CAACxD,EAAE,EAAEjB,IAAI,EAAEoC,aAAa,EAAErC,UAAU,EAAEO,MAAM,EAAEgB,OAAO,EAAEzB,OAAO,EAAEC,WAAW,EAAEI,iBAAiB,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,oBAAoB,EAAEG,KAAK,EAAEC,UAAU,EAAEE,aAAa,EAAED,WAAW,CAAC,CAAC;EACvMnB,QAAQ,CAAC,MAAM;IACb,IAAI,EAAEgC,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACkB,OAAO,CAAC,EAAE;IAC3ClB,OAAO,CAACkB,OAAO,CAACiD,OAAO,GAAG,KAAK;IAC/B,MAAMC,gBAAgB,GAAG1E,EAAE,CAAC2E,EAAE,CAACC,OAAO;IACtC,MAAMC,uBAAuB,GAAG7E,EAAE,CAAC8E,SAAS,CAACC,UAAU;IACvD1D,YAAY,CAAC,CAAC;IACdrB,EAAE,CAAC2E,EAAE,CAACC,OAAO,GAAG,KAAK;IACrB5E,EAAE,CAAC8E,SAAS,CAACC,UAAU,GAAG,KAAK;IAC/B/E,EAAE,CAACgF,eAAe,CAAC3B,IAAI,CAAC;IACxBrD,EAAE,CAACiF,KAAK,CAACC,OAAO,CAACC,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC;IACpC,IAAI,CAACpF,EAAE,CAACqF,SAAS,EAAErF,EAAE,CAACsF,KAAK,CAAC,CAAC;IAC7BtF,EAAE,CAACuF,MAAM,CAACrF,KAAK,EAAEkB,aAAa,CAAC;IAC/B,IAAIf,OAAO,EAAEkD,QAAQ,CAACgC,MAAM,CAACvF,EAAE,EAAEqD,IAAI,EAAEC,IAAI,CAAC;IAC5CtD,EAAE,CAAC2E,EAAE,CAACC,OAAO,GAAGF,gBAAgB;IAChC1E,EAAE,CAAC8E,SAAS,CAACC,UAAU,GAAGF,uBAAuB;IACjDvE,OAAO,CAACkB,OAAO,CAACiD,OAAO,GAAG,IAAI;IAC9BzE,EAAE,CAACgF,eAAe,CAAC,IAAI,CAAC;EAC1B,CAAC,CAAC;EACF,OAAO,aAAaxH,KAAK,CAACgI,aAAa,CAAC,MAAM,EAAEjI,QAAQ,CAAC;IACvDqC,GAAG,EAAErB,SAAS,CAAC,CAAC+B,OAAO,EAAEV,GAAG,CAAC;EAC/B,CAAC,EAAED,KAAK,CAAC,EAAE,aAAanC,KAAK,CAACgI,aAAa,CAAC,eAAe,EAAE;IAC3DxG,IAAI,EAAEA;EACR,CAAC,CAAC,EAAEM,QAAQ,GAAGA,QAAQ,CAAC,uBAAuB,EAAEkE,cAAc,CAAC,GAAG,aAAahG,KAAK,CAACgI,aAAa,CAAC,uBAAuB,EAAEhC,cAAc,CAAC,CAAC;AAC/I,CAAC,CAAC;AAEF,SAAS9E,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}