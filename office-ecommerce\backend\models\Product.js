const BaseModel = require('./BaseModel');
const logger = require('../utils/logger');
const sql = require('mssql');

class Product extends BaseModel {
  constructor() {
    super('Products', 'ProductID');
  }

  // Get all products with category information
  async getAllWithCategory() {
    try {
      const query = `
        SELECT 
          p.*,
          c.CategoryName,
          c.Description as CategoryDescription
        FROM Products p
        INNER JOIN Categories c ON p.CategoryID = c.CategoryID
        WHERE p.IsActive = 1
        ORDER BY p.ProductName
      `;
      
      return await this.executeQuery(query);
    } catch (error) {
      logger.error('Error getting products with category:', error);
      throw error;
    }
  }

  // Get products by category
  async getByCategory(categoryId) {
    try {
      const query = `
        SELECT 
          p.*,
          c.CategoryName
        FROM Products p
        INNER JOIN Categories c ON p.CategoryID = c.CategoryID
        WHERE p.CategoryID = @categoryId AND p.IsActive = 1
        ORDER BY p.ProductName
      `;
      
      return await this.executeQuery(query, { categoryId });
    } catch (error) {
      logger.error('Error getting products by category:', error);
      throw error;
    }
  }

  // Get product with variants and inventory
  async getWithVariantsAndInventory(productId) {
    try {
      const query = `
        SELECT 
          p.*,
          c.CategoryName,
          pv.VariantID,
          pv.VariantCode,
          pv.VariantName,
          pv.SKU,
          pv.Price,
          pv.Dimensions as VariantDimensions,
          pv.Material as VariantMaterial,
          pv.Color as VariantColor,
          pv.Finish,
          pi.QuantityOnHand,
          pi.QuantityReserved,
          pi.QuantityAvailable,
          pi.MinStockLevel,
          pi.ReorderPoint
        FROM Products p
        INNER JOIN Categories c ON p.CategoryID = c.CategoryID
        LEFT JOIN ProductVariants pv ON p.ProductID = pv.ProductID AND pv.IsActive = 1
        LEFT JOIN ProductInventory pi ON pv.VariantID = pi.VariantID
        WHERE p.ProductID = @productId AND p.IsActive = 1
        ORDER BY pv.VariantName
      `;
      
      const results = await this.executeQuery(query, { productId });
      
      if (results.length === 0) {
        return null;
      }

      // Group variants under the product
      const product = {
        ProductID: results[0].ProductID,
        ProductCode: results[0].ProductCode,
        ProductName: results[0].ProductName,
        CategoryID: results[0].CategoryID,
        CategoryName: results[0].CategoryName,
        Description: results[0].Description,
        BasePrice: results[0].BasePrice,
        IsCustomizable: results[0].IsCustomizable,
        Weight: results[0].Weight,
        Dimensions: results[0].Dimensions,
        Material: results[0].Material,
        Color: results[0].Color,
        ImageURL: results[0].ImageURL,
        CreatedAt: results[0].CreatedAt,
        UpdatedAt: results[0].UpdatedAt,
        variants: []
      };

      // Add variants if they exist
      results.forEach(row => {
        if (row.VariantID) {
          product.variants.push({
            VariantID: row.VariantID,
            VariantCode: row.VariantCode,
            VariantName: row.VariantName,
            SKU: row.SKU,
            Price: row.Price,
            Dimensions: row.VariantDimensions,
            Material: row.VariantMaterial,
            Color: row.VariantColor,
            Finish: row.Finish,
            inventory: {
              QuantityOnHand: row.QuantityOnHand || 0,
              QuantityReserved: row.QuantityReserved || 0,
              QuantityAvailable: row.QuantityAvailable || 0,
              MinStockLevel: row.MinStockLevel || 0,
              ReorderPoint: row.ReorderPoint || 0
            }
          });
        }
      });

      return product;
    } catch (error) {
      logger.error('Error getting product with variants and inventory:', error);
      throw error;
    }
  }

  // Search products
  async search(searchTerm, categoryId = null, limit = 20) {
    try {
      let query = `
        SELECT 
          p.*,
          c.CategoryName
        FROM Products p
        INNER JOIN Categories c ON p.CategoryID = c.CategoryID
        WHERE p.IsActive = 1
        AND (
          p.ProductName LIKE @searchTerm 
          OR p.Description LIKE @searchTerm
          OR p.ProductCode LIKE @searchTerm
        )
      `;

      const params = { searchTerm: `%${searchTerm}%` };

      if (categoryId) {
        query += ` AND p.CategoryID = @categoryId`;
        params.categoryId = categoryId;
      }

      query += ` ORDER BY p.ProductName`;

      if (limit) {
        query = query.replace('SELECT', `SELECT TOP ${limit}`);
      }

      return await this.executeQuery(query, params);
    } catch (error) {
      logger.error('Error searching products:', error);
      throw error;
    }
  }

  // Get low stock products
  async getLowStockProducts() {
    try {
      const query = `
        SELECT 
          p.ProductID,
          p.ProductCode,
          p.ProductName,
          pv.VariantID,
          pv.VariantCode,
          pv.VariantName,
          pv.SKU,
          pi.QuantityOnHand,
          pi.QuantityReserved,
          pi.QuantityAvailable,
          pi.MinStockLevel,
          pi.ReorderPoint,
          c.CategoryName
        FROM Products p
        INNER JOIN ProductVariants pv ON p.ProductID = pv.ProductID
        INNER JOIN ProductInventory pi ON pv.VariantID = pi.VariantID
        INNER JOIN Categories c ON p.CategoryID = c.CategoryID
        WHERE p.IsActive = 1 
        AND pv.IsActive = 1
        AND pi.QuantityAvailable <= pi.ReorderPoint
        ORDER BY pi.QuantityAvailable ASC, p.ProductName
      `;
      
      return await this.executeQuery(query);
    } catch (error) {
      logger.error('Error getting low stock products:', error);
      throw error;
    }
  }

  // Get product sales analytics
  async getSalesAnalytics(productId, startDate, endDate) {
    try {
      const query = `
        SELECT 
          p.ProductID,
          p.ProductName,
          pv.VariantID,
          pv.VariantName,
          COUNT(oi.OrderItemID) as TotalOrders,
          SUM(oi.Quantity) as TotalQuantitySold,
          SUM(oi.TotalPrice) as TotalRevenue,
          AVG(oi.UnitPrice) as AveragePrice
        FROM Products p
        INNER JOIN ProductVariants pv ON p.ProductID = pv.ProductID
        INNER JOIN OrderItems oi ON pv.VariantID = oi.VariantID
        INNER JOIN Orders o ON oi.OrderID = o.OrderID
        WHERE p.ProductID = @productId
        AND o.CreatedAt BETWEEN @startDate AND @endDate
        AND o.OrderStatus NOT IN ('Cancelled')
        GROUP BY p.ProductID, p.ProductName, pv.VariantID, pv.VariantName
        ORDER BY TotalQuantitySold DESC
      `;
      
      return await this.executeQuery(query, { productId, startDate, endDate });
    } catch (error) {
      logger.error('Error getting product sales analytics:', error);
      throw error;
    }
  }

  // Create product with variants
  async createWithVariants(productData, variantsData = []) {
    const transaction = await this.beginTransaction();
    
    try {
      const request = transaction.request();
      
      // Create product
      const productColumns = Object.keys(productData);
      const productValues = Object.values(productData);
      const productPlaceholders = productColumns.map((_, index) => `@productParam${index}`);

      productValues.forEach((value, index) => {
        request.input(`productParam${index}`, value);
      });

      const productQuery = `
        INSERT INTO Products (${productColumns.join(', ')})
        OUTPUT INSERTED.*
        VALUES (${productPlaceholders.join(', ')})
      `;

      const productResult = await request.query(productQuery);
      const product = productResult.recordset[0];

      // Create variants if provided
      const variants = [];
      for (let i = 0; i < variantsData.length; i++) {
        const variantData = { ...variantsData[i], ProductID: product.ProductID };
        const variantRequest = transaction.request();
        
        const variantColumns = Object.keys(variantData);
        const variantValues = Object.values(variantData);
        const variantPlaceholders = variantColumns.map((_, index) => `@variantParam${i}_${index}`);

        variantValues.forEach((value, index) => {
          variantRequest.input(`variantParam${i}_${index}`, value);
        });

        const variantQuery = `
          INSERT INTO ProductVariants (${variantColumns.join(', ')})
          OUTPUT INSERTED.*
          VALUES (${variantPlaceholders.join(', ')})
        `;

        const variantResult = await variantRequest.query(variantQuery);
        const variant = variantResult.recordset[0];
        
        // Create initial inventory record
        const inventoryRequest = transaction.request();
        inventoryRequest.input(`inventoryVariantID${i}`, variant.VariantID);
        
        const inventoryQuery = `
          INSERT INTO ProductInventory (VariantID, QuantityOnHand, QuantityReserved, MinStockLevel, ReorderPoint)
          VALUES (@inventoryVariantID${i}, 0, 0, 0, 0)
        `;
        
        await inventoryRequest.query(inventoryQuery);
        variants.push(variant);
      }

      await transaction.commit();
      
      return {
        product,
        variants
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('Error creating product with variants:', error);
      throw error;
    }
  }

  // =============================================
  // ENHANCED PRODUCT MANAGEMENT METHODS
  // =============================================

  // Create or update product using stored procedure
  async createOrUpdateProduct(productData, userId, changeReason = null) {
    try {
      const request = new sql.Request();

      // Set parameters for stored procedure
      request.input('ProductID', sql.UniqueIdentifier, productData.ProductID || null);
      request.input('ProductCode', sql.NVarChar(50), productData.ProductCode);
      request.input('ProductName', sql.NVarChar(255), productData.ProductName);
      request.input('CategoryID', sql.UniqueIdentifier, productData.CategoryID);
      request.input('Description', sql.NVarChar(sql.MAX), productData.Description || null);
      request.input('BasePrice', sql.Decimal(10, 2), productData.BasePrice);
      request.input('Weight', sql.Decimal(8, 2), productData.Weight || null);
      request.input('Dimensions', sql.NVarChar(100), productData.Dimensions || null);
      request.input('Material', sql.NVarChar(100), productData.Material || null);
      request.input('Color', sql.NVarChar(50), productData.Color || null);
      request.input('Status', sql.NVarChar(50), productData.Status || 'Draft');
      request.input('Tags', sql.NVarChar(sql.MAX), productData.Tags || null);
      request.input('MetaData', sql.NVarChar(sql.MAX), productData.MetaData || null);
      request.input('IsCustomizable', sql.Bit, productData.IsCustomizable || false);
      request.input('IsActive', sql.Bit, productData.IsActive !== undefined ? productData.IsActive : true);
      request.input('CreatedBy', sql.UniqueIdentifier, userId);
      request.input('ChangeReason', sql.NVarChar(500), changeReason);

      const result = await request.execute('sp_CreateOrUpdateProduct');
      return result.recordset[0];
    } catch (error) {
      logger.error('Error creating/updating product:', error);
      throw error;
    }
  }

  // Get products with pagination and filtering using stored procedure
  async getProductsPaginated(options = {}) {
    try {
      const {
        pageNumber = 1,
        pageSize = 20,
        searchTerm = null,
        categoryId = null,
        status = null,
        isActive = null,
        sortBy = 'ProductName',
        sortDirection = 'ASC'
      } = options;

      const request = new sql.Request();
      request.input('PageNumber', sql.Int, pageNumber);
      request.input('PageSize', sql.Int, pageSize);
      request.input('SearchTerm', sql.NVarChar(255), searchTerm);
      request.input('CategoryID', sql.UniqueIdentifier, categoryId);
      request.input('Status', sql.NVarChar(50), status);
      request.input('IsActive', sql.Bit, isActive);
      request.input('SortBy', sql.NVarChar(50), sortBy);
      request.input('SortDirection', sql.NVarChar(10), sortDirection);

      const result = await request.execute('sp_GetProducts');

      return {
        totalCount: result.recordsets[0][0].TotalCount,
        products: result.recordsets[1]
      };
    } catch (error) {
      logger.error('Error getting paginated products:', error);
      throw error;
    }
  }

  // Get product by ID with full details using stored procedure
  async getProductByIdWithDetails(productId) {
    try {
      const request = new sql.Request();
      request.input('ProductID', sql.UniqueIdentifier, productId);

      const result = await request.execute('sp_GetProductById');

      if (result.recordsets[0].length === 0) {
        return null;
      }

      return {
        product: result.recordsets[0][0],
        models: result.recordsets[1],
        images: result.recordsets[2],
        components: result.recordsets[3],
        colors: result.recordsets[4]
      };
    } catch (error) {
      logger.error('Error getting product by ID with details:', error);
      throw error;
    }
  }

  // Soft delete product using stored procedure
  async deleteProduct(productId, userId, deleteReason = null) {
    try {
      const request = new sql.Request();
      request.input('ProductID', sql.UniqueIdentifier, productId);
      request.input('DeletedBy', sql.UniqueIdentifier, userId);
      request.input('DeleteReason', sql.NVarChar(500), deleteReason);

      const result = await request.execute('sp_DeleteProduct');
      return result.recordset[0];
    } catch (error) {
      logger.error('Error deleting product:', error);
      throw error;
    }
  }

  // Add 3D model to product
  async add3DModel(modelData) {
    try {
      const query = `
        INSERT INTO Product3DModels (
          ProductID, FileName, OriginalFileName, FilePath, FileSize,
          FileType, MimeType, IsActive, IsPrimary, ModelMetadata, UploadedBy
        )
        OUTPUT INSERTED.*
        VALUES (
          @ProductID, @FileName, @OriginalFileName, @FilePath, @FileSize,
          @FileType, @MimeType, @IsActive, @IsPrimary, @ModelMetadata, @UploadedBy
        )
      `;

      return await this.executeQuery(query, modelData);
    } catch (error) {
      logger.error('Error adding 3D model:', error);
      throw error;
    }
  }

  // Add image to product
  async addImage(imageData) {
    try {
      const query = `
        INSERT INTO ProductImages (
          ProductID, FileName, OriginalFileName, FilePath, ThumbnailPath,
          FileSize, FileType, MimeType, Width, Height, IsActive, IsPrimary,
          DisplayOrder, AltText, Caption, UploadedBy
        )
        OUTPUT INSERTED.*
        VALUES (
          @ProductID, @FileName, @OriginalFileName, @FilePath, @ThumbnailPath,
          @FileSize, @FileType, @MimeType, @Width, @Height, @IsActive, @IsPrimary,
          @DisplayOrder, @AltText, @Caption, @UploadedBy
        )
      `;

      return await this.executeQuery(query, imageData);
    } catch (error) {
      logger.error('Error adding image:', error);
      throw error;
    }
  }

  // Add component/part to product
  async addComponent(componentData) {
    try {
      const query = `
        INSERT INTO ProductComponents (
          ProductID, PartID, Quantity, IsOptional, IsCustomizable,
          DefaultConfiguration, Notes
        )
        OUTPUT INSERTED.*
        VALUES (
          @ProductID, @PartID, @Quantity, @IsOptional, @IsCustomizable,
          @DefaultConfiguration, @Notes
        )
      `;

      return await this.executeQuery(query, componentData);
    } catch (error) {
      logger.error('Error adding component:', error);
      throw error;
    }
  }

  // Add color option to product
  async addColor(colorData) {
    try {
      const query = `
        INSERT INTO ProductColors (
          ProductID, ColorName, ColorCode, ColorFamily, PriceAdjustment,
          IsActive, IsDefault, SortOrder
        )
        OUTPUT INSERTED.*
        VALUES (
          @ProductID, @ColorName, @ColorCode, @ColorFamily, @PriceAdjustment,
          @IsActive, @IsDefault, @SortOrder
        )
      `;

      return await this.executeQuery(query, colorData);
    } catch (error) {
      logger.error('Error adding color:', error);
      throw error;
    }
  }

  // Update file status (for upload tracking)
  async updateFileUploadStatus(uploadId, status, progress = null, errorMessage = null) {
    try {
      const query = `
        UPDATE ProductFileUploads
        SET
          UploadStatus = @status,
          UploadProgress = @progress,
          ErrorMessage = @errorMessage,
          CompletedAt = CASE WHEN @status = 'Completed' THEN GETUTCDATE() ELSE CompletedAt END
        WHERE UploadID = @uploadId
      `;

      return await this.executeQuery(query, { uploadId, status, progress, errorMessage });
    } catch (error) {
      logger.error('Error updating file upload status:', error);
      throw error;
    }
  }

  // Get product audit trail
  async getAuditTrail(productId, limit = 50) {
    try {
      const query = `
        SELECT TOP ${limit}
          pat.*,
          u.FirstName + ' ' + u.LastName as ChangedByName
        FROM ProductAuditTrail pat
        LEFT JOIN Users u ON pat.ChangedBy = u.UserID
        WHERE pat.ProductID = @productId
        ORDER BY pat.ChangedAt DESC
      `;

      return await this.executeQuery(query, { productId });
    } catch (error) {
      logger.error('Error getting audit trail:', error);
      throw error;
    }
  }
}

module.exports = Product;
