{"ast": null, "code": "let updateQueue = makeQueue();\nconst raf = fn => schedule(fn, updateQueue);\nlet writeQueue = makeQueue();\nraf.write = fn => schedule(fn, writeQueue);\nlet onStartQueue = makeQueue();\nraf.onStart = fn => schedule(fn, onStartQueue);\nlet onFrameQueue = makeQueue();\nraf.onFrame = fn => schedule(fn, onFrameQueue);\nlet onFinishQueue = makeQueue();\nraf.onFinish = fn => schedule(fn, onFinishQueue);\nlet timeouts = [];\nraf.setTimeout = (handler, ms) => {\n  let time = raf.now() + ms;\n  let cancel = () => {\n    let i = timeouts.findIndex(t => t.cancel == cancel);\n    if (~i) timeouts.splice(i, 1);\n    pendingCount -= ~i ? 1 : 0;\n  };\n  let timeout = {\n    time,\n    handler,\n    cancel\n  };\n  timeouts.splice(findTimeout(time), 0, timeout);\n  pendingCount += 1;\n  start();\n  return timeout;\n};\nlet findTimeout = time => ~(~timeouts.findIndex(t => t.time > time) || ~timeouts.length);\nraf.cancel = fn => {\n  onStartQueue.delete(fn);\n  onFrameQueue.delete(fn);\n  onFinishQueue.delete(fn);\n  updateQueue.delete(fn);\n  writeQueue.delete(fn);\n};\nraf.sync = fn => {\n  sync = true;\n  raf.batchedUpdates(fn);\n  sync = false;\n};\nraf.throttle = fn => {\n  let lastArgs;\n  function queuedFn() {\n    try {\n      fn(...lastArgs);\n    } finally {\n      lastArgs = null;\n    }\n  }\n  function throttled(...args) {\n    lastArgs = args;\n    raf.onStart(queuedFn);\n  }\n  throttled.handler = fn;\n  throttled.cancel = () => {\n    onStartQueue.delete(queuedFn);\n    lastArgs = null;\n  };\n  return throttled;\n};\nlet nativeRaf = typeof window != 'undefined' ? window.requestAnimationFrame : () => {};\nraf.use = impl => nativeRaf = impl;\nraf.now = typeof performance != 'undefined' ? () => performance.now() : Date.now;\nraf.batchedUpdates = fn => fn();\nraf.catch = console.error;\nraf.frameLoop = 'always';\nraf.advance = () => {\n  if (raf.frameLoop !== 'demand') {\n    console.warn('Cannot call the manual advancement of rafz whilst frameLoop is not set as demand');\n  } else {\n    update();\n  }\n};\nlet ts = -1;\nlet pendingCount = 0;\nlet sync = false;\nfunction schedule(fn, queue) {\n  if (sync) {\n    queue.delete(fn);\n    fn(0);\n  } else {\n    queue.add(fn);\n    start();\n  }\n}\nfunction start() {\n  if (ts < 0) {\n    ts = 0;\n    if (raf.frameLoop !== 'demand') {\n      nativeRaf(loop);\n    }\n  }\n}\nfunction stop() {\n  ts = -1;\n}\nfunction loop() {\n  if (~ts) {\n    nativeRaf(loop);\n    raf.batchedUpdates(update);\n  }\n}\nfunction update() {\n  let prevTs = ts;\n  ts = raf.now();\n  let count = findTimeout(ts);\n  if (count) {\n    eachSafely(timeouts.splice(0, count), t => t.handler());\n    pendingCount -= count;\n  }\n  if (!pendingCount) {\n    stop();\n    return;\n  }\n  onStartQueue.flush();\n  updateQueue.flush(prevTs ? Math.min(64, ts - prevTs) : 16.667);\n  onFrameQueue.flush();\n  writeQueue.flush();\n  onFinishQueue.flush();\n}\nfunction makeQueue() {\n  let next = new Set();\n  let current = next;\n  return {\n    add(fn) {\n      pendingCount += current == next && !next.has(fn) ? 1 : 0;\n      next.add(fn);\n    },\n    delete(fn) {\n      pendingCount -= current == next && next.has(fn) ? 1 : 0;\n      return next.delete(fn);\n    },\n    flush(arg) {\n      if (current.size) {\n        next = new Set();\n        pendingCount -= current.size;\n        eachSafely(current, fn => fn(arg) && next.add(fn));\n        pendingCount += next.size;\n        current = next;\n      }\n    }\n  };\n}\nfunction eachSafely(values, each) {\n  values.forEach(value => {\n    try {\n      each(value);\n    } catch (e) {\n      raf.catch(e);\n    }\n  });\n}\nconst __raf = {\n  count() {\n    return pendingCount;\n  },\n  isRunning() {\n    return ts >= 0;\n  },\n  clear() {\n    ts = -1;\n    timeouts = [];\n    onStartQueue = makeQueue();\n    updateQueue = makeQueue();\n    onFrameQueue = makeQueue();\n    writeQueue = makeQueue();\n    onFinishQueue = makeQueue();\n    pendingCount = 0;\n  }\n};\nexport { __raf, raf };", "map": {"version": 3, "names": ["updateQueue", "makeQueue", "raf", "fn", "schedule", "writeQueue", "write", "onStartQueue", "onStart", "onFrameQueue", "onFrame", "onFinishQueue", "onFinish", "timeouts", "setTimeout", "handler", "ms", "time", "now", "cancel", "i", "findIndex", "t", "splice", "pendingCount", "timeout", "findTimeout", "start", "length", "delete", "sync", "batchedUpdates", "throttle", "lastArgs", "queuedFn", "throttled", "args", "nativeRaf", "window", "requestAnimationFrame", "use", "impl", "performance", "Date", "catch", "console", "error", "frameLoop", "advance", "warn", "update", "ts", "queue", "add", "loop", "stop", "prevTs", "count", "eachSafely", "flush", "Math", "min", "next", "Set", "current", "has", "arg", "size", "values", "each", "for<PERSON>ach", "value", "e", "__raf", "isRunning", "clear"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-spring/rafz/dist/react-spring-rafz.esm.js"], "sourcesContent": ["let updateQueue = makeQueue();\nconst raf = fn => schedule(fn, updateQueue);\nlet writeQueue = makeQueue();\n\nraf.write = fn => schedule(fn, writeQueue);\n\nlet onStartQueue = makeQueue();\n\nraf.onStart = fn => schedule(fn, onStartQueue);\n\nlet onFrameQueue = makeQueue();\n\nraf.onFrame = fn => schedule(fn, onFrameQueue);\n\nlet onFinishQueue = makeQueue();\n\nraf.onFinish = fn => schedule(fn, onFinishQueue);\n\nlet timeouts = [];\n\nraf.setTimeout = (handler, ms) => {\n  let time = raf.now() + ms;\n\n  let cancel = () => {\n    let i = timeouts.findIndex(t => t.cancel == cancel);\n    if (~i) timeouts.splice(i, 1);\n    pendingCount -= ~i ? 1 : 0;\n  };\n\n  let timeout = {\n    time,\n    handler,\n    cancel\n  };\n  timeouts.splice(findTimeout(time), 0, timeout);\n  pendingCount += 1;\n  start();\n  return timeout;\n};\n\nlet findTimeout = time => ~(~timeouts.findIndex(t => t.time > time) || ~timeouts.length);\n\nraf.cancel = fn => {\n  onStartQueue.delete(fn);\n  onFrameQueue.delete(fn);\n  onFinishQueue.delete(fn);\n  updateQueue.delete(fn);\n  writeQueue.delete(fn);\n};\n\nraf.sync = fn => {\n  sync = true;\n  raf.batchedUpdates(fn);\n  sync = false;\n};\n\nraf.throttle = fn => {\n  let lastArgs;\n\n  function queuedFn() {\n    try {\n      fn(...lastArgs);\n    } finally {\n      lastArgs = null;\n    }\n  }\n\n  function throttled(...args) {\n    lastArgs = args;\n    raf.onStart(queuedFn);\n  }\n\n  throttled.handler = fn;\n\n  throttled.cancel = () => {\n    onStartQueue.delete(queuedFn);\n    lastArgs = null;\n  };\n\n  return throttled;\n};\n\nlet nativeRaf = typeof window != 'undefined' ? window.requestAnimationFrame : () => {};\n\nraf.use = impl => nativeRaf = impl;\n\nraf.now = typeof performance != 'undefined' ? () => performance.now() : Date.now;\n\nraf.batchedUpdates = fn => fn();\n\nraf.catch = console.error;\nraf.frameLoop = 'always';\n\nraf.advance = () => {\n  if (raf.frameLoop !== 'demand') {\n    console.warn('Cannot call the manual advancement of rafz whilst frameLoop is not set as demand');\n  } else {\n    update();\n  }\n};\n\nlet ts = -1;\nlet pendingCount = 0;\nlet sync = false;\n\nfunction schedule(fn, queue) {\n  if (sync) {\n    queue.delete(fn);\n    fn(0);\n  } else {\n    queue.add(fn);\n    start();\n  }\n}\n\nfunction start() {\n  if (ts < 0) {\n    ts = 0;\n\n    if (raf.frameLoop !== 'demand') {\n      nativeRaf(loop);\n    }\n  }\n}\n\nfunction stop() {\n  ts = -1;\n}\n\nfunction loop() {\n  if (~ts) {\n    nativeRaf(loop);\n    raf.batchedUpdates(update);\n  }\n}\n\nfunction update() {\n  let prevTs = ts;\n  ts = raf.now();\n  let count = findTimeout(ts);\n\n  if (count) {\n    eachSafely(timeouts.splice(0, count), t => t.handler());\n    pendingCount -= count;\n  }\n\n  if (!pendingCount) {\n    stop();\n    return;\n  }\n\n  onStartQueue.flush();\n  updateQueue.flush(prevTs ? Math.min(64, ts - prevTs) : 16.667);\n  onFrameQueue.flush();\n  writeQueue.flush();\n  onFinishQueue.flush();\n}\n\nfunction makeQueue() {\n  let next = new Set();\n  let current = next;\n  return {\n    add(fn) {\n      pendingCount += current == next && !next.has(fn) ? 1 : 0;\n      next.add(fn);\n    },\n\n    delete(fn) {\n      pendingCount -= current == next && next.has(fn) ? 1 : 0;\n      return next.delete(fn);\n    },\n\n    flush(arg) {\n      if (current.size) {\n        next = new Set();\n        pendingCount -= current.size;\n        eachSafely(current, fn => fn(arg) && next.add(fn));\n        pendingCount += next.size;\n        current = next;\n      }\n    }\n\n  };\n}\n\nfunction eachSafely(values, each) {\n  values.forEach(value => {\n    try {\n      each(value);\n    } catch (e) {\n      raf.catch(e);\n    }\n  });\n}\n\nconst __raf = {\n  count() {\n    return pendingCount;\n  },\n\n  isRunning() {\n    return ts >= 0;\n  },\n\n  clear() {\n    ts = -1;\n    timeouts = [];\n    onStartQueue = makeQueue();\n    updateQueue = makeQueue();\n    onFrameQueue = makeQueue();\n    writeQueue = makeQueue();\n    onFinishQueue = makeQueue();\n    pendingCount = 0;\n  }\n\n};\n\nexport { __raf, raf };\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,SAAS,CAAC,CAAC;AAC7B,MAAMC,GAAG,GAAGC,EAAE,IAAIC,QAAQ,CAACD,EAAE,EAAEH,WAAW,CAAC;AAC3C,IAAIK,UAAU,GAAGJ,SAAS,CAAC,CAAC;AAE5BC,GAAG,CAACI,KAAK,GAAGH,EAAE,IAAIC,QAAQ,CAACD,EAAE,EAAEE,UAAU,CAAC;AAE1C,IAAIE,YAAY,GAAGN,SAAS,CAAC,CAAC;AAE9BC,GAAG,CAACM,OAAO,GAAGL,EAAE,IAAIC,QAAQ,CAACD,EAAE,EAAEI,YAAY,CAAC;AAE9C,IAAIE,YAAY,GAAGR,SAAS,CAAC,CAAC;AAE9BC,GAAG,CAACQ,OAAO,GAAGP,EAAE,IAAIC,QAAQ,CAACD,EAAE,EAAEM,YAAY,CAAC;AAE9C,IAAIE,aAAa,GAAGV,SAAS,CAAC,CAAC;AAE/BC,GAAG,CAACU,QAAQ,GAAGT,EAAE,IAAIC,QAAQ,CAACD,EAAE,EAAEQ,aAAa,CAAC;AAEhD,IAAIE,QAAQ,GAAG,EAAE;AAEjBX,GAAG,CAACY,UAAU,GAAG,CAACC,OAAO,EAAEC,EAAE,KAAK;EAChC,IAAIC,IAAI,GAAGf,GAAG,CAACgB,GAAG,CAAC,CAAC,GAAGF,EAAE;EAEzB,IAAIG,MAAM,GAAGA,CAAA,KAAM;IACjB,IAAIC,CAAC,GAAGP,QAAQ,CAACQ,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACH,MAAM,IAAIA,MAAM,CAAC;IACnD,IAAI,CAACC,CAAC,EAAEP,QAAQ,CAACU,MAAM,CAACH,CAAC,EAAE,CAAC,CAAC;IAC7BI,YAAY,IAAI,CAACJ,CAAC,GAAG,CAAC,GAAG,CAAC;EAC5B,CAAC;EAED,IAAIK,OAAO,GAAG;IACZR,IAAI;IACJF,OAAO;IACPI;EACF,CAAC;EACDN,QAAQ,CAACU,MAAM,CAACG,WAAW,CAACT,IAAI,CAAC,EAAE,CAAC,EAAEQ,OAAO,CAAC;EAC9CD,YAAY,IAAI,CAAC;EACjBG,KAAK,CAAC,CAAC;EACP,OAAOF,OAAO;AAChB,CAAC;AAED,IAAIC,WAAW,GAAGT,IAAI,IAAI,EAAE,CAACJ,QAAQ,CAACQ,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACL,IAAI,GAAGA,IAAI,CAAC,IAAI,CAACJ,QAAQ,CAACe,MAAM,CAAC;AAExF1B,GAAG,CAACiB,MAAM,GAAGhB,EAAE,IAAI;EACjBI,YAAY,CAACsB,MAAM,CAAC1B,EAAE,CAAC;EACvBM,YAAY,CAACoB,MAAM,CAAC1B,EAAE,CAAC;EACvBQ,aAAa,CAACkB,MAAM,CAAC1B,EAAE,CAAC;EACxBH,WAAW,CAAC6B,MAAM,CAAC1B,EAAE,CAAC;EACtBE,UAAU,CAACwB,MAAM,CAAC1B,EAAE,CAAC;AACvB,CAAC;AAEDD,GAAG,CAAC4B,IAAI,GAAG3B,EAAE,IAAI;EACf2B,IAAI,GAAG,IAAI;EACX5B,GAAG,CAAC6B,cAAc,CAAC5B,EAAE,CAAC;EACtB2B,IAAI,GAAG,KAAK;AACd,CAAC;AAED5B,GAAG,CAAC8B,QAAQ,GAAG7B,EAAE,IAAI;EACnB,IAAI8B,QAAQ;EAEZ,SAASC,QAAQA,CAAA,EAAG;IAClB,IAAI;MACF/B,EAAE,CAAC,GAAG8B,QAAQ,CAAC;IACjB,CAAC,SAAS;MACRA,QAAQ,GAAG,IAAI;IACjB;EACF;EAEA,SAASE,SAASA,CAAC,GAAGC,IAAI,EAAE;IAC1BH,QAAQ,GAAGG,IAAI;IACflC,GAAG,CAACM,OAAO,CAAC0B,QAAQ,CAAC;EACvB;EAEAC,SAAS,CAACpB,OAAO,GAAGZ,EAAE;EAEtBgC,SAAS,CAAChB,MAAM,GAAG,MAAM;IACvBZ,YAAY,CAACsB,MAAM,CAACK,QAAQ,CAAC;IAC7BD,QAAQ,GAAG,IAAI;EACjB,CAAC;EAED,OAAOE,SAAS;AAClB,CAAC;AAED,IAAIE,SAAS,GAAG,OAAOC,MAAM,IAAI,WAAW,GAAGA,MAAM,CAACC,qBAAqB,GAAG,MAAM,CAAC,CAAC;AAEtFrC,GAAG,CAACsC,GAAG,GAAGC,IAAI,IAAIJ,SAAS,GAAGI,IAAI;AAElCvC,GAAG,CAACgB,GAAG,GAAG,OAAOwB,WAAW,IAAI,WAAW,GAAG,MAAMA,WAAW,CAACxB,GAAG,CAAC,CAAC,GAAGyB,IAAI,CAACzB,GAAG;AAEhFhB,GAAG,CAAC6B,cAAc,GAAG5B,EAAE,IAAIA,EAAE,CAAC,CAAC;AAE/BD,GAAG,CAAC0C,KAAK,GAAGC,OAAO,CAACC,KAAK;AACzB5C,GAAG,CAAC6C,SAAS,GAAG,QAAQ;AAExB7C,GAAG,CAAC8C,OAAO,GAAG,MAAM;EAClB,IAAI9C,GAAG,CAAC6C,SAAS,KAAK,QAAQ,EAAE;IAC9BF,OAAO,CAACI,IAAI,CAAC,kFAAkF,CAAC;EAClG,CAAC,MAAM;IACLC,MAAM,CAAC,CAAC;EACV;AACF,CAAC;AAED,IAAIC,EAAE,GAAG,CAAC,CAAC;AACX,IAAI3B,YAAY,GAAG,CAAC;AACpB,IAAIM,IAAI,GAAG,KAAK;AAEhB,SAAS1B,QAAQA,CAACD,EAAE,EAAEiD,KAAK,EAAE;EAC3B,IAAItB,IAAI,EAAE;IACRsB,KAAK,CAACvB,MAAM,CAAC1B,EAAE,CAAC;IAChBA,EAAE,CAAC,CAAC,CAAC;EACP,CAAC,MAAM;IACLiD,KAAK,CAACC,GAAG,CAAClD,EAAE,CAAC;IACbwB,KAAK,CAAC,CAAC;EACT;AACF;AAEA,SAASA,KAAKA,CAAA,EAAG;EACf,IAAIwB,EAAE,GAAG,CAAC,EAAE;IACVA,EAAE,GAAG,CAAC;IAEN,IAAIjD,GAAG,CAAC6C,SAAS,KAAK,QAAQ,EAAE;MAC9BV,SAAS,CAACiB,IAAI,CAAC;IACjB;EACF;AACF;AAEA,SAASC,IAAIA,CAAA,EAAG;EACdJ,EAAE,GAAG,CAAC,CAAC;AACT;AAEA,SAASG,IAAIA,CAAA,EAAG;EACd,IAAI,CAACH,EAAE,EAAE;IACPd,SAAS,CAACiB,IAAI,CAAC;IACfpD,GAAG,CAAC6B,cAAc,CAACmB,MAAM,CAAC;EAC5B;AACF;AAEA,SAASA,MAAMA,CAAA,EAAG;EAChB,IAAIM,MAAM,GAAGL,EAAE;EACfA,EAAE,GAAGjD,GAAG,CAACgB,GAAG,CAAC,CAAC;EACd,IAAIuC,KAAK,GAAG/B,WAAW,CAACyB,EAAE,CAAC;EAE3B,IAAIM,KAAK,EAAE;IACTC,UAAU,CAAC7C,QAAQ,CAACU,MAAM,CAAC,CAAC,EAAEkC,KAAK,CAAC,EAAEnC,CAAC,IAAIA,CAAC,CAACP,OAAO,CAAC,CAAC,CAAC;IACvDS,YAAY,IAAIiC,KAAK;EACvB;EAEA,IAAI,CAACjC,YAAY,EAAE;IACjB+B,IAAI,CAAC,CAAC;IACN;EACF;EAEAhD,YAAY,CAACoD,KAAK,CAAC,CAAC;EACpB3D,WAAW,CAAC2D,KAAK,CAACH,MAAM,GAAGI,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEV,EAAE,GAAGK,MAAM,CAAC,GAAG,MAAM,CAAC;EAC9D/C,YAAY,CAACkD,KAAK,CAAC,CAAC;EACpBtD,UAAU,CAACsD,KAAK,CAAC,CAAC;EAClBhD,aAAa,CAACgD,KAAK,CAAC,CAAC;AACvB;AAEA,SAAS1D,SAASA,CAAA,EAAG;EACnB,IAAI6D,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;EACpB,IAAIC,OAAO,GAAGF,IAAI;EAClB,OAAO;IACLT,GAAGA,CAAClD,EAAE,EAAE;MACNqB,YAAY,IAAIwC,OAAO,IAAIF,IAAI,IAAI,CAACA,IAAI,CAACG,GAAG,CAAC9D,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;MACxD2D,IAAI,CAACT,GAAG,CAAClD,EAAE,CAAC;IACd,CAAC;IAED0B,MAAMA,CAAC1B,EAAE,EAAE;MACTqB,YAAY,IAAIwC,OAAO,IAAIF,IAAI,IAAIA,IAAI,CAACG,GAAG,CAAC9D,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;MACvD,OAAO2D,IAAI,CAACjC,MAAM,CAAC1B,EAAE,CAAC;IACxB,CAAC;IAEDwD,KAAKA,CAACO,GAAG,EAAE;MACT,IAAIF,OAAO,CAACG,IAAI,EAAE;QAChBL,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;QAChBvC,YAAY,IAAIwC,OAAO,CAACG,IAAI;QAC5BT,UAAU,CAACM,OAAO,EAAE7D,EAAE,IAAIA,EAAE,CAAC+D,GAAG,CAAC,IAAIJ,IAAI,CAACT,GAAG,CAAClD,EAAE,CAAC,CAAC;QAClDqB,YAAY,IAAIsC,IAAI,CAACK,IAAI;QACzBH,OAAO,GAAGF,IAAI;MAChB;IACF;EAEF,CAAC;AACH;AAEA,SAASJ,UAAUA,CAACU,MAAM,EAAEC,IAAI,EAAE;EAChCD,MAAM,CAACE,OAAO,CAACC,KAAK,IAAI;IACtB,IAAI;MACFF,IAAI,CAACE,KAAK,CAAC;IACb,CAAC,CAAC,OAAOC,CAAC,EAAE;MACVtE,GAAG,CAAC0C,KAAK,CAAC4B,CAAC,CAAC;IACd;EACF,CAAC,CAAC;AACJ;AAEA,MAAMC,KAAK,GAAG;EACZhB,KAAKA,CAAA,EAAG;IACN,OAAOjC,YAAY;EACrB,CAAC;EAEDkD,SAASA,CAAA,EAAG;IACV,OAAOvB,EAAE,IAAI,CAAC;EAChB,CAAC;EAEDwB,KAAKA,CAAA,EAAG;IACNxB,EAAE,GAAG,CAAC,CAAC;IACPtC,QAAQ,GAAG,EAAE;IACbN,YAAY,GAAGN,SAAS,CAAC,CAAC;IAC1BD,WAAW,GAAGC,SAAS,CAAC,CAAC;IACzBQ,YAAY,GAAGR,SAAS,CAAC,CAAC;IAC1BI,UAAU,GAAGJ,SAAS,CAAC,CAAC;IACxBU,aAAa,GAAGV,SAAS,CAAC,CAAC;IAC3BuB,YAAY,GAAG,CAAC;EAClB;AAEF,CAAC;AAED,SAASiD,KAAK,EAAEvE,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}