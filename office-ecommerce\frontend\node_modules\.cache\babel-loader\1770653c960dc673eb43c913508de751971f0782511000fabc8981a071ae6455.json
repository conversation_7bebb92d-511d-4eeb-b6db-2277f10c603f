{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { extend } from '@react-three/fiber';\nimport { DiscardMaterial } from '../materials/DiscardMaterial.js';\nconst MeshDiscardMaterial = /*#__PURE__*/React.forwardRef((props, fref) => {\n  extend({\n    DiscardMaterialImpl: DiscardMaterial\n  });\n  return /*#__PURE__*/React.createElement(\"discardMaterialImpl\", _extends({\n    ref: fref\n  }, props));\n});\nexport { MeshDiscardMaterial };", "map": {"version": 3, "names": ["_extends", "React", "extend", "DiscardMaterial", "MeshDiscardMaterial", "forwardRef", "props", "fref", "DiscardMaterialImpl", "createElement", "ref"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/MeshDiscardMaterial.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { extend } from '@react-three/fiber';\nimport { DiscardMaterial } from '../materials/DiscardMaterial.js';\n\nconst MeshDiscardMaterial = /*#__PURE__*/React.forwardRef((props, fref) => {\n  extend({\n    DiscardMaterialImpl: DiscardMaterial\n  });\n  return /*#__PURE__*/React.createElement(\"discardMaterialImpl\", _extends({\n    ref: fref\n  }, props));\n});\n\nexport { MeshDiscardMaterial };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,eAAe,QAAQ,iCAAiC;AAEjE,MAAMC,mBAAmB,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAK;EACzEL,MAAM,CAAC;IACLM,mBAAmB,EAAEL;EACvB,CAAC,CAAC;EACF,OAAO,aAAaF,KAAK,CAACQ,aAAa,CAAC,qBAAqB,EAAET,QAAQ,CAAC;IACtEU,GAAG,EAAEH;EACP,CAAC,EAAED,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF,SAASF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}