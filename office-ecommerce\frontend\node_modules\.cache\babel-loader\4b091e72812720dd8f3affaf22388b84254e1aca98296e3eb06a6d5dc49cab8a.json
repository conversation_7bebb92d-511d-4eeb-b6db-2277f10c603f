{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { shaderMaterial } from './shaderMaterial.js';\nconst SparklesImplMaterial = shaderMaterial({\n  time: 0,\n  pixelRatio: 1\n}, ` uniform float pixelRatio;\n    uniform float time;\n    attribute float size;  \n    attribute float speed;  \n    attribute float opacity;\n    attribute vec3 noise;\n    attribute vec3 color;\n    varying vec3 vColor;\n    varying float vOpacity;\n    void main() {\n      vec4 modelPosition = modelMatrix * vec4(position, 1.0);\n      modelPosition.y += sin(time * speed + modelPosition.x * noise.x * 100.0) * 0.2;\n      modelPosition.z += cos(time * speed + modelPosition.x * noise.y * 100.0) * 0.2;\n      modelPosition.x += cos(time * speed + modelPosition.x * noise.z * 100.0) * 0.2;\n      vec4 viewPosition = viewMatrix * modelPosition;\n      vec4 projectionPostion = projectionMatrix * viewPosition;\n      gl_Position = projectionPostion;\n      gl_PointSize = size * 25. * pixelRatio;\n      gl_PointSize *= (1.0 / - viewPosition.z);\n      vColor = color;\n      vOpacity = opacity;\n    }`, ` varying vec3 vColor;\n    varying float vOpacity;\n    void main() {\n      float distanceToCenter = distance(gl_PointCoord, vec2(0.5));\n      float strength = 0.05 / distanceToCenter - 0.1;\n      gl_FragColor = vec4(vColor, strength * vOpacity);\n      #include <tonemapping_fragment>\n      #include <encodings_fragment>\n    }`);\nconst isFloat32Array = def => def && def.constructor === Float32Array;\nconst expandColor = v => [v.r, v.g, v.b];\nconst isVector = v => v instanceof THREE.Vector2 || v instanceof THREE.Vector3 || v instanceof THREE.Vector4;\nconst normalizeVector = v => {\n  if (Array.isArray(v)) return v;else if (isVector(v)) return v.toArray();\n  return [v, v, v];\n};\nfunction usePropAsIsOrAsAttribute(count, prop, setDefault) {\n  return React.useMemo(() => {\n    if (prop !== undefined) {\n      if (isFloat32Array(prop)) {\n        return prop;\n      } else {\n        if (prop instanceof THREE.Color) {\n          const a = Array.from({\n            length: count * 3\n          }, () => expandColor(prop)).flat();\n          return Float32Array.from(a);\n        } else if (isVector(prop) || Array.isArray(prop)) {\n          const a = Array.from({\n            length: count * 3\n          }, () => normalizeVector(prop)).flat();\n          return Float32Array.from(a);\n        }\n        return Float32Array.from({\n          length: count\n        }, () => prop);\n      }\n    }\n    return Float32Array.from({\n      length: count\n    }, setDefault);\n  }, [prop]);\n}\nconst Sparkles = /*#__PURE__*/React.forwardRef(({\n  noise = 1,\n  count = 100,\n  speed = 1,\n  opacity = 1,\n  scale = 1,\n  size,\n  color,\n  children,\n  ...props\n}, forwardRef) => {\n  React.useMemo(() => extend({\n    SparklesImplMaterial\n  }), []);\n  const ref = React.useRef(null);\n  const dpr = useThree(state => state.viewport.dpr);\n  const _scale = normalizeVector(scale);\n  const positions = React.useMemo(() => Float32Array.from(Array.from({\n    length: count\n  }, () => _scale.map(THREE.MathUtils.randFloatSpread)).flat()), [count, ..._scale]);\n  const sizes = usePropAsIsOrAsAttribute(count, size, Math.random);\n  const opacities = usePropAsIsOrAsAttribute(count, opacity);\n  const speeds = usePropAsIsOrAsAttribute(count, speed);\n  const noises = usePropAsIsOrAsAttribute(count * 3, noise);\n  const colors = usePropAsIsOrAsAttribute(color === undefined ? count * 3 : count, !isFloat32Array(color) ? new THREE.Color(color) : color, () => 1);\n  useFrame(state => {\n    if (ref.current && ref.current.material) ref.current.material.time = state.clock.elapsedTime;\n  });\n  React.useImperativeHandle(forwardRef, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"points\", _extends({\n    key: `particle-${count}-${JSON.stringify(scale)}`\n  }, props, {\n    ref: ref\n  }), /*#__PURE__*/React.createElement(\"bufferGeometry\", null, /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-position\",\n    args: [positions, 3]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-size\",\n    args: [sizes, 1]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-opacity\",\n    args: [opacities, 1]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-speed\",\n    args: [speeds, 1]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-color\",\n    args: [colors, 3]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-noise\",\n    args: [noises, 3]\n  })), children ? children : /*#__PURE__*/React.createElement(\"sparklesImplMaterial\", {\n    transparent: true,\n    pixelRatio: dpr,\n    depthWrite: false\n  }));\n});\nexport { Sparkles };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "extend", "useThree", "useFrame", "shaderMaterial", "SparklesImplMaterial", "time", "pixelRatio", "isFloat32Array", "def", "constructor", "Float32Array", "expandColor", "v", "r", "g", "b", "isVector", "Vector2", "Vector3", "Vector4", "normalizeVector", "Array", "isArray", "toArray", "usePropAsIsOrAsAttribute", "count", "prop", "<PERSON><PERSON><PERSON><PERSON>", "useMemo", "undefined", "Color", "a", "from", "length", "flat", "<PERSON><PERSON><PERSON>", "forwardRef", "noise", "speed", "opacity", "scale", "size", "color", "children", "props", "ref", "useRef", "dpr", "state", "viewport", "_scale", "positions", "map", "MathUtils", "randFloatSpread", "sizes", "Math", "random", "opacities", "speeds", "noises", "colors", "current", "material", "clock", "elapsedTime", "useImperativeHandle", "createElement", "key", "JSON", "stringify", "attach", "args", "transparent", "depthWrite"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Sparkles.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { shaderMaterial } from './shaderMaterial.js';\n\nconst SparklesImplMaterial = shaderMaterial({\n  time: 0,\n  pixelRatio: 1\n}, ` uniform float pixelRatio;\n    uniform float time;\n    attribute float size;  \n    attribute float speed;  \n    attribute float opacity;\n    attribute vec3 noise;\n    attribute vec3 color;\n    varying vec3 vColor;\n    varying float vOpacity;\n    void main() {\n      vec4 modelPosition = modelMatrix * vec4(position, 1.0);\n      modelPosition.y += sin(time * speed + modelPosition.x * noise.x * 100.0) * 0.2;\n      modelPosition.z += cos(time * speed + modelPosition.x * noise.y * 100.0) * 0.2;\n      modelPosition.x += cos(time * speed + modelPosition.x * noise.z * 100.0) * 0.2;\n      vec4 viewPosition = viewMatrix * modelPosition;\n      vec4 projectionPostion = projectionMatrix * viewPosition;\n      gl_Position = projectionPostion;\n      gl_PointSize = size * 25. * pixelRatio;\n      gl_PointSize *= (1.0 / - viewPosition.z);\n      vColor = color;\n      vOpacity = opacity;\n    }`, ` varying vec3 vColor;\n    varying float vOpacity;\n    void main() {\n      float distanceToCenter = distance(gl_PointCoord, vec2(0.5));\n      float strength = 0.05 / distanceToCenter - 0.1;\n      gl_FragColor = vec4(vColor, strength * vOpacity);\n      #include <tonemapping_fragment>\n      #include <encodings_fragment>\n    }`);\n\nconst isFloat32Array = def => def && def.constructor === Float32Array;\n\nconst expandColor = v => [v.r, v.g, v.b];\n\nconst isVector = v => v instanceof THREE.Vector2 || v instanceof THREE.Vector3 || v instanceof THREE.Vector4;\n\nconst normalizeVector = v => {\n  if (Array.isArray(v)) return v;else if (isVector(v)) return v.toArray();\n  return [v, v, v];\n};\n\nfunction usePropAsIsOrAsAttribute(count, prop, setDefault) {\n  return React.useMemo(() => {\n    if (prop !== undefined) {\n      if (isFloat32Array(prop)) {\n        return prop;\n      } else {\n        if (prop instanceof THREE.Color) {\n          const a = Array.from({\n            length: count * 3\n          }, () => expandColor(prop)).flat();\n          return Float32Array.from(a);\n        } else if (isVector(prop) || Array.isArray(prop)) {\n          const a = Array.from({\n            length: count * 3\n          }, () => normalizeVector(prop)).flat();\n          return Float32Array.from(a);\n        }\n\n        return Float32Array.from({\n          length: count\n        }, () => prop);\n      }\n    }\n\n    return Float32Array.from({\n      length: count\n    }, setDefault);\n  }, [prop]);\n}\n\nconst Sparkles = /*#__PURE__*/React.forwardRef(({\n  noise = 1,\n  count = 100,\n  speed = 1,\n  opacity = 1,\n  scale = 1,\n  size,\n  color,\n  children,\n  ...props\n}, forwardRef) => {\n  React.useMemo(() => extend({\n    SparklesImplMaterial\n  }), []);\n  const ref = React.useRef(null);\n  const dpr = useThree(state => state.viewport.dpr);\n\n  const _scale = normalizeVector(scale);\n\n  const positions = React.useMemo(() => Float32Array.from(Array.from({\n    length: count\n  }, () => _scale.map(THREE.MathUtils.randFloatSpread)).flat()), [count, ..._scale]);\n  const sizes = usePropAsIsOrAsAttribute(count, size, Math.random);\n  const opacities = usePropAsIsOrAsAttribute(count, opacity);\n  const speeds = usePropAsIsOrAsAttribute(count, speed);\n  const noises = usePropAsIsOrAsAttribute(count * 3, noise);\n  const colors = usePropAsIsOrAsAttribute(color === undefined ? count * 3 : count, !isFloat32Array(color) ? new THREE.Color(color) : color, () => 1);\n  useFrame(state => {\n    if (ref.current && ref.current.material) ref.current.material.time = state.clock.elapsedTime;\n  });\n  React.useImperativeHandle(forwardRef, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"points\", _extends({\n    key: `particle-${count}-${JSON.stringify(scale)}`\n  }, props, {\n    ref: ref\n  }), /*#__PURE__*/React.createElement(\"bufferGeometry\", null, /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-position\",\n    args: [positions, 3]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-size\",\n    args: [sizes, 1]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-opacity\",\n    args: [opacities, 1]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-speed\",\n    args: [speeds, 1]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-color\",\n    args: [colors, 3]\n  }), /*#__PURE__*/React.createElement(\"bufferAttribute\", {\n    attach: \"attributes-noise\",\n    args: [noises, 3]\n  })), children ? children : /*#__PURE__*/React.createElement(\"sparklesImplMaterial\", {\n    transparent: true,\n    pixelRatio: dpr,\n    depthWrite: false\n  }));\n});\n\nexport { Sparkles };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAC/D,SAASC,cAAc,QAAQ,qBAAqB;AAEpD,MAAMC,oBAAoB,GAAGD,cAAc,CAAC;EAC1CE,IAAI,EAAE,CAAC;EACPC,UAAU,EAAE;AACd,CAAC,EAAE;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,EAAE;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,CAAC;AAEP,MAAMC,cAAc,GAAGC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACC,WAAW,KAAKC,YAAY;AAErE,MAAMC,WAAW,GAAGC,CAAC,IAAI,CAACA,CAAC,CAACC,CAAC,EAAED,CAAC,CAACE,CAAC,EAAEF,CAAC,CAACG,CAAC,CAAC;AAExC,MAAMC,QAAQ,GAAGJ,CAAC,IAAIA,CAAC,YAAYb,KAAK,CAACkB,OAAO,IAAIL,CAAC,YAAYb,KAAK,CAACmB,OAAO,IAAIN,CAAC,YAAYb,KAAK,CAACoB,OAAO;AAE5G,MAAMC,eAAe,GAAGR,CAAC,IAAI;EAC3B,IAAIS,KAAK,CAACC,OAAO,CAACV,CAAC,CAAC,EAAE,OAAOA,CAAC,CAAC,KAAK,IAAII,QAAQ,CAACJ,CAAC,CAAC,EAAE,OAAOA,CAAC,CAACW,OAAO,CAAC,CAAC;EACvE,OAAO,CAACX,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC;AAClB,CAAC;AAED,SAASY,wBAAwBA,CAACC,KAAK,EAAEC,IAAI,EAAEC,UAAU,EAAE;EACzD,OAAO7B,KAAK,CAAC8B,OAAO,CAAC,MAAM;IACzB,IAAIF,IAAI,KAAKG,SAAS,EAAE;MACtB,IAAItB,cAAc,CAACmB,IAAI,CAAC,EAAE;QACxB,OAAOA,IAAI;MACb,CAAC,MAAM;QACL,IAAIA,IAAI,YAAY3B,KAAK,CAAC+B,KAAK,EAAE;UAC/B,MAAMC,CAAC,GAAGV,KAAK,CAACW,IAAI,CAAC;YACnBC,MAAM,EAAER,KAAK,GAAG;UAClB,CAAC,EAAE,MAAMd,WAAW,CAACe,IAAI,CAAC,CAAC,CAACQ,IAAI,CAAC,CAAC;UAClC,OAAOxB,YAAY,CAACsB,IAAI,CAACD,CAAC,CAAC;QAC7B,CAAC,MAAM,IAAIf,QAAQ,CAACU,IAAI,CAAC,IAAIL,KAAK,CAACC,OAAO,CAACI,IAAI,CAAC,EAAE;UAChD,MAAMK,CAAC,GAAGV,KAAK,CAACW,IAAI,CAAC;YACnBC,MAAM,EAAER,KAAK,GAAG;UAClB,CAAC,EAAE,MAAML,eAAe,CAACM,IAAI,CAAC,CAAC,CAACQ,IAAI,CAAC,CAAC;UACtC,OAAOxB,YAAY,CAACsB,IAAI,CAACD,CAAC,CAAC;QAC7B;QAEA,OAAOrB,YAAY,CAACsB,IAAI,CAAC;UACvBC,MAAM,EAAER;QACV,CAAC,EAAE,MAAMC,IAAI,CAAC;MAChB;IACF;IAEA,OAAOhB,YAAY,CAACsB,IAAI,CAAC;MACvBC,MAAM,EAAER;IACV,CAAC,EAAEE,UAAU,CAAC;EAChB,CAAC,EAAE,CAACD,IAAI,CAAC,CAAC;AACZ;AAEA,MAAMS,QAAQ,GAAG,aAAarC,KAAK,CAACsC,UAAU,CAAC,CAAC;EAC9CC,KAAK,GAAG,CAAC;EACTZ,KAAK,GAAG,GAAG;EACXa,KAAK,GAAG,CAAC;EACTC,OAAO,GAAG,CAAC;EACXC,KAAK,GAAG,CAAC;EACTC,IAAI;EACJC,KAAK;EACLC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAER,UAAU,KAAK;EAChBtC,KAAK,CAAC8B,OAAO,CAAC,MAAM5B,MAAM,CAAC;IACzBI;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,MAAMyC,GAAG,GAAG/C,KAAK,CAACgD,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,GAAG,GAAG9C,QAAQ,CAAC+C,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAACF,GAAG,CAAC;EAEjD,MAAMG,MAAM,GAAG9B,eAAe,CAACoB,KAAK,CAAC;EAErC,MAAMW,SAAS,GAAGrD,KAAK,CAAC8B,OAAO,CAAC,MAAMlB,YAAY,CAACsB,IAAI,CAACX,KAAK,CAACW,IAAI,CAAC;IACjEC,MAAM,EAAER;EACV,CAAC,EAAE,MAAMyB,MAAM,CAACE,GAAG,CAACrD,KAAK,CAACsD,SAAS,CAACC,eAAe,CAAC,CAAC,CAACpB,IAAI,CAAC,CAAC,CAAC,EAAE,CAACT,KAAK,EAAE,GAAGyB,MAAM,CAAC,CAAC;EAClF,MAAMK,KAAK,GAAG/B,wBAAwB,CAACC,KAAK,EAAEgB,IAAI,EAAEe,IAAI,CAACC,MAAM,CAAC;EAChE,MAAMC,SAAS,GAAGlC,wBAAwB,CAACC,KAAK,EAAEc,OAAO,CAAC;EAC1D,MAAMoB,MAAM,GAAGnC,wBAAwB,CAACC,KAAK,EAAEa,KAAK,CAAC;EACrD,MAAMsB,MAAM,GAAGpC,wBAAwB,CAACC,KAAK,GAAG,CAAC,EAAEY,KAAK,CAAC;EACzD,MAAMwB,MAAM,GAAGrC,wBAAwB,CAACkB,KAAK,KAAKb,SAAS,GAAGJ,KAAK,GAAG,CAAC,GAAGA,KAAK,EAAE,CAAClB,cAAc,CAACmC,KAAK,CAAC,GAAG,IAAI3C,KAAK,CAAC+B,KAAK,CAACY,KAAK,CAAC,GAAGA,KAAK,EAAE,MAAM,CAAC,CAAC;EAClJxC,QAAQ,CAAC8C,KAAK,IAAI;IAChB,IAAIH,GAAG,CAACiB,OAAO,IAAIjB,GAAG,CAACiB,OAAO,CAACC,QAAQ,EAAElB,GAAG,CAACiB,OAAO,CAACC,QAAQ,CAAC1D,IAAI,GAAG2C,KAAK,CAACgB,KAAK,CAACC,WAAW;EAC9F,CAAC,CAAC;EACFnE,KAAK,CAACoE,mBAAmB,CAAC9B,UAAU,EAAE,MAAMS,GAAG,CAACiB,OAAO,EAAE,EAAE,CAAC;EAC5D,OAAO,aAAahE,KAAK,CAACqE,aAAa,CAAC,QAAQ,EAAEtE,QAAQ,CAAC;IACzDuE,GAAG,EAAE,YAAY3C,KAAK,IAAI4C,IAAI,CAACC,SAAS,CAAC9B,KAAK,CAAC;EACjD,CAAC,EAAEI,KAAK,EAAE;IACRC,GAAG,EAAEA;EACP,CAAC,CAAC,EAAE,aAAa/C,KAAK,CAACqE,aAAa,CAAC,gBAAgB,EAAE,IAAI,EAAE,aAAarE,KAAK,CAACqE,aAAa,CAAC,iBAAiB,EAAE;IAC/GI,MAAM,EAAE,qBAAqB;IAC7BC,IAAI,EAAE,CAACrB,SAAS,EAAE,CAAC;EACrB,CAAC,CAAC,EAAE,aAAarD,KAAK,CAACqE,aAAa,CAAC,iBAAiB,EAAE;IACtDI,MAAM,EAAE,iBAAiB;IACzBC,IAAI,EAAE,CAACjB,KAAK,EAAE,CAAC;EACjB,CAAC,CAAC,EAAE,aAAazD,KAAK,CAACqE,aAAa,CAAC,iBAAiB,EAAE;IACtDI,MAAM,EAAE,oBAAoB;IAC5BC,IAAI,EAAE,CAACd,SAAS,EAAE,CAAC;EACrB,CAAC,CAAC,EAAE,aAAa5D,KAAK,CAACqE,aAAa,CAAC,iBAAiB,EAAE;IACtDI,MAAM,EAAE,kBAAkB;IAC1BC,IAAI,EAAE,CAACb,MAAM,EAAE,CAAC;EAClB,CAAC,CAAC,EAAE,aAAa7D,KAAK,CAACqE,aAAa,CAAC,iBAAiB,EAAE;IACtDI,MAAM,EAAE,kBAAkB;IAC1BC,IAAI,EAAE,CAACX,MAAM,EAAE,CAAC;EAClB,CAAC,CAAC,EAAE,aAAa/D,KAAK,CAACqE,aAAa,CAAC,iBAAiB,EAAE;IACtDI,MAAM,EAAE,kBAAkB;IAC1BC,IAAI,EAAE,CAACZ,MAAM,EAAE,CAAC;EAClB,CAAC,CAAC,CAAC,EAAEjB,QAAQ,GAAGA,QAAQ,GAAG,aAAa7C,KAAK,CAACqE,aAAa,CAAC,sBAAsB,EAAE;IAClFM,WAAW,EAAE,IAAI;IACjBnE,UAAU,EAAEyC,GAAG;IACf2B,UAAU,EAAE;EACd,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASvC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}