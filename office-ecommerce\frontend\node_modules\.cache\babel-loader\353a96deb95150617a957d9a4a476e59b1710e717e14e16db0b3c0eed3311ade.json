{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom/client';\nimport { Vector3, DoubleSide, OrthographicCamera, PerspectiveCamera } from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\nconst v1 = new Vector3();\nconst v2 = new Vector3();\nconst v3 = new Vector3();\nfunction defaultCalculatePosition(el, camera, size) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  objectPos.project(camera);\n  const widthHalf = size.width / 2;\n  const heightHalf = size.height / 2;\n  return [objectPos.x * widthHalf + widthHalf, -(objectPos.y * heightHalf) + heightHalf];\n}\nfunction isObjectBehindCamera(el, camera) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n  const deltaCamObj = objectPos.sub(cameraPos);\n  const camDir = camera.getWorldDirection(v3);\n  return deltaCamObj.angleTo(camDir) > Math.PI / 2;\n}\nfunction isObjectVisible(el, camera, raycaster, occlude) {\n  const elPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const screenPos = elPos.clone();\n  screenPos.project(camera);\n  raycaster.setFromCamera(screenPos, camera);\n  const intersects = raycaster.intersectObjects(occlude, true);\n  if (intersects.length) {\n    const intersectionDistance = intersects[0].distance;\n    const pointDistance = elPos.distanceTo(raycaster.ray.origin);\n    return pointDistance < intersectionDistance;\n  }\n  return true;\n}\nfunction objectScale(el, camera) {\n  if (camera instanceof OrthographicCamera) {\n    return camera.zoom;\n  } else if (camera instanceof PerspectiveCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const vFOV = camera.fov * Math.PI / 180;\n    const dist = objectPos.distanceTo(cameraPos);\n    const scaleFOV = 2 * Math.tan(vFOV / 2) * dist;\n    return 1 / scaleFOV;\n  } else {\n    return 1;\n  }\n}\nfunction objectZIndex(el, camera, zIndexRange) {\n  if (camera instanceof PerspectiveCamera || camera instanceof OrthographicCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const dist = objectPos.distanceTo(cameraPos);\n    const A = (zIndexRange[1] - zIndexRange[0]) / (camera.far - camera.near);\n    const B = zIndexRange[1] - A * camera.far;\n    return Math.round(A * dist + B);\n  }\n  return undefined;\n}\nconst epsilon = value => Math.abs(value) < 1e-10 ? 0 : value;\nfunction getCSSMatrix(matrix, multipliers, prepend = '') {\n  let matrix3d = 'matrix3d(';\n  for (let i = 0; i !== 16; i++) {\n    matrix3d += epsilon(multipliers[i] * matrix.elements[i]) + (i !== 15 ? ',' : ')');\n  }\n  return prepend + matrix3d;\n}\nconst getCameraCSSMatrix = (multipliers => {\n  return matrix => getCSSMatrix(matrix, multipliers);\n})([1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1]);\nconst getObjectCSSMatrix = (scaleMultipliers => {\n  return (matrix, factor) => getCSSMatrix(matrix, scaleMultipliers(factor), 'translate(-50%,-50%)');\n})(f => [1 / f, 1 / f, 1 / f, 1, -1 / f, -1 / f, -1 / f, -1, 1 / f, 1 / f, 1 / f, 1, 1, 1, 1, 1]);\nfunction isRefObject(ref) {\n  return ref && typeof ref === 'object' && 'current' in ref;\n}\nconst Html = /*#__PURE__*/React.forwardRef(({\n  children,\n  eps = 0.001,\n  style,\n  className,\n  prepend,\n  center,\n  fullscreen,\n  portal,\n  distanceFactor,\n  sprite = false,\n  transform = false,\n  occlude,\n  onOcclude,\n  castShadow,\n  receiveShadow,\n  material,\n  geometry,\n  zIndexRange = [16777271, 0],\n  calculatePosition = defaultCalculatePosition,\n  as = 'div',\n  wrapperClass,\n  pointerEvents = 'auto',\n  ...props\n}, ref) => {\n  const {\n    gl,\n    camera,\n    scene,\n    size,\n    raycaster,\n    events,\n    viewport\n  } = useThree();\n  const [el] = React.useState(() => document.createElement(as));\n  const root = React.useRef();\n  const group = React.useRef(null);\n  const oldZoom = React.useRef(0);\n  const oldPosition = React.useRef([0, 0]);\n  const transformOuterRef = React.useRef(null);\n  const transformInnerRef = React.useRef(null); // Append to the connected element, which makes HTML work with views\n\n  const target = (portal == null ? void 0 : portal.current) || events.connected || gl.domElement.parentNode;\n  const occlusionMeshRef = React.useRef(null);\n  const isMeshSizeSet = React.useRef(false);\n  const isRayCastOcclusion = React.useMemo(() => {\n    return occlude && occlude !== 'blending' || Array.isArray(occlude) && occlude.length && isRefObject(occlude[0]);\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    const el = gl.domElement;\n    if (occlude && occlude === 'blending') {\n      el.style.zIndex = `${Math.floor(zIndexRange[0] / 2)}`;\n      el.style.position = 'absolute';\n      el.style.pointerEvents = 'none';\n    } else {\n      el.style.zIndex = null;\n      el.style.position = null;\n      el.style.pointerEvents = null;\n    }\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    if (group.current) {\n      const currentRoot = root.current = ReactDOM.createRoot(el);\n      scene.updateMatrixWorld();\n      if (transform) {\n        el.style.cssText = `position:absolute;top:0;left:0;pointer-events:none;overflow:hidden;`;\n      } else {\n        const vec = calculatePosition(group.current, camera, size);\n        el.style.cssText = `position:absolute;top:0;left:0;transform:translate3d(${vec[0]}px,${vec[1]}px,0);transform-origin:0 0;`;\n      }\n      if (target) {\n        if (prepend) target.prepend(el);else target.appendChild(el);\n      }\n      return () => {\n        if (target) target.removeChild(el);\n        currentRoot.unmount();\n      };\n    }\n  }, [target, transform]);\n  React.useLayoutEffect(() => {\n    if (wrapperClass) el.className = wrapperClass;\n  }, [wrapperClass]);\n  const styles = React.useMemo(() => {\n    if (transform) {\n      return {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: size.width,\n        height: size.height,\n        transformStyle: 'preserve-3d',\n        pointerEvents: 'none'\n      };\n    } else {\n      return {\n        position: 'absolute',\n        transform: center ? 'translate3d(-50%,-50%,0)' : 'none',\n        ...(fullscreen && {\n          top: -size.height / 2,\n          left: -size.width / 2,\n          width: size.width,\n          height: size.height\n        }),\n        ...style\n      };\n    }\n  }, [style, center, fullscreen, size, transform]);\n  const transformInnerStyles = React.useMemo(() => ({\n    position: 'absolute',\n    pointerEvents\n  }), [pointerEvents]);\n  React.useLayoutEffect(() => {\n    isMeshSizeSet.current = false;\n    if (transform) {\n      var _root$current;\n      (_root$current = root.current) == null ? void 0 : _root$current.render(/*#__PURE__*/React.createElement(\"div\", {\n        ref: transformOuterRef,\n        style: styles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: transformInnerRef,\n        style: transformInnerStyles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        className: className,\n        style: style,\n        children: children\n      }))));\n    } else {\n      var _root$current2;\n      (_root$current2 = root.current) == null ? void 0 : _root$current2.render(/*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        style: styles,\n        className: className,\n        children: children\n      }));\n    }\n  });\n  const visible = React.useRef(true);\n  useFrame(gl => {\n    if (group.current) {\n      camera.updateMatrixWorld();\n      group.current.updateWorldMatrix(true, false);\n      const vec = transform ? oldPosition.current : calculatePosition(group.current, camera, size);\n      if (transform || Math.abs(oldZoom.current - camera.zoom) > eps || Math.abs(oldPosition.current[0] - vec[0]) > eps || Math.abs(oldPosition.current[1] - vec[1]) > eps) {\n        const isBehindCamera = isObjectBehindCamera(group.current, camera);\n        let raytraceTarget = false;\n        if (isRayCastOcclusion) {\n          if (occlude !== 'blending') {\n            raytraceTarget = [scene];\n          } else if (Array.isArray(occlude)) {\n            raytraceTarget = occlude.map(item => item.current);\n          }\n        }\n        const previouslyVisible = visible.current;\n        if (raytraceTarget) {\n          const isvisible = isObjectVisible(group.current, camera, raycaster, raytraceTarget);\n          visible.current = isvisible && !isBehindCamera;\n        } else {\n          visible.current = !isBehindCamera;\n        }\n        if (previouslyVisible !== visible.current) {\n          if (onOcclude) onOcclude(!visible.current);else el.style.display = visible.current ? 'block' : 'none';\n        }\n        const halfRange = Math.floor(zIndexRange[0] / 2);\n        const zRange = occlude ? isRayCastOcclusion //\n        ? [zIndexRange[0], halfRange] : [halfRange - 1, 0] : zIndexRange;\n        el.style.zIndex = `${objectZIndex(group.current, camera, zRange)}`;\n        if (transform) {\n          const [widthHalf, heightHalf] = [size.width / 2, size.height / 2];\n          const fov = camera.projectionMatrix.elements[5] * heightHalf;\n          const {\n            isOrthographicCamera,\n            top,\n            left,\n            bottom,\n            right\n          } = camera;\n          const cameraMatrix = getCameraCSSMatrix(camera.matrixWorldInverse);\n          const cameraTransform = isOrthographicCamera ? `scale(${fov})translate(${epsilon(-(right + left) / 2)}px,${epsilon((top + bottom) / 2)}px)` : `translateZ(${fov}px)`;\n          let matrix = group.current.matrixWorld;\n          if (sprite) {\n            matrix = camera.matrixWorldInverse.clone().transpose().copyPosition(matrix).scale(group.current.scale);\n            matrix.elements[3] = matrix.elements[7] = matrix.elements[11] = 0;\n            matrix.elements[15] = 1;\n          }\n          el.style.width = size.width + 'px';\n          el.style.height = size.height + 'px';\n          el.style.perspective = isOrthographicCamera ? '' : `${fov}px`;\n          if (transformOuterRef.current && transformInnerRef.current) {\n            transformOuterRef.current.style.transform = `${cameraTransform}${cameraMatrix}translate(${widthHalf}px,${heightHalf}px)`;\n            transformInnerRef.current.style.transform = getObjectCSSMatrix(matrix, 1 / ((distanceFactor || 10) / 400));\n          }\n        } else {\n          const scale = distanceFactor === undefined ? 1 : objectScale(group.current, camera) * distanceFactor;\n          el.style.transform = `translate3d(${vec[0]}px,${vec[1]}px,0) scale(${scale})`;\n        }\n        oldPosition.current = vec;\n        oldZoom.current = camera.zoom;\n      }\n    }\n    if (!isRayCastOcclusion && occlusionMeshRef.current && !isMeshSizeSet.current) {\n      if (transform) {\n        if (transformOuterRef.current) {\n          const el = transformOuterRef.current.children[0];\n          if (el != null && el.clientWidth && el != null && el.clientHeight) {\n            const {\n              isOrthographicCamera\n            } = camera;\n            if (isOrthographicCamera || geometry) {\n              if (props.scale) {\n                if (!Array.isArray(props.scale)) {\n                  occlusionMeshRef.current.scale.setScalar(1 / props.scale);\n                } else if (props.scale instanceof Vector3) {\n                  occlusionMeshRef.current.scale.copy(props.scale.clone().divideScalar(1));\n                } else {\n                  occlusionMeshRef.current.scale.set(1 / props.scale[0], 1 / props.scale[1], 1 / props.scale[2]);\n                }\n              }\n            } else {\n              const ratio = (distanceFactor || 10) / 400;\n              const w = el.clientWidth * ratio;\n              const h = el.clientHeight * ratio;\n              occlusionMeshRef.current.scale.set(w, h, 1);\n            }\n            isMeshSizeSet.current = true;\n          }\n        }\n      } else {\n        const ele = el.children[0];\n        if (ele != null && ele.clientWidth && ele != null && ele.clientHeight) {\n          const ratio = 1 / viewport.factor;\n          const w = ele.clientWidth * ratio;\n          const h = ele.clientHeight * ratio;\n          occlusionMeshRef.current.scale.set(w, h, 1);\n          isMeshSizeSet.current = true;\n        }\n        occlusionMeshRef.current.lookAt(gl.camera.position);\n      }\n    }\n  });\n  const shaders = React.useMemo(() => ({\n    vertexShader: !transform ? /* glsl */\n    `\n          /*\n            This shader is from the THREE's SpriteMaterial.\n            We need to turn the backing plane into a Sprite\n            (make it always face the camera) if \"transfrom\" \n            is false. \n          */\n          #include <common>\n\n          void main() {\n            vec2 center = vec2(0., 1.);\n            float rotation = 0.0;\n            \n            // This is somewhat arbitrary, but it seems to work well\n            // Need to figure out how to derive this dynamically if it even matters\n            float size = 0.03;\n\n            vec4 mvPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );\n            vec2 scale;\n            scale.x = length( vec3( modelMatrix[ 0 ].x, modelMatrix[ 0 ].y, modelMatrix[ 0 ].z ) );\n            scale.y = length( vec3( modelMatrix[ 1 ].x, modelMatrix[ 1 ].y, modelMatrix[ 1 ].z ) );\n\n            bool isPerspective = isPerspectiveMatrix( projectionMatrix );\n            if ( isPerspective ) scale *= - mvPosition.z;\n\n            vec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale * size;\n            vec2 rotatedPosition;\n            rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;\n            rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;\n            mvPosition.xy += rotatedPosition;\n\n            gl_Position = projectionMatrix * mvPosition;\n          }\n      ` : undefined,\n    fragmentShader: /* glsl */\n    `\n        void main() {\n          gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n        }\n      `\n  }), [transform]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({}, props, {\n    ref: group\n  }), occlude && !isRayCastOcclusion && /*#__PURE__*/React.createElement(\"mesh\", {\n    castShadow: castShadow,\n    receiveShadow: receiveShadow,\n    ref: occlusionMeshRef\n  }, geometry || /*#__PURE__*/React.createElement(\"planeGeometry\", null), material || /*#__PURE__*/React.createElement(\"shaderMaterial\", {\n    side: DoubleSide,\n    vertexShader: shaders.vertexShader,\n    fragmentShader: shaders.fragmentShader\n  })));\n});\nexport { Html };", "map": {"version": 3, "names": ["_extends", "React", "ReactDOM", "Vector3", "DoubleSide", "OrthographicCamera", "PerspectiveCamera", "useThree", "useFrame", "v1", "v2", "v3", "defaultCalculatePosition", "el", "camera", "size", "objectPos", "setFromMatrixPosition", "matrixWorld", "project", "widthHalf", "width", "heightHalf", "height", "x", "y", "isObjectBehindCamera", "cameraPos", "deltaCamObj", "sub", "camDir", "getWorldDirection", "angleTo", "Math", "PI", "isObjectVisible", "raycaster", "occlude", "elPos", "screenPos", "clone", "setFromCamera", "intersects", "intersectObjects", "length", "intersectionDistance", "distance", "pointDistance", "distanceTo", "ray", "origin", "objectScale", "zoom", "vFOV", "fov", "dist", "scaleFOV", "tan", "objectZIndex", "zIndexRange", "A", "far", "near", "B", "round", "undefined", "epsilon", "value", "abs", "getCSSMatrix", "matrix", "multipliers", "prepend", "matrix3d", "i", "elements", "getCameraCSSMatrix", "getObjectCSSMatrix", "scaleMultipliers", "factor", "f", "isRefObject", "ref", "Html", "forwardRef", "children", "eps", "style", "className", "center", "fullscreen", "portal", "distanceFactor", "sprite", "transform", "onOcclude", "<PERSON><PERSON><PERSON><PERSON>", "receiveShadow", "material", "geometry", "calculatePosition", "as", "wrapperClass", "pointerEvents", "props", "gl", "scene", "events", "viewport", "useState", "document", "createElement", "root", "useRef", "group", "oldZoom", "oldPosition", "transformOuterRef", "transformInnerRef", "target", "current", "connected", "dom<PERSON>lement", "parentNode", "occlusionMeshRef", "isMeshSizeSet", "isRayCastOcclusion", "useMemo", "Array", "isArray", "useLayoutEffect", "zIndex", "floor", "position", "currentRoot", "createRoot", "updateMatrixWorld", "cssText", "vec", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "unmount", "styles", "top", "left", "transformStyle", "transformInnerStyles", "_root$current", "render", "_root$current2", "visible", "updateWorldMatrix", "isBehindCamera", "raytraceTarget", "map", "item", "previouslyVisible", "isvisible", "display", "<PERSON><PERSON><PERSON><PERSON>", "zRange", "projectionMatrix", "isOrthographicCamera", "bottom", "right", "cameraMatrix", "matrixWorldInverse", "cameraTransform", "transpose", "copyPosition", "scale", "perspective", "clientWidth", "clientHeight", "setScalar", "copy", "divideScalar", "set", "ratio", "w", "h", "ele", "lookAt", "shaders", "vertexShader", "fragmentShader", "side"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/web/Html.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom/client';\nimport { Vector3, DoubleSide, OrthographicCamera, PerspectiveCamera } from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\n\nconst v1 = new Vector3();\nconst v2 = new Vector3();\nconst v3 = new Vector3();\n\nfunction defaultCalculatePosition(el, camera, size) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  objectPos.project(camera);\n  const widthHalf = size.width / 2;\n  const heightHalf = size.height / 2;\n  return [objectPos.x * widthHalf + widthHalf, -(objectPos.y * heightHalf) + heightHalf];\n}\n\nfunction isObjectBehindCamera(el, camera) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n  const deltaCamObj = objectPos.sub(cameraPos);\n  const camDir = camera.getWorldDirection(v3);\n  return deltaCamObj.angleTo(camDir) > Math.PI / 2;\n}\n\nfunction isObjectVisible(el, camera, raycaster, occlude) {\n  const elPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const screenPos = elPos.clone();\n  screenPos.project(camera);\n  raycaster.setFromCamera(screenPos, camera);\n  const intersects = raycaster.intersectObjects(occlude, true);\n\n  if (intersects.length) {\n    const intersectionDistance = intersects[0].distance;\n    const pointDistance = elPos.distanceTo(raycaster.ray.origin);\n    return pointDistance < intersectionDistance;\n  }\n\n  return true;\n}\n\nfunction objectScale(el, camera) {\n  if (camera instanceof OrthographicCamera) {\n    return camera.zoom;\n  } else if (camera instanceof PerspectiveCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const vFOV = camera.fov * Math.PI / 180;\n    const dist = objectPos.distanceTo(cameraPos);\n    const scaleFOV = 2 * Math.tan(vFOV / 2) * dist;\n    return 1 / scaleFOV;\n  } else {\n    return 1;\n  }\n}\n\nfunction objectZIndex(el, camera, zIndexRange) {\n  if (camera instanceof PerspectiveCamera || camera instanceof OrthographicCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const dist = objectPos.distanceTo(cameraPos);\n    const A = (zIndexRange[1] - zIndexRange[0]) / (camera.far - camera.near);\n    const B = zIndexRange[1] - A * camera.far;\n    return Math.round(A * dist + B);\n  }\n\n  return undefined;\n}\n\nconst epsilon = value => Math.abs(value) < 1e-10 ? 0 : value;\n\nfunction getCSSMatrix(matrix, multipliers, prepend = '') {\n  let matrix3d = 'matrix3d(';\n\n  for (let i = 0; i !== 16; i++) {\n    matrix3d += epsilon(multipliers[i] * matrix.elements[i]) + (i !== 15 ? ',' : ')');\n  }\n\n  return prepend + matrix3d;\n}\n\nconst getCameraCSSMatrix = (multipliers => {\n  return matrix => getCSSMatrix(matrix, multipliers);\n})([1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1]);\n\nconst getObjectCSSMatrix = (scaleMultipliers => {\n  return (matrix, factor) => getCSSMatrix(matrix, scaleMultipliers(factor), 'translate(-50%,-50%)');\n})(f => [1 / f, 1 / f, 1 / f, 1, -1 / f, -1 / f, -1 / f, -1, 1 / f, 1 / f, 1 / f, 1, 1, 1, 1, 1]);\n\nfunction isRefObject(ref) {\n  return ref && typeof ref === 'object' && 'current' in ref;\n}\n\nconst Html = /*#__PURE__*/React.forwardRef(({\n  children,\n  eps = 0.001,\n  style,\n  className,\n  prepend,\n  center,\n  fullscreen,\n  portal,\n  distanceFactor,\n  sprite = false,\n  transform = false,\n  occlude,\n  onOcclude,\n  castShadow,\n  receiveShadow,\n  material,\n  geometry,\n  zIndexRange = [16777271, 0],\n  calculatePosition = defaultCalculatePosition,\n  as = 'div',\n  wrapperClass,\n  pointerEvents = 'auto',\n  ...props\n}, ref) => {\n  const {\n    gl,\n    camera,\n    scene,\n    size,\n    raycaster,\n    events,\n    viewport\n  } = useThree();\n  const [el] = React.useState(() => document.createElement(as));\n  const root = React.useRef();\n  const group = React.useRef(null);\n  const oldZoom = React.useRef(0);\n  const oldPosition = React.useRef([0, 0]);\n  const transformOuterRef = React.useRef(null);\n  const transformInnerRef = React.useRef(null); // Append to the connected element, which makes HTML work with views\n\n  const target = (portal == null ? void 0 : portal.current) || events.connected || gl.domElement.parentNode;\n  const occlusionMeshRef = React.useRef(null);\n  const isMeshSizeSet = React.useRef(false);\n  const isRayCastOcclusion = React.useMemo(() => {\n    return occlude && occlude !== 'blending' || Array.isArray(occlude) && occlude.length && isRefObject(occlude[0]);\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    const el = gl.domElement;\n\n    if (occlude && occlude === 'blending') {\n      el.style.zIndex = `${Math.floor(zIndexRange[0] / 2)}`;\n      el.style.position = 'absolute';\n      el.style.pointerEvents = 'none';\n    } else {\n      el.style.zIndex = null;\n      el.style.position = null;\n      el.style.pointerEvents = null;\n    }\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    if (group.current) {\n      const currentRoot = root.current = ReactDOM.createRoot(el);\n      scene.updateMatrixWorld();\n\n      if (transform) {\n        el.style.cssText = `position:absolute;top:0;left:0;pointer-events:none;overflow:hidden;`;\n      } else {\n        const vec = calculatePosition(group.current, camera, size);\n        el.style.cssText = `position:absolute;top:0;left:0;transform:translate3d(${vec[0]}px,${vec[1]}px,0);transform-origin:0 0;`;\n      }\n\n      if (target) {\n        if (prepend) target.prepend(el);else target.appendChild(el);\n      }\n\n      return () => {\n        if (target) target.removeChild(el);\n        currentRoot.unmount();\n      };\n    }\n  }, [target, transform]);\n  React.useLayoutEffect(() => {\n    if (wrapperClass) el.className = wrapperClass;\n  }, [wrapperClass]);\n  const styles = React.useMemo(() => {\n    if (transform) {\n      return {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: size.width,\n        height: size.height,\n        transformStyle: 'preserve-3d',\n        pointerEvents: 'none'\n      };\n    } else {\n      return {\n        position: 'absolute',\n        transform: center ? 'translate3d(-50%,-50%,0)' : 'none',\n        ...(fullscreen && {\n          top: -size.height / 2,\n          left: -size.width / 2,\n          width: size.width,\n          height: size.height\n        }),\n        ...style\n      };\n    }\n  }, [style, center, fullscreen, size, transform]);\n  const transformInnerStyles = React.useMemo(() => ({\n    position: 'absolute',\n    pointerEvents\n  }), [pointerEvents]);\n  React.useLayoutEffect(() => {\n    isMeshSizeSet.current = false;\n\n    if (transform) {\n      var _root$current;\n\n      (_root$current = root.current) == null ? void 0 : _root$current.render( /*#__PURE__*/React.createElement(\"div\", {\n        ref: transformOuterRef,\n        style: styles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: transformInnerRef,\n        style: transformInnerStyles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        className: className,\n        style: style,\n        children: children\n      }))));\n    } else {\n      var _root$current2;\n\n      (_root$current2 = root.current) == null ? void 0 : _root$current2.render( /*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        style: styles,\n        className: className,\n        children: children\n      }));\n    }\n  });\n  const visible = React.useRef(true);\n  useFrame(gl => {\n    if (group.current) {\n      camera.updateMatrixWorld();\n      group.current.updateWorldMatrix(true, false);\n      const vec = transform ? oldPosition.current : calculatePosition(group.current, camera, size);\n\n      if (transform || Math.abs(oldZoom.current - camera.zoom) > eps || Math.abs(oldPosition.current[0] - vec[0]) > eps || Math.abs(oldPosition.current[1] - vec[1]) > eps) {\n        const isBehindCamera = isObjectBehindCamera(group.current, camera);\n        let raytraceTarget = false;\n\n        if (isRayCastOcclusion) {\n          if (occlude !== 'blending') {\n            raytraceTarget = [scene];\n          } else if (Array.isArray(occlude)) {\n            raytraceTarget = occlude.map(item => item.current);\n          }\n        }\n\n        const previouslyVisible = visible.current;\n\n        if (raytraceTarget) {\n          const isvisible = isObjectVisible(group.current, camera, raycaster, raytraceTarget);\n          visible.current = isvisible && !isBehindCamera;\n        } else {\n          visible.current = !isBehindCamera;\n        }\n\n        if (previouslyVisible !== visible.current) {\n          if (onOcclude) onOcclude(!visible.current);else el.style.display = visible.current ? 'block' : 'none';\n        }\n\n        const halfRange = Math.floor(zIndexRange[0] / 2);\n        const zRange = occlude ? isRayCastOcclusion //\n        ? [zIndexRange[0], halfRange] : [halfRange - 1, 0] : zIndexRange;\n        el.style.zIndex = `${objectZIndex(group.current, camera, zRange)}`;\n\n        if (transform) {\n          const [widthHalf, heightHalf] = [size.width / 2, size.height / 2];\n          const fov = camera.projectionMatrix.elements[5] * heightHalf;\n          const {\n            isOrthographicCamera,\n            top,\n            left,\n            bottom,\n            right\n          } = camera;\n          const cameraMatrix = getCameraCSSMatrix(camera.matrixWorldInverse);\n          const cameraTransform = isOrthographicCamera ? `scale(${fov})translate(${epsilon(-(right + left) / 2)}px,${epsilon((top + bottom) / 2)}px)` : `translateZ(${fov}px)`;\n          let matrix = group.current.matrixWorld;\n\n          if (sprite) {\n            matrix = camera.matrixWorldInverse.clone().transpose().copyPosition(matrix).scale(group.current.scale);\n            matrix.elements[3] = matrix.elements[7] = matrix.elements[11] = 0;\n            matrix.elements[15] = 1;\n          }\n\n          el.style.width = size.width + 'px';\n          el.style.height = size.height + 'px';\n          el.style.perspective = isOrthographicCamera ? '' : `${fov}px`;\n\n          if (transformOuterRef.current && transformInnerRef.current) {\n            transformOuterRef.current.style.transform = `${cameraTransform}${cameraMatrix}translate(${widthHalf}px,${heightHalf}px)`;\n            transformInnerRef.current.style.transform = getObjectCSSMatrix(matrix, 1 / ((distanceFactor || 10) / 400));\n          }\n        } else {\n          const scale = distanceFactor === undefined ? 1 : objectScale(group.current, camera) * distanceFactor;\n          el.style.transform = `translate3d(${vec[0]}px,${vec[1]}px,0) scale(${scale})`;\n        }\n\n        oldPosition.current = vec;\n        oldZoom.current = camera.zoom;\n      }\n    }\n\n    if (!isRayCastOcclusion && occlusionMeshRef.current && !isMeshSizeSet.current) {\n      if (transform) {\n        if (transformOuterRef.current) {\n          const el = transformOuterRef.current.children[0];\n\n          if (el != null && el.clientWidth && el != null && el.clientHeight) {\n            const {\n              isOrthographicCamera\n            } = camera;\n\n            if (isOrthographicCamera || geometry) {\n              if (props.scale) {\n                if (!Array.isArray(props.scale)) {\n                  occlusionMeshRef.current.scale.setScalar(1 / props.scale);\n                } else if (props.scale instanceof Vector3) {\n                  occlusionMeshRef.current.scale.copy(props.scale.clone().divideScalar(1));\n                } else {\n                  occlusionMeshRef.current.scale.set(1 / props.scale[0], 1 / props.scale[1], 1 / props.scale[2]);\n                }\n              }\n            } else {\n              const ratio = (distanceFactor || 10) / 400;\n              const w = el.clientWidth * ratio;\n              const h = el.clientHeight * ratio;\n              occlusionMeshRef.current.scale.set(w, h, 1);\n            }\n\n            isMeshSizeSet.current = true;\n          }\n        }\n      } else {\n        const ele = el.children[0];\n\n        if (ele != null && ele.clientWidth && ele != null && ele.clientHeight) {\n          const ratio = 1 / viewport.factor;\n          const w = ele.clientWidth * ratio;\n          const h = ele.clientHeight * ratio;\n          occlusionMeshRef.current.scale.set(w, h, 1);\n          isMeshSizeSet.current = true;\n        }\n\n        occlusionMeshRef.current.lookAt(gl.camera.position);\n      }\n    }\n  });\n  const shaders = React.useMemo(() => ({\n    vertexShader: !transform ?\n    /* glsl */\n    `\n          /*\n            This shader is from the THREE's SpriteMaterial.\n            We need to turn the backing plane into a Sprite\n            (make it always face the camera) if \"transfrom\" \n            is false. \n          */\n          #include <common>\n\n          void main() {\n            vec2 center = vec2(0., 1.);\n            float rotation = 0.0;\n            \n            // This is somewhat arbitrary, but it seems to work well\n            // Need to figure out how to derive this dynamically if it even matters\n            float size = 0.03;\n\n            vec4 mvPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );\n            vec2 scale;\n            scale.x = length( vec3( modelMatrix[ 0 ].x, modelMatrix[ 0 ].y, modelMatrix[ 0 ].z ) );\n            scale.y = length( vec3( modelMatrix[ 1 ].x, modelMatrix[ 1 ].y, modelMatrix[ 1 ].z ) );\n\n            bool isPerspective = isPerspectiveMatrix( projectionMatrix );\n            if ( isPerspective ) scale *= - mvPosition.z;\n\n            vec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale * size;\n            vec2 rotatedPosition;\n            rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;\n            rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;\n            mvPosition.xy += rotatedPosition;\n\n            gl_Position = projectionMatrix * mvPosition;\n          }\n      ` : undefined,\n    fragmentShader:\n    /* glsl */\n    `\n        void main() {\n          gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n        }\n      `\n  }), [transform]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({}, props, {\n    ref: group\n  }), occlude && !isRayCastOcclusion && /*#__PURE__*/React.createElement(\"mesh\", {\n    castShadow: castShadow,\n    receiveShadow: receiveShadow,\n    ref: occlusionMeshRef\n  }, geometry || /*#__PURE__*/React.createElement(\"planeGeometry\", null), material || /*#__PURE__*/React.createElement(\"shaderMaterial\", {\n    side: DoubleSide,\n    vertexShader: shaders.vertexShader,\n    fragmentShader: shaders.fragmentShader\n  })));\n});\n\nexport { Html };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,kBAAkB;AAC5C,SAASC,OAAO,EAAEC,UAAU,EAAEC,kBAAkB,EAAEC,iBAAiB,QAAQ,OAAO;AAClF,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAEvD,MAAMC,EAAE,GAAG,IAAIN,OAAO,CAAC,CAAC;AACxB,MAAMO,EAAE,GAAG,IAAIP,OAAO,CAAC,CAAC;AACxB,MAAMQ,EAAE,GAAG,IAAIR,OAAO,CAAC,CAAC;AAExB,SAASS,wBAAwBA,CAACC,EAAE,EAAEC,MAAM,EAAEC,IAAI,EAAE;EAClD,MAAMC,SAAS,GAAGP,EAAE,CAACQ,qBAAqB,CAACJ,EAAE,CAACK,WAAW,CAAC;EAC1DF,SAAS,CAACG,OAAO,CAACL,MAAM,CAAC;EACzB,MAAMM,SAAS,GAAGL,IAAI,CAACM,KAAK,GAAG,CAAC;EAChC,MAAMC,UAAU,GAAGP,IAAI,CAACQ,MAAM,GAAG,CAAC;EAClC,OAAO,CAACP,SAAS,CAACQ,CAAC,GAAGJ,SAAS,GAAGA,SAAS,EAAE,EAAEJ,SAAS,CAACS,CAAC,GAAGH,UAAU,CAAC,GAAGA,UAAU,CAAC;AACxF;AAEA,SAASI,oBAAoBA,CAACb,EAAE,EAAEC,MAAM,EAAE;EACxC,MAAME,SAAS,GAAGP,EAAE,CAACQ,qBAAqB,CAACJ,EAAE,CAACK,WAAW,CAAC;EAC1D,MAAMS,SAAS,GAAGjB,EAAE,CAACO,qBAAqB,CAACH,MAAM,CAACI,WAAW,CAAC;EAC9D,MAAMU,WAAW,GAAGZ,SAAS,CAACa,GAAG,CAACF,SAAS,CAAC;EAC5C,MAAMG,MAAM,GAAGhB,MAAM,CAACiB,iBAAiB,CAACpB,EAAE,CAAC;EAC3C,OAAOiB,WAAW,CAACI,OAAO,CAACF,MAAM,CAAC,GAAGG,IAAI,CAACC,EAAE,GAAG,CAAC;AAClD;AAEA,SAASC,eAAeA,CAACtB,EAAE,EAAEC,MAAM,EAAEsB,SAAS,EAAEC,OAAO,EAAE;EACvD,MAAMC,KAAK,GAAG7B,EAAE,CAACQ,qBAAqB,CAACJ,EAAE,CAACK,WAAW,CAAC;EACtD,MAAMqB,SAAS,GAAGD,KAAK,CAACE,KAAK,CAAC,CAAC;EAC/BD,SAAS,CAACpB,OAAO,CAACL,MAAM,CAAC;EACzBsB,SAAS,CAACK,aAAa,CAACF,SAAS,EAAEzB,MAAM,CAAC;EAC1C,MAAM4B,UAAU,GAAGN,SAAS,CAACO,gBAAgB,CAACN,OAAO,EAAE,IAAI,CAAC;EAE5D,IAAIK,UAAU,CAACE,MAAM,EAAE;IACrB,MAAMC,oBAAoB,GAAGH,UAAU,CAAC,CAAC,CAAC,CAACI,QAAQ;IACnD,MAAMC,aAAa,GAAGT,KAAK,CAACU,UAAU,CAACZ,SAAS,CAACa,GAAG,CAACC,MAAM,CAAC;IAC5D,OAAOH,aAAa,GAAGF,oBAAoB;EAC7C;EAEA,OAAO,IAAI;AACb;AAEA,SAASM,WAAWA,CAACtC,EAAE,EAAEC,MAAM,EAAE;EAC/B,IAAIA,MAAM,YAAYT,kBAAkB,EAAE;IACxC,OAAOS,MAAM,CAACsC,IAAI;EACpB,CAAC,MAAM,IAAItC,MAAM,YAAYR,iBAAiB,EAAE;IAC9C,MAAMU,SAAS,GAAGP,EAAE,CAACQ,qBAAqB,CAACJ,EAAE,CAACK,WAAW,CAAC;IAC1D,MAAMS,SAAS,GAAGjB,EAAE,CAACO,qBAAqB,CAACH,MAAM,CAACI,WAAW,CAAC;IAC9D,MAAMmC,IAAI,GAAGvC,MAAM,CAACwC,GAAG,GAAGrB,IAAI,CAACC,EAAE,GAAG,GAAG;IACvC,MAAMqB,IAAI,GAAGvC,SAAS,CAACgC,UAAU,CAACrB,SAAS,CAAC;IAC5C,MAAM6B,QAAQ,GAAG,CAAC,GAAGvB,IAAI,CAACwB,GAAG,CAACJ,IAAI,GAAG,CAAC,CAAC,GAAGE,IAAI;IAC9C,OAAO,CAAC,GAAGC,QAAQ;EACrB,CAAC,MAAM;IACL,OAAO,CAAC;EACV;AACF;AAEA,SAASE,YAAYA,CAAC7C,EAAE,EAAEC,MAAM,EAAE6C,WAAW,EAAE;EAC7C,IAAI7C,MAAM,YAAYR,iBAAiB,IAAIQ,MAAM,YAAYT,kBAAkB,EAAE;IAC/E,MAAMW,SAAS,GAAGP,EAAE,CAACQ,qBAAqB,CAACJ,EAAE,CAACK,WAAW,CAAC;IAC1D,MAAMS,SAAS,GAAGjB,EAAE,CAACO,qBAAqB,CAACH,MAAM,CAACI,WAAW,CAAC;IAC9D,MAAMqC,IAAI,GAAGvC,SAAS,CAACgC,UAAU,CAACrB,SAAS,CAAC;IAC5C,MAAMiC,CAAC,GAAG,CAACD,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,KAAK7C,MAAM,CAAC+C,GAAG,GAAG/C,MAAM,CAACgD,IAAI,CAAC;IACxE,MAAMC,CAAC,GAAGJ,WAAW,CAAC,CAAC,CAAC,GAAGC,CAAC,GAAG9C,MAAM,CAAC+C,GAAG;IACzC,OAAO5B,IAAI,CAAC+B,KAAK,CAACJ,CAAC,GAAGL,IAAI,GAAGQ,CAAC,CAAC;EACjC;EAEA,OAAOE,SAAS;AAClB;AAEA,MAAMC,OAAO,GAAGC,KAAK,IAAIlC,IAAI,CAACmC,GAAG,CAACD,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAGA,KAAK;AAE5D,SAASE,YAAYA,CAACC,MAAM,EAAEC,WAAW,EAAEC,OAAO,GAAG,EAAE,EAAE;EACvD,IAAIC,QAAQ,GAAG,WAAW;EAE1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAK,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC7BD,QAAQ,IAAIP,OAAO,CAACK,WAAW,CAACG,CAAC,CAAC,GAAGJ,MAAM,CAACK,QAAQ,CAACD,CAAC,CAAC,CAAC,IAAIA,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;EACnF;EAEA,OAAOF,OAAO,GAAGC,QAAQ;AAC3B;AAEA,MAAMG,kBAAkB,GAAG,CAACL,WAAW,IAAI;EACzC,OAAOD,MAAM,IAAID,YAAY,CAACC,MAAM,EAAEC,WAAW,CAAC;AACpD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAExD,MAAMM,kBAAkB,GAAG,CAACC,gBAAgB,IAAI;EAC9C,OAAO,CAACR,MAAM,EAAES,MAAM,KAAKV,YAAY,CAACC,MAAM,EAAEQ,gBAAgB,CAACC,MAAM,CAAC,EAAE,sBAAsB,CAAC;AACnG,CAAC,EAAEC,CAAC,IAAI,CAAC,CAAC,GAAGA,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAGA,CAAC,EAAE,CAAC,CAAC,GAAGA,CAAC,EAAE,CAAC,CAAC,GAAGA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAEjG,SAASC,WAAWA,CAACC,GAAG,EAAE;EACxB,OAAOA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,SAAS,IAAIA,GAAG;AAC3D;AAEA,MAAMC,IAAI,GAAG,aAAalF,KAAK,CAACmF,UAAU,CAAC,CAAC;EAC1CC,QAAQ;EACRC,GAAG,GAAG,KAAK;EACXC,KAAK;EACLC,SAAS;EACThB,OAAO;EACPiB,MAAM;EACNC,UAAU;EACVC,MAAM;EACNC,cAAc;EACdC,MAAM,GAAG,KAAK;EACdC,SAAS,GAAG,KAAK;EACjBzD,OAAO;EACP0D,SAAS;EACTC,UAAU;EACVC,aAAa;EACbC,QAAQ;EACRC,QAAQ;EACRxC,WAAW,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;EAC3ByC,iBAAiB,GAAGxF,wBAAwB;EAC5CyF,EAAE,GAAG,KAAK;EACVC,YAAY;EACZC,aAAa,GAAG,MAAM;EACtB,GAAGC;AACL,CAAC,EAAEtB,GAAG,KAAK;EACT,MAAM;IACJuB,EAAE;IACF3F,MAAM;IACN4F,KAAK;IACL3F,IAAI;IACJqB,SAAS;IACTuE,MAAM;IACNC;EACF,CAAC,GAAGrG,QAAQ,CAAC,CAAC;EACd,MAAM,CAACM,EAAE,CAAC,GAAGZ,KAAK,CAAC4G,QAAQ,CAAC,MAAMC,QAAQ,CAACC,aAAa,CAACV,EAAE,CAAC,CAAC;EAC7D,MAAMW,IAAI,GAAG/G,KAAK,CAACgH,MAAM,CAAC,CAAC;EAC3B,MAAMC,KAAK,GAAGjH,KAAK,CAACgH,MAAM,CAAC,IAAI,CAAC;EAChC,MAAME,OAAO,GAAGlH,KAAK,CAACgH,MAAM,CAAC,CAAC,CAAC;EAC/B,MAAMG,WAAW,GAAGnH,KAAK,CAACgH,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACxC,MAAMI,iBAAiB,GAAGpH,KAAK,CAACgH,MAAM,CAAC,IAAI,CAAC;EAC5C,MAAMK,iBAAiB,GAAGrH,KAAK,CAACgH,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAE9C,MAAMM,MAAM,GAAG,CAAC5B,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC6B,OAAO,KAAKb,MAAM,CAACc,SAAS,IAAIhB,EAAE,CAACiB,UAAU,CAACC,UAAU;EACzG,MAAMC,gBAAgB,GAAG3H,KAAK,CAACgH,MAAM,CAAC,IAAI,CAAC;EAC3C,MAAMY,aAAa,GAAG5H,KAAK,CAACgH,MAAM,CAAC,KAAK,CAAC;EACzC,MAAMa,kBAAkB,GAAG7H,KAAK,CAAC8H,OAAO,CAAC,MAAM;IAC7C,OAAO1F,OAAO,IAAIA,OAAO,KAAK,UAAU,IAAI2F,KAAK,CAACC,OAAO,CAAC5F,OAAO,CAAC,IAAIA,OAAO,CAACO,MAAM,IAAIqC,WAAW,CAAC5C,OAAO,CAAC,CAAC,CAAC,CAAC;EACjH,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACbpC,KAAK,CAACiI,eAAe,CAAC,MAAM;IAC1B,MAAMrH,EAAE,GAAG4F,EAAE,CAACiB,UAAU;IAExB,IAAIrF,OAAO,IAAIA,OAAO,KAAK,UAAU,EAAE;MACrCxB,EAAE,CAAC0E,KAAK,CAAC4C,MAAM,GAAG,GAAGlG,IAAI,CAACmG,KAAK,CAACzE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;MACrD9C,EAAE,CAAC0E,KAAK,CAAC8C,QAAQ,GAAG,UAAU;MAC9BxH,EAAE,CAAC0E,KAAK,CAACgB,aAAa,GAAG,MAAM;IACjC,CAAC,MAAM;MACL1F,EAAE,CAAC0E,KAAK,CAAC4C,MAAM,GAAG,IAAI;MACtBtH,EAAE,CAAC0E,KAAK,CAAC8C,QAAQ,GAAG,IAAI;MACxBxH,EAAE,CAAC0E,KAAK,CAACgB,aAAa,GAAG,IAAI;IAC/B;EACF,CAAC,EAAE,CAAClE,OAAO,CAAC,CAAC;EACbpC,KAAK,CAACiI,eAAe,CAAC,MAAM;IAC1B,IAAIhB,KAAK,CAACM,OAAO,EAAE;MACjB,MAAMc,WAAW,GAAGtB,IAAI,CAACQ,OAAO,GAAGtH,QAAQ,CAACqI,UAAU,CAAC1H,EAAE,CAAC;MAC1D6F,KAAK,CAAC8B,iBAAiB,CAAC,CAAC;MAEzB,IAAI1C,SAAS,EAAE;QACbjF,EAAE,CAAC0E,KAAK,CAACkD,OAAO,GAAG,qEAAqE;MAC1F,CAAC,MAAM;QACL,MAAMC,GAAG,GAAGtC,iBAAiB,CAACc,KAAK,CAACM,OAAO,EAAE1G,MAAM,EAAEC,IAAI,CAAC;QAC1DF,EAAE,CAAC0E,KAAK,CAACkD,OAAO,GAAG,wDAAwDC,GAAG,CAAC,CAAC,CAAC,MAAMA,GAAG,CAAC,CAAC,CAAC,6BAA6B;MAC5H;MAEA,IAAInB,MAAM,EAAE;QACV,IAAI/C,OAAO,EAAE+C,MAAM,CAAC/C,OAAO,CAAC3D,EAAE,CAAC,CAAC,KAAK0G,MAAM,CAACoB,WAAW,CAAC9H,EAAE,CAAC;MAC7D;MAEA,OAAO,MAAM;QACX,IAAI0G,MAAM,EAAEA,MAAM,CAACqB,WAAW,CAAC/H,EAAE,CAAC;QAClCyH,WAAW,CAACO,OAAO,CAAC,CAAC;MACvB,CAAC;IACH;EACF,CAAC,EAAE,CAACtB,MAAM,EAAEzB,SAAS,CAAC,CAAC;EACvB7F,KAAK,CAACiI,eAAe,CAAC,MAAM;IAC1B,IAAI5B,YAAY,EAAEzF,EAAE,CAAC2E,SAAS,GAAGc,YAAY;EAC/C,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAClB,MAAMwC,MAAM,GAAG7I,KAAK,CAAC8H,OAAO,CAAC,MAAM;IACjC,IAAIjC,SAAS,EAAE;MACb,OAAO;QACLuC,QAAQ,EAAE,UAAU;QACpBU,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACP3H,KAAK,EAAEN,IAAI,CAACM,KAAK;QACjBE,MAAM,EAAER,IAAI,CAACQ,MAAM;QACnB0H,cAAc,EAAE,aAAa;QAC7B1C,aAAa,EAAE;MACjB,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACL8B,QAAQ,EAAE,UAAU;QACpBvC,SAAS,EAAEL,MAAM,GAAG,0BAA0B,GAAG,MAAM;QACvD,IAAIC,UAAU,IAAI;UAChBqD,GAAG,EAAE,CAAChI,IAAI,CAACQ,MAAM,GAAG,CAAC;UACrByH,IAAI,EAAE,CAACjI,IAAI,CAACM,KAAK,GAAG,CAAC;UACrBA,KAAK,EAAEN,IAAI,CAACM,KAAK;UACjBE,MAAM,EAAER,IAAI,CAACQ;QACf,CAAC,CAAC;QACF,GAAGgE;MACL,CAAC;IACH;EACF,CAAC,EAAE,CAACA,KAAK,EAAEE,MAAM,EAAEC,UAAU,EAAE3E,IAAI,EAAE+E,SAAS,CAAC,CAAC;EAChD,MAAMoD,oBAAoB,GAAGjJ,KAAK,CAAC8H,OAAO,CAAC,OAAO;IAChDM,QAAQ,EAAE,UAAU;IACpB9B;EACF,CAAC,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EACpBtG,KAAK,CAACiI,eAAe,CAAC,MAAM;IAC1BL,aAAa,CAACL,OAAO,GAAG,KAAK;IAE7B,IAAI1B,SAAS,EAAE;MACb,IAAIqD,aAAa;MAEjB,CAACA,aAAa,GAAGnC,IAAI,CAACQ,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2B,aAAa,CAACC,MAAM,CAAE,aAAanJ,KAAK,CAAC8G,aAAa,CAAC,KAAK,EAAE;QAC9G7B,GAAG,EAAEmC,iBAAiB;QACtB9B,KAAK,EAAEuD;MACT,CAAC,EAAE,aAAa7I,KAAK,CAAC8G,aAAa,CAAC,KAAK,EAAE;QACzC7B,GAAG,EAAEoC,iBAAiB;QACtB/B,KAAK,EAAE2D;MACT,CAAC,EAAE,aAAajJ,KAAK,CAAC8G,aAAa,CAAC,KAAK,EAAE;QACzC7B,GAAG,EAAEA,GAAG;QACRM,SAAS,EAAEA,SAAS;QACpBD,KAAK,EAAEA,KAAK;QACZF,QAAQ,EAAEA;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,MAAM;MACL,IAAIgE,cAAc;MAElB,CAACA,cAAc,GAAGrC,IAAI,CAACQ,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG6B,cAAc,CAACD,MAAM,CAAE,aAAanJ,KAAK,CAAC8G,aAAa,CAAC,KAAK,EAAE;QAChH7B,GAAG,EAAEA,GAAG;QACRK,KAAK,EAAEuD,MAAM;QACbtD,SAAS,EAAEA,SAAS;QACpBH,QAAQ,EAAEA;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC;EACF,MAAMiE,OAAO,GAAGrJ,KAAK,CAACgH,MAAM,CAAC,IAAI,CAAC;EAClCzG,QAAQ,CAACiG,EAAE,IAAI;IACb,IAAIS,KAAK,CAACM,OAAO,EAAE;MACjB1G,MAAM,CAAC0H,iBAAiB,CAAC,CAAC;MAC1BtB,KAAK,CAACM,OAAO,CAAC+B,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC;MAC5C,MAAMb,GAAG,GAAG5C,SAAS,GAAGsB,WAAW,CAACI,OAAO,GAAGpB,iBAAiB,CAACc,KAAK,CAACM,OAAO,EAAE1G,MAAM,EAAEC,IAAI,CAAC;MAE5F,IAAI+E,SAAS,IAAI7D,IAAI,CAACmC,GAAG,CAAC+C,OAAO,CAACK,OAAO,GAAG1G,MAAM,CAACsC,IAAI,CAAC,GAAGkC,GAAG,IAAIrD,IAAI,CAACmC,GAAG,CAACgD,WAAW,CAACI,OAAO,CAAC,CAAC,CAAC,GAAGkB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGpD,GAAG,IAAIrD,IAAI,CAACmC,GAAG,CAACgD,WAAW,CAACI,OAAO,CAAC,CAAC,CAAC,GAAGkB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGpD,GAAG,EAAE;QACpK,MAAMkE,cAAc,GAAG9H,oBAAoB,CAACwF,KAAK,CAACM,OAAO,EAAE1G,MAAM,CAAC;QAClE,IAAI2I,cAAc,GAAG,KAAK;QAE1B,IAAI3B,kBAAkB,EAAE;UACtB,IAAIzF,OAAO,KAAK,UAAU,EAAE;YAC1BoH,cAAc,GAAG,CAAC/C,KAAK,CAAC;UAC1B,CAAC,MAAM,IAAIsB,KAAK,CAACC,OAAO,CAAC5F,OAAO,CAAC,EAAE;YACjCoH,cAAc,GAAGpH,OAAO,CAACqH,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACnC,OAAO,CAAC;UACpD;QACF;QAEA,MAAMoC,iBAAiB,GAAGN,OAAO,CAAC9B,OAAO;QAEzC,IAAIiC,cAAc,EAAE;UAClB,MAAMI,SAAS,GAAG1H,eAAe,CAAC+E,KAAK,CAACM,OAAO,EAAE1G,MAAM,EAAEsB,SAAS,EAAEqH,cAAc,CAAC;UACnFH,OAAO,CAAC9B,OAAO,GAAGqC,SAAS,IAAI,CAACL,cAAc;QAChD,CAAC,MAAM;UACLF,OAAO,CAAC9B,OAAO,GAAG,CAACgC,cAAc;QACnC;QAEA,IAAII,iBAAiB,KAAKN,OAAO,CAAC9B,OAAO,EAAE;UACzC,IAAIzB,SAAS,EAAEA,SAAS,CAAC,CAACuD,OAAO,CAAC9B,OAAO,CAAC,CAAC,KAAK3G,EAAE,CAAC0E,KAAK,CAACuE,OAAO,GAAGR,OAAO,CAAC9B,OAAO,GAAG,OAAO,GAAG,MAAM;QACvG;QAEA,MAAMuC,SAAS,GAAG9H,IAAI,CAACmG,KAAK,CAACzE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAChD,MAAMqG,MAAM,GAAG3H,OAAO,GAAGyF,kBAAkB,CAAC;QAAA,EAC1C,CAACnE,WAAW,CAAC,CAAC,CAAC,EAAEoG,SAAS,CAAC,GAAG,CAACA,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC,GAAGpG,WAAW;QAChE9C,EAAE,CAAC0E,KAAK,CAAC4C,MAAM,GAAG,GAAGzE,YAAY,CAACwD,KAAK,CAACM,OAAO,EAAE1G,MAAM,EAAEkJ,MAAM,CAAC,EAAE;QAElE,IAAIlE,SAAS,EAAE;UACb,MAAM,CAAC1E,SAAS,EAAEE,UAAU,CAAC,GAAG,CAACP,IAAI,CAACM,KAAK,GAAG,CAAC,EAAEN,IAAI,CAACQ,MAAM,GAAG,CAAC,CAAC;UACjE,MAAM+B,GAAG,GAAGxC,MAAM,CAACmJ,gBAAgB,CAACtF,QAAQ,CAAC,CAAC,CAAC,GAAGrD,UAAU;UAC5D,MAAM;YACJ4I,oBAAoB;YACpBnB,GAAG;YACHC,IAAI;YACJmB,MAAM;YACNC;UACF,CAAC,GAAGtJ,MAAM;UACV,MAAMuJ,YAAY,GAAGzF,kBAAkB,CAAC9D,MAAM,CAACwJ,kBAAkB,CAAC;UAClE,MAAMC,eAAe,GAAGL,oBAAoB,GAAG,SAAS5G,GAAG,cAAcY,OAAO,CAAC,EAAEkG,KAAK,GAAGpB,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM9E,OAAO,CAAC,CAAC6E,GAAG,GAAGoB,MAAM,IAAI,CAAC,CAAC,KAAK,GAAG,cAAc7G,GAAG,KAAK;UACpK,IAAIgB,MAAM,GAAG4C,KAAK,CAACM,OAAO,CAACtG,WAAW;UAEtC,IAAI2E,MAAM,EAAE;YACVvB,MAAM,GAAGxD,MAAM,CAACwJ,kBAAkB,CAAC9H,KAAK,CAAC,CAAC,CAACgI,SAAS,CAAC,CAAC,CAACC,YAAY,CAACnG,MAAM,CAAC,CAACoG,KAAK,CAACxD,KAAK,CAACM,OAAO,CAACkD,KAAK,CAAC;YACtGpG,MAAM,CAACK,QAAQ,CAAC,CAAC,CAAC,GAAGL,MAAM,CAACK,QAAQ,CAAC,CAAC,CAAC,GAAGL,MAAM,CAACK,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC;YACjEL,MAAM,CAACK,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC;UACzB;UAEA9D,EAAE,CAAC0E,KAAK,CAAClE,KAAK,GAAGN,IAAI,CAACM,KAAK,GAAG,IAAI;UAClCR,EAAE,CAAC0E,KAAK,CAAChE,MAAM,GAAGR,IAAI,CAACQ,MAAM,GAAG,IAAI;UACpCV,EAAE,CAAC0E,KAAK,CAACoF,WAAW,GAAGT,oBAAoB,GAAG,EAAE,GAAG,GAAG5G,GAAG,IAAI;UAE7D,IAAI+D,iBAAiB,CAACG,OAAO,IAAIF,iBAAiB,CAACE,OAAO,EAAE;YAC1DH,iBAAiB,CAACG,OAAO,CAACjC,KAAK,CAACO,SAAS,GAAG,GAAGyE,eAAe,GAAGF,YAAY,aAAajJ,SAAS,MAAME,UAAU,KAAK;YACxHgG,iBAAiB,CAACE,OAAO,CAACjC,KAAK,CAACO,SAAS,GAAGjB,kBAAkB,CAACP,MAAM,EAAE,CAAC,IAAI,CAACsB,cAAc,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC;UAC5G;QACF,CAAC,MAAM;UACL,MAAM8E,KAAK,GAAG9E,cAAc,KAAK3B,SAAS,GAAG,CAAC,GAAGd,WAAW,CAAC+D,KAAK,CAACM,OAAO,EAAE1G,MAAM,CAAC,GAAG8E,cAAc;UACpG/E,EAAE,CAAC0E,KAAK,CAACO,SAAS,GAAG,eAAe4C,GAAG,CAAC,CAAC,CAAC,MAAMA,GAAG,CAAC,CAAC,CAAC,eAAegC,KAAK,GAAG;QAC/E;QAEAtD,WAAW,CAACI,OAAO,GAAGkB,GAAG;QACzBvB,OAAO,CAACK,OAAO,GAAG1G,MAAM,CAACsC,IAAI;MAC/B;IACF;IAEA,IAAI,CAAC0E,kBAAkB,IAAIF,gBAAgB,CAACJ,OAAO,IAAI,CAACK,aAAa,CAACL,OAAO,EAAE;MAC7E,IAAI1B,SAAS,EAAE;QACb,IAAIuB,iBAAiB,CAACG,OAAO,EAAE;UAC7B,MAAM3G,EAAE,GAAGwG,iBAAiB,CAACG,OAAO,CAACnC,QAAQ,CAAC,CAAC,CAAC;UAEhD,IAAIxE,EAAE,IAAI,IAAI,IAAIA,EAAE,CAAC+J,WAAW,IAAI/J,EAAE,IAAI,IAAI,IAAIA,EAAE,CAACgK,YAAY,EAAE;YACjE,MAAM;cACJX;YACF,CAAC,GAAGpJ,MAAM;YAEV,IAAIoJ,oBAAoB,IAAI/D,QAAQ,EAAE;cACpC,IAAIK,KAAK,CAACkE,KAAK,EAAE;gBACf,IAAI,CAAC1C,KAAK,CAACC,OAAO,CAACzB,KAAK,CAACkE,KAAK,CAAC,EAAE;kBAC/B9C,gBAAgB,CAACJ,OAAO,CAACkD,KAAK,CAACI,SAAS,CAAC,CAAC,GAAGtE,KAAK,CAACkE,KAAK,CAAC;gBAC3D,CAAC,MAAM,IAAIlE,KAAK,CAACkE,KAAK,YAAYvK,OAAO,EAAE;kBACzCyH,gBAAgB,CAACJ,OAAO,CAACkD,KAAK,CAACK,IAAI,CAACvE,KAAK,CAACkE,KAAK,CAAClI,KAAK,CAAC,CAAC,CAACwI,YAAY,CAAC,CAAC,CAAC,CAAC;gBAC1E,CAAC,MAAM;kBACLpD,gBAAgB,CAACJ,OAAO,CAACkD,KAAK,CAACO,GAAG,CAAC,CAAC,GAAGzE,KAAK,CAACkE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGlE,KAAK,CAACkE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGlE,KAAK,CAACkE,KAAK,CAAC,CAAC,CAAC,CAAC;gBAChG;cACF;YACF,CAAC,MAAM;cACL,MAAMQ,KAAK,GAAG,CAACtF,cAAc,IAAI,EAAE,IAAI,GAAG;cAC1C,MAAMuF,CAAC,GAAGtK,EAAE,CAAC+J,WAAW,GAAGM,KAAK;cAChC,MAAME,CAAC,GAAGvK,EAAE,CAACgK,YAAY,GAAGK,KAAK;cACjCtD,gBAAgB,CAACJ,OAAO,CAACkD,KAAK,CAACO,GAAG,CAACE,CAAC,EAAEC,CAAC,EAAE,CAAC,CAAC;YAC7C;YAEAvD,aAAa,CAACL,OAAO,GAAG,IAAI;UAC9B;QACF;MACF,CAAC,MAAM;QACL,MAAM6D,GAAG,GAAGxK,EAAE,CAACwE,QAAQ,CAAC,CAAC,CAAC;QAE1B,IAAIgG,GAAG,IAAI,IAAI,IAAIA,GAAG,CAACT,WAAW,IAAIS,GAAG,IAAI,IAAI,IAAIA,GAAG,CAACR,YAAY,EAAE;UACrE,MAAMK,KAAK,GAAG,CAAC,GAAGtE,QAAQ,CAAC7B,MAAM;UACjC,MAAMoG,CAAC,GAAGE,GAAG,CAACT,WAAW,GAAGM,KAAK;UACjC,MAAME,CAAC,GAAGC,GAAG,CAACR,YAAY,GAAGK,KAAK;UAClCtD,gBAAgB,CAACJ,OAAO,CAACkD,KAAK,CAACO,GAAG,CAACE,CAAC,EAAEC,CAAC,EAAE,CAAC,CAAC;UAC3CvD,aAAa,CAACL,OAAO,GAAG,IAAI;QAC9B;QAEAI,gBAAgB,CAACJ,OAAO,CAAC8D,MAAM,CAAC7E,EAAE,CAAC3F,MAAM,CAACuH,QAAQ,CAAC;MACrD;IACF;EACF,CAAC,CAAC;EACF,MAAMkD,OAAO,GAAGtL,KAAK,CAAC8H,OAAO,CAAC,OAAO;IACnCyD,YAAY,EAAE,CAAC1F,SAAS,GACxB;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,GAAG7B,SAAS;IACfwH,cAAc,EACd;IACA;AACJ;AACA;AACA;AACA;EACE,CAAC,CAAC,EAAE,CAAC3F,SAAS,CAAC,CAAC;EAChB,OAAO,aAAa7F,KAAK,CAAC8G,aAAa,CAAC,OAAO,EAAE/G,QAAQ,CAAC,CAAC,CAAC,EAAEwG,KAAK,EAAE;IACnEtB,GAAG,EAAEgC;EACP,CAAC,CAAC,EAAE7E,OAAO,IAAI,CAACyF,kBAAkB,IAAI,aAAa7H,KAAK,CAAC8G,aAAa,CAAC,MAAM,EAAE;IAC7Ef,UAAU,EAAEA,UAAU;IACtBC,aAAa,EAAEA,aAAa;IAC5Bf,GAAG,EAAE0C;EACP,CAAC,EAAEzB,QAAQ,IAAI,aAAalG,KAAK,CAAC8G,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,EAAEb,QAAQ,IAAI,aAAajG,KAAK,CAAC8G,aAAa,CAAC,gBAAgB,EAAE;IACrI2E,IAAI,EAAEtL,UAAU;IAChBoL,YAAY,EAAED,OAAO,CAACC,YAAY;IAClCC,cAAc,EAAEF,OAAO,CAACE;EAC1B,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AAEF,SAAStG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}