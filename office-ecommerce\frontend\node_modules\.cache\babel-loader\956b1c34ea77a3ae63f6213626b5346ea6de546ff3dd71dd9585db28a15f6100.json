{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, '__esModule', {\n  value: true\n});\nvar react = require('react');\nfunction createStore(createState) {\n  var state;\n  var listeners = new Set();\n  var setState = function setState(partial, replace) {\n    var nextState = typeof partial === 'function' ? partial(state) : partial;\n    if (nextState !== state) {\n      var _previousState = state;\n      state = replace ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach(function (listener) {\n        return listener(state, _previousState);\n      });\n    }\n  };\n  var getState = function getState() {\n    return state;\n  };\n  var subscribeWithSelector = function subscribeWithSelector(listener, selector, equalityFn) {\n    if (selector === void 0) {\n      selector = getState;\n    }\n    if (equalityFn === void 0) {\n      equalityFn = Object.is;\n    }\n    console.warn('[DEPRECATED] Please use `subscribeWithSelector` middleware');\n    var currentSlice = selector(state);\n    function listenerToAdd() {\n      var nextSlice = selector(state);\n      if (!equalityFn(currentSlice, nextSlice)) {\n        var _previousSlice = currentSlice;\n        listener(currentSlice = nextSlice, _previousSlice);\n      }\n    }\n    listeners.add(listenerToAdd);\n    return function () {\n      return listeners.delete(listenerToAdd);\n    };\n  };\n  var subscribe = function subscribe(listener, selector, equalityFn) {\n    if (selector || equalityFn) {\n      return subscribeWithSelector(listener, selector, equalityFn);\n    }\n    listeners.add(listener);\n    return function () {\n      return listeners.delete(listener);\n    };\n  };\n  var destroy = function destroy() {\n    return listeners.clear();\n  };\n  var api = {\n    setState: setState,\n    getState: getState,\n    subscribe: subscribe,\n    destroy: destroy\n  };\n  state = createState(setState, getState, api);\n  return api;\n}\nvar isSSR = typeof window === 'undefined' || !window.navigator || /ServerSideRendering|^Deno\\//.test(window.navigator.userAgent);\nvar useIsomorphicLayoutEffect = isSSR ? react.useEffect : react.useLayoutEffect;\nfunction create(createState) {\n  var api = typeof createState === 'function' ? createStore(createState) : createState;\n  var useStore = function useStore(selector, equalityFn) {\n    if (selector === void 0) {\n      selector = api.getState;\n    }\n    if (equalityFn === void 0) {\n      equalityFn = Object.is;\n    }\n    var _ref = react.useReducer(function (c) {\n        return c + 1;\n      }, 0),\n      forceUpdate = _ref[1];\n    var state = api.getState();\n    var stateRef = react.useRef(state);\n    var selectorRef = react.useRef(selector);\n    var equalityFnRef = react.useRef(equalityFn);\n    var erroredRef = react.useRef(false);\n    var currentSliceRef = react.useRef();\n    if (currentSliceRef.current === undefined) {\n      currentSliceRef.current = selector(state);\n    }\n    var newStateSlice;\n    var hasNewStateSlice = false;\n    if (stateRef.current !== state || selectorRef.current !== selector || equalityFnRef.current !== equalityFn || erroredRef.current) {\n      newStateSlice = selector(state);\n      hasNewStateSlice = !equalityFn(currentSliceRef.current, newStateSlice);\n    }\n    useIsomorphicLayoutEffect(function () {\n      if (hasNewStateSlice) {\n        currentSliceRef.current = newStateSlice;\n      }\n      stateRef.current = state;\n      selectorRef.current = selector;\n      equalityFnRef.current = equalityFn;\n      erroredRef.current = false;\n    });\n    var stateBeforeSubscriptionRef = react.useRef(state);\n    useIsomorphicLayoutEffect(function () {\n      var listener = function listener() {\n        try {\n          var nextState = api.getState();\n          var nextStateSlice = selectorRef.current(nextState);\n          if (!equalityFnRef.current(currentSliceRef.current, nextStateSlice)) {\n            stateRef.current = nextState;\n            currentSliceRef.current = nextStateSlice;\n            forceUpdate();\n          }\n        } catch (error) {\n          erroredRef.current = true;\n          forceUpdate();\n        }\n      };\n      var unsubscribe = api.subscribe(listener);\n      if (api.getState() !== stateBeforeSubscriptionRef.current) {\n        listener();\n      }\n      return unsubscribe;\n    }, []);\n    var sliceToReturn = hasNewStateSlice ? newStateSlice : currentSliceRef.current;\n    react.useDebugValue(sliceToReturn);\n    return sliceToReturn;\n  };\n  Object.assign(useStore, api);\n  useStore[Symbol.iterator] = function () {\n    console.warn('[useStore, api] = create() is deprecated and will be removed in v4');\n    var items = [useStore, api];\n    return {\n      next: function next() {\n        var done = items.length <= 0;\n        return {\n          value: items.shift(),\n          done: done\n        };\n      }\n    };\n  };\n  return useStore;\n}\nexports[\"default\"] = create;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "react", "require", "createStore", "createState", "state", "listeners", "Set", "setState", "partial", "replace", "nextState", "_previousState", "assign", "for<PERSON>ach", "listener", "getState", "subscribeWithSelector", "selector", "equalityFn", "is", "console", "warn", "currentSlice", "listenerToAdd", "nextSlice", "_previousSlice", "add", "delete", "subscribe", "destroy", "clear", "api", "isSSR", "window", "navigator", "test", "userAgent", "useIsomorphicLayoutEffect", "useEffect", "useLayoutEffect", "create", "useStore", "_ref", "useReducer", "c", "forceUpdate", "stateRef", "useRef", "selectorRef", "equalityFnRef", "erroredRef", "currentSliceRef", "current", "undefined", "newStateSlice", "hasNewStateSlice", "stateBeforeSubscriptionRef", "nextStateSlice", "error", "unsubscribe", "sliceToReturn", "useDebugValue", "Symbol", "iterator", "items", "next", "done", "length", "shift"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/zustand/index.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nvar react = require('react');\n\nfunction createStore(createState) {\n  var state;\n  var listeners = new Set();\n\n  var setState = function setState(partial, replace) {\n    var nextState = typeof partial === 'function' ? partial(state) : partial;\n\n    if (nextState !== state) {\n      var _previousState = state;\n      state = replace ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach(function (listener) {\n        return listener(state, _previousState);\n      });\n    }\n  };\n\n  var getState = function getState() {\n    return state;\n  };\n\n  var subscribeWithSelector = function subscribeWithSelector(listener, selector, equalityFn) {\n    if (selector === void 0) {\n      selector = getState;\n    }\n\n    if (equalityFn === void 0) {\n      equalityFn = Object.is;\n    }\n\n    console.warn('[DEPRECATED] Please use `subscribeWithSelector` middleware');\n    var currentSlice = selector(state);\n\n    function listenerToAdd() {\n      var nextSlice = selector(state);\n\n      if (!equalityFn(currentSlice, nextSlice)) {\n        var _previousSlice = currentSlice;\n        listener(currentSlice = nextSlice, _previousSlice);\n      }\n    }\n\n    listeners.add(listenerToAdd);\n    return function () {\n      return listeners.delete(listenerToAdd);\n    };\n  };\n\n  var subscribe = function subscribe(listener, selector, equalityFn) {\n    if (selector || equalityFn) {\n      return subscribeWithSelector(listener, selector, equalityFn);\n    }\n\n    listeners.add(listener);\n    return function () {\n      return listeners.delete(listener);\n    };\n  };\n\n  var destroy = function destroy() {\n    return listeners.clear();\n  };\n\n  var api = {\n    setState: setState,\n    getState: getState,\n    subscribe: subscribe,\n    destroy: destroy\n  };\n  state = createState(setState, getState, api);\n  return api;\n}\n\nvar isSSR = typeof window === 'undefined' || !window.navigator || /ServerSideRendering|^Deno\\//.test(window.navigator.userAgent);\nvar useIsomorphicLayoutEffect = isSSR ? react.useEffect : react.useLayoutEffect;\n\nfunction create(createState) {\n  var api = typeof createState === 'function' ? createStore(createState) : createState;\n\n  var useStore = function useStore(selector, equalityFn) {\n    if (selector === void 0) {\n      selector = api.getState;\n    }\n\n    if (equalityFn === void 0) {\n      equalityFn = Object.is;\n    }\n\n    var _ref = react.useReducer(function (c) {\n      return c + 1;\n    }, 0),\n        forceUpdate = _ref[1];\n\n    var state = api.getState();\n    var stateRef = react.useRef(state);\n    var selectorRef = react.useRef(selector);\n    var equalityFnRef = react.useRef(equalityFn);\n    var erroredRef = react.useRef(false);\n    var currentSliceRef = react.useRef();\n\n    if (currentSliceRef.current === undefined) {\n      currentSliceRef.current = selector(state);\n    }\n\n    var newStateSlice;\n    var hasNewStateSlice = false;\n\n    if (stateRef.current !== state || selectorRef.current !== selector || equalityFnRef.current !== equalityFn || erroredRef.current) {\n      newStateSlice = selector(state);\n      hasNewStateSlice = !equalityFn(currentSliceRef.current, newStateSlice);\n    }\n\n    useIsomorphicLayoutEffect(function () {\n      if (hasNewStateSlice) {\n        currentSliceRef.current = newStateSlice;\n      }\n\n      stateRef.current = state;\n      selectorRef.current = selector;\n      equalityFnRef.current = equalityFn;\n      erroredRef.current = false;\n    });\n    var stateBeforeSubscriptionRef = react.useRef(state);\n    useIsomorphicLayoutEffect(function () {\n      var listener = function listener() {\n        try {\n          var nextState = api.getState();\n          var nextStateSlice = selectorRef.current(nextState);\n\n          if (!equalityFnRef.current(currentSliceRef.current, nextStateSlice)) {\n            stateRef.current = nextState;\n            currentSliceRef.current = nextStateSlice;\n            forceUpdate();\n          }\n        } catch (error) {\n          erroredRef.current = true;\n          forceUpdate();\n        }\n      };\n\n      var unsubscribe = api.subscribe(listener);\n\n      if (api.getState() !== stateBeforeSubscriptionRef.current) {\n        listener();\n      }\n\n      return unsubscribe;\n    }, []);\n    var sliceToReturn = hasNewStateSlice ? newStateSlice : currentSliceRef.current;\n    react.useDebugValue(sliceToReturn);\n    return sliceToReturn;\n  };\n\n  Object.assign(useStore, api);\n\n  useStore[Symbol.iterator] = function () {\n    console.warn('[useStore, api] = create() is deprecated and will be removed in v4');\n    var items = [useStore, api];\n    return {\n      next: function next() {\n        var done = items.length <= 0;\n        return {\n          value: items.shift(),\n          done: done\n        };\n      }\n    };\n  };\n\n  return useStore;\n}\n\nexports[\"default\"] = create;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAE7D,IAAIC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE5B,SAASC,WAAWA,CAACC,WAAW,EAAE;EAChC,IAAIC,KAAK;EACT,IAAIC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EAEzB,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,OAAO,EAAEC,OAAO,EAAE;IACjD,IAAIC,SAAS,GAAG,OAAOF,OAAO,KAAK,UAAU,GAAGA,OAAO,CAACJ,KAAK,CAAC,GAAGI,OAAO;IAExE,IAAIE,SAAS,KAAKN,KAAK,EAAE;MACvB,IAAIO,cAAc,GAAGP,KAAK;MAC1BA,KAAK,GAAGK,OAAO,GAAGC,SAAS,GAAGd,MAAM,CAACgB,MAAM,CAAC,CAAC,CAAC,EAAER,KAAK,EAAEM,SAAS,CAAC;MACjEL,SAAS,CAACQ,OAAO,CAAC,UAAUC,QAAQ,EAAE;QACpC,OAAOA,QAAQ,CAACV,KAAK,EAAEO,cAAc,CAAC;MACxC,CAAC,CAAC;IACJ;EACF,CAAC;EAED,IAAII,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACjC,OAAOX,KAAK;EACd,CAAC;EAED,IAAIY,qBAAqB,GAAG,SAASA,qBAAqBA,CAACF,QAAQ,EAAEG,QAAQ,EAAEC,UAAU,EAAE;IACzF,IAAID,QAAQ,KAAK,KAAK,CAAC,EAAE;MACvBA,QAAQ,GAAGF,QAAQ;IACrB;IAEA,IAAIG,UAAU,KAAK,KAAK,CAAC,EAAE;MACzBA,UAAU,GAAGtB,MAAM,CAACuB,EAAE;IACxB;IAEAC,OAAO,CAACC,IAAI,CAAC,4DAA4D,CAAC;IAC1E,IAAIC,YAAY,GAAGL,QAAQ,CAACb,KAAK,CAAC;IAElC,SAASmB,aAAaA,CAAA,EAAG;MACvB,IAAIC,SAAS,GAAGP,QAAQ,CAACb,KAAK,CAAC;MAE/B,IAAI,CAACc,UAAU,CAACI,YAAY,EAAEE,SAAS,CAAC,EAAE;QACxC,IAAIC,cAAc,GAAGH,YAAY;QACjCR,QAAQ,CAACQ,YAAY,GAAGE,SAAS,EAAEC,cAAc,CAAC;MACpD;IACF;IAEApB,SAAS,CAACqB,GAAG,CAACH,aAAa,CAAC;IAC5B,OAAO,YAAY;MACjB,OAAOlB,SAAS,CAACsB,MAAM,CAACJ,aAAa,CAAC;IACxC,CAAC;EACH,CAAC;EAED,IAAIK,SAAS,GAAG,SAASA,SAASA,CAACd,QAAQ,EAAEG,QAAQ,EAAEC,UAAU,EAAE;IACjE,IAAID,QAAQ,IAAIC,UAAU,EAAE;MAC1B,OAAOF,qBAAqB,CAACF,QAAQ,EAAEG,QAAQ,EAAEC,UAAU,CAAC;IAC9D;IAEAb,SAAS,CAACqB,GAAG,CAACZ,QAAQ,CAAC;IACvB,OAAO,YAAY;MACjB,OAAOT,SAAS,CAACsB,MAAM,CAACb,QAAQ,CAAC;IACnC,CAAC;EACH,CAAC;EAED,IAAIe,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/B,OAAOxB,SAAS,CAACyB,KAAK,CAAC,CAAC;EAC1B,CAAC;EAED,IAAIC,GAAG,GAAG;IACRxB,QAAQ,EAAEA,QAAQ;IAClBQ,QAAQ,EAAEA,QAAQ;IAClBa,SAAS,EAAEA,SAAS;IACpBC,OAAO,EAAEA;EACX,CAAC;EACDzB,KAAK,GAAGD,WAAW,CAACI,QAAQ,EAAEQ,QAAQ,EAAEgB,GAAG,CAAC;EAC5C,OAAOA,GAAG;AACZ;AAEA,IAAIC,KAAK,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAI,CAACA,MAAM,CAACC,SAAS,IAAI,6BAA6B,CAACC,IAAI,CAACF,MAAM,CAACC,SAAS,CAACE,SAAS,CAAC;AAChI,IAAIC,yBAAyB,GAAGL,KAAK,GAAGhC,KAAK,CAACsC,SAAS,GAAGtC,KAAK,CAACuC,eAAe;AAE/E,SAASC,MAAMA,CAACrC,WAAW,EAAE;EAC3B,IAAI4B,GAAG,GAAG,OAAO5B,WAAW,KAAK,UAAU,GAAGD,WAAW,CAACC,WAAW,CAAC,GAAGA,WAAW;EAEpF,IAAIsC,QAAQ,GAAG,SAASA,QAAQA,CAACxB,QAAQ,EAAEC,UAAU,EAAE;IACrD,IAAID,QAAQ,KAAK,KAAK,CAAC,EAAE;MACvBA,QAAQ,GAAGc,GAAG,CAAChB,QAAQ;IACzB;IAEA,IAAIG,UAAU,KAAK,KAAK,CAAC,EAAE;MACzBA,UAAU,GAAGtB,MAAM,CAACuB,EAAE;IACxB;IAEA,IAAIuB,IAAI,GAAG1C,KAAK,CAAC2C,UAAU,CAAC,UAAUC,CAAC,EAAE;QACvC,OAAOA,CAAC,GAAG,CAAC;MACd,CAAC,EAAE,CAAC,CAAC;MACDC,WAAW,GAAGH,IAAI,CAAC,CAAC,CAAC;IAEzB,IAAItC,KAAK,GAAG2B,GAAG,CAAChB,QAAQ,CAAC,CAAC;IAC1B,IAAI+B,QAAQ,GAAG9C,KAAK,CAAC+C,MAAM,CAAC3C,KAAK,CAAC;IAClC,IAAI4C,WAAW,GAAGhD,KAAK,CAAC+C,MAAM,CAAC9B,QAAQ,CAAC;IACxC,IAAIgC,aAAa,GAAGjD,KAAK,CAAC+C,MAAM,CAAC7B,UAAU,CAAC;IAC5C,IAAIgC,UAAU,GAAGlD,KAAK,CAAC+C,MAAM,CAAC,KAAK,CAAC;IACpC,IAAII,eAAe,GAAGnD,KAAK,CAAC+C,MAAM,CAAC,CAAC;IAEpC,IAAII,eAAe,CAACC,OAAO,KAAKC,SAAS,EAAE;MACzCF,eAAe,CAACC,OAAO,GAAGnC,QAAQ,CAACb,KAAK,CAAC;IAC3C;IAEA,IAAIkD,aAAa;IACjB,IAAIC,gBAAgB,GAAG,KAAK;IAE5B,IAAIT,QAAQ,CAACM,OAAO,KAAKhD,KAAK,IAAI4C,WAAW,CAACI,OAAO,KAAKnC,QAAQ,IAAIgC,aAAa,CAACG,OAAO,KAAKlC,UAAU,IAAIgC,UAAU,CAACE,OAAO,EAAE;MAChIE,aAAa,GAAGrC,QAAQ,CAACb,KAAK,CAAC;MAC/BmD,gBAAgB,GAAG,CAACrC,UAAU,CAACiC,eAAe,CAACC,OAAO,EAAEE,aAAa,CAAC;IACxE;IAEAjB,yBAAyB,CAAC,YAAY;MACpC,IAAIkB,gBAAgB,EAAE;QACpBJ,eAAe,CAACC,OAAO,GAAGE,aAAa;MACzC;MAEAR,QAAQ,CAACM,OAAO,GAAGhD,KAAK;MACxB4C,WAAW,CAACI,OAAO,GAAGnC,QAAQ;MAC9BgC,aAAa,CAACG,OAAO,GAAGlC,UAAU;MAClCgC,UAAU,CAACE,OAAO,GAAG,KAAK;IAC5B,CAAC,CAAC;IACF,IAAII,0BAA0B,GAAGxD,KAAK,CAAC+C,MAAM,CAAC3C,KAAK,CAAC;IACpDiC,yBAAyB,CAAC,YAAY;MACpC,IAAIvB,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;QACjC,IAAI;UACF,IAAIJ,SAAS,GAAGqB,GAAG,CAAChB,QAAQ,CAAC,CAAC;UAC9B,IAAI0C,cAAc,GAAGT,WAAW,CAACI,OAAO,CAAC1C,SAAS,CAAC;UAEnD,IAAI,CAACuC,aAAa,CAACG,OAAO,CAACD,eAAe,CAACC,OAAO,EAAEK,cAAc,CAAC,EAAE;YACnEX,QAAQ,CAACM,OAAO,GAAG1C,SAAS;YAC5ByC,eAAe,CAACC,OAAO,GAAGK,cAAc;YACxCZ,WAAW,CAAC,CAAC;UACf;QACF,CAAC,CAAC,OAAOa,KAAK,EAAE;UACdR,UAAU,CAACE,OAAO,GAAG,IAAI;UACzBP,WAAW,CAAC,CAAC;QACf;MACF,CAAC;MAED,IAAIc,WAAW,GAAG5B,GAAG,CAACH,SAAS,CAACd,QAAQ,CAAC;MAEzC,IAAIiB,GAAG,CAAChB,QAAQ,CAAC,CAAC,KAAKyC,0BAA0B,CAACJ,OAAO,EAAE;QACzDtC,QAAQ,CAAC,CAAC;MACZ;MAEA,OAAO6C,WAAW;IACpB,CAAC,EAAE,EAAE,CAAC;IACN,IAAIC,aAAa,GAAGL,gBAAgB,GAAGD,aAAa,GAAGH,eAAe,CAACC,OAAO;IAC9EpD,KAAK,CAAC6D,aAAa,CAACD,aAAa,CAAC;IAClC,OAAOA,aAAa;EACtB,CAAC;EAEDhE,MAAM,CAACgB,MAAM,CAAC6B,QAAQ,EAAEV,GAAG,CAAC;EAE5BU,QAAQ,CAACqB,MAAM,CAACC,QAAQ,CAAC,GAAG,YAAY;IACtC3C,OAAO,CAACC,IAAI,CAAC,oEAAoE,CAAC;IAClF,IAAI2C,KAAK,GAAG,CAACvB,QAAQ,EAAEV,GAAG,CAAC;IAC3B,OAAO;MACLkC,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpB,IAAIC,IAAI,GAAGF,KAAK,CAACG,MAAM,IAAI,CAAC;QAC5B,OAAO;UACLpE,KAAK,EAAEiE,KAAK,CAACI,KAAK,CAAC,CAAC;UACpBF,IAAI,EAAEA;QACR,CAAC;MACH;IACF,CAAC;EACH,CAAC;EAED,OAAOzB,QAAQ;AACjB;AAEA3C,OAAO,CAAC,SAAS,CAAC,GAAG0C,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}