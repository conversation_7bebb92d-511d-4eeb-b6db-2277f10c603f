/*
 Custom build of Typr.ts (https://github.com/fredli74/Typr.ts) for use in Troika text rendering.
  Original MIT license applies: https://github.com/fredli74/Typr.ts/blob/master/LICENSE
 Custom bundle of woff2otf (https://github.com/arty-name/woff2otf) with fflate
  (https://github.com/101arrowz/fflate) for use in Troika text rendering. 
  Original licenses apply: 
  - fflate: https://github.com/101arrowz/fflate/blob/master/LICENSE (MIT)
  - woff2otf.js: https://github.com/arty-name/woff2otf/blob/master/woff2otf.js (Apache2)
*/
'use strict';(function(E,v){"object"===typeof exports&&"undefined"!==typeof module?v(exports,require("three"),require("troika-worker-utils"),require("webgl-sdf-generator"),require("bidi-js"),require("troika-three-utils")):"function"===typeof define&&define.amd?define("exports three troika-worker-utils webgl-sdf-generator bidi-js troika-three-utils".split(" "),v):(E="undefined"!==typeof globalThis?globalThis:E||self,v(E.troika_three_text={},E.THREE,E.troika_worker_utils,E.webgl_sdf_generator,E.bidi_js,
E.troika_three_utils))})(this,function(E,v,W,Y,Q,ta){function ua(e){return e&&"object"===typeof e&&"default"in e?e:{"default":e}}function Pa(e,f,k,l,b,a,c,d,g,h,m=!0){return m?Qa(e,f,k,l,b,a,c,d,g,h).then(null,m=>{va||(console.warn("WebGL SDF generation failed, falling back to JS",m),va=!0);return wa(e,f,k,l,b,a,c,d,g,h)}):wa(e,f,k,l,b,a,c,d,g,h)}function xa(){let e=X();for(;Z.length&&5>X()-e;)Z.shift()();na=Z.length?setTimeout(xa,0):0}function wa(e,f,k,l,b,a,c,d,g,h){let m="TroikaTextSDFGenerator_JS_"+
Ra++%4,D=ya[m];D||(D=ya[m]={workerModule:W.defineWorkerModule({name:m,workerId:m,dependencies:[za["default"],X],init(a,b){let c=a().javascript.generate;return function(...a){let d=b();return{textureData:c(...a),timing:b()-d}}},getTransferables(a){return[a.textureData.buffer]}}),requests:0,idleTimer:null});D.requests++;clearTimeout(D.idleTimer);return D.workerModule(e,f,k,l,b,a).then(({textureData:a,timing:b})=>{let p=X(),n=new Uint8Array(4*a.length);for(let b=0;b<a.length;b++)n[4*b+h]=a[b];aa.webglUtils.renderImageData(c,
n,d,g,e,f,1<<3-h);b+=X()-p;0===--D.requests&&(D.idleTimer=setTimeout(()=>{W.terminateWorker(m)},2E3));return{timing:b}})}function R(){return(self.performance||Date).now()}function Aa(e,f){Ba=!0;e=Ca({},e);let k=R();e.font=Sa(e.font||S.defaultFontURL);e.text=""+e.text;e.sdfGlyphSize=e.sdfGlyphSize||S.sdfGlyphSize;if(null!=e.colorRanges){var l={};for(let a in e.colorRanges)if(e.colorRanges.hasOwnProperty(a)){let b=e.colorRanges[a];"number"!==typeof b&&(b=Ta.set(b).getHex());l[a]=b}e.colorRanges=l}Object.freeze(e);
let {textureWidth:b,sdfExponent:a}=S,{sdfGlyphSize:c}=e,d=b/c*4,g=ea[c];g||(l=document.createElement("canvas"),l.width=b,l.height=256*c/d,g=ea[c]={glyphCount:0,sdfGlyphSize:c,sdfCanvas:l,sdfTexture:new v.Texture(l,void 0,void 0,void 0,v.LinearFilter,v.LinearFilter),contextLost:!1,glyphsByFont:new Map},g.sdfTexture.generateMipmaps=!1,Ua(g));let {sdfTexture:h,sdfCanvas:m}=g,D=g.glyphsByFont.get(e.font);D||g.glyphsByFont.set(e.font,D=new Map);Va(e).then(p=>{let {glyphIds:l,glyphPositions:w,fontSize:n,
unitsPerEm:r,timings:q}=p,x=[],t=new Float32Array(4*l.length),u=n/r,H=0,I=0;var G=R();l.forEach((a,b)=>{var d=D.get(a);if(!d){let {path:b,pathBounds:h}=p.glyphData[a];d=Math.max(h[2]-h[0],h[3]-h[1])/c*(S.sdfMargin*c+.5);var f=g.glyphCount++;D.set(a,d={path:b,atlasIndex:f,sdfViewBox:[h[0]-d,h[1]-d,h[2]+d,h[3]+d]});x.push(d)}({sdfViewBox:a}=d);f=w[I++];let h=w[I++];t[H++]=f+a[0]*u;t[H++]=h+a[1]*u;t[H++]=f+a[2]*u;t[H++]=h+a[3]*u;l[b]=d.atlasIndex});q.quads=(q.quads||0)+(R()-G);let B=R();q.sdf={};G=m.height;
let L=Math.pow(2,Math.ceil(Math.log2(Math.ceil(g.glyphCount/d)*c)));L>G&&(console.info(`Increasing SDF texture size ${G}->${L}`),Wa(m,b,L),h.dispose());Promise.all(x.map(a=>Da(a,g,e.gpuAccelerateSDF).then(({timing:b})=>{q.sdf[a.atlasIndex]=b}))).then(()=>{x.length&&!g.contextLost&&(Ea(g),h.needsUpdate=!0);q.sdfTotal=R()-B;q.total=R()-k;f(Object.freeze({parameters:e,sdfTexture:h,sdfGlyphSize:c,sdfExponent:a,glyphBounds:t,glyphAtlasIndices:l,glyphColors:p.glyphColors,caretPositions:p.caretPositions,
caretHeight:p.caretHeight,chunkedBounds:p.chunkedBounds,ascender:p.ascender,descender:p.descender,lineHeight:p.lineHeight,capHeight:p.capHeight,xHeight:p.xHeight,topBaseline:p.topBaseline,blockBounds:p.blockBounds,visibleBounds:p.visibleBounds,timings:p.timings}))})});Promise.resolve().then(()=>{g.contextLost||m._warm||(aa.webgl.isSupported(m),m._warm=!0)})}function Da({path:e,atlasIndex:f,sdfViewBox:k},{sdfGlyphSize:l,sdfCanvas:b,contextLost:a},c){if(a)return Promise.resolve({timing:-1});let {textureWidth:d,
sdfExponent:g}=S;a=Math.floor(f/4);return Pa(l,l,e,k,Math.max(k[2]-k[0],k[3]-k[1]),g,b,a%(d/l)*l,Math.floor(a/(d/l))*l,f%4,c)}function Ua(e){let f=e.sdfCanvas;f.addEventListener("webglcontextlost",f=>{console.log("Context Lost",f);f.preventDefault();e.contextLost=!0});f.addEventListener("webglcontextrestored",f=>{console.log("Context Restored",f);e.contextLost=!1;let k=[];e.glyphsByFont.forEach(b=>{b.forEach(a=>{k.push(Da(a,e,!0))})});Promise.all(k).then(()=>{Ea(e);e.sdfTexture.needsUpdate=!0})})}
function Ca(e,f){for(let k in f)f.hasOwnProperty(k)&&(e[k]=f[k]);return e}function Sa(e){ba||(ba="undefined"===typeof document?{}:document.createElement("a"));ba.href=e;return ba.href}function Ea(e){if("function"!==typeof createImageBitmap){console.info("Safari<15: applying SDF canvas workaround");let {sdfCanvas:f,sdfTexture:k}=e,{width:l,height:b}=f;e=e.sdfCanvas.getContext("webgl");let a=k.image.data;a&&a.length===l*b*4||(a=new Uint8Array(l*b*4),k.image={width:l,height:b,data:a},k.flipY=!1,k.isDataTexture=
!0);e.readPixels(0,0,l,b,e.RGBA,e.UNSIGNED_BYTE,a)}}function Xa(e){var f=Fa[e];if(!f){f=new v.PlaneGeometry(1,1,e,e);let k=f.clone(),l=f.attributes,b=k.attributes,a=new v.BufferGeometry,c=l.uv.count;for(let a=0;a<c;a++)b.position.array[3*a]*=-1,b.normal.array[3*a+2]*=-1;["position","normal","uv"].forEach(c=>{a.setAttribute(c,new v.Float32BufferAttribute([...l[c].array,...b[c].array],l[c].itemSize))});a.setIndex([...f.index.array,...k.index.array.map(a=>a+c)]);a.translate(.5,.5,0);f=Fa[e]=a}return f}
function oa(e,f,k,l){let b=e.getAttribute(f);k?b&&b.array.length===k.length?(b.array.set(k),b.needsUpdate=!0):(e.setAttribute(f,new v.InstancedBufferAttribute(k,l)),delete e._maxInstanceCount,e.dispose()):b&&e.deleteAttribute(f)}function Ga(e){e=ta.createDerivedMaterial(e,{chained:!0,extensions:{derivatives:!0},uniforms:{uTroikaSDFTexture:{value:null},uTroikaSDFTextureSize:{value:new v.Vector2},uTroikaSDFGlyphSize:{value:0},uTroikaSDFExponent:{value:0},uTroikaTotalBounds:{value:new v.Vector4(0,0,
0,0)},uTroikaClipRect:{value:new v.Vector4(0,0,0,0)},uTroikaDistanceOffset:{value:0},uTroikaOutlineOpacity:{value:0},uTroikaFillOpacity:{value:1},uTroikaPositionOffset:{value:new v.Vector2},uTroikaCurveRadius:{value:0},uTroikaBlurRadius:{value:0},uTroikaStrokeWidth:{value:0},uTroikaStrokeColor:{value:new v.Color},uTroikaStrokeOpacity:{value:1},uTroikaOrient:{value:new v.Matrix3},uTroikaUseGlyphColors:{value:!0},uTroikaSDFDebug:{value:!1}},vertexDefs:"\nuniform vec2 uTroikaSDFTextureSize;\nuniform float uTroikaSDFGlyphSize;\nuniform vec4 uTroikaTotalBounds;\nuniform vec4 uTroikaClipRect;\nuniform mat3 uTroikaOrient;\nuniform bool uTroikaUseGlyphColors;\nuniform float uTroikaDistanceOffset;\nuniform float uTroikaBlurRadius;\nuniform vec2 uTroikaPositionOffset;\nuniform float uTroikaCurveRadius;\nattribute vec4 aTroikaGlyphBounds;\nattribute float aTroikaGlyphIndex;\nattribute vec3 aTroikaGlyphColor;\nvarying vec2 vTroikaGlyphUV;\nvarying vec4 vTroikaTextureUVBounds;\nvarying float vTroikaTextureChannel;\nvarying vec3 vTroikaGlyphColor;\nvarying vec2 vTroikaGlyphDimensions;\n",
vertexTransform:"\nvec4 bounds = aTroikaGlyphBounds;\nbounds.xz += uTroikaPositionOffset.x;\nbounds.yw -= uTroikaPositionOffset.y;\n\nvec4 outlineBounds = vec4(\n  bounds.xy - uTroikaDistanceOffset - uTroikaBlurRadius,\n  bounds.zw + uTroikaDistanceOffset + uTroikaBlurRadius\n);\nvec4 clippedBounds = vec4(\n  clamp(outlineBounds.xy, uTroikaClipRect.xy, uTroikaClipRect.zw),\n  clamp(outlineBounds.zw, uTroikaClipRect.xy, uTroikaClipRect.zw)\n);\n\nvec2 clippedXY = (mix(clippedBounds.xy, clippedBounds.zw, position.xy) - bounds.xy) / (bounds.zw - bounds.xy);\n\nposition.xy = mix(bounds.xy, bounds.zw, clippedXY);\n\nuv = (position.xy - uTroikaTotalBounds.xy) / (uTroikaTotalBounds.zw - uTroikaTotalBounds.xy);\n\nfloat rad = uTroikaCurveRadius;\nif (rad != 0.0) {\n  float angle = position.x / rad;\n  position.xz = vec2(sin(angle) * rad, rad - cos(angle) * rad);\n  normal.xz = vec2(sin(angle), cos(angle));\n}\n  \nposition = uTroikaOrient * position;\nnormal = uTroikaOrient * normal;\n\nvTroikaGlyphUV = clippedXY.xy;\nvTroikaGlyphDimensions = vec2(bounds[2] - bounds[0], bounds[3] - bounds[1]);\n\n\nfloat txCols = uTroikaSDFTextureSize.x / uTroikaSDFGlyphSize;\nvec2 txUvPerSquare = uTroikaSDFGlyphSize / uTroikaSDFTextureSize;\nvec2 txStartUV = txUvPerSquare * vec2(\n  mod(floor(aTroikaGlyphIndex / 4.0), txCols),\n  floor(floor(aTroikaGlyphIndex / 4.0) / txCols)\n);\nvTroikaTextureUVBounds = vec4(txStartUV, vec2(txStartUV) + txUvPerSquare);\nvTroikaTextureChannel = mod(aTroikaGlyphIndex, 4.0);\n",
fragmentDefs:"\nuniform sampler2D uTroikaSDFTexture;\nuniform vec2 uTroikaSDFTextureSize;\nuniform float uTroikaSDFGlyphSize;\nuniform float uTroikaSDFExponent;\nuniform float uTroikaDistanceOffset;\nuniform float uTroikaFillOpacity;\nuniform float uTroikaOutlineOpacity;\nuniform float uTroikaBlurRadius;\nuniform vec3 uTroikaStrokeColor;\nuniform float uTroikaStrokeWidth;\nuniform float uTroikaStrokeOpacity;\nuniform bool uTroikaSDFDebug;\nvarying vec2 vTroikaGlyphUV;\nvarying vec4 vTroikaTextureUVBounds;\nvarying float vTroikaTextureChannel;\nvarying vec2 vTroikaGlyphDimensions;\n\nfloat troikaSdfValueToSignedDistance(float alpha) {\n  // Inverse of exponential encoding in webgl-sdf-generator\n  \n  float maxDimension = max(vTroikaGlyphDimensions.x, vTroikaGlyphDimensions.y);\n  float absDist = (1.0 - pow(2.0 * (alpha > 0.5 ? 1.0 - alpha : alpha), 1.0 / uTroikaSDFExponent)) * maxDimension;\n  float signedDist = absDist * (alpha > 0.5 ? -1.0 : 1.0);\n  return signedDist;\n}\n\nfloat troikaGlyphUvToSdfValue(vec2 glyphUV) {\n  vec2 textureUV = mix(vTroikaTextureUVBounds.xy, vTroikaTextureUVBounds.zw, glyphUV);\n  vec4 rgba = texture2D(uTroikaSDFTexture, textureUV);\n  float ch = floor(vTroikaTextureChannel + 0.5); //NOTE: can't use round() in WebGL1\n  return ch == 0.0 ? rgba.r : ch == 1.0 ? rgba.g : ch == 2.0 ? rgba.b : rgba.a;\n}\n\nfloat troikaGlyphUvToDistance(vec2 uv) {\n  return troikaSdfValueToSignedDistance(troikaGlyphUvToSdfValue(uv));\n}\n\nfloat troikaGetAADist() {\n  \n  #if defined(GL_OES_standard_derivatives) || __VERSION__ >= 300\n  return length(fwidth(vTroikaGlyphUV * vTroikaGlyphDimensions)) * 0.5;\n  #else\n  return vTroikaGlyphDimensions.x / 64.0;\n  #endif\n}\n\nfloat troikaGetFragDistValue() {\n  vec2 clampedGlyphUV = clamp(vTroikaGlyphUV, 0.5 / uTroikaSDFGlyphSize, 1.0 - 0.5 / uTroikaSDFGlyphSize);\n  float distance = troikaGlyphUvToDistance(clampedGlyphUV);\n \n  // Extrapolate distance when outside bounds:\n  distance += clampedGlyphUV == vTroikaGlyphUV ? 0.0 : \n    length((vTroikaGlyphUV - clampedGlyphUV) * vTroikaGlyphDimensions);\n\n  \n\n  return distance;\n}\n\nfloat troikaGetEdgeAlpha(float distance, float distanceOffset, float aaDist) {\n  #if defined(IS_DEPTH_MATERIAL) || defined(IS_DISTANCE_MATERIAL)\n  float alpha = step(-distanceOffset, -distance);\n  #else\n\n  float alpha = smoothstep(\n    distanceOffset + aaDist,\n    distanceOffset - aaDist,\n    distance\n  );\n  #endif\n\n  return alpha;\n}\n",
fragmentColorTransform:"\nfloat aaDist = troikaGetAADist();\nfloat fragDistance = troikaGetFragDistValue();\nfloat edgeAlpha = uTroikaSDFDebug ?\n  troikaGlyphUvToSdfValue(vTroikaGlyphUV) :\n  troikaGetEdgeAlpha(fragDistance, uTroikaDistanceOffset, max(aaDist, uTroikaBlurRadius));\n\n#if !defined(IS_DEPTH_MATERIAL) && !defined(IS_DISTANCE_MATERIAL)\nvec4 fillRGBA = gl_FragColor;\nfillRGBA.a *= uTroikaFillOpacity;\nvec4 strokeRGBA = uTroikaStrokeWidth == 0.0 ? fillRGBA : vec4(uTroikaStrokeColor, uTroikaStrokeOpacity);\nif (fillRGBA.a == 0.0) fillRGBA.rgb = strokeRGBA.rgb;\ngl_FragColor = mix(fillRGBA, strokeRGBA, smoothstep(\n  -uTroikaStrokeWidth - aaDist,\n  -uTroikaStrokeWidth + aaDist,\n  fragDistance\n));\ngl_FragColor.a *= edgeAlpha;\n#endif\n\nif (edgeAlpha == 0.0) {\n  discard;\n}\n",
customRewriter({vertexShader:f,fragmentShader:e}){let k=/\buniform\s+vec3\s+diffuse\b/;k.test(e)&&(e=e.replace(k,"varying vec3 vTroikaGlyphColor").replace(/\bdiffuse\b/g,"vTroikaGlyphColor"),k.test(f)||(f=f.replace(ta.voidMainRegExp,"uniform vec3 diffuse;\n$&\nvTroikaGlyphColor = uTroikaUseGlyphColors ? aTroikaGlyphColor / 255.0 : diffuse;\n")));return{vertexShader:f,fragmentShader:e}}});e.transparent=!0;Object.defineProperties(e,{isTroikaTextMaterial:{value:!0},shadowSide:{get(){return this.side},
set(){}}});return e}function Ha(e){return Array.isArray(e)?e[0]:e}function Ya(e){let f=Ia.get(e);if(!f){let {caretPositions:k,caretHeight:l}=e;f=new Map;for(let b=0;b<k.length;b+=3){let a=k[b+2],c=f.get(a);c||f.set(a,c=[]);c.push({x:k[b],y:a,height:l,charIndex:b/3});b+3>=k.length&&c.push({x:k[b+1],y:a,height:l,charIndex:b/3+1})}}Ia.set(e,f);return f}var za=ua(Y);Y=ua(Q);let X=()=>(self.performance||Date).now(),aa=za["default"](),va,Z=[],na=0,Qa=(...e)=>new Promise((f,k)=>{Z.push(()=>{const l=X();
try{aa.webgl.generateIntoCanvas(...e),f({timing:X()-l})}catch(b){k(b)}});na||(na=setTimeout(xa,0))}),ya={},Ra=0,Wa=aa.webglUtils.resizeWebGLCanvasWithoutClearing;Q=W.defineWorkerModule({name:"Typr Font Parser",dependencies:[function(){return"undefined"==typeof window&&(self.window=self),function(e){var f={parse:function(b){var a=f._bin;b=new Uint8Array(b);if("ttcf"==a.readASCII(b,0,4)){var c=4;a.readUshort(b,c);c+=2;a.readUshort(b,c);c+=2;var d=a.readUint(b,c);c+=4;for(var g=[],h=0;h<d;h++){var m=
a.readUint(b,c);c+=4;g.push(f._readFont(b,m))}return g}return[f._readFont(b,0)]},_readFont:function(b,a){var c=f._bin,d=a;c.readFixed(b,a);a+=4;var g=c.readUshort(b,a);a+=2;c.readUshort(b,a);a+=2;c.readUshort(b,a);a+=2;c.readUshort(b,a);a+=2;var h="cmap;head;hhea;maxp;hmtx;name;OS/2;post;loca;glyf;kern;CFF ;GPOS;GSUB;SVG ".split(";");d={_data:b,_offset:d};for(var m={},e=0;e<g;e++){var p=c.readASCII(b,a,4);a+=4;c.readUint(b,a);a+=4;var k=c.readUint(b,a);a+=4;var l=c.readUint(b,a);a+=4;m[p]={offset:k,
length:l}}for(e=0;e<h.length;e++)a=h[e],m[a]&&(d[a.trim()]=f[a.trim()].parse(b,m[a].offset,m[a].length,d));return d},_tabOffset:function(b,a,c){var d=f._bin,g=d.readUshort(b,c+4);c+=12;for(var h=0;h<g;h++){var m=d.readASCII(b,c,4);c+=4;d.readUint(b,c);c+=4;var e=d.readUint(b,c);if(c+=4,d.readUint(b,c),c+=4,m==a)return e}return 0}};f._bin={readFixed:function(b,a){return(b[a]<<8|b[a+1])+(b[a+2]<<8|b[a+3])/65540},readF2dot14:function(b,a){return f._bin.readShort(b,a)/16384},readInt:function(b,a){return f._bin._view(b).getInt32(a)},
readInt8:function(b,a){return f._bin._view(b).getInt8(a)},readShort:function(b,a){return f._bin._view(b).getInt16(a)},readUshort:function(b,a){return f._bin._view(b).getUint16(a)},readUshorts:function(b,a,c){for(var d=[],g=0;g<c;g++)d.push(f._bin.readUshort(b,a+2*g));return d},readUint:function(b,a){return f._bin._view(b).getUint32(a)},readUint64:function(b,a){return 4294967296*f._bin.readUint(b,a)+f._bin.readUint(b,a+4)},readASCII:function(b,a,c){for(var d="",f=0;f<c;f++)d+=String.fromCharCode(b[a+
f]);return d},readUnicode:function(b,a,c){for(var d="",f=0;f<c;f++){var h=b[a++]<<8|b[a++];d+=String.fromCharCode(h)}return d},_tdec:"undefined"!=typeof window&&window.TextDecoder?new window.TextDecoder:null,readUTF8:function(b,a,c){var d=f._bin._tdec;return d&&0==a&&c==b.length?d.decode(b):f._bin.readASCII(b,a,c)},readBytes:function(b,a,c){for(var d=[],f=0;f<c;f++)d.push(b[a+f]);return d},readASCIIArray:function(b,a,c){for(var d=[],f=0;f<c;f++)d.push(String.fromCharCode(b[a+f]));return d},_view:function(b){return b._dataView||
(b._dataView=b.buffer?new DataView(b.buffer,b.byteOffset,b.byteLength):new DataView((new Uint8Array(b)).buffer))}};f._lctf={};f._lctf.parse=function(b,a,c,d,g){var h=f._bin;c={};d=a;h.readFixed(b,a);a+=4;var m=h.readUshort(b,a);a+=2;var e=h.readUshort(b,a);a=h.readUshort(b,a+2);return c.scriptList=f._lctf.readScriptList(b,d+m),c.featureList=f._lctf.readFeatureList(b,d+e),c.lookupList=f._lctf.readLookupList(b,d+a,g),c};f._lctf.readLookupList=function(b,a,c){var d=f._bin,g=a,h=[],m=d.readUshort(b,a);
a+=2;for(var e=0;e<m;e++){var p=d.readUshort(b,a);a+=2;p=f._lctf.readLookupTable(b,g+p,c);h.push(p)}return h};f._lctf.readLookupTable=function(b,a,c){var d=f._bin,g=a,h={tabs:[]};h.ltype=d.readUshort(b,a);a+=2;h.flag=d.readUshort(b,a);a+=2;var m=d.readUshort(b,a);a+=2;for(var e=h.ltype,p=0;p<m;p++){var k=d.readUshort(b,a);a+=2;k=c(b,e,g+k,h);h.tabs.push(k)}return h};f._lctf.numOfOnes=function(b){for(var a=0,c=0;32>c;c++)0!=(b>>>c&1)&&a++;return a};f._lctf.readClassDef=function(b,a){var c=f._bin,d=
[],g=c.readUshort(b,a);if(a+=2,1==g){var h=c.readUshort(b,a);a+=2;var m=c.readUshort(b,a);a+=2;for(var e=0;e<m;e++)d.push(h+e),d.push(h+e),d.push(c.readUshort(b,a)),a+=2}if(2==g)for(g=c.readUshort(b,a),a+=2,e=0;e<g;e++)d.push(c.readUshort(b,a)),a+=2,d.push(c.readUshort(b,a)),a+=2,d.push(c.readUshort(b,a)),a+=2;return d};f._lctf.getInterval=function(b,a){for(var c=0;c<b.length;c+=3){var d=b[c],f=b[c+1];if(b[c+2],d<=a&&a<=f)return c}return-1};f._lctf.readCoverage=function(b,a){var c=f._bin,d={};d.fmt=
c.readUshort(b,a);a+=2;var g=c.readUshort(b,a);return a+=2,1==d.fmt&&(d.tab=c.readUshorts(b,a,g)),2==d.fmt&&(d.tab=c.readUshorts(b,a,3*g)),d};f._lctf.coverageIndex=function(b,a){var c=b.tab;return 1==b.fmt?c.indexOf(a):2==b.fmt&&(b=f._lctf.getInterval(c,a),-1!=b)?c[b+2]+(a-c[b]):-1};f._lctf.readFeatureList=function(b,a){var c=f._bin,d=a,g=[],h=c.readUshort(b,a);a+=2;for(var m=0;m<h;m++){var e=c.readASCII(b,a,4);a+=4;var p=c.readUshort(b,a);a+=2;p=f._lctf.readFeatureTable(b,d+p);p.tag=e.trim();g.push(p)}return g};
f._lctf.readFeatureTable=function(b,a){var c=f._bin,d=a,g={},h=c.readUshort(b,a);a+=2;0<h&&(g.featureParams=d+h);d=c.readUshort(b,a);a+=2;g.tab=[];for(h=0;h<d;h++)g.tab.push(c.readUshort(b,a+2*h));return g};f._lctf.readScriptList=function(b,a){var c=f._bin,d=a,g={},h=c.readUshort(b,a);a+=2;for(var m=0;m<h;m++){var e=c.readASCII(b,a,4);a+=4;var p=c.readUshort(b,a);a+=2;g[e.trim()]=f._lctf.readScriptTable(b,d+p)}return g};f._lctf.readScriptTable=function(b,a){var c=f._bin,d=a,g={},h=c.readUshort(b,
a);a+=2;g.default=f._lctf.readLangSysTable(b,d+h);h=c.readUshort(b,a);a+=2;for(var m=0;m<h;m++){var e=c.readASCII(b,a,4);a+=4;var p=c.readUshort(b,a);a+=2;g[e.trim()]=f._lctf.readLangSysTable(b,d+p)}return g};f._lctf.readLangSysTable=function(b,a){var c=f._bin,d={};c.readUshort(b,a);a+=2;d.reqFeature=c.readUshort(b,a);a+=2;var g=c.readUshort(b,a);return a+=2,d.features=c.readUshorts(b,a,g),d};f.CFF={};f.CFF.parse=function(b,a,c){var d=f._bin;(b=new Uint8Array(b.buffer,a,c))[a=0];b[++a];b[++a];b[++a];
a++;var g=[];a=f.CFF.readIndex(b,a,g);var h=[];for(c=0;c<g.length-1;c++)h.push(d.readASCII(b,a+g[c],g[c+1]-g[c]));a+=g[g.length-1];g=[];a=f.CFF.readIndex(b,a,g);h=[];for(c=0;c<g.length-1;c++)h.push(f.CFF.readDict(b,a+g[c],a+g[c+1]));a+=g[g.length-1];g=h[0];var m=[];a=f.CFF.readIndex(b,a,m);h=[];for(c=0;c<m.length-1;c++)h.push(d.readASCII(b,a+m[c],m[c+1]-m[c]));if(a+=m[m.length-1],f.CFF.readSubrs(b,a,g),g.CharStrings){a=g.CharStrings;m=[];a=f.CFF.readIndex(b,a,m);var e=[];for(c=0;c<m.length-1;c++)e.push(d.readBytes(b,
a+m[c],m[c+1]-m[c]));g.CharStrings=e}if(g.ROS){a=g.FDArray;m=[];a=f.CFF.readIndex(b,a,m);g.FDArray=[];for(c=0;c<m.length-1;c++)e=f.CFF.readDict(b,a+m[c],a+m[c+1]),f.CFF._readFDict(b,e,h),g.FDArray.push(e);a=g.FDSelect;g.FDSelect=[];c=b[a];if(a++,3!=c)throw c;m=d.readUshort(b,a);a+=2;for(c=0;c<m+1;c++)g.FDSelect.push(d.readUshort(b,a),b[a+2]),a+=3}return g.Encoding&&(g.Encoding=f.CFF.readEncoding(b,g.Encoding,g.CharStrings.length)),g.charset&&(g.charset=f.CFF.readCharset(b,g.charset,g.CharStrings.length)),
f.CFF._readFDict(b,g,h),g};f.CFF._readFDict=function(b,a,c){var d,g;for(g in a.Private&&(d=a.Private[1],a.Private=f.CFF.readDict(b,d,d+a.Private[0]),a.Private.Subrs&&f.CFF.readSubrs(b,d+a.Private.Subrs,a.Private)),a)-1!="FamilyName FontName FullName Notice version Copyright".split(" ").indexOf(g)&&(a[g]=c[a[g]-426+35])};f.CFF.readSubrs=function(b,a,c){var d=f._bin,g=[];a=f.CFF.readIndex(b,a,g);var h=g.length;c.Bias=1240>h?107:33900>h?1131:32768;c.Subrs=[];for(h=0;h<g.length-1;h++)c.Subrs.push(d.readBytes(b,
a+g[h],g[h+1]-g[h]))};f.CFF.tableSE=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,
0,111,112,113,114,0,115,116,117,118,119,120,121,122,0,123,0,124,125,126,127,128,129,130,131,0,132,133,0,134,135,136,137,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,138,0,139,0,0,0,0,140,141,142,143,0,0,0,0,0,144,0,0,0,145,0,0,146,147,148,149,0,0,0,0];f.CFF.glyphByUnicode=function(b,a){for(var c=0;c<b.charset.length;c++)if(b.charset[c]==a)return c;return-1};f.CFF.glyphBySE=function(b,a){return 0>a||255<a?-1:f.CFF.glyphByUnicode(b,f.CFF.tableSE[a])};f.CFF.readEncoding=function(b,a,c){f._bin;c=[".notdef"];var d=
b[a];if(a++,0!=d)throw"error: unknown encoding format: "+d;d=b[a];a++;for(var g=0;g<d;g++)c.push(b[a+g]);return c};f.CFF.readCharset=function(b,a,c){var d=f._bin,g=[".notdef"],h=b[a];if(a++,0==h)for(var m=0;m<c;m++){var e=d.readUshort(b,a);a+=2;g.push(e)}else{if(1!=h&&2!=h)throw"error: format: "+h;for(;g.length<c;){e=d.readUshort(b,a);a+=2;var p=0;1==h?(p=b[a],a++):(p=d.readUshort(b,a),a+=2);for(m=0;m<=p;m++)g.push(e),e++}}return g};f.CFF.readIndex=function(b,a,c){var d=f._bin,g=d.readUshort(b,a)+
1,h=b[a+=2];if(a++,1==h)for(var m=0;m<g;m++)c.push(b[a+m]);else if(2==h)for(m=0;m<g;m++)c.push(d.readUshort(b,a+2*m));else if(3==h)for(m=0;m<g;m++)c.push(16777215&d.readUint(b,a+3*m-1));else if(1!=g)throw"unsupported offset size: "+h+", count: "+g;return a+g*h-1};f.CFF.getCharString=function(b,a,c){var d=f._bin,g=b[a],h=b[a+1];b[a+2];b[a+3];b[a+4];var m=1,e=null,p=null;20>=g&&(e=g,m=1);12==g&&(e=100*g+h,m=2);21<=g&&27>=g&&(e=g,m=1);28==g&&(p=d.readShort(b,a+1),m=3);29<=g&&31>=g&&(e=g,m=1);32<=g&&
246>=g&&(p=g-139,m=1);247<=g&&250>=g&&(p=256*(g-247)+h+108,m=2);251<=g&&254>=g&&(p=256*-(g-251)-h-108,m=2);255==g&&(p=d.readInt(b,a+1)/65535,m=5);c.val=null!=p?p:"o"+e;c.size=m};f.CFF.readCharString=function(b,a,c){c=a+c;for(var d=f._bin,g=[];a<c;){var h=b[a],m=b[a+1];b[a+2];b[a+3];b[a+4];var e=1,p=null,k=null;20>=h&&(p=h,e=1);12==h&&(p=100*h+m,e=2);19!=h&&20!=h||(p=h,e=2);21<=h&&27>=h&&(p=h,e=1);28==h&&(k=d.readShort(b,a+1),e=3);29<=h&&31>=h&&(p=h,e=1);32<=h&&246>=h&&(k=h-139,e=1);247<=h&&250>=h&&
(k=256*(h-247)+m+108,e=2);251<=h&&254>=h&&(k=256*-(h-251)-m-108,e=2);255==h&&(k=d.readInt(b,a+1)/65535,e=5);g.push(null!=k?k:"o"+p);a+=e}return g};f.CFF.readDict=function(b,a,c){for(var d=f._bin,g={},h=[];a<c;){var m=b[a],e=b[a+1];b[a+2];b[a+3];b[a+4];var k=1,l=null,w=null;if(28==m&&(w=d.readShort(b,a+1),k=3),29==m&&(w=d.readInt(b,a+1),k=5),32<=m&&246>=m&&(w=m-139,k=1),247<=m&&250>=m&&(w=256*(m-247)+e+108,k=2),251<=m&&254>=m&&(w=256*-(m-251)-e-108,k=2),255==m)throw d.readInt(b,a+1),"unknown number";
if(30==m){w=[];for(k=1;;){var n=b[a+k];k++;var r=n>>4;n&=15;if(15!=r&&w.push(r),15!=n&&w.push(n),15==n)break}r="";n=[0,1,2,3,4,5,6,7,8,9,".","e","e-","reserved","-","endOfNumber"];for(var q=0;q<w.length;q++)r+=n[w[q]];w=parseFloat(r)}21>=m&&(l="version Notice FullName FamilyName Weight FontBBox BlueValues OtherBlues FamilyBlues FamilyOtherBlues StdHW StdVW escape UniqueID XUID charset Encoding CharStrings Private Subrs defaultWidthX nominalWidthX".split(" ")[m],k=1,12==m)&&(l=["Copyright","isFixedPitch",
"ItalicAngle","UnderlinePosition","UnderlineThickness","PaintType","CharstringType","FontMatrix","StrokeWidth","BlueScale","BlueShift","BlueFuzz","StemSnapH","StemSnapV","ForceBold",0,0,"LanguageGroup","ExpansionFactor","initialRandomSeed","SyntheticBase","PostScript","BaseFontName","BaseFontBlend",0,0,0,0,0,0,"ROS","CIDFontVersion","CIDFontRevision","CIDFontType","CIDCount","UIDBase","FDArray","FDSelect","FontName"][e],k=2);null!=l?(g[l]=1==h.length?h[0]:h,h=[]):h.push(w);a+=k}return g};f.cmap={};
f.cmap.parse=function(b,a,c){b=new Uint8Array(b.buffer,a,c);a=0;c=f._bin;var d={};c.readUshort(b,a);a+=2;var g=c.readUshort(b,a);a+=2;var h=[];d.tables=[];for(var m=0;m<g;m++){var e=c.readUshort(b,a);a+=2;var k=c.readUshort(b,a);a+=2;var l=c.readUint(b,a);a+=4;var w="p"+e+"e"+k,n=h.indexOf(l);if(-1==n){var r;n=d.tables.length;h.push(l);var q=c.readUshort(b,l);0==q?r=f.cmap.parse0(b,l):4==q?r=f.cmap.parse4(b,l):6==q?r=f.cmap.parse6(b,l):12==q?r=f.cmap.parse12(b,l):console.debug("unknown format: "+
q,e,k,l);d.tables.push(r)}if(null!=d[w])throw"multiple tables for one platform+encoding";d[w]=n}return d};f.cmap.parse0=function(b,a){var c=f._bin,d={};d.format=c.readUshort(b,a);a+=2;var g=c.readUshort(b,a);a+=2;c.readUshort(b,a);a+=2;d.map=[];for(c=0;c<g-6;c++)d.map.push(b[a+c]);return d};f.cmap.parse4=function(b,a){var c=f._bin,d=a,g={};g.format=c.readUshort(b,a);a+=2;var h=c.readUshort(b,a);a+=2;c.readUshort(b,a);a+=2;var m=c.readUshort(b,a);a+=2;m/=2;g.searchRange=c.readUshort(b,a);a+=2;g.entrySelector=
c.readUshort(b,a);a+=2;g.rangeShift=c.readUshort(b,a);a+=2;g.endCount=c.readUshorts(b,a,m);a=a+2*m+2;g.startCount=c.readUshorts(b,a,m);a+=2*m;g.idDelta=[];for(var e=0;e<m;e++)g.idDelta.push(c.readShort(b,a)),a+=2;g.idRangeOffset=c.readUshorts(b,a,m);a+=2*m;for(g.glyphIdArray=[];a<d+h;)g.glyphIdArray.push(c.readUshort(b,a)),a+=2;return g};f.cmap.parse6=function(b,a){var c=f._bin,d={};d.format=c.readUshort(b,a);a+=2;c.readUshort(b,a);a+=2;c.readUshort(b,a);a+=2;d.firstCode=c.readUshort(b,a);a+=2;var g=
c.readUshort(b,a);a+=2;d.glyphIdArray=[];for(var h=0;h<g;h++)d.glyphIdArray.push(c.readUshort(b,a)),a+=2;return d};f.cmap.parse12=function(b,a){var c=f._bin,d={};d.format=c.readUshort(b,a);a=a+2+2;c.readUint(b,a);a+=4;c.readUint(b,a);a+=4;var g=c.readUint(b,a);a+=4;d.groups=[];for(var h=0;h<g;h++){var e=a+12*h,k=c.readUint(b,e+0),p=c.readUint(b,e+4);e=c.readUint(b,e+8);d.groups.push([k,p,e])}return d};f.glyf={};f.glyf.parse=function(b,a,c,d){b=[];for(a=0;a<d.maxp.numGlyphs;a++)b.push(null);return b};
f.glyf._parseGlyf=function(b,a){var c=f._bin,d=b._data,g=f._tabOffset(d,"glyf",b._offset)+b.loca[a];if(b.loca[a]==b.loca[a+1])return null;b={};if(b.noc=c.readShort(d,g),g+=2,b.xMin=c.readShort(d,g),g+=2,b.yMin=c.readShort(d,g),g+=2,b.xMax=c.readShort(d,g),g+=2,b.yMax=c.readShort(d,g),g+=2,b.xMin>=b.xMax||b.yMin>=b.yMax)return null;if(0<b.noc){b.endPts=[];for(a=0;a<b.noc;a++)b.endPts.push(c.readUshort(d,g)),g+=2;a=c.readUshort(d,g);if(g+=2,d.length-g<a)return null;b.instructions=c.readBytes(d,g,a);
g+=a;var h=b.endPts[b.noc-1]+1;b.flags=[];for(a=0;a<h;a++){var e=d[g];if(g++,b.flags.push(e),0!=(8&e)){var k=d[g];g++;for(var p=0;p<k;p++)b.flags.push(e),a++}}b.xs=[];for(a=0;a<h;a++)e=0!=(2&b.flags[a]),k=0!=(16&b.flags[a]),e?(b.xs.push(k?d[g]:-d[g]),g++):k?b.xs.push(0):(b.xs.push(c.readShort(d,g)),g+=2);b.ys=[];for(a=0;a<h;a++)e=0!=(4&b.flags[a]),k=0!=(32&b.flags[a]),e?(b.ys.push(k?d[g]:-d[g]),g++):k?b.ys.push(0):(b.ys.push(c.readShort(d,g)),g+=2);for(a=g=d=0;a<h;a++)d+=b.xs[a],g+=b.ys[a],b.xs[a]=
d,b.ys[a]=g}else{b.parts=[];do a=c.readUshort(d,g),g+=2,h={m:{a:1,b:0,c:0,d:1,tx:0,ty:0},p1:-1,p2:-1},(b.parts.push(h),h.glyphIndex=c.readUshort(d,g),g+=2,1&a)?(e=c.readShort(d,g),g+=2,k=c.readShort(d,g),g+=2):(e=c.readInt8(d,g),g++,k=c.readInt8(d,g),g++),2&a?(h.m.tx=e,h.m.ty=k):(h.p1=e,h.p2=k),8&a?(h.m.a=h.m.d=c.readF2dot14(d,g),g+=2):64&a?(h.m.a=c.readF2dot14(d,g),g+=2,h.m.d=c.readF2dot14(d,g),g+=2):128&a&&(h.m.a=c.readF2dot14(d,g),g+=2,h.m.b=c.readF2dot14(d,g),g+=2,h.m.c=c.readF2dot14(d,g),g+=
2,h.m.d=c.readF2dot14(d,g),g+=2);while(32&a);if(256&a)for(c=c.readUshort(d,g),g+=2,b.instr=[],a=0;a<c;a++)b.instr.push(d[g]),g++}return b};f.GPOS={};f.GPOS.parse=function(b,a,c,d){return f._lctf.parse(b,a,c,d,f.GPOS.subt)};f.GPOS.subt=function(b,a,c,d){var g=f._bin,h=c,e={};if(e.fmt=g.readUshort(b,c),c+=2,1==a||2==a||3==a||7==a||8==a&&2>=e.fmt){var k=g.readUshort(b,c);c+=2;e.coverage=f._lctf.readCoverage(b,k+h)}if(1==a&&1==e.fmt)d=g.readUshort(b,c),c+=2,f._lctf.numOfOnes(d),0!=d&&(e.pos=f.GPOS.readValueRecord(b,
c,d));else if(2==a&&1<=e.fmt&&2>=e.fmt){d=g.readUshort(b,c);c+=2;k=g.readUshort(b,c);c+=2;a=f._lctf.numOfOnes(d);var p=f._lctf.numOfOnes(k);if(1==e.fmt){e.pairsets=[];var l=g.readUshort(b,c);c+=2;for(var w=0;w<l;w++){var n=h+g.readUshort(b,c);c+=2;var r=g.readUshort(b,n);n+=2;for(var q=[],x=0;x<r;x++){var t=g.readUshort(b,n);n+=2;0!=d&&(u=f.GPOS.readValueRecord(b,n,d),n+=2*a);0!=k&&(H=f.GPOS.readValueRecord(b,n,k),n+=2*p);q.push({gid2:t,val1:u,val2:H})}e.pairsets.push(q)}}if(2==e.fmt)for(u=g.readUshort(b,
c),c+=2,H=g.readUshort(b,c),c+=2,l=g.readUshort(b,c),c+=2,g=g.readUshort(b,c),c+=2,e.classDef1=f._lctf.readClassDef(b,h+u),e.classDef2=f._lctf.readClassDef(b,h+H),e.matrix=[],w=0;w<l;w++){h=[];for(x=0;x<g;x++){var u=null,H=null;0!=d&&(u=f.GPOS.readValueRecord(b,c,d),c+=2*a);0!=k&&(H=f.GPOS.readValueRecord(b,c,k),c+=2*p);h.push({val1:u,val2:H})}e.matrix.push(h)}}else{if(9==a&&1==e.fmt){e=g.readUshort(b,c);c=g.readUint(b,c+2);if(9==d.ltype)d.ltype=e;else if(d.ltype!=e)throw"invalid extension substitution";
return f.GPOS.subt(b,d.ltype,h+c)}console.debug("unsupported GPOS table LookupType",a,"format",e.fmt)}return e};f.GPOS.readValueRecord=function(b,a,c){var d=f._bin,g=[];return g.push(1&c?d.readShort(b,a):0),a+=1&c?2:0,g.push(2&c?d.readShort(b,a):0),a+=2&c?2:0,g.push(4&c?d.readShort(b,a):0),a+=4&c?2:0,g.push(8&c?d.readShort(b,a):0),g};f.GSUB={};f.GSUB.parse=function(b,a,c,d){return f._lctf.parse(b,a,c,d,f.GSUB.subt)};f.GSUB.subt=function(b,a,c,d){var g=f._bin,h=c,e={};if(e.fmt=g.readUshort(b,c),c+=
2,1!=a&&4!=a&&5!=a&&6!=a)return null;if(1==a||4==a||5==a&&2>=e.fmt||6==a&&2>=e.fmt){var k=g.readUshort(b,c);c+=2;e.coverage=f._lctf.readCoverage(b,h+k)}if(1==a&&1<=e.fmt&&2>=e.fmt)1==e.fmt?e.delta=g.readShort(b,c):2==e.fmt&&(a=g.readUshort(b,c),e.newg=g.readUshorts(b,c+2,a));else if(4==a)for(e.vals=[],a=g.readUshort(b,c),c+=2,d=0;d<a;d++)k=g.readUshort(b,c),c+=2,e.vals.push(f.GSUB.readLigatureSet(b,h+k));else if(5==a&&2==e.fmt){if(2==e.fmt)for(d=g.readUshort(b,c),c+=2,e.cDef=f._lctf.readClassDef(b,
h+d),e.scset=[],a=g.readUshort(b,c),c+=2,d=0;d<a;d++)k=g.readUshort(b,c),c+=2,e.scset.push(0==k?null:f.GSUB.readSubClassSet(b,h+k))}else if(6==a&&3==e.fmt){if(3==e.fmt){for(d=0;3>d;d++){a=g.readUshort(b,c);c+=2;k=[];for(var p=0;p<a;p++)k.push(f._lctf.readCoverage(b,h+g.readUshort(b,c+2*p)));c+=2*a;0==d&&(e.backCvg=k);1==d&&(e.inptCvg=k);2==d&&(e.ahedCvg=k)}a=g.readUshort(b,c);e.lookupRec=f.GSUB.readSubstLookupRecords(b,c+2,a)}}else{if(7==a&&1==e.fmt){e=g.readUshort(b,c);c=g.readUint(b,c+2);if(9==
d.ltype)d.ltype=e;else if(d.ltype!=e)throw"invalid extension substitution";return f.GSUB.subt(b,d.ltype,h+c)}console.debug("unsupported GSUB table LookupType",a,"format",e.fmt)}return e};f.GSUB.readSubClassSet=function(b,a){var c=f._bin.readUshort,d=a,g=[],h=c(b,a);a+=2;for(var e=0;e<h;e++){var k=c(b,a);a+=2;g.push(f.GSUB.readSubClassRule(b,d+k))}return g};f.GSUB.readSubClassRule=function(b,a){var c=f._bin.readUshort,d={},g=c(b,a),h=c(b,a+=2);a+=2;d.input=[];for(var e=0;e<g-1;e++)d.input.push(c(b,
a)),a+=2;return d.substLookupRecords=f.GSUB.readSubstLookupRecords(b,a,h),d};f.GSUB.readSubstLookupRecords=function(b,a,c){for(var d=f._bin.readUshort,g=[],h=0;h<c;h++)g.push(d(b,a),d(b,a+2)),a+=4;return g};f.GSUB.readChainSubClassSet=function(b,a){var c=f._bin,d=a,g=[],h=c.readUshort(b,a);a+=2;for(var e=0;e<h;e++){var k=c.readUshort(b,a);a+=2;g.push(f.GSUB.readChainSubClassRule(b,d+k))}return g};f.GSUB.readChainSubClassRule=function(b,a){for(var c=f._bin,d={},g=["backtrack","input","lookahead"],
h=0;h<g.length;h++){var e=c.readUshort(b,a);a+=2;1==h&&e--;d[g[h]]=c.readUshorts(b,a,e);a+=2*d[g[h]].length}e=c.readUshort(b,a);return a+=2,d.subst=c.readUshorts(b,a,2*e),d};f.GSUB.readLigatureSet=function(b,a){var c=f._bin,d=a,g=[],h=c.readUshort(b,a);a+=2;for(var e=0;e<h;e++){var k=c.readUshort(b,a);a+=2;g.push(f.GSUB.readLigature(b,d+k))}return g};f.GSUB.readLigature=function(b,a){var c=f._bin,d={chain:[]};d.nglyph=c.readUshort(b,a);a+=2;var g=c.readUshort(b,a);a+=2;for(var h=0;h<g-1;h++)d.chain.push(c.readUshort(b,
a)),a+=2;return d};f.head={};f.head.parse=function(b,a,c){c=f._bin;var d={};return c.readFixed(b,a),a+=4,d.fontRevision=c.readFixed(b,a),a+=4,c.readUint(b,a),a+=4,c.readUint(b,a),a+=4,d.flags=c.readUshort(b,a),a+=2,d.unitsPerEm=c.readUshort(b,a),a+=2,d.created=c.readUint64(b,a),a+=8,d.modified=c.readUint64(b,a),a+=8,d.xMin=c.readShort(b,a),a+=2,d.yMin=c.readShort(b,a),a+=2,d.xMax=c.readShort(b,a),a+=2,d.yMax=c.readShort(b,a),a+=2,d.macStyle=c.readUshort(b,a),a+=2,d.lowestRecPPEM=c.readUshort(b,a),
a+=2,d.fontDirectionHint=c.readShort(b,a),a+=2,d.indexToLocFormat=c.readShort(b,a),a+=2,d.glyphDataFormat=c.readShort(b,a),d};f.hhea={};f.hhea.parse=function(b,a,c){c=f._bin;var d={};return c.readFixed(b,a),a+=4,d.ascender=c.readShort(b,a),a+=2,d.descender=c.readShort(b,a),a+=2,d.lineGap=c.readShort(b,a),a+=2,d.advanceWidthMax=c.readUshort(b,a),a+=2,d.minLeftSideBearing=c.readShort(b,a),a+=2,d.minRightSideBearing=c.readShort(b,a),a+=2,d.xMaxExtent=c.readShort(b,a),a+=2,d.caretSlopeRise=c.readShort(b,
a),a+=2,d.caretSlopeRun=c.readShort(b,a),a+=2,d.caretOffset=c.readShort(b,a),a+=2,a+=8,d.metricDataFormat=c.readShort(b,a),a+=2,d.numberOfHMetrics=c.readUshort(b,a),d};f.hmtx={};f.hmtx.parse=function(b,a,c,d){c=f._bin;for(var g={aWidth:[],lsBearing:[]},h=0,e=0,k=0;k<d.maxp.numGlyphs;k++)k<d.hhea.numberOfHMetrics&&(h=c.readUshort(b,a),a+=2,e=c.readShort(b,a),a+=2),g.aWidth.push(h),g.lsBearing.push(e);return g};f.kern={};f.kern.parse=function(b,a,c,d){var g=f._bin,h=g.readUshort(b,a);if(a+=2,1==h)return f.kern.parseV1(b,
a-2,c,d);c=g.readUshort(b,a);a+=2;d={glyph1:[],rval:[]};for(h=0;h<c;h++){a+=2;g.readUshort(b,a);a+=2;var e=g.readUshort(b,a);a+=2;e>>>=8;if(0!=(e&=15))throw"unknown kern table format: "+e;a=f.kern.readFormat0(b,a,d)}return d};f.kern.parseV1=function(b,a,c,d){c=f._bin;c.readFixed(b,a);a+=4;d=c.readUint(b,a);a+=4;for(var g={glyph1:[],rval:[]},h=0;h<d;h++){c.readUint(b,a);a+=4;var e=c.readUshort(b,a);a+=2;c.readUshort(b,a);a+=2;e>>>=8;if(0!=(e&=15))throw"unknown kern table format: "+e;a=f.kern.readFormat0(b,
a,g)}return g};f.kern.readFormat0=function(b,a,c){var d=f._bin,g=-1,h=d.readUshort(b,a);a+=2;d.readUshort(b,a);a+=2;d.readUshort(b,a);a+=2;d.readUshort(b,a);a+=2;for(var e=0;e<h;e++){var k=d.readUshort(b,a);a+=2;var p=d.readUshort(b,a);a+=2;var l=d.readShort(b,a);a+=2;k!=g&&(c.glyph1.push(k),c.rval.push({glyph2:[],vals:[]}));g=c.rval[c.rval.length-1];g.glyph2.push(p);g.vals.push(l);g=k}return a};f.loca={};f.loca.parse=function(b,a,c,d){c=f._bin;var g=[],h=d.head.indexToLocFormat;d=d.maxp.numGlyphs+
1;if(0==h)for(var e=0;e<d;e++)g.push(c.readUshort(b,a+(e<<1))<<1);if(1==h)for(e=0;e<d;e++)g.push(c.readUint(b,a+(e<<2)));return g};f.maxp={};f.maxp.parse=function(b,a,c){c=f._bin;var d={},g=c.readUint(b,a);return a+=4,d.numGlyphs=c.readUshort(b,a),a+=2,65536==g&&(d.maxPoints=c.readUshort(b,a),a+=2,d.maxContours=c.readUshort(b,a),a+=2,d.maxCompositePoints=c.readUshort(b,a),a+=2,d.maxCompositeContours=c.readUshort(b,a),a+=2,d.maxZones=c.readUshort(b,a),a+=2,d.maxTwilightPoints=c.readUshort(b,a),a+=
2,d.maxStorage=c.readUshort(b,a),a+=2,d.maxFunctionDefs=c.readUshort(b,a),a+=2,d.maxInstructionDefs=c.readUshort(b,a),a+=2,d.maxStackElements=c.readUshort(b,a),a+=2,d.maxSizeOfInstructions=c.readUshort(b,a),a+=2,d.maxComponentElements=c.readUshort(b,a),a+=2,d.maxComponentDepth=c.readUshort(b,a)),d};f.name={};f.name.parse=function(b,a,c){c=f._bin;var d={};c.readUshort(b,a);a+=2;var g=c.readUshort(b,a);a+=2;c.readUshort(b,a);for(var e,k="copyright fontFamily fontSubfamily ID fullName version postScriptName trademark manufacturer designer description urlVendor urlDesigner licence licenceURL --- typoFamilyName typoSubfamilyName compatibleFull sampleText postScriptCID wwsFamilyName wwsSubfamilyName lightPalette darkPalette".split(" "),
l=a+=2,p=0;p<g;p++){var y=c.readUshort(b,a);a+=2;var w=c.readUshort(b,a);a+=2;var n=c.readUshort(b,a);a+=2;var r=c.readUshort(b,a);a+=2;var q=c.readUshort(b,a);a+=2;var x=c.readUshort(b,a);a+=2;var t=k[r];x=l+12*g+x;if(0==y)q=c.readUnicode(b,x,q/2);else if(3==y&&0==w)q=c.readUnicode(b,x,q/2);else if(0==w)q=c.readASCII(b,x,q);else if(1==w)q=c.readUnicode(b,x,q/2);else if(3==w)q=c.readUnicode(b,x,q/2);else{if(1!=y)throw"unknown encoding "+w+", platformID: "+y;q=c.readASCII(b,x,q);console.debug("reading unknown MAC encoding "+
w+" as ASCII")}y="p"+y+","+n.toString(16);null==d[y]&&(d[y]={});d[y][void 0!==t?t:r]=q;d[y]._lang=n}for(var u in d)if(null!=d[u].postScriptName&&1033==d[u]._lang)return d[u];for(u in d)if(null!=d[u].postScriptName&&0==d[u]._lang)return d[u];for(u in d)if(null!=d[u].postScriptName&&3084==d[u]._lang)return d[u];for(u in d)if(null!=d[u].postScriptName)return d[u];for(u in d){e=u;break}return console.debug("returning name table with languageID "+d[e]._lang),d[e]};f["OS/2"]={};f["OS/2"].parse=function(b,
a,c){c=f._bin.readUshort(b,a);a+=2;var d={};if(0==c)f["OS/2"].version0(b,a,d);else if(1==c)f["OS/2"].version1(b,a,d);else if(2==c||3==c||4==c)f["OS/2"].version2(b,a,d);else{if(5!=c)throw"unknown OS/2 table version: "+c;f["OS/2"].version5(b,a,d)}return d};f["OS/2"].version0=function(b,a,c){var d=f._bin;return c.xAvgCharWidth=d.readShort(b,a),a+=2,c.usWeightClass=d.readUshort(b,a),a+=2,c.usWidthClass=d.readUshort(b,a),a+=2,c.fsType=d.readUshort(b,a),a+=2,c.ySubscriptXSize=d.readShort(b,a),a+=2,c.ySubscriptYSize=
d.readShort(b,a),a+=2,c.ySubscriptXOffset=d.readShort(b,a),a+=2,c.ySubscriptYOffset=d.readShort(b,a),a+=2,c.ySuperscriptXSize=d.readShort(b,a),a+=2,c.ySuperscriptYSize=d.readShort(b,a),a+=2,c.ySuperscriptXOffset=d.readShort(b,a),a+=2,c.ySuperscriptYOffset=d.readShort(b,a),a+=2,c.yStrikeoutSize=d.readShort(b,a),a+=2,c.yStrikeoutPosition=d.readShort(b,a),a+=2,c.sFamilyClass=d.readShort(b,a),a+=2,c.panose=d.readBytes(b,a,10),a+=10,c.ulUnicodeRange1=d.readUint(b,a),a+=4,c.ulUnicodeRange2=d.readUint(b,
a),a+=4,c.ulUnicodeRange3=d.readUint(b,a),a+=4,c.ulUnicodeRange4=d.readUint(b,a),a+=4,c.achVendID=[d.readInt8(b,a),d.readInt8(b,a+1),d.readInt8(b,a+2),d.readInt8(b,a+3)],a+=4,c.fsSelection=d.readUshort(b,a),a+=2,c.usFirstCharIndex=d.readUshort(b,a),a+=2,c.usLastCharIndex=d.readUshort(b,a),a+=2,c.sTypoAscender=d.readShort(b,a),a+=2,c.sTypoDescender=d.readShort(b,a),a+=2,c.sTypoLineGap=d.readShort(b,a),a+=2,c.usWinAscent=d.readUshort(b,a),a+=2,c.usWinDescent=d.readUshort(b,a),a+2};f["OS/2"].version1=
function(b,a,c){var d=f._bin;return a=f["OS/2"].version0(b,a,c),c.ulCodePageRange1=d.readUint(b,a),a+=4,c.ulCodePageRange2=d.readUint(b,a),a+4};f["OS/2"].version2=function(b,a,c){var d=f._bin;return a=f["OS/2"].version1(b,a,c),c.sxHeight=d.readShort(b,a),a+=2,c.sCapHeight=d.readShort(b,a),a+=2,c.usDefault=d.readUshort(b,a),a+=2,c.usBreak=d.readUshort(b,a),a+=2,c.usMaxContext=d.readUshort(b,a),a+2};f["OS/2"].version5=function(b,a,c){var d=f._bin;return a=f["OS/2"].version2(b,a,c),c.usLowerOpticalPointSize=
d.readUshort(b,a),a+=2,c.usUpperOpticalPointSize=d.readUshort(b,a),a+2};f.post={};f.post.parse=function(b,a,c){c=f._bin;var d={};return d.version=c.readFixed(b,a),a+=4,d.italicAngle=c.readFixed(b,a),a+=4,d.underlinePosition=c.readShort(b,a),a+=2,d.underlineThickness=c.readShort(b,a),d};null==f&&(f={});null==f.U&&(f.U={});f.U.codeToGlyph=function(b,a){b=b.cmap;var c=-1;if(null!=b.p0e4?c=b.p0e4:null!=b.p3e1?c=b.p3e1:null!=b.p1e0?c=b.p1e0:null!=b.p0e3&&(c=b.p0e3),-1==c)throw"no familiar platform and encoding!";
b=b.tables[c];if(0==b.format)return a>=b.map.length?0:b.map[a];if(4==b.format){var d=-1;for(c=0;c<b.endCount.length;c++)if(a<=b.endCount[c]){d=c;break}return-1==d||b.startCount[d]>a?0:65535&(0!=b.idRangeOffset[d]?b.glyphIdArray[a-b.startCount[d]+(b.idRangeOffset[d]>>1)-(b.idRangeOffset.length-d)]:a+b.idDelta[d])}if(12==b.format){if(a>b.groups[b.groups.length-1][1])return 0;for(c=0;c<b.groups.length;c++)if(d=b.groups[c],d[0]<=a&&a<=d[1])return d[2]+(a-d[0]);return 0}throw"unknown cmap table format "+
b.format;};f.U.glyphToPath=function(b,a){var c={cmds:[],crds:[]};if(b.SVG&&b.SVG.entries[a]){var d=b.SVG.entries[a];return null==d?c:("string"==typeof d&&(d=f.SVG.toPath(d),b.SVG.entries[a]=d),d)}if(b.CFF){d={x:0,y:0,stack:[],nStems:0,haveWidth:!1,width:b.CFF.Private?b.CFF.Private.defaultWidthX:0,open:!1};var g=b.CFF,e=b.CFF.Private;if(g.ROS){for(e=0;g.FDSelect[e+2]<=a;)e+=2;e=g.FDArray[g.FDSelect[e+1]].Private}f.U._drawCFF(b.CFF.CharStrings[a],d,g,e,c)}else b.glyf&&f.U._drawGlyf(a,b,c);return c};
f.U._drawGlyf=function(b,a,c){var d=a.glyf[b];null==d&&(d=a.glyf[b]=f.glyf._parseGlyf(a,b));null!=d&&(-1<d.noc?f.U._simpleGlyph(d,c):f.U._compoGlyph(d,a,c))};f.U._simpleGlyph=function(b,a){for(var c=0;c<b.noc;c++){for(var d=0==c?0:b.endPts[c-1]+1,g=b.endPts[c],e=d;e<=g;e++){var k=e==d?g:e-1,l=e==g?d:e+1,p=1&b.flags[e],y=1&b.flags[k],w=1&b.flags[l],n=b.xs[e],r=b.ys[e];if(e==d)if(p){if(!y){f.U.P.moveTo(a,n,r);continue}f.U.P.moveTo(a,b.xs[k],b.ys[k])}else y?f.U.P.moveTo(a,b.xs[k],b.ys[k]):f.U.P.moveTo(a,
(b.xs[k]+n)/2,(b.ys[k]+r)/2);p?y&&f.U.P.lineTo(a,n,r):w?f.U.P.qcurveTo(a,n,r,b.xs[l],b.ys[l]):f.U.P.qcurveTo(a,n,r,(n+b.xs[l])/2,(r+b.ys[l])/2)}f.U.P.closePath(a)}};f.U._compoGlyph=function(b,a,c){for(var d=0;d<b.parts.length;d++){var e={cmds:[],crds:[]},h=b.parts[d];f.U._drawGlyf(h.glyphIndex,a,e);h=h.m;for(var k=0;k<e.crds.length;k+=2){var l=e.crds[k],p=e.crds[k+1];c.crds.push(l*h.a+p*h.b+h.tx);c.crds.push(l*h.c+p*h.d+h.ty)}for(k=0;k<e.cmds.length;k++)c.cmds.push(e.cmds[k])}};f.U._getGlyphClass=
function(b,a){b=f._lctf.getInterval(a,b);return-1==b?0:a[b+2]};f.U.getPairAdjustment=function(b,a,c){var d=!1;if(b.GPOS){var e=b.GPOS,h=e.lookupList;e=e.featureList;for(var k=[],l=0;l<e.length;l++){var p=e[l];if("kern"==p.tag){d=!0;for(var y=0;y<p.tab.length;y++)if(!k[p.tab[y]]){k[p.tab[y]]=!0;for(var w=h[p.tab[y]],n=0;n<w.tabs.length;n++)if(null!=w.tabs[n]){var r,q=w.tabs[n];if((!q.coverage||-1!=(r=f._lctf.coverageIndex(q.coverage,a)))&&1!=w.ltype&&2==w.ltype){var x=null;if(1==q.fmt)for(q=q.pairsets[r],
l=0;l<q.length;l++)q[l].gid2==c&&(x=q[l]);else if(2==q.fmt){x=f.U._getGlyphClass(a,q.classDef1);var t=f.U._getGlyphClass(c,q.classDef2);x=q.matrix[x][t]}if(x)return b=0,x.val1&&x.val1[2]&&(b+=x.val1[2]),x.val2&&x.val2[0]&&(b+=x.val2[0]),b}}}}}}return b.kern&&!d&&(a=b.kern.glyph1.indexOf(a),-1!=a&&(c=b.kern.rval[a].glyph2.indexOf(c),-1!=c))?b.kern.rval[a].vals[c]:0};f.U._applySubs=function(b,a,c,d){for(var e=b.length-a-1,h=0;h<c.tabs.length;h++)if(null!=c.tabs[h]){var k,l=c.tabs[h];if(!l.coverage||
-1!=(k=f._lctf.coverageIndex(l.coverage,b[a])))if(1==c.ltype)b[a],1==l.fmt?b[a]+=l.delta:b[a]=l.newg[k];else if(4==c.ltype)for(var p=l.vals[k],y=0;y<p.length;y++){var w=p[y];l=w.chain.length;if(!(l>e)){for(var n=!0,r=0,q=0;q<l;q++){for(;-1==b[a+r+(1+q)];)r++;w.chain[q]!=b[a+r+(1+q)]&&(n=!1)}if(n){b[a]=w.nglyph;for(q=0;q<l+r;q++)b[a+q+1]=-1;break}}}else if(5==c.ltype&&2==l.fmt)for(p=f._lctf.getInterval(l.cDef,b[a]),r=l.scset[l.cDef[p+2]],w=0;w<r.length;w++){y=r[w];var x=y.input;if(!(x.length>e)){n=
!0;for(q=0;q<x.length;q++){var t=f._lctf.getInterval(l.cDef,b[a+1+q]);if(-1==p&&l.cDef[t+2]!=x[q]){n=!1;break}}if(n)for(n=y.substLookupRecords,y=0;y<n.length;y+=2)n[y],n[y+1]}}else if(6==c.ltype&&3==l.fmt&&f.U._glsCovered(b,l.backCvg,a-l.backCvg.length)&&f.U._glsCovered(b,l.inptCvg,a)&&f.U._glsCovered(b,l.ahedCvg,a+l.inptCvg.length))for(n=l.lookupRec,w=0;w<n.length;w+=2)p=n[w],f.U._applySubs(b,a+p,d[n[w+1]],d)}};f.U._glsCovered=function(b,a,c){for(var d=0;d<a.length;d++)if(-1==f._lctf.coverageIndex(a[d],
b[c+d]))return!1;return!0};f.U.glyphsToPath=function(b,a,c){for(var d={cmds:[],crds:[]},e=0,h=0;h<a.length;h++){var k=a[h];if(-1!=k){for(var l=h<a.length-1&&-1!=a[h+1]?a[h+1]:0,p=f.U.glyphToPath(b,k),y=0;y<p.crds.length;y+=2)d.crds.push(p.crds[y]+e),d.crds.push(p.crds[y+1]);c&&d.cmds.push(c);for(y=0;y<p.cmds.length;y++)d.cmds.push(p.cmds[y]);c&&d.cmds.push("X");e+=b.hmtx.aWidth[k];h<a.length-1&&(e+=f.U.getPairAdjustment(b,k,l))}}return d};f.U.P={};f.U.P.moveTo=function(b,a,c){b.cmds.push("M");b.crds.push(a,
c)};f.U.P.lineTo=function(b,a,c){b.cmds.push("L");b.crds.push(a,c)};f.U.P.curveTo=function(b,a,c,d,f,e,k){b.cmds.push("C");b.crds.push(a,c,d,f,e,k)};f.U.P.qcurveTo=function(b,a,c,d,f){b.cmds.push("Q");b.crds.push(a,c,d,f)};f.U.P.closePath=function(b){b.cmds.push("Z")};f.U._drawCFF=function(b,a,c,d,e){for(var h=a.stack,g=a.nStems,k=a.haveWidth,l=a.width,y=a.open,w=0,n=a.x,r=a.y,q=0,x=0,t=0,u=0,H=0,I=0,G=0,B=0,L=0,F=0,N={val:0,size:0};w<b.length;){f.CFF.getCharString(b,w,N);var C=N.val;if(w+=N.size,
"o1"==C||"o18"==C)0!=h.length%2&&!k&&(l=h.shift()+d.nominalWidthX),g+=h.length>>1,h.length=0,k=!0;else if("o3"==C||"o23"==C)0!=h.length%2&&!k&&(l=h.shift()+d.nominalWidthX),g+=h.length>>1,h.length=0,k=!0;else if("o4"==C)1<h.length&&!k&&(l=h.shift()+d.nominalWidthX,k=!0),y&&f.U.P.closePath(e),r+=h.pop(),f.U.P.moveTo(e,n,r),y=!0;else if("o5"==C)for(;0<h.length;)n+=h.shift(),r+=h.shift(),f.U.P.lineTo(e,n,r);else if("o6"==C||"o7"==C){var K=h.length,J="o6"==C;for(C=0;C<K;C++){var z=h.shift();J?n+=z:r+=
z;J=!J;f.U.P.lineTo(e,n,r)}}else if("o8"==C||"o24"==C){K=h.length;for(J=0;J+6<=K;)q=n+h.shift(),x=r+h.shift(),t=q+h.shift(),u=x+h.shift(),n=t+h.shift(),r=u+h.shift(),f.U.P.curveTo(e,q,x,t,u,n,r),J+=6;"o24"==C&&(n+=h.shift(),r+=h.shift(),f.U.P.lineTo(e,n,r))}else{if("o11"==C)break;if("o1234"==C||"o1235"==C||"o1236"==C||"o1237"==C)"o1234"==C&&(x=r,t=(q=n+h.shift())+h.shift(),F=u=x+h.shift(),I=u,B=r,n=(G=(H=(L=t+h.shift())+h.shift())+h.shift())+h.shift(),f.U.P.curveTo(e,q,x,t,u,L,F),f.U.P.curveTo(e,
H,I,G,B,n,r)),"o1235"==C&&(q=n+h.shift(),x=r+h.shift(),t=q+h.shift(),u=x+h.shift(),L=t+h.shift(),F=u+h.shift(),H=L+h.shift(),I=F+h.shift(),G=H+h.shift(),B=I+h.shift(),n=G+h.shift(),r=B+h.shift(),h.shift(),f.U.P.curveTo(e,q,x,t,u,L,F),f.U.P.curveTo(e,H,I,G,B,n,r)),"o1236"==C&&(q=n+h.shift(),x=r+h.shift(),t=q+h.shift(),F=u=x+h.shift(),I=u,G=(H=(L=t+h.shift())+h.shift())+h.shift(),B=I+h.shift(),n=G+h.shift(),f.U.P.curveTo(e,q,x,t,u,L,F),f.U.P.curveTo(e,H,I,G,B,n,r)),"o1237"==C&&(q=n+h.shift(),x=r+h.shift(),
t=q+h.shift(),u=x+h.shift(),L=t+h.shift(),F=u+h.shift(),H=L+h.shift(),I=F+h.shift(),G=H+h.shift(),B=I+h.shift(),Math.abs(G-n)>Math.abs(B-r)?n=G+h.shift():r=B+h.shift(),f.U.P.curveTo(e,q,x,t,u,L,F),f.U.P.curveTo(e,H,I,G,B,n,r));else if("o14"==C){if(0<h.length&&!k&&(l=h.shift()+c.nominalWidthX,k=!0),4==h.length)K=h.shift(),J=h.shift(),z=h.shift(),C=h.shift(),z=f.CFF.glyphBySE(c,z),C=f.CFF.glyphBySE(c,C),f.U._drawCFF(c.CharStrings[z],a,c,d,e),a.x=K,a.y=J,f.U._drawCFF(c.CharStrings[C],a,c,d,e);y&&(f.U.P.closePath(e),
y=!1)}else if("o19"==C||"o20"==C)0!=h.length%2&&!k&&(l=h.shift()+d.nominalWidthX),g+=h.length>>1,h.length=0,k=!0,w+=g+7>>3;else if("o21"==C)2<h.length&&!k&&(l=h.shift()+d.nominalWidthX,k=!0),r+=h.pop(),n+=h.pop(),y&&f.U.P.closePath(e),f.U.P.moveTo(e,n,r),y=!0;else if("o22"==C)1<h.length&&!k&&(l=h.shift()+d.nominalWidthX,k=!0),n+=h.pop(),y&&f.U.P.closePath(e),f.U.P.moveTo(e,n,r),y=!0;else if("o25"==C){for(;6<h.length;)n+=h.shift(),r+=h.shift(),f.U.P.lineTo(e,n,r);q=n+h.shift();x=r+h.shift();t=q+h.shift();
u=x+h.shift();n=t+h.shift();r=u+h.shift();f.U.P.curveTo(e,q,x,t,u,n,r)}else if("o26"==C)for(h.length%2&&(n+=h.shift());0<h.length;)q=n,x=r+h.shift(),n=t=q+h.shift(),r=(u=x+h.shift())+h.shift(),f.U.P.curveTo(e,q,x,t,u,n,r);else if("o27"==C)for(h.length%2&&(r+=h.shift());0<h.length;)x=r,t=(q=n+h.shift())+h.shift(),u=x+h.shift(),n=t+h.shift(),r=u,f.U.P.curveTo(e,q,x,t,u,n,r);else if("o10"==C||"o29"==C)K="o10"==C?d:c,0==h.length?console.debug("error: empty stack"):(J=h.pop(),K=K.Subrs[J+K.Bias],a.x=n,
a.y=r,a.nStems=g,a.haveWidth=k,a.width=l,a.open=y,f.U._drawCFF(K,a,c,d,e),n=a.x,r=a.y,g=a.nStems,k=a.haveWidth,l=a.width,y=a.open);else if("o30"==C||"o31"==C)for(K=h.length,C=(J=0,"o31"==C),J+=K-(K&=-3);J<K;)C?(x=r,t=(q=n+h.shift())+h.shift(),r=(u=x+h.shift())+h.shift(),5==K-J?(n=t+h.shift(),J++):n=t,C=!1):(q=n,x=r+h.shift(),t=q+h.shift(),u=x+h.shift(),n=t+h.shift(),5==K-J?(r=u+h.shift(),J++):r=u,C=!0),f.U.P.curveTo(e,q,x,t,u,n,r),J+=4;else{if("o"==(C+"").charAt(0))throw console.debug("Unknown operation: "+
C,b),C;h.push(C)}}}a.x=n;a.y=r;a.nStems=g;a.haveWidth=k;a.width=l;a.open=y};var k=f,l={Typr:k};return e.Typr=k,e.default=l,Object.defineProperty(e,"__esModule",{value:!0}),e}({}).Typr},function(){return function(e){var f=Uint8Array,k=Uint16Array,l=Uint32Array,b=new f([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),a=new f([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),c=new f([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),d=function(a,b){for(var c=
new k(31),d=0;31>d;++d)c[d]=b+=1<<a[d-1];a=new l(c[30]);for(d=1;30>d;++d)for(b=c[d];b<c[d+1];++b)a[b]=b-c[d]<<5|d;return[c,a]},g=d(b,2),h=g[0];g=g[1];h[28]=258;g[258]=28;var m=d(a,0)[0],D=new k(32768);for(d=0;32768>d;++d)g=(43690&d)>>>1|(21845&d)<<1,g=(61680&(g=(52428&g)>>>2|(13107&g)<<2))>>>4|(3855&g)<<4,D[d]=((65280&g)>>>8|(255&g)<<8)>>>1;var p=function(a,b,c){for(var d=a.length,e=0,f=new k(b);e<d;++e)++f[a[e]-1];var g=new k(b);for(e=0;e<b;++e)g[e]=g[e-1]+f[e-1]<<1;if(c)for(c=new k(1<<b),f=15-b,
e=0;e<d;++e){if(a[e]){var h=e<<4|a[e],l=b-a[e],m=g[a[e]-1]++<<l;for(l=m|(1<<l)-1;m<=l;++m)c[D[m]>>>f]=h}}else for(c=new k(d),e=0;e<d;++e)a[e]&&(c[e]=D[g[a[e]-1]++]>>>15-a[e]);return c};g=new f(288);for(d=0;144>d;++d)g[d]=8;for(d=144;256>d;++d)g[d]=9;for(d=256;280>d;++d)g[d]=7;for(d=280;288>d;++d)g[d]=8;var y=new f(32);for(d=0;32>d;++d)y[d]=5;var w=p(g,9,1),n=p(y,5,1),r=function(a){for(var b=a[0],c=1;c<a.length;++c)a[c]>b&&(b=a[c]);return b},q=function(a,b,c){var d=b/8|0;return(a[d]|a[d+1]<<8)>>(7&
b)&c},x=function(a,b){var c=b/8|0;return(a[c]|a[c+1]<<8|a[c+2]<<16)>>(7&b)},t=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],u=function(a,b,c){b=Error(b||t[a]);if(b.code=a,Error.captureStackTrace&&Error.captureStackTrace(b,u),!c)throw b;return b},H=function(d,e,g){var t=d.length;
if(!t||g&&!g.l&&5>t)return e||new f(0);var y=!e||g,D=!g||g.i;g||(g={});e||(e=new f(3*t));var C,H=function(a){var b=e.length;a>b&&(a=new f(Math.max(2*b,a)),a.set(e),e=a)},J=g.f||0,z=g.p||0,v=g.b||0,B=g.l,G=g.d,I=g.m,U=g.n,E=8*t;do{if(!B){g.f=J=q(d,z,1);var A=q(d,z+1,3);if(z+=3,!A){z=d[(V=((C=z)/8|0)+(7&C&&1)+4)-4]|d[V-3]<<8;A=V+z;if(A>t){D&&u(0);break}y&&H(v+z);e.set(d.subarray(V,A),v);g.b=v+=z;g.p=z=8*A;continue}if(1==A)B=w,G=n,I=9,U=5;else if(2==A){I=q(d,z,31)+257;G=q(d,z+10,15)+4;B=I+q(d,z+5,31)+
1;z+=14;U=new f(B);var P=new f(19);for(A=0;A<G;++A)P[c[A]]=q(d,z+3*A,7);z+=3*G;A=r(P);G=(1<<A)-1;var pa=p(P,A,1);for(A=0;A<B;){var V;V=pa[q(d,z,G)];if(z+=15&V,16>(V>>>=4))U[A++]=V;else{var fa=P=0;for(16==V?(fa=3+q(d,z,3),z+=2,P=U[A-1]):17==V?(fa=3+q(d,z,7),z+=3):18==V&&(fa=11+q(d,z,127),z+=7);fa--;)U[A++]=P}}B=U.subarray(0,I);A=U.subarray(I);I=r(B);U=r(A);B=p(B,I,1);G=p(A,U,1)}else u(1);if(z>E){D&&u(0);break}}y&&H(v+131072);pa=(1<<I)-1;fa=(1<<U)-1;for(var qa=z;;qa=z){A=(P=B[x(d,z)&pa])>>>4;if((z+=
15&P)>E){D&&u(0);break}if(P||u(2),256>A)e[v++]=A;else{if(256==A){qa=z;B=null;break}P=A-254;if(264<A){var M=b[A-=257];P=q(d,z,(1<<M)-1)+h[A];z+=M}A=G[x(d,z)&fa];M=A>>>4;A||u(3);z+=15&A;A=m[M];3<M&&(M=a[M],A+=x(d,z)&(1<<M)-1,z+=M);if(z>E){D&&u(0);break}y&&H(v+131072);for(P=v+P;v<P;v+=4)e[v]=e[v-A],e[v+1]=e[v+1-A],e[v+2]=e[v+2-A],e[v+3]=e[v+3-A];v=P}}g.l=B;g.p=qa;g.b=v;B&&(J=1,g.m=I,g.d=G,g.n=U)}while(!J);return v==e.length?e:function(a,b,c){(null==b||0>b)&&(b=0);(null==c||c>a.length)&&(c=a.length);
var d=new (a instanceof k?k:a instanceof l?l:f)(c-b);return d.set(a.subarray(b,c)),d}(e,0,v)};d=new f(0);g="undefined"!=typeof TextDecoder&&new TextDecoder;try{g.decode(d,{stream:!0}),1}catch(I){}return e.convert_streams=function(a){function b(){var a=f.getUint16(g);return g+=2,a}function c(){var a=f.getUint32(g);return g+=4,a}function d(a){u.setUint16(y,a);y+=2}function e(a){u.setUint32(y,a);y+=4}var f=new DataView(a),g=0;c();var h=c();c();var k=b();b();c();b();b();c();c();c();c();c();for(var l=
0;Math.pow(2,l)<=k;)l++;l--;for(var m=16*Math.pow(2,l),n=16*k-m,p=12,q=[],r=0;r<k;r++)q.push({tag:c(),offset:c(),compLength:c(),origLength:c(),origChecksum:c()}),p+=16;var x,t=new Uint8Array(12+16*q.length+q.reduce(function(a,b){return a+b.origLength+4},0));r=t.buffer;var u=new DataView(r),y=0;return e(h),d(k),d(m),d(l),d(n),q.forEach(function(a){e(a.tag);e(a.origChecksum);e(p);e(a.origLength);a.outOffset=p;0!=(p+=a.origLength)%4&&(p+=4-p%4)}),q.forEach(function(b){var c=a.slice(b.offset,b.offset+
b.compLength);if(b.compLength!=b.origLength){var d=new Uint8Array(b.origLength);c=new Uint8Array(c,2);H(c,d)}else d=new Uint8Array(c);t.set(d,b.outOffset);d=0;0!=(p=b.outOffset+b.origLength)%4&&(d=4-p%4);t.set((new Uint8Array(d)).buffer,b.outOffset+b.origLength);x=p+d}),r.slice(0,x)},Object.defineProperty(e,"__esModule",{value:!0}),e}({}).convert_streams},function(e,f){function k(a){if(!g){let a={R:2,L:1,D:4,C:16,U:32,T:8};g=new Map;for(let b in d){let c=0;d[b].split(",").forEach(d=>{let [e,f]=d.split("+");
e=parseInt(e,36);f=f?parseInt(f,36):0;g.set(c+=e,a[b]);for(d=f;d--;)g.set(++c,a[b])})}}return g.get(a)||32}function l(a,b){let c=[];for(let d=0;d<b.length;d++){let f=b.codePointAt(d);65535<f&&d++;c.push(e.U.codeToGlyph(a,f))}if(a=a.GSUB){let {lookupList:d,featureList:f}=a,g,l=/^(rlig|liga|mset|isol|init|fina|medi|half|pres|blws)$/,m=[];f.forEach(a=>{if(l.test(a.tag))for(let l=0;l<a.tab.length;l++){if(m[a.tab[l]])continue;m[a.tab[l]]=!0;let n=d[a.tab[l]],p=/^(isol|init|fina|medi)$/.test(a.tag);if(p&&
!g){{var f=b;let a=new Uint8Array(f.length),c=32,d=1,e=-1;for(let b=0;b<f.length;b++){let g=f.codePointAt(b),h=k(g)|0,l=1;h&8||(c&21?h&22?(l=3,1!==d&&3!==d||a[e]++):h&33&&(2===d||4===d)&&a[e]--:c&34&&(2===d||4===d)&&a[e]--,d=a[b]=l,c=h,e=b,65535<g&&b++)}g=a}}for(f=0;f<c.length;f++)g&&p&&h[g[f]]!==a.tag||e.U._applySubs(c,f,n,d)}})}return c}function b(...a){for(let b=0;b<a.length;b++)if("number"===typeof a[b])return a[b]}function a(a){let d=Object.create(null),f=a["OS/2"],g=a.hhea,h=a.head.unitsPerEm,
k=b(f&&f.sTypoAscender,g&&g.ascender,h),m={unitsPerEm:h,ascender:k,descender:b(f&&f.sTypoDescender,g&&g.descender,0),capHeight:b(f&&f.sCapHeight,k),xHeight:b(f&&f.sxHeight,k),lineGap:b(f&&f.sTypoLineGap,g&&g.lineGap),forEachGlyph(b,f,g,h){let k=0;const n=1/m.unitsPerEm*f;let p=0,q=-1;l(a,b).forEach((l,m)=>{if(-1!==l){m=d[l];if(!m){const {cmds:b,crds:f}=e.U.glyphToPath(a,l);m="";var r=0;for(let a=0,d=b.length;a<d;a++){var x=c[b[a]];m+=b[a];for(var t=1;t<=x;t++)m+=(1<t?",":"")+f[r++]}let g;if(f.length){r=
x=Infinity;t=g=-Infinity;for(let a=0,b=f.length;a<b;a+=2){let b=f[a],c=f[a+1];b<r&&(r=b);c<x&&(x=c);b>t&&(t=b);c>g&&(g=c)}}else r=t=x=g=0;m=d[l]={index:l,advanceWidth:a.hmtx.aWidth[l],xMin:r,yMin:x,xMax:t,yMax:g,path:m,pathCommandCount:b.length}}-1!==q&&(k+=e.U.getPairAdjustment(a,q,l)*n);h.call(null,m,k,p);m.advanceWidth&&(k+=m.advanceWidth*n);g&&(k+=g*f);q=l}p+=65535<b.codePointAt(p)?2:1});return k}};return m}let c={M:2,L:2,Q:4,C:6,Z:0},d={C:"18g,ca,368,1kz",D:"17k,6,2,2+4,5+c,2+6,2+1,10+1,9+f,j+11,2+1,a,2,2+1,15+2,3,j+2,6+3,2+8,2,2,2+1,w+a,4+e,3+3,2,3+2,3+5,23+w,2f+4,3,2+9,2,b,2+3,3,1k+9,6+1,3+1,2+2,2+d,30g,p+y,1,1+1g,f+x,2,sd2+1d,jf3+4,f+3,2+4,2+2,b+3,42,2,4+2,2+1,2,3,t+1,9f+w,2,el+2,2+g,d+2,2l,2+1,5,3+1,2+1,2,3,6,16wm+1v",
R:"17m+3,2,2,6+3,m,15+2,2+2,h+h,13,3+8,2,2,3+1,2,p+1,x,5+4,5,a,2,2,3,u,c+2,g+1,5,2+1,4+1,5j,6+1,2,b,2+2,f,2+1,1s+2,2,3+1,7,1ez0,2,2+1,4+4,b,4,3,b,42,2+2,4,3,2+1,2,o+3,ae,ep,x,2o+2,3+1,3,5+1,6",L:"x9u,jff,a,fd,jv",T:"4t,gj+33,7o+4,1+1,7c+18,2,2+1,2+1,2,21+a,2,1b+k,h,2u+6,3+5,3+1,2+3,y,2,v+q,2k+a,1n+8,a,p+3,2+8,2+2,2+4,18+2,3c+e,2+v,1k,2,5+7,5,4+6,b+1,u,1n,5+3,9,l+1,r,3+1,1m,5+1,5+1,3+2,4,v+1,4,c+1,1m,5+4,2+1,5,l+1,n+5,2,1n,3,2+3,9,8+1,c+1,v,1q,d,1f,4,1m+2,6+2,2+3,8+1,c+1,u,1n,3,7,6+1,l+1,t+1,1m+1,5+3,9,l+1,u,21,8+2,2,2j,3+6,d+7,2r,3+8,c+5,23+1,s,2,2,1k+d,2+4,2+1,6+a,2+z,a,2v+3,2+5,2+1,3+1,q+1,5+2,h+3,e,3+1,7,g,jk+2,qb+2,u+2,u+1,v+1,1t+1,2+6,9,3+a,a,1a+2,3c+1,z,3b+2,5+1,a,7+2,64+1,3,1n,2+6,2,2,3+7,7+9,3,1d+d,1,1+1,1s+3,1d,2+4,2,6,15+8,d+1,x+3,3+1,2+2,1l,2+1,4,2+2,1n+7,3+1,49+2,2+c,2+6,5,7,4+1,5j+1l,2+4,ek,3+1,r+4,1e+4,6+5,2p+c,1+3,1,1+2,1+b,2db+2,3y,2p+v,ff+3,30+1,n9x,1+2,2+9,x+1,29+1,7l,4,5,q+1,6,48+1,r+h,e,13+7,q+a,1b+2,1d,3+3,3+1,14,1w+5,3+1,3+1,d,9,1c,1g,2+2,3+1,6+1,2,17+1,9,6n,3,5,fn5,ki+f,h+f,5s,6y+2,ea,6b,46+4,1af+2,2+1,6+3,15+2,5,4m+1,fy+3,as+1,4a+a,4x,1j+e,1l+2,1e+3,3+1,1y+2,11+4,2+7,1r,d+1,1h+8,b+3,3,2o+2,3,2+1,7,4h,4+7,m+1,1m+1,4,12+6,4+4,5g+7,3+2,2,o,2d+5,2,5+1,2+1,6n+3,7+1,2+1,s+1,2e+7,3,2+1,2z,2,3+5,2,2u+2,3+3,2+4,78+8,2+1,75+1,2,5,41+3,3+1,5,x+9,15+5,3+3,9,a+5,3+2,1b+c,2+1,bb+6,2+5,2,2b+l,3+6,2+1,2+1,3f+5,4,2+1,2+6,2,21+1,4,2,9o+1,470+8,at4+4,1o+6,t5,1s+3,2a,f5l+1,2+3,43o+2,a+7,1+7,3+6,v+3,45+2,1j0+1i,5+1d,9,f,n+4,2+e,11t+6,2+g,3+6,2+1,2+4,7a+6,c6+3,15t+6,32+6,1,gzau,v+2n,3l+6n"},
g,h=[null,"isol","init","fina","medi"];return function(b){var c=new Uint8Array(b,0,4);c=e._bin.readASCII(c,0,4);if("wOFF"===c)b=f(b);else if("wOF2"===c)throw Error("woff2 fonts not supported");return a(e.parse(b)[0])}}],init(e,f,k){e=e();f=f();return k(e,f)}});let S={defaultFontURL:"https://fonts.gstatic.com/s/roboto/v18/KFOmCnqEu92Fr1Mu4mxM.woff",sdfGlyphSize:64,sdfMargin:.0625,sdfExponent:9,textureWidth:2048},Ta=new v.Color,Ba=!1,ea=Object.create(null),ba;Y=W.defineWorkerModule({name:"Typesetter",
dependencies:[S,Q,function(e,f,k){function l(a,b){function c(){let d=b=>{console.error(`Failure loading font ${a}${a===m?"":"; trying fallback"}`,b);a!==m&&(a=m,c())};try{let c=new XMLHttpRequest;c.open("get",a,!0);c.responseType="arraybuffer";c.onload=function(){if(400<=c.status)d(Error(c.statusText));else if(0<c.status)try{let a=e(c.response);b(a)}catch(u){d(u)}};c.onerror=d;c.send()}catch(t){d(t)}}c()}function b(a,b){a||(a=m);let c=v[a];c?c.pending?c.pending.push(b):b(c):(v[a]={pending:[b]},l(a,
b=>{let c=v[a].pending;v[a]=b;c.forEach(a=>a(b))}))}function a({text:a="",font:e=m,fontSize:k=1,letterSpacing:l=0,lineHeight:t="normal",maxWidth:u=Infinity,direction:w,textAlign:v="left",textIndent:D=0,whiteSpace:B="normal",overflowWrap:E="normal",anchorX:F=0,anchorY:N=0,includeCaretPositions:C=!1,chunkedBoundsSize:K=8192,colorRanges:J=null},z,X=!1){let m=g(),n={fontLoad:0,typesetting:0};-1<a.indexOf("\r")&&(console.info("Typesetter: got text with \\r chars; normalizing to \\n"),a=a.replace(/\r\n/g,
"\n").replace(/\r/g,"\n"));k=+k;l=+l;u=+u;t=t||"normal";D=+D;b(e,b=>{let e=isFinite(u),r=null,q=null,x=null,H=null,I=null,G=null,L=null,M=0,S=0,Y="nowrap"!==B,{ascender:Q,descender:R,unitsPerEm:W,lineGap:da,capHeight:Z,xHeight:aa}=b;n.fontLoad=g()-m;let ea=g(),O=k/W;"normal"===t&&(t=(Q-R+da)/W);t*=k;var la=(t-(Q-R)*O)/2;let ma=-(Q*O+la),ba=Math.min(t,(Q-R)*O),ka=(Q+R)/2*O-ba/2,ha=D,T=new h,ia=[T];b.forEachGlyph(a,k,l,(b,c,d)=>{let f=a.charAt(d),g=b.advanceWidth*O;var m=T.count;"isEmpty"in b||(b.isWhitespace=
!!f&&/[^\S\u00A0]/.test(f),b.canBreakAfter=!!f&&y.test(f),b.isEmpty=b.xMin===b.xMax||b.yMin===b.yMax||p.test(f));b.isWhitespace||b.isEmpty||S++;if(Y&&e&&!b.isWhitespace&&c+g+ha>u&&m){if(T.glyphAt(m-1).glyphObj.canBreakAfter){var n=new h;ha=-c}else for(;m--;)if(0===m&&"break-word"===E){n=new h;ha=-c;break}else if(T.glyphAt(m).glyphObj.canBreakAfter){n=T.splitAt(m+1);m=n.glyphAt(0).x;ha-=m;for(let a=n.count;a--;)n.glyphAt(a).x-=m;break}n&&(T.isSoftWrapped=!0,T=n,ia.push(T),M=u)}n=T.glyphAt(T.count);
n.glyphObj=b;n.x=c+ha;n.width=g;n.charIndex=d;"\n"===f&&(T=new h,ia.push(T),ha=-(c+g+l*k)+D)});ia.forEach(a=>{for(let b=a.count;b--;){let {glyphObj:c,x:d,width:e}=a.glyphAt(b);if(!c.isWhitespace){a.width=d+e;a.width>M&&(M=a.width);break}}});let ca=0,ja=0;F&&("number"===typeof F?ca=-F:"string"===typeof F&&(ca=-M*("left"===F?0:"center"===F?.5:"right"===F?1:c(F))));if(N)if("number"===typeof N)ja=-N;else if("string"===typeof N){let a=ia.length*t;ja="top"===N?0:"top-baseline"===N?-ma:"top-cap"===N?-ma-
Z*O:"top-ex"===N?-ma-aa*O:"middle"===N?a/2:"bottom"===N?a:"bottom-baseline"===N?a-la+R*O:c(N)*a}if(!X){let c=f.getEmbeddingLevels(a,w);r=new Uint16Array(S);q=new Float32Array(2*S);x={};G=[Infinity,Infinity,-Infinity,-Infinity];L=[];let e=ma;C&&(I=new Float32Array(3*a.length));J&&(H=new Uint8Array(3*S));let g=0,h=-1,k=-1,l,m;ia.forEach((n,p)=>{let {count:u,width:y}=n;if(0<u){p=0;for(var w=u;w--&&n.glyphAt(w).glyphObj.isWhitespace;)p++;var D=w=0;if("center"===v)w=(M-y)/2;else if("right"===v)w=M-y;else if("justify"===
v&&n.isSoftWrapped){D=0;for(var z=u-p;z--;)n.glyphAt(z).glyphObj.isWhitespace&&D++;D=(M-y)/D}if(D||w){z=0;for(var A=0;A<u;A++){var B=n.glyphAt(A),E=B.glyphObj;B.x+=w+z;0!==D&&E.isWhitespace&&A<u-p&&(z+=D,B.width+=D)}}w=f.getReorderSegments(a,c,n.glyphAt(0).charIndex,n.glyphAt(n.count-1).charIndex);for(D=0;D<w.length;D++){let [a,b]=w[D];z=Infinity;A=-Infinity;for(B=0;B<u;B++)if(n.glyphAt(B).charIndex>=a){for(E=B;B<u;B++){var F=n.glyphAt(B);if(F.charIndex>b)break;B<u-p&&(z=Math.min(z,F.x),A=Math.max(A,
F.x+F.width))}for(;E<B;E++)F=n.glyphAt(E),F.x=A-(F.x+F.width-z);break}}let t;p=a=>t=a;for(w=0;w<u;w++){z=n.glyphAt(w);t=z.glyphObj;D=t.index;(A=c.levels[z.charIndex]&1)&&(B=f.getMirroredCharacter(a[z.charIndex]))&&b.forEachGlyph(B,0,0,p);C&&({charIndex:B}=z,E=z.x+ca,F=z.x+z.width+ca,I[3*B]=A?F:E,I[3*B+1]=A?E:F,I[3*B+2]=e+ka+ja,A=B-h,1<A&&d(I,h,A),h=B);if(J)for({charIndex:A}=z;A>k;)k++,J.hasOwnProperty(k)&&(m=J[k]);if(!t.isWhitespace&&!t.isEmpty){A=g++;x[D]||(x[D]={path:t.path,pathBounds:[t.xMin,t.yMin,
t.xMax,t.yMax]});E=z.x+ca;F=e+ja;q[2*A]=E;q[2*A+1]=F;z=E+t.xMin*O;B=F+t.yMin*O;E+=t.xMax*O;F+=t.yMax*O;z<G[0]&&(G[0]=z);B<G[1]&&(G[1]=B);E>G[2]&&(G[2]=E);F>G[3]&&(G[3]=F);0===A%K&&(l={start:A,end:A,rect:[Infinity,Infinity,-Infinity,-Infinity]},L.push(l));l.end++;let a=l.rect;z<a[0]&&(a[0]=z);B<a[1]&&(a[1]=B);E>a[2]&&(a[2]=E);F>a[3]&&(a[3]=F);r[A]=D;J&&(D=3*A,H[D]=m>>16&255,H[D+1]=m>>8&255,H[D+2]=m&255)}}}e-=t});I&&(la=a.length-h,1<la&&d(I,h,la))}n.typesetting=g()-ea;z({glyphIds:r,glyphPositions:q,
glyphData:x,caretPositions:I,caretHeight:ba,glyphColors:H,chunkedBounds:L,fontSize:k,unitsPerEm:W,ascender:Q*O,descender:R*O,capHeight:Z*O,xHeight:aa*O,lineHeight:t,topBaseline:ma,blockBounds:[ca,ja-ia.length*t,ca+M,ja],visibleBounds:G,timings:n})})}function c(a){a=(a=a.match(/^([\d.]+)%$/))?parseFloat(a[1]):NaN;return isNaN(a)?0:a/100}function d(a,b,c){let d=a[3*b],e=a[3*b+2],f=(a[3*b+1]-d)/c;for(let g=0;g<c;g++){let c=3*(b+g);a[c]=d+f*g;a[c+1]=d+f*(g+1);a[c+2]=e}}function g(){return(self.performance||
Date).now()}function h(){this.data=[]}let {defaultFontURL:m}=k,v=Object.create(null),p=/[\u00AD\u034F\u061C\u115F-\u1160\u17B4-\u17B5\u180B-\u180E\u200B-\u200F\u202A-\u202E\u2060-\u206F\u3164\uFE00-\uFE0F\uFEFF\uFFA0\uFFF0-\uFFF8]/,y=/[^\S\u00A0]|[\-\u007C\u00AD\u2010\u2012-\u2014\u2027\u2056\u2E17\u2E40]/,w=["glyphObj","x","width","charIndex"];h.prototype={width:0,isSoftWrapped:!1,get count(){return Math.ceil(this.data.length/w.length)},glyphAt(a){let b=h.flyweight;b.data=this.data;b.index=a;return b},
splitAt(a){let b=new h;b.data=this.data.splice(a*w.length);return b}};h.flyweight=w.reduce((a,b,c,d)=>{Object.defineProperty(a,b,{get(){return this.data[this.index*w.length+c]},set(a){this.data[this.index*w.length+c]=a}});return a},{data:null,index:0});return{typeset:a,measure:function(b,c){a(b,a=>{let [b,d,e,f]=a.blockBounds;c({width:e-b,height:f-d})},{metricsOnly:!0})},loadFont:b}},Y["default"]],init(e,f,k,l){({defaultFontURL:e}=e);return k(f,l(),{defaultFontURL:e})}});let Va=W.defineWorkerModule({name:"Typesetter",
dependencies:[Y],init(e){return function(f){return new Promise(k=>{e.typeset(f,k)})}},getTransferables(e){const f=[e.glyphPositions.buffer,e.glyphIds.buffer];e.caretPositions&&f.push(e.caretPositions.buffer);e.glyphColors&&f.push(e.glyphColors.buffer);return f}}),Fa={};class Ja extends v.InstancedBufferGeometry{constructor(){super();this.detail=1;this.curveRadius=0;this.groups=[{start:0,count:Infinity,materialIndex:0},{start:0,count:Infinity,materialIndex:1}];this.boundingSphere=new v.Sphere;this.boundingBox=
new v.Box3}computeBoundingSphere(){}computeBoundingBox(){}setSide(e){let f=this.getIndex().count;this.setDrawRange(e===v.BackSide?f/2:0,e===v.DoubleSide?f:f/2)}set detail(e){if(e!==this._detail){this._detail=e;if("number"!==typeof e||1>e)e=1;let f=Xa(e);["position","normal","uv"].forEach(e=>{this.attributes[e]=f.attributes[e].clone()});this.setIndex(f.getIndex().clone())}}get detail(){return this._detail}set curveRadius(e){e!==this._curveRadius&&(this._curveRadius=e,this._updateBounds())}get curveRadius(){return this._curveRadius}updateGlyphs(e,
f,k,l,b){oa(this,"aTroikaGlyphBounds",e,4);oa(this,"aTroikaGlyphIndex",f,1);oa(this,"aTroikaGlyphColor",b,3);this._blockBounds=k;this._chunkedBounds=l;this.instanceCount=f.length;this._updateBounds()}_updateBounds(){let e=this._blockBounds;if(e){let {curveRadius:l,boundingBox:b}=this;if(l){let {PI:a,floor:c,min:d,max:g,sin:h,cos:m}=Math;var f=a/2,k=2*a;let v=Math.abs(l),p=e[0]/v,y=e[2]/v,w=c((p+f)/k)!==c((y+f)/k)?-v:d(h(p)*v,h(y)*v);f=c((p-f)/k)!==c((y-f)/k)?v:g(h(p)*v,h(y)*v);k=c((p+a)/k)!==c((y+
a)/k)?2*v:g(v-m(p)*v,v-m(y)*v);b.min.set(w,e[1],0>l?-k:0);b.max.set(f,e[3],0>l?0:k)}else b.min.set(e[0],e[1],0),b.max.set(e[2],e[3],0);b.getBoundingSphere(this.boundingSphere)}}applyClipRect(e){let f=this.getAttribute("aTroikaGlyphIndex").count,k=this._chunkedBounds;if(k)for(let l=k.length;l--;){f=k[l].end;let b=k[l].rect;if(b[1]<e.w&&b[3]>e.y&&b[0]<e.z&&b[2]>e.x)break}this.instanceCount=f}}let ra=new v.MeshBasicMaterial({color:16777215,side:v.DoubleSide,transparent:!0}),Ka=new v.Matrix4,ka=new v.Vector3,
sa=new v.Vector3,da=[],Za=new v.Vector3,La=()=>{let e=new v.Mesh(new v.PlaneGeometry(1,1),ra);La=()=>e;return e},Ma=()=>{let e=new v.Mesh(new v.PlaneGeometry(1,1,32,1),ra);Ma=()=>e;return e},$a={type:"syncstart"},ab={type:"synccomplete"};Q="font fontSize letterSpacing lineHeight maxWidth overflowWrap text direction textAlign textIndent whiteSpace anchorX anchorY colorRanges sdfGlyphSize".split(" ");let bb=Q.concat("material","color","depthOffset","clipRect","curveRadius","orientation","glyphGeometryDetail");
class Na extends v.Mesh{constructor(){super(new Ja,null);this.text="";this.curveRadius=this.anchorY=this.anchorX=0;this.direction="auto";this.font=null;this.fontSize=.1;this.letterSpacing=0;this.lineHeight="normal";this.maxWidth=Infinity;this.overflowWrap="normal";this.textAlign="left";this.textIndent=0;this.whiteSpace="normal";this.colorRanges=this.color=this.material=null;this.outlineColor=this.outlineWidth=0;this.outlineOpacity=1;this.strokeWidth=this.outlineOffsetY=this.outlineOffsetX=this.outlineBlur=
0;this.strokeColor=8421504;this.fillOpacity=this.strokeOpacity=1;this.depthOffset=0;this.clipRect=null;this.orientation="+x+y";this.glyphGeometryDetail=1;this.sdfGlyphSize=null;this.gpuAccelerateSDF=!0;this.debugSDF=!1}sync(e){this._needsSync&&(this._needsSync=!1,this._isSyncing?(this._queuedSyncs||(this._queuedSyncs=[])).push(e):(this._isSyncing=!0,this.dispatchEvent($a),Aa({text:this.text,font:this.font,fontSize:this.fontSize||.1,letterSpacing:this.letterSpacing||0,lineHeight:this.lineHeight||"normal",
maxWidth:this.maxWidth,direction:this.direction||"auto",textAlign:this.textAlign,textIndent:this.textIndent,whiteSpace:this.whiteSpace,overflowWrap:this.overflowWrap,anchorX:this.anchorX,anchorY:this.anchorY,colorRanges:this.colorRanges,includeCaretPositions:!0,sdfGlyphSize:this.sdfGlyphSize,gpuAccelerateSDF:this.gpuAccelerateSDF},f=>{this._isSyncing=!1;this._textRenderInfo=f;this.geometry.updateGlyphs(f.glyphBounds,f.glyphAtlasIndices,f.blockBounds,f.chunkedBounds,f.glyphColors);let k=this._queuedSyncs;
k&&(this._queuedSyncs=null,this._needsSync=!0,this.sync(()=>{k.forEach(e=>e&&e())}));this.dispatchEvent(ab);e&&e()})))}onBeforeRender(e,f,k,l,b,a){this.sync();b.isTroikaTextMaterial&&this._prepareForRender(b);b._hadOwnSide=b.hasOwnProperty("side");this.geometry.setSide(b._actualSide=b.side);b.side=v.FrontSide}onAfterRender(e,f,k,l,b,a){b._hadOwnSide?b.side=b._actualSide:delete b.side}dispose(){this.geometry.dispose()}get textRenderInfo(){return this._textRenderInfo||null}get material(){let e=this._derivedMaterial,
f=this._baseMaterial||this._defaultMaterial||(this._defaultMaterial=ra.clone());e&&e.baseMaterial===f||(e=this._derivedMaterial=Ga(f),f.addEventListener("dispose",function l(){f.removeEventListener("dispose",l);e.dispose()}));if(this.outlineWidth||this.outlineBlur||this.outlineOffsetX||this.outlineOffsetY){let f=e._outlineMtl;f||(f=e._outlineMtl=Object.create(e,{id:{value:e.id+.1}}),f.isTextOutlineMaterial=!0,f.depthWrite=!1,f.map=null,e.addEventListener("dispose",function b(){e.removeEventListener("dispose",
b);f.dispose()}));return[f,e]}return e}set material(e){e&&e.isTroikaTextMaterial?(this._derivedMaterial=e,this._baseMaterial=e.baseMaterial):this._baseMaterial=e}get glyphGeometryDetail(){return this.geometry.detail}set glyphGeometryDetail(e){this.geometry.detail=e}get curveRadius(){return this.geometry.curveRadius}set curveRadius(e){this.geometry.curveRadius=e}get customDepthMaterial(){return Ha(this.material).getDepthMaterial()}get customDistanceMaterial(){return Ha(this.material).getDistanceMaterial()}_prepareForRender(e){var f=
e.isTextOutlineMaterial,k=e.uniforms,l=this.textRenderInfo;if(l){let {sdfTexture:a,blockBounds:c}=l;k.uTroikaSDFTexture.value=a;k.uTroikaSDFTextureSize.value.set(a.image.width,a.image.height);k.uTroikaSDFGlyphSize.value=l.sdfGlyphSize;k.uTroikaSDFExponent.value=l.sdfExponent;k.uTroikaTotalBounds.value.fromArray(c);k.uTroikaUseGlyphColors.value=!f&&!!l.glyphColors;let d=l=0,e=0,h;let m=0,v=0;if(f){let {outlineWidth:a,outlineOffsetX:b,outlineOffsetY:c,outlineBlur:e,outlineOpacity:f}=this;l=this._parsePercent(a)||
0;d=Math.max(0,this._parsePercent(e)||0);h=f;m=this._parsePercent(b)||0;v=this._parsePercent(c)||0}else{if(e=Math.max(0,this._parsePercent(this.strokeWidth)||0)){var b=this.strokeColor;k.uTroikaStrokeColor.value.set(null==b?8421504:b);b=this.strokeOpacity;null==b&&(b=1)}h=this.fillOpacity}k.uTroikaDistanceOffset.value=l;k.uTroikaPositionOffset.value.set(m,v);k.uTroikaBlurRadius.value=d;k.uTroikaStrokeWidth.value=e;k.uTroikaStrokeOpacity.value=b;k.uTroikaFillOpacity.value=null==h?1:h;k.uTroikaCurveRadius.value=
this.curveRadius||0;(l=this.clipRect)&&Array.isArray(l)&&4===l.length?k.uTroikaClipRect.value.fromArray(l):(l=100*(this.fontSize||.1),k.uTroikaClipRect.value.set(c[0]-l,c[1]-l,c[2]+l,c[3]+l));this.geometry.applyClipRect(k.uTroikaClipRect.value)}k.uTroikaSDFDebug.value=!!this.debugSDF;e.polygonOffset=!!this.depthOffset;e.polygonOffsetFactor=e.polygonOffsetUnits=this.depthOffset||0;f=f?this.outlineColor||0:this.color;null==f?delete e.color:(l=e.hasOwnProperty("color")?e.color:e.color=new v.Color,(f!==
l._input||"object"===typeof f)&&l.set(l._input=f));f=this.orientation||"+x+y";if(f!==e._orientation){k=k.uTroikaOrient.value;f=f.replace(/[^-+xyz]/g,"");if(l="+x+y"!==f&&f.match(/^([-+])([xyz])([-+])([xyz])$/)){let [,a,b,d,e]=l;ka.set(0,0,0)[b]="-"===a?1:-1;sa.set(0,0,0)[e]="-"===d?-1:1;Ka.lookAt(Za,ka.cross(sa),sa);k.setFromMatrix4(Ka)}else k.identity();e._orientation=f}}_parsePercent(e){"string"===typeof e&&(e=(e=e.match(/^(-?[\d.]+)%$/))?parseFloat(e[1]):NaN,e=(isNaN(e)?0:e/100)*this.fontSize);
return e}localPositionToTextCoords(e,f=new v.Vector2){f.copy(e);let k=this.curveRadius;k&&(f.x=Math.atan2(e.x,Math.abs(k)-Math.abs(e.z))*Math.abs(k));return f}worldPositionToTextCoords(e,f=new v.Vector2){ka.copy(e);return this.localPositionToTextCoords(this.worldToLocal(ka),f)}raycast(e,f){let {textRenderInfo:k,curveRadius:l}=this;if(k){let b=k.blockBounds,a=l?Ma():La(),c=a.geometry,{position:d,uv:g}=c.attributes;for(let a=0;a<g.count;a++){let c=b[0]+g.getX(a)*(b[2]-b[0]),e=b[1]+g.getY(a)*(b[3]-b[1]),
f=0;l&&(f=l-Math.cos(c/l)*l,c=Math.sin(c/l)*l);d.setXYZ(a,c,e,f)}c.boundingSphere=this.geometry.boundingSphere;c.boundingBox=this.geometry.boundingBox;a.matrixWorld=this.matrixWorld;a.material.side=this.material.side;da.length=0;a.raycast(e,da);for(e=0;e<da.length;e++)da[e].object=this,f.push(da[e])}}copy(e){let f=this.geometry;super.copy(e);this.geometry=f;bb.forEach(f=>{this[f]=e[f]});return this}clone(){return(new this.constructor).copy(this)}}Q.forEach(e=>{let f="_private_"+e;Object.defineProperty(Na.prototype,
e,{get(){return this[f]},set(e){e!==this[f]&&(this[f]=e,this._needsSync=!0)}})});let Oa=new WeakMap,Ia=new WeakMap;E.GlyphsGeometry=Ja;E.Text=Na;E.configureTextBuilder=function(e){Ba?console.warn("configureTextBuilder called after first font request; will be ignored."):Ca(S,e)};E.createTextDerivedMaterial=Ga;E.dumpSDFTextures=function(){Object.keys(ea).forEach(e=>{e=ea[e].sdfCanvas;let {width:f,height:k}=e;console.log("%c.",`
      background: url(${e.toDataURL()});
      background-size: ${f}px ${k}px;
      color: transparent;
      font-size: 0;
      line-height: ${k}px;
      padding-left: ${f}px;
    `)})};E.getCaretAtPoint=function(e,f,k){let l=null,{caretHeight:b}=e;e=Ya(e);let a=Infinity;e.forEach((c,d)=>{Math.abs(k-(d+b/2))<Math.abs(k-(a+b/2))&&(a=d)});e.get(a).forEach(a=>{if(!l||Math.abs(f-a.x)<Math.abs(f-l.x))l=a});return l};E.getSelectionRects=function(e,f,k){var l;if(e){if((l=Oa.get(e))&&l.start===f&&l.end===k)return l.rects;let {caretPositions:h,caretHeight:m}=e;k<f&&(l=f,f=k,k=l);f=Math.max(f,0);k=Math.min(k,h.length+1);l=[];var b=null;for(var a=f;a<k;a++){var c=h[3*a],d=h[3*a+1],
g=Math.min(c,d);c=Math.max(c,d);d=h[3*a+2];if(!b||d!==b.bottom||g>b.right||c<b.left)b={left:Infinity,right:-Infinity,bottom:d,top:d+m},l.push(b);b.left=Math.min(g,b.left);b.right=Math.max(c,b.right)}l.sort((a,b)=>b.bottom-a.bottom||a.left-b.left);for(b=l.length-1;0<b--;)a=l[b],g=l[b+1],a.bottom===g.bottom&&a.left<=g.right&&a.right>=g.left&&(g.left=Math.min(g.left,a.left),g.right=Math.max(g.right,a.right),l.splice(b,1));Oa.set(e,{start:f,end:k,rects:l})}return l};E.preloadFont=function({font:e,characters:f,
sdfGlyphSize:k},l){f=Array.isArray(f)?f.join("\n"):""+f;Aa({font:e,sdfGlyphSize:k,text:f},l)};E.typesetterWorkerModule=Y;Object.defineProperty(E,"__esModule",{value:!0})})
