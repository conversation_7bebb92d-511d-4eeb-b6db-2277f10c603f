{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, '__esModule', {\n  value: true\n});\nfunction shallow(objA, objB) {\n  if (Object.is(objA, objB)) {\n    return true;\n  }\n  if (typeof objA !== 'object' || objA === null || typeof objB !== 'object' || objB === null) {\n    return false;\n  }\n  var keysA = Object.keys(objA);\n  if (keysA.length !== Object.keys(objB).length) {\n    return false;\n  }\n  for (var i = 0; i < keysA.length; i++) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keysA[i]) || !Object.is(objA[keysA[i]], objB[keysA[i]])) {\n      return false;\n    }\n  }\n  return true;\n}\nexports[\"default\"] = shallow;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "shallow", "objA", "objB", "is", "keysA", "keys", "length", "i", "prototype", "hasOwnProperty", "call"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/zustand/shallow.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nfunction shallow(objA, objB) {\n  if (Object.is(objA, objB)) {\n    return true;\n  }\n\n  if (typeof objA !== 'object' || objA === null || typeof objB !== 'object' || objB === null) {\n    return false;\n  }\n\n  var keysA = Object.keys(objA);\n\n  if (keysA.length !== Object.keys(objB).length) {\n    return false;\n  }\n\n  for (var i = 0; i < keysA.length; i++) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keysA[i]) || !Object.is(objA[keysA[i]], objB[keysA[i]])) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nexports[\"default\"] = shallow;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAE7D,SAASC,OAAOA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC3B,IAAIN,MAAM,CAACO,EAAE,CAACF,IAAI,EAAEC,IAAI,CAAC,EAAE;IACzB,OAAO,IAAI;EACb;EAEA,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,IAAI,OAAOC,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;IAC1F,OAAO,KAAK;EACd;EAEA,IAAIE,KAAK,GAAGR,MAAM,CAACS,IAAI,CAACJ,IAAI,CAAC;EAE7B,IAAIG,KAAK,CAACE,MAAM,KAAKV,MAAM,CAACS,IAAI,CAACH,IAAI,CAAC,CAACI,MAAM,EAAE;IAC7C,OAAO,KAAK;EACd;EAEA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IACrC,IAAI,CAACX,MAAM,CAACY,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,IAAI,EAAEE,KAAK,CAACG,CAAC,CAAC,CAAC,IAAI,CAACX,MAAM,CAACO,EAAE,CAACF,IAAI,CAACG,KAAK,CAACG,CAAC,CAAC,CAAC,EAAEL,IAAI,CAACE,KAAK,CAACG,CAAC,CAAC,CAAC,CAAC,EAAE;MACvG,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb;AAEAT,OAAO,CAAC,SAAS,CAAC,GAAGE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}