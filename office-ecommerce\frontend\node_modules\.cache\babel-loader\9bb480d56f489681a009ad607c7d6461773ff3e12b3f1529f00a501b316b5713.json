{"ast": null, "code": "import { DataTexture, FloatType, UnsignedIntType, RGBAFormat, RGIntegerFormat, NearestFilter } from 'three';\nimport { FloatVertexAttributeTexture, UIntVertexAttributeTexture } from './VertexAttributeTexture.js';\nimport { BYTES_PER_NODE } from '../core/Constants.js';\nimport { BOUNDING_DATA_INDEX, COUNT, IS_LEAF, RIGHT_NODE, OFFSET, SPLIT_AXIS } from '../core/nodeBufferFunctions.js';\nfunction bvhToTextures(bvh, boundsTexture, contentsTexture) {\n  const roots = bvh._roots;\n  if (roots.length !== 1) {\n    throw new Error('MeshBVHUniformStruct: Multi-root BVHs not supported.');\n  }\n  const root = roots[0];\n  const uint16Array = new Uint16Array(root);\n  const uint32Array = new Uint32Array(root);\n  const float32Array = new Float32Array(root);\n\n  // Both bounds need two elements per node so compute the height so it's twice as long as\n  // the width so we can expand the row by two and still have a square texture\n  const nodeCount = root.byteLength / BYTES_PER_NODE;\n  const boundsDimension = 2 * Math.ceil(Math.sqrt(nodeCount / 2));\n  const boundsArray = new Float32Array(4 * boundsDimension * boundsDimension);\n  const contentsDimension = Math.ceil(Math.sqrt(nodeCount));\n  const contentsArray = new Uint32Array(2 * contentsDimension * contentsDimension);\n  for (let i = 0; i < nodeCount; i++) {\n    const nodeIndex32 = i * BYTES_PER_NODE / 4;\n    const nodeIndex16 = nodeIndex32 * 2;\n    const boundsIndex = BOUNDING_DATA_INDEX(nodeIndex32);\n    for (let b = 0; b < 3; b++) {\n      boundsArray[8 * i + 0 + b] = float32Array[boundsIndex + 0 + b];\n      boundsArray[8 * i + 4 + b] = float32Array[boundsIndex + 3 + b];\n    }\n    if (IS_LEAF(nodeIndex16, uint16Array)) {\n      const count = COUNT(nodeIndex16, uint16Array);\n      const offset = OFFSET(nodeIndex32, uint32Array);\n      const mergedLeafCount = 0xffff0000 | count;\n      contentsArray[i * 2 + 0] = mergedLeafCount;\n      contentsArray[i * 2 + 1] = offset;\n    } else {\n      const rightIndex = 4 * RIGHT_NODE(nodeIndex32, uint32Array) / BYTES_PER_NODE;\n      const splitAxis = SPLIT_AXIS(nodeIndex32, uint32Array);\n      contentsArray[i * 2 + 0] = splitAxis;\n      contentsArray[i * 2 + 1] = rightIndex;\n    }\n  }\n  boundsTexture.image.data = boundsArray;\n  boundsTexture.image.width = boundsDimension;\n  boundsTexture.image.height = boundsDimension;\n  boundsTexture.format = RGBAFormat;\n  boundsTexture.type = FloatType;\n  boundsTexture.internalFormat = 'RGBA32F';\n  boundsTexture.minFilter = NearestFilter;\n  boundsTexture.magFilter = NearestFilter;\n  boundsTexture.generateMipmaps = false;\n  boundsTexture.needsUpdate = true;\n  boundsTexture.dispose();\n  contentsTexture.image.data = contentsArray;\n  contentsTexture.image.width = contentsDimension;\n  contentsTexture.image.height = contentsDimension;\n  contentsTexture.format = RGIntegerFormat;\n  contentsTexture.type = UnsignedIntType;\n  contentsTexture.internalFormat = 'RG32UI';\n  contentsTexture.minFilter = NearestFilter;\n  contentsTexture.magFilter = NearestFilter;\n  contentsTexture.generateMipmaps = false;\n  contentsTexture.needsUpdate = true;\n  contentsTexture.dispose();\n}\nexport class MeshBVHUniformStruct {\n  constructor() {\n    this.autoDispose = true;\n    this.index = new UIntVertexAttributeTexture();\n    this.position = new FloatVertexAttributeTexture();\n    this.bvhBounds = new DataTexture();\n    this.bvhContents = new DataTexture();\n    this.index.overrideItemSize = 3;\n  }\n  updateFrom(bvh) {\n    const {\n      geometry\n    } = bvh;\n    bvhToTextures(bvh, this.bvhBounds, this.bvhContents);\n    this.index.updateFrom(geometry.index);\n    this.position.updateFrom(geometry.attributes.position);\n  }\n  dispose() {\n    const {\n      index,\n      position,\n      bvhBounds,\n      bvhContents\n    } = this;\n    if (index) index.dispose();\n    if (position) position.dispose();\n    if (bvhBounds) bvhBounds.dispose();\n    if (bvhContents) bvhContents.dispose();\n  }\n}", "map": {"version": 3, "names": ["DataTexture", "FloatType", "UnsignedIntType", "RGBAFormat", "RGIntegerFormat", "NearestFilter", "FloatVertexAttributeTexture", "UIntVertexAttributeTexture", "BYTES_PER_NODE", "BOUNDING_DATA_INDEX", "COUNT", "IS_LEAF", "RIGHT_NODE", "OFFSET", "SPLIT_AXIS", "bvhToTextures", "bvh", "boundsTexture", "contentsTexture", "roots", "_roots", "length", "Error", "root", "uint16Array", "Uint16Array", "uint32Array", "Uint32Array", "float32Array", "Float32Array", "nodeCount", "byteLength", "boundsDimension", "Math", "ceil", "sqrt", "boundsArray", "contentsDimension", "contentsArray", "i", "nodeIndex32", "nodeIndex16", "boundsIndex", "b", "count", "offset", "mergedLeafCount", "rightIndex", "splitAxis", "image", "data", "width", "height", "format", "type", "internalFormat", "minFilter", "magFilter", "generateMipmaps", "needsUpdate", "dispose", "MeshBVHUniformStruct", "constructor", "autoDispose", "index", "position", "bvhBounds", "bvhContents", "overrideItemSize", "updateFrom", "geometry", "attributes"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/three-mesh-bvh/src/gpu/MeshBVHUniformStruct.js"], "sourcesContent": ["import {\n\tDataTexture,\n\tFloatType,\n\tUnsignedIntType,\n\tRGBAFormat,\n\tRGIntegerFormat,\n\tNearestFilter,\n} from 'three';\nimport {\n\tFloatVertexAttributeTexture,\n\tUIntVertexAttributeTexture,\n} from './VertexAttributeTexture.js';\nimport { BYTES_PER_NODE } from '../core/Constants.js';\nimport {\n\tBOUNDING_DATA_INDEX,\n\tCOUNT,\n\tIS_LEAF,\n\tRIGHT_NODE,\n\tOFFSET,\n\tSPLIT_AXIS,\n} from '../core/nodeBufferFunctions.js';\n\nfunction bvhToTextures( bvh, boundsTexture, contentsTexture ) {\n\n\tconst roots = bvh._roots;\n\n\tif ( roots.length !== 1 ) {\n\n\t\tthrow new Error( 'MeshBVHUniformStruct: Multi-root BVHs not supported.' );\n\n\t}\n\n\tconst root = roots[ 0 ];\n\tconst uint16Array = new Uint16Array( root );\n\tconst uint32Array = new Uint32Array( root );\n\tconst float32Array = new Float32Array( root );\n\n\t// Both bounds need two elements per node so compute the height so it's twice as long as\n\t// the width so we can expand the row by two and still have a square texture\n\tconst nodeCount = root.byteLength / BYTES_PER_NODE;\n\tconst boundsDimension = 2 * Math.ceil( Math.sqrt( nodeCount / 2 ) );\n\tconst boundsArray = new Float32Array( 4 * boundsDimension * boundsDimension );\n\n\tconst contentsDimension = Math.ceil( Math.sqrt( nodeCount ) );\n\tconst contentsArray = new Uint32Array( 2 * contentsDimension * contentsDimension );\n\n\tfor ( let i = 0; i < nodeCount; i ++ ) {\n\n\t\tconst nodeIndex32 = i * BYTES_PER_NODE / 4;\n\t\tconst nodeIndex16 = nodeIndex32 * 2;\n\t\tconst boundsIndex = BOUNDING_DATA_INDEX( nodeIndex32 );\n\t\tfor ( let b = 0; b < 3; b ++ ) {\n\n\t\t\tboundsArray[ 8 * i + 0 + b ] = float32Array[ boundsIndex + 0 + b ];\n\t\t\tboundsArray[ 8 * i + 4 + b ] = float32Array[ boundsIndex + 3 + b ];\n\n\t\t}\n\n\t\tif ( IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\t\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\n\t\t\tconst mergedLeafCount = 0xffff0000 | count;\n\t\t\tcontentsArray[ i * 2 + 0 ] = mergedLeafCount;\n\t\t\tcontentsArray[ i * 2 + 1 ] = offset;\n\n\t\t} else {\n\n\t\t\tconst rightIndex = 4 * RIGHT_NODE( nodeIndex32, uint32Array ) / BYTES_PER_NODE;\n\t\t\tconst splitAxis = SPLIT_AXIS( nodeIndex32, uint32Array );\n\n\t\t\tcontentsArray[ i * 2 + 0 ] = splitAxis;\n\t\t\tcontentsArray[ i * 2 + 1 ] = rightIndex;\n\n\t\t}\n\n\t}\n\n\tboundsTexture.image.data = boundsArray;\n\tboundsTexture.image.width = boundsDimension;\n\tboundsTexture.image.height = boundsDimension;\n\tboundsTexture.format = RGBAFormat;\n\tboundsTexture.type = FloatType;\n\tboundsTexture.internalFormat = 'RGBA32F';\n\tboundsTexture.minFilter = NearestFilter;\n\tboundsTexture.magFilter = NearestFilter;\n\tboundsTexture.generateMipmaps = false;\n\tboundsTexture.needsUpdate = true;\n\tboundsTexture.dispose();\n\n\tcontentsTexture.image.data = contentsArray;\n\tcontentsTexture.image.width = contentsDimension;\n\tcontentsTexture.image.height = contentsDimension;\n\tcontentsTexture.format = RGIntegerFormat;\n\tcontentsTexture.type = UnsignedIntType;\n\tcontentsTexture.internalFormat = 'RG32UI';\n\tcontentsTexture.minFilter = NearestFilter;\n\tcontentsTexture.magFilter = NearestFilter;\n\tcontentsTexture.generateMipmaps = false;\n\tcontentsTexture.needsUpdate = true;\n\tcontentsTexture.dispose();\n\n}\n\nexport class MeshBVHUniformStruct {\n\n\tconstructor() {\n\n\t\tthis.autoDispose = true;\n\t\tthis.index = new UIntVertexAttributeTexture();\n\t\tthis.position = new FloatVertexAttributeTexture();\n\t\tthis.bvhBounds = new DataTexture();\n\t\tthis.bvhContents = new DataTexture();\n\n\t\tthis.index.overrideItemSize = 3;\n\n\t}\n\n\tupdateFrom( bvh ) {\n\n\t\tconst { geometry } = bvh;\n\n\t\tbvhToTextures( bvh, this.bvhBounds, this.bvhContents );\n\n\t\tthis.index.updateFrom( geometry.index );\n\t\tthis.position.updateFrom( geometry.attributes.position );\n\n\t}\n\n\tdispose() {\n\n\t\tconst { index, position, bvhBounds, bvhContents } = this;\n\n\t\tif ( index ) index.dispose();\n\t\tif ( position ) position.dispose();\n\t\tif ( bvhBounds ) bvhBounds.dispose();\n\t\tif ( bvhContents ) bvhContents.dispose();\n\n\t}\n\n}\n"], "mappings": "AAAA,SACCA,WAAW,EACXC,SAAS,EACTC,eAAe,EACfC,UAAU,EACVC,eAAe,EACfC,aAAa,QACP,OAAO;AACd,SACCC,2BAA2B,EAC3BC,0BAA0B,QACpB,6BAA6B;AACpC,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SACCC,mBAAmB,EACnBC,KAAK,EACLC,OAAO,EACPC,UAAU,EACVC,MAAM,EACNC,UAAU,QACJ,gCAAgC;AAEvC,SAASC,aAAaA,CAAEC,GAAG,EAAEC,aAAa,EAAEC,eAAe,EAAG;EAE7D,MAAMC,KAAK,GAAGH,GAAG,CAACI,MAAM;EAExB,IAAKD,KAAK,CAACE,MAAM,KAAK,CAAC,EAAG;IAEzB,MAAM,IAAIC,KAAK,CAAE,sDAAuD,CAAC;EAE1E;EAEA,MAAMC,IAAI,GAAGJ,KAAK,CAAE,CAAC,CAAE;EACvB,MAAMK,WAAW,GAAG,IAAIC,WAAW,CAAEF,IAAK,CAAC;EAC3C,MAAMG,WAAW,GAAG,IAAIC,WAAW,CAAEJ,IAAK,CAAC;EAC3C,MAAMK,YAAY,GAAG,IAAIC,YAAY,CAAEN,IAAK,CAAC;;EAE7C;EACA;EACA,MAAMO,SAAS,GAAGP,IAAI,CAACQ,UAAU,GAAGvB,cAAc;EAClD,MAAMwB,eAAe,GAAG,CAAC,GAAGC,IAAI,CAACC,IAAI,CAAED,IAAI,CAACE,IAAI,CAAEL,SAAS,GAAG,CAAE,CAAE,CAAC;EACnE,MAAMM,WAAW,GAAG,IAAIP,YAAY,CAAE,CAAC,GAAGG,eAAe,GAAGA,eAAgB,CAAC;EAE7E,MAAMK,iBAAiB,GAAGJ,IAAI,CAACC,IAAI,CAAED,IAAI,CAACE,IAAI,CAAEL,SAAU,CAAE,CAAC;EAC7D,MAAMQ,aAAa,GAAG,IAAIX,WAAW,CAAE,CAAC,GAAGU,iBAAiB,GAAGA,iBAAkB,CAAC;EAElF,KAAM,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,SAAS,EAAES,CAAC,EAAG,EAAG;IAEtC,MAAMC,WAAW,GAAGD,CAAC,GAAG/B,cAAc,GAAG,CAAC;IAC1C,MAAMiC,WAAW,GAAGD,WAAW,GAAG,CAAC;IACnC,MAAME,WAAW,GAAGjC,mBAAmB,CAAE+B,WAAY,CAAC;IACtD,KAAM,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAG,EAAG;MAE9BP,WAAW,CAAE,CAAC,GAAGG,CAAC,GAAG,CAAC,GAAGI,CAAC,CAAE,GAAGf,YAAY,CAAEc,WAAW,GAAG,CAAC,GAAGC,CAAC,CAAE;MAClEP,WAAW,CAAE,CAAC,GAAGG,CAAC,GAAG,CAAC,GAAGI,CAAC,CAAE,GAAGf,YAAY,CAAEc,WAAW,GAAG,CAAC,GAAGC,CAAC,CAAE;IAEnE;IAEA,IAAKhC,OAAO,CAAE8B,WAAW,EAAEjB,WAAY,CAAC,EAAG;MAE1C,MAAMoB,KAAK,GAAGlC,KAAK,CAAE+B,WAAW,EAAEjB,WAAY,CAAC;MAC/C,MAAMqB,MAAM,GAAGhC,MAAM,CAAE2B,WAAW,EAAEd,WAAY,CAAC;MAEjD,MAAMoB,eAAe,GAAG,UAAU,GAAGF,KAAK;MAC1CN,aAAa,CAAEC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAGO,eAAe;MAC5CR,aAAa,CAAEC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAGM,MAAM;IAEpC,CAAC,MAAM;MAEN,MAAME,UAAU,GAAG,CAAC,GAAGnC,UAAU,CAAE4B,WAAW,EAAEd,WAAY,CAAC,GAAGlB,cAAc;MAC9E,MAAMwC,SAAS,GAAGlC,UAAU,CAAE0B,WAAW,EAAEd,WAAY,CAAC;MAExDY,aAAa,CAAEC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAGS,SAAS;MACtCV,aAAa,CAAEC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAE,GAAGQ,UAAU;IAExC;EAED;EAEA9B,aAAa,CAACgC,KAAK,CAACC,IAAI,GAAGd,WAAW;EACtCnB,aAAa,CAACgC,KAAK,CAACE,KAAK,GAAGnB,eAAe;EAC3Cf,aAAa,CAACgC,KAAK,CAACG,MAAM,GAAGpB,eAAe;EAC5Cf,aAAa,CAACoC,MAAM,GAAGlD,UAAU;EACjCc,aAAa,CAACqC,IAAI,GAAGrD,SAAS;EAC9BgB,aAAa,CAACsC,cAAc,GAAG,SAAS;EACxCtC,aAAa,CAACuC,SAAS,GAAGnD,aAAa;EACvCY,aAAa,CAACwC,SAAS,GAAGpD,aAAa;EACvCY,aAAa,CAACyC,eAAe,GAAG,KAAK;EACrCzC,aAAa,CAAC0C,WAAW,GAAG,IAAI;EAChC1C,aAAa,CAAC2C,OAAO,CAAC,CAAC;EAEvB1C,eAAe,CAAC+B,KAAK,CAACC,IAAI,GAAGZ,aAAa;EAC1CpB,eAAe,CAAC+B,KAAK,CAACE,KAAK,GAAGd,iBAAiB;EAC/CnB,eAAe,CAAC+B,KAAK,CAACG,MAAM,GAAGf,iBAAiB;EAChDnB,eAAe,CAACmC,MAAM,GAAGjD,eAAe;EACxCc,eAAe,CAACoC,IAAI,GAAGpD,eAAe;EACtCgB,eAAe,CAACqC,cAAc,GAAG,QAAQ;EACzCrC,eAAe,CAACsC,SAAS,GAAGnD,aAAa;EACzCa,eAAe,CAACuC,SAAS,GAAGpD,aAAa;EACzCa,eAAe,CAACwC,eAAe,GAAG,KAAK;EACvCxC,eAAe,CAACyC,WAAW,GAAG,IAAI;EAClCzC,eAAe,CAAC0C,OAAO,CAAC,CAAC;AAE1B;AAEA,OAAO,MAAMC,oBAAoB,CAAC;EAEjCC,WAAWA,CAAA,EAAG;IAEb,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,KAAK,GAAG,IAAIzD,0BAA0B,CAAC,CAAC;IAC7C,IAAI,CAAC0D,QAAQ,GAAG,IAAI3D,2BAA2B,CAAC,CAAC;IACjD,IAAI,CAAC4D,SAAS,GAAG,IAAIlE,WAAW,CAAC,CAAC;IAClC,IAAI,CAACmE,WAAW,GAAG,IAAInE,WAAW,CAAC,CAAC;IAEpC,IAAI,CAACgE,KAAK,CAACI,gBAAgB,GAAG,CAAC;EAEhC;EAEAC,UAAUA,CAAErD,GAAG,EAAG;IAEjB,MAAM;MAAEsD;IAAS,CAAC,GAAGtD,GAAG;IAExBD,aAAa,CAAEC,GAAG,EAAE,IAAI,CAACkD,SAAS,EAAE,IAAI,CAACC,WAAY,CAAC;IAEtD,IAAI,CAACH,KAAK,CAACK,UAAU,CAAEC,QAAQ,CAACN,KAAM,CAAC;IACvC,IAAI,CAACC,QAAQ,CAACI,UAAU,CAAEC,QAAQ,CAACC,UAAU,CAACN,QAAS,CAAC;EAEzD;EAEAL,OAAOA,CAAA,EAAG;IAET,MAAM;MAAEI,KAAK;MAAEC,QAAQ;MAAEC,SAAS;MAAEC;IAAY,CAAC,GAAG,IAAI;IAExD,IAAKH,KAAK,EAAGA,KAAK,CAACJ,OAAO,CAAC,CAAC;IAC5B,IAAKK,QAAQ,EAAGA,QAAQ,CAACL,OAAO,CAAC,CAAC;IAClC,IAAKM,SAAS,EAAGA,SAAS,CAACN,OAAO,CAAC,CAAC;IACpC,IAAKO,WAAW,EAAGA,WAAW,CAACP,OAAO,CAAC,CAAC;EAEzC;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}