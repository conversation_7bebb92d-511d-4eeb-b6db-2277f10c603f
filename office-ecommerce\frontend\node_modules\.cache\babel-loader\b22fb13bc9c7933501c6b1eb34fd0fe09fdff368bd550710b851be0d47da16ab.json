{"ast": null, "code": "import * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as THREE from 'three';\nconst SpriteAnimator = ({\n  startFrame,\n  endFrame,\n  fps,\n  frameName,\n  textureDataURL,\n  textureImageURL,\n  loop,\n  numberOfFrames,\n  autoPlay,\n  animationNames,\n  onStart,\n  onEnd,\n  onLoopEnd,\n  onFrame,\n  play,\n  pause,\n  flipX,\n  alphaTest,\n  children,\n  ...props\n}, fref) => {\n  useThree(state => state.viewport);\n  const spriteData = React.useRef(null);\n  const [isJsonReady, setJsonReady] = React.useState(false);\n  const matRef = React.useRef();\n  const spriteRef = React.useRef();\n  const timerOffset = React.useRef(window.performance.now());\n  const textureData = React.useRef();\n  const currentFrame = React.useRef(startFrame || 0);\n  const currentFrameName = React.useRef(frameName || '');\n  const fpsInterval = 1000 / (fps || 30);\n  const [spriteTexture, setSpriteTexture] = React.useState(new THREE.Texture());\n  const totalFrames = React.useRef(0);\n  const [aspect, setAspect] = React.useState([1, 1, 1]);\n  const flipOffset = flipX ? -1 : 1;\n  function loadJsonAndTextureAndExecuteCallback(jsonUrl, textureUrl, callback) {\n    const textureLoader = new THREE.TextureLoader();\n    const jsonPromise = fetch(jsonUrl).then(response => response.json());\n    const texturePromise = new Promise(resolve => {\n      textureLoader.load(textureUrl, resolve);\n    });\n    Promise.all([jsonPromise, texturePromise]).then(response => {\n      callback(response[0], response[1]);\n    });\n  }\n  const calculateAspectRatio = (width, height) => {\n    const aspectRatio = height / width;\n    spriteRef.current.scale.set(1, aspectRatio, 1);\n    return [1, aspectRatio, 1];\n  }; // initial loads\n\n  React.useEffect(() => {\n    if (textureDataURL && textureImageURL) {\n      loadJsonAndTextureAndExecuteCallback(textureDataURL, textureImageURL, parseSpriteData);\n    } else if (textureImageURL) {\n      // only load the texture, this is an image sprite only\n      const textureLoader = new THREE.TextureLoader();\n      new Promise(resolve => {\n        textureLoader.load(textureImageURL, resolve);\n      }).then(texture => {\n        parseSpriteData(null, texture);\n      });\n    }\n  }, []);\n  React.useLayoutEffect(() => {\n    modifySpritePosition();\n  }, [spriteTexture]);\n  React.useEffect(() => {}, [pause]);\n  React.useEffect(() => {\n    if (currentFrameName.current !== frameName && frameName) {\n      currentFrame.current = 0;\n      currentFrameName.current = frameName;\n    }\n  }, [frameName]);\n  const parseSpriteData = (json, _spriteTexture) => {\n    // sprite only case\n    if (json === null) {\n      if (_spriteTexture && numberOfFrames) {\n        //get size from texture\n        const width = _spriteTexture.image.width;\n        const height = _spriteTexture.image.height;\n        const frameWidth = width / numberOfFrames;\n        const frameHeight = height;\n        textureData.current = _spriteTexture;\n        totalFrames.current = numberOfFrames;\n        spriteData.current = {\n          frames: [],\n          meta: {\n            version: '1.0',\n            size: {\n              w: width,\n              h: height\n            },\n            scale: '1'\n          }\n        };\n        if (parseInt(frameWidth.toString(), 10) === frameWidth) {\n          // if it fits\n          for (let i = 0; i < numberOfFrames; i++) {\n            spriteData.current.frames.push({\n              frame: {\n                x: i * frameWidth,\n                y: 0,\n                w: frameWidth,\n                h: frameHeight\n              },\n              rotated: false,\n              trimmed: false,\n              spriteSourceSize: {\n                x: 0,\n                y: 0,\n                w: frameWidth,\n                h: frameHeight\n              },\n              sourceSize: {\n                w: frameWidth,\n                h: height\n              }\n            });\n          }\n        }\n      }\n    } else if (_spriteTexture) {\n      spriteData.current = json;\n      spriteData.current.frames = Array.isArray(json.frames) ? json.frames : parseFrames();\n      totalFrames.current = Array.isArray(json.frames) ? json.frames.length : Object.keys(json.frames).length;\n      textureData.current = _spriteTexture;\n      const {\n        w,\n        h\n      } = getFirstItem(json.frames).sourceSize;\n      const aspect = calculateAspectRatio(w, h);\n      setAspect(aspect);\n      if (matRef.current) {\n        matRef.current.map = _spriteTexture;\n      }\n    }\n    _spriteTexture.premultiplyAlpha = false;\n    setSpriteTexture(_spriteTexture);\n  }; // for frame based JSON Hash sprite data\n\n  const parseFrames = () => {\n    const sprites = {};\n    const data = spriteData.current;\n    const delimiters = animationNames;\n    if (delimiters) {\n      for (let i = 0; i < delimiters.length; i++) {\n        sprites[delimiters[i]] = [];\n        for (let innerKey in data['frames']) {\n          const value = data['frames'][innerKey];\n          const frameData = value['frame'];\n          const x = frameData['x'];\n          const y = frameData['y'];\n          const width = frameData['w'];\n          const height = frameData['h'];\n          const sourceWidth = value['sourceSize']['w'];\n          const sourceHeight = value['sourceSize']['h'];\n          if (typeof innerKey === 'string' && innerKey.toLowerCase().indexOf(delimiters[i].toLowerCase()) !== -1) {\n            sprites[delimiters[i]].push({\n              x: x,\n              y: y,\n              w: width,\n              h: height,\n              frame: frameData,\n              sourceSize: {\n                w: sourceWidth,\n                h: sourceHeight\n              }\n            });\n          }\n        }\n      }\n    }\n    return sprites;\n  }; // modify the sprite material after json is parsed and state updated\n\n  const modifySpritePosition = () => {\n    if (!spriteData.current) return;\n    const {\n      meta: {\n        size: metaInfo\n      },\n      frames\n    } = spriteData.current;\n    const {\n      w: frameW,\n      h: frameH\n    } = Array.isArray(frames) ? frames[0].sourceSize : frameName ? frames[frameName] ? frames[frameName][0].sourceSize : {\n      w: 0,\n      h: 0\n    } : {\n      w: 0,\n      h: 0\n    };\n    matRef.current.map.wrapS = matRef.current.map.wrapT = THREE.RepeatWrapping;\n    matRef.current.map.center.set(0, 0);\n    matRef.current.map.repeat.set(1 * flipOffset / (metaInfo.w / frameW), 1 / (metaInfo.h / frameH)); //const framesH = (metaInfo.w - 1) / frameW\n\n    const framesV = (metaInfo.h - 1) / frameH;\n    const frameOffsetY = 1 / framesV;\n    matRef.current.map.offset.x = 0.0; //-matRef.current.map.repeat.x\n\n    matRef.current.map.offset.y = 1 - frameOffsetY;\n    setJsonReady(true);\n    if (onStart) onStart({\n      currentFrameName: frameName,\n      currentFrame: currentFrame.current\n    });\n  }; // run the animation on each frame\n\n  const runAnimation = () => {\n    //if (!frameName) return\n    const now = window.performance.now();\n    const diff = now - timerOffset.current;\n    const {\n      meta: {\n        size: metaInfo\n      },\n      frames\n    } = spriteData.current;\n    const {\n      w: frameW,\n      h: frameH\n    } = getFirstItem(frames).sourceSize;\n    const spriteFrames = Array.isArray(frames) ? frames : frameName ? frames[frameName] : [];\n    let finalValX = 0;\n    let finalValY = 0;\n    const _endFrame = endFrame || spriteFrames.length - 1;\n    if (currentFrame.current > _endFrame) {\n      currentFrame.current = loop ? startFrame !== null && startFrame !== void 0 ? startFrame : 0 : 0;\n      if (loop) {\n        onLoopEnd == null ? void 0 : onLoopEnd({\n          currentFrameName: frameName,\n          currentFrame: currentFrame.current\n        });\n      } else {\n        onEnd == null ? void 0 : onEnd({\n          currentFrameName: frameName,\n          currentFrame: currentFrame.current\n        });\n      }\n      if (!loop) return;\n    }\n    if (diff <= fpsInterval) return;\n    timerOffset.current = now - diff % fpsInterval;\n    calculateAspectRatio(frameW, frameH);\n    const framesH = (metaInfo.w - 1) / frameW;\n    const framesV = (metaInfo.h - 1) / frameH;\n    const {\n      frame: {\n        x: frameX,\n        y: frameY\n      },\n      sourceSize: {\n        w: originalSizeX,\n        h: originalSizeY\n      }\n    } = spriteFrames[currentFrame.current];\n    const frameOffsetX = 1 / framesH;\n    const frameOffsetY = 1 / framesV;\n    finalValX = flipOffset > 0 ? frameOffsetX * (frameX / originalSizeX) : frameOffsetX * (frameX / originalSizeX) - matRef.current.map.repeat.x;\n    finalValY = Math.abs(1 - frameOffsetY) - frameOffsetY * (frameY / originalSizeY);\n    matRef.current.map.offset.x = finalValX;\n    matRef.current.map.offset.y = finalValY;\n    currentFrame.current += 1;\n  }; // *** Warning! It runs on every frame! ***\n\n  useFrame((state, delta) => {\n    var _spriteData$current, _matRef$current;\n    if (!((_spriteData$current = spriteData.current) != null && _spriteData$current.frames) || !((_matRef$current = matRef.current) != null && _matRef$current.map)) {\n      return;\n    }\n    if (pause) {\n      return;\n    }\n    if (autoPlay || play) {\n      runAnimation();\n      onFrame && onFrame({\n        currentFrameName: currentFrameName.current,\n        currentFrame: currentFrame.current\n      });\n    }\n  }); // utils\n\n  const getFirstItem = param => {\n    if (Array.isArray(param)) {\n      return param[0];\n    } else if (typeof param === 'object' && param !== null) {\n      const keys = Object.keys(param);\n      return param[keys[0]][0];\n    } else {\n      return {\n        w: 0,\n        h: 0\n      };\n    }\n  };\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(React.Suspense, {\n    fallback: null\n  }, /*#__PURE__*/React.createElement(\"sprite\", {\n    ref: spriteRef,\n    scale: aspect\n  }, /*#__PURE__*/React.createElement(\"spriteMaterial\", {\n    toneMapped: false,\n    ref: matRef,\n    map: spriteTexture,\n    transparent: true,\n    alphaTest: alphaTest !== null && alphaTest !== void 0 ? alphaTest : 0.0\n  }))), children);\n};\nexport { SpriteAnimator };", "map": {"version": 3, "names": ["React", "useThree", "useFrame", "THREE", "SpriteAnimator", "startFrame", "endFrame", "fps", "frameName", "textureDataURL", "textureImageURL", "loop", "numberOfFrames", "autoPlay", "animationNames", "onStart", "onEnd", "onLoopEnd", "onFrame", "play", "pause", "flipX", "alphaTest", "children", "props", "fref", "state", "viewport", "spriteData", "useRef", "isJsonReady", "setJsonReady", "useState", "mat<PERSON><PERSON>", "spriteRef", "timerOffset", "window", "performance", "now", "textureData", "currentFrame", "currentFrameName", "fpsInterval", "spriteTexture", "setSpriteTexture", "Texture", "totalFrames", "aspect", "setAspect", "flipOffset", "loadJsonAndTextureAndExecuteCallback", "jsonUrl", "textureUrl", "callback", "textureLoader", "TextureLoader", "jsonPromise", "fetch", "then", "response", "json", "texturePromise", "Promise", "resolve", "load", "all", "calculateAspectRatio", "width", "height", "aspectRatio", "current", "scale", "set", "useEffect", "parseSpriteData", "texture", "useLayoutEffect", "modifySpritePosition", "_spriteTexture", "image", "frameWidth", "frameHeight", "frames", "meta", "version", "size", "w", "h", "parseInt", "toString", "i", "push", "frame", "x", "y", "rotated", "trimmed", "spriteSourceSize", "sourceSize", "Array", "isArray", "parseFrames", "length", "Object", "keys", "getFirstItem", "map", "premultiplyAlpha", "sprites", "data", "delimiters", "innerKey", "value", "frameData", "sourceWidth", "sourceHeight", "toLowerCase", "indexOf", "metaInfo", "frameW", "frameH", "wrapS", "wrapT", "RepeatWrapping", "center", "repeat", "framesV", "frameOffsetY", "offset", "runAnimation", "diff", "spriteFrames", "finalValX", "finalValY", "_endFrame", "framesH", "frameX", "frameY", "originalSizeX", "originalSizeY", "frameOffsetX", "Math", "abs", "delta", "_spriteData$current", "_matRef$current", "param", "createElement", "Suspense", "fallback", "ref", "toneMapped", "transparent"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/SpriteAnimator.js"], "sourcesContent": ["import * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as THREE from 'three';\n\nconst SpriteAnimator = ({\n  startFrame,\n  endFrame,\n  fps,\n  frameName,\n  textureDataURL,\n  textureImageURL,\n  loop,\n  numberOfFrames,\n  autoPlay,\n  animationNames,\n  onStart,\n  onEnd,\n  onLoopEnd,\n  onFrame,\n  play,\n  pause,\n  flipX,\n  alphaTest,\n  children,\n  ...props\n}, fref) => {\n  useThree(state => state.viewport);\n  const spriteData = React.useRef(null);\n  const [isJsonReady, setJsonReady] = React.useState(false);\n  const matRef = React.useRef();\n  const spriteRef = React.useRef();\n  const timerOffset = React.useRef(window.performance.now());\n  const textureData = React.useRef();\n  const currentFrame = React.useRef(startFrame || 0);\n  const currentFrameName = React.useRef(frameName || '');\n  const fpsInterval = 1000 / (fps || 30);\n  const [spriteTexture, setSpriteTexture] = React.useState(new THREE.Texture());\n  const totalFrames = React.useRef(0);\n  const [aspect, setAspect] = React.useState([1, 1, 1]);\n  const flipOffset = flipX ? -1 : 1;\n\n  function loadJsonAndTextureAndExecuteCallback(jsonUrl, textureUrl, callback) {\n    const textureLoader = new THREE.TextureLoader();\n    const jsonPromise = fetch(jsonUrl).then(response => response.json());\n    const texturePromise = new Promise(resolve => {\n      textureLoader.load(textureUrl, resolve);\n    });\n    Promise.all([jsonPromise, texturePromise]).then(response => {\n      callback(response[0], response[1]);\n    });\n  }\n\n  const calculateAspectRatio = (width, height) => {\n    const aspectRatio = height / width;\n    spriteRef.current.scale.set(1, aspectRatio, 1);\n    return [1, aspectRatio, 1];\n  }; // initial loads\n\n\n  React.useEffect(() => {\n    if (textureDataURL && textureImageURL) {\n      loadJsonAndTextureAndExecuteCallback(textureDataURL, textureImageURL, parseSpriteData);\n    } else if (textureImageURL) {\n      // only load the texture, this is an image sprite only\n      const textureLoader = new THREE.TextureLoader();\n      new Promise(resolve => {\n        textureLoader.load(textureImageURL, resolve);\n      }).then(texture => {\n        parseSpriteData(null, texture);\n      });\n    }\n  }, []);\n  React.useLayoutEffect(() => {\n    modifySpritePosition();\n  }, [spriteTexture]);\n  React.useEffect(() => {\n  }, [pause]);\n  React.useEffect(() => {\n    if (currentFrameName.current !== frameName && frameName) {\n      currentFrame.current = 0;\n      currentFrameName.current = frameName;\n    }\n  }, [frameName]);\n\n  const parseSpriteData = (json, _spriteTexture) => {\n    // sprite only case\n    if (json === null) {\n      if (_spriteTexture && numberOfFrames) {\n        //get size from texture\n        const width = _spriteTexture.image.width;\n        const height = _spriteTexture.image.height;\n        const frameWidth = width / numberOfFrames;\n        const frameHeight = height;\n        textureData.current = _spriteTexture;\n        totalFrames.current = numberOfFrames;\n        spriteData.current = {\n          frames: [],\n          meta: {\n            version: '1.0',\n            size: {\n              w: width,\n              h: height\n            },\n            scale: '1'\n          }\n        };\n\n        if (parseInt(frameWidth.toString(), 10) === frameWidth) {\n          // if it fits\n          for (let i = 0; i < numberOfFrames; i++) {\n            spriteData.current.frames.push({\n              frame: {\n                x: i * frameWidth,\n                y: 0,\n                w: frameWidth,\n                h: frameHeight\n              },\n              rotated: false,\n              trimmed: false,\n              spriteSourceSize: {\n                x: 0,\n                y: 0,\n                w: frameWidth,\n                h: frameHeight\n              },\n              sourceSize: {\n                w: frameWidth,\n                h: height\n              }\n            });\n          }\n        }\n      }\n    } else if (_spriteTexture) {\n      spriteData.current = json;\n      spriteData.current.frames = Array.isArray(json.frames) ? json.frames : parseFrames();\n      totalFrames.current = Array.isArray(json.frames) ? json.frames.length : Object.keys(json.frames).length;\n      textureData.current = _spriteTexture;\n      const {\n        w,\n        h\n      } = getFirstItem(json.frames).sourceSize;\n      const aspect = calculateAspectRatio(w, h);\n      setAspect(aspect);\n\n      if (matRef.current) {\n        matRef.current.map = _spriteTexture;\n      }\n    }\n\n    _spriteTexture.premultiplyAlpha = false;\n    setSpriteTexture(_spriteTexture);\n  }; // for frame based JSON Hash sprite data\n\n\n  const parseFrames = () => {\n    const sprites = {};\n    const data = spriteData.current;\n    const delimiters = animationNames;\n\n    if (delimiters) {\n      for (let i = 0; i < delimiters.length; i++) {\n        sprites[delimiters[i]] = [];\n\n        for (let innerKey in data['frames']) {\n          const value = data['frames'][innerKey];\n          const frameData = value['frame'];\n          const x = frameData['x'];\n          const y = frameData['y'];\n          const width = frameData['w'];\n          const height = frameData['h'];\n          const sourceWidth = value['sourceSize']['w'];\n          const sourceHeight = value['sourceSize']['h'];\n\n          if (typeof innerKey === 'string' && innerKey.toLowerCase().indexOf(delimiters[i].toLowerCase()) !== -1) {\n            sprites[delimiters[i]].push({\n              x: x,\n              y: y,\n              w: width,\n              h: height,\n              frame: frameData,\n              sourceSize: {\n                w: sourceWidth,\n                h: sourceHeight\n              }\n            });\n          }\n        }\n      }\n    }\n\n    return sprites;\n  }; // modify the sprite material after json is parsed and state updated\n\n\n  const modifySpritePosition = () => {\n    if (!spriteData.current) return;\n    const {\n      meta: {\n        size: metaInfo\n      },\n      frames\n    } = spriteData.current;\n    const {\n      w: frameW,\n      h: frameH\n    } = Array.isArray(frames) ? frames[0].sourceSize : frameName ? frames[frameName] ? frames[frameName][0].sourceSize : {\n      w: 0,\n      h: 0\n    } : {\n      w: 0,\n      h: 0\n    };\n    matRef.current.map.wrapS = matRef.current.map.wrapT = THREE.RepeatWrapping;\n    matRef.current.map.center.set(0, 0);\n    matRef.current.map.repeat.set(1 * flipOffset / (metaInfo.w / frameW), 1 / (metaInfo.h / frameH)); //const framesH = (metaInfo.w - 1) / frameW\n\n    const framesV = (metaInfo.h - 1) / frameH;\n    const frameOffsetY = 1 / framesV;\n    matRef.current.map.offset.x = 0.0; //-matRef.current.map.repeat.x\n\n    matRef.current.map.offset.y = 1 - frameOffsetY;\n    setJsonReady(true);\n    if (onStart) onStart({\n      currentFrameName: frameName,\n      currentFrame: currentFrame.current\n    });\n  }; // run the animation on each frame\n\n\n  const runAnimation = () => {\n    //if (!frameName) return\n    const now = window.performance.now();\n    const diff = now - timerOffset.current;\n    const {\n      meta: {\n        size: metaInfo\n      },\n      frames\n    } = spriteData.current;\n    const {\n      w: frameW,\n      h: frameH\n    } = getFirstItem(frames).sourceSize;\n    const spriteFrames = Array.isArray(frames) ? frames : frameName ? frames[frameName] : [];\n    let finalValX = 0;\n    let finalValY = 0;\n\n    const _endFrame = endFrame || spriteFrames.length - 1;\n\n    if (currentFrame.current > _endFrame) {\n      currentFrame.current = loop ? startFrame !== null && startFrame !== void 0 ? startFrame : 0 : 0;\n\n      if (loop) {\n        onLoopEnd == null ? void 0 : onLoopEnd({\n          currentFrameName: frameName,\n          currentFrame: currentFrame.current\n        });\n      } else {\n        onEnd == null ? void 0 : onEnd({\n          currentFrameName: frameName,\n          currentFrame: currentFrame.current\n        });\n      }\n\n      if (!loop) return;\n    }\n\n    if (diff <= fpsInterval) return;\n    timerOffset.current = now - diff % fpsInterval;\n    calculateAspectRatio(frameW, frameH);\n    const framesH = (metaInfo.w - 1) / frameW;\n    const framesV = (metaInfo.h - 1) / frameH;\n    const {\n      frame: {\n        x: frameX,\n        y: frameY\n      },\n      sourceSize: {\n        w: originalSizeX,\n        h: originalSizeY\n      }\n    } = spriteFrames[currentFrame.current];\n    const frameOffsetX = 1 / framesH;\n    const frameOffsetY = 1 / framesV;\n    finalValX = flipOffset > 0 ? frameOffsetX * (frameX / originalSizeX) : frameOffsetX * (frameX / originalSizeX) - matRef.current.map.repeat.x;\n    finalValY = Math.abs(1 - frameOffsetY) - frameOffsetY * (frameY / originalSizeY);\n    matRef.current.map.offset.x = finalValX;\n    matRef.current.map.offset.y = finalValY;\n    currentFrame.current += 1;\n  }; // *** Warning! It runs on every frame! ***\n\n\n  useFrame((state, delta) => {\n    var _spriteData$current, _matRef$current;\n\n    if (!((_spriteData$current = spriteData.current) != null && _spriteData$current.frames) || !((_matRef$current = matRef.current) != null && _matRef$current.map)) {\n      return;\n    }\n\n    if (pause) {\n      return;\n    }\n\n    if (autoPlay || play) {\n      runAnimation();\n      onFrame && onFrame({\n        currentFrameName: currentFrameName.current,\n        currentFrame: currentFrame.current\n      });\n    }\n  }); // utils\n\n  const getFirstItem = param => {\n    if (Array.isArray(param)) {\n      return param[0];\n    } else if (typeof param === 'object' && param !== null) {\n      const keys = Object.keys(param);\n      return param[keys[0]][0];\n    } else {\n      return {\n        w: 0,\n        h: 0\n      };\n    }\n  };\n\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(React.Suspense, {\n    fallback: null\n  }, /*#__PURE__*/React.createElement(\"sprite\", {\n    ref: spriteRef,\n    scale: aspect\n  }, /*#__PURE__*/React.createElement(\"spriteMaterial\", {\n    toneMapped: false,\n    ref: matRef,\n    map: spriteTexture,\n    transparent: true,\n    alphaTest: alphaTest !== null && alphaTest !== void 0 ? alphaTest : 0.0\n  }))), children);\n};\n\nexport { SpriteAnimator };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,MAAMC,cAAc,GAAGA,CAAC;EACtBC,UAAU;EACVC,QAAQ;EACRC,GAAG;EACHC,SAAS;EACTC,cAAc;EACdC,eAAe;EACfC,IAAI;EACJC,cAAc;EACdC,QAAQ;EACRC,cAAc;EACdC,OAAO;EACPC,KAAK;EACLC,SAAS;EACTC,OAAO;EACPC,IAAI;EACJC,KAAK;EACLC,KAAK;EACLC,SAAS;EACTC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACVxB,QAAQ,CAACyB,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;EACjC,MAAMC,UAAU,GAAG5B,KAAK,CAAC6B,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM,CAACC,WAAW,EAAEC,YAAY,CAAC,GAAG/B,KAAK,CAACgC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAMC,MAAM,GAAGjC,KAAK,CAAC6B,MAAM,CAAC,CAAC;EAC7B,MAAMK,SAAS,GAAGlC,KAAK,CAAC6B,MAAM,CAAC,CAAC;EAChC,MAAMM,WAAW,GAAGnC,KAAK,CAAC6B,MAAM,CAACO,MAAM,CAACC,WAAW,CAACC,GAAG,CAAC,CAAC,CAAC;EAC1D,MAAMC,WAAW,GAAGvC,KAAK,CAAC6B,MAAM,CAAC,CAAC;EAClC,MAAMW,YAAY,GAAGxC,KAAK,CAAC6B,MAAM,CAACxB,UAAU,IAAI,CAAC,CAAC;EAClD,MAAMoC,gBAAgB,GAAGzC,KAAK,CAAC6B,MAAM,CAACrB,SAAS,IAAI,EAAE,CAAC;EACtD,MAAMkC,WAAW,GAAG,IAAI,IAAInC,GAAG,IAAI,EAAE,CAAC;EACtC,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAG5C,KAAK,CAACgC,QAAQ,CAAC,IAAI7B,KAAK,CAAC0C,OAAO,CAAC,CAAC,CAAC;EAC7E,MAAMC,WAAW,GAAG9C,KAAK,CAAC6B,MAAM,CAAC,CAAC,CAAC;EACnC,MAAM,CAACkB,MAAM,EAAEC,SAAS,CAAC,GAAGhD,KAAK,CAACgC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACrD,MAAMiB,UAAU,GAAG5B,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;EAEjC,SAAS6B,oCAAoCA,CAACC,OAAO,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IAC3E,MAAMC,aAAa,GAAG,IAAInD,KAAK,CAACoD,aAAa,CAAC,CAAC;IAC/C,MAAMC,WAAW,GAAGC,KAAK,CAACN,OAAO,CAAC,CAACO,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC;IACpE,MAAMC,cAAc,GAAG,IAAIC,OAAO,CAACC,OAAO,IAAI;MAC5CT,aAAa,CAACU,IAAI,CAACZ,UAAU,EAAEW,OAAO,CAAC;IACzC,CAAC,CAAC;IACFD,OAAO,CAACG,GAAG,CAAC,CAACT,WAAW,EAAEK,cAAc,CAAC,CAAC,CAACH,IAAI,CAACC,QAAQ,IAAI;MAC1DN,QAAQ,CAACM,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC;EACJ;EAEA,MAAMO,oBAAoB,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;IAC9C,MAAMC,WAAW,GAAGD,MAAM,GAAGD,KAAK;IAClCjC,SAAS,CAACoC,OAAO,CAACC,KAAK,CAACC,GAAG,CAAC,CAAC,EAAEH,WAAW,EAAE,CAAC,CAAC;IAC9C,OAAO,CAAC,CAAC,EAAEA,WAAW,EAAE,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC;;EAGHrE,KAAK,CAACyE,SAAS,CAAC,MAAM;IACpB,IAAIhE,cAAc,IAAIC,eAAe,EAAE;MACrCwC,oCAAoC,CAACzC,cAAc,EAAEC,eAAe,EAAEgE,eAAe,CAAC;IACxF,CAAC,MAAM,IAAIhE,eAAe,EAAE;MAC1B;MACA,MAAM4C,aAAa,GAAG,IAAInD,KAAK,CAACoD,aAAa,CAAC,CAAC;MAC/C,IAAIO,OAAO,CAACC,OAAO,IAAI;QACrBT,aAAa,CAACU,IAAI,CAACtD,eAAe,EAAEqD,OAAO,CAAC;MAC9C,CAAC,CAAC,CAACL,IAAI,CAACiB,OAAO,IAAI;QACjBD,eAAe,CAAC,IAAI,EAAEC,OAAO,CAAC;MAChC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EACN3E,KAAK,CAAC4E,eAAe,CAAC,MAAM;IAC1BC,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAAClC,aAAa,CAAC,CAAC;EACnB3C,KAAK,CAACyE,SAAS,CAAC,MAAM,CACtB,CAAC,EAAE,CAACrD,KAAK,CAAC,CAAC;EACXpB,KAAK,CAACyE,SAAS,CAAC,MAAM;IACpB,IAAIhC,gBAAgB,CAAC6B,OAAO,KAAK9D,SAAS,IAAIA,SAAS,EAAE;MACvDgC,YAAY,CAAC8B,OAAO,GAAG,CAAC;MACxB7B,gBAAgB,CAAC6B,OAAO,GAAG9D,SAAS;IACtC;EACF,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAEf,MAAMkE,eAAe,GAAGA,CAACd,IAAI,EAAEkB,cAAc,KAAK;IAChD;IACA,IAAIlB,IAAI,KAAK,IAAI,EAAE;MACjB,IAAIkB,cAAc,IAAIlE,cAAc,EAAE;QACpC;QACA,MAAMuD,KAAK,GAAGW,cAAc,CAACC,KAAK,CAACZ,KAAK;QACxC,MAAMC,MAAM,GAAGU,cAAc,CAACC,KAAK,CAACX,MAAM;QAC1C,MAAMY,UAAU,GAAGb,KAAK,GAAGvD,cAAc;QACzC,MAAMqE,WAAW,GAAGb,MAAM;QAC1B7B,WAAW,CAAC+B,OAAO,GAAGQ,cAAc;QACpChC,WAAW,CAACwB,OAAO,GAAG1D,cAAc;QACpCgB,UAAU,CAAC0C,OAAO,GAAG;UACnBY,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE;YACJC,OAAO,EAAE,KAAK;YACdC,IAAI,EAAE;cACJC,CAAC,EAAEnB,KAAK;cACRoB,CAAC,EAAEnB;YACL,CAAC;YACDG,KAAK,EAAE;UACT;QACF,CAAC;QAED,IAAIiB,QAAQ,CAACR,UAAU,CAACS,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,KAAKT,UAAU,EAAE;UACtD;UACA,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9E,cAAc,EAAE8E,CAAC,EAAE,EAAE;YACvC9D,UAAU,CAAC0C,OAAO,CAACY,MAAM,CAACS,IAAI,CAAC;cAC7BC,KAAK,EAAE;gBACLC,CAAC,EAAEH,CAAC,GAAGV,UAAU;gBACjBc,CAAC,EAAE,CAAC;gBACJR,CAAC,EAAEN,UAAU;gBACbO,CAAC,EAAEN;cACL,CAAC;cACDc,OAAO,EAAE,KAAK;cACdC,OAAO,EAAE,KAAK;cACdC,gBAAgB,EAAE;gBAChBJ,CAAC,EAAE,CAAC;gBACJC,CAAC,EAAE,CAAC;gBACJR,CAAC,EAAEN,UAAU;gBACbO,CAAC,EAAEN;cACL,CAAC;cACDiB,UAAU,EAAE;gBACVZ,CAAC,EAAEN,UAAU;gBACbO,CAAC,EAAEnB;cACL;YACF,CAAC,CAAC;UACJ;QACF;MACF;IACF,CAAC,MAAM,IAAIU,cAAc,EAAE;MACzBlD,UAAU,CAAC0C,OAAO,GAAGV,IAAI;MACzBhC,UAAU,CAAC0C,OAAO,CAACY,MAAM,GAAGiB,KAAK,CAACC,OAAO,CAACxC,IAAI,CAACsB,MAAM,CAAC,GAAGtB,IAAI,CAACsB,MAAM,GAAGmB,WAAW,CAAC,CAAC;MACpFvD,WAAW,CAACwB,OAAO,GAAG6B,KAAK,CAACC,OAAO,CAACxC,IAAI,CAACsB,MAAM,CAAC,GAAGtB,IAAI,CAACsB,MAAM,CAACoB,MAAM,GAAGC,MAAM,CAACC,IAAI,CAAC5C,IAAI,CAACsB,MAAM,CAAC,CAACoB,MAAM;MACvG/D,WAAW,CAAC+B,OAAO,GAAGQ,cAAc;MACpC,MAAM;QACJQ,CAAC;QACDC;MACF,CAAC,GAAGkB,YAAY,CAAC7C,IAAI,CAACsB,MAAM,CAAC,CAACgB,UAAU;MACxC,MAAMnD,MAAM,GAAGmB,oBAAoB,CAACoB,CAAC,EAAEC,CAAC,CAAC;MACzCvC,SAAS,CAACD,MAAM,CAAC;MAEjB,IAAId,MAAM,CAACqC,OAAO,EAAE;QAClBrC,MAAM,CAACqC,OAAO,CAACoC,GAAG,GAAG5B,cAAc;MACrC;IACF;IAEAA,cAAc,CAAC6B,gBAAgB,GAAG,KAAK;IACvC/D,gBAAgB,CAACkC,cAAc,CAAC;EAClC,CAAC,CAAC,CAAC;;EAGH,MAAMuB,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMO,OAAO,GAAG,CAAC,CAAC;IAClB,MAAMC,IAAI,GAAGjF,UAAU,CAAC0C,OAAO;IAC/B,MAAMwC,UAAU,GAAGhG,cAAc;IAEjC,IAAIgG,UAAU,EAAE;MACd,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,UAAU,CAACR,MAAM,EAAEZ,CAAC,EAAE,EAAE;QAC1CkB,OAAO,CAACE,UAAU,CAACpB,CAAC,CAAC,CAAC,GAAG,EAAE;QAE3B,KAAK,IAAIqB,QAAQ,IAAIF,IAAI,CAAC,QAAQ,CAAC,EAAE;UACnC,MAAMG,KAAK,GAAGH,IAAI,CAAC,QAAQ,CAAC,CAACE,QAAQ,CAAC;UACtC,MAAME,SAAS,GAAGD,KAAK,CAAC,OAAO,CAAC;UAChC,MAAMnB,CAAC,GAAGoB,SAAS,CAAC,GAAG,CAAC;UACxB,MAAMnB,CAAC,GAAGmB,SAAS,CAAC,GAAG,CAAC;UACxB,MAAM9C,KAAK,GAAG8C,SAAS,CAAC,GAAG,CAAC;UAC5B,MAAM7C,MAAM,GAAG6C,SAAS,CAAC,GAAG,CAAC;UAC7B,MAAMC,WAAW,GAAGF,KAAK,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC;UAC5C,MAAMG,YAAY,GAAGH,KAAK,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC;UAE7C,IAAI,OAAOD,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAACK,WAAW,CAAC,CAAC,CAACC,OAAO,CAACP,UAAU,CAACpB,CAAC,CAAC,CAAC0B,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YACtGR,OAAO,CAACE,UAAU,CAACpB,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC;cAC1BE,CAAC,EAAEA,CAAC;cACJC,CAAC,EAAEA,CAAC;cACJR,CAAC,EAAEnB,KAAK;cACRoB,CAAC,EAAEnB,MAAM;cACTwB,KAAK,EAAEqB,SAAS;cAChBf,UAAU,EAAE;gBACVZ,CAAC,EAAE4B,WAAW;gBACd3B,CAAC,EAAE4B;cACL;YACF,CAAC,CAAC;UACJ;QACF;MACF;IACF;IAEA,OAAOP,OAAO;EAChB,CAAC,CAAC,CAAC;;EAGH,MAAM/B,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAACjD,UAAU,CAAC0C,OAAO,EAAE;IACzB,MAAM;MACJa,IAAI,EAAE;QACJE,IAAI,EAAEiC;MACR,CAAC;MACDpC;IACF,CAAC,GAAGtD,UAAU,CAAC0C,OAAO;IACtB,MAAM;MACJgB,CAAC,EAAEiC,MAAM;MACThC,CAAC,EAAEiC;IACL,CAAC,GAAGrB,KAAK,CAACC,OAAO,CAAClB,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAACgB,UAAU,GAAG1F,SAAS,GAAG0E,MAAM,CAAC1E,SAAS,CAAC,GAAG0E,MAAM,CAAC1E,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC0F,UAAU,GAAG;MACnHZ,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC,GAAG;MACFD,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC;IACDtD,MAAM,CAACqC,OAAO,CAACoC,GAAG,CAACe,KAAK,GAAGxF,MAAM,CAACqC,OAAO,CAACoC,GAAG,CAACgB,KAAK,GAAGvH,KAAK,CAACwH,cAAc;IAC1E1F,MAAM,CAACqC,OAAO,CAACoC,GAAG,CAACkB,MAAM,CAACpD,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACnCvC,MAAM,CAACqC,OAAO,CAACoC,GAAG,CAACmB,MAAM,CAACrD,GAAG,CAAC,CAAC,GAAGvB,UAAU,IAAIqE,QAAQ,CAAChC,CAAC,GAAGiC,MAAM,CAAC,EAAE,CAAC,IAAID,QAAQ,CAAC/B,CAAC,GAAGiC,MAAM,CAAC,CAAC,CAAC,CAAC;;IAElG,MAAMM,OAAO,GAAG,CAACR,QAAQ,CAAC/B,CAAC,GAAG,CAAC,IAAIiC,MAAM;IACzC,MAAMO,YAAY,GAAG,CAAC,GAAGD,OAAO;IAChC7F,MAAM,CAACqC,OAAO,CAACoC,GAAG,CAACsB,MAAM,CAACnC,CAAC,GAAG,GAAG,CAAC,CAAC;;IAEnC5D,MAAM,CAACqC,OAAO,CAACoC,GAAG,CAACsB,MAAM,CAAClC,CAAC,GAAG,CAAC,GAAGiC,YAAY;IAC9ChG,YAAY,CAAC,IAAI,CAAC;IAClB,IAAIhB,OAAO,EAAEA,OAAO,CAAC;MACnB0B,gBAAgB,EAAEjC,SAAS;MAC3BgC,YAAY,EAAEA,YAAY,CAAC8B;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;;EAGH,MAAM2D,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA,MAAM3F,GAAG,GAAGF,MAAM,CAACC,WAAW,CAACC,GAAG,CAAC,CAAC;IACpC,MAAM4F,IAAI,GAAG5F,GAAG,GAAGH,WAAW,CAACmC,OAAO;IACtC,MAAM;MACJa,IAAI,EAAE;QACJE,IAAI,EAAEiC;MACR,CAAC;MACDpC;IACF,CAAC,GAAGtD,UAAU,CAAC0C,OAAO;IACtB,MAAM;MACJgB,CAAC,EAAEiC,MAAM;MACThC,CAAC,EAAEiC;IACL,CAAC,GAAGf,YAAY,CAACvB,MAAM,CAAC,CAACgB,UAAU;IACnC,MAAMiC,YAAY,GAAGhC,KAAK,CAACC,OAAO,CAAClB,MAAM,CAAC,GAAGA,MAAM,GAAG1E,SAAS,GAAG0E,MAAM,CAAC1E,SAAS,CAAC,GAAG,EAAE;IACxF,IAAI4H,SAAS,GAAG,CAAC;IACjB,IAAIC,SAAS,GAAG,CAAC;IAEjB,MAAMC,SAAS,GAAGhI,QAAQ,IAAI6H,YAAY,CAAC7B,MAAM,GAAG,CAAC;IAErD,IAAI9D,YAAY,CAAC8B,OAAO,GAAGgE,SAAS,EAAE;MACpC9F,YAAY,CAAC8B,OAAO,GAAG3D,IAAI,GAAGN,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG,CAAC,GAAG,CAAC;MAE/F,IAAIM,IAAI,EAAE;QACRM,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC;UACrCwB,gBAAgB,EAAEjC,SAAS;UAC3BgC,YAAY,EAAEA,YAAY,CAAC8B;QAC7B,CAAC,CAAC;MACJ,CAAC,MAAM;QACLtD,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC;UAC7ByB,gBAAgB,EAAEjC,SAAS;UAC3BgC,YAAY,EAAEA,YAAY,CAAC8B;QAC7B,CAAC,CAAC;MACJ;MAEA,IAAI,CAAC3D,IAAI,EAAE;IACb;IAEA,IAAIuH,IAAI,IAAIxF,WAAW,EAAE;IACzBP,WAAW,CAACmC,OAAO,GAAGhC,GAAG,GAAG4F,IAAI,GAAGxF,WAAW;IAC9CwB,oBAAoB,CAACqD,MAAM,EAAEC,MAAM,CAAC;IACpC,MAAMe,OAAO,GAAG,CAACjB,QAAQ,CAAChC,CAAC,GAAG,CAAC,IAAIiC,MAAM;IACzC,MAAMO,OAAO,GAAG,CAACR,QAAQ,CAAC/B,CAAC,GAAG,CAAC,IAAIiC,MAAM;IACzC,MAAM;MACJ5B,KAAK,EAAE;QACLC,CAAC,EAAE2C,MAAM;QACT1C,CAAC,EAAE2C;MACL,CAAC;MACDvC,UAAU,EAAE;QACVZ,CAAC,EAAEoD,aAAa;QAChBnD,CAAC,EAAEoD;MACL;IACF,CAAC,GAAGR,YAAY,CAAC3F,YAAY,CAAC8B,OAAO,CAAC;IACtC,MAAMsE,YAAY,GAAG,CAAC,GAAGL,OAAO;IAChC,MAAMR,YAAY,GAAG,CAAC,GAAGD,OAAO;IAChCM,SAAS,GAAGnF,UAAU,GAAG,CAAC,GAAG2F,YAAY,IAAIJ,MAAM,GAAGE,aAAa,CAAC,GAAGE,YAAY,IAAIJ,MAAM,GAAGE,aAAa,CAAC,GAAGzG,MAAM,CAACqC,OAAO,CAACoC,GAAG,CAACmB,MAAM,CAAChC,CAAC;IAC5IwC,SAAS,GAAGQ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGf,YAAY,CAAC,GAAGA,YAAY,IAAIU,MAAM,GAAGE,aAAa,CAAC;IAChF1G,MAAM,CAACqC,OAAO,CAACoC,GAAG,CAACsB,MAAM,CAACnC,CAAC,GAAGuC,SAAS;IACvCnG,MAAM,CAACqC,OAAO,CAACoC,GAAG,CAACsB,MAAM,CAAClC,CAAC,GAAGuC,SAAS;IACvC7F,YAAY,CAAC8B,OAAO,IAAI,CAAC;EAC3B,CAAC,CAAC,CAAC;;EAGHpE,QAAQ,CAAC,CAACwB,KAAK,EAAEqH,KAAK,KAAK;IACzB,IAAIC,mBAAmB,EAAEC,eAAe;IAExC,IAAI,EAAE,CAACD,mBAAmB,GAAGpH,UAAU,CAAC0C,OAAO,KAAK,IAAI,IAAI0E,mBAAmB,CAAC9D,MAAM,CAAC,IAAI,EAAE,CAAC+D,eAAe,GAAGhH,MAAM,CAACqC,OAAO,KAAK,IAAI,IAAI2E,eAAe,CAACvC,GAAG,CAAC,EAAE;MAC/J;IACF;IAEA,IAAItF,KAAK,EAAE;MACT;IACF;IAEA,IAAIP,QAAQ,IAAIM,IAAI,EAAE;MACpB8G,YAAY,CAAC,CAAC;MACd/G,OAAO,IAAIA,OAAO,CAAC;QACjBuB,gBAAgB,EAAEA,gBAAgB,CAAC6B,OAAO;QAC1C9B,YAAY,EAAEA,YAAY,CAAC8B;MAC7B,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC,CAAC;;EAEJ,MAAMmC,YAAY,GAAGyC,KAAK,IAAI;IAC5B,IAAI/C,KAAK,CAACC,OAAO,CAAC8C,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;MACtD,MAAM1C,IAAI,GAAGD,MAAM,CAACC,IAAI,CAAC0C,KAAK,CAAC;MAC/B,OAAOA,KAAK,CAAC1C,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,MAAM;MACL,OAAO;QACLlB,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE;MACL,CAAC;IACH;EACF,CAAC;EAED,OAAO,aAAavF,KAAK,CAACmJ,aAAa,CAAC,OAAO,EAAE3H,KAAK,EAAE,aAAaxB,KAAK,CAACmJ,aAAa,CAACnJ,KAAK,CAACoJ,QAAQ,EAAE;IACvGC,QAAQ,EAAE;EACZ,CAAC,EAAE,aAAarJ,KAAK,CAACmJ,aAAa,CAAC,QAAQ,EAAE;IAC5CG,GAAG,EAAEpH,SAAS;IACdqC,KAAK,EAAExB;EACT,CAAC,EAAE,aAAa/C,KAAK,CAACmJ,aAAa,CAAC,gBAAgB,EAAE;IACpDI,UAAU,EAAE,KAAK;IACjBD,GAAG,EAAErH,MAAM;IACXyE,GAAG,EAAE/D,aAAa;IAClB6G,WAAW,EAAE,IAAI;IACjBlI,SAAS,EAAEA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG;EACtE,CAAC,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAAC;AACjB,CAAC;AAED,SAASnB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}