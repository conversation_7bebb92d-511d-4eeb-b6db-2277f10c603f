{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { TrackballControls as TrackballControls$1 } from 'three-stdlib';\nconst TrackballControls = /*#__PURE__*/React.forwardRef(({\n  makeDefault,\n  camera,\n  domElement,\n  regress,\n  onChange,\n  onStart,\n  onEnd,\n  ...restProps\n}, ref) => {\n  const {\n    invalidate,\n    camera: defaultCamera,\n    gl,\n    events,\n    set,\n    get,\n    performance,\n    viewport\n  } = useThree();\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = React.useMemo(() => new TrackballControls$1(explCamera), [explCamera]);\n  useFrame(() => {\n    if (controls.enabled) controls.update();\n  }, -1);\n  React.useEffect(() => {\n    controls.connect(explDomElement);\n    return () => void controls.dispose();\n  }, [explDomElement, regress, controls, invalidate]);\n  React.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (regress) performance.regress();\n      if (onChange) onChange(e);\n    };\n    controls.addEventListener('change', callback);\n    if (onStart) controls.addEventListener('start', onStart);\n    if (onEnd) controls.addEventListener('end', onEnd);\n    return () => {\n      if (onStart) controls.removeEventListener('start', onStart);\n      if (onEnd) controls.removeEventListener('end', onEnd);\n      controls.removeEventListener('change', callback);\n    };\n  }, [onChange, onStart, onEnd, controls, invalidate]);\n  React.useEffect(() => {\n    controls.handleResize();\n  }, [viewport]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls\n  }, restProps));\n});\nexport { TrackballControls };", "map": {"version": 3, "names": ["_extends", "useThree", "useFrame", "React", "TrackballControls", "TrackballControls$1", "forwardRef", "makeDefault", "camera", "dom<PERSON>lement", "regress", "onChange", "onStart", "onEnd", "restProps", "ref", "invalidate", "defaultCamera", "gl", "events", "set", "get", "performance", "viewport", "explCamera", "explDomElement", "connected", "controls", "useMemo", "enabled", "update", "useEffect", "connect", "dispose", "callback", "e", "addEventListener", "removeEventListener", "handleResize", "old", "createElement", "object"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/TrackballControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { TrackballControls as TrackballControls$1 } from 'three-stdlib';\n\nconst TrackballControls = /*#__PURE__*/React.forwardRef(({\n  makeDefault,\n  camera,\n  domElement,\n  regress,\n  onChange,\n  onStart,\n  onEnd,\n  ...restProps\n}, ref) => {\n  const {\n    invalidate,\n    camera: defaultCamera,\n    gl,\n    events,\n    set,\n    get,\n    performance,\n    viewport\n  } = useThree();\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = React.useMemo(() => new TrackballControls$1(explCamera), [explCamera]);\n  useFrame(() => {\n    if (controls.enabled) controls.update();\n  }, -1);\n  React.useEffect(() => {\n    controls.connect(explDomElement);\n    return () => void controls.dispose();\n  }, [explDomElement, regress, controls, invalidate]);\n  React.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (regress) performance.regress();\n      if (onChange) onChange(e);\n    };\n\n    controls.addEventListener('change', callback);\n    if (onStart) controls.addEventListener('start', onStart);\n    if (onEnd) controls.addEventListener('end', onEnd);\n    return () => {\n      if (onStart) controls.removeEventListener('start', onStart);\n      if (onEnd) controls.removeEventListener('end', onEnd);\n      controls.removeEventListener('change', callback);\n    };\n  }, [onChange, onStart, onEnd, controls, invalidate]);\n  React.useEffect(() => {\n    controls.handleResize();\n  }, [viewport]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls\n  }, restProps));\n});\n\nexport { TrackballControls };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,IAAIC,mBAAmB,QAAQ,cAAc;AAEvE,MAAMD,iBAAiB,GAAG,aAAaD,KAAK,CAACG,UAAU,CAAC,CAAC;EACvDC,WAAW;EACXC,MAAM;EACNC,UAAU;EACVC,OAAO;EACPC,QAAQ;EACRC,OAAO;EACPC,KAAK;EACL,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM;IACJC,UAAU;IACVR,MAAM,EAAES,aAAa;IACrBC,EAAE;IACFC,MAAM;IACNC,GAAG;IACHC,GAAG;IACHC,WAAW;IACXC;EACF,CAAC,GAAGtB,QAAQ,CAAC,CAAC;EACd,MAAMuB,UAAU,GAAGhB,MAAM,IAAIS,aAAa;EAC1C,MAAMQ,cAAc,GAAGhB,UAAU,IAAIU,MAAM,CAACO,SAAS,IAAIR,EAAE,CAACT,UAAU;EACtE,MAAMkB,QAAQ,GAAGxB,KAAK,CAACyB,OAAO,CAAC,MAAM,IAAIvB,mBAAmB,CAACmB,UAAU,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EACvFtB,QAAQ,CAAC,MAAM;IACb,IAAIyB,QAAQ,CAACE,OAAO,EAAEF,QAAQ,CAACG,MAAM,CAAC,CAAC;EACzC,CAAC,EAAE,CAAC,CAAC,CAAC;EACN3B,KAAK,CAAC4B,SAAS,CAAC,MAAM;IACpBJ,QAAQ,CAACK,OAAO,CAACP,cAAc,CAAC;IAChC,OAAO,MAAM,KAAKE,QAAQ,CAACM,OAAO,CAAC,CAAC;EACtC,CAAC,EAAE,CAACR,cAAc,EAAEf,OAAO,EAAEiB,QAAQ,EAAEX,UAAU,CAAC,CAAC;EACnDb,KAAK,CAAC4B,SAAS,CAAC,MAAM;IACpB,MAAMG,QAAQ,GAAGC,CAAC,IAAI;MACpBnB,UAAU,CAAC,CAAC;MACZ,IAAIN,OAAO,EAAEY,WAAW,CAACZ,OAAO,CAAC,CAAC;MAClC,IAAIC,QAAQ,EAAEA,QAAQ,CAACwB,CAAC,CAAC;IAC3B,CAAC;IAEDR,QAAQ,CAACS,gBAAgB,CAAC,QAAQ,EAAEF,QAAQ,CAAC;IAC7C,IAAItB,OAAO,EAAEe,QAAQ,CAACS,gBAAgB,CAAC,OAAO,EAAExB,OAAO,CAAC;IACxD,IAAIC,KAAK,EAAEc,QAAQ,CAACS,gBAAgB,CAAC,KAAK,EAAEvB,KAAK,CAAC;IAClD,OAAO,MAAM;MACX,IAAID,OAAO,EAAEe,QAAQ,CAACU,mBAAmB,CAAC,OAAO,EAAEzB,OAAO,CAAC;MAC3D,IAAIC,KAAK,EAAEc,QAAQ,CAACU,mBAAmB,CAAC,KAAK,EAAExB,KAAK,CAAC;MACrDc,QAAQ,CAACU,mBAAmB,CAAC,QAAQ,EAAEH,QAAQ,CAAC;IAClD,CAAC;EACH,CAAC,EAAE,CAACvB,QAAQ,EAAEC,OAAO,EAAEC,KAAK,EAAEc,QAAQ,EAAEX,UAAU,CAAC,CAAC;EACpDb,KAAK,CAAC4B,SAAS,CAAC,MAAM;IACpBJ,QAAQ,CAACW,YAAY,CAAC,CAAC;EACzB,CAAC,EAAE,CAACf,QAAQ,CAAC,CAAC;EACdpB,KAAK,CAAC4B,SAAS,CAAC,MAAM;IACpB,IAAIxB,WAAW,EAAE;MACf,MAAMgC,GAAG,GAAGlB,GAAG,CAAC,CAAC,CAACM,QAAQ;MAC1BP,GAAG,CAAC;QACFO;MACF,CAAC,CAAC;MACF,OAAO,MAAMP,GAAG,CAAC;QACfO,QAAQ,EAAEY;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAChC,WAAW,EAAEoB,QAAQ,CAAC,CAAC;EAC3B,OAAO,aAAaxB,KAAK,CAACqC,aAAa,CAAC,WAAW,EAAExC,QAAQ,CAAC;IAC5De,GAAG,EAAEA,GAAG;IACR0B,MAAM,EAAEd;EACV,CAAC,EAAEb,SAAS,CAAC,CAAC;AAChB,CAAC,CAAC;AAEF,SAASV,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}