"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),o=require("@react-three/fiber"),i=require("./shaderMaterial.cjs.js");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function a(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var l=n(e),s=a(t),c=a(r);const u=i.shaderMaterial({time:0,pixelRatio:1}," uniform float pixelRatio;\n    uniform float time;\n    attribute float size;  \n    attribute float speed;  \n    attribute float opacity;\n    attribute vec3 noise;\n    attribute vec3 color;\n    varying vec3 vColor;\n    varying float vOpacity;\n    void main() {\n      vec4 modelPosition = modelMatrix * vec4(position, 1.0);\n      modelPosition.y += sin(time * speed + modelPosition.x * noise.x * 100.0) * 0.2;\n      modelPosition.z += cos(time * speed + modelPosition.x * noise.y * 100.0) * 0.2;\n      modelPosition.x += cos(time * speed + modelPosition.x * noise.z * 100.0) * 0.2;\n      vec4 viewPosition = viewMatrix * modelPosition;\n      vec4 projectionPostion = projectionMatrix * viewPosition;\n      gl_Position = projectionPostion;\n      gl_PointSize = size * 25. * pixelRatio;\n      gl_PointSize *= (1.0 / - viewPosition.z);\n      vColor = color;\n      vOpacity = opacity;\n    }"," varying vec3 vColor;\n    varying float vOpacity;\n    void main() {\n      float distanceToCenter = distance(gl_PointCoord, vec2(0.5));\n      float strength = 0.05 / distanceToCenter - 0.1;\n      gl_FragColor = vec4(vColor, strength * vOpacity);\n      #include <tonemapping_fragment>\n      #include <encodings_fragment>\n    }"),f=e=>e&&e.constructor===Float32Array,m=e=>e instanceof c.Vector2||e instanceof c.Vector3||e instanceof c.Vector4,d=e=>Array.isArray(e)?e:m(e)?e.toArray():[e,e,e];function p(e,t,r){return s.useMemo((()=>{if(void 0!==t){if(f(t))return t;if(t instanceof c.Color){const r=Array.from({length:3*e},(()=>{return[(e=t).r,e.g,e.b];var e})).flat();return Float32Array.from(r)}if(m(t)||Array.isArray(t)){const r=Array.from({length:3*e},(()=>d(t))).flat();return Float32Array.from(r)}return Float32Array.from({length:e},(()=>t))}return Float32Array.from({length:e},r)}),[t])}const v=s.forwardRef((({noise:e=1,count:t=100,speed:r=1,opacity:i=1,scale:n=1,size:a,color:m,children:v,...b},y)=>{s.useMemo((()=>o.extend({SparklesImplMaterial:u})),[]);const g=s.useRef(null),h=o.useThree((e=>e.viewport.dpr)),A=s.useMemo((()=>Float32Array.from(Array.from({length:t},(()=>d(n).map(c.MathUtils.randFloatSpread))).flat())),[t,n]),P=p(t,a,Math.random),x=p(t,i),M=p(t,r),j=p(3*t,e),O=p(void 0===m?3*t:t,f(m)?m:new c.Color(m),(()=>1));return o.useFrame((e=>{g.current&&g.current.material&&(g.current.material.time=e.clock.elapsedTime)})),s.useImperativeHandle(y,(()=>g.current),[]),s.createElement("points",l.default({key:`particle-${t}-${JSON.stringify(n)}`},b,{ref:g}),s.createElement("bufferGeometry",null,s.createElement("bufferAttribute",{attach:"attributes-position",args:[A,3]}),s.createElement("bufferAttribute",{attach:"attributes-size",args:[P,1]}),s.createElement("bufferAttribute",{attach:"attributes-opacity",args:[x,1]}),s.createElement("bufferAttribute",{attach:"attributes-speed",args:[M,1]}),s.createElement("bufferAttribute",{attach:"attributes-color",args:[O,3]}),s.createElement("bufferAttribute",{attach:"attributes-noise",args:[j,3]})),v||s.createElement("sparklesImplMaterial",{transparent:!0,pixelRatio:h,depthWrite:!1}))}));exports.Sparkles=v;
