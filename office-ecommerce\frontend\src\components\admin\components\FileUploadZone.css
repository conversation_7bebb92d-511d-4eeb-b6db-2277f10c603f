/* File Upload Zone Styles */
.file-upload-zone {
  width: 100%;
}

.upload-area {
  border: 2px dashed #e1e8ed;
  border-radius: 12px;
  padding: 32px 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafbfc;
  position: relative;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: #F0B21B;
  background: #fffbf0;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(240, 178, 27, 0.1);
}

.upload-area.drag-over {
  border-color: #F0B21B;
  background: #fffbf0;
  border-style: solid;
  box-shadow: 0 0 20px rgba(240, 178, 27, 0.2);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 8px;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.upload-area:hover .upload-icon {
  opacity: 1;
  transform: scale(1.1);
}

.upload-text h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.upload-text p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
}

.upload-link {
  color: #F0B21B;
  font-weight: 500;
  text-decoration: underline;
}

.upload-link:hover {
  color: #d4a017;
}

.upload-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
  text-align: left;
  width: 100%;
  max-width: 400px;
}

.info-item {
  font-size: 12px;
  color: #6c757d;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item strong {
  color: #2c3e50;
  font-weight: 500;
}

/* Upload Progress */
.upload-progress {
  position: absolute;
  bottom: 16px;
  left: 24px;
  right: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #e1e8ed;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #F0B21B, #d4a017);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  font-weight: 500;
  color: #2c3e50;
  min-width: 35px;
}

/* Upload Tips */
.upload-tips {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #F0B21B;
}

.upload-tips h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
}

.upload-tips ul {
  margin: 0;
  padding-left: 20px;
  list-style-type: disc;
}

.upload-tips li {
  font-size: 13px;
  color: #6c757d;
  line-height: 1.4;
  margin-bottom: 6px;
}

.upload-tips li:last-child {
  margin-bottom: 0;
}

/* States */
.upload-area.uploading {
  pointer-events: none;
  opacity: 0.7;
}

.upload-area.error {
  border-color: #dc3545;
  background: #fff5f5;
}

.upload-area.success {
  border-color: #28a745;
  background: #f8fff9;
}

/* Responsive Design */
@media (max-width: 768px) {
  .upload-area {
    padding: 24px 16px;
    min-height: 160px;
  }

  .upload-icon {
    font-size: 36px;
  }

  .upload-text h3 {
    font-size: 18px;
  }

  .upload-text p {
    font-size: 13px;
  }

  .upload-info {
    padding: 12px;
    max-width: none;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }

  .upload-tips {
    padding: 12px;
  }

  .upload-tips h4 {
    font-size: 13px;
  }

  .upload-tips li {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .upload-area {
    padding: 20px 12px;
    min-height: 140px;
  }

  .upload-icon {
    font-size: 32px;
  }

  .upload-text h3 {
    font-size: 16px;
  }

  .upload-text p {
    font-size: 12px;
  }

  .upload-content {
    gap: 12px;
  }

  .upload-info {
    margin-top: 12px;
    padding: 10px;
  }

  .info-item {
    font-size: 11px;
  }

  .upload-progress {
    bottom: 12px;
    left: 12px;
    right: 12px;
  }

  .progress-text {
    font-size: 11px;
    min-width: 30px;
  }
}

/* Animation for drag and drop */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.upload-area.drag-over .upload-icon {
  animation: bounce 1s ease infinite;
}

/* File type specific styling */
.upload-area[data-file-type="3d"] {
  background: linear-gradient(135deg, #fafbfc 0%, #f0f8ff 100%);
}

.upload-area[data-file-type="image"] {
  background: linear-gradient(135deg, #fafbfc 0%, #fff8f0 100%);
}

.upload-area[data-file-type="document"] {
  background: linear-gradient(135deg, #fafbfc 0%, #f8fff8 100%);
}
