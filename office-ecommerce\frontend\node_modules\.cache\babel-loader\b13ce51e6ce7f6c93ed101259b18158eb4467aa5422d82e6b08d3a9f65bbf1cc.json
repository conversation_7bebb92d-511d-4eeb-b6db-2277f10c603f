{"ast": null, "code": "import { useLoader } from '@react-three/fiber';\nimport { CubeReflectionMapping, EquirectangularReflectionMapping, sRGBEncoding, LinearEncoding, CubeTextureLoader } from 'three';\nimport { RGBELoader } from 'three-stdlib';\nimport { presetsObj } from '../helpers/environment-assets.js';\nconst CUBEMAP_ROOT = 'https://market-assets.fra1.cdn.digitaloceanspaces.com/market-assets/hdris/';\nfunction useEnvironment({\n  files = ['/px.png', '/nx.png', '/py.png', '/ny.png', '/pz.png', '/nz.png'],\n  path = '',\n  preset = undefined,\n  encoding = undefined,\n  extensions\n} = {}) {\n  if (preset) {\n    if (!(preset in presetsObj)) throw new Error('Preset must be one of: ' + Object.keys(presetsObj).join(', '));\n    files = presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  }\n  const isCubeMap = Array.isArray(files);\n  const loader = isCubeMap ? CubeTextureLoader : RGBELoader;\n  const loaderResult = useLoader(\n  // @ts-expect-error\n  loader, isCubeMap ? [files] : files, loader => {\n    loader.setPath(path);\n    if (extensions) extensions(loader);\n  });\n  const texture = isCubeMap ?\n  // @ts-ignore\n  loaderResult[0] : loaderResult;\n  texture.mapping = isCubeMap ? CubeReflectionMapping : EquirectangularReflectionMapping;\n  texture.encoding = (encoding !== null && encoding !== void 0 ? encoding : isCubeMap) ? sRGBEncoding : LinearEncoding;\n  return texture;\n}\nexport { useEnvironment };", "map": {"version": 3, "names": ["useLoader", "CubeReflectionMapping", "EquirectangularReflectionMapping", "sRGBEncoding", "LinearEncoding", "CubeTextureLoader", "RGBELoader", "presetsObj", "CUBEMAP_ROOT", "useEnvironment", "files", "path", "preset", "undefined", "encoding", "extensions", "Error", "Object", "keys", "join", "isCubeMap", "Array", "isArray", "loader", "loaderResult", "set<PERSON>ath", "texture", "mapping"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useEnvironment.js"], "sourcesContent": ["import { useLoader } from '@react-three/fiber';\nimport { CubeReflectionMapping, EquirectangularReflectionMapping, sRGBEncoding, LinearEncoding, CubeTextureLoader } from 'three';\nimport { RGBELoader } from 'three-stdlib';\nimport { presetsObj } from '../helpers/environment-assets.js';\n\nconst CUBEMAP_ROOT = 'https://market-assets.fra1.cdn.digitaloceanspaces.com/market-assets/hdris/';\nfunction useEnvironment({\n  files = ['/px.png', '/nx.png', '/py.png', '/ny.png', '/pz.png', '/nz.png'],\n  path = '',\n  preset = undefined,\n  encoding = undefined,\n  extensions\n} = {}) {\n  if (preset) {\n    if (!(preset in presetsObj)) throw new Error('Preset must be one of: ' + Object.keys(presetsObj).join(', '));\n    files = presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  }\n\n  const isCubeMap = Array.isArray(files);\n  const loader = isCubeMap ? CubeTextureLoader : RGBELoader;\n  const loaderResult = useLoader( // @ts-expect-error\n  loader, isCubeMap ? [files] : files, loader => {\n    loader.setPath(path);\n    if (extensions) extensions(loader);\n  });\n  const texture = isCubeMap ? // @ts-ignore\n  loaderResult[0] : loaderResult;\n  texture.mapping = isCubeMap ? CubeReflectionMapping : EquirectangularReflectionMapping;\n  texture.encoding = (encoding !== null && encoding !== void 0 ? encoding : isCubeMap) ? sRGBEncoding : LinearEncoding;\n  return texture;\n}\n\nexport { useEnvironment };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,qBAAqB,EAAEC,gCAAgC,EAAEC,YAAY,EAAEC,cAAc,EAAEC,iBAAiB,QAAQ,OAAO;AAChI,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,UAAU,QAAQ,kCAAkC;AAE7D,MAAMC,YAAY,GAAG,4EAA4E;AACjG,SAASC,cAAcA,CAAC;EACtBC,KAAK,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAC1EC,IAAI,GAAG,EAAE;EACTC,MAAM,GAAGC,SAAS;EAClBC,QAAQ,GAAGD,SAAS;EACpBE;AACF,CAAC,GAAG,CAAC,CAAC,EAAE;EACN,IAAIH,MAAM,EAAE;IACV,IAAI,EAAEA,MAAM,IAAIL,UAAU,CAAC,EAAE,MAAM,IAAIS,KAAK,CAAC,yBAAyB,GAAGC,MAAM,CAACC,IAAI,CAACX,UAAU,CAAC,CAACY,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5GT,KAAK,GAAGH,UAAU,CAACK,MAAM,CAAC;IAC1BD,IAAI,GAAGH,YAAY;EACrB;EAEA,MAAMY,SAAS,GAAGC,KAAK,CAACC,OAAO,CAACZ,KAAK,CAAC;EACtC,MAAMa,MAAM,GAAGH,SAAS,GAAGf,iBAAiB,GAAGC,UAAU;EACzD,MAAMkB,YAAY,GAAGxB,SAAS;EAAE;EAChCuB,MAAM,EAAEH,SAAS,GAAG,CAACV,KAAK,CAAC,GAAGA,KAAK,EAAEa,MAAM,IAAI;IAC7CA,MAAM,CAACE,OAAO,CAACd,IAAI,CAAC;IACpB,IAAII,UAAU,EAAEA,UAAU,CAACQ,MAAM,CAAC;EACpC,CAAC,CAAC;EACF,MAAMG,OAAO,GAAGN,SAAS;EAAG;EAC5BI,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY;EAC9BE,OAAO,CAACC,OAAO,GAAGP,SAAS,GAAGnB,qBAAqB,GAAGC,gCAAgC;EACtFwB,OAAO,CAACZ,QAAQ,GAAG,CAACA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGM,SAAS,IAAIjB,YAAY,GAAGC,cAAc;EACpH,OAAOsB,OAAO;AAChB;AAEA,SAASjB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}