import { useLoader } from '@react-three/fiber';
import { CubeReflectionMapping, EquirectangularReflectionMapping, sRGBEncoding, LinearEncoding, CubeTextureLoader } from 'three';
import { RGBELoader } from 'three-stdlib';
import { presetsObj } from '../helpers/environment-assets.js';

const CUBEMAP_ROOT = 'https://market-assets.fra1.cdn.digitaloceanspaces.com/market-assets/hdris/';
function useEnvironment({
  files = ['/px.png', '/nx.png', '/py.png', '/ny.png', '/pz.png', '/nz.png'],
  path = '',
  preset = undefined,
  encoding = undefined,
  extensions
} = {}) {
  if (preset) {
    if (!(preset in presetsObj)) throw new Error('Preset must be one of: ' + Object.keys(presetsObj).join(', '));
    files = presetsObj[preset];
    path = CUBEMAP_ROOT;
  }

  const isCubeMap = Array.isArray(files);
  const loader = isCubeMap ? CubeTextureLoader : RGBELoader;
  const loaderResult = useLoader( // @ts-expect-error
  loader, isCubeMap ? [files] : files, loader => {
    loader.setPath(path);
    if (extensions) extensions(loader);
  });
  const texture = isCubeMap ? // @ts-ignore
  loaderResult[0] : loaderResult;
  texture.mapping = isCubeMap ? CubeReflectionMapping : EquirectangularReflectionMapping;
  texture.encoding = (encoding !== null && encoding !== void 0 ? encoding : isCubeMap) ? sRGBEncoding : LinearEncoding;
  return texture;
}

export { useEnvironment };
