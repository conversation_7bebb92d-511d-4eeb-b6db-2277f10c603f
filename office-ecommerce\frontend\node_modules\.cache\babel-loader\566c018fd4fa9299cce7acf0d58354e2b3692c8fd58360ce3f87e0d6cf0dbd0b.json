{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\admin\\\\components\\\\ThreeJSPreview.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useRef, useEffect, useState } from 'react';\nimport { Canvas, useFrame, useLoader } from '@react-three/fiber';\nimport { OrbitControls, Environment, ContactShadows } from '@react-three/drei';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { Suspense } from 'react';\nimport './ThreeJSPreview.css';\n\n// Loading component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"preview-loading\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loading-spinner\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Loading 3D model...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 10,\n  columnNumber: 3\n}, this);\n\n// Error component\n_c = LoadingSpinner;\nconst ErrorDisplay = ({\n  error\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"preview-error\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"error-icon\",\n    children: \"\\u26A0\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n    children: \"Failed to load 3D model\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n    children: error.message || 'Unknown error occurred'\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 18,\n  columnNumber: 3\n}, this);\n\n// Model component\n_c2 = ErrorDisplay;\nconst Model = ({\n  modelFile,\n  onLoad,\n  onError\n}) => {\n  _s();\n  const meshRef = useRef();\n  const [model, setModel] = useState(null);\n  useEffect(() => {\n    if (!modelFile) return;\n    const loader = new GLTFLoader();\n\n    // Create object URL from file\n    const url = URL.createObjectURL(modelFile);\n    loader.load(url, gltf => {\n      setModel(gltf);\n      onLoad && onLoad(gltf);\n\n      // Clean up object URL\n      URL.revokeObjectURL(url);\n    }, progress => {\n      // Handle loading progress if needed\n      console.log('Loading progress:', progress.loaded / progress.total * 100 + '%');\n    }, error => {\n      console.error('Error loading model:', error);\n      onError && onError(error);\n      URL.revokeObjectURL(url);\n    });\n    return () => {\n      URL.revokeObjectURL(url);\n    };\n  }, [modelFile, onLoad, onError]);\n  useFrame(state => {\n    if (meshRef.current) {\n      // Gentle rotation animation\n      meshRef.current.rotation.y += 0.005;\n    }\n  });\n  if (!model) return null;\n  return /*#__PURE__*/_jsxDEV(\"primitive\", {\n    ref: meshRef,\n    object: model.scene,\n    scale: 1,\n    position: [0, 0, 0]\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n\n// Camera controller\n_s(Model, \"5rAD25nDVg+FoJxnoxfeWxPoxP8=\", false, function () {\n  return [useFrame];\n});\n_c3 = Model;\nconst CameraController = () => {\n  _s2();\n  useFrame(state => {\n    // Smooth camera movement\n    state.camera.lookAt(0, 0, 0);\n  });\n  return null;\n};\n_s2(CameraController, \"xC67171NPRcCAzsbrenetil66NI=\", false, function () {\n  return [useFrame];\n});\n_c4 = CameraController;\nconst ThreeJSPreview = ({\n  modelFile,\n  className = ''\n}) => {\n  _s3();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [modelInfo, setModelInfo] = useState(null);\n  const handleModelLoad = gltf => {\n    setLoading(false);\n    setError(null);\n\n    // Extract model information\n    const scene = gltf.scene;\n    const animations = gltf.animations;\n\n    // Calculate bounding box\n    const box = new THREE.Box3().setFromObject(scene);\n    const size = box.getSize(new THREE.Vector3());\n    setModelInfo({\n      triangles: 0,\n      // Would need to traverse geometry to count\n      materials: scene.traverse(child => {\n        if (child.isMesh) return child.material;\n      }),\n      animations: animations.length,\n      size: {\n        width: size.x.toFixed(2),\n        height: size.y.toFixed(2),\n        depth: size.z.toFixed(2)\n      }\n    });\n  };\n  const handleModelError = error => {\n    setLoading(false);\n    setError(error);\n  };\n  if (!modelFile) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `threejs-preview ${className}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-placeholder\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"placeholder-icon\",\n          children: \"\\uD83C\\uDFAF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No 3D Model Selected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Upload a GLB or GLTF file to see the 3D preview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `threejs-preview ${className}`,\n      children: /*#__PURE__*/_jsxDEV(ErrorDisplay, {\n        error: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `threejs-preview ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"preview-container\",\n      children: [loading && /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Canvas, {\n        camera: {\n          position: [5, 5, 5],\n          fov: 50\n        },\n        style: {\n          background: '#f8f9fa'\n        },\n        children: /*#__PURE__*/_jsxDEV(Suspense, {\n          fallback: null,\n          children: [/*#__PURE__*/_jsxDEV(\"ambientLight\", {\n            intensity: 0.4\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"directionalLight\", {\n            position: [10, 10, 5],\n            intensity: 1,\n            castShadow: true,\n            \"shadow-mapSize-width\": 2048,\n            \"shadow-mapSize-height\": 2048\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"pointLight\", {\n            position: [-10, -10, -10],\n            intensity: 0.3\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Environment, {\n            preset: \"studio\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Model, {\n            modelFile: modelFile,\n            onLoad: handleModelLoad,\n            onError: handleModelError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ContactShadows, {\n            position: [0, -1, 0],\n            opacity: 0.4,\n            scale: 10,\n            blur: 2,\n            far: 4\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(OrbitControls, {\n            enablePan: true,\n            enableZoom: true,\n            enableRotate: true,\n            minDistance: 2,\n            maxDistance: 20,\n            autoRotate: false,\n            autoRotateSpeed: 0.5\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CameraController, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), modelInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"model-info-panel\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Model Information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"info-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"info-label\",\n            children: \"File:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"info-value\",\n            children: modelFile.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"info-label\",\n            children: \"Size:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"info-value\",\n            children: [(modelFile.size / 1024 / 1024).toFixed(2), \" MB\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"info-label\",\n            children: \"Dimensions:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"info-value\",\n            children: [modelInfo.size.width, \" \\xD7 \", modelInfo.size.height, \" \\xD7 \", modelInfo.size.depth]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"info-label\",\n            children: \"Animations:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"info-value\",\n            children: modelInfo.animations\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"preview-controls\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"control-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"control-label\",\n          children: \"Controls:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"control-hints\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDDB1\\uFE0F Drag to rotate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDD0D Scroll to zoom\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u2328\\uFE0F Right-click + drag to pan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 5\n  }, this);\n};\n_s3(ThreeJSPreview, \"iuUVogmQrFDrONtqXWJt7C5UxYE=\");\n_c5 = ThreeJSPreview;\nexport default ThreeJSPreview;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"LoadingSpinner\");\n$RefreshReg$(_c2, \"ErrorDisplay\");\n$RefreshReg$(_c3, \"Model\");\n$RefreshReg$(_c4, \"CameraController\");\n$RefreshReg$(_c5, \"ThreeJSPreview\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useState", "<PERSON><PERSON>", "useFrame", "useLoader", "OrbitControls", "Environment", "ContactShadows", "GLTFLoader", "Suspense", "jsxDEV", "_jsxDEV", "LoadingSpinner", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ErrorDisplay", "error", "message", "_c2", "Model", "modelFile", "onLoad", "onError", "_s", "meshRef", "model", "setModel", "loader", "url", "URL", "createObjectURL", "load", "gltf", "revokeObjectURL", "progress", "console", "log", "loaded", "total", "state", "current", "rotation", "y", "ref", "object", "scene", "scale", "position", "_c3", "CameraController", "_s2", "camera", "lookAt", "_c4", "ThreeJSPreview", "_s3", "loading", "setLoading", "setError", "modelInfo", "setModelInfo", "handleModelLoad", "animations", "box", "THREE", "Box3", "setFromObject", "size", "getSize", "Vector3", "triangles", "materials", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "length", "width", "x", "toFixed", "height", "depth", "z", "handleModelError", "fov", "style", "background", "fallback", "intensity", "<PERSON><PERSON><PERSON><PERSON>", "preset", "opacity", "blur", "far", "enablePan", "enableZoom", "enableRotate", "minDistance", "maxDistance", "autoRotate", "autoRotateSpeed", "name", "_c5", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/admin/components/ThreeJSPreview.js"], "sourcesContent": ["import React, { useRef, useEffect, useState } from 'react';\nimport { Canvas, useFrame, useLoader } from '@react-three/fiber';\nimport { OrbitControls, Environment, ContactShadows } from '@react-three/drei';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { Suspense } from 'react';\nimport './ThreeJSPreview.css';\n\n// Loading component\nconst LoadingSpinner = () => (\n  <div className=\"preview-loading\">\n    <div className=\"loading-spinner\"></div>\n    <p>Loading 3D model...</p>\n  </div>\n);\n\n// Error component\nconst ErrorDisplay = ({ error }) => (\n  <div className=\"preview-error\">\n    <div className=\"error-icon\">⚠️</div>\n    <h3>Failed to load 3D model</h3>\n    <p>{error.message || 'Unknown error occurred'}</p>\n  </div>\n);\n\n// Model component\nconst Model = ({ modelFile, onLoad, onError }) => {\n  const meshRef = useRef();\n  const [model, setModel] = useState(null);\n\n  useEffect(() => {\n    if (!modelFile) return;\n\n    const loader = new GLTFLoader();\n    \n    // Create object URL from file\n    const url = URL.createObjectURL(modelFile);\n    \n    loader.load(\n      url,\n      (gltf) => {\n        setModel(gltf);\n        onLoad && onLoad(gltf);\n        \n        // Clean up object URL\n        URL.revokeObjectURL(url);\n      },\n      (progress) => {\n        // Handle loading progress if needed\n        console.log('Loading progress:', (progress.loaded / progress.total) * 100 + '%');\n      },\n      (error) => {\n        console.error('Error loading model:', error);\n        onError && onError(error);\n        URL.revokeObjectURL(url);\n      }\n    );\n\n    return () => {\n      URL.revokeObjectURL(url);\n    };\n  }, [modelFile, onLoad, onError]);\n\n  useFrame((state) => {\n    if (meshRef.current) {\n      // Gentle rotation animation\n      meshRef.current.rotation.y += 0.005;\n    }\n  });\n\n  if (!model) return null;\n\n  return (\n    <primitive \n      ref={meshRef}\n      object={model.scene} \n      scale={1}\n      position={[0, 0, 0]}\n    />\n  );\n};\n\n// Camera controller\nconst CameraController = () => {\n  useFrame((state) => {\n    // Smooth camera movement\n    state.camera.lookAt(0, 0, 0);\n  });\n  return null;\n};\n\nconst ThreeJSPreview = ({ modelFile, className = '' }) => {\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [modelInfo, setModelInfo] = useState(null);\n\n  const handleModelLoad = (gltf) => {\n    setLoading(false);\n    setError(null);\n    \n    // Extract model information\n    const scene = gltf.scene;\n    const animations = gltf.animations;\n    \n    // Calculate bounding box\n    const box = new THREE.Box3().setFromObject(scene);\n    const size = box.getSize(new THREE.Vector3());\n    \n    setModelInfo({\n      triangles: 0, // Would need to traverse geometry to count\n      materials: scene.traverse((child) => {\n        if (child.isMesh) return child.material;\n      }),\n      animations: animations.length,\n      size: {\n        width: size.x.toFixed(2),\n        height: size.y.toFixed(2),\n        depth: size.z.toFixed(2)\n      }\n    });\n  };\n\n  const handleModelError = (error) => {\n    setLoading(false);\n    setError(error);\n  };\n\n  if (!modelFile) {\n    return (\n      <div className={`threejs-preview ${className}`}>\n        <div className=\"preview-placeholder\">\n          <div className=\"placeholder-icon\">🎯</div>\n          <h3>No 3D Model Selected</h3>\n          <p>Upload a GLB or GLTF file to see the 3D preview</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className={`threejs-preview ${className}`}>\n        <ErrorDisplay error={error} />\n      </div>\n    );\n  }\n\n  return (\n    <div className={`threejs-preview ${className}`}>\n      <div className=\"preview-container\">\n        {loading && <LoadingSpinner />}\n        \n        <Canvas\n          camera={{ position: [5, 5, 5], fov: 50 }}\n          style={{ background: '#f8f9fa' }}\n        >\n          <Suspense fallback={null}>\n            {/* Lighting */}\n            <ambientLight intensity={0.4} />\n            <directionalLight \n              position={[10, 10, 5]} \n              intensity={1}\n              castShadow\n              shadow-mapSize-width={2048}\n              shadow-mapSize-height={2048}\n            />\n            <pointLight position={[-10, -10, -10]} intensity={0.3} />\n            \n            {/* Environment */}\n            <Environment preset=\"studio\" />\n            \n            {/* Model */}\n            <Model \n              modelFile={modelFile}\n              onLoad={handleModelLoad}\n              onError={handleModelError}\n            />\n            \n            {/* Ground shadow */}\n            <ContactShadows \n              position={[0, -1, 0]} \n              opacity={0.4} \n              scale={10} \n              blur={2} \n              far={4} \n            />\n            \n            {/* Controls */}\n            <OrbitControls \n              enablePan={true}\n              enableZoom={true}\n              enableRotate={true}\n              minDistance={2}\n              maxDistance={20}\n              autoRotate={false}\n              autoRotateSpeed={0.5}\n            />\n            \n            {/* Camera controller */}\n            <CameraController />\n          </Suspense>\n        </Canvas>\n      </div>\n\n      {/* Model Information Panel */}\n      {modelInfo && (\n        <div className=\"model-info-panel\">\n          <h4>Model Information</h4>\n          <div className=\"info-grid\">\n            <div className=\"info-item\">\n              <span className=\"info-label\">File:</span>\n              <span className=\"info-value\">{modelFile.name}</span>\n            </div>\n            <div className=\"info-item\">\n              <span className=\"info-label\">Size:</span>\n              <span className=\"info-value\">{(modelFile.size / 1024 / 1024).toFixed(2)} MB</span>\n            </div>\n            <div className=\"info-item\">\n              <span className=\"info-label\">Dimensions:</span>\n              <span className=\"info-value\">\n                {modelInfo.size.width} × {modelInfo.size.height} × {modelInfo.size.depth}\n              </span>\n            </div>\n            <div className=\"info-item\">\n              <span className=\"info-label\">Animations:</span>\n              <span className=\"info-value\">{modelInfo.animations}</span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Controls Panel */}\n      <div className=\"preview-controls\">\n        <div className=\"control-group\">\n          <span className=\"control-label\">Controls:</span>\n          <div className=\"control-hints\">\n            <span>🖱️ Drag to rotate</span>\n            <span>🔍 Scroll to zoom</span>\n            <span>⌨️ Right-click + drag to pan</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ThreeJSPreview;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,oBAAoB;AAChE,SAASC,aAAa,EAAEC,WAAW,EAAEC,cAAc,QAAQ,mBAAmB;AAC9E,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAO,sBAAsB;;AAE7B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAGA,CAAA,kBACrBD,OAAA;EAAKE,SAAS,EAAC,iBAAiB;EAAAC,QAAA,gBAC9BH,OAAA;IAAKE,SAAS,EAAC;EAAiB;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC,eACvCP,OAAA;IAAAG,QAAA,EAAG;EAAmB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACvB,CACN;;AAED;AAAAC,EAAA,GAPMP,cAAc;AAQpB,MAAMQ,YAAY,GAAGA,CAAC;EAAEC;AAAM,CAAC,kBAC7BV,OAAA;EAAKE,SAAS,EAAC,eAAe;EAAAC,QAAA,gBAC5BH,OAAA;IAAKE,SAAS,EAAC,YAAY;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC,eACpCP,OAAA;IAAAG,QAAA,EAAI;EAAuB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC,eAChCP,OAAA;IAAAG,QAAA,EAAIO,KAAK,CAACC,OAAO,IAAI;EAAwB;IAAAP,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC/C,CACN;;AAED;AAAAK,GAAA,GARMH,YAAY;AASlB,MAAMI,KAAK,GAAGA,CAAC;EAAEC,SAAS;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAMC,OAAO,GAAG9B,MAAM,CAAC,CAAC;EACxB,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAExCD,SAAS,CAAC,MAAM;IACd,IAAI,CAACyB,SAAS,EAAE;IAEhB,MAAMO,MAAM,GAAG,IAAIxB,UAAU,CAAC,CAAC;;IAE/B;IACA,MAAMyB,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACV,SAAS,CAAC;IAE1CO,MAAM,CAACI,IAAI,CACTH,GAAG,EACFI,IAAI,IAAK;MACRN,QAAQ,CAACM,IAAI,CAAC;MACdX,MAAM,IAAIA,MAAM,CAACW,IAAI,CAAC;;MAEtB;MACAH,GAAG,CAACI,eAAe,CAACL,GAAG,CAAC;IAC1B,CAAC,EACAM,QAAQ,IAAK;MACZ;MACAC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAGF,QAAQ,CAACG,MAAM,GAAGH,QAAQ,CAACI,KAAK,GAAI,GAAG,GAAG,GAAG,CAAC;IAClF,CAAC,EACAtB,KAAK,IAAK;MACTmB,OAAO,CAACnB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CM,OAAO,IAAIA,OAAO,CAACN,KAAK,CAAC;MACzBa,GAAG,CAACI,eAAe,CAACL,GAAG,CAAC;IAC1B,CACF,CAAC;IAED,OAAO,MAAM;MACXC,GAAG,CAACI,eAAe,CAACL,GAAG,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAACR,SAAS,EAAEC,MAAM,EAAEC,OAAO,CAAC,CAAC;EAEhCxB,QAAQ,CAAEyC,KAAK,IAAK;IAClB,IAAIf,OAAO,CAACgB,OAAO,EAAE;MACnB;MACAhB,OAAO,CAACgB,OAAO,CAACC,QAAQ,CAACC,CAAC,IAAI,KAAK;IACrC;EACF,CAAC,CAAC;EAEF,IAAI,CAACjB,KAAK,EAAE,OAAO,IAAI;EAEvB,oBACEnB,OAAA;IACEqC,GAAG,EAAEnB,OAAQ;IACboB,MAAM,EAAEnB,KAAK,CAACoB,KAAM;IACpBC,KAAK,EAAE,CAAE;IACTC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAAE;IAAArC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrB,CAAC;AAEN,CAAC;;AAED;AAAAU,EAAA,CAxDMJ,KAAK;EAAA,QAqCTrB,QAAQ;AAAA;AAAAkD,GAAA,GArCJ7B,KAAK;AAyDX,MAAM8B,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC7BpD,QAAQ,CAAEyC,KAAK,IAAK;IAClB;IACAA,KAAK,CAACY,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9B,CAAC,CAAC;EACF,OAAO,IAAI;AACb,CAAC;AAACF,GAAA,CANID,gBAAgB;EAAA,QACpBnD,QAAQ;AAAA;AAAAuD,GAAA,GADJJ,gBAAgB;AAQtB,MAAMK,cAAc,GAAGA,CAAC;EAAElC,SAAS;EAAEZ,SAAS,GAAG;AAAG,CAAC,KAAK;EAAA+C,GAAA;EACxD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAE0C,QAAQ,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC+D,SAAS,EAAEC,YAAY,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EAEhD,MAAMiE,eAAe,GAAI7B,IAAI,IAAK;IAChCyB,UAAU,CAAC,KAAK,CAAC;IACjBC,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAMb,KAAK,GAAGb,IAAI,CAACa,KAAK;IACxB,MAAMiB,UAAU,GAAG9B,IAAI,CAAC8B,UAAU;;IAElC;IACA,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAACC,IAAI,CAAC,CAAC,CAACC,aAAa,CAACrB,KAAK,CAAC;IACjD,MAAMsB,IAAI,GAAGJ,GAAG,CAACK,OAAO,CAAC,IAAIJ,KAAK,CAACK,OAAO,CAAC,CAAC,CAAC;IAE7CT,YAAY,CAAC;MACXU,SAAS,EAAE,CAAC;MAAE;MACdC,SAAS,EAAE1B,KAAK,CAAC2B,QAAQ,CAAEC,KAAK,IAAK;QACnC,IAAIA,KAAK,CAACC,MAAM,EAAE,OAAOD,KAAK,CAACE,QAAQ;MACzC,CAAC,CAAC;MACFb,UAAU,EAAEA,UAAU,CAACc,MAAM;MAC7BT,IAAI,EAAE;QACJU,KAAK,EAAEV,IAAI,CAACW,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;QACxBC,MAAM,EAAEb,IAAI,CAACzB,CAAC,CAACqC,OAAO,CAAC,CAAC,CAAC;QACzBE,KAAK,EAAEd,IAAI,CAACe,CAAC,CAACH,OAAO,CAAC,CAAC;MACzB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,gBAAgB,GAAInE,KAAK,IAAK;IAClCyC,UAAU,CAAC,KAAK,CAAC;IACjBC,QAAQ,CAAC1C,KAAK,CAAC;EACjB,CAAC;EAED,IAAI,CAACI,SAAS,EAAE;IACd,oBACEd,OAAA;MAAKE,SAAS,EAAE,mBAAmBA,SAAS,EAAG;MAAAC,QAAA,eAC7CH,OAAA;QAAKE,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClCH,OAAA;UAAKE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1CP,OAAA;UAAAG,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BP,OAAA;UAAAG,QAAA,EAAG;QAA+C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIG,KAAK,EAAE;IACT,oBACEV,OAAA;MAAKE,SAAS,EAAE,mBAAmBA,SAAS,EAAG;MAAAC,QAAA,eAC7CH,OAAA,CAACS,YAAY;QAACC,KAAK,EAAEA;MAAM;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,oBACEP,OAAA;IAAKE,SAAS,EAAE,mBAAmBA,SAAS,EAAG;IAAAC,QAAA,gBAC7CH,OAAA;MAAKE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,GAC/B+C,OAAO,iBAAIlD,OAAA,CAACC,cAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE9BP,OAAA,CAACT,MAAM;QACLsD,MAAM,EAAE;UAAEJ,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAAEqC,GAAG,EAAE;QAAG,CAAE;QACzCC,KAAK,EAAE;UAAEC,UAAU,EAAE;QAAU,CAAE;QAAA7E,QAAA,eAEjCH,OAAA,CAACF,QAAQ;UAACmF,QAAQ,EAAE,IAAK;UAAA9E,QAAA,gBAEvBH,OAAA;YAAckF,SAAS,EAAE;UAAI;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChCP,OAAA;YACEyC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAE;YACtByC,SAAS,EAAE,CAAE;YACbC,UAAU;YACV,wBAAsB,IAAK;YAC3B,yBAAuB;UAAK;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACFP,OAAA;YAAYyC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAE;YAACyC,SAAS,EAAE;UAAI;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGzDP,OAAA,CAACL,WAAW;YAACyF,MAAM,EAAC;UAAQ;YAAAhF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG/BP,OAAA,CAACa,KAAK;YACJC,SAAS,EAAEA,SAAU;YACrBC,MAAM,EAAEwC,eAAgB;YACxBvC,OAAO,EAAE6D;UAAiB;YAAAzE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGFP,OAAA,CAACJ,cAAc;YACb6C,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;YACrB4C,OAAO,EAAE,GAAI;YACb7C,KAAK,EAAE,EAAG;YACV8C,IAAI,EAAE,CAAE;YACRC,GAAG,EAAE;UAAE;YAAAnF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGFP,OAAA,CAACN,aAAa;YACZ8F,SAAS,EAAE,IAAK;YAChBC,UAAU,EAAE,IAAK;YACjBC,YAAY,EAAE,IAAK;YACnBC,WAAW,EAAE,CAAE;YACfC,WAAW,EAAE,EAAG;YAChBC,UAAU,EAAE,KAAM;YAClBC,eAAe,EAAE;UAAI;YAAA1F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAGFP,OAAA,CAAC2C,gBAAgB;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL8C,SAAS,iBACRrD,OAAA;MAAKE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BH,OAAA;QAAAG,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BP,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBH,OAAA;YAAME,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzCP,OAAA;YAAME,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEW,SAAS,CAACiF;UAAI;YAAA3F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBH,OAAA;YAAME,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzCP,OAAA;YAAME,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAE,CAACW,SAAS,CAAC+C,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEY,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;UAAA;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBH,OAAA;YAAME,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/CP,OAAA;YAAME,SAAS,EAAC,YAAY;YAAAC,QAAA,GACzBkD,SAAS,CAACQ,IAAI,CAACU,KAAK,EAAC,QAAG,EAAClB,SAAS,CAACQ,IAAI,CAACa,MAAM,EAAC,QAAG,EAACrB,SAAS,CAACQ,IAAI,CAACc,KAAK;UAAA;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNP,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBH,OAAA;YAAME,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/CP,OAAA;YAAME,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEkD,SAAS,CAACG;UAAU;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDP,OAAA;MAAKE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BH,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BH,OAAA;UAAME,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChDP,OAAA;UAAKE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BH,OAAA;YAAAG,QAAA,EAAM;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BP,OAAA;YAAAG,QAAA,EAAM;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9BP,OAAA;YAAAG,QAAA,EAAM;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC0C,GAAA,CAzJID,cAAc;AAAAgD,GAAA,GAAdhD,cAAc;AA2JpB,eAAeA,cAAc;AAAC,IAAAxC,EAAA,EAAAI,GAAA,EAAA8B,GAAA,EAAAK,GAAA,EAAAiD,GAAA;AAAAC,YAAA,CAAAzF,EAAA;AAAAyF,YAAA,CAAArF,GAAA;AAAAqF,YAAA,CAAAvD,GAAA;AAAAuD,YAAA,CAAAlD,GAAA;AAAAkD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}