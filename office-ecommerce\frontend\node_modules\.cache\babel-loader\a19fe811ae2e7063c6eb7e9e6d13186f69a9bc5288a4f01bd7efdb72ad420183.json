{"ast": null, "code": "import { G<PERSON><PERSON>oader, DRACOLoader, MeshoptDecoder } from 'three-stdlib';\nimport { useLoader } from '@react-three/fiber';\n\n// @ts-ignore\nlet dracoLoader = null;\nfunction extensions(useDraco, useMeshopt, extendLoader) {\n  return loader => {\n    if (extendLoader) {\n      extendLoader(loader);\n    }\n    if (useDraco) {\n      if (!dracoLoader) {\n        dracoLoader = new DRACOLoader();\n      }\n      dracoLoader.setDecoderPath(typeof useDraco === 'string' ? useDraco : 'https://www.gstatic.com/draco/versioned/decoders/1.5.5/');\n      loader.setDRACOLoader(dracoLoader);\n    }\n    if (useMeshopt) {\n      loader.setMeshoptDecoder(typeof MeshoptDecoder === 'function' ? MeshoptDecoder() : MeshoptDecoder);\n    }\n  };\n}\nfunction useGLTF(path, useDraco = true, useMeshOpt = true, extendLoader) {\n  const gltf = useLoader(GLTFLoader, path, extensions(useDraco, useMeshOpt, extendLoader));\n  return gltf;\n}\nuseGLTF.preload = (path, useDraco = true, useMeshOpt = true, extendLoader) => useLoader.preload(GLTFLoader, path, extensions(useDraco, useMeshOpt, extendLoader));\nuseGLTF.clear = input => useLoader.clear(GLTFLoader, input);\nexport { useGLTF };", "map": {"version": 3, "names": ["GLTFLoader", "DRACOLoader", "MeshoptDecoder", "useLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "extensions", "useDraco", "useMeshopt", "<PERSON><PERSON><PERSON><PERSON>", "loader", "setDecoderPath", "setDRACOLoader", "setMeshoptDecoder", "useGLTF", "path", "useMeshOpt", "gltf", "preload", "clear", "input"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/useGLTF.js"], "sourcesContent": ["import { G<PERSON><PERSON>oader, DRACOLoader, MeshoptDecoder } from 'three-stdlib';\nimport { useLoader } from '@react-three/fiber';\n\n// @ts-ignore\nlet dracoLoader = null;\n\nfunction extensions(useDraco, useMeshopt, extendLoader) {\n  return loader => {\n    if (extendLoader) {\n      extendLoader(loader);\n    }\n\n    if (useDraco) {\n      if (!dracoLoader) {\n        dracoLoader = new DRACOLoader();\n      }\n\n      dracoLoader.setDecoderPath(typeof useDraco === 'string' ? useDraco : 'https://www.gstatic.com/draco/versioned/decoders/1.5.5/');\n      loader.setDRACOLoader(dracoLoader);\n    }\n\n    if (useMeshopt) {\n      loader.setMeshoptDecoder(typeof MeshoptDecoder === 'function' ? MeshoptDecoder() : MeshoptDecoder);\n    }\n  };\n}\n\nfunction useGLTF(path, useDraco = true, useMeshOpt = true, extendLoader) {\n  const gltf = useLoader(GLTFLoader, path, extensions(useDraco, useMeshOpt, extendLoader));\n  return gltf;\n}\n\nuseGLTF.preload = (path, useDraco = true, useMeshOpt = true, extendLoader) => useLoader.preload(GLTFLoader, path, extensions(useDraco, useMeshOpt, extendLoader));\n\nuseGLTF.clear = input => useLoader.clear(GLTFLoader, input);\n\nexport { useGLTF };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,WAAW,EAAEC,cAAc,QAAQ,cAAc;AACtE,SAASC,SAAS,QAAQ,oBAAoB;;AAE9C;AACA,IAAIC,WAAW,GAAG,IAAI;AAEtB,SAASC,UAAUA,CAACC,QAAQ,EAAEC,UAAU,EAAEC,YAAY,EAAE;EACtD,OAAOC,MAAM,IAAI;IACf,IAAID,YAAY,EAAE;MAChBA,YAAY,CAACC,MAAM,CAAC;IACtB;IAEA,IAAIH,QAAQ,EAAE;MACZ,IAAI,CAACF,WAAW,EAAE;QAChBA,WAAW,GAAG,IAAIH,WAAW,CAAC,CAAC;MACjC;MAEAG,WAAW,CAACM,cAAc,CAAC,OAAOJ,QAAQ,KAAK,QAAQ,GAAGA,QAAQ,GAAG,yDAAyD,CAAC;MAC/HG,MAAM,CAACE,cAAc,CAACP,WAAW,CAAC;IACpC;IAEA,IAAIG,UAAU,EAAE;MACdE,MAAM,CAACG,iBAAiB,CAAC,OAAOV,cAAc,KAAK,UAAU,GAAGA,cAAc,CAAC,CAAC,GAAGA,cAAc,CAAC;IACpG;EACF,CAAC;AACH;AAEA,SAASW,OAAOA,CAACC,IAAI,EAAER,QAAQ,GAAG,IAAI,EAAES,UAAU,GAAG,IAAI,EAAEP,YAAY,EAAE;EACvE,MAAMQ,IAAI,GAAGb,SAAS,CAACH,UAAU,EAAEc,IAAI,EAAET,UAAU,CAACC,QAAQ,EAAES,UAAU,EAAEP,YAAY,CAAC,CAAC;EACxF,OAAOQ,IAAI;AACb;AAEAH,OAAO,CAACI,OAAO,GAAG,CAACH,IAAI,EAAER,QAAQ,GAAG,IAAI,EAAES,UAAU,GAAG,IAAI,EAAEP,YAAY,KAAKL,SAAS,CAACc,OAAO,CAACjB,UAAU,EAAEc,IAAI,EAAET,UAAU,CAACC,QAAQ,EAAES,UAAU,EAAEP,YAAY,CAAC,CAAC;AAEjKK,OAAO,CAACK,KAAK,GAAGC,KAAK,IAAIhB,SAAS,CAACe,KAAK,CAAClB,UAAU,EAAEmB,KAAK,CAAC;AAE3D,SAASN,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}