{"ast": null, "code": "import * as React from 'react';\nimport * as THREE from 'three';\nimport { createPortal } from '@react-three/fiber';\nimport { Flow } from 'three-stdlib';\nconst CurveModifier = /*#__PURE__*/React.forwardRef(({\n  children,\n  curve\n}, ref) => {\n  const [scene] = React.useState(() => new THREE.Scene());\n  const [obj, set] = React.useState();\n  const modifier = React.useRef();\n  React.useEffect(() => {\n    modifier.current = new Flow(scene.children[0]);\n    set(modifier.current.object3D);\n  }, [children]);\n  React.useEffect(() => {\n    var _modifier$current;\n    if (curve) (_modifier$current = modifier.current) == null ? void 0 : _modifier$current.updateCurve(0, curve);\n  }, [curve]);\n  React.useImperativeHandle(ref, () => ({\n    moveAlongCurve: val => {\n      var _modifier$current2;\n      (_modifier$current2 = modifier.current) == null ? void 0 : _modifier$current2.moveAlongCurve(val);\n    }\n  }));\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal(children, scene), obj && /*#__PURE__*/React.createElement(\"primitive\", {\n    object: obj\n  }));\n});\nexport { CurveModifier };", "map": {"version": 3, "names": ["React", "THREE", "createPortal", "Flow", "CurveModifier", "forwardRef", "children", "curve", "ref", "scene", "useState", "Scene", "obj", "set", "modifier", "useRef", "useEffect", "current", "object3D", "_modifier$current", "updateCurve", "useImperativeHandle", "moveAlongCurve", "val", "_modifier$current2", "createElement", "Fragment", "object"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/CurveModifier.js"], "sourcesContent": ["import * as React from 'react';\nimport * as THREE from 'three';\nimport { createPortal } from '@react-three/fiber';\nimport { Flow } from 'three-stdlib';\n\nconst CurveModifier = /*#__PURE__*/React.forwardRef(({\n  children,\n  curve\n}, ref) => {\n  const [scene] = React.useState(() => new THREE.Scene());\n  const [obj, set] = React.useState();\n  const modifier = React.useRef();\n  React.useEffect(() => {\n    modifier.current = new Flow(scene.children[0]);\n    set(modifier.current.object3D);\n  }, [children]);\n  React.useEffect(() => {\n    var _modifier$current;\n\n    if (curve) (_modifier$current = modifier.current) == null ? void 0 : _modifier$current.updateCurve(0, curve);\n  }, [curve]);\n  React.useImperativeHandle(ref, () => ({\n    moveAlongCurve: val => {\n      var _modifier$current2;\n\n      (_modifier$current2 = modifier.current) == null ? void 0 : _modifier$current2.moveAlongCurve(val);\n    }\n  }));\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal(children, scene), obj && /*#__PURE__*/React.createElement(\"primitive\", {\n    object: obj\n  }));\n});\n\nexport { CurveModifier };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,IAAI,QAAQ,cAAc;AAEnC,MAAMC,aAAa,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAC;EACnDC,QAAQ;EACRC;AACF,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM,CAACC,KAAK,CAAC,GAAGT,KAAK,CAACU,QAAQ,CAAC,MAAM,IAAIT,KAAK,CAACU,KAAK,CAAC,CAAC,CAAC;EACvD,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC,GAAGb,KAAK,CAACU,QAAQ,CAAC,CAAC;EACnC,MAAMI,QAAQ,GAAGd,KAAK,CAACe,MAAM,CAAC,CAAC;EAC/Bf,KAAK,CAACgB,SAAS,CAAC,MAAM;IACpBF,QAAQ,CAACG,OAAO,GAAG,IAAId,IAAI,CAACM,KAAK,CAACH,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC9CO,GAAG,CAACC,QAAQ,CAACG,OAAO,CAACC,QAAQ,CAAC;EAChC,CAAC,EAAE,CAACZ,QAAQ,CAAC,CAAC;EACdN,KAAK,CAACgB,SAAS,CAAC,MAAM;IACpB,IAAIG,iBAAiB;IAErB,IAAIZ,KAAK,EAAE,CAACY,iBAAiB,GAAGL,QAAQ,CAACG,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,iBAAiB,CAACC,WAAW,CAAC,CAAC,EAAEb,KAAK,CAAC;EAC9G,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACXP,KAAK,CAACqB,mBAAmB,CAACb,GAAG,EAAE,OAAO;IACpCc,cAAc,EAAEC,GAAG,IAAI;MACrB,IAAIC,kBAAkB;MAEtB,CAACA,kBAAkB,GAAGV,QAAQ,CAACG,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGO,kBAAkB,CAACF,cAAc,CAACC,GAAG,CAAC;IACnG;EACF,CAAC,CAAC,CAAC;EACH,OAAO,aAAavB,KAAK,CAACyB,aAAa,CAACzB,KAAK,CAAC0B,QAAQ,EAAE,IAAI,EAAExB,YAAY,CAACI,QAAQ,EAAEG,KAAK,CAAC,EAAEG,GAAG,IAAI,aAAaZ,KAAK,CAACyB,aAAa,CAAC,WAAW,EAAE;IAChJE,MAAM,EAAEf;EACV,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,SAASR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}