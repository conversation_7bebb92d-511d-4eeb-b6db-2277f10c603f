{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { WebGLRenderTarget, HalfFloatType, RGBAFormat, UnsignedByteType } from 'three';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { EffectComposer, RenderPass, ShaderPass, GammaCorrectionShader } from 'three-stdlib';\nimport mergeRefs from 'react-merge-refs';\nconst isWebGL2Available = () => {\n  try {\n    var canvas = document.createElement('canvas');\n    return !!(window.WebGL2RenderingContext && canvas.getContext('webgl2'));\n  } catch (e) {\n    return false;\n  }\n};\nconst Effects = /*#__PURE__*/React.forwardRef(({\n  children,\n  multisamping = 8,\n  renderIndex = 1,\n  disableRender,\n  disableGamma,\n  disableRenderPass,\n  depthBuffer = true,\n  stencilBuffer = false,\n  anisotropy = 1,\n  encoding,\n  type,\n  ...props\n}, ref) => {\n  React.useMemo(() => extend({\n    EffectComposer,\n    RenderPass,\n    ShaderPass\n  }), []);\n  const composer = React.useRef();\n  const {\n    scene,\n    camera,\n    gl,\n    size,\n    viewport\n  } = useThree();\n  const [target] = React.useState(() => {\n    const t = new WebGLRenderTarget(size.width, size.height, {\n      type: type || HalfFloatType,\n      format: RGBAFormat,\n      depthBuffer,\n      stencilBuffer,\n      anisotropy\n    }); // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n\n    if (type === UnsignedByteType && encoding != null) {\n      if ('colorSpace' in t) t.texture.colorSpace = encoding;else t.texture.encoding = encoding;\n    }\n    t.samples = multisamping;\n    return t;\n  });\n  React.useEffect(() => {\n    var _composer$current, _composer$current2;\n    (_composer$current = composer.current) == null ? void 0 : _composer$current.setSize(size.width, size.height);\n    (_composer$current2 = composer.current) == null ? void 0 : _composer$current2.setPixelRatio(viewport.dpr);\n  }, [gl, size, viewport.dpr]);\n  useFrame(() => {\n    var _composer$current3;\n    if (!disableRender) (_composer$current3 = composer.current) == null ? void 0 : _composer$current3.render();\n  }, renderIndex);\n  const passes = [];\n  if (!disableRenderPass) passes.push(/*#__PURE__*/React.createElement(\"renderPass\", {\n    key: \"renderpass\",\n    attach: `passes-${passes.length}`,\n    args: [scene, camera]\n  }));\n  if (!disableGamma) passes.push(/*#__PURE__*/React.createElement(\"shaderPass\", {\n    attach: `passes-${passes.length}`,\n    key: \"gammapass\",\n    args: [GammaCorrectionShader]\n  }));\n  React.Children.forEach(children, el => {\n    el && passes.push(/*#__PURE__*/React.cloneElement(el, {\n      key: passes.length,\n      attach: `passes-${passes.length}`\n    }));\n  });\n  return /*#__PURE__*/React.createElement(\"effectComposer\", _extends({\n    ref: mergeRefs([ref, composer]),\n    args: [gl, target]\n  }, props), passes);\n});\nexport { Effects, isWebGL2Available };", "map": {"version": 3, "names": ["_extends", "React", "WebGLRenderTarget", "HalfFloatType", "RGBAFormat", "UnsignedByteType", "extend", "useThree", "useFrame", "EffectComposer", "RenderPass", "<PERSON><PERSON><PERSON><PERSON>", "GammaCorrectionShader", "mergeRefs", "isWebGL2Available", "canvas", "document", "createElement", "window", "WebGL2RenderingContext", "getContext", "e", "Effects", "forwardRef", "children", "multisamping", "renderIndex", "disableRender", "disable<PERSON><PERSON><PERSON>", "disable<PERSON><PERSON><PERSON><PERSON>", "depthBuffer", "stencil<PERSON>uffer", "anisotropy", "encoding", "type", "props", "ref", "useMemo", "composer", "useRef", "scene", "camera", "gl", "size", "viewport", "target", "useState", "t", "width", "height", "format", "texture", "colorSpace", "samples", "useEffect", "_composer$current", "_composer$current2", "current", "setSize", "setPixelRatio", "dpr", "_composer$current3", "render", "passes", "push", "key", "attach", "length", "args", "Children", "for<PERSON>ach", "el", "cloneElement"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Effects.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { WebGLRenderTarget, HalfFloatType, RGBAFormat, UnsignedByteType } from 'three';\nimport { extend, useThree, useFrame } from '@react-three/fiber';\nimport { EffectComposer, RenderPass, ShaderPass, GammaCorrectionShader } from 'three-stdlib';\nimport mergeRefs from 'react-merge-refs';\n\nconst isWebGL2Available = () => {\n  try {\n    var canvas = document.createElement('canvas');\n    return !!(window.WebGL2RenderingContext && canvas.getContext('webgl2'));\n  } catch (e) {\n    return false;\n  }\n};\nconst Effects = /*#__PURE__*/React.forwardRef(({\n  children,\n  multisamping = 8,\n  renderIndex = 1,\n  disableRender,\n  disableGamma,\n  disableRenderPass,\n  depthBuffer = true,\n  stencilBuffer = false,\n  anisotropy = 1,\n  encoding,\n  type,\n  ...props\n}, ref) => {\n  React.useMemo(() => extend({\n    EffectComposer,\n    RenderPass,\n    ShaderPass\n  }), []);\n  const composer = React.useRef();\n  const {\n    scene,\n    camera,\n    gl,\n    size,\n    viewport\n  } = useThree();\n  const [target] = React.useState(() => {\n    const t = new WebGLRenderTarget(size.width, size.height, {\n      type: type || HalfFloatType,\n      format: RGBAFormat,\n      depthBuffer,\n      stencilBuffer,\n      anisotropy\n    }); // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n\n    if (type === UnsignedByteType && encoding != null) {\n      if ('colorSpace' in t) t.texture.colorSpace = encoding;else t.texture.encoding = encoding;\n    }\n\n    t.samples = multisamping;\n    return t;\n  });\n  React.useEffect(() => {\n    var _composer$current, _composer$current2;\n\n    (_composer$current = composer.current) == null ? void 0 : _composer$current.setSize(size.width, size.height);\n    (_composer$current2 = composer.current) == null ? void 0 : _composer$current2.setPixelRatio(viewport.dpr);\n  }, [gl, size, viewport.dpr]);\n  useFrame(() => {\n    var _composer$current3;\n\n    if (!disableRender) (_composer$current3 = composer.current) == null ? void 0 : _composer$current3.render();\n  }, renderIndex);\n  const passes = [];\n  if (!disableRenderPass) passes.push( /*#__PURE__*/React.createElement(\"renderPass\", {\n    key: \"renderpass\",\n    attach: `passes-${passes.length}`,\n    args: [scene, camera]\n  }));\n  if (!disableGamma) passes.push( /*#__PURE__*/React.createElement(\"shaderPass\", {\n    attach: `passes-${passes.length}`,\n    key: \"gammapass\",\n    args: [GammaCorrectionShader]\n  }));\n  React.Children.forEach(children, el => {\n    el && passes.push( /*#__PURE__*/React.cloneElement(el, {\n      key: passes.length,\n      attach: `passes-${passes.length}`\n    }));\n  });\n  return /*#__PURE__*/React.createElement(\"effectComposer\", _extends({\n    ref: mergeRefs([ref, composer]),\n    args: [gl, target]\n  }, props), passes);\n});\n\nexport { Effects, isWebGL2Available };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,EAAEC,aAAa,EAAEC,UAAU,EAAEC,gBAAgB,QAAQ,OAAO;AACtF,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AAC/D,SAASC,cAAc,EAAEC,UAAU,EAAEC,UAAU,EAAEC,qBAAqB,QAAQ,cAAc;AAC5F,OAAOC,SAAS,MAAM,kBAAkB;AAExC,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,IAAI;IACF,IAAIC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC7C,OAAO,CAAC,EAAEC,MAAM,CAACC,sBAAsB,IAAIJ,MAAM,CAACK,UAAU,CAAC,QAAQ,CAAC,CAAC;EACzE,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF,CAAC;AACD,MAAMC,OAAO,GAAG,aAAarB,KAAK,CAACsB,UAAU,CAAC,CAAC;EAC7CC,QAAQ;EACRC,YAAY,GAAG,CAAC;EAChBC,WAAW,GAAG,CAAC;EACfC,aAAa;EACbC,YAAY;EACZC,iBAAiB;EACjBC,WAAW,GAAG,IAAI;EAClBC,aAAa,GAAG,KAAK;EACrBC,UAAU,GAAG,CAAC;EACdC,QAAQ;EACRC,IAAI;EACJ,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTnC,KAAK,CAACoC,OAAO,CAAC,MAAM/B,MAAM,CAAC;IACzBG,cAAc;IACdC,UAAU;IACVC;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,MAAM2B,QAAQ,GAAGrC,KAAK,CAACsC,MAAM,CAAC,CAAC;EAC/B,MAAM;IACJC,KAAK;IACLC,MAAM;IACNC,EAAE;IACFC,IAAI;IACJC;EACF,CAAC,GAAGrC,QAAQ,CAAC,CAAC;EACd,MAAM,CAACsC,MAAM,CAAC,GAAG5C,KAAK,CAAC6C,QAAQ,CAAC,MAAM;IACpC,MAAMC,CAAC,GAAG,IAAI7C,iBAAiB,CAACyC,IAAI,CAACK,KAAK,EAAEL,IAAI,CAACM,MAAM,EAAE;MACvDf,IAAI,EAAEA,IAAI,IAAI/B,aAAa;MAC3B+C,MAAM,EAAE9C,UAAU;MAClB0B,WAAW;MACXC,aAAa;MACbC;IACF,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAIE,IAAI,KAAK7B,gBAAgB,IAAI4B,QAAQ,IAAI,IAAI,EAAE;MACjD,IAAI,YAAY,IAAIc,CAAC,EAAEA,CAAC,CAACI,OAAO,CAACC,UAAU,GAAGnB,QAAQ,CAAC,KAAKc,CAAC,CAACI,OAAO,CAAClB,QAAQ,GAAGA,QAAQ;IAC3F;IAEAc,CAAC,CAACM,OAAO,GAAG5B,YAAY;IACxB,OAAOsB,CAAC;EACV,CAAC,CAAC;EACF9C,KAAK,CAACqD,SAAS,CAAC,MAAM;IACpB,IAAIC,iBAAiB,EAAEC,kBAAkB;IAEzC,CAACD,iBAAiB,GAAGjB,QAAQ,CAACmB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,iBAAiB,CAACG,OAAO,CAACf,IAAI,CAACK,KAAK,EAAEL,IAAI,CAACM,MAAM,CAAC;IAC5G,CAACO,kBAAkB,GAAGlB,QAAQ,CAACmB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,kBAAkB,CAACG,aAAa,CAACf,QAAQ,CAACgB,GAAG,CAAC;EAC3G,CAAC,EAAE,CAAClB,EAAE,EAAEC,IAAI,EAAEC,QAAQ,CAACgB,GAAG,CAAC,CAAC;EAC5BpD,QAAQ,CAAC,MAAM;IACb,IAAIqD,kBAAkB;IAEtB,IAAI,CAAClC,aAAa,EAAE,CAACkC,kBAAkB,GAAGvB,QAAQ,CAACmB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,kBAAkB,CAACC,MAAM,CAAC,CAAC;EAC5G,CAAC,EAAEpC,WAAW,CAAC;EACf,MAAMqC,MAAM,GAAG,EAAE;EACjB,IAAI,CAAClC,iBAAiB,EAAEkC,MAAM,CAACC,IAAI,CAAE,aAAa/D,KAAK,CAACgB,aAAa,CAAC,YAAY,EAAE;IAClFgD,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE,UAAUH,MAAM,CAACI,MAAM,EAAE;IACjCC,IAAI,EAAE,CAAC5B,KAAK,EAAEC,MAAM;EACtB,CAAC,CAAC,CAAC;EACH,IAAI,CAACb,YAAY,EAAEmC,MAAM,CAACC,IAAI,CAAE,aAAa/D,KAAK,CAACgB,aAAa,CAAC,YAAY,EAAE;IAC7EiD,MAAM,EAAE,UAAUH,MAAM,CAACI,MAAM,EAAE;IACjCF,GAAG,EAAE,WAAW;IAChBG,IAAI,EAAE,CAACxD,qBAAqB;EAC9B,CAAC,CAAC,CAAC;EACHX,KAAK,CAACoE,QAAQ,CAACC,OAAO,CAAC9C,QAAQ,EAAE+C,EAAE,IAAI;IACrCA,EAAE,IAAIR,MAAM,CAACC,IAAI,CAAE,aAAa/D,KAAK,CAACuE,YAAY,CAACD,EAAE,EAAE;MACrDN,GAAG,EAAEF,MAAM,CAACI,MAAM;MAClBD,MAAM,EAAE,UAAUH,MAAM,CAACI,MAAM;IACjC,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,OAAO,aAAalE,KAAK,CAACgB,aAAa,CAAC,gBAAgB,EAAEjB,QAAQ,CAAC;IACjEoC,GAAG,EAAEvB,SAAS,CAAC,CAACuB,GAAG,EAAEE,QAAQ,CAAC,CAAC;IAC/B8B,IAAI,EAAE,CAAC1B,EAAE,EAAEG,MAAM;EACnB,CAAC,EAAEV,KAAK,CAAC,EAAE4B,MAAM,CAAC;AACpB,CAAC,CAAC;AAEF,SAASzC,OAAO,EAAER,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}