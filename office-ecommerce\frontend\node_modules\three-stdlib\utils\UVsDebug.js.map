{"version": 3, "file": "UVsDebug.js", "sources": ["../../src/utils/UVsDebug.js"], "sourcesContent": ["import { Vector2 } from 'three'\n\n/**\n * tool for \"unwrapping\" and debugging three.js geometries UV mapping\n *\n * Sample usage:\n *\tdocument.body.appendChild( UVsDebug( new THREE.SphereGeometry( 10, 10, 10, 10 ) );\n *\n */\n\nfunction UVsDebug(geometry, size = 1024) {\n  // handles wrapping of uv.x > 1 only\n\n  const abc = 'abc'\n  const a = new Vector2()\n  const b = new Vector2()\n\n  const uvs = [new Vector2(), new Vector2(), new Vector2()]\n\n  const face = []\n\n  const canvas = document.createElement('canvas')\n  const width = size // power of 2 required for wrapping\n  const height = size\n  canvas.width = width\n  canvas.height = height\n\n  const ctx = canvas.getContext('2d')\n  ctx.lineWidth = 1\n  ctx.strokeStyle = 'rgb( 63, 63, 63 )'\n  ctx.textAlign = 'center'\n\n  // paint background white\n\n  ctx.fillStyle = 'rgb( 255, 255, 255 )'\n  ctx.fillRect(0, 0, width, height)\n\n  const index = geometry.index\n  const uvAttribute = geometry.attributes.uv\n\n  if (index) {\n    // indexed geometry\n\n    for (let i = 0, il = index.count; i < il; i += 3) {\n      face[0] = index.getX(i)\n      face[1] = index.getX(i + 1)\n      face[2] = index.getX(i + 2)\n\n      uvs[0].fromBufferAttribute(uvAttribute, face[0])\n      uvs[1].fromBufferAttribute(uvAttribute, face[1])\n      uvs[2].fromBufferAttribute(uvAttribute, face[2])\n\n      processFace(face, uvs, i / 3)\n    }\n  } else {\n    // non-indexed geometry\n\n    for (let i = 0, il = uvAttribute.count; i < il; i += 3) {\n      face[0] = i\n      face[1] = i + 1\n      face[2] = i + 2\n\n      uvs[0].fromBufferAttribute(uvAttribute, face[0])\n      uvs[1].fromBufferAttribute(uvAttribute, face[1])\n      uvs[2].fromBufferAttribute(uvAttribute, face[2])\n\n      processFace(face, uvs, i / 3)\n    }\n  }\n\n  return canvas\n\n  function processFace(face, uvs, index) {\n    // draw contour of face\n\n    ctx.beginPath()\n\n    a.set(0, 0)\n\n    for (let j = 0, jl = uvs.length; j < jl; j++) {\n      const uv = uvs[j]\n\n      a.x += uv.x\n      a.y += uv.y\n\n      if (j === 0) {\n        ctx.moveTo(uv.x * (width - 2) + 0.5, (1 - uv.y) * (height - 2) + 0.5)\n      } else {\n        ctx.lineTo(uv.x * (width - 2) + 0.5, (1 - uv.y) * (height - 2) + 0.5)\n      }\n    }\n\n    ctx.closePath()\n    ctx.stroke()\n\n    // calculate center of face\n\n    a.divideScalar(uvs.length)\n\n    // label the face number\n\n    ctx.font = '18px Arial'\n    ctx.fillStyle = 'rgb( 63, 63, 63 )'\n    ctx.fillText(index, a.x * width, (1 - a.y) * height)\n\n    if (a.x > 0.95) {\n      // wrap x // 0.95 is arbitrary\n\n      ctx.fillText(index, (a.x % 1) * width, (1 - a.y) * height)\n    }\n\n    //\n\n    ctx.font = '12px Arial'\n    ctx.fillStyle = 'rgb( 191, 191, 191 )'\n\n    // label uv edge orders\n\n    for (let j = 0, jl = uvs.length; j < jl; j++) {\n      const uv = uvs[j]\n      b.addVectors(a, uv).divideScalar(2)\n\n      const vnum = face[j]\n      ctx.fillText(abc[j] + vnum, b.x * width, (1 - b.y) * height)\n\n      if (b.x > 0.95) {\n        // wrap x\n\n        ctx.fillText(abc[j] + vnum, (b.x % 1) * width, (1 - b.y) * height)\n      }\n    }\n  }\n}\n\nexport { UVsDebug }\n"], "names": ["face", "uvs", "index"], "mappings": ";AAUA,SAAS,SAAS,UAAU,OAAO,MAAM;AAGvC,QAAM,MAAM;AACZ,QAAM,IAAI,IAAI,QAAS;AACvB,QAAM,IAAI,IAAI,QAAS;AAEvB,QAAM,MAAM,CAAC,IAAI,QAAO,GAAI,IAAI,QAAS,GAAE,IAAI,SAAS;AAExD,QAAM,OAAO,CAAE;AAEf,QAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,QAAM,QAAQ;AACd,QAAM,SAAS;AACf,SAAO,QAAQ;AACf,SAAO,SAAS;AAEhB,QAAM,MAAM,OAAO,WAAW,IAAI;AAClC,MAAI,YAAY;AAChB,MAAI,cAAc;AAClB,MAAI,YAAY;AAIhB,MAAI,YAAY;AAChB,MAAI,SAAS,GAAG,GAAG,OAAO,MAAM;AAEhC,QAAM,QAAQ,SAAS;AACvB,QAAM,cAAc,SAAS,WAAW;AAExC,MAAI,OAAO;AAGT,aAAS,IAAI,GAAG,KAAK,MAAM,OAAO,IAAI,IAAI,KAAK,GAAG;AAChD,WAAK,CAAC,IAAI,MAAM,KAAK,CAAC;AACtB,WAAK,CAAC,IAAI,MAAM,KAAK,IAAI,CAAC;AAC1B,WAAK,CAAC,IAAI,MAAM,KAAK,IAAI,CAAC;AAE1B,UAAI,CAAC,EAAE,oBAAoB,aAAa,KAAK,CAAC,CAAC;AAC/C,UAAI,CAAC,EAAE,oBAAoB,aAAa,KAAK,CAAC,CAAC;AAC/C,UAAI,CAAC,EAAE,oBAAoB,aAAa,KAAK,CAAC,CAAC;AAE/C,kBAAY,MAAM,KAAK,IAAI,CAAC;AAAA,IAC7B;AAAA,EACL,OAAS;AAGL,aAAS,IAAI,GAAG,KAAK,YAAY,OAAO,IAAI,IAAI,KAAK,GAAG;AACtD,WAAK,CAAC,IAAI;AACV,WAAK,CAAC,IAAI,IAAI;AACd,WAAK,CAAC,IAAI,IAAI;AAEd,UAAI,CAAC,EAAE,oBAAoB,aAAa,KAAK,CAAC,CAAC;AAC/C,UAAI,CAAC,EAAE,oBAAoB,aAAa,KAAK,CAAC,CAAC;AAC/C,UAAI,CAAC,EAAE,oBAAoB,aAAa,KAAK,CAAC,CAAC;AAE/C,kBAAY,MAAM,KAAK,IAAI,CAAC;AAAA,IAC7B;AAAA,EACF;AAED,SAAO;AAEP,WAAS,YAAYA,OAAMC,MAAKC,QAAO;AAGrC,QAAI,UAAW;AAEf,MAAE,IAAI,GAAG,CAAC;AAEV,aAAS,IAAI,GAAG,KAAKD,KAAI,QAAQ,IAAI,IAAI,KAAK;AAC5C,YAAM,KAAKA,KAAI,CAAC;AAEhB,QAAE,KAAK,GAAG;AACV,QAAE,KAAK,GAAG;AAEV,UAAI,MAAM,GAAG;AACX,YAAI,OAAO,GAAG,KAAK,QAAQ,KAAK,MAAM,IAAI,GAAG,MAAM,SAAS,KAAK,GAAG;AAAA,MAC5E,OAAa;AACL,YAAI,OAAO,GAAG,KAAK,QAAQ,KAAK,MAAM,IAAI,GAAG,MAAM,SAAS,KAAK,GAAG;AAAA,MACrE;AAAA,IACF;AAED,QAAI,UAAW;AACf,QAAI,OAAQ;AAIZ,MAAE,aAAaA,KAAI,MAAM;AAIzB,QAAI,OAAO;AACX,QAAI,YAAY;AAChB,QAAI,SAASC,QAAO,EAAE,IAAI,QAAQ,IAAI,EAAE,KAAK,MAAM;AAEnD,QAAI,EAAE,IAAI,MAAM;AAGd,UAAI,SAASA,QAAQ,EAAE,IAAI,IAAK,QAAQ,IAAI,EAAE,KAAK,MAAM;AAAA,IAC1D;AAID,QAAI,OAAO;AACX,QAAI,YAAY;AAIhB,aAAS,IAAI,GAAG,KAAKD,KAAI,QAAQ,IAAI,IAAI,KAAK;AAC5C,YAAM,KAAKA,KAAI,CAAC;AAChB,QAAE,WAAW,GAAG,EAAE,EAAE,aAAa,CAAC;AAElC,YAAM,OAAOD,MAAK,CAAC;AACnB,UAAI,SAAS,IAAI,CAAC,IAAI,MAAM,EAAE,IAAI,QAAQ,IAAI,EAAE,KAAK,MAAM;AAE3D,UAAI,EAAE,IAAI,MAAM;AAGd,YAAI,SAAS,IAAI,CAAC,IAAI,MAAO,EAAE,IAAI,IAAK,QAAQ,IAAI,EAAE,KAAK,MAAM;AAAA,MAClE;AAAA,IACF;AAAA,EACF;AACH;"}