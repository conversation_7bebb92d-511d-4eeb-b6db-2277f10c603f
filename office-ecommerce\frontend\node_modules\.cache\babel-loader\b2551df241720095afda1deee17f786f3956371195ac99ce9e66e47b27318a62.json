{"ast": null, "code": "import { _ as _objectSpread2 } from './objectSpread2-284232a6.esm.js';\nimport { _ as _slicedToArray } from './triangle-b62b9067.esm.js';\nimport { Quaternion, Vector3 } from 'three';\nimport { l as lerp$1 } from './misc-7d870b3c.esm.js';\nimport { z as zero, a as add$1 } from './vector2-d2bf51f1.esm.js';\nimport { a as add } from './vector3-0a088b7f.esm.js';\nfunction swizzle(buffer) {\n  var stride = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 3;\n  var swizzle = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : \"xyz\";\n  var o = {\n    x: 0,\n    y: 0,\n    z: 0\n  };\n  for (var _i = 0; _i < buffer.length; _i += stride) {\n    o.x = buffer[_i];\n    o.y = buffer[_i + 1];\n    o.z = buffer[_i + 2];\n    var _swizzle$split = swizzle.split(\"\"),\n      _swizzle$split2 = _slicedToArray(_swizzle$split, 3),\n      x = _swizzle$split2[0],\n      y = _swizzle$split2[1],\n      z = _swizzle$split2[2]; // TODO Fix this ugly type\n\n    buffer[_i] = o[x];\n    buffer[_i + 1] = o[y];\n    if (stride === 3) {\n      buffer[_i + 2] = o[z];\n    }\n  }\n  return buffer;\n}\n/**\n * @param buffer A stride 2 points buffer\n * @param valueGenerator A function that returns the value of the z axis at index i\n * @returns\n */\n\nfunction addAxis(buffer, size) {\n  var valueGenerator = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : function () {\n    return Math.random();\n  };\n  var newSize = size + 1;\n  var newBuffer = new Float32Array(buffer.length / size * newSize);\n  for (var _i2 = 0; _i2 < buffer.length; _i2 += size) {\n    var _j = _i2 / size * newSize;\n    newBuffer[_j] = buffer[_i2];\n    newBuffer[_j + 1] = buffer[_i2 + 1];\n    if (size === 2) {\n      newBuffer[_j + 2] = valueGenerator(_j);\n    }\n    if (size === 3) {\n      newBuffer[_j + 2] = buffer[_i2 + 2];\n      newBuffer[_j + 3] = valueGenerator(_j);\n    }\n  }\n  return newBuffer;\n}\n/**\n * Lerps bufferA and bufferB into final\n *\n * @param bufferA\n * @param bufferB\n * @param final\n * @param t\n */\n\nfunction lerp(bufferA, bufferB, _final, t) {\n  for (var _i3 = 0; _i3 < bufferA.length; _i3++) {\n    _final[_i3] = lerp$1(bufferA[_i3], bufferB[_i3], t);\n  }\n} // TODO add stride\n// TODO Fix types & vectors\n\n/**\n *\n * Translate all points in the passed buffer by the passed translactionVector.\n *\n * @param buffer\n * @param translationVector\n * @returns\n */\n\nfunction translate(buffer, translationVector) {\n  var stride = translationVector.length;\n  for (var _i4 = 0; _i4 < buffer.length; _i4 += stride) {\n    buffer[_i4] += translationVector[0];\n    buffer[_i4 + 1] += translationVector[1];\n    buffer[_i4 + 2] += translationVector[2];\n  }\n  return buffer;\n} // TODO add stride\n// TODO remove quaternion & vector3 dependencies\n\nfunction rotate(buffer, rotation) {\n  var defaultRotation = {\n    center: [0, 0, 0],\n    q: new Quaternion().identity()\n  };\n  var v = new Vector3();\n  var _defaultRotation$rota = _objectSpread2(_objectSpread2({}, defaultRotation), rotation),\n    q = _defaultRotation$rota.q,\n    center = _defaultRotation$rota.center;\n  for (var _i5 = 0; _i5 < buffer.length; _i5 += 3) {\n    v.set(buffer[_i5] - center[0], buffer[_i5 + 1] - center[1], buffer[_i5 + 2] - center[2]);\n    v.applyQuaternion(q);\n    buffer[_i5] = v.x + center[0];\n    buffer[_i5 + 1] = v.y + center[1];\n    buffer[_i5 + 2] = v.z + center[1];\n  }\n  return buffer;\n}\nfunction map(buffer, stride, callback) {\n  for (var _i6 = 0, _j2 = 0; _i6 < buffer.length; _i6 += stride, _j2++) {\n    if (stride === 3) {\n      var res = callback([buffer[_i6], buffer[_i6 + 1], buffer[_i6 + 2]], _j2);\n      buffer.set(res, _i6);\n    } else {\n      buffer.set(callback([buffer[_i6], buffer[_i6 + 1]], _j2), _i6);\n    }\n  }\n  return buffer;\n}\n/**\n * Reduces passed buffer\n */\n\nfunction reduce(b, stride, callback, acc) {\n  for (var _i7 = 0, _j3 = 0; _i7 < b.length; _i7 += stride, _j3++) {\n    if (stride === 2) {\n      acc = callback(acc, [b[_i7], b[_i7 + 1]], _j3);\n    } else {\n      acc = callback(acc, [b[_i7], b[_i7 + 1], b[_i7 + 2]], _j3);\n    }\n  }\n  return acc;\n}\nfunction expand(b, stride, opts) {\n  var defaultExpandOptions = {\n    center: [0, 0, 0]\n  };\n  var _defaultExpandOptions = _objectSpread2(_objectSpread2({}, defaultExpandOptions), opts),\n    center = _defaultExpandOptions.center,\n    distance = _defaultExpandOptions.distance;\n  for (var _i8 = 0; _i8 < b.length; _i8 += stride) {\n    /**\n     * 1. translate to origin (subtract the scaling center)\n     * 2. scale by the correct amount (multiply by a constant)\n     * 2. translate from origin (add the scaling center)\n     */\n    b[_i8] = (b[_i8] - center[0]) * (1 + distance) + center[0];\n    b[_i8 + 1] = (b[_i8 + 1] - center[1]) * (1 + distance) + center[1];\n    if (stride === 3) {\n      b[_i8 + 2] = (b[_i8 + 2] - center[1]) * (1 + distance) + center[2];\n    }\n  }\n  return b;\n}\nfunction center(myBuffer, stride) {\n  return reduce(myBuffer, stride, function (acc, point) {\n    if (stride === 3) {\n      // some type hacking is necessary to avoid type errors going from [n, n] => [n, n, n]\n      // but it's not an actual problem, as this path would always get a v3\n      acc = add(acc, point);\n    } else {\n      acc = add$1(acc, point);\n    }\n    return acc;\n  }, zero());\n}\nfunction sort(myBuffer, stride, callback) {\n  // 1. make an array of the correct size\n  var indices = Int16Array.from({\n    length: myBuffer.length / stride\n  }, function (_, i) {\n    return i;\n  }); // 2. sort the indices array\n\n  indices.sort(function (a, b) {\n    var pa = myBuffer.slice(a * stride, a * stride + stride);\n    var pb = myBuffer.slice(b * stride, b * stride + stride);\n    return callback(pa, pb);\n  }); // 3. make a copy of the original array to fetch indices from\n\n  var prevBuffer = myBuffer.slice(0); // 4. mutate the passed array\n\n  for (var _i9 = 0; _i9 < indices.length; _i9++) {\n    var _j4 = indices[_i9];\n    myBuffer.set(prevBuffer.slice(_j4 * stride, _j4 * stride + stride), _i9 * 3);\n  }\n  return myBuffer;\n}\nvar buffer = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  swizzle: swizzle,\n  addAxis: addAxis,\n  lerp: lerp,\n  translate: translate,\n  rotate: rotate,\n  map: map,\n  reduce: reduce,\n  expand: expand,\n  center: center,\n  sort: sort\n});\nexport { addAxis as a, buffer as b, reduce as c, center as d, expand as e, sort as f, lerp as l, map as m, rotate as r, swizzle as s, translate as t };", "map": {"version": 3, "names": ["_", "_objectSpread2", "_slicedToArray", "Quaternion", "Vector3", "l", "lerp$1", "z", "zero", "a", "add$1", "add", "swizzle", "buffer", "stride", "arguments", "length", "undefined", "o", "x", "y", "_i", "_swizzle$split", "split", "_swizzle$split2", "addAxis", "size", "valueGenerator", "Math", "random", "newSize", "new<PERSON>uffer", "Float32Array", "_i2", "_j", "lerp", "bufferA", "bufferB", "_final", "t", "_i3", "translate", "translationVector", "_i4", "rotate", "rotation", "defaultRotation", "center", "q", "identity", "v", "_defaultRotation$rota", "_i5", "set", "applyQuaternion", "map", "callback", "_i6", "_j2", "res", "reduce", "b", "acc", "_i7", "_j3", "expand", "opts", "defaultExpandOptions", "_defaultExpandOptions", "distance", "_i8", "my<PERSON><PERSON><PERSON>", "point", "sort", "indices", "Int16Array", "from", "i", "pa", "slice", "pb", "prevBuffer", "_i9", "_j4", "Object", "freeze", "__proto__", "c", "d", "e", "f", "m", "r", "s"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/maath/dist/buffer-d2a4726c.esm.js"], "sourcesContent": ["import { _ as _objectSpread2 } from './objectSpread2-284232a6.esm.js';\nimport { _ as _slicedToArray } from './triangle-b62b9067.esm.js';\nimport { Quaternion, Vector3 } from 'three';\nimport { l as lerp$1 } from './misc-7d870b3c.esm.js';\nimport { z as zero, a as add$1 } from './vector2-d2bf51f1.esm.js';\nimport { a as add } from './vector3-0a088b7f.esm.js';\n\nfunction swizzle(buffer) {\n  var stride = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 3;\n  var swizzle = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : \"xyz\";\n  var o = {\n    x: 0,\n    y: 0,\n    z: 0\n  };\n\n  for (var _i = 0; _i < buffer.length; _i += stride) {\n    o.x = buffer[_i];\n    o.y = buffer[_i + 1];\n    o.z = buffer[_i + 2];\n\n    var _swizzle$split = swizzle.split(\"\"),\n        _swizzle$split2 = _slicedToArray(_swizzle$split, 3),\n        x = _swizzle$split2[0],\n        y = _swizzle$split2[1],\n        z = _swizzle$split2[2]; // TODO Fix this ugly type\n\n\n    buffer[_i] = o[x];\n    buffer[_i + 1] = o[y];\n\n    if (stride === 3) {\n      buffer[_i + 2] = o[z];\n    }\n  }\n\n  return buffer;\n}\n/**\n * @param buffer A stride 2 points buffer\n * @param valueGenerator A function that returns the value of the z axis at index i\n * @returns\n */\n\nfunction addAxis(buffer, size) {\n  var valueGenerator = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : function () {\n    return Math.random();\n  };\n  var newSize = size + 1;\n  var newBuffer = new Float32Array(buffer.length / size * newSize);\n\n  for (var _i2 = 0; _i2 < buffer.length; _i2 += size) {\n    var _j = _i2 / size * newSize;\n\n    newBuffer[_j] = buffer[_i2];\n    newBuffer[_j + 1] = buffer[_i2 + 1];\n\n    if (size === 2) {\n      newBuffer[_j + 2] = valueGenerator(_j);\n    }\n\n    if (size === 3) {\n      newBuffer[_j + 2] = buffer[_i2 + 2];\n      newBuffer[_j + 3] = valueGenerator(_j);\n    }\n  }\n\n  return newBuffer;\n}\n/**\n * Lerps bufferA and bufferB into final\n *\n * @param bufferA\n * @param bufferB\n * @param final\n * @param t\n */\n\nfunction lerp(bufferA, bufferB, _final, t) {\n  for (var _i3 = 0; _i3 < bufferA.length; _i3++) {\n    _final[_i3] = lerp$1(bufferA[_i3], bufferB[_i3], t);\n  }\n} // TODO add stride\n// TODO Fix types & vectors\n\n/**\n *\n * Translate all points in the passed buffer by the passed translactionVector.\n *\n * @param buffer\n * @param translationVector\n * @returns\n */\n\nfunction translate(buffer, translationVector) {\n  var stride = translationVector.length;\n\n  for (var _i4 = 0; _i4 < buffer.length; _i4 += stride) {\n    buffer[_i4] += translationVector[0];\n    buffer[_i4 + 1] += translationVector[1];\n    buffer[_i4 + 2] += translationVector[2];\n  }\n\n  return buffer;\n} // TODO add stride\n// TODO remove quaternion & vector3 dependencies\n\nfunction rotate(buffer, rotation) {\n  var defaultRotation = {\n    center: [0, 0, 0],\n    q: new Quaternion().identity()\n  };\n  var v = new Vector3();\n\n  var _defaultRotation$rota = _objectSpread2(_objectSpread2({}, defaultRotation), rotation),\n      q = _defaultRotation$rota.q,\n      center = _defaultRotation$rota.center;\n\n  for (var _i5 = 0; _i5 < buffer.length; _i5 += 3) {\n    v.set(buffer[_i5] - center[0], buffer[_i5 + 1] - center[1], buffer[_i5 + 2] - center[2]);\n    v.applyQuaternion(q);\n    buffer[_i5] = v.x + center[0];\n    buffer[_i5 + 1] = v.y + center[1];\n    buffer[_i5 + 2] = v.z + center[1];\n  }\n\n  return buffer;\n}\nfunction map(buffer, stride, callback) {\n  for (var _i6 = 0, _j2 = 0; _i6 < buffer.length; _i6 += stride, _j2++) {\n    if (stride === 3) {\n      var res = callback([buffer[_i6], buffer[_i6 + 1], buffer[_i6 + 2]], _j2);\n      buffer.set(res, _i6);\n    } else {\n      buffer.set(callback([buffer[_i6], buffer[_i6 + 1]], _j2), _i6);\n    }\n  }\n\n  return buffer;\n}\n/**\n * Reduces passed buffer\n */\n\nfunction reduce(b, stride, callback, acc) {\n  for (var _i7 = 0, _j3 = 0; _i7 < b.length; _i7 += stride, _j3++) {\n    if (stride === 2) {\n      acc = callback(acc, [b[_i7], b[_i7 + 1]], _j3);\n    } else {\n      acc = callback(acc, [b[_i7], b[_i7 + 1], b[_i7 + 2]], _j3);\n    }\n  }\n\n  return acc;\n}\nfunction expand(b, stride, opts) {\n  var defaultExpandOptions = {\n    center: [0, 0, 0]\n  };\n\n  var _defaultExpandOptions = _objectSpread2(_objectSpread2({}, defaultExpandOptions), opts),\n      center = _defaultExpandOptions.center,\n      distance = _defaultExpandOptions.distance;\n\n  for (var _i8 = 0; _i8 < b.length; _i8 += stride) {\n    /**\n     * 1. translate to origin (subtract the scaling center)\n     * 2. scale by the correct amount (multiply by a constant)\n     * 2. translate from origin (add the scaling center)\n     */\n    b[_i8] = (b[_i8] - center[0]) * (1 + distance) + center[0];\n    b[_i8 + 1] = (b[_i8 + 1] - center[1]) * (1 + distance) + center[1];\n\n    if (stride === 3) {\n      b[_i8 + 2] = (b[_i8 + 2] - center[1]) * (1 + distance) + center[2];\n    }\n  }\n\n  return b;\n}\nfunction center(myBuffer, stride) {\n  return reduce(myBuffer, stride, function (acc, point) {\n    if (stride === 3) {\n      // some type hacking is necessary to avoid type errors going from [n, n] => [n, n, n]\n      // but it's not an actual problem, as this path would always get a v3\n      acc = add(acc, point);\n    } else {\n      acc = add$1(acc, point);\n    }\n\n    return acc;\n  }, zero());\n}\nfunction sort(myBuffer, stride, callback) {\n  // 1. make an array of the correct size\n  var indices = Int16Array.from({\n    length: myBuffer.length / stride\n  }, function (_, i) {\n    return i;\n  }); // 2. sort the indices array\n\n  indices.sort(function (a, b) {\n    var pa = myBuffer.slice(a * stride, a * stride + stride);\n    var pb = myBuffer.slice(b * stride, b * stride + stride);\n    return callback(pa, pb);\n  }); // 3. make a copy of the original array to fetch indices from\n\n  var prevBuffer = myBuffer.slice(0); // 4. mutate the passed array\n\n  for (var _i9 = 0; _i9 < indices.length; _i9++) {\n    var _j4 = indices[_i9];\n    myBuffer.set(prevBuffer.slice(_j4 * stride, _j4 * stride + stride), _i9 * 3);\n  }\n\n  return myBuffer;\n}\n\nvar buffer = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  swizzle: swizzle,\n  addAxis: addAxis,\n  lerp: lerp,\n  translate: translate,\n  rotate: rotate,\n  map: map,\n  reduce: reduce,\n  expand: expand,\n  center: center,\n  sort: sort\n});\n\nexport { addAxis as a, buffer as b, reduce as c, center as d, expand as e, sort as f, lerp as l, map as m, rotate as r, swizzle as s, translate as t };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,cAAc,QAAQ,iCAAiC;AACrE,SAASD,CAAC,IAAIE,cAAc,QAAQ,4BAA4B;AAChE,SAASC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC3C,SAASC,CAAC,IAAIC,MAAM,QAAQ,wBAAwB;AACpD,SAASC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,KAAK,QAAQ,2BAA2B;AACjE,SAASD,CAAC,IAAIE,GAAG,QAAQ,2BAA2B;AAEpD,SAASC,OAAOA,CAACC,MAAM,EAAE;EACvB,IAAIC,MAAM,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EAClF,IAAIH,OAAO,GAAGG,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACvF,IAAIG,CAAC,GAAG;IACNC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJb,CAAC,EAAE;EACL,CAAC;EAED,KAAK,IAAIc,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGR,MAAM,CAACG,MAAM,EAAEK,EAAE,IAAIP,MAAM,EAAE;IACjDI,CAAC,CAACC,CAAC,GAAGN,MAAM,CAACQ,EAAE,CAAC;IAChBH,CAAC,CAACE,CAAC,GAAGP,MAAM,CAACQ,EAAE,GAAG,CAAC,CAAC;IACpBH,CAAC,CAACX,CAAC,GAAGM,MAAM,CAACQ,EAAE,GAAG,CAAC,CAAC;IAEpB,IAAIC,cAAc,GAAGV,OAAO,CAACW,KAAK,CAAC,EAAE,CAAC;MAClCC,eAAe,GAAGtB,cAAc,CAACoB,cAAc,EAAE,CAAC,CAAC;MACnDH,CAAC,GAAGK,eAAe,CAAC,CAAC,CAAC;MACtBJ,CAAC,GAAGI,eAAe,CAAC,CAAC,CAAC;MACtBjB,CAAC,GAAGiB,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;;IAG5BX,MAAM,CAACQ,EAAE,CAAC,GAAGH,CAAC,CAACC,CAAC,CAAC;IACjBN,MAAM,CAACQ,EAAE,GAAG,CAAC,CAAC,GAAGH,CAAC,CAACE,CAAC,CAAC;IAErB,IAAIN,MAAM,KAAK,CAAC,EAAE;MAChBD,MAAM,CAACQ,EAAE,GAAG,CAAC,CAAC,GAAGH,CAAC,CAACX,CAAC,CAAC;IACvB;EACF;EAEA,OAAOM,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASY,OAAOA,CAACZ,MAAM,EAAEa,IAAI,EAAE;EAC7B,IAAIC,cAAc,GAAGZ,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,YAAY;IACnG,OAAOa,IAAI,CAACC,MAAM,CAAC,CAAC;EACtB,CAAC;EACD,IAAIC,OAAO,GAAGJ,IAAI,GAAG,CAAC;EACtB,IAAIK,SAAS,GAAG,IAAIC,YAAY,CAACnB,MAAM,CAACG,MAAM,GAAGU,IAAI,GAAGI,OAAO,CAAC;EAEhE,KAAK,IAAIG,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGpB,MAAM,CAACG,MAAM,EAAEiB,GAAG,IAAIP,IAAI,EAAE;IAClD,IAAIQ,EAAE,GAAGD,GAAG,GAAGP,IAAI,GAAGI,OAAO;IAE7BC,SAAS,CAACG,EAAE,CAAC,GAAGrB,MAAM,CAACoB,GAAG,CAAC;IAC3BF,SAAS,CAACG,EAAE,GAAG,CAAC,CAAC,GAAGrB,MAAM,CAACoB,GAAG,GAAG,CAAC,CAAC;IAEnC,IAAIP,IAAI,KAAK,CAAC,EAAE;MACdK,SAAS,CAACG,EAAE,GAAG,CAAC,CAAC,GAAGP,cAAc,CAACO,EAAE,CAAC;IACxC;IAEA,IAAIR,IAAI,KAAK,CAAC,EAAE;MACdK,SAAS,CAACG,EAAE,GAAG,CAAC,CAAC,GAAGrB,MAAM,CAACoB,GAAG,GAAG,CAAC,CAAC;MACnCF,SAAS,CAACG,EAAE,GAAG,CAAC,CAAC,GAAGP,cAAc,CAACO,EAAE,CAAC;IACxC;EACF;EAEA,OAAOH,SAAS;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASI,IAAIA,CAACC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,CAAC,EAAE;EACzC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGJ,OAAO,CAACpB,MAAM,EAAEwB,GAAG,EAAE,EAAE;IAC7CF,MAAM,CAACE,GAAG,CAAC,GAAGlC,MAAM,CAAC8B,OAAO,CAACI,GAAG,CAAC,EAAEH,OAAO,CAACG,GAAG,CAAC,EAAED,CAAC,CAAC;EACrD;AACF,CAAC,CAAC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASE,SAASA,CAAC5B,MAAM,EAAE6B,iBAAiB,EAAE;EAC5C,IAAI5B,MAAM,GAAG4B,iBAAiB,CAAC1B,MAAM;EAErC,KAAK,IAAI2B,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG9B,MAAM,CAACG,MAAM,EAAE2B,GAAG,IAAI7B,MAAM,EAAE;IACpDD,MAAM,CAAC8B,GAAG,CAAC,IAAID,iBAAiB,CAAC,CAAC,CAAC;IACnC7B,MAAM,CAAC8B,GAAG,GAAG,CAAC,CAAC,IAAID,iBAAiB,CAAC,CAAC,CAAC;IACvC7B,MAAM,CAAC8B,GAAG,GAAG,CAAC,CAAC,IAAID,iBAAiB,CAAC,CAAC,CAAC;EACzC;EAEA,OAAO7B,MAAM;AACf,CAAC,CAAC;AACF;;AAEA,SAAS+B,MAAMA,CAAC/B,MAAM,EAAEgC,QAAQ,EAAE;EAChC,IAAIC,eAAe,GAAG;IACpBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACjBC,CAAC,EAAE,IAAI7C,UAAU,CAAC,CAAC,CAAC8C,QAAQ,CAAC;EAC/B,CAAC;EACD,IAAIC,CAAC,GAAG,IAAI9C,OAAO,CAAC,CAAC;EAErB,IAAI+C,qBAAqB,GAAGlD,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6C,eAAe,CAAC,EAAED,QAAQ,CAAC;IACrFG,CAAC,GAAGG,qBAAqB,CAACH,CAAC;IAC3BD,MAAM,GAAGI,qBAAqB,CAACJ,MAAM;EAEzC,KAAK,IAAIK,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGvC,MAAM,CAACG,MAAM,EAAEoC,GAAG,IAAI,CAAC,EAAE;IAC/CF,CAAC,CAACG,GAAG,CAACxC,MAAM,CAACuC,GAAG,CAAC,GAAGL,MAAM,CAAC,CAAC,CAAC,EAAElC,MAAM,CAACuC,GAAG,GAAG,CAAC,CAAC,GAAGL,MAAM,CAAC,CAAC,CAAC,EAAElC,MAAM,CAACuC,GAAG,GAAG,CAAC,CAAC,GAAGL,MAAM,CAAC,CAAC,CAAC,CAAC;IACxFG,CAAC,CAACI,eAAe,CAACN,CAAC,CAAC;IACpBnC,MAAM,CAACuC,GAAG,CAAC,GAAGF,CAAC,CAAC/B,CAAC,GAAG4B,MAAM,CAAC,CAAC,CAAC;IAC7BlC,MAAM,CAACuC,GAAG,GAAG,CAAC,CAAC,GAAGF,CAAC,CAAC9B,CAAC,GAAG2B,MAAM,CAAC,CAAC,CAAC;IACjClC,MAAM,CAACuC,GAAG,GAAG,CAAC,CAAC,GAAGF,CAAC,CAAC3C,CAAC,GAAGwC,MAAM,CAAC,CAAC,CAAC;EACnC;EAEA,OAAOlC,MAAM;AACf;AACA,SAAS0C,GAAGA,CAAC1C,MAAM,EAAEC,MAAM,EAAE0C,QAAQ,EAAE;EACrC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,EAAED,GAAG,GAAG5C,MAAM,CAACG,MAAM,EAAEyC,GAAG,IAAI3C,MAAM,EAAE4C,GAAG,EAAE,EAAE;IACpE,IAAI5C,MAAM,KAAK,CAAC,EAAE;MAChB,IAAI6C,GAAG,GAAGH,QAAQ,CAAC,CAAC3C,MAAM,CAAC4C,GAAG,CAAC,EAAE5C,MAAM,CAAC4C,GAAG,GAAG,CAAC,CAAC,EAAE5C,MAAM,CAAC4C,GAAG,GAAG,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC;MACxE7C,MAAM,CAACwC,GAAG,CAACM,GAAG,EAAEF,GAAG,CAAC;IACtB,CAAC,MAAM;MACL5C,MAAM,CAACwC,GAAG,CAACG,QAAQ,CAAC,CAAC3C,MAAM,CAAC4C,GAAG,CAAC,EAAE5C,MAAM,CAAC4C,GAAG,GAAG,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC,EAAED,GAAG,CAAC;IAChE;EACF;EAEA,OAAO5C,MAAM;AACf;AACA;AACA;AACA;;AAEA,SAAS+C,MAAMA,CAACC,CAAC,EAAE/C,MAAM,EAAE0C,QAAQ,EAAEM,GAAG,EAAE;EACxC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,EAAED,GAAG,GAAGF,CAAC,CAAC7C,MAAM,EAAE+C,GAAG,IAAIjD,MAAM,EAAEkD,GAAG,EAAE,EAAE;IAC/D,IAAIlD,MAAM,KAAK,CAAC,EAAE;MAChBgD,GAAG,GAAGN,QAAQ,CAACM,GAAG,EAAE,CAACD,CAAC,CAACE,GAAG,CAAC,EAAEF,CAAC,CAACE,GAAG,GAAG,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC;IAChD,CAAC,MAAM;MACLF,GAAG,GAAGN,QAAQ,CAACM,GAAG,EAAE,CAACD,CAAC,CAACE,GAAG,CAAC,EAAEF,CAAC,CAACE,GAAG,GAAG,CAAC,CAAC,EAAEF,CAAC,CAACE,GAAG,GAAG,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC;IAC5D;EACF;EAEA,OAAOF,GAAG;AACZ;AACA,SAASG,MAAMA,CAACJ,CAAC,EAAE/C,MAAM,EAAEoD,IAAI,EAAE;EAC/B,IAAIC,oBAAoB,GAAG;IACzBpB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAClB,CAAC;EAED,IAAIqB,qBAAqB,GAAGnE,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEkE,oBAAoB,CAAC,EAAED,IAAI,CAAC;IACtFnB,MAAM,GAAGqB,qBAAqB,CAACrB,MAAM;IACrCsB,QAAQ,GAAGD,qBAAqB,CAACC,QAAQ;EAE7C,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGT,CAAC,CAAC7C,MAAM,EAAEsD,GAAG,IAAIxD,MAAM,EAAE;IAC/C;AACJ;AACA;AACA;AACA;IACI+C,CAAC,CAACS,GAAG,CAAC,GAAG,CAACT,CAAC,CAACS,GAAG,CAAC,GAAGvB,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,GAAGsB,QAAQ,CAAC,GAAGtB,MAAM,CAAC,CAAC,CAAC;IAC1Dc,CAAC,CAACS,GAAG,GAAG,CAAC,CAAC,GAAG,CAACT,CAAC,CAACS,GAAG,GAAG,CAAC,CAAC,GAAGvB,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,GAAGsB,QAAQ,CAAC,GAAGtB,MAAM,CAAC,CAAC,CAAC;IAElE,IAAIjC,MAAM,KAAK,CAAC,EAAE;MAChB+C,CAAC,CAACS,GAAG,GAAG,CAAC,CAAC,GAAG,CAACT,CAAC,CAACS,GAAG,GAAG,CAAC,CAAC,GAAGvB,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,GAAGsB,QAAQ,CAAC,GAAGtB,MAAM,CAAC,CAAC,CAAC;IACpE;EACF;EAEA,OAAOc,CAAC;AACV;AACA,SAASd,MAAMA,CAACwB,QAAQ,EAAEzD,MAAM,EAAE;EAChC,OAAO8C,MAAM,CAACW,QAAQ,EAAEzD,MAAM,EAAE,UAAUgD,GAAG,EAAEU,KAAK,EAAE;IACpD,IAAI1D,MAAM,KAAK,CAAC,EAAE;MAChB;MACA;MACAgD,GAAG,GAAGnD,GAAG,CAACmD,GAAG,EAAEU,KAAK,CAAC;IACvB,CAAC,MAAM;MACLV,GAAG,GAAGpD,KAAK,CAACoD,GAAG,EAAEU,KAAK,CAAC;IACzB;IAEA,OAAOV,GAAG;EACZ,CAAC,EAAEtD,IAAI,CAAC,CAAC,CAAC;AACZ;AACA,SAASiE,IAAIA,CAACF,QAAQ,EAAEzD,MAAM,EAAE0C,QAAQ,EAAE;EACxC;EACA,IAAIkB,OAAO,GAAGC,UAAU,CAACC,IAAI,CAAC;IAC5B5D,MAAM,EAAEuD,QAAQ,CAACvD,MAAM,GAAGF;EAC5B,CAAC,EAAE,UAAUd,CAAC,EAAE6E,CAAC,EAAE;IACjB,OAAOA,CAAC;EACV,CAAC,CAAC,CAAC,CAAC;;EAEJH,OAAO,CAACD,IAAI,CAAC,UAAUhE,CAAC,EAAEoD,CAAC,EAAE;IAC3B,IAAIiB,EAAE,GAAGP,QAAQ,CAACQ,KAAK,CAACtE,CAAC,GAAGK,MAAM,EAAEL,CAAC,GAAGK,MAAM,GAAGA,MAAM,CAAC;IACxD,IAAIkE,EAAE,GAAGT,QAAQ,CAACQ,KAAK,CAAClB,CAAC,GAAG/C,MAAM,EAAE+C,CAAC,GAAG/C,MAAM,GAAGA,MAAM,CAAC;IACxD,OAAO0C,QAAQ,CAACsB,EAAE,EAAEE,EAAE,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC;;EAEJ,IAAIC,UAAU,GAAGV,QAAQ,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEpC,KAAK,IAAIG,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGR,OAAO,CAAC1D,MAAM,EAAEkE,GAAG,EAAE,EAAE;IAC7C,IAAIC,GAAG,GAAGT,OAAO,CAACQ,GAAG,CAAC;IACtBX,QAAQ,CAAClB,GAAG,CAAC4B,UAAU,CAACF,KAAK,CAACI,GAAG,GAAGrE,MAAM,EAAEqE,GAAG,GAAGrE,MAAM,GAAGA,MAAM,CAAC,EAAEoE,GAAG,GAAG,CAAC,CAAC;EAC9E;EAEA,OAAOX,QAAQ;AACjB;AAEA,IAAI1D,MAAM,GAAG,aAAauE,MAAM,CAACC,MAAM,CAAC;EACtCC,SAAS,EAAE,IAAI;EACf1E,OAAO,EAAEA,OAAO;EAChBa,OAAO,EAAEA,OAAO;EAChBU,IAAI,EAAEA,IAAI;EACVM,SAAS,EAAEA,SAAS;EACpBG,MAAM,EAAEA,MAAM;EACdW,GAAG,EAAEA,GAAG;EACRK,MAAM,EAAEA,MAAM;EACdK,MAAM,EAAEA,MAAM;EACdlB,MAAM,EAAEA,MAAM;EACd0B,IAAI,EAAEA;AACR,CAAC,CAAC;AAEF,SAAShD,OAAO,IAAIhB,CAAC,EAAEI,MAAM,IAAIgD,CAAC,EAAED,MAAM,IAAI2B,CAAC,EAAExC,MAAM,IAAIyC,CAAC,EAAEvB,MAAM,IAAIwB,CAAC,EAAEhB,IAAI,IAAIiB,CAAC,EAAEvD,IAAI,IAAI9B,CAAC,EAAEkD,GAAG,IAAIoC,CAAC,EAAE/C,MAAM,IAAIgD,CAAC,EAAEhF,OAAO,IAAIiF,CAAC,EAAEpD,SAAS,IAAIF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}