{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport { Box3, Vector3, Sphere } from 'three';\nimport * as React from 'react';\nconst Center = /*#__PURE__*/React.forwardRef(function Center({\n  children,\n  disable,\n  disableX,\n  disableY,\n  disableZ,\n  left,\n  right,\n  top,\n  bottom,\n  front,\n  back,\n  onCentered,\n  precise = true,\n  cacheKey = 0,\n  ...props\n}, fRef) {\n  const ref = React.useRef(null);\n  const outer = React.useRef(null);\n  const inner = React.useRef(null);\n  React.useLayoutEffect(() => {\n    outer.current.matrixWorld.identity();\n    const box3 = new Box3().setFromObject(inner.current, precise);\n    const center = new Vector3();\n    const sphere = new Sphere();\n    const width = box3.max.x - box3.min.x;\n    const height = box3.max.y - box3.min.y;\n    const depth = box3.max.z - box3.min.z;\n    box3.getCenter(center);\n    box3.getBoundingSphere(sphere);\n    const vAlign = top ? height / 2 : bottom ? -height / 2 : 0;\n    const hAlign = left ? -width / 2 : right ? width / 2 : 0;\n    const dAlign = front ? depth / 2 : back ? -depth / 2 : 0;\n    outer.current.position.set(disable || disableX ? 0 : -center.x + hAlign, disable || disableY ? 0 : -center.y + vAlign, disable || disableZ ? 0 : -center.z + dAlign); // Only fire onCentered if the bounding box has changed\n\n    if (typeof onCentered !== 'undefined') {\n      onCentered({\n        parent: ref.current.parent,\n        container: ref.current,\n        width,\n        height,\n        depth,\n        boundingBox: box3,\n        boundingSphere: sphere,\n        center: center,\n        verticalAlignment: vAlign,\n        horizontalAlignment: hAlign,\n        depthAlignment: dAlign\n      });\n    }\n  }, [cacheKey, onCentered, top, left, front, disable, disableX, disableY, disableZ, precise, right, bottom, back]);\n  React.useImperativeHandle(fRef, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props), /*#__PURE__*/React.createElement(\"group\", {\n    ref: outer\n  }, /*#__PURE__*/React.createElement(\"group\", {\n    ref: inner\n  }, children)));\n});\nexport { Center };", "map": {"version": 3, "names": ["_extends", "Box3", "Vector3", "Sphere", "React", "Center", "forwardRef", "children", "disable", "disableX", "disableY", "disableZ", "left", "right", "top", "bottom", "front", "back", "onCentered", "precise", "cache<PERSON>ey", "props", "fRef", "ref", "useRef", "outer", "inner", "useLayoutEffect", "current", "matrixWorld", "identity", "box3", "setFromObject", "center", "sphere", "width", "max", "x", "min", "height", "y", "depth", "z", "getCenter", "getBoundingSphere", "vAlign", "hAlign", "dAlign", "position", "set", "parent", "container", "boundingBox", "boundingSphere", "verticalAlignment", "horizontalAlignment", "depthAlignment", "useImperativeHandle", "createElement"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Center.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { Box3, Vector3, Sphere } from 'three';\nimport * as React from 'react';\n\nconst Center = /*#__PURE__*/React.forwardRef(function Center({\n  children,\n  disable,\n  disableX,\n  disableY,\n  disableZ,\n  left,\n  right,\n  top,\n  bottom,\n  front,\n  back,\n  onCentered,\n  precise = true,\n  cacheKey = 0,\n  ...props\n}, fRef) {\n  const ref = React.useRef(null);\n  const outer = React.useRef(null);\n  const inner = React.useRef(null);\n  React.useLayoutEffect(() => {\n    outer.current.matrixWorld.identity();\n    const box3 = new Box3().setFromObject(inner.current, precise);\n    const center = new Vector3();\n    const sphere = new Sphere();\n    const width = box3.max.x - box3.min.x;\n    const height = box3.max.y - box3.min.y;\n    const depth = box3.max.z - box3.min.z;\n    box3.getCenter(center);\n    box3.getBoundingSphere(sphere);\n    const vAlign = top ? height / 2 : bottom ? -height / 2 : 0;\n    const hAlign = left ? -width / 2 : right ? width / 2 : 0;\n    const dAlign = front ? depth / 2 : back ? -depth / 2 : 0;\n    outer.current.position.set(disable || disableX ? 0 : -center.x + hAlign, disable || disableY ? 0 : -center.y + vAlign, disable || disableZ ? 0 : -center.z + dAlign); // Only fire onCentered if the bounding box has changed\n\n    if (typeof onCentered !== 'undefined') {\n      onCentered({\n        parent: ref.current.parent,\n        container: ref.current,\n        width,\n        height,\n        depth,\n        boundingBox: box3,\n        boundingSphere: sphere,\n        center: center,\n        verticalAlignment: vAlign,\n        horizontalAlignment: hAlign,\n        depthAlignment: dAlign\n      });\n    }\n  }, [cacheKey, onCentered, top, left, front, disable, disableX, disableY, disableZ, precise, right, bottom, back]);\n  React.useImperativeHandle(fRef, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, props), /*#__PURE__*/React.createElement(\"group\", {\n    ref: outer\n  }, /*#__PURE__*/React.createElement(\"group\", {\n    ref: inner\n  }, children)));\n});\n\nexport { Center };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,IAAI,EAAEC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AAC7C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,MAAMC,MAAM,GAAG,aAAaD,KAAK,CAACE,UAAU,CAAC,SAASD,MAAMA,CAAC;EAC3DE,QAAQ;EACRC,OAAO;EACPC,QAAQ;EACRC,QAAQ;EACRC,QAAQ;EACRC,IAAI;EACJC,KAAK;EACLC,GAAG;EACHC,MAAM;EACNC,KAAK;EACLC,IAAI;EACJC,UAAU;EACVC,OAAO,GAAG,IAAI;EACdC,QAAQ,GAAG,CAAC;EACZ,GAAGC;AACL,CAAC,EAAEC,IAAI,EAAE;EACP,MAAMC,GAAG,GAAGnB,KAAK,CAACoB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,KAAK,GAAGrB,KAAK,CAACoB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAME,KAAK,GAAGtB,KAAK,CAACoB,MAAM,CAAC,IAAI,CAAC;EAChCpB,KAAK,CAACuB,eAAe,CAAC,MAAM;IAC1BF,KAAK,CAACG,OAAO,CAACC,WAAW,CAACC,QAAQ,CAAC,CAAC;IACpC,MAAMC,IAAI,GAAG,IAAI9B,IAAI,CAAC,CAAC,CAAC+B,aAAa,CAACN,KAAK,CAACE,OAAO,EAAET,OAAO,CAAC;IAC7D,MAAMc,MAAM,GAAG,IAAI/B,OAAO,CAAC,CAAC;IAC5B,MAAMgC,MAAM,GAAG,IAAI/B,MAAM,CAAC,CAAC;IAC3B,MAAMgC,KAAK,GAAGJ,IAAI,CAACK,GAAG,CAACC,CAAC,GAAGN,IAAI,CAACO,GAAG,CAACD,CAAC;IACrC,MAAME,MAAM,GAAGR,IAAI,CAACK,GAAG,CAACI,CAAC,GAAGT,IAAI,CAACO,GAAG,CAACE,CAAC;IACtC,MAAMC,KAAK,GAAGV,IAAI,CAACK,GAAG,CAACM,CAAC,GAAGX,IAAI,CAACO,GAAG,CAACI,CAAC;IACrCX,IAAI,CAACY,SAAS,CAACV,MAAM,CAAC;IACtBF,IAAI,CAACa,iBAAiB,CAACV,MAAM,CAAC;IAC9B,MAAMW,MAAM,GAAG/B,GAAG,GAAGyB,MAAM,GAAG,CAAC,GAAGxB,MAAM,GAAG,CAACwB,MAAM,GAAG,CAAC,GAAG,CAAC;IAC1D,MAAMO,MAAM,GAAGlC,IAAI,GAAG,CAACuB,KAAK,GAAG,CAAC,GAAGtB,KAAK,GAAGsB,KAAK,GAAG,CAAC,GAAG,CAAC;IACxD,MAAMY,MAAM,GAAG/B,KAAK,GAAGyB,KAAK,GAAG,CAAC,GAAGxB,IAAI,GAAG,CAACwB,KAAK,GAAG,CAAC,GAAG,CAAC;IACxDhB,KAAK,CAACG,OAAO,CAACoB,QAAQ,CAACC,GAAG,CAACzC,OAAO,IAAIC,QAAQ,GAAG,CAAC,GAAG,CAACwB,MAAM,CAACI,CAAC,GAAGS,MAAM,EAAEtC,OAAO,IAAIE,QAAQ,GAAG,CAAC,GAAG,CAACuB,MAAM,CAACO,CAAC,GAAGK,MAAM,EAAErC,OAAO,IAAIG,QAAQ,GAAG,CAAC,GAAG,CAACsB,MAAM,CAACS,CAAC,GAAGK,MAAM,CAAC,CAAC,CAAC;;IAEtK,IAAI,OAAO7B,UAAU,KAAK,WAAW,EAAE;MACrCA,UAAU,CAAC;QACTgC,MAAM,EAAE3B,GAAG,CAACK,OAAO,CAACsB,MAAM;QAC1BC,SAAS,EAAE5B,GAAG,CAACK,OAAO;QACtBO,KAAK;QACLI,MAAM;QACNE,KAAK;QACLW,WAAW,EAAErB,IAAI;QACjBsB,cAAc,EAAEnB,MAAM;QACtBD,MAAM,EAAEA,MAAM;QACdqB,iBAAiB,EAAET,MAAM;QACzBU,mBAAmB,EAAET,MAAM;QAC3BU,cAAc,EAAET;MAClB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC3B,QAAQ,EAAEF,UAAU,EAAEJ,GAAG,EAAEF,IAAI,EAAEI,KAAK,EAAER,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEQ,OAAO,EAAEN,KAAK,EAAEE,MAAM,EAAEE,IAAI,CAAC,CAAC;EACjHb,KAAK,CAACqD,mBAAmB,CAACnC,IAAI,EAAE,MAAMC,GAAG,CAACK,OAAO,EAAE,EAAE,CAAC;EACtD,OAAO,aAAaxB,KAAK,CAACsD,aAAa,CAAC,OAAO,EAAE1D,QAAQ,CAAC;IACxDuB,GAAG,EAAEA;EACP,CAAC,EAAEF,KAAK,CAAC,EAAE,aAAajB,KAAK,CAACsD,aAAa,CAAC,OAAO,EAAE;IACnDnC,GAAG,EAAEE;EACP,CAAC,EAAE,aAAarB,KAAK,CAACsD,aAAa,CAAC,OAAO,EAAE;IAC3CnC,GAAG,EAAEG;EACP,CAAC,EAAEnB,QAAQ,CAAC,CAAC,CAAC;AAChB,CAAC,CAAC;AAEF,SAASF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}