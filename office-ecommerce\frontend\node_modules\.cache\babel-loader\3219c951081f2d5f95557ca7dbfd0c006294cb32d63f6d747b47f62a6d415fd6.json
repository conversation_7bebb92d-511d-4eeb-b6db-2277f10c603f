{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport * as FIBER from '@react-three/fiber';\nimport { WireframeMaterial, WireframeMaterialShaders, useWireframeUniforms, setWireframeOverride } from '../materials/WireframeMaterial.js';\nFIBER.extend({\n  MeshWireframeMaterial: WireframeMaterial\n});\nfunction isWithGeometry(object) {\n  return !!(object != null && object.geometry);\n}\nfunction isGeometry(object) {\n  return !!(object != null && object.isBufferGeometry);\n}\nfunction isRefObject(object) {\n  return !!(object != null && object.current);\n}\nfunction isRef(object) {\n  return (object == null ? void 0 : object.current) !== undefined;\n}\nfunction isWireframeGeometry(geometry) {\n  return geometry.type === 'WireframeGeometry';\n}\nfunction getUniforms() {\n  const u = {};\n  for (const key in WireframeMaterialShaders.uniforms) {\n    u[key] = {\n      value: WireframeMaterialShaders.uniforms[key]\n    };\n  }\n  return u;\n}\nfunction getBarycentricCoordinates(geometry, removeEdge) {\n  const position = geometry.getAttribute('position');\n  const count = position.count;\n  const barycentric = [];\n  for (let i = 0; i < count; i++) {\n    const even = i % 2 === 0;\n    const Q = removeEdge ? 1 : 0;\n    if (even) {\n      barycentric.push(0, 0, 1, 0, 1, 0, 1, 0, Q);\n    } else {\n      barycentric.push(0, 1, 0, 0, 0, 1, 1, 0, Q);\n    }\n  }\n  return new THREE.BufferAttribute(Float32Array.from(barycentric), 3);\n}\nfunction getInputGeometry(inputGeometry) {\n  const geo = isRefObject(inputGeometry) ? inputGeometry.current : inputGeometry;\n  if (!isGeometry(geo)) {\n    // Disallow WireframeGeometry\n    if (isWireframeGeometry(geo)) {\n      throw new Error('Wireframe: WireframeGeometry is not supported.');\n    }\n    const parent = geo.parent;\n    if (isWithGeometry(parent)) {\n      // Disallow WireframeGeometry\n      if (isWireframeGeometry(parent.geometry)) {\n        throw new Error('Wireframe: WireframeGeometry is not supported.');\n      }\n      return parent.geometry;\n    }\n  } else {\n    return geo;\n  }\n}\nfunction setBarycentricCoordinates(geometry, simplify) {\n  if (geometry.index) {\n    console.warn('Wireframe: Requires non-indexed geometry, converting to non-indexed geometry.');\n    const nonIndexedGeo = geometry.toNonIndexed();\n    geometry.copy(nonIndexedGeo);\n    geometry.setIndex(null);\n  }\n  const newBarycentric = getBarycentricCoordinates(geometry, simplify);\n  geometry.setAttribute('barycentric', newBarycentric);\n}\nfunction WireframeWithCustomGeo({\n  geometry: customGeometry,\n  simplify = false,\n  ...props\n}) {\n  const [geometry, setGeometry] = React.useState(null);\n  React.useLayoutEffect(() => {\n    const geom = getInputGeometry(customGeometry);\n    if (!geom) {\n      throw new Error('Wireframe: geometry prop must be a BufferGeometry or a ref to a BufferGeometry.');\n    }\n    setBarycentricCoordinates(geom, simplify);\n    if (isRef(customGeometry)) {\n      setGeometry(geom);\n    }\n  }, [simplify, customGeometry]);\n  const drawnGeo = isRef(customGeometry) ? geometry : customGeometry;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, drawnGeo && /*#__PURE__*/React.createElement(\"mesh\", {\n    geometry: drawnGeo\n  }, /*#__PURE__*/React.createElement(\"meshWireframeMaterial\", _extends({\n    attach: \"material\",\n    transparent: true,\n    side: THREE.DoubleSide,\n    polygonOffset: true //\n    ,\n\n    polygonOffsetFactor: -4\n  }, props, {\n    extensions: {\n      derivatives: true,\n      fragDepth: false,\n      drawBuffers: false,\n      shaderTextureLOD: false\n    }\n  }))));\n}\nfunction WireframeWithoutCustomGeo({\n  simplify = false,\n  ...props\n}) {\n  const objectRef = React.useRef(null);\n  const uniforms = React.useMemo(() => getUniforms(), [WireframeMaterialShaders.uniforms]);\n  useWireframeUniforms(uniforms, props);\n  React.useLayoutEffect(() => {\n    const geom = getInputGeometry(objectRef);\n    if (!geom) {\n      throw new Error('Wireframe: Must be a child of a Mesh, Line or Points object or specify a geometry prop.');\n    }\n    const og = geom.clone();\n    setBarycentricCoordinates(geom, simplify);\n    return () => {\n      geom.copy(og);\n      og.dispose();\n    };\n  }, [simplify]);\n  React.useLayoutEffect(() => {\n    const parentMesh = objectRef.current.parent;\n    const og = parentMesh.material.clone();\n    setWireframeOverride(parentMesh.material, uniforms);\n    return () => {\n      parentMesh.material.dispose();\n      parentMesh.material = og;\n    };\n  }, []);\n  return /*#__PURE__*/React.createElement(\"object3D\", {\n    ref: objectRef\n  });\n}\nfunction Wireframe({\n  geometry: customGeometry,\n  ...props\n}) {\n  if (customGeometry) {\n    return /*#__PURE__*/React.createElement(WireframeWithCustomGeo, _extends({\n      geometry: customGeometry\n    }, props));\n  }\n  return /*#__PURE__*/React.createElement(WireframeWithoutCustomGeo, props);\n}\nexport { Wireframe };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "FIBER", "WireframeMaterial", "WireframeMaterialShaders", "useWireframeUniforms", "setWireframeOverride", "extend", "MeshWireframeMaterial", "isWithGeometry", "object", "geometry", "isGeometry", "isBufferGeometry", "isRefObject", "current", "isRef", "undefined", "isWireframeGeometry", "type", "getUniforms", "u", "key", "uniforms", "value", "getBarycentricCoordinates", "removeEdge", "position", "getAttribute", "count", "barycentric", "i", "even", "Q", "push", "BufferAttribute", "Float32Array", "from", "getInputGeometry", "inputGeometry", "geo", "Error", "parent", "setBarycentricCoordinates", "simplify", "index", "console", "warn", "nonIndexedGeo", "toNonIndexed", "copy", "setIndex", "newBarycentric", "setAttribute", "WireframeWithCustomGeo", "customGeometry", "props", "setGeometry", "useState", "useLayoutEffect", "geom", "drawnGeo", "createElement", "Fragment", "attach", "transparent", "side", "DoubleSide", "polygonOffset", "polygonOffsetFactor", "extensions", "derivatives", "fragDepth", "drawBuffers", "shaderTextureLOD", "WireframeWithoutCustomGeo", "objectRef", "useRef", "useMemo", "og", "clone", "dispose", "parent<PERSON><PERSON>", "material", "ref", "Wireframe"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Wireframe.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport * as FIBER from '@react-three/fiber';\nimport { WireframeMaterial, WireframeMaterialShaders, useWireframeUniforms, setWireframeOverride } from '../materials/WireframeMaterial.js';\n\nFIBER.extend({\n  MeshWireframeMaterial: WireframeMaterial\n});\n\nfunction isWithGeometry(object) {\n  return !!(object != null && object.geometry);\n}\n\nfunction isGeometry(object) {\n  return !!(object != null && object.isBufferGeometry);\n}\n\nfunction isRefObject(object) {\n  return !!(object != null && object.current);\n}\n\nfunction isRef(object) {\n  return (object == null ? void 0 : object.current) !== undefined;\n}\n\nfunction isWireframeGeometry(geometry) {\n  return geometry.type === 'WireframeGeometry';\n}\n\nfunction getUniforms() {\n  const u = {};\n\n  for (const key in WireframeMaterialShaders.uniforms) {\n    u[key] = {\n      value: WireframeMaterialShaders.uniforms[key]\n    };\n  }\n\n  return u;\n}\n\nfunction getBarycentricCoordinates(geometry, removeEdge) {\n  const position = geometry.getAttribute('position');\n  const count = position.count;\n  const barycentric = [];\n\n  for (let i = 0; i < count; i++) {\n    const even = i % 2 === 0;\n    const Q = removeEdge ? 1 : 0;\n\n    if (even) {\n      barycentric.push(0, 0, 1, 0, 1, 0, 1, 0, Q);\n    } else {\n      barycentric.push(0, 1, 0, 0, 0, 1, 1, 0, Q);\n    }\n  }\n\n  return new THREE.BufferAttribute(Float32Array.from(barycentric), 3);\n}\n\nfunction getInputGeometry(inputGeometry) {\n  const geo = isRefObject(inputGeometry) ? inputGeometry.current : inputGeometry;\n\n  if (!isGeometry(geo)) {\n    // Disallow WireframeGeometry\n    if (isWireframeGeometry(geo)) {\n      throw new Error('Wireframe: WireframeGeometry is not supported.');\n    }\n\n    const parent = geo.parent;\n\n    if (isWithGeometry(parent)) {\n      // Disallow WireframeGeometry\n      if (isWireframeGeometry(parent.geometry)) {\n        throw new Error('Wireframe: WireframeGeometry is not supported.');\n      }\n\n      return parent.geometry;\n    }\n  } else {\n    return geo;\n  }\n}\n\nfunction setBarycentricCoordinates(geometry, simplify) {\n  if (geometry.index) {\n    console.warn('Wireframe: Requires non-indexed geometry, converting to non-indexed geometry.');\n    const nonIndexedGeo = geometry.toNonIndexed();\n    geometry.copy(nonIndexedGeo);\n    geometry.setIndex(null);\n  }\n\n  const newBarycentric = getBarycentricCoordinates(geometry, simplify);\n  geometry.setAttribute('barycentric', newBarycentric);\n}\n\nfunction WireframeWithCustomGeo({\n  geometry: customGeometry,\n  simplify = false,\n  ...props\n}) {\n  const [geometry, setGeometry] = React.useState(null);\n  React.useLayoutEffect(() => {\n    const geom = getInputGeometry(customGeometry);\n\n    if (!geom) {\n      throw new Error('Wireframe: geometry prop must be a BufferGeometry or a ref to a BufferGeometry.');\n    }\n\n    setBarycentricCoordinates(geom, simplify);\n\n    if (isRef(customGeometry)) {\n      setGeometry(geom);\n    }\n  }, [simplify, customGeometry]);\n  const drawnGeo = isRef(customGeometry) ? geometry : customGeometry;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, drawnGeo && /*#__PURE__*/React.createElement(\"mesh\", {\n    geometry: drawnGeo\n  }, /*#__PURE__*/React.createElement(\"meshWireframeMaterial\", _extends({\n    attach: \"material\",\n    transparent: true,\n    side: THREE.DoubleSide,\n    polygonOffset: true //\n    ,\n    polygonOffsetFactor: -4\n  }, props, {\n    extensions: {\n      derivatives: true,\n      fragDepth: false,\n      drawBuffers: false,\n      shaderTextureLOD: false\n    }\n  }))));\n}\n\nfunction WireframeWithoutCustomGeo({\n  simplify = false,\n  ...props\n}) {\n  const objectRef = React.useRef(null);\n  const uniforms = React.useMemo(() => getUniforms(), [WireframeMaterialShaders.uniforms]);\n  useWireframeUniforms(uniforms, props);\n  React.useLayoutEffect(() => {\n    const geom = getInputGeometry(objectRef);\n\n    if (!geom) {\n      throw new Error('Wireframe: Must be a child of a Mesh, Line or Points object or specify a geometry prop.');\n    }\n\n    const og = geom.clone();\n    setBarycentricCoordinates(geom, simplify);\n    return () => {\n      geom.copy(og);\n      og.dispose();\n    };\n  }, [simplify]);\n  React.useLayoutEffect(() => {\n    const parentMesh = objectRef.current.parent;\n    const og = parentMesh.material.clone();\n    setWireframeOverride(parentMesh.material, uniforms);\n    return () => {\n      parentMesh.material.dispose();\n      parentMesh.material = og;\n    };\n  }, []);\n  return /*#__PURE__*/React.createElement(\"object3D\", {\n    ref: objectRef\n  });\n}\n\nfunction Wireframe({\n  geometry: customGeometry,\n  ...props\n}) {\n  if (customGeometry) {\n    return /*#__PURE__*/React.createElement(WireframeWithCustomGeo, _extends({\n      geometry: customGeometry\n    }, props));\n  }\n\n  return /*#__PURE__*/React.createElement(WireframeWithoutCustomGeo, props);\n}\n\nexport { Wireframe };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,oBAAoB;AAC3C,SAASC,iBAAiB,EAAEC,wBAAwB,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,mCAAmC;AAE3IJ,KAAK,CAACK,MAAM,CAAC;EACXC,qBAAqB,EAAEL;AACzB,CAAC,CAAC;AAEF,SAASM,cAAcA,CAACC,MAAM,EAAE;EAC9B,OAAO,CAAC,EAAEA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACC,QAAQ,CAAC;AAC9C;AAEA,SAASC,UAAUA,CAACF,MAAM,EAAE;EAC1B,OAAO,CAAC,EAAEA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACG,gBAAgB,CAAC;AACtD;AAEA,SAASC,WAAWA,CAACJ,MAAM,EAAE;EAC3B,OAAO,CAAC,EAAEA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACK,OAAO,CAAC;AAC7C;AAEA,SAASC,KAAKA,CAACN,MAAM,EAAE;EACrB,OAAO,CAACA,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACK,OAAO,MAAME,SAAS;AACjE;AAEA,SAASC,mBAAmBA,CAACP,QAAQ,EAAE;EACrC,OAAOA,QAAQ,CAACQ,IAAI,KAAK,mBAAmB;AAC9C;AAEA,SAASC,WAAWA,CAAA,EAAG;EACrB,MAAMC,CAAC,GAAG,CAAC,CAAC;EAEZ,KAAK,MAAMC,GAAG,IAAIlB,wBAAwB,CAACmB,QAAQ,EAAE;IACnDF,CAAC,CAACC,GAAG,CAAC,GAAG;MACPE,KAAK,EAAEpB,wBAAwB,CAACmB,QAAQ,CAACD,GAAG;IAC9C,CAAC;EACH;EAEA,OAAOD,CAAC;AACV;AAEA,SAASI,yBAAyBA,CAACd,QAAQ,EAAEe,UAAU,EAAE;EACvD,MAAMC,QAAQ,GAAGhB,QAAQ,CAACiB,YAAY,CAAC,UAAU,CAAC;EAClD,MAAMC,KAAK,GAAGF,QAAQ,CAACE,KAAK;EAC5B,MAAMC,WAAW,GAAG,EAAE;EAEtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,EAAEE,CAAC,EAAE,EAAE;IAC9B,MAAMC,IAAI,GAAGD,CAAC,GAAG,CAAC,KAAK,CAAC;IACxB,MAAME,CAAC,GAAGP,UAAU,GAAG,CAAC,GAAG,CAAC;IAE5B,IAAIM,IAAI,EAAE;MACRF,WAAW,CAACI,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,CAAC,CAAC;IAC7C,CAAC,MAAM;MACLH,WAAW,CAACI,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,CAAC,CAAC;IAC7C;EACF;EAEA,OAAO,IAAIhC,KAAK,CAACkC,eAAe,CAACC,YAAY,CAACC,IAAI,CAACP,WAAW,CAAC,EAAE,CAAC,CAAC;AACrE;AAEA,SAASQ,gBAAgBA,CAACC,aAAa,EAAE;EACvC,MAAMC,GAAG,GAAG1B,WAAW,CAACyB,aAAa,CAAC,GAAGA,aAAa,CAACxB,OAAO,GAAGwB,aAAa;EAE9E,IAAI,CAAC3B,UAAU,CAAC4B,GAAG,CAAC,EAAE;IACpB;IACA,IAAItB,mBAAmB,CAACsB,GAAG,CAAC,EAAE;MAC5B,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;IACnE;IAEA,MAAMC,MAAM,GAAGF,GAAG,CAACE,MAAM;IAEzB,IAAIjC,cAAc,CAACiC,MAAM,CAAC,EAAE;MAC1B;MACA,IAAIxB,mBAAmB,CAACwB,MAAM,CAAC/B,QAAQ,CAAC,EAAE;QACxC,MAAM,IAAI8B,KAAK,CAAC,gDAAgD,CAAC;MACnE;MAEA,OAAOC,MAAM,CAAC/B,QAAQ;IACxB;EACF,CAAC,MAAM;IACL,OAAO6B,GAAG;EACZ;AACF;AAEA,SAASG,yBAAyBA,CAAChC,QAAQ,EAAEiC,QAAQ,EAAE;EACrD,IAAIjC,QAAQ,CAACkC,KAAK,EAAE;IAClBC,OAAO,CAACC,IAAI,CAAC,+EAA+E,CAAC;IAC7F,MAAMC,aAAa,GAAGrC,QAAQ,CAACsC,YAAY,CAAC,CAAC;IAC7CtC,QAAQ,CAACuC,IAAI,CAACF,aAAa,CAAC;IAC5BrC,QAAQ,CAACwC,QAAQ,CAAC,IAAI,CAAC;EACzB;EAEA,MAAMC,cAAc,GAAG3B,yBAAyB,CAACd,QAAQ,EAAEiC,QAAQ,CAAC;EACpEjC,QAAQ,CAAC0C,YAAY,CAAC,aAAa,EAAED,cAAc,CAAC;AACtD;AAEA,SAASE,sBAAsBA,CAAC;EAC9B3C,QAAQ,EAAE4C,cAAc;EACxBX,QAAQ,GAAG,KAAK;EAChB,GAAGY;AACL,CAAC,EAAE;EACD,MAAM,CAAC7C,QAAQ,EAAE8C,WAAW,CAAC,GAAGzD,KAAK,CAAC0D,QAAQ,CAAC,IAAI,CAAC;EACpD1D,KAAK,CAAC2D,eAAe,CAAC,MAAM;IAC1B,MAAMC,IAAI,GAAGtB,gBAAgB,CAACiB,cAAc,CAAC;IAE7C,IAAI,CAACK,IAAI,EAAE;MACT,MAAM,IAAInB,KAAK,CAAC,iFAAiF,CAAC;IACpG;IAEAE,yBAAyB,CAACiB,IAAI,EAAEhB,QAAQ,CAAC;IAEzC,IAAI5B,KAAK,CAACuC,cAAc,CAAC,EAAE;MACzBE,WAAW,CAACG,IAAI,CAAC;IACnB;EACF,CAAC,EAAE,CAAChB,QAAQ,EAAEW,cAAc,CAAC,CAAC;EAC9B,MAAMM,QAAQ,GAAG7C,KAAK,CAACuC,cAAc,CAAC,GAAG5C,QAAQ,GAAG4C,cAAc;EAClE,OAAO,aAAavD,KAAK,CAAC8D,aAAa,CAAC9D,KAAK,CAAC+D,QAAQ,EAAE,IAAI,EAAEF,QAAQ,IAAI,aAAa7D,KAAK,CAAC8D,aAAa,CAAC,MAAM,EAAE;IACjHnD,QAAQ,EAAEkD;EACZ,CAAC,EAAE,aAAa7D,KAAK,CAAC8D,aAAa,CAAC,uBAAuB,EAAE/D,QAAQ,CAAC;IACpEiE,MAAM,EAAE,UAAU;IAClBC,WAAW,EAAE,IAAI;IACjBC,IAAI,EAAEjE,KAAK,CAACkE,UAAU;IACtBC,aAAa,EAAE,IAAI,CAAC;IAAA;;IAEpBC,mBAAmB,EAAE,CAAC;EACxB,CAAC,EAAEb,KAAK,EAAE;IACRc,UAAU,EAAE;MACVC,WAAW,EAAE,IAAI;MACjBC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE,KAAK;MAClBC,gBAAgB,EAAE;IACpB;EACF,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAEA,SAASC,yBAAyBA,CAAC;EACjC/B,QAAQ,GAAG,KAAK;EAChB,GAAGY;AACL,CAAC,EAAE;EACD,MAAMoB,SAAS,GAAG5E,KAAK,CAAC6E,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMtD,QAAQ,GAAGvB,KAAK,CAAC8E,OAAO,CAAC,MAAM1D,WAAW,CAAC,CAAC,EAAE,CAAChB,wBAAwB,CAACmB,QAAQ,CAAC,CAAC;EACxFlB,oBAAoB,CAACkB,QAAQ,EAAEiC,KAAK,CAAC;EACrCxD,KAAK,CAAC2D,eAAe,CAAC,MAAM;IAC1B,MAAMC,IAAI,GAAGtB,gBAAgB,CAACsC,SAAS,CAAC;IAExC,IAAI,CAAChB,IAAI,EAAE;MACT,MAAM,IAAInB,KAAK,CAAC,yFAAyF,CAAC;IAC5G;IAEA,MAAMsC,EAAE,GAAGnB,IAAI,CAACoB,KAAK,CAAC,CAAC;IACvBrC,yBAAyB,CAACiB,IAAI,EAAEhB,QAAQ,CAAC;IACzC,OAAO,MAAM;MACXgB,IAAI,CAACV,IAAI,CAAC6B,EAAE,CAAC;MACbA,EAAE,CAACE,OAAO,CAAC,CAAC;IACd,CAAC;EACH,CAAC,EAAE,CAACrC,QAAQ,CAAC,CAAC;EACd5C,KAAK,CAAC2D,eAAe,CAAC,MAAM;IAC1B,MAAMuB,UAAU,GAAGN,SAAS,CAAC7D,OAAO,CAAC2B,MAAM;IAC3C,MAAMqC,EAAE,GAAGG,UAAU,CAACC,QAAQ,CAACH,KAAK,CAAC,CAAC;IACtC1E,oBAAoB,CAAC4E,UAAU,CAACC,QAAQ,EAAE5D,QAAQ,CAAC;IACnD,OAAO,MAAM;MACX2D,UAAU,CAACC,QAAQ,CAACF,OAAO,CAAC,CAAC;MAC7BC,UAAU,CAACC,QAAQ,GAAGJ,EAAE;IAC1B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAa/E,KAAK,CAAC8D,aAAa,CAAC,UAAU,EAAE;IAClDsB,GAAG,EAAER;EACP,CAAC,CAAC;AACJ;AAEA,SAASS,SAASA,CAAC;EACjB1E,QAAQ,EAAE4C,cAAc;EACxB,GAAGC;AACL,CAAC,EAAE;EACD,IAAID,cAAc,EAAE;IAClB,OAAO,aAAavD,KAAK,CAAC8D,aAAa,CAACR,sBAAsB,EAAEvD,QAAQ,CAAC;MACvEY,QAAQ,EAAE4C;IACZ,CAAC,EAAEC,KAAK,CAAC,CAAC;EACZ;EAEA,OAAO,aAAaxD,KAAK,CAAC8D,aAAa,CAACa,yBAAyB,EAAEnB,KAAK,CAAC;AAC3E;AAEA,SAAS6B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}