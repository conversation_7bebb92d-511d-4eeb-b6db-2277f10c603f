"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),n=require("@react-three/fiber"),o=require("three-stdlib");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var s=a(e),c=i(t),l=i(r);function u(e=[0,0,0]){return function(e){return Array.isArray(e)}(e)?e:e instanceof l.Vector3||e instanceof l.Euler?[e.x,e.y,e.z]:[e,e,e]}const f=c.forwardRef((function({debug:e,mesh:t,children:r,position:a,rotation:i,scale:f,...p},m){const y=c.useRef(null);c.useImperativeHandle(m,(()=>y.current));const d=c.useRef(null);return c.useLayoutEffect((()=>{const e=(null==t?void 0:t.current)||y.current.parent,r=y.current;if(!(e instanceof l.Mesh))throw new Error('Decal must have a Mesh as parent or specify its "mesh" prop');const s={position:new l.Vector3,rotation:new l.Euler,scale:new l.Vector3(1,1,1)};if(e){n.applyProps(s,{position:a,scale:f});const t=e.matrixWorld.clone();if(e.matrixWorld.identity(),i&&"number"!=typeof i)n.applyProps(s,{rotation:i});else{const t=new l.Object3D;t.position.copy(s.position),t.lookAt(e.position),"number"==typeof i&&t.rotateZ(i),n.applyProps(s,{rotation:t.rotation})}return r.geometry=new o.DecalGeometry(e,s.position,s.rotation,s.scale),d.current&&n.applyProps(d.current,s),e.matrixWorld=t,()=>{r.geometry.dispose()}}}),[t,...u(a),...u(f),...u(i)]),c.createElement("mesh",{ref:y},r||c.createElement("meshStandardMaterial",s.default({transparent:!0,polygonOffset:!0,polygonOffsetFactor:-10},p)),e&&c.createElement("mesh",{ref:d},c.createElement("boxGeometry",null),c.createElement("meshNormalMaterial",{wireframe:!0}),c.createElement("axesHelper",null)))}));exports.Decal=f;
