"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),n=require("react"),i=require("three"),o=require("@react-three/fiber"),r=require("./shaderMaterial.cjs.js");function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function l(e){if(e&&e.__esModule)return e;var n=Object.create(null);return e&&Object.keys(e).forEach((function(i){if("default"!==i){var o=Object.getOwnPropertyDescriptor(e,i);Object.defineProperty(n,i,o.get?o:{enumerable:!0,get:function(){return e[i]}})}})),n.default=e,Object.freeze(n)}var a=t(e),c=l(n),s=l(i);const f=r.shaderMaterial({cellSize:.5,sectionSize:1,fadeDistance:100,fadeStrength:1,cellThickness:.5,sectionThickness:1,cellColor:new s.Color,sectionColor:new s.Color,infiniteGrid:!1,followCamera:!1},"\n    varying vec3 worldPosition;\n    uniform float fadeDistance;\n    uniform bool infiniteGrid;\n    uniform bool followCamera;\n\n    void main() {\n      worldPosition = position.xzy;\n      if (infiniteGrid) worldPosition *= 1.0 + fadeDistance;\n      if (followCamera) worldPosition.xz +=cameraPosition.xz;\n\n      gl_Position = projectionMatrix * modelViewMatrix * vec4(worldPosition, 1.0);\n    }\n  ","\n    varying vec3 worldPosition;\n    uniform float cellSize;\n    uniform float sectionSize;\n    uniform vec3 cellColor;\n    uniform vec3 sectionColor;\n    uniform float fadeDistance;\n    uniform float fadeStrength;\n    uniform float cellThickness;\n    uniform float sectionThickness;\n\n    float getGrid(float size, float thickness) {\n      vec2 r = worldPosition.xz / size;\n      vec2 grid = abs(fract(r - 0.5) - 0.5) / fwidth(r);\n      float line = min(grid.x, grid.y) + 1. - thickness;\n      return 1.0 - min(line, 1.);\n    }\n\n    void main() {\n      float g1 = getGrid(cellSize, cellThickness);\n      float g2 = getGrid(sectionSize, sectionThickness);\n\n      float d = 1.0 - min(distance(cameraPosition.xz, worldPosition.xz) / fadeDistance, 1.);\n      vec3 color = mix(cellColor, sectionColor, min(1.,sectionThickness * g2));\n\n      gl_FragColor = vec4(color, (g1 + g2) * pow(d,fadeStrength));\n      gl_FragColor.a = mix(0.75 * gl_FragColor.a, gl_FragColor.a, g2);\n      if (gl_FragColor.a <= 0.0) discard;\n\n      #include <tonemapping_fragment>\n      #include <encodings_fragment>\n    }\n  "),d=c.forwardRef((({args:e,cellColor:n="#000000",sectionColor:i="#2080ff",cellSize:r=.5,sectionSize:t=1,followCamera:l=!1,infiniteGrid:d=!1,fadeDistance:u=100,fadeStrength:g=1,cellThickness:m=.5,sectionThickness:h=1,side:C=s.BackSide,...v},w)=>{o.extend({GridMaterial:f});const z={cellSize:r,sectionSize:t,cellColor:n,sectionColor:i,cellThickness:m,sectionThickness:h},x={fadeDistance:u,fadeStrength:g,infiniteGrid:d,followCamera:l};return c.createElement("mesh",a.default({ref:w,frustumCulled:!1},v),c.createElement("gridMaterial",a.default({transparent:!0,"extensions-derivatives":!0,side:C},z,x)),c.createElement("planeGeometry",{args:e}))}));exports.Grid=d;
