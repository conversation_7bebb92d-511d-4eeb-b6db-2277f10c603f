{"ast": null, "code": "import { LineBasicMaterial, BufferAttribute, Box3, Group, MeshBasicMaterial, Object3D, BufferGeometry } from 'three';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nconst boundingBox = /* @__PURE__ */new Box3();\nclass MeshBVHRootVisualizer extends Object3D {\n  get isMesh() {\n    return !this.displayEdges;\n  }\n  get isLineSegments() {\n    return this.displayEdges;\n  }\n  get isLine() {\n    return this.displayEdges;\n  }\n  constructor(mesh, material, depth = 10, group = 0) {\n    super();\n    this.material = material;\n    this.geometry = new BufferGeometry();\n    this.name = 'MeshBVHRootVisualizer';\n    this.depth = depth;\n    this.displayParents = false;\n    this.mesh = mesh;\n    this.displayEdges = true;\n    this._group = group;\n  }\n  raycast() {}\n  update() {\n    const geometry = this.geometry;\n    const boundsTree = this.mesh.geometry.boundsTree;\n    const group = this._group;\n    geometry.dispose();\n    this.visible = false;\n    if (boundsTree) {\n      // count the number of bounds required\n      const targetDepth = this.depth - 1;\n      const displayParents = this.displayParents;\n      let boundsCount = 0;\n      boundsTree.traverse((depth, isLeaf) => {\n        if (depth === targetDepth || isLeaf) {\n          boundsCount++;\n          return true;\n        } else if (displayParents) {\n          boundsCount++;\n        }\n      }, group);\n\n      // fill in the position buffer with the bounds corners\n      let posIndex = 0;\n      const positionArray = new Float32Array(8 * 3 * boundsCount);\n      boundsTree.traverse((depth, isLeaf, boundingData) => {\n        const terminate = depth === targetDepth || isLeaf;\n        if (terminate || displayParents) {\n          arrayToBox(0, boundingData, boundingBox);\n          const {\n            min,\n            max\n          } = boundingBox;\n          for (let x = -1; x <= 1; x += 2) {\n            const xVal = x < 0 ? min.x : max.x;\n            for (let y = -1; y <= 1; y += 2) {\n              const yVal = y < 0 ? min.y : max.y;\n              for (let z = -1; z <= 1; z += 2) {\n                const zVal = z < 0 ? min.z : max.z;\n                positionArray[posIndex + 0] = xVal;\n                positionArray[posIndex + 1] = yVal;\n                positionArray[posIndex + 2] = zVal;\n                posIndex += 3;\n              }\n            }\n          }\n          return terminate;\n        }\n      }, group);\n      let indexArray;\n      let indices;\n      if (this.displayEdges) {\n        // fill in the index buffer to point to the corner points\n        indices = new Uint8Array([\n        // x axis\n        0, 4, 1, 5, 2, 6, 3, 7,\n        // y axis\n        0, 2, 1, 3, 4, 6, 5, 7,\n        // z axis\n        0, 1, 2, 3, 4, 5, 6, 7]);\n      } else {\n        indices = new Uint8Array([\n        // X-, X+\n        0, 1, 2, 2, 1, 3, 4, 6, 5, 6, 7, 5,\n        // Y-, Y+\n        1, 4, 5, 0, 4, 1, 2, 3, 6, 3, 7, 6,\n        // Z-, Z+\n        0, 2, 4, 2, 6, 4, 1, 5, 3, 3, 5, 7]);\n      }\n      if (positionArray.length > 65535) {\n        indexArray = new Uint32Array(indices.length * boundsCount);\n      } else {\n        indexArray = new Uint16Array(indices.length * boundsCount);\n      }\n      const indexLength = indices.length;\n      for (let i = 0; i < boundsCount; i++) {\n        const posOffset = i * 8;\n        const indexOffset = i * indexLength;\n        for (let j = 0; j < indexLength; j++) {\n          indexArray[indexOffset + j] = posOffset + indices[j];\n        }\n      }\n\n      // update the geometry\n      geometry.setIndex(new BufferAttribute(indexArray, 1, false));\n      geometry.setAttribute('position', new BufferAttribute(positionArray, 3, false));\n      this.visible = true;\n    }\n  }\n}\nclass MeshBVHVisualizer extends Group {\n  get color() {\n    return this.edgeMaterial.color;\n  }\n  get opacity() {\n    return this.edgeMaterial.opacity;\n  }\n  set opacity(v) {\n    this.edgeMaterial.opacity = v;\n    this.meshMaterial.opacity = v;\n  }\n  constructor(mesh, depth = 10) {\n    super();\n    this.name = 'MeshBVHVisualizer';\n    this.depth = depth;\n    this.mesh = mesh;\n    this.displayParents = false;\n    this.displayEdges = true;\n    this._roots = [];\n    const edgeMaterial = new LineBasicMaterial({\n      color: 0x00FF88,\n      transparent: true,\n      opacity: 0.3,\n      depthWrite: false\n    });\n    const meshMaterial = new MeshBasicMaterial({\n      color: 0x00FF88,\n      transparent: true,\n      opacity: 0.3,\n      depthWrite: false\n    });\n    meshMaterial.color = edgeMaterial.color;\n    this.edgeMaterial = edgeMaterial;\n    this.meshMaterial = meshMaterial;\n    this.update();\n  }\n  update() {\n    const bvh = this.mesh.geometry.boundsTree;\n    const totalRoots = bvh ? bvh._roots.length : 0;\n    while (this._roots.length > totalRoots) {\n      const root = this._roots.pop();\n      root.geometry.dispose();\n      this.remove(root);\n    }\n    for (let i = 0; i < totalRoots; i++) {\n      if (i >= this._roots.length) {\n        const root = new MeshBVHRootVisualizer(this.mesh, this.edgeMaterial, this.depth, i);\n        this.add(root);\n        this._roots.push(root);\n      }\n      const root = this._roots[i];\n      root.depth = this.depth;\n      root.mesh = this.mesh;\n      root.displayParents = this.displayParents;\n      root.displayEdges = this.displayEdges;\n      root.material = this.displayEdges ? this.edgeMaterial : this.meshMaterial;\n      root.update();\n    }\n  }\n  updateMatrixWorld(...args) {\n    this.position.copy(this.mesh.position);\n    this.rotation.copy(this.mesh.rotation);\n    this.scale.copy(this.mesh.scale);\n    super.updateMatrixWorld(...args);\n  }\n  copy(source) {\n    this.depth = source.depth;\n    this.mesh = source.mesh;\n  }\n  clone() {\n    return new MeshBVHVisualizer(this.mesh, this.depth);\n  }\n  dispose() {\n    this.edgeMaterial.dispose();\n    this.meshMaterial.dispose();\n    const children = this.children;\n    for (let i = 0, l = children.length; i < l; i++) {\n      children[i].geometry.dispose();\n    }\n  }\n}\nexport { MeshBVHVisualizer };", "map": {"version": 3, "names": ["LineBasicMaterial", "BufferAttribute", "Box3", "Group", "MeshBasicMaterial", "Object3D", "BufferGeometry", "arrayToBox", "boundingBox", "MeshBVHRootVisualizer", "<PERSON><PERSON><PERSON>", "displayEdges", "isLineSegments", "isLine", "constructor", "mesh", "material", "depth", "group", "geometry", "name", "displayParents", "_group", "raycast", "update", "boundsTree", "dispose", "visible", "targetDepth", "boundsCount", "traverse", "<PERSON><PERSON><PERSON><PERSON>", "posIndex", "positionArray", "Float32Array", "boundingData", "terminate", "min", "max", "x", "xVal", "y", "yVal", "z", "zVal", "indexArray", "indices", "Uint8Array", "length", "Uint32Array", "Uint16Array", "indexLength", "i", "posOffset", "indexOffset", "j", "setIndex", "setAttribute", "MeshBVHVisualizer", "color", "edgeMaterial", "opacity", "v", "meshMaterial", "_roots", "transparent", "depthWrite", "bvh", "totalRoots", "root", "pop", "remove", "add", "push", "updateMatrixWorld", "args", "position", "copy", "rotation", "scale", "source", "clone", "children", "l"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/three-mesh-bvh/src/objects/MeshBVHVisualizer.js"], "sourcesContent": ["import { LineBasicMaterial, BufferAttribute, Box3, Group, MeshBasicMaterial, Object3D, BufferGeometry } from 'three';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\n\nconst boundingBox = /* @__PURE__ */ new Box3();\nclass MeshBVHRootVisualizer extends Object3D {\n\n\tget isMesh() {\n\n\t\treturn ! this.displayEdges;\n\n\t}\n\n\tget isLineSegments() {\n\n\t\treturn this.displayEdges;\n\n\t}\n\n\tget isLine() {\n\n\t\treturn this.displayEdges;\n\n\t}\n\n\tconstructor( mesh, material, depth = 10, group = 0 ) {\n\n\t\tsuper();\n\n\t\tthis.material = material;\n\t\tthis.geometry = new BufferGeometry();\n\t\tthis.name = 'MeshBVHRootVisualizer';\n\t\tthis.depth = depth;\n\t\tthis.displayParents = false;\n\t\tthis.mesh = mesh;\n\t\tthis.displayEdges = true;\n\t\tthis._group = group;\n\n\t}\n\n\traycast() {}\n\n\tupdate() {\n\n\t\tconst geometry = this.geometry;\n\t\tconst boundsTree = this.mesh.geometry.boundsTree;\n\t\tconst group = this._group;\n\t\tgeometry.dispose();\n\t\tthis.visible = false;\n\t\tif ( boundsTree ) {\n\n\t\t\t// count the number of bounds required\n\t\t\tconst targetDepth = this.depth - 1;\n\t\t\tconst displayParents = this.displayParents;\n\t\t\tlet boundsCount = 0;\n\t\t\tboundsTree.traverse( ( depth, isLeaf ) => {\n\n\t\t\t\tif ( depth === targetDepth || isLeaf ) {\n\n\t\t\t\t\tboundsCount ++;\n\t\t\t\t\treturn true;\n\n\t\t\t\t} else if ( displayParents ) {\n\n\t\t\t\t\tboundsCount ++;\n\n\t\t\t\t}\n\n\t\t\t}, group );\n\n\t\t\t// fill in the position buffer with the bounds corners\n\t\t\tlet posIndex = 0;\n\t\t\tconst positionArray = new Float32Array( 8 * 3 * boundsCount );\n\t\t\tboundsTree.traverse( ( depth, isLeaf, boundingData ) => {\n\n\t\t\t\tconst terminate = depth === targetDepth || isLeaf;\n\t\t\t\tif ( terminate || displayParents ) {\n\n\t\t\t\t\tarrayToBox( 0, boundingData, boundingBox );\n\n\t\t\t\t\tconst { min, max } = boundingBox;\n\t\t\t\t\tfor ( let x = - 1; x <= 1; x += 2 ) {\n\n\t\t\t\t\t\tconst xVal = x < 0 ? min.x : max.x;\n\t\t\t\t\t\tfor ( let y = - 1; y <= 1; y += 2 ) {\n\n\t\t\t\t\t\t\tconst yVal = y < 0 ? min.y : max.y;\n\t\t\t\t\t\t\tfor ( let z = - 1; z <= 1; z += 2 ) {\n\n\t\t\t\t\t\t\t\tconst zVal = z < 0 ? min.z : max.z;\n\t\t\t\t\t\t\t\tpositionArray[ posIndex + 0 ] = xVal;\n\t\t\t\t\t\t\t\tpositionArray[ posIndex + 1 ] = yVal;\n\t\t\t\t\t\t\t\tpositionArray[ posIndex + 2 ] = zVal;\n\n\t\t\t\t\t\t\t\tposIndex += 3;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn terminate;\n\n\t\t\t\t}\n\n\t\t\t}, group );\n\n\t\t\tlet indexArray;\n\t\t\tlet indices;\n\t\t\tif ( this.displayEdges ) {\n\n\t\t\t\t// fill in the index buffer to point to the corner points\n\t\t\t\tindices = new Uint8Array( [\n\t\t\t\t\t// x axis\n\t\t\t\t\t0, 4,\n\t\t\t\t\t1, 5,\n\t\t\t\t\t2, 6,\n\t\t\t\t\t3, 7,\n\n\t\t\t\t\t// y axis\n\t\t\t\t\t0, 2,\n\t\t\t\t\t1, 3,\n\t\t\t\t\t4, 6,\n\t\t\t\t\t5, 7,\n\n\t\t\t\t\t// z axis\n\t\t\t\t\t0, 1,\n\t\t\t\t\t2, 3,\n\t\t\t\t\t4, 5,\n\t\t\t\t\t6, 7,\n\t\t\t\t] );\n\n\t\t\t} else {\n\n\t\t\t\tindices = new Uint8Array( [\n\n\t\t\t\t\t// X-, X+\n\t\t\t\t\t0, 1, 2,\n\t\t\t\t\t2, 1, 3,\n\n\t\t\t\t\t4, 6, 5,\n\t\t\t\t\t6, 7, 5,\n\n\t\t\t\t\t// Y-, Y+\n\t\t\t\t\t1, 4, 5,\n\t\t\t\t\t0, 4, 1,\n\n\t\t\t\t\t2, 3, 6,\n\t\t\t\t\t3, 7, 6,\n\n\t\t\t\t\t// Z-, Z+\n\t\t\t\t\t0, 2, 4,\n\t\t\t\t\t2, 6, 4,\n\n\t\t\t\t\t1, 5, 3,\n\t\t\t\t\t3, 5, 7,\n\n\t\t\t\t] );\n\n\t\t\t}\n\n\t\t\tif ( positionArray.length > 65535 ) {\n\n\t\t\t\tindexArray = new Uint32Array( indices.length * boundsCount );\n\n\t\t\t} else {\n\n\t\t\t\tindexArray = new Uint16Array( indices.length * boundsCount );\n\n\t\t\t}\n\n\t\t\tconst indexLength = indices.length;\n\t\t\tfor ( let i = 0; i < boundsCount; i ++ ) {\n\n\t\t\t\tconst posOffset = i * 8;\n\t\t\t\tconst indexOffset = i * indexLength;\n\t\t\t\tfor ( let j = 0; j < indexLength; j ++ ) {\n\n\t\t\t\t\tindexArray[ indexOffset + j ] = posOffset + indices[ j ];\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// update the geometry\n\t\t\tgeometry.setIndex(\n\t\t\t\tnew BufferAttribute( indexArray, 1, false ),\n\t\t\t);\n\t\t\tgeometry.setAttribute(\n\t\t\t\t'position',\n\t\t\t\tnew BufferAttribute( positionArray, 3, false ),\n\t\t\t);\n\t\t\tthis.visible = true;\n\n\t\t}\n\n\t}\n\n}\n\nclass MeshBVHVisualizer extends Group {\n\n\tget color() {\n\n\t\treturn this.edgeMaterial.color;\n\n\t}\n\n\tget opacity() {\n\n\t\treturn this.edgeMaterial.opacity;\n\n\t}\n\n\tset opacity( v ) {\n\n\t\tthis.edgeMaterial.opacity = v;\n\t\tthis.meshMaterial.opacity = v;\n\n\t}\n\n\tconstructor( mesh, depth = 10 ) {\n\n\t\tsuper();\n\n\t\tthis.name = 'MeshBVHVisualizer';\n\t\tthis.depth = depth;\n\t\tthis.mesh = mesh;\n\t\tthis.displayParents = false;\n\t\tthis.displayEdges = true;\n\t\tthis._roots = [];\n\n\t\tconst edgeMaterial = new LineBasicMaterial( {\n\t\t\tcolor: 0x00FF88,\n\t\t\ttransparent: true,\n\t\t\topacity: 0.3,\n\t\t\tdepthWrite: false,\n\t\t} );\n\n\t\tconst meshMaterial = new MeshBasicMaterial( {\n\t\t\tcolor: 0x00FF88,\n\t\t\ttransparent: true,\n\t\t\topacity: 0.3,\n\t\t\tdepthWrite: false,\n\t\t} );\n\n\t\tmeshMaterial.color = edgeMaterial.color;\n\n\t\tthis.edgeMaterial = edgeMaterial;\n\t\tthis.meshMaterial = meshMaterial;\n\n\t\tthis.update();\n\n\t}\n\n\tupdate() {\n\n\t\tconst bvh = this.mesh.geometry.boundsTree;\n\t\tconst totalRoots = bvh ? bvh._roots.length : 0;\n\t\twhile ( this._roots.length > totalRoots ) {\n\n\t\t\tconst root = this._roots.pop();\n\t\t\troot.geometry.dispose();\n\t\t\tthis.remove( root );\n\n\t\t}\n\n\t\tfor ( let i = 0; i < totalRoots; i ++ ) {\n\n\t\t\tif ( i >= this._roots.length ) {\n\n\t\t\t\tconst root = new MeshBVHRootVisualizer( this.mesh, this.edgeMaterial, this.depth, i );\n\t\t\t\tthis.add( root );\n\t\t\t\tthis._roots.push( root );\n\n\t\t\t}\n\n\t\t\tconst root = this._roots[ i ];\n\t\t\troot.depth = this.depth;\n\t\t\troot.mesh = this.mesh;\n\t\t\troot.displayParents = this.displayParents;\n\t\t\troot.displayEdges = this.displayEdges;\n\t\t\troot.material = this.displayEdges ? this.edgeMaterial : this.meshMaterial;\n\t\t\troot.update();\n\n\t\t}\n\n\t}\n\n\tupdateMatrixWorld( ...args ) {\n\n\t\tthis.position.copy( this.mesh.position );\n\t\tthis.rotation.copy( this.mesh.rotation );\n\t\tthis.scale.copy( this.mesh.scale );\n\n\t\tsuper.updateMatrixWorld( ...args );\n\n\t}\n\n\tcopy( source ) {\n\n\t\tthis.depth = source.depth;\n\t\tthis.mesh = source.mesh;\n\n\t}\n\n\tclone() {\n\n\t\treturn new MeshBVHVisualizer( this.mesh, this.depth );\n\n\t}\n\n\tdispose() {\n\n\t\tthis.edgeMaterial.dispose();\n\t\tthis.meshMaterial.dispose();\n\n\t\tconst children = this.children;\n\t\tfor ( let i = 0, l = children.length; i < l; i ++ ) {\n\n\t\t\tchildren[ i ].geometry.dispose();\n\n\t\t}\n\n\t}\n\n}\n\n\nexport { MeshBVHVisualizer };\n"], "mappings": "AAAA,SAASA,iBAAiB,EAAEC,eAAe,EAAEC,IAAI,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,OAAO;AACpH,SAASC,UAAU,QAAQ,+BAA+B;AAE1D,MAAMC,WAAW,GAAG,eAAgB,IAAIN,IAAI,CAAC,CAAC;AAC9C,MAAMO,qBAAqB,SAASJ,QAAQ,CAAC;EAE5C,IAAIK,MAAMA,CAAA,EAAG;IAEZ,OAAO,CAAE,IAAI,CAACC,YAAY;EAE3B;EAEA,IAAIC,cAAcA,CAAA,EAAG;IAEpB,OAAO,IAAI,CAACD,YAAY;EAEzB;EAEA,IAAIE,MAAMA,CAAA,EAAG;IAEZ,OAAO,IAAI,CAACF,YAAY;EAEzB;EAEAG,WAAWA,CAAEC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,GAAG,EAAE,EAAEC,KAAK,GAAG,CAAC,EAAG;IAEpD,KAAK,CAAC,CAAC;IAEP,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACG,QAAQ,GAAG,IAAIb,cAAc,CAAC,CAAC;IACpC,IAAI,CAACc,IAAI,GAAG,uBAAuB;IACnC,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACI,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACN,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACJ,YAAY,GAAG,IAAI;IACxB,IAAI,CAACW,MAAM,GAAGJ,KAAK;EAEpB;EAEAK,OAAOA,CAAA,EAAG,CAAC;EAEXC,MAAMA,CAAA,EAAG;IAER,MAAML,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMM,UAAU,GAAG,IAAI,CAACV,IAAI,CAACI,QAAQ,CAACM,UAAU;IAChD,MAAMP,KAAK,GAAG,IAAI,CAACI,MAAM;IACzBH,QAAQ,CAACO,OAAO,CAAC,CAAC;IAClB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAKF,UAAU,EAAG;MAEjB;MACA,MAAMG,WAAW,GAAG,IAAI,CAACX,KAAK,GAAG,CAAC;MAClC,MAAMI,cAAc,GAAG,IAAI,CAACA,cAAc;MAC1C,IAAIQ,WAAW,GAAG,CAAC;MACnBJ,UAAU,CAACK,QAAQ,CAAE,CAAEb,KAAK,EAAEc,MAAM,KAAM;QAEzC,IAAKd,KAAK,KAAKW,WAAW,IAAIG,MAAM,EAAG;UAEtCF,WAAW,EAAG;UACd,OAAO,IAAI;QAEZ,CAAC,MAAM,IAAKR,cAAc,EAAG;UAE5BQ,WAAW,EAAG;QAEf;MAED,CAAC,EAAEX,KAAM,CAAC;;MAEV;MACA,IAAIc,QAAQ,GAAG,CAAC;MAChB,MAAMC,aAAa,GAAG,IAAIC,YAAY,CAAE,CAAC,GAAG,CAAC,GAAGL,WAAY,CAAC;MAC7DJ,UAAU,CAACK,QAAQ,CAAE,CAAEb,KAAK,EAAEc,MAAM,EAAEI,YAAY,KAAM;QAEvD,MAAMC,SAAS,GAAGnB,KAAK,KAAKW,WAAW,IAAIG,MAAM;QACjD,IAAKK,SAAS,IAAIf,cAAc,EAAG;UAElCd,UAAU,CAAE,CAAC,EAAE4B,YAAY,EAAE3B,WAAY,CAAC;UAE1C,MAAM;YAAE6B,GAAG;YAAEC;UAAI,CAAC,GAAG9B,WAAW;UAChC,KAAM,IAAI+B,CAAC,GAAG,CAAE,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAG;YAEnC,MAAMC,IAAI,GAAGD,CAAC,GAAG,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGD,GAAG,CAACC,CAAC;YAClC,KAAM,IAAIE,CAAC,GAAG,CAAE,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAG;cAEnC,MAAMC,IAAI,GAAGD,CAAC,GAAG,CAAC,GAAGJ,GAAG,CAACI,CAAC,GAAGH,GAAG,CAACG,CAAC;cAClC,KAAM,IAAIE,CAAC,GAAG,CAAE,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAG;gBAEnC,MAAMC,IAAI,GAAGD,CAAC,GAAG,CAAC,GAAGN,GAAG,CAACM,CAAC,GAAGL,GAAG,CAACK,CAAC;gBAClCV,aAAa,CAAED,QAAQ,GAAG,CAAC,CAAE,GAAGQ,IAAI;gBACpCP,aAAa,CAAED,QAAQ,GAAG,CAAC,CAAE,GAAGU,IAAI;gBACpCT,aAAa,CAAED,QAAQ,GAAG,CAAC,CAAE,GAAGY,IAAI;gBAEpCZ,QAAQ,IAAI,CAAC;cAEd;YAED;UAED;UAEA,OAAOI,SAAS;QAEjB;MAED,CAAC,EAAElB,KAAM,CAAC;MAEV,IAAI2B,UAAU;MACd,IAAIC,OAAO;MACX,IAAK,IAAI,CAACnC,YAAY,EAAG;QAExB;QACAmC,OAAO,GAAG,IAAIC,UAAU,CAAE;QACzB;QACA,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC;QAEJ;QACA,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC;QAEJ;QACA,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,EACJ,CAAC,EAAE,CAAC,CACH,CAAC;MAEJ,CAAC,MAAM;QAEND,OAAO,GAAG,IAAIC,UAAU,CAAE;QAEzB;QACA,CAAC,EAAE,CAAC,EAAE,CAAC,EACP,CAAC,EAAE,CAAC,EAAE,CAAC,EAEP,CAAC,EAAE,CAAC,EAAE,CAAC,EACP,CAAC,EAAE,CAAC,EAAE,CAAC;QAEP;QACA,CAAC,EAAE,CAAC,EAAE,CAAC,EACP,CAAC,EAAE,CAAC,EAAE,CAAC,EAEP,CAAC,EAAE,CAAC,EAAE,CAAC,EACP,CAAC,EAAE,CAAC,EAAE,CAAC;QAEP;QACA,CAAC,EAAE,CAAC,EAAE,CAAC,EACP,CAAC,EAAE,CAAC,EAAE,CAAC,EAEP,CAAC,EAAE,CAAC,EAAE,CAAC,EACP,CAAC,EAAE,CAAC,EAAE,CAAC,CAEN,CAAC;MAEJ;MAEA,IAAKd,aAAa,CAACe,MAAM,GAAG,KAAK,EAAG;QAEnCH,UAAU,GAAG,IAAII,WAAW,CAAEH,OAAO,CAACE,MAAM,GAAGnB,WAAY,CAAC;MAE7D,CAAC,MAAM;QAENgB,UAAU,GAAG,IAAIK,WAAW,CAAEJ,OAAO,CAACE,MAAM,GAAGnB,WAAY,CAAC;MAE7D;MAEA,MAAMsB,WAAW,GAAGL,OAAO,CAACE,MAAM;MAClC,KAAM,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,WAAW,EAAEuB,CAAC,EAAG,EAAG;QAExC,MAAMC,SAAS,GAAGD,CAAC,GAAG,CAAC;QACvB,MAAME,WAAW,GAAGF,CAAC,GAAGD,WAAW;QACnC,KAAM,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,WAAW,EAAEI,CAAC,EAAG,EAAG;UAExCV,UAAU,CAAES,WAAW,GAAGC,CAAC,CAAE,GAAGF,SAAS,GAAGP,OAAO,CAAES,CAAC,CAAE;QAEzD;MAED;;MAEA;MACApC,QAAQ,CAACqC,QAAQ,CAChB,IAAIvD,eAAe,CAAE4C,UAAU,EAAE,CAAC,EAAE,KAAM,CAC3C,CAAC;MACD1B,QAAQ,CAACsC,YAAY,CACpB,UAAU,EACV,IAAIxD,eAAe,CAAEgC,aAAa,EAAE,CAAC,EAAE,KAAM,CAC9C,CAAC;MACD,IAAI,CAACN,OAAO,GAAG,IAAI;IAEpB;EAED;AAED;AAEA,MAAM+B,iBAAiB,SAASvD,KAAK,CAAC;EAErC,IAAIwD,KAAKA,CAAA,EAAG;IAEX,OAAO,IAAI,CAACC,YAAY,CAACD,KAAK;EAE/B;EAEA,IAAIE,OAAOA,CAAA,EAAG;IAEb,OAAO,IAAI,CAACD,YAAY,CAACC,OAAO;EAEjC;EAEA,IAAIA,OAAOA,CAAEC,CAAC,EAAG;IAEhB,IAAI,CAACF,YAAY,CAACC,OAAO,GAAGC,CAAC;IAC7B,IAAI,CAACC,YAAY,CAACF,OAAO,GAAGC,CAAC;EAE9B;EAEAhD,WAAWA,CAAEC,IAAI,EAAEE,KAAK,GAAG,EAAE,EAAG;IAE/B,KAAK,CAAC,CAAC;IAEP,IAAI,CAACG,IAAI,GAAG,mBAAmB;IAC/B,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACM,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACV,YAAY,GAAG,IAAI;IACxB,IAAI,CAACqD,MAAM,GAAG,EAAE;IAEhB,MAAMJ,YAAY,GAAG,IAAI5D,iBAAiB,CAAE;MAC3C2D,KAAK,EAAE,QAAQ;MACfM,WAAW,EAAE,IAAI;MACjBJ,OAAO,EAAE,GAAG;MACZK,UAAU,EAAE;IACb,CAAE,CAAC;IAEH,MAAMH,YAAY,GAAG,IAAI3D,iBAAiB,CAAE;MAC3CuD,KAAK,EAAE,QAAQ;MACfM,WAAW,EAAE,IAAI;MACjBJ,OAAO,EAAE,GAAG;MACZK,UAAU,EAAE;IACb,CAAE,CAAC;IAEHH,YAAY,CAACJ,KAAK,GAAGC,YAAY,CAACD,KAAK;IAEvC,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACG,YAAY,GAAGA,YAAY;IAEhC,IAAI,CAACvC,MAAM,CAAC,CAAC;EAEd;EAEAA,MAAMA,CAAA,EAAG;IAER,MAAM2C,GAAG,GAAG,IAAI,CAACpD,IAAI,CAACI,QAAQ,CAACM,UAAU;IACzC,MAAM2C,UAAU,GAAGD,GAAG,GAAGA,GAAG,CAACH,MAAM,CAAChB,MAAM,GAAG,CAAC;IAC9C,OAAQ,IAAI,CAACgB,MAAM,CAAChB,MAAM,GAAGoB,UAAU,EAAG;MAEzC,MAAMC,IAAI,GAAG,IAAI,CAACL,MAAM,CAACM,GAAG,CAAC,CAAC;MAC9BD,IAAI,CAAClD,QAAQ,CAACO,OAAO,CAAC,CAAC;MACvB,IAAI,CAAC6C,MAAM,CAAEF,IAAK,CAAC;IAEpB;IAEA,KAAM,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,UAAU,EAAEhB,CAAC,EAAG,EAAG;MAEvC,IAAKA,CAAC,IAAI,IAAI,CAACY,MAAM,CAAChB,MAAM,EAAG;QAE9B,MAAMqB,IAAI,GAAG,IAAI5D,qBAAqB,CAAE,IAAI,CAACM,IAAI,EAAE,IAAI,CAAC6C,YAAY,EAAE,IAAI,CAAC3C,KAAK,EAAEmC,CAAE,CAAC;QACrF,IAAI,CAACoB,GAAG,CAAEH,IAAK,CAAC;QAChB,IAAI,CAACL,MAAM,CAACS,IAAI,CAAEJ,IAAK,CAAC;MAEzB;MAEA,MAAMA,IAAI,GAAG,IAAI,CAACL,MAAM,CAAEZ,CAAC,CAAE;MAC7BiB,IAAI,CAACpD,KAAK,GAAG,IAAI,CAACA,KAAK;MACvBoD,IAAI,CAACtD,IAAI,GAAG,IAAI,CAACA,IAAI;MACrBsD,IAAI,CAAChD,cAAc,GAAG,IAAI,CAACA,cAAc;MACzCgD,IAAI,CAAC1D,YAAY,GAAG,IAAI,CAACA,YAAY;MACrC0D,IAAI,CAACrD,QAAQ,GAAG,IAAI,CAACL,YAAY,GAAG,IAAI,CAACiD,YAAY,GAAG,IAAI,CAACG,YAAY;MACzEM,IAAI,CAAC7C,MAAM,CAAC,CAAC;IAEd;EAED;EAEAkD,iBAAiBA,CAAE,GAAGC,IAAI,EAAG;IAE5B,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAE,IAAI,CAAC9D,IAAI,CAAC6D,QAAS,CAAC;IACxC,IAAI,CAACE,QAAQ,CAACD,IAAI,CAAE,IAAI,CAAC9D,IAAI,CAAC+D,QAAS,CAAC;IACxC,IAAI,CAACC,KAAK,CAACF,IAAI,CAAE,IAAI,CAAC9D,IAAI,CAACgE,KAAM,CAAC;IAElC,KAAK,CAACL,iBAAiB,CAAE,GAAGC,IAAK,CAAC;EAEnC;EAEAE,IAAIA,CAAEG,MAAM,EAAG;IAEd,IAAI,CAAC/D,KAAK,GAAG+D,MAAM,CAAC/D,KAAK;IACzB,IAAI,CAACF,IAAI,GAAGiE,MAAM,CAACjE,IAAI;EAExB;EAEAkE,KAAKA,CAAA,EAAG;IAEP,OAAO,IAAIvB,iBAAiB,CAAE,IAAI,CAAC3C,IAAI,EAAE,IAAI,CAACE,KAAM,CAAC;EAEtD;EAEAS,OAAOA,CAAA,EAAG;IAET,IAAI,CAACkC,YAAY,CAAClC,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACqC,YAAY,CAACrC,OAAO,CAAC,CAAC;IAE3B,MAAMwD,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,KAAM,IAAI9B,CAAC,GAAG,CAAC,EAAE+B,CAAC,GAAGD,QAAQ,CAAClC,MAAM,EAAEI,CAAC,GAAG+B,CAAC,EAAE/B,CAAC,EAAG,EAAG;MAEnD8B,QAAQ,CAAE9B,CAAC,CAAE,CAACjC,QAAQ,CAACO,OAAO,CAAC,CAAC;IAEjC;EAED;AAED;AAGA,SAASgC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}