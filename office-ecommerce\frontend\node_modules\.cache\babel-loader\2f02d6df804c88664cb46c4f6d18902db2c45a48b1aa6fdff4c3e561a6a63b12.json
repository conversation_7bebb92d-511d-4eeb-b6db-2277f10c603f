{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Text as Text$1, preloadFont } from 'troika-three-text';\nimport { useThree } from '@react-three/fiber';\nimport { suspend } from 'suspend-react';\n\n// eslint-disable-next-line prettier/prettier\nconst Text = /*#__PURE__*/React.forwardRef(({\n  sdfGlyphSize = 64,\n  anchorX = 'center',\n  anchorY = 'middle',\n  font,\n  fontSize = 1,\n  children,\n  characters,\n  onSync,\n  ...props\n}, ref) => {\n  const invalidate = useThree(({\n    invalidate\n  }) => invalidate);\n  const [troikaMesh] = React.useState(() => new Text$1());\n  const [nodes, text] = React.useMemo(() => {\n    const n = [];\n    let t = '';\n    React.Children.forEach(children, child => {\n      if (typeof child === 'string' || typeof child === 'number') {\n        t += child;\n      } else {\n        n.push(child);\n      }\n    });\n    return [n, t];\n  }, [children]);\n  suspend(() => new Promise(res => preloadFont({\n    font,\n    characters\n  }, res)), ['troika-text', font, characters]);\n  React.useLayoutEffect(() => void troikaMesh.sync(() => {\n    invalidate();\n    if (onSync) onSync(troikaMesh);\n  }));\n  React.useEffect(() => {\n    return () => troikaMesh.dispose();\n  }, [troikaMesh]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: troikaMesh,\n    ref: ref,\n    font: font,\n    text: text,\n    anchorX: anchorX,\n    anchorY: anchorY,\n    fontSize: fontSize,\n    sdfGlyphSize: sdfGlyphSize\n  }, props), nodes);\n});\nexport { Text };", "map": {"version": 3, "names": ["_extends", "React", "Text", "Text$1", "preloadFont", "useThree", "suspend", "forwardRef", "sdfGlyphSize", "anchorX", "anchorY", "font", "fontSize", "children", "characters", "onSync", "props", "ref", "invalidate", "troikaMesh", "useState", "nodes", "text", "useMemo", "n", "t", "Children", "for<PERSON>ach", "child", "push", "Promise", "res", "useLayoutEffect", "sync", "useEffect", "dispose", "createElement", "object"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Text.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Text as Text$1, preloadFont } from 'troika-three-text';\nimport { useThree } from '@react-three/fiber';\nimport { suspend } from 'suspend-react';\n\n// eslint-disable-next-line prettier/prettier\nconst Text = /*#__PURE__*/React.forwardRef(({\n  sdfGlyphSize = 64,\n  anchorX = 'center',\n  anchorY = 'middle',\n  font,\n  fontSize = 1,\n  children,\n  characters,\n  onSync,\n  ...props\n}, ref) => {\n  const invalidate = useThree(({\n    invalidate\n  }) => invalidate);\n  const [troikaMesh] = React.useState(() => new Text$1());\n  const [nodes, text] = React.useMemo(() => {\n    const n = [];\n    let t = '';\n    React.Children.forEach(children, child => {\n      if (typeof child === 'string' || typeof child === 'number') {\n        t += child;\n      } else {\n        n.push(child);\n      }\n    });\n    return [n, t];\n  }, [children]);\n  suspend(() => new Promise(res => preloadFont({\n    font,\n    characters\n  }, res)), ['troika-text', font, characters]);\n  React.useLayoutEffect(() => void troikaMesh.sync(() => {\n    invalidate();\n    if (onSync) onSync(troikaMesh);\n  }));\n  React.useEffect(() => {\n    return () => troikaMesh.dispose();\n  }, [troikaMesh]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: troikaMesh,\n    ref: ref,\n    font: font,\n    text: text,\n    anchorX: anchorX,\n    anchorY: anchorY,\n    fontSize: fontSize,\n    sdfGlyphSize: sdfGlyphSize\n  }, props), nodes);\n});\n\nexport { Text };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,IAAIC,MAAM,EAAEC,WAAW,QAAQ,mBAAmB;AAC/D,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,OAAO,QAAQ,eAAe;;AAEvC;AACA,MAAMJ,IAAI,GAAG,aAAaD,KAAK,CAACM,UAAU,CAAC,CAAC;EAC1CC,YAAY,GAAG,EAAE;EACjBC,OAAO,GAAG,QAAQ;EAClBC,OAAO,GAAG,QAAQ;EAClBC,IAAI;EACJC,QAAQ,GAAG,CAAC;EACZC,QAAQ;EACRC,UAAU;EACVC,MAAM;EACN,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,UAAU,GAAGb,QAAQ,CAAC,CAAC;IAC3Ba;EACF,CAAC,KAAKA,UAAU,CAAC;EACjB,MAAM,CAACC,UAAU,CAAC,GAAGlB,KAAK,CAACmB,QAAQ,CAAC,MAAM,IAAIjB,MAAM,CAAC,CAAC,CAAC;EACvD,MAAM,CAACkB,KAAK,EAAEC,IAAI,CAAC,GAAGrB,KAAK,CAACsB,OAAO,CAAC,MAAM;IACxC,MAAMC,CAAC,GAAG,EAAE;IACZ,IAAIC,CAAC,GAAG,EAAE;IACVxB,KAAK,CAACyB,QAAQ,CAACC,OAAO,CAACd,QAAQ,EAAEe,KAAK,IAAI;MACxC,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC1DH,CAAC,IAAIG,KAAK;MACZ,CAAC,MAAM;QACLJ,CAAC,CAACK,IAAI,CAACD,KAAK,CAAC;MACf;IACF,CAAC,CAAC;IACF,OAAO,CAACJ,CAAC,EAAEC,CAAC,CAAC;EACf,CAAC,EAAE,CAACZ,QAAQ,CAAC,CAAC;EACdP,OAAO,CAAC,MAAM,IAAIwB,OAAO,CAACC,GAAG,IAAI3B,WAAW,CAAC;IAC3CO,IAAI;IACJG;EACF,CAAC,EAAEiB,GAAG,CAAC,CAAC,EAAE,CAAC,aAAa,EAAEpB,IAAI,EAAEG,UAAU,CAAC,CAAC;EAC5Cb,KAAK,CAAC+B,eAAe,CAAC,MAAM,KAAKb,UAAU,CAACc,IAAI,CAAC,MAAM;IACrDf,UAAU,CAAC,CAAC;IACZ,IAAIH,MAAM,EAAEA,MAAM,CAACI,UAAU,CAAC;EAChC,CAAC,CAAC,CAAC;EACHlB,KAAK,CAACiC,SAAS,CAAC,MAAM;IACpB,OAAO,MAAMf,UAAU,CAACgB,OAAO,CAAC,CAAC;EACnC,CAAC,EAAE,CAAChB,UAAU,CAAC,CAAC;EAChB,OAAO,aAAalB,KAAK,CAACmC,aAAa,CAAC,WAAW,EAAEpC,QAAQ,CAAC;IAC5DqC,MAAM,EAAElB,UAAU;IAClBF,GAAG,EAAEA,GAAG;IACRN,IAAI,EAAEA,IAAI;IACVW,IAAI,EAAEA,IAAI;IACVb,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA,OAAO;IAChBE,QAAQ,EAAEA,QAAQ;IAClBJ,YAAY,EAAEA;EAChB,CAAC,EAAEQ,KAAK,CAAC,EAAEK,KAAK,CAAC;AACnB,CAAC,CAAC;AAEF,SAASnB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}