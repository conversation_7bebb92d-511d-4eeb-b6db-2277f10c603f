{"ast": null, "code": "// Mock API service for frontend-only application\n// This replaces the backend API with local mock data\n\n// Mock data for products\nconst mockProducts = [{\n  id: 1,\n  name: \"Executive Office Table\",\n  description: \"Premium executive office table with modern design and customizable features.\",\n  basePrice: 500,\n  category: \"Tables\",\n  image: \"/images/table-1.jpg\",\n  features: [\"Adjustable height\", \"Premium materials\", \"Modern design\", \"Customizable\"]\n}, {\n  id: 2,\n  name: \"Ergonomic Office Chair\",\n  description: \"Comfortable ergonomic office chair with lumbar support and adjustable features.\",\n  basePrice: 200,\n  category: \"Chairs\",\n  image: \"/images/chair-1.jpg\",\n  features: [\"Ergonomic design\", \"Lumbar support\", \"Adjustable height\", \"Breathable mesh\"]\n}, {\n  id: 3,\n  name: \"Storage Cabinet\",\n  description: \"Modern storage cabinet with multiple compartments and secure locking system.\",\n  basePrice: 800,\n  category: \"Storage\",\n  image: \"/images/cabinet-1.jpg\",\n  features: [\"Multiple compartments\", \"Secure locks\", \"Modern design\", \"Durable materials\"]\n}];\n\n// Mock categories\nconst mockCategories = [{\n  id: 1,\n  name: \"Tables\",\n  description: \"Office tables and desks\"\n}, {\n  id: 2,\n  name: \"Chairs\",\n  description: \"Office chairs and seating\"\n}, {\n  id: 3,\n  name: \"Storage\",\n  description: \"Storage solutions and cabinets\"\n}, {\n  id: 4,\n  name: \"Workstations\",\n  description: \"Complete workstation setups\"\n}];\n\n// Mock API functions\nconst api = {\n  // Get all products\n  getProducts: () => {\n    return Promise.resolve({\n      success: true,\n      data: mockProducts\n    });\n  },\n  // Get product by ID\n  getProduct: id => {\n    const product = mockProducts.find(p => p.id === parseInt(id));\n    return Promise.resolve({\n      success: true,\n      data: product || null\n    });\n  },\n  // Get categories\n  getCategories: () => {\n    return Promise.resolve({\n      success: true,\n      data: mockCategories\n    });\n  }\n};\n\n// Enhanced Products API for admin functionality\nconst productsApi = {\n  // Get products with pagination and filtering\n  getProducts: (params = {}) => {\n    const {\n      page = 1,\n      limit = 20,\n      search = '',\n      category = '',\n      status = '',\n      sortBy = 'ProductName',\n      sortDirection = 'ASC'\n    } = params;\n\n    // Simulate API delay\n    return new Promise(resolve => {\n      setTimeout(() => {\n        let filteredProducts = [...mockProducts];\n\n        // Apply search filter\n        if (search) {\n          filteredProducts = filteredProducts.filter(product => product.name.toLowerCase().includes(search.toLowerCase()) || product.description.toLowerCase().includes(search.toLowerCase()));\n        }\n\n        // Apply category filter\n        if (category) {\n          filteredProducts = filteredProducts.filter(product => product.category === category);\n        }\n\n        // Apply status filter (mock all as Active)\n        if (status && status !== 'Active') {\n          filteredProducts = [];\n        }\n\n        // Apply sorting\n        filteredProducts.sort((a, b) => {\n          let aValue = a.name;\n          let bValue = b.name;\n          if (sortBy === 'BasePrice') {\n            aValue = a.basePrice;\n            bValue = b.basePrice;\n          }\n          if (sortDirection === 'DESC') {\n            return bValue > aValue ? 1 : -1;\n          }\n          return aValue > bValue ? 1 : -1;\n        });\n\n        // Apply pagination\n        const startIndex = (page - 1) * limit;\n        const endIndex = startIndex + limit;\n        const paginatedProducts = filteredProducts.slice(startIndex, endIndex);\n\n        // Transform to match backend structure\n        const transformedProducts = paginatedProducts.map(product => ({\n          ProductID: product.id,\n          ProductName: product.name,\n          ProductCode: `PRD-${product.id.toString().padStart(3, '0')}`,\n          CategoryName: product.category,\n          BasePrice: product.basePrice,\n          Status: 'Active',\n          ImageCount: 1,\n          ModelCount: 1,\n          UpdatedAt: new Date().toISOString()\n        }));\n        resolve({\n          success: true,\n          data: transformedProducts,\n          pagination: {\n            currentPage: page,\n            totalPages: Math.ceil(filteredProducts.length / limit),\n            totalCount: filteredProducts.length,\n            pageSize: limit\n          }\n        });\n      }, 500);\n    });\n  },\n  // Get product by ID with full details\n  getProductById: id => {\n    return new Promise(resolve => {\n      setTimeout(() => {\n        var _mockCategories$find;\n        const product = mockProducts.find(p => p.id === parseInt(id));\n        if (!product) {\n          resolve({\n            success: false,\n            message: 'Product not found'\n          });\n          return;\n        }\n\n        // Transform to match backend structure with full details\n        const transformedProduct = {\n          product: {\n            ProductID: product.id,\n            ProductName: product.name,\n            ProductCode: `PRD-${product.id.toString().padStart(3, '0')}`,\n            CategoryName: product.category,\n            CategoryID: ((_mockCategories$find = mockCategories.find(c => c.name === product.category)) === null || _mockCategories$find === void 0 ? void 0 : _mockCategories$find.id) || 1,\n            Description: product.description,\n            BasePrice: product.basePrice,\n            Status: 'Active',\n            IsCustomizable: true,\n            IsActive: true,\n            CreatedAt: new Date().toISOString(),\n            UpdatedAt: new Date().toISOString(),\n            CreatedBy: 'Admin User',\n            UpdatedBy: 'Admin User'\n          },\n          models: [{\n            FileName: `${product.name.toLowerCase().replace(/\\s+/g, '-')}.glb`,\n            FileSize: 2048000,\n            IsPrimary: true\n          }],\n          images: [{\n            FileName: `${product.name.toLowerCase().replace(/\\s+/g, '-')}.jpg`,\n            FileSize: 512000,\n            IsPrimary: true,\n            AltText: product.name\n          }],\n          components: [],\n          colors: [{\n            ColorName: 'Natural Wood',\n            HexCode: '#D2B48C',\n            PriceModifier: 0\n          }, {\n            ColorName: 'Dark Walnut',\n            HexCode: '#5D4037',\n            PriceModifier: 50\n          }],\n          auditTrail: [{\n            Action: 'Created',\n            ChangeDate: new Date().toISOString(),\n            ChangedBy: 'Admin User',\n            ChangeDetails: 'Product created'\n          }]\n        };\n        resolve({\n          success: true,\n          data: transformedProduct\n        });\n      }, 300);\n    });\n  },\n  // Create or update product\n  createOrUpdateProduct: async productData => {\n    try {\n      const url = productData.ProductID ? `/api/products/${productData.ProductID}` : '/api/products';\n      const method = productData.ProductID ? 'PUT' : 'POST';\n      const response = await apiClient[method.toLowerCase()](url, productData);\n      return response;\n    } catch (error) {\n      console.error('Error creating/updating product:', error);\n      throw error;\n    }\n  },\n  // Delete product\n  deleteProduct: productId => {\n    return new Promise(resolve => {\n      setTimeout(() => {\n        resolve({\n          success: true,\n          message: 'Product deleted successfully'\n        });\n      }, 500);\n    });\n  },\n  // Upload 3D model\n  uploadModel: async (productId, formData) => {\n    try {\n      const response = await apiClient.client.post(`/api/products/${productId}/models`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error uploading 3D model:', error);\n      throw error;\n    }\n  },\n  // Upload images\n  uploadImages: async (productId, formData) => {\n    try {\n      const response = await apiClient.client.post(`/api/products/${productId}/images`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error uploading images:', error);\n      throw error;\n    }\n  },\n  // Get categories\n  getCategories: () => {\n    return Promise.resolve({\n      success: true,\n      data: mockCategories\n    });\n  }\n};\n\n// Original API with authentication\nconst authApi = {\n  // Mock authentication with admin support\n  login: credentials => {\n    // Check for admin credentials\n    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {\n      return Promise.resolve({\n        success: true,\n        data: {\n          token: 'mock-admin-jwt-token',\n          user: {\n            id: 1,\n            firstName: 'Admin',\n            lastName: 'User',\n            email: credentials.email,\n            role: 'Admin'\n          }\n        }\n      });\n    }\n\n    // Check for employee credentials\n    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {\n      return Promise.resolve({\n        success: true,\n        data: {\n          token: 'mock-employee-jwt-token',\n          user: {\n            id: 2,\n            firstName: 'Manager',\n            lastName: 'User',\n            email: credentials.email,\n            role: 'Employee'\n          }\n        }\n      });\n    }\n\n    // Regular customer login\n    return Promise.resolve({\n      success: true,\n      data: {\n        token: 'mock-jwt-token',\n        user: {\n          id: 3,\n          firstName: 'Demo',\n          lastName: 'User',\n          email: credentials.email,\n          role: 'Customer'\n        }\n      }\n    });\n  },\n  // Mock registration (always succeeds)\n  register: userData => {\n    return Promise.resolve({\n      success: true,\n      data: {\n        token: 'mock-jwt-token',\n        user: {\n          id: 1,\n          name: userData.name,\n          email: userData.email\n        }\n      }\n    });\n  }\n};\n\n// Combine APIs\nconst combinedApi = {\n  ...api,\n  ...authApi\n};\nexport default combinedApi;\nexport { productsApi };", "map": {"version": 3, "names": ["mockProducts", "id", "name", "description", "basePrice", "category", "image", "features", "mockCategories", "api", "getProducts", "Promise", "resolve", "success", "data", "getProduct", "product", "find", "p", "parseInt", "getCategories", "productsApi", "params", "page", "limit", "search", "status", "sortBy", "sortDirection", "setTimeout", "filteredProducts", "filter", "toLowerCase", "includes", "sort", "a", "b", "aValue", "bValue", "startIndex", "endIndex", "paginatedProducts", "slice", "transformedProducts", "map", "ProductID", "ProductName", "ProductCode", "toString", "padStart", "CategoryName", "BasePrice", "Status", "ImageCount", "ModelCount", "UpdatedAt", "Date", "toISOString", "pagination", "currentPage", "totalPages", "Math", "ceil", "length", "totalCount", "pageSize", "getProductById", "_mockCategories$find", "message", "transformedProduct", "CategoryID", "c", "Description", "IsCustomizable", "IsActive", "CreatedAt", "CreatedBy", "UpdatedBy", "models", "FileName", "replace", "FileSize", "IsPrimary", "images", "AltText", "components", "colors", "ColorName", "HexCode", "PriceModifier", "auditTrail", "Action", "ChangeDate", "ChangedBy", "ChangeDetails", "createOrUpdateProduct", "productData", "url", "method", "response", "apiClient", "error", "console", "deleteProduct", "productId", "uploadModel", "formData", "client", "post", "headers", "uploadImages", "authApi", "login", "credentials", "email", "password", "token", "user", "firstName", "lastName", "role", "register", "userData", "combinedApi"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/services/api.js"], "sourcesContent": ["// Mock API service for frontend-only application\n// This replaces the backend API with local mock data\n\n// Mock data for products\nconst mockProducts = [\n  {\n    id: 1,\n    name: \"Executive Office Table\",\n    description: \"Premium executive office table with modern design and customizable features.\",\n    basePrice: 500,\n    category: \"Tables\",\n    image: \"/images/table-1.jpg\",\n    features: [\"Adjustable height\", \"Premium materials\", \"Modern design\", \"Customizable\"]\n  },\n  {\n    id: 2,\n    name: \"Ergonomic Office Chair\",\n    description: \"Comfortable ergonomic office chair with lumbar support and adjustable features.\",\n    basePrice: 200,\n    category: \"Chairs\",\n    image: \"/images/chair-1.jpg\",\n    features: [\"Ergonomic design\", \"Lumbar support\", \"Adjustable height\", \"Breathable mesh\"]\n  },\n  {\n    id: 3,\n    name: \"Storage Cabinet\",\n    description: \"Modern storage cabinet with multiple compartments and secure locking system.\",\n    basePrice: 800,\n    category: \"Storage\",\n    image: \"/images/cabinet-1.jpg\",\n    features: [\"Multiple compartments\", \"Secure locks\", \"Modern design\", \"Durable materials\"]\n  }\n];\n\n// Mock categories\nconst mockCategories = [\n  { id: 1, name: \"Tables\", description: \"Office tables and desks\" },\n  { id: 2, name: \"Chairs\", description: \"Office chairs and seating\" },\n  { id: 3, name: \"Storage\", description: \"Storage solutions and cabinets\" },\n  { id: 4, name: \"Workstations\", description: \"Complete workstation setups\" }\n];\n\n// Mock API functions\nconst api = {\n  // Get all products\n  getProducts: () => {\n    return Promise.resolve({\n      success: true,\n      data: mockProducts\n    });\n  },\n\n  // Get product by ID\n  getProduct: (id) => {\n    const product = mockProducts.find(p => p.id === parseInt(id));\n    return Promise.resolve({\n      success: true,\n      data: product || null\n    });\n  },\n\n  // Get categories\n  getCategories: () => {\n    return Promise.resolve({\n      success: true,\n      data: mockCategories\n    });\n  }\n};\n\n// Enhanced Products API for admin functionality\nconst productsApi = {\n  // Get products with pagination and filtering\n  getProducts: (params = {}) => {\n    const {\n      page = 1,\n      limit = 20,\n      search = '',\n      category = '',\n      status = '',\n      sortBy = 'ProductName',\n      sortDirection = 'ASC'\n    } = params;\n\n    // Simulate API delay\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        let filteredProducts = [...mockProducts];\n\n        // Apply search filter\n        if (search) {\n          filteredProducts = filteredProducts.filter(product =>\n            product.name.toLowerCase().includes(search.toLowerCase()) ||\n            product.description.toLowerCase().includes(search.toLowerCase())\n          );\n        }\n\n        // Apply category filter\n        if (category) {\n          filteredProducts = filteredProducts.filter(product =>\n            product.category === category\n          );\n        }\n\n        // Apply status filter (mock all as Active)\n        if (status && status !== 'Active') {\n          filteredProducts = [];\n        }\n\n        // Apply sorting\n        filteredProducts.sort((a, b) => {\n          let aValue = a.name;\n          let bValue = b.name;\n\n          if (sortBy === 'BasePrice') {\n            aValue = a.basePrice;\n            bValue = b.basePrice;\n          }\n\n          if (sortDirection === 'DESC') {\n            return bValue > aValue ? 1 : -1;\n          }\n          return aValue > bValue ? 1 : -1;\n        });\n\n        // Apply pagination\n        const startIndex = (page - 1) * limit;\n        const endIndex = startIndex + limit;\n        const paginatedProducts = filteredProducts.slice(startIndex, endIndex);\n\n        // Transform to match backend structure\n        const transformedProducts = paginatedProducts.map(product => ({\n          ProductID: product.id,\n          ProductName: product.name,\n          ProductCode: `PRD-${product.id.toString().padStart(3, '0')}`,\n          CategoryName: product.category,\n          BasePrice: product.basePrice,\n          Status: 'Active',\n          ImageCount: 1,\n          ModelCount: 1,\n          UpdatedAt: new Date().toISOString()\n        }));\n\n        resolve({\n          success: true,\n          data: transformedProducts,\n          pagination: {\n            currentPage: page,\n            totalPages: Math.ceil(filteredProducts.length / limit),\n            totalCount: filteredProducts.length,\n            pageSize: limit\n          }\n        });\n      }, 500);\n    });\n  },\n\n  // Get product by ID with full details\n  getProductById: (id) => {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const product = mockProducts.find(p => p.id === parseInt(id));\n\n        if (!product) {\n          resolve({\n            success: false,\n            message: 'Product not found'\n          });\n          return;\n        }\n\n        // Transform to match backend structure with full details\n        const transformedProduct = {\n          product: {\n            ProductID: product.id,\n            ProductName: product.name,\n            ProductCode: `PRD-${product.id.toString().padStart(3, '0')}`,\n            CategoryName: product.category,\n            CategoryID: mockCategories.find(c => c.name === product.category)?.id || 1,\n            Description: product.description,\n            BasePrice: product.basePrice,\n            Status: 'Active',\n            IsCustomizable: true,\n            IsActive: true,\n            CreatedAt: new Date().toISOString(),\n            UpdatedAt: new Date().toISOString(),\n            CreatedBy: 'Admin User',\n            UpdatedBy: 'Admin User'\n          },\n          models: [\n            {\n              FileName: `${product.name.toLowerCase().replace(/\\s+/g, '-')}.glb`,\n              FileSize: 2048000,\n              IsPrimary: true\n            }\n          ],\n          images: [\n            {\n              FileName: `${product.name.toLowerCase().replace(/\\s+/g, '-')}.jpg`,\n              FileSize: 512000,\n              IsPrimary: true,\n              AltText: product.name\n            }\n          ],\n          components: [],\n          colors: [\n            {\n              ColorName: 'Natural Wood',\n              HexCode: '#D2B48C',\n              PriceModifier: 0\n            },\n            {\n              ColorName: 'Dark Walnut',\n              HexCode: '#5D4037',\n              PriceModifier: 50\n            }\n          ],\n          auditTrail: [\n            {\n              Action: 'Created',\n              ChangeDate: new Date().toISOString(),\n              ChangedBy: 'Admin User',\n              ChangeDetails: 'Product created'\n            }\n          ]\n        };\n\n        resolve({\n          success: true,\n          data: transformedProduct\n        });\n      }, 300);\n    });\n  },\n\n  // Create or update product\n  createOrUpdateProduct: async (productData) => {\n    try {\n      const url = productData.ProductID\n        ? `/api/products/${productData.ProductID}`\n        : '/api/products';\n\n      const method = productData.ProductID ? 'PUT' : 'POST';\n\n      const response = await apiClient[method.toLowerCase()](url, productData);\n      return response;\n    } catch (error) {\n      console.error('Error creating/updating product:', error);\n      throw error;\n    }\n  },\n\n  // Delete product\n  deleteProduct: (productId) => {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        resolve({\n          success: true,\n          message: 'Product deleted successfully'\n        });\n      }, 500);\n    });\n  },\n\n  // Upload 3D model\n  uploadModel: async (productId, formData) => {\n    try {\n      const response = await apiClient.client.post(`/api/products/${productId}/models`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error uploading 3D model:', error);\n      throw error;\n    }\n  },\n\n  // Upload images\n  uploadImages: async (productId, formData) => {\n    try {\n      const response = await apiClient.client.post(`/api/products/${productId}/images`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error uploading images:', error);\n      throw error;\n    }\n  },\n\n  // Get categories\n  getCategories: () => {\n    return Promise.resolve({\n      success: true,\n      data: mockCategories\n    });\n  }\n};\n\n// Original API with authentication\nconst authApi = {\n  // Mock authentication with admin support\n  login: (credentials) => {\n    // Check for admin credentials\n    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {\n      return Promise.resolve({\n        success: true,\n        data: {\n          token: 'mock-admin-jwt-token',\n          user: {\n            id: 1,\n            firstName: 'Admin',\n            lastName: 'User',\n            email: credentials.email,\n            role: 'Admin'\n          }\n        }\n      });\n    }\n\n    // Check for employee credentials\n    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {\n      return Promise.resolve({\n        success: true,\n        data: {\n          token: 'mock-employee-jwt-token',\n          user: {\n            id: 2,\n            firstName: 'Manager',\n            lastName: 'User',\n            email: credentials.email,\n            role: 'Employee'\n          }\n        }\n      });\n    }\n\n    // Regular customer login\n    return Promise.resolve({\n      success: true,\n      data: {\n        token: 'mock-jwt-token',\n        user: {\n          id: 3,\n          firstName: 'Demo',\n          lastName: 'User',\n          email: credentials.email,\n          role: 'Customer'\n        }\n      }\n    });\n  },\n\n  // Mock registration (always succeeds)\n  register: (userData) => {\n    return Promise.resolve({\n      success: true,\n      data: {\n        token: 'mock-jwt-token',\n        user: {\n          id: 1,\n          name: userData.name,\n          email: userData.email\n        }\n      }\n    });\n  }\n};\n\n// Combine APIs\nconst combinedApi = {\n  ...api,\n  ...authApi\n};\n\nexport default combinedApi;\nexport { productsApi };\n"], "mappings": "AAAA;AACA;;AAEA;AACA,MAAMA,YAAY,GAAG,CACnB;EACEC,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,wBAAwB;EAC9BC,WAAW,EAAE,8EAA8E;EAC3FC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,QAAQ;EAClBC,KAAK,EAAE,qBAAqB;EAC5BC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,eAAe,EAAE,cAAc;AACtF,CAAC,EACD;EACEN,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,wBAAwB;EAC9BC,WAAW,EAAE,iFAAiF;EAC9FC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,QAAQ;EAClBC,KAAK,EAAE,qBAAqB;EAC5BC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,iBAAiB;AACzF,CAAC,EACD;EACEN,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,iBAAiB;EACvBC,WAAW,EAAE,8EAA8E;EAC3FC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,uBAAuB;EAC9BC,QAAQ,EAAE,CAAC,uBAAuB,EAAE,cAAc,EAAE,eAAe,EAAE,mBAAmB;AAC1F,CAAC,CACF;;AAED;AACA,MAAMC,cAAc,GAAG,CACrB;EAAEP,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,QAAQ;EAAEC,WAAW,EAAE;AAA0B,CAAC,EACjE;EAAEF,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,QAAQ;EAAEC,WAAW,EAAE;AAA4B,CAAC,EACnE;EAAEF,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,SAAS;EAAEC,WAAW,EAAE;AAAiC,CAAC,EACzE;EAAEF,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,cAAc;EAAEC,WAAW,EAAE;AAA8B,CAAC,CAC5E;;AAED;AACA,MAAMM,GAAG,GAAG;EACV;EACAC,WAAW,EAAEA,CAAA,KAAM;IACjB,OAAOC,OAAO,CAACC,OAAO,CAAC;MACrBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEd;IACR,CAAC,CAAC;EACJ,CAAC;EAED;EACAe,UAAU,EAAGd,EAAE,IAAK;IAClB,MAAMe,OAAO,GAAGhB,YAAY,CAACiB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjB,EAAE,KAAKkB,QAAQ,CAAClB,EAAE,CAAC,CAAC;IAC7D,OAAOU,OAAO,CAACC,OAAO,CAAC;MACrBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEE,OAAO,IAAI;IACnB,CAAC,CAAC;EACJ,CAAC;EAED;EACAI,aAAa,EAAEA,CAAA,KAAM;IACnB,OAAOT,OAAO,CAACC,OAAO,CAAC;MACrBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEN;IACR,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,MAAMa,WAAW,GAAG;EAClB;EACAX,WAAW,EAAEA,CAACY,MAAM,GAAG,CAAC,CAAC,KAAK;IAC5B,MAAM;MACJC,IAAI,GAAG,CAAC;MACRC,KAAK,GAAG,EAAE;MACVC,MAAM,GAAG,EAAE;MACXpB,QAAQ,GAAG,EAAE;MACbqB,MAAM,GAAG,EAAE;MACXC,MAAM,GAAG,aAAa;MACtBC,aAAa,GAAG;IAClB,CAAC,GAAGN,MAAM;;IAEV;IACA,OAAO,IAAIX,OAAO,CAAEC,OAAO,IAAK;MAC9BiB,UAAU,CAAC,MAAM;QACf,IAAIC,gBAAgB,GAAG,CAAC,GAAG9B,YAAY,CAAC;;QAExC;QACA,IAAIyB,MAAM,EAAE;UACVK,gBAAgB,GAAGA,gBAAgB,CAACC,MAAM,CAACf,OAAO,IAChDA,OAAO,CAACd,IAAI,CAAC8B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACR,MAAM,CAACO,WAAW,CAAC,CAAC,CAAC,IACzDhB,OAAO,CAACb,WAAW,CAAC6B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACR,MAAM,CAACO,WAAW,CAAC,CAAC,CACjE,CAAC;QACH;;QAEA;QACA,IAAI3B,QAAQ,EAAE;UACZyB,gBAAgB,GAAGA,gBAAgB,CAACC,MAAM,CAACf,OAAO,IAChDA,OAAO,CAACX,QAAQ,KAAKA,QACvB,CAAC;QACH;;QAEA;QACA,IAAIqB,MAAM,IAAIA,MAAM,KAAK,QAAQ,EAAE;UACjCI,gBAAgB,GAAG,EAAE;QACvB;;QAEA;QACAA,gBAAgB,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UAC9B,IAAIC,MAAM,GAAGF,CAAC,CAACjC,IAAI;UACnB,IAAIoC,MAAM,GAAGF,CAAC,CAAClC,IAAI;UAEnB,IAAIyB,MAAM,KAAK,WAAW,EAAE;YAC1BU,MAAM,GAAGF,CAAC,CAAC/B,SAAS;YACpBkC,MAAM,GAAGF,CAAC,CAAChC,SAAS;UACtB;UAEA,IAAIwB,aAAa,KAAK,MAAM,EAAE;YAC5B,OAAOU,MAAM,GAAGD,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;UACjC;UACA,OAAOA,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;QACjC,CAAC,CAAC;;QAEF;QACA,MAAMC,UAAU,GAAG,CAAChB,IAAI,GAAG,CAAC,IAAIC,KAAK;QACrC,MAAMgB,QAAQ,GAAGD,UAAU,GAAGf,KAAK;QACnC,MAAMiB,iBAAiB,GAAGX,gBAAgB,CAACY,KAAK,CAACH,UAAU,EAAEC,QAAQ,CAAC;;QAEtE;QACA,MAAMG,mBAAmB,GAAGF,iBAAiB,CAACG,GAAG,CAAC5B,OAAO,KAAK;UAC5D6B,SAAS,EAAE7B,OAAO,CAACf,EAAE;UACrB6C,WAAW,EAAE9B,OAAO,CAACd,IAAI;UACzB6C,WAAW,EAAE,OAAO/B,OAAO,CAACf,EAAE,CAAC+C,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;UAC5DC,YAAY,EAAElC,OAAO,CAACX,QAAQ;UAC9B8C,SAAS,EAAEnC,OAAO,CAACZ,SAAS;UAC5BgD,MAAM,EAAE,QAAQ;UAChBC,UAAU,EAAE,CAAC;UACbC,UAAU,EAAE,CAAC;UACbC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH7C,OAAO,CAAC;UACNC,OAAO,EAAE,IAAI;UACbC,IAAI,EAAE6B,mBAAmB;UACzBe,UAAU,EAAE;YACVC,WAAW,EAAEpC,IAAI;YACjBqC,UAAU,EAAEC,IAAI,CAACC,IAAI,CAAChC,gBAAgB,CAACiC,MAAM,GAAGvC,KAAK,CAAC;YACtDwC,UAAU,EAAElC,gBAAgB,CAACiC,MAAM;YACnCE,QAAQ,EAAEzC;UACZ;QACF,CAAC,CAAC;MACJ,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ,CAAC;EAED;EACA0C,cAAc,EAAGjE,EAAE,IAAK;IACtB,OAAO,IAAIU,OAAO,CAAEC,OAAO,IAAK;MAC9BiB,UAAU,CAAC,MAAM;QAAA,IAAAsC,oBAAA;QACf,MAAMnD,OAAO,GAAGhB,YAAY,CAACiB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjB,EAAE,KAAKkB,QAAQ,CAAClB,EAAE,CAAC,CAAC;QAE7D,IAAI,CAACe,OAAO,EAAE;UACZJ,OAAO,CAAC;YACNC,OAAO,EAAE,KAAK;YACduD,OAAO,EAAE;UACX,CAAC,CAAC;UACF;QACF;;QAEA;QACA,MAAMC,kBAAkB,GAAG;UACzBrD,OAAO,EAAE;YACP6B,SAAS,EAAE7B,OAAO,CAACf,EAAE;YACrB6C,WAAW,EAAE9B,OAAO,CAACd,IAAI;YACzB6C,WAAW,EAAE,OAAO/B,OAAO,CAACf,EAAE,CAAC+C,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;YAC5DC,YAAY,EAAElC,OAAO,CAACX,QAAQ;YAC9BiE,UAAU,EAAE,EAAAH,oBAAA,GAAA3D,cAAc,CAACS,IAAI,CAACsD,CAAC,IAAIA,CAAC,CAACrE,IAAI,KAAKc,OAAO,CAACX,QAAQ,CAAC,cAAA8D,oBAAA,uBAArDA,oBAAA,CAAuDlE,EAAE,KAAI,CAAC;YAC1EuE,WAAW,EAAExD,OAAO,CAACb,WAAW;YAChCgD,SAAS,EAAEnC,OAAO,CAACZ,SAAS;YAC5BgD,MAAM,EAAE,QAAQ;YAChBqB,cAAc,EAAE,IAAI;YACpBC,QAAQ,EAAE,IAAI;YACdC,SAAS,EAAE,IAAInB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACnCF,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACnCmB,SAAS,EAAE,YAAY;YACvBC,SAAS,EAAE;UACb,CAAC;UACDC,MAAM,EAAE,CACN;YACEC,QAAQ,EAAE,GAAG/D,OAAO,CAACd,IAAI,CAAC8B,WAAW,CAAC,CAAC,CAACgD,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM;YAClEC,QAAQ,EAAE,OAAO;YACjBC,SAAS,EAAE;UACb,CAAC,CACF;UACDC,MAAM,EAAE,CACN;YACEJ,QAAQ,EAAE,GAAG/D,OAAO,CAACd,IAAI,CAAC8B,WAAW,CAAC,CAAC,CAACgD,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM;YAClEC,QAAQ,EAAE,MAAM;YAChBC,SAAS,EAAE,IAAI;YACfE,OAAO,EAAEpE,OAAO,CAACd;UACnB,CAAC,CACF;UACDmF,UAAU,EAAE,EAAE;UACdC,MAAM,EAAE,CACN;YACEC,SAAS,EAAE,cAAc;YACzBC,OAAO,EAAE,SAAS;YAClBC,aAAa,EAAE;UACjB,CAAC,EACD;YACEF,SAAS,EAAE,aAAa;YACxBC,OAAO,EAAE,SAAS;YAClBC,aAAa,EAAE;UACjB,CAAC,CACF;UACDC,UAAU,EAAE,CACV;YACEC,MAAM,EAAE,SAAS;YACjBC,UAAU,EAAE,IAAIpC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACpCoC,SAAS,EAAE,YAAY;YACvBC,aAAa,EAAE;UACjB,CAAC;QAEL,CAAC;QAEDlF,OAAO,CAAC;UACNC,OAAO,EAAE,IAAI;UACbC,IAAI,EAAEuD;QACR,CAAC,CAAC;MACJ,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ,CAAC;EAED;EACA0B,qBAAqB,EAAE,MAAOC,WAAW,IAAK;IAC5C,IAAI;MACF,MAAMC,GAAG,GAAGD,WAAW,CAACnD,SAAS,GAC7B,iBAAiBmD,WAAW,CAACnD,SAAS,EAAE,GACxC,eAAe;MAEnB,MAAMqD,MAAM,GAAGF,WAAW,CAACnD,SAAS,GAAG,KAAK,GAAG,MAAM;MAErD,MAAMsD,QAAQ,GAAG,MAAMC,SAAS,CAACF,MAAM,CAAClE,WAAW,CAAC,CAAC,CAAC,CAACiE,GAAG,EAAED,WAAW,CAAC;MACxE,OAAOG,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAE,aAAa,EAAGC,SAAS,IAAK;IAC5B,OAAO,IAAI7F,OAAO,CAAEC,OAAO,IAAK;MAC9BiB,UAAU,CAAC,MAAM;QACfjB,OAAO,CAAC;UACNC,OAAO,EAAE,IAAI;UACbuD,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ,CAAC;EAED;EACAqC,WAAW,EAAE,MAAAA,CAAOD,SAAS,EAAEE,QAAQ,KAAK;IAC1C,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMC,SAAS,CAACO,MAAM,CAACC,IAAI,CAAC,iBAAiBJ,SAAS,SAAS,EAAEE,QAAQ,EAAE;QAC1FG,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAOV,QAAQ,CAACrF,IAAI;IACtB,CAAC,CAAC,OAAOuF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAS,YAAY,EAAE,MAAAA,CAAON,SAAS,EAAEE,QAAQ,KAAK;IAC3C,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMC,SAAS,CAACO,MAAM,CAACC,IAAI,CAAC,iBAAiBJ,SAAS,SAAS,EAAEE,QAAQ,EAAE;QAC1FG,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAOV,QAAQ,CAACrF,IAAI;IACtB,CAAC,CAAC,OAAOuF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAjF,aAAa,EAAEA,CAAA,KAAM;IACnB,OAAOT,OAAO,CAACC,OAAO,CAAC;MACrBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEN;IACR,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,MAAMuG,OAAO,GAAG;EACd;EACAC,KAAK,EAAGC,WAAW,IAAK;IACtB;IACA,IAAIA,WAAW,CAACC,KAAK,KAAK,sBAAsB,IAAID,WAAW,CAACE,QAAQ,KAAK,UAAU,EAAE;MACvF,OAAOxG,OAAO,CAACC,OAAO,CAAC;QACrBC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE;UACJsG,KAAK,EAAE,sBAAsB;UAC7BC,IAAI,EAAE;YACJpH,EAAE,EAAE,CAAC;YACLqH,SAAS,EAAE,OAAO;YAClBC,QAAQ,EAAE,MAAM;YAChBL,KAAK,EAAED,WAAW,CAACC,KAAK;YACxBM,IAAI,EAAE;UACR;QACF;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIP,WAAW,CAACC,KAAK,KAAK,wBAAwB,IAAID,WAAW,CAACE,QAAQ,KAAK,UAAU,EAAE;MACzF,OAAOxG,OAAO,CAACC,OAAO,CAAC;QACrBC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE;UACJsG,KAAK,EAAE,yBAAyB;UAChCC,IAAI,EAAE;YACJpH,EAAE,EAAE,CAAC;YACLqH,SAAS,EAAE,SAAS;YACpBC,QAAQ,EAAE,MAAM;YAChBL,KAAK,EAAED,WAAW,CAACC,KAAK;YACxBM,IAAI,EAAE;UACR;QACF;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,OAAO7G,OAAO,CAACC,OAAO,CAAC;MACrBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJsG,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE;UACJpH,EAAE,EAAE,CAAC;UACLqH,SAAS,EAAE,MAAM;UACjBC,QAAQ,EAAE,MAAM;UAChBL,KAAK,EAAED,WAAW,CAACC,KAAK;UACxBM,IAAI,EAAE;QACR;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,QAAQ,EAAGC,QAAQ,IAAK;IACtB,OAAO/G,OAAO,CAACC,OAAO,CAAC;MACrBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE;QACJsG,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE;UACJpH,EAAE,EAAE,CAAC;UACLC,IAAI,EAAEwH,QAAQ,CAACxH,IAAI;UACnBgH,KAAK,EAAEQ,QAAQ,CAACR;QAClB;MACF;IACF,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,MAAMS,WAAW,GAAG;EAClB,GAAGlH,GAAG;EACN,GAAGsG;AACL,CAAC;AAED,eAAeY,WAAW;AAC1B,SAAStG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}