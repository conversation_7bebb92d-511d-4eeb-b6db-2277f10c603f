{"version": 3, "file": "Text2D.cjs", "sources": ["../../src/webxr/Text2D.js"], "sourcesContent": ["import * as THREE from 'three'\n\nfunction createText(message, height) {\n  const canvas = document.createElement('canvas')\n  const context = canvas.getContext('2d')\n  let metrics = null\n  const textHeight = 100\n  context.font = 'normal ' + textHeight + 'px Arial'\n  metrics = context.measureText(message)\n  const textWidth = metrics.width\n  canvas.width = textWidth\n  canvas.height = textHeight\n  context.font = 'normal ' + textHeight + 'px Arial'\n  context.textAlign = 'center'\n  context.textBaseline = 'middle'\n  context.fillStyle = '#ffffff'\n  context.fillText(message, textWidth / 2, textHeight / 2)\n\n  const texture = new THREE.Texture(canvas)\n  texture.needsUpdate = true\n  //var spriteAlignment = new THREE.Vector2(0,0) ;\n  const material = new THREE.MeshBasicMaterial({\n    color: 0xffffff,\n    side: THREE.DoubleSide,\n    map: texture,\n    transparent: true,\n  })\n  const geometry = new THREE.PlaneGeometry((height * textWidth) / textHeight, height)\n  const plane = new THREE.Mesh(geometry, material)\n  return plane\n}\n\nexport { createText }\n"], "names": ["THREE"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEA,SAAS,WAAW,SAAS,QAAQ;AACnC,QAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,QAAM,UAAU,OAAO,WAAW,IAAI;AACtC,MAAI,UAAU;AACd,QAAM,aAAa;AACnB,UAAQ,OAAO,YAAY,aAAa;AACxC,YAAU,QAAQ,YAAY,OAAO;AACrC,QAAM,YAAY,QAAQ;AAC1B,SAAO,QAAQ;AACf,SAAO,SAAS;AAChB,UAAQ,OAAO,YAAY,aAAa;AACxC,UAAQ,YAAY;AACpB,UAAQ,eAAe;AACvB,UAAQ,YAAY;AACpB,UAAQ,SAAS,SAAS,YAAY,GAAG,aAAa,CAAC;AAEvD,QAAM,UAAU,IAAIA,iBAAM,QAAQ,MAAM;AACxC,UAAQ,cAAc;AAEtB,QAAM,WAAW,IAAIA,iBAAM,kBAAkB;AAAA,IAC3C,OAAO;AAAA,IACP,MAAMA,iBAAM;AAAA,IACZ,KAAK;AAAA,IACL,aAAa;AAAA,EACjB,CAAG;AACD,QAAM,WAAW,IAAIA,iBAAM,cAAe,SAAS,YAAa,YAAY,MAAM;AAClF,QAAM,QAAQ,IAAIA,iBAAM,KAAK,UAAU,QAAQ;AAC/C,SAAO;AACT;;"}