{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\cart\\\\CartIcon.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useCart } from '../../contexts/CartContext';\nimport CartSidebar from './CartSidebar';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CartIcon = () => {\n  _s();\n  const {\n    getItemCount\n  } = useCart();\n  const [isCartOpen, setIsCartOpen] = useState(false);\n  const itemCount = getItemCount();\n  const toggleCart = () => {\n    setIsCartOpen(!isCartOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cart-icon-container\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"cart-icon-button\",\n        onClick: toggleCart,\n        \"aria-label\": `Shopping cart with ${itemCount} items`,\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"18\",\n          height: \"18\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          className: \"cart-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V16.5M9 19.5C9.8 19.5 10.5 20.2 10.5 21S9.8 22.5 9 22.5 7.5 21.8 7.5 21 8.2 19.5 9 19.5ZM20 19.5C20.8 19.5 21.5 20.2 21.5 21S20.8 22.5 20 22.5 18.5 21.8 18.5 21 19.2 19.5 20 19.5Z\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 21\n        }, this), itemCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"cart-badge\",\n          children: itemCount > 99 ? '99+' : itemCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(CartSidebar, {\n      isOpen: isCartOpen,\n      onClose: () => setIsCartOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(CartIcon, \"hInjj/LRVsEr8UwItlVv5hKlN64=\", false, function () {\n  return [useCart];\n});\n_c = CartIcon;\nexport default CartIcon;\nvar _c;\n$RefreshReg$(_c, \"CartIcon\");", "map": {"version": 3, "names": ["React", "useState", "useCart", "CartSidebar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CartIcon", "_s", "getItemCount", "isCartOpen", "setIsCartOpen", "itemCount", "toggleCart", "children", "className", "onClick", "width", "height", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/cart/CartIcon.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useCart } from '../../contexts/CartContext';\nimport CartSidebar from './CartSidebar';\n\nconst CartIcon = () => {\n    const { getItemCount } = useCart();\n    const [isCartOpen, setIsCartOpen] = useState(false);\n    const itemCount = getItemCount();\n\n    const toggleCart = () => {\n        setIsCartOpen(!isCartOpen);\n    };\n\n    return (\n        <>\n            <div className=\"cart-icon-container\">\n                <button\n                    className=\"cart-icon-button\"\n                    onClick={toggleCart}\n                    aria-label={`Shopping cart with ${itemCount} items`}\n                >\n                    <svg\n                        width=\"18\"\n                        height=\"18\"\n                        viewBox=\"0 0 24 24\"\n                        fill=\"none\"\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        className=\"cart-icon\"\n                    >\n                        <path\n                            d=\"M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V16.5M9 19.5C9.8 19.5 10.5 20.2 10.5 21S9.8 22.5 9 22.5 7.5 21.8 7.5 21 8.2 19.5 9 19.5ZM20 19.5C20.8 19.5 21.5 20.2 21.5 21S20.8 22.5 20 22.5 18.5 21.8 18.5 21 19.2 19.5 20 19.5Z\"\n                            stroke=\"currentColor\"\n                            strokeWidth=\"2\"\n                            strokeLinecap=\"round\"\n                            strokeLinejoin=\"round\"\n                        />\n                    </svg>\n                    {itemCount > 0 && (\n                        <span className=\"cart-badge\">\n                            {itemCount > 99 ? '99+' : itemCount}\n                        </span>\n                    )}\n                </button>\n            </div>\n\n            {/* Cart Sidebar */}\n            <CartSidebar\n                isOpen={isCartOpen}\n                onClose={() => setIsCartOpen(false)}\n            />\n        </>\n    );\n};\n\nexport default CartIcon;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,WAAW,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC;EAAa,CAAC,GAAGR,OAAO,CAAC,CAAC;EAClC,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMY,SAAS,GAAGH,YAAY,CAAC,CAAC;EAEhC,MAAMI,UAAU,GAAGA,CAAA,KAAM;IACrBF,aAAa,CAAC,CAACD,UAAU,CAAC;EAC9B,CAAC;EAED,oBACIN,OAAA,CAAAE,SAAA;IAAAQ,QAAA,gBACIV,OAAA;MAAKW,SAAS,EAAC,qBAAqB;MAAAD,QAAA,eAChCV,OAAA;QACIW,SAAS,EAAC,kBAAkB;QAC5BC,OAAO,EAAEH,UAAW;QACpB,cAAY,sBAAsBD,SAAS,QAAS;QAAAE,QAAA,gBAEpDV,OAAA;UACIa,KAAK,EAAC,IAAI;UACVC,MAAM,EAAC,IAAI;UACXC,OAAO,EAAC,WAAW;UACnBC,IAAI,EAAC,MAAM;UACXC,KAAK,EAAC,4BAA4B;UAClCN,SAAS,EAAC,WAAW;UAAAD,QAAA,eAErBV,OAAA;YACIkB,CAAC,EAAC,+QAA+Q;YACjRC,MAAM,EAAC,cAAc;YACrBC,WAAW,EAAC,GAAG;YACfC,aAAa,EAAC,OAAO;YACrBC,cAAc,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACLlB,SAAS,GAAG,CAAC,iBACVR,OAAA;UAAMW,SAAS,EAAC,YAAY;UAAAD,QAAA,EACvBF,SAAS,GAAG,EAAE,GAAG,KAAK,GAAGA;QAAS;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGN1B,OAAA,CAACF,WAAW;MACR6B,MAAM,EAAErB,UAAW;MACnBsB,OAAO,EAAEA,CAAA,KAAMrB,aAAa,CAAC,KAAK;IAAE;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAAA,eACJ,CAAC;AAEX,CAAC;AAACtB,EAAA,CAhDID,QAAQ;EAAA,QACeN,OAAO;AAAA;AAAAgC,EAAA,GAD9B1B,QAAQ;AAkDd,eAAeA,QAAQ;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}