{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\components\\\\admin\\\\modals\\\\ProductFormModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport FileUploadZone from '../components/FileUploadZone';\nimport ThreeJSPreview from '../components/ThreeJSPreview';\nimport { productsApi } from '../../../services/api';\nimport websocketService from '../../../services/websocketService';\nimport './ProductFormModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductFormModal = ({\n  product,\n  categories,\n  onSave,\n  onClose\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    ProductCode: '',\n    ProductName: '',\n    CategoryID: '',\n    Description: '',\n    BasePrice: '',\n    Weight: '',\n    Dimensions: '',\n    Material: '',\n    Color: '',\n    Status: 'Draft',\n    Tags: '',\n    IsCustomizable: false,\n    IsActive: true\n  });\n  const [uploadedFiles, setUploadedFiles] = useState({\n    models: [],\n    images: []\n  });\n  const [previewModel, setPreviewModel] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState('basic');\n  useEffect(() => {\n    if (product) {\n      setFormData({\n        ProductCode: product.ProductCode || '',\n        ProductName: product.ProductName || '',\n        CategoryID: product.CategoryID || '',\n        Description: product.Description || '',\n        BasePrice: product.BasePrice || '',\n        Weight: product.Weight || '',\n        Dimensions: product.Dimensions || '',\n        Material: product.Material || '',\n        Color: product.Color || '',\n        Status: product.Status || 'Draft',\n        Tags: product.Tags || '',\n        IsCustomizable: product.IsCustomizable || false,\n        IsActive: product.IsActive !== undefined ? product.IsActive : true\n      });\n    }\n  }, [product]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  const handleFileUpload = (files, fileType) => {\n    setUploadedFiles(prev => ({\n      ...prev,\n      [fileType]: [...prev[fileType], ...files]\n    }));\n\n    // Set preview for first 3D model\n    if (fileType === 'models' && files.length > 0 && !previewModel) {\n      setPreviewModel(files[0]);\n    }\n  };\n  const handleRemoveFile = (index, fileType) => {\n    setUploadedFiles(prev => ({\n      ...prev,\n      [fileType]: prev[fileType].filter((_, i) => i !== index)\n    }));\n\n    // Update preview if removing the current preview model\n    if (fileType === 'models' && previewModel && uploadedFiles.models[index] === previewModel) {\n      const remainingModels = uploadedFiles.models.filter((_, i) => i !== index);\n      setPreviewModel(remainingModels.length > 0 ? remainingModels[0] : null);\n    }\n  };\n  const validateForm = () => {\n    const errors = [];\n    if (!formData.ProductCode.trim()) errors.push('Product code is required');\n    if (!formData.ProductName.trim()) errors.push('Product name is required');\n    if (!formData.CategoryID) errors.push('Category is required');\n    if (!formData.BasePrice || parseFloat(formData.BasePrice) <= 0) errors.push('Valid base price is required');\n    return errors;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const errors = validateForm();\n    if (errors.length > 0) {\n      toast.error(errors.join(', '));\n      return;\n    }\n    setLoading(true);\n    try {\n      // Create or update product\n      const productData = {\n        ...formData,\n        BasePrice: parseFloat(formData.BasePrice),\n        Weight: formData.Weight ? parseFloat(formData.Weight) : null,\n        Tags: formData.Tags ? JSON.stringify(formData.Tags.split(',').map(tag => tag.trim())) : null\n      };\n      if (product) {\n        productData.ProductID = product.ProductID;\n      }\n      const response = await productsApi.createOrUpdateProduct(productData);\n      if (!response.success) {\n        throw new Error(response.message || 'Failed to save product');\n      }\n      const savedProduct = response.data;\n\n      // Upload files if any\n      if (uploadedFiles.models.length > 0) {\n        await uploadFiles(savedProduct.ProductID, uploadedFiles.models, 'models');\n      }\n      if (uploadedFiles.images.length > 0) {\n        await uploadFiles(savedProduct.ProductID, uploadedFiles.images, 'images');\n      }\n      onSave();\n    } catch (error) {\n      console.error('Error saving product:', error);\n      toast.error(error.message || 'Failed to save product');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const uploadFiles = async (productId, files, fileType) => {\n    const formData = new FormData();\n    try {\n      if (fileType === 'models') {\n        files.forEach(file => {\n          formData.append('model', file);\n        });\n        const response = await productsApi.uploadModel(productId, formData);\n\n        // Emit WebSocket event for file upload\n        if (response.success) {\n          websocketService.notifyProductFileUploaded(productId, {\n            fileType: 'model',\n            fileName: files[0].name,\n            fileSize: files[0].size\n          });\n        }\n      } else if (fileType === 'images') {\n        files.forEach(file => {\n          formData.append('images', file);\n        });\n        const response = await productsApi.uploadImages(productId, formData);\n\n        // Emit WebSocket event for file upload\n        if (response.success) {\n          websocketService.notifyProductFileUploaded(productId, {\n            fileType: 'images',\n            fileCount: files.length,\n            totalSize: files.reduce((sum, file) => sum + file.size, 0)\n          });\n        }\n      }\n    } catch (error) {\n      console.error(`Error uploading ${fileType}:`, error);\n      throw error;\n    }\n  };\n  const tabs = [{\n    id: 'basic',\n    label: 'Basic Info',\n    icon: '📝'\n  }, {\n    id: 'files',\n    label: 'Files & Media',\n    icon: '📁'\n  }, {\n    id: 'preview',\n    label: '3D Preview',\n    icon: '🎯'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"product-form-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: product ? 'Edit Product' : 'Add New Product'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: onClose,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-tabs\",\n        children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-button ${activeTab === tab.id ? 'active' : ''}`,\n          onClick: () => setActiveTab(tab.id),\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tab-icon\",\n            children: tab.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this), tab.label]\n        }, tab.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"modal-body\",\n        children: [activeTab === 'basic' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"ProductCode\",\n                children: \"Product Code *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"ProductCode\",\n                name: \"ProductCode\",\n                value: formData.ProductCode,\n                onChange: handleInputChange,\n                className: \"form-input\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"ProductName\",\n                children: \"Product Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"ProductName\",\n                name: \"ProductName\",\n                value: formData.ProductName,\n                onChange: handleInputChange,\n                className: \"form-input\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"CategoryID\",\n                children: \"Category *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"CategoryID\",\n                name: \"CategoryID\",\n                value: formData.CategoryID,\n                onChange: handleInputChange,\n                className: \"form-select\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 21\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.CategoryID,\n                  children: category.CategoryName\n                }, category.CategoryID, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"BasePrice\",\n                children: \"Base Price (PHP) *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                id: \"BasePrice\",\n                name: \"BasePrice\",\n                value: formData.BasePrice,\n                onChange: handleInputChange,\n                className: \"form-input\",\n                min: \"0\",\n                step: \"0.01\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"Weight\",\n                children: \"Weight (kg)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                id: \"Weight\",\n                name: \"Weight\",\n                value: formData.Weight,\n                onChange: handleInputChange,\n                className: \"form-input\",\n                min: \"0\",\n                step: \"0.01\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"Dimensions\",\n                children: \"Dimensions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"Dimensions\",\n                name: \"Dimensions\",\n                value: formData.Dimensions,\n                onChange: handleInputChange,\n                className: \"form-input\",\n                placeholder: \"e.g., 120cm x 60cm x 75cm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"Material\",\n                children: \"Material\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"Material\",\n                name: \"Material\",\n                value: formData.Material,\n                onChange: handleInputChange,\n                className: \"form-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"Color\",\n                children: \"Color\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"Color\",\n                name: \"Color\",\n                value: formData.Color,\n                onChange: handleInputChange,\n                className: \"form-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"Status\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"Status\",\n                name: \"Status\",\n                value: formData.Status,\n                onChange: handleInputChange,\n                className: \"form-select\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Draft\",\n                  children: \"Draft\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Active\",\n                  children: \"Active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Inactive\",\n                  children: \"Inactive\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Pending Review\",\n                  children: \"Pending Review\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"Tags\",\n                children: \"Tags (comma-separated)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"Tags\",\n                name: \"Tags\",\n                value: formData.Tags,\n                onChange: handleInputChange,\n                className: \"form-input\",\n                placeholder: \"e.g., office, chair, ergonomic\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group full-width\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"Description\",\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              id: \"Description\",\n              name: \"Description\",\n              value: formData.Description,\n              onChange: handleInputChange,\n              className: \"form-textarea\",\n              rows: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-checkboxes\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"checkbox-label\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                name: \"IsCustomizable\",\n                checked: formData.IsCustomizable,\n                onChange: handleInputChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"checkbox-text\",\n                children: \"Is Customizable\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"checkbox-label\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                name: \"IsActive\",\n                checked: formData.IsActive,\n                onChange: handleInputChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"checkbox-text\",\n                children: \"Is Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this), activeTab === 'files' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"file-upload-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"3D Models\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FileUploadZone, {\n              accept: \".glb,.gltf\",\n              multiple: false,\n              onFilesSelected: files => handleFileUpload(files, 'models'),\n              fileType: \"3D Model\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 17\n            }, this), uploadedFiles.models.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"uploaded-files\",\n              children: uploadedFiles.models.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"uploaded-file\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: file.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleRemoveFile(index, 'models'),\n                  className: \"remove-file\",\n                  children: \"\\xD7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"file-upload-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Product Images\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FileUploadZone, {\n              accept: \".jpg,.jpeg,.png,.webp\",\n              multiple: true,\n              onFilesSelected: files => handleFileUpload(files, 'images'),\n              fileType: \"Image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this), uploadedFiles.images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"uploaded-files\",\n              children: uploadedFiles.images.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"uploaded-file\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: file.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleRemoveFile(index, 'images'),\n                  className: \"remove-file\",\n                  children: \"\\xD7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 13\n        }, this), activeTab === 'preview' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"preview-section\",\n            children: previewModel ? /*#__PURE__*/_jsxDEV(ThreeJSPreview, {\n              modelFile: previewModel\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-preview\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Upload a 3D model to see preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-footer\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"btn btn-secondary\",\n          onClick: onClose,\n          disabled: loading,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          onClick: handleSubmit,\n          disabled: loading,\n          children: loading ? 'Saving...' : product ? 'Update Product' : 'Create Product'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductFormModal, \"bX9TdQL83MkyYZW0GpdBhyabCys=\");\n_c = ProductFormModal;\nexport default ProductFormModal;\nvar _c;\n$RefreshReg$(_c, \"ProductFormModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "toast", "FileUploadZone", "ThreeJSPreview", "productsApi", "websocketService", "jsxDEV", "_jsxDEV", "ProductFormModal", "product", "categories", "onSave", "onClose", "_s", "formData", "setFormData", "ProductCode", "ProductName", "CategoryID", "Description", "BasePrice", "Weight", "Dimensions", "Material", "Color", "Status", "Tags", "IsCustomizable", "IsActive", "uploadedFiles", "setUploadedFiles", "models", "images", "previewModel", "setPreviewModel", "loading", "setLoading", "activeTab", "setActiveTab", "undefined", "handleInputChange", "e", "name", "value", "type", "checked", "target", "prev", "handleFileUpload", "files", "fileType", "length", "handleRemoveFile", "index", "filter", "_", "i", "remainingModels", "validateForm", "errors", "trim", "push", "parseFloat", "handleSubmit", "preventDefault", "error", "join", "productData", "JSON", "stringify", "split", "map", "tag", "ProductID", "response", "createOrUpdateProduct", "success", "Error", "message", "savedProduct", "data", "uploadFiles", "console", "productId", "FormData", "for<PERSON>ach", "file", "append", "uploadModel", "notifyProductFileUploaded", "fileName", "fileSize", "size", "uploadImages", "fileCount", "totalSize", "reduce", "sum", "tabs", "id", "label", "icon", "className", "children", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "tab", "onSubmit", "htmlFor", "onChange", "required", "category", "CategoryName", "min", "step", "placeholder", "rows", "accept", "multiple", "onFilesSelected", "modelFile", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/components/admin/modals/ProductFormModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { toast } from 'react-toastify';\nimport FileUploadZone from '../components/FileUploadZone';\nimport ThreeJSPreview from '../components/ThreeJSPreview';\nimport { productsApi } from '../../../services/api';\nimport websocketService from '../../../services/websocketService';\nimport './ProductFormModal.css';\n\nconst ProductFormModal = ({ product, categories, onSave, onClose }) => {\n  const [formData, setFormData] = useState({\n    ProductCode: '',\n    ProductName: '',\n    CategoryID: '',\n    Description: '',\n    BasePrice: '',\n    Weight: '',\n    Dimensions: '',\n    Material: '',\n    Color: '',\n    Status: 'Draft',\n    Tags: '',\n    IsCustomizable: false,\n    IsActive: true\n  });\n\n  const [uploadedFiles, setUploadedFiles] = useState({\n    models: [],\n    images: []\n  });\n\n  const [previewModel, setPreviewModel] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState('basic');\n\n  useEffect(() => {\n    if (product) {\n      setFormData({\n        ProductCode: product.ProductCode || '',\n        ProductName: product.ProductName || '',\n        CategoryID: product.CategoryID || '',\n        Description: product.Description || '',\n        BasePrice: product.BasePrice || '',\n        Weight: product.Weight || '',\n        Dimensions: product.Dimensions || '',\n        Material: product.Material || '',\n        Color: product.Color || '',\n        Status: product.Status || 'Draft',\n        Tags: product.Tags || '',\n        IsCustomizable: product.IsCustomizable || false,\n        IsActive: product.IsActive !== undefined ? product.IsActive : true\n      });\n    }\n  }, [product]);\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  const handleFileUpload = (files, fileType) => {\n    setUploadedFiles(prev => ({\n      ...prev,\n      [fileType]: [...prev[fileType], ...files]\n    }));\n\n    // Set preview for first 3D model\n    if (fileType === 'models' && files.length > 0 && !previewModel) {\n      setPreviewModel(files[0]);\n    }\n  };\n\n  const handleRemoveFile = (index, fileType) => {\n    setUploadedFiles(prev => ({\n      ...prev,\n      [fileType]: prev[fileType].filter((_, i) => i !== index)\n    }));\n\n    // Update preview if removing the current preview model\n    if (fileType === 'models' && previewModel && uploadedFiles.models[index] === previewModel) {\n      const remainingModels = uploadedFiles.models.filter((_, i) => i !== index);\n      setPreviewModel(remainingModels.length > 0 ? remainingModels[0] : null);\n    }\n  };\n\n  const validateForm = () => {\n    const errors = [];\n\n    if (!formData.ProductCode.trim()) errors.push('Product code is required');\n    if (!formData.ProductName.trim()) errors.push('Product name is required');\n    if (!formData.CategoryID) errors.push('Category is required');\n    if (!formData.BasePrice || parseFloat(formData.BasePrice) <= 0) errors.push('Valid base price is required');\n\n    return errors;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    const errors = validateForm();\n    if (errors.length > 0) {\n      toast.error(errors.join(', '));\n      return;\n    }\n\n    setLoading(true);\n\n    try {\n      // Create or update product\n      const productData = {\n        ...formData,\n        BasePrice: parseFloat(formData.BasePrice),\n        Weight: formData.Weight ? parseFloat(formData.Weight) : null,\n        Tags: formData.Tags ? JSON.stringify(formData.Tags.split(',').map(tag => tag.trim())) : null\n      };\n\n      if (product) {\n        productData.ProductID = product.ProductID;\n      }\n\n      const response = await productsApi.createOrUpdateProduct(productData);\n      \n      if (!response.success) {\n        throw new Error(response.message || 'Failed to save product');\n      }\n\n      const savedProduct = response.data;\n\n      // Upload files if any\n      if (uploadedFiles.models.length > 0) {\n        await uploadFiles(savedProduct.ProductID, uploadedFiles.models, 'models');\n      }\n\n      if (uploadedFiles.images.length > 0) {\n        await uploadFiles(savedProduct.ProductID, uploadedFiles.images, 'images');\n      }\n\n      onSave();\n    } catch (error) {\n      console.error('Error saving product:', error);\n      toast.error(error.message || 'Failed to save product');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const uploadFiles = async (productId, files, fileType) => {\n    const formData = new FormData();\n\n    try {\n      if (fileType === 'models') {\n        files.forEach(file => {\n          formData.append('model', file);\n        });\n        const response = await productsApi.uploadModel(productId, formData);\n\n        // Emit WebSocket event for file upload\n        if (response.success) {\n          websocketService.notifyProductFileUploaded(productId, {\n            fileType: 'model',\n            fileName: files[0].name,\n            fileSize: files[0].size\n          });\n        }\n      } else if (fileType === 'images') {\n        files.forEach(file => {\n          formData.append('images', file);\n        });\n        const response = await productsApi.uploadImages(productId, formData);\n\n        // Emit WebSocket event for file upload\n        if (response.success) {\n          websocketService.notifyProductFileUploaded(productId, {\n            fileType: 'images',\n            fileCount: files.length,\n            totalSize: files.reduce((sum, file) => sum + file.size, 0)\n          });\n        }\n      }\n    } catch (error) {\n      console.error(`Error uploading ${fileType}:`, error);\n      throw error;\n    }\n  };\n\n  const tabs = [\n    { id: 'basic', label: 'Basic Info', icon: '📝' },\n    { id: 'files', label: 'Files & Media', icon: '📁' },\n    { id: 'preview', label: '3D Preview', icon: '🎯' }\n  ];\n\n  return (\n    <div className=\"modal-overlay\">\n      <div className=\"product-form-modal\">\n        <div className=\"modal-header\">\n          <h2>{product ? 'Edit Product' : 'Add New Product'}</h2>\n          <button className=\"modal-close\" onClick={onClose}>×</button>\n        </div>\n\n        <div className=\"modal-tabs\">\n          {tabs.map(tab => (\n            <button\n              key={tab.id}\n              className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}\n              onClick={() => setActiveTab(tab.id)}\n            >\n              <span className=\"tab-icon\">{tab.icon}</span>\n              {tab.label}\n            </button>\n          ))}\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"modal-body\">\n          {activeTab === 'basic' && (\n            <div className=\"tab-content\">\n              <div className=\"form-grid\">\n                <div className=\"form-group\">\n                  <label htmlFor=\"ProductCode\">Product Code *</label>\n                  <input\n                    type=\"text\"\n                    id=\"ProductCode\"\n                    name=\"ProductCode\"\n                    value={formData.ProductCode}\n                    onChange={handleInputChange}\n                    className=\"form-input\"\n                    required\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"ProductName\">Product Name *</label>\n                  <input\n                    type=\"text\"\n                    id=\"ProductName\"\n                    name=\"ProductName\"\n                    value={formData.ProductName}\n                    onChange={handleInputChange}\n                    className=\"form-input\"\n                    required\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"CategoryID\">Category *</label>\n                  <select\n                    id=\"CategoryID\"\n                    name=\"CategoryID\"\n                    value={formData.CategoryID}\n                    onChange={handleInputChange}\n                    className=\"form-select\"\n                    required\n                  >\n                    <option value=\"\">Select Category</option>\n                    {categories.map(category => (\n                      <option key={category.CategoryID} value={category.CategoryID}>\n                        {category.CategoryName}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"BasePrice\">Base Price (PHP) *</label>\n                  <input\n                    type=\"number\"\n                    id=\"BasePrice\"\n                    name=\"BasePrice\"\n                    value={formData.BasePrice}\n                    onChange={handleInputChange}\n                    className=\"form-input\"\n                    min=\"0\"\n                    step=\"0.01\"\n                    required\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"Weight\">Weight (kg)</label>\n                  <input\n                    type=\"number\"\n                    id=\"Weight\"\n                    name=\"Weight\"\n                    value={formData.Weight}\n                    onChange={handleInputChange}\n                    className=\"form-input\"\n                    min=\"0\"\n                    step=\"0.01\"\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"Dimensions\">Dimensions</label>\n                  <input\n                    type=\"text\"\n                    id=\"Dimensions\"\n                    name=\"Dimensions\"\n                    value={formData.Dimensions}\n                    onChange={handleInputChange}\n                    className=\"form-input\"\n                    placeholder=\"e.g., 120cm x 60cm x 75cm\"\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"Material\">Material</label>\n                  <input\n                    type=\"text\"\n                    id=\"Material\"\n                    name=\"Material\"\n                    value={formData.Material}\n                    onChange={handleInputChange}\n                    className=\"form-input\"\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"Color\">Color</label>\n                  <input\n                    type=\"text\"\n                    id=\"Color\"\n                    name=\"Color\"\n                    value={formData.Color}\n                    onChange={handleInputChange}\n                    className=\"form-input\"\n                  />\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"Status\">Status</label>\n                  <select\n                    id=\"Status\"\n                    name=\"Status\"\n                    value={formData.Status}\n                    onChange={handleInputChange}\n                    className=\"form-select\"\n                  >\n                    <option value=\"Draft\">Draft</option>\n                    <option value=\"Active\">Active</option>\n                    <option value=\"Inactive\">Inactive</option>\n                    <option value=\"Pending Review\">Pending Review</option>\n                  </select>\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"Tags\">Tags (comma-separated)</label>\n                  <input\n                    type=\"text\"\n                    id=\"Tags\"\n                    name=\"Tags\"\n                    value={formData.Tags}\n                    onChange={handleInputChange}\n                    className=\"form-input\"\n                    placeholder=\"e.g., office, chair, ergonomic\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"form-group full-width\">\n                <label htmlFor=\"Description\">Description</label>\n                <textarea\n                  id=\"Description\"\n                  name=\"Description\"\n                  value={formData.Description}\n                  onChange={handleInputChange}\n                  className=\"form-textarea\"\n                  rows=\"4\"\n                />\n              </div>\n\n              <div className=\"form-checkboxes\">\n                <label className=\"checkbox-label\">\n                  <input\n                    type=\"checkbox\"\n                    name=\"IsCustomizable\"\n                    checked={formData.IsCustomizable}\n                    onChange={handleInputChange}\n                  />\n                  <span className=\"checkbox-text\">Is Customizable</span>\n                </label>\n\n                <label className=\"checkbox-label\">\n                  <input\n                    type=\"checkbox\"\n                    name=\"IsActive\"\n                    checked={formData.IsActive}\n                    onChange={handleInputChange}\n                  />\n                  <span className=\"checkbox-text\">Is Active</span>\n                </label>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'files' && (\n            <div className=\"tab-content\">\n              <div className=\"file-upload-section\">\n                <h3>3D Models</h3>\n                <FileUploadZone\n                  accept=\".glb,.gltf\"\n                  multiple={false}\n                  onFilesSelected={(files) => handleFileUpload(files, 'models')}\n                  fileType=\"3D Model\"\n                />\n                {uploadedFiles.models.length > 0 && (\n                  <div className=\"uploaded-files\">\n                    {uploadedFiles.models.map((file, index) => (\n                      <div key={index} className=\"uploaded-file\">\n                        <span>{file.name}</span>\n                        <button\n                          type=\"button\"\n                          onClick={() => handleRemoveFile(index, 'models')}\n                          className=\"remove-file\"\n                        >\n                          ×\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n\n              <div className=\"file-upload-section\">\n                <h3>Product Images</h3>\n                <FileUploadZone\n                  accept=\".jpg,.jpeg,.png,.webp\"\n                  multiple={true}\n                  onFilesSelected={(files) => handleFileUpload(files, 'images')}\n                  fileType=\"Image\"\n                />\n                {uploadedFiles.images.length > 0 && (\n                  <div className=\"uploaded-files\">\n                    {uploadedFiles.images.map((file, index) => (\n                      <div key={index} className=\"uploaded-file\">\n                        <span>{file.name}</span>\n                        <button\n                          type=\"button\"\n                          onClick={() => handleRemoveFile(index, 'images')}\n                          className=\"remove-file\"\n                        >\n                          ×\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'preview' && (\n            <div className=\"tab-content\">\n              <div className=\"preview-section\">\n                {previewModel ? (\n                  <ThreeJSPreview modelFile={previewModel} />\n                ) : (\n                  <div className=\"no-preview\">\n                    <p>Upload a 3D model to see preview</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </form>\n\n        <div className=\"modal-footer\">\n          <button\n            type=\"button\"\n            className=\"btn btn-secondary\"\n            onClick={onClose}\n            disabled={loading}\n          >\n            Cancel\n          </button>\n          <button\n            type=\"submit\"\n            className=\"btn btn-primary\"\n            onClick={handleSubmit}\n            disabled={loading}\n          >\n            {loading ? 'Saving...' : (product ? 'Update Product' : 'Create Product')}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductFormModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,SAASC,WAAW,QAAQ,uBAAuB;AACnD,OAAOC,gBAAgB,MAAM,oCAAoC;AACjE,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,OAAO;EAAEC,UAAU;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACrE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC;IACvCiB,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,OAAO;IACfC,IAAI,EAAE,EAAE;IACRC,cAAc,EAAE,KAAK;IACrBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC;IACjDgC,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,OAAO,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd,IAAIS,OAAO,EAAE;MACXM,WAAW,CAAC;QACVC,WAAW,EAAEP,OAAO,CAACO,WAAW,IAAI,EAAE;QACtCC,WAAW,EAAER,OAAO,CAACQ,WAAW,IAAI,EAAE;QACtCC,UAAU,EAAET,OAAO,CAACS,UAAU,IAAI,EAAE;QACpCC,WAAW,EAAEV,OAAO,CAACU,WAAW,IAAI,EAAE;QACtCC,SAAS,EAAEX,OAAO,CAACW,SAAS,IAAI,EAAE;QAClCC,MAAM,EAAEZ,OAAO,CAACY,MAAM,IAAI,EAAE;QAC5BC,UAAU,EAAEb,OAAO,CAACa,UAAU,IAAI,EAAE;QACpCC,QAAQ,EAAEd,OAAO,CAACc,QAAQ,IAAI,EAAE;QAChCC,KAAK,EAAEf,OAAO,CAACe,KAAK,IAAI,EAAE;QAC1BC,MAAM,EAAEhB,OAAO,CAACgB,MAAM,IAAI,OAAO;QACjCC,IAAI,EAAEjB,OAAO,CAACiB,IAAI,IAAI,EAAE;QACxBC,cAAc,EAAElB,OAAO,CAACkB,cAAc,IAAI,KAAK;QAC/CC,QAAQ,EAAEnB,OAAO,CAACmB,QAAQ,KAAKW,SAAS,GAAG9B,OAAO,CAACmB,QAAQ,GAAG;MAChE,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACnB,OAAO,CAAC,CAAC;EAEb,MAAM+B,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAC/C/B,WAAW,CAACgC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACL,IAAI,GAAGE,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC5CpB,gBAAgB,CAACiB,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACG,QAAQ,GAAG,CAAC,GAAGH,IAAI,CAACG,QAAQ,CAAC,EAAE,GAAGD,KAAK;IAC1C,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIC,QAAQ,KAAK,QAAQ,IAAID,KAAK,CAACE,MAAM,GAAG,CAAC,IAAI,CAAClB,YAAY,EAAE;MAC9DC,eAAe,CAACe,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC;EAED,MAAMG,gBAAgB,GAAGA,CAACC,KAAK,EAAEH,QAAQ,KAAK;IAC5CpB,gBAAgB,CAACiB,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACG,QAAQ,GAAGH,IAAI,CAACG,QAAQ,CAAC,CAACI,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKH,KAAK;IACzD,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIH,QAAQ,KAAK,QAAQ,IAAIjB,YAAY,IAAIJ,aAAa,CAACE,MAAM,CAACsB,KAAK,CAAC,KAAKpB,YAAY,EAAE;MACzF,MAAMwB,eAAe,GAAG5B,aAAa,CAACE,MAAM,CAACuB,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKH,KAAK,CAAC;MAC1EnB,eAAe,CAACuB,eAAe,CAACN,MAAM,GAAG,CAAC,GAAGM,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACzE;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG,EAAE;IAEjB,IAAI,CAAC7C,QAAQ,CAACE,WAAW,CAAC4C,IAAI,CAAC,CAAC,EAAED,MAAM,CAACE,IAAI,CAAC,0BAA0B,CAAC;IACzE,IAAI,CAAC/C,QAAQ,CAACG,WAAW,CAAC2C,IAAI,CAAC,CAAC,EAAED,MAAM,CAACE,IAAI,CAAC,0BAA0B,CAAC;IACzE,IAAI,CAAC/C,QAAQ,CAACI,UAAU,EAAEyC,MAAM,CAACE,IAAI,CAAC,sBAAsB,CAAC;IAC7D,IAAI,CAAC/C,QAAQ,CAACM,SAAS,IAAI0C,UAAU,CAAChD,QAAQ,CAACM,SAAS,CAAC,IAAI,CAAC,EAAEuC,MAAM,CAACE,IAAI,CAAC,8BAA8B,CAAC;IAE3G,OAAOF,MAAM;EACf,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOtB,CAAC,IAAK;IAChCA,CAAC,CAACuB,cAAc,CAAC,CAAC;IAElB,MAAML,MAAM,GAAGD,YAAY,CAAC,CAAC;IAC7B,IAAIC,MAAM,CAACR,MAAM,GAAG,CAAC,EAAE;MACrBlD,KAAK,CAACgE,KAAK,CAACN,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC,CAAC;MAC9B;IACF;IAEA9B,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,MAAM+B,WAAW,GAAG;QAClB,GAAGrD,QAAQ;QACXM,SAAS,EAAE0C,UAAU,CAAChD,QAAQ,CAACM,SAAS,CAAC;QACzCC,MAAM,EAAEP,QAAQ,CAACO,MAAM,GAAGyC,UAAU,CAAChD,QAAQ,CAACO,MAAM,CAAC,GAAG,IAAI;QAC5DK,IAAI,EAAEZ,QAAQ,CAACY,IAAI,GAAG0C,IAAI,CAACC,SAAS,CAACvD,QAAQ,CAACY,IAAI,CAAC4C,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACZ,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG;MAC1F,CAAC;MAED,IAAInD,OAAO,EAAE;QACX0D,WAAW,CAACM,SAAS,GAAGhE,OAAO,CAACgE,SAAS;MAC3C;MAEA,MAAMC,QAAQ,GAAG,MAAMtE,WAAW,CAACuE,qBAAqB,CAACR,WAAW,CAAC;MAErE,IAAI,CAACO,QAAQ,CAACE,OAAO,EAAE;QACrB,MAAM,IAAIC,KAAK,CAACH,QAAQ,CAACI,OAAO,IAAI,wBAAwB,CAAC;MAC/D;MAEA,MAAMC,YAAY,GAAGL,QAAQ,CAACM,IAAI;;MAElC;MACA,IAAInD,aAAa,CAACE,MAAM,CAACoB,MAAM,GAAG,CAAC,EAAE;QACnC,MAAM8B,WAAW,CAACF,YAAY,CAACN,SAAS,EAAE5C,aAAa,CAACE,MAAM,EAAE,QAAQ,CAAC;MAC3E;MAEA,IAAIF,aAAa,CAACG,MAAM,CAACmB,MAAM,GAAG,CAAC,EAAE;QACnC,MAAM8B,WAAW,CAACF,YAAY,CAACN,SAAS,EAAE5C,aAAa,CAACG,MAAM,EAAE,QAAQ,CAAC;MAC3E;MAEArB,MAAM,CAAC,CAAC;IACV,CAAC,CAAC,OAAOsD,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7ChE,KAAK,CAACgE,KAAK,CAACA,KAAK,CAACa,OAAO,IAAI,wBAAwB,CAAC;IACxD,CAAC,SAAS;MACR1C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6C,WAAW,GAAG,MAAAA,CAAOE,SAAS,EAAElC,KAAK,EAAEC,QAAQ,KAAK;IACxD,MAAMpC,QAAQ,GAAG,IAAIsE,QAAQ,CAAC,CAAC;IAE/B,IAAI;MACF,IAAIlC,QAAQ,KAAK,QAAQ,EAAE;QACzBD,KAAK,CAACoC,OAAO,CAACC,IAAI,IAAI;UACpBxE,QAAQ,CAACyE,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;QAChC,CAAC,CAAC;QACF,MAAMZ,QAAQ,GAAG,MAAMtE,WAAW,CAACoF,WAAW,CAACL,SAAS,EAAErE,QAAQ,CAAC;;QAEnE;QACA,IAAI4D,QAAQ,CAACE,OAAO,EAAE;UACpBvE,gBAAgB,CAACoF,yBAAyB,CAACN,SAAS,EAAE;YACpDjC,QAAQ,EAAE,OAAO;YACjBwC,QAAQ,EAAEzC,KAAK,CAAC,CAAC,CAAC,CAACP,IAAI;YACvBiD,QAAQ,EAAE1C,KAAK,CAAC,CAAC,CAAC,CAAC2C;UACrB,CAAC,CAAC;QACJ;MACF,CAAC,MAAM,IAAI1C,QAAQ,KAAK,QAAQ,EAAE;QAChCD,KAAK,CAACoC,OAAO,CAACC,IAAI,IAAI;UACpBxE,QAAQ,CAACyE,MAAM,CAAC,QAAQ,EAAED,IAAI,CAAC;QACjC,CAAC,CAAC;QACF,MAAMZ,QAAQ,GAAG,MAAMtE,WAAW,CAACyF,YAAY,CAACV,SAAS,EAAErE,QAAQ,CAAC;;QAEpE;QACA,IAAI4D,QAAQ,CAACE,OAAO,EAAE;UACpBvE,gBAAgB,CAACoF,yBAAyB,CAACN,SAAS,EAAE;YACpDjC,QAAQ,EAAE,QAAQ;YAClB4C,SAAS,EAAE7C,KAAK,CAACE,MAAM;YACvB4C,SAAS,EAAE9C,KAAK,CAAC+C,MAAM,CAAC,CAACC,GAAG,EAAEX,IAAI,KAAKW,GAAG,GAAGX,IAAI,CAACM,IAAI,EAAE,CAAC;UAC3D,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,mBAAmBf,QAAQ,GAAG,EAAEe,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMiC,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAK,CAAC,EAChD;IAAEF,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE;EAAK,CAAC,EACnD;IAAEF,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAK,CAAC,CACnD;EAED,oBACE9F,OAAA;IAAK+F,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5BhG,OAAA;MAAK+F,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjChG,OAAA;QAAK+F,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhG,OAAA;UAAAgG,QAAA,EAAK9F,OAAO,GAAG,cAAc,GAAG;QAAiB;UAAAiF,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvDnG,OAAA;UAAQ+F,SAAS,EAAC,aAAa;UAACK,OAAO,EAAE/F,OAAQ;UAAA2F,QAAA,EAAC;QAAC;UAAAb,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAhB,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eAENnG,OAAA;QAAK+F,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxBL,IAAI,CAAC3B,GAAG,CAACqC,GAAG,iBACXrG,OAAA;UAEE+F,SAAS,EAAE,cAAcjE,SAAS,KAAKuE,GAAG,CAACT,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;UAChEQ,OAAO,EAAEA,CAAA,KAAMrE,YAAY,CAACsE,GAAG,CAACT,EAAE,CAAE;UAAAI,QAAA,gBAEpChG,OAAA;YAAM+F,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEK,GAAG,CAACP;UAAI;YAAAX,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAC3CE,GAAG,CAACR,KAAK;QAAA,GALLQ,GAAG,CAACT,EAAE;UAAAT,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAML,CACT;MAAC;QAAAhB,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnG,OAAA;QAAMsG,QAAQ,EAAE9C,YAAa;QAACuC,SAAS,EAAC,YAAY;QAAAC,QAAA,GACjDlE,SAAS,KAAK,OAAO,iBACpB9B,OAAA;UAAK+F,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BhG,OAAA;YAAK+F,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBhG,OAAA;cAAK+F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhG,OAAA;gBAAOuG,OAAO,EAAC,aAAa;gBAAAP,QAAA,EAAC;cAAc;gBAAAb,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnDnG,OAAA;gBACEqC,IAAI,EAAC,MAAM;gBACXuD,EAAE,EAAC,aAAa;gBAChBzD,IAAI,EAAC,aAAa;gBAClBC,KAAK,EAAE7B,QAAQ,CAACE,WAAY;gBAC5B+F,QAAQ,EAAEvE,iBAAkB;gBAC5B8D,SAAS,EAAC,YAAY;gBACtBU,QAAQ;cAAA;gBAAAtB,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAhB,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnG,OAAA;cAAK+F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhG,OAAA;gBAAOuG,OAAO,EAAC,aAAa;gBAAAP,QAAA,EAAC;cAAc;gBAAAb,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnDnG,OAAA;gBACEqC,IAAI,EAAC,MAAM;gBACXuD,EAAE,EAAC,aAAa;gBAChBzD,IAAI,EAAC,aAAa;gBAClBC,KAAK,EAAE7B,QAAQ,CAACG,WAAY;gBAC5B8F,QAAQ,EAAEvE,iBAAkB;gBAC5B8D,SAAS,EAAC,YAAY;gBACtBU,QAAQ;cAAA;gBAAAtB,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAhB,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnG,OAAA;cAAK+F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhG,OAAA;gBAAOuG,OAAO,EAAC,YAAY;gBAAAP,QAAA,EAAC;cAAU;gBAAAb,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9CnG,OAAA;gBACE4F,EAAE,EAAC,YAAY;gBACfzD,IAAI,EAAC,YAAY;gBACjBC,KAAK,EAAE7B,QAAQ,CAACI,UAAW;gBAC3B6F,QAAQ,EAAEvE,iBAAkB;gBAC5B8D,SAAS,EAAC,aAAa;gBACvBU,QAAQ;gBAAAT,QAAA,gBAERhG,OAAA;kBAAQoC,KAAK,EAAC,EAAE;kBAAA4D,QAAA,EAAC;gBAAe;kBAAAb,QAAA,EAAAc,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACxChG,UAAU,CAAC6D,GAAG,CAAC0C,QAAQ,iBACtB1G,OAAA;kBAAkCoC,KAAK,EAAEsE,QAAQ,CAAC/F,UAAW;kBAAAqF,QAAA,EAC1DU,QAAQ,CAACC;gBAAY,GADXD,QAAQ,CAAC/F,UAAU;kBAAAwE,QAAA,EAAAc,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAExB,CACT,CAAC;cAAA;gBAAAhB,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAhB,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENnG,OAAA;cAAK+F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhG,OAAA;gBAAOuG,OAAO,EAAC,WAAW;gBAAAP,QAAA,EAAC;cAAkB;gBAAAb,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrDnG,OAAA;gBACEqC,IAAI,EAAC,QAAQ;gBACbuD,EAAE,EAAC,WAAW;gBACdzD,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAE7B,QAAQ,CAACM,SAAU;gBAC1B2F,QAAQ,EAAEvE,iBAAkB;gBAC5B8D,SAAS,EAAC,YAAY;gBACtBa,GAAG,EAAC,GAAG;gBACPC,IAAI,EAAC,MAAM;gBACXJ,QAAQ;cAAA;gBAAAtB,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAhB,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnG,OAAA;cAAK+F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhG,OAAA;gBAAOuG,OAAO,EAAC,QAAQ;gBAAAP,QAAA,EAAC;cAAW;gBAAAb,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3CnG,OAAA;gBACEqC,IAAI,EAAC,QAAQ;gBACbuD,EAAE,EAAC,QAAQ;gBACXzD,IAAI,EAAC,QAAQ;gBACbC,KAAK,EAAE7B,QAAQ,CAACO,MAAO;gBACvB0F,QAAQ,EAAEvE,iBAAkB;gBAC5B8D,SAAS,EAAC,YAAY;gBACtBa,GAAG,EAAC,GAAG;gBACPC,IAAI,EAAC;cAAM;gBAAA1B,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAhB,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnG,OAAA;cAAK+F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhG,OAAA;gBAAOuG,OAAO,EAAC,YAAY;gBAAAP,QAAA,EAAC;cAAU;gBAAAb,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9CnG,OAAA;gBACEqC,IAAI,EAAC,MAAM;gBACXuD,EAAE,EAAC,YAAY;gBACfzD,IAAI,EAAC,YAAY;gBACjBC,KAAK,EAAE7B,QAAQ,CAACQ,UAAW;gBAC3ByF,QAAQ,EAAEvE,iBAAkB;gBAC5B8D,SAAS,EAAC,YAAY;gBACtBe,WAAW,EAAC;cAA2B;gBAAA3B,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAhB,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnG,OAAA;cAAK+F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhG,OAAA;gBAAOuG,OAAO,EAAC,UAAU;gBAAAP,QAAA,EAAC;cAAQ;gBAAAb,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1CnG,OAAA;gBACEqC,IAAI,EAAC,MAAM;gBACXuD,EAAE,EAAC,UAAU;gBACbzD,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAE7B,QAAQ,CAACS,QAAS;gBACzBwF,QAAQ,EAAEvE,iBAAkB;gBAC5B8D,SAAS,EAAC;cAAY;gBAAAZ,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAhB,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnG,OAAA;cAAK+F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhG,OAAA;gBAAOuG,OAAO,EAAC,OAAO;gBAAAP,QAAA,EAAC;cAAK;gBAAAb,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpCnG,OAAA;gBACEqC,IAAI,EAAC,MAAM;gBACXuD,EAAE,EAAC,OAAO;gBACVzD,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAE7B,QAAQ,CAACU,KAAM;gBACtBuF,QAAQ,EAAEvE,iBAAkB;gBAC5B8D,SAAS,EAAC;cAAY;gBAAAZ,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAhB,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENnG,OAAA;cAAK+F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhG,OAAA;gBAAOuG,OAAO,EAAC,QAAQ;gBAAAP,QAAA,EAAC;cAAM;gBAAAb,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtCnG,OAAA;gBACE4F,EAAE,EAAC,QAAQ;gBACXzD,IAAI,EAAC,QAAQ;gBACbC,KAAK,EAAE7B,QAAQ,CAACW,MAAO;gBACvBsF,QAAQ,EAAEvE,iBAAkB;gBAC5B8D,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAEvBhG,OAAA;kBAAQoC,KAAK,EAAC,OAAO;kBAAA4D,QAAA,EAAC;gBAAK;kBAAAb,QAAA,EAAAc,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCnG,OAAA;kBAAQoC,KAAK,EAAC,QAAQ;kBAAA4D,QAAA,EAAC;gBAAM;kBAAAb,QAAA,EAAAc,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCnG,OAAA;kBAAQoC,KAAK,EAAC,UAAU;kBAAA4D,QAAA,EAAC;gBAAQ;kBAAAb,QAAA,EAAAc,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CnG,OAAA;kBAAQoC,KAAK,EAAC,gBAAgB;kBAAA4D,QAAA,EAAC;gBAAc;kBAAAb,QAAA,EAAAc,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAhB,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAhB,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENnG,OAAA;cAAK+F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhG,OAAA;gBAAOuG,OAAO,EAAC,MAAM;gBAAAP,QAAA,EAAC;cAAsB;gBAAAb,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpDnG,OAAA;gBACEqC,IAAI,EAAC,MAAM;gBACXuD,EAAE,EAAC,MAAM;gBACTzD,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAE7B,QAAQ,CAACY,IAAK;gBACrBqF,QAAQ,EAAEvE,iBAAkB;gBAC5B8D,SAAS,EAAC,YAAY;gBACtBe,WAAW,EAAC;cAAgC;gBAAA3B,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAhB,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAhB,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnG,OAAA;YAAK+F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpChG,OAAA;cAAOuG,OAAO,EAAC,aAAa;cAAAP,QAAA,EAAC;YAAW;cAAAb,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChDnG,OAAA;cACE4F,EAAE,EAAC,aAAa;cAChBzD,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAE7B,QAAQ,CAACK,WAAY;cAC5B4F,QAAQ,EAAEvE,iBAAkB;cAC5B8D,SAAS,EAAC,eAAe;cACzBgB,IAAI,EAAC;YAAG;cAAA5B,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAhB,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENnG,OAAA;YAAK+F,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BhG,OAAA;cAAO+F,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC/BhG,OAAA;gBACEqC,IAAI,EAAC,UAAU;gBACfF,IAAI,EAAC,gBAAgB;gBACrBG,OAAO,EAAE/B,QAAQ,CAACa,cAAe;gBACjCoF,QAAQ,EAAEvE;cAAkB;gBAAAkD,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACFnG,OAAA;gBAAM+F,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAe;gBAAAb,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAhB,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eAERnG,OAAA;cAAO+F,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC/BhG,OAAA;gBACEqC,IAAI,EAAC,UAAU;gBACfF,IAAI,EAAC,UAAU;gBACfG,OAAO,EAAE/B,QAAQ,CAACc,QAAS;gBAC3BmF,QAAQ,EAAEvE;cAAkB;gBAAAkD,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACFnG,OAAA;gBAAM+F,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAS;gBAAAb,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAhB,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAhB,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAhB,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEArE,SAAS,KAAK,OAAO,iBACpB9B,OAAA;UAAK+F,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BhG,OAAA;YAAK+F,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClChG,OAAA;cAAAgG,QAAA,EAAI;YAAS;cAAAb,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBnG,OAAA,CAACL,cAAc;cACbqH,MAAM,EAAC,YAAY;cACnBC,QAAQ,EAAE,KAAM;cAChBC,eAAe,EAAGxE,KAAK,IAAKD,gBAAgB,CAACC,KAAK,EAAE,QAAQ,CAAE;cAC9DC,QAAQ,EAAC;YAAU;cAAAwC,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,EACD7E,aAAa,CAACE,MAAM,CAACoB,MAAM,GAAG,CAAC,iBAC9B5C,OAAA;cAAK+F,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5B1E,aAAa,CAACE,MAAM,CAACwC,GAAG,CAAC,CAACe,IAAI,EAAEjC,KAAK,kBACpC9C,OAAA;gBAAiB+F,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBACxChG,OAAA;kBAAAgG,QAAA,EAAOjB,IAAI,CAAC5C;gBAAI;kBAAAgD,QAAA,EAAAc,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxBnG,OAAA;kBACEqC,IAAI,EAAC,QAAQ;kBACb+D,OAAO,EAAEA,CAAA,KAAMvD,gBAAgB,CAACC,KAAK,EAAE,QAAQ,CAAE;kBACjDiD,SAAS,EAAC,aAAa;kBAAAC,QAAA,EACxB;gBAED;kBAAAb,QAAA,EAAAc,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GARDrD,KAAK;gBAAAqC,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASV,CACN;YAAC;cAAAhB,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAhB,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENnG,OAAA;YAAK+F,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClChG,OAAA;cAAAgG,QAAA,EAAI;YAAc;cAAAb,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBnG,OAAA,CAACL,cAAc;cACbqH,MAAM,EAAC,uBAAuB;cAC9BC,QAAQ,EAAE,IAAK;cACfC,eAAe,EAAGxE,KAAK,IAAKD,gBAAgB,CAACC,KAAK,EAAE,QAAQ,CAAE;cAC9DC,QAAQ,EAAC;YAAO;cAAAwC,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,EACD7E,aAAa,CAACG,MAAM,CAACmB,MAAM,GAAG,CAAC,iBAC9B5C,OAAA;cAAK+F,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5B1E,aAAa,CAACG,MAAM,CAACuC,GAAG,CAAC,CAACe,IAAI,EAAEjC,KAAK,kBACpC9C,OAAA;gBAAiB+F,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBACxChG,OAAA;kBAAAgG,QAAA,EAAOjB,IAAI,CAAC5C;gBAAI;kBAAAgD,QAAA,EAAAc,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxBnG,OAAA;kBACEqC,IAAI,EAAC,QAAQ;kBACb+D,OAAO,EAAEA,CAAA,KAAMvD,gBAAgB,CAACC,KAAK,EAAE,QAAQ,CAAE;kBACjDiD,SAAS,EAAC,aAAa;kBAAAC,QAAA,EACxB;gBAED;kBAAAb,QAAA,EAAAc,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GARDrD,KAAK;gBAAAqC,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASV,CACN;YAAC;cAAAhB,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAhB,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAhB,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEArE,SAAS,KAAK,SAAS,iBACtB9B,OAAA;UAAK+F,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BhG,OAAA;YAAK+F,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BtE,YAAY,gBACX1B,OAAA,CAACJ,cAAc;cAACuH,SAAS,EAAEzF;YAAa;cAAAyD,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE3CnG,OAAA;cAAK+F,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBhG,OAAA;gBAAAgG,QAAA,EAAG;cAAgC;gBAAAb,QAAA,EAAAc,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAhB,QAAA,EAAAc,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UACN;YAAAhB,QAAA,EAAAc,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAhB,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAhB,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEPnG,OAAA;QAAK+F,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BhG,OAAA;UACEqC,IAAI,EAAC,QAAQ;UACb0D,SAAS,EAAC,mBAAmB;UAC7BK,OAAO,EAAE/F,OAAQ;UACjB+G,QAAQ,EAAExF,OAAQ;UAAAoE,QAAA,EACnB;QAED;UAAAb,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnG,OAAA;UACEqC,IAAI,EAAC,QAAQ;UACb0D,SAAS,EAAC,iBAAiB;UAC3BK,OAAO,EAAE5C,YAAa;UACtB4D,QAAQ,EAAExF,OAAQ;UAAAoE,QAAA,EAEjBpE,OAAO,GAAG,WAAW,GAAI1B,OAAO,GAAG,gBAAgB,GAAG;QAAiB;UAAAiF,QAAA,EAAAc,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAhB,QAAA,EAAAc,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAhB,QAAA,EAAAc,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAhB,QAAA,EAAAc,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7F,EAAA,CA/dIL,gBAAgB;AAAAoH,EAAA,GAAhBpH,gBAAgB;AAietB,eAAeA,gBAAgB;AAAC,IAAAoH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}