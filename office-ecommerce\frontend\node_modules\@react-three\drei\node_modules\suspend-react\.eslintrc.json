{
    "env": {
      "browser": true,
      "es6": true,
      "node": true
    },
    "extends": [
      "prettier",
      "prettier/react",
      "prettier/@typescript-eslint",
      "plugin:prettier/recommended",
      "plugin:react-hooks/recommended",
      "plugin:import/errors",
      "plugin:import/warnings",
      "prettier",
      "prettier/react",
      "prettier/@typescript-eslint"
    ],
    "plugins": ["@typescript-eslint", "react", "react-hooks", "import", "jest", "prettier"],
    "parser": "@typescript-eslint/parser",
    "parserOptions": {
      "ecmaFeatures": {
        "jsx": true
      },
      "ecmaVersion": 2018,
      "sourceType": "module",
      "rules": {
        "curly": ["warn", "multi-line", "consistent"],
        "no-console": "off",
        "no-empty-pattern": "warn",
        "no-duplicate-imports": "error",
        "import/no-unresolved": "off",
        "import/export": "error",
        // https://github.com/typescript-eslint/typescript-eslint/blob/master/docs/getting-started/linting/FAQ.md#eslint-plugin-import
        // We recommend you do not use the following import/* rules, as TypeScript provides the same checks as part of standard type checking:
        "import/named": "off",
        "import/namespace": "off",
        "import/default": "off",
        "no-unused-vars": ["warn", { "argsIgnorePattern": "^_", "varsIgnorePattern": "^_" }],
        "@typescript-eslint/no-unused-vars": ["warn", { "argsIgnorePattern": "^_", "varsIgnorePattern": "^_" }],
        "@typescript-eslint/no-use-before-define": "off",
        "@typescript-eslint/no-empty-function": "off",
        "@typescript-eslint/no-empty-interface": "off",
        "@typescript-eslint/no-explicit-any": "off",
        "jest/consistent-test-it": ["error", { "fn": "it", "withinDescribe": "it" }]
      }
    },
    "settings": {
      "react": {
        "version": "detect"
      },
      "import/extensions": [".js", ".jsx", ".ts", ".tsx"],
      "import/parsers": {
        "@typescript-eslint/parser": [".js", ".jsx", ".ts", ".tsx"]
      },
      "import/resolver": {
        "node": {
          "extensions": [".js", ".jsx", ".ts", ".tsx", ".json"],
          "paths": ["src"]
        },
        "alias": {
          "extensions": [".js", ".jsx", ".ts", ".tsx", ".json"],
          "map": [["react-three-fiber", "./src/targets/web.tsx"]]
        }
      }
    },
    "overrides": [
      {
        "files": ["src"],
        "parserOptions": {
          "project": "./tsconfig.json"
        }
      }
    ]
  }
  