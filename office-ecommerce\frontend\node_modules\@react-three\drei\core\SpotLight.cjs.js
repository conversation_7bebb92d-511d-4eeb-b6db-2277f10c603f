"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),a=require("@react-three/fiber"),n=require("three-stdlib"),o=require("react-merge-refs"),i=require("../materials/SpotLightMaterial.cjs.js");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function l(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var a=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,a.get?a:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var s=u(e),c=l(t),p=u(o);function d({opacity:e=1,radiusTop:t,radiusBottom:n,depthBuffer:o,color:u="white",distance:l=5,angle:s=.15,attenuation:p=5,anglePower:d=5}){const m=c.useRef(null),h=a.useThree((e=>e.size)),f=a.useThree((e=>e.camera)),g=a.useThree((e=>e.viewport.dpr)),[v]=c.useState((()=>new i.SpotLightMaterial)),[w]=c.useState((()=>new r.Vector3));t=void 0===t?.1:t,n=void 0===n?7*s:n,a.useFrame((()=>{v.uniforms.spotPosition.value.copy(m.current.getWorldPosition(w)),m.current.lookAt(m.current.parent.target.getWorldPosition(w))}));const S=c.useMemo((()=>{const e=new r.CylinderGeometry(t,n,l,128,64,!0);return e.applyMatrix4((new r.Matrix4).makeTranslation(0,-l/2,0)),e.applyMatrix4((new r.Matrix4).makeRotationX(-Math.PI/2)),e}),[l,t,n]);return c.createElement(c.Fragment,null,c.createElement("mesh",{ref:m,geometry:S,raycast:()=>null},c.createElement("primitive",{object:v,attach:"material","uniforms-opacity-value":e,"uniforms-lightColor-value":u,"uniforms-attenuation-value":p,"uniforms-anglePower-value":d,"uniforms-depth-value":o,"uniforms-cameraNear-value":f.near,"uniforms-cameraFar-value":f.far,"uniforms-resolution-value":o?[h.width*g,h.height*g]:[0,0]})))}function m(e,t,n,o,i){const[[u,l]]=c.useState((()=>[new r.Vector3,new r.Vector3]));c.useLayoutEffect((()=>{if(!(null==(t=e.current)?void 0:t.isSpotLight))throw new Error("SpotlightShadow must be a child of a SpotLight");var t;e.current.shadow.mapSize.set(n,o),e.current.shadow.needsUpdate=!0}),[e,n,o]),a.useFrame((()=>{if(!e.current)return;const r=e.current.position,a=e.current.target.position;l.copy(a).sub(r);var n=l.length();l.normalize().multiplyScalar(n*i),u.copy(r).add(l),t.current.position.copy(u),t.current.lookAt(e.current.target.position)}))}function h({distance:e=.4,alphaTest:t=.5,map:o,shader:i="#define GLSLIFY 1\nvarying vec2 vUv;uniform sampler2D uShadowMap;uniform float uTime;void main(){vec3 color=texture2D(uShadowMap,vUv).xyz;gl_FragColor=vec4(color,1.);}",width:u=512,height:l=512,scale:s=1,children:p,...d}){const h=c.useRef(null),f=d.spotlightRef,g=d.debug;m(f,h,u,l,e);const v=c.useMemo((()=>new r.WebGLRenderTarget(u,l,{format:r.RGBAFormat,encoding:r.LinearEncoding,stencilBuffer:!1})),[u,l]),w=c.useRef({uShadowMap:{value:o},uTime:{value:0}});c.useEffect((()=>{w.current.uShadowMap.value=o}),[o]);const S=c.useMemo((()=>new n.FullScreenQuad(new r.ShaderMaterial({uniforms:w.current,vertexShader:"\n          varying vec2 vUv;\n\n          void main() {\n            vUv = uv;\n            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n          }\n          ",fragmentShader:i}))),[i]);return c.useEffect((()=>()=>{S.material.dispose(),S.dispose()}),[S]),c.useEffect((()=>()=>v.dispose()),[v]),a.useFrame((({gl:e},t)=>{w.current.uTime.value+=t,e.setRenderTarget(v),S.render(e),e.setRenderTarget(null)})),c.createElement(c.Fragment,null,c.createElement("mesh",{ref:h,scale:s,castShadow:!0},c.createElement("planeGeometry",null),c.createElement("meshBasicMaterial",{transparent:!0,side:r.DoubleSide,alphaTest:t,alphaMap:v.texture,"alphaMap-wrapS":r.RepeatWrapping,"alphaMap-wrapT":r.RepeatWrapping,opacity:g?1:0},p)))}function f({distance:e=.4,alphaTest:t=.5,map:a,width:n=512,height:o=512,scale:i,children:u,...l}){const s=c.useRef(null),p=l.spotlightRef,d=l.debug;return m(p,s,n,o,e),c.createElement(c.Fragment,null,c.createElement("mesh",{ref:s,scale:i,castShadow:!0},c.createElement("planeGeometry",null),c.createElement("meshBasicMaterial",{transparent:!0,side:r.DoubleSide,alphaTest:t,alphaMap:a,"alphaMap-wrapS":r.RepeatWrapping,"alphaMap-wrapT":r.RepeatWrapping,opacity:d?1:0},u)))}const g=c.forwardRef((({opacity:e=1,radiusTop:t,radiusBottom:r,depthBuffer:a,color:n="white",distance:o=5,angle:i=.15,attenuation:u=5,anglePower:l=5,volumetric:m=!0,debug:h=!1,children:f,...g},v)=>{const w=c.useRef(null);return c.createElement("group",null,h&&w.current&&c.createElement("spotLightHelper",{args:[w.current]}),c.createElement("spotLight",s.default({ref:p.default([v,w]),angle:i,color:n,distance:o,castShadow:!0},g),m&&c.createElement(d,{debug:h,opacity:e,radiusTop:t,radiusBottom:r,depthBuffer:a,color:n,distance:o,angle:i,attenuation:u,anglePower:l})),f&&c.cloneElement(f,{spotlightRef:w,debug:h}))}));exports.SpotLight=g,exports.SpotLightShadow=function(e){return e.shader?c.createElement(h,e):c.createElement(f,e)};
