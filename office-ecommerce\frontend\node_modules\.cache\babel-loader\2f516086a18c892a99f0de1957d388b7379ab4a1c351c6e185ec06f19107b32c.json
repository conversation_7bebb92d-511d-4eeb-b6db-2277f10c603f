{"ast": null, "code": "import { ShaderMaterial, Uniform, Vector2, NoBlending } from 'three';\nclass ConvolutionMaterial extends ShaderMaterial {\n  constructor(texelSize = new Vector2()) {\n    super({\n      uniforms: {\n        inputBuffer: new Uniform(null),\n        depthBuffer: new Uniform(null),\n        resolution: new Uniform(new Vector2()),\n        texelSize: new Uniform(new Vector2()),\n        halfTexelSize: new Uniform(new Vector2()),\n        kernel: new Uniform(0.0),\n        scale: new Uniform(1.0),\n        cameraNear: new Uniform(0.0),\n        cameraFar: new Uniform(1.0),\n        minDepthThreshold: new Uniform(0.0),\n        maxDepthThreshold: new Uniform(1.0),\n        depthScale: new Uniform(0.0),\n        depthToBlurRatioBias: new Uniform(0.25)\n      },\n      fragmentShader: `#include <common>\n        #include <dithering_pars_fragment>      \n        uniform sampler2D inputBuffer;\n        uniform sampler2D depthBuffer;\n        uniform float cameraNear;\n        uniform float cameraFar;\n        uniform float minDepthThreshold;\n        uniform float maxDepthThreshold;\n        uniform float depthScale;\n        uniform float depthToBlurRatioBias;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          float depthFactor = 0.0;\n          \n          #ifdef USE_DEPTH\n            vec4 depth = texture2D(depthBuffer, vUv);\n            depthFactor = smoothstep(minDepthThreshold, maxDepthThreshold, 1.0-(depth.r * depth.a));\n            depthFactor *= depthScale;\n            depthFactor = max(0.0, min(1.0, depthFactor + 0.25));\n          #endif\n          \n          vec4 sum = texture2D(inputBuffer, mix(vUv0, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv1, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv2, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv3, vUv, depthFactor));\n          gl_FragColor = sum * 0.25 ;\n\n          #include <dithering_fragment>\n          #include <tonemapping_fragment>\n          #include <encodings_fragment>\n        }`,\n      vertexShader: `uniform vec2 texelSize;\n        uniform vec2 halfTexelSize;\n        uniform float kernel;\n        uniform float scale;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          vec2 uv = position.xy * 0.5 + 0.5;\n          vUv = uv;\n\n          vec2 dUv = (texelSize * vec2(kernel) + halfTexelSize) * scale;\n          vUv0 = vec2(uv.x - dUv.x, uv.y + dUv.y);\n          vUv1 = vec2(uv.x + dUv.x, uv.y + dUv.y);\n          vUv2 = vec2(uv.x + dUv.x, uv.y - dUv.y);\n          vUv3 = vec2(uv.x - dUv.x, uv.y - dUv.y);\n\n          gl_Position = vec4(position.xy, 1.0, 1.0);\n        }`,\n      blending: NoBlending,\n      depthWrite: false,\n      depthTest: false\n    });\n    this.toneMapped = false;\n    this.setTexelSize(texelSize.x, texelSize.y);\n    this.kernel = new Float32Array([0.0, 1.0, 2.0, 2.0, 3.0]);\n  }\n  setTexelSize(x, y) {\n    this.uniforms.texelSize.value.set(x, y);\n    this.uniforms.halfTexelSize.value.set(x, y).multiplyScalar(0.5);\n  }\n  setResolution(resolution) {\n    this.uniforms.resolution.value.copy(resolution);\n  }\n}\nexport { ConvolutionMaterial };", "map": {"version": 3, "names": ["ShaderMaterial", "Uniform", "Vector2", "NoBlending", "ConvolutionMaterial", "constructor", "texelSize", "uniforms", "inputBuffer", "depthBuffer", "resolution", "halfTexelSize", "kernel", "scale", "cameraNear", "cameraFar", "minDepthThr<PERSON>old", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "depthScale", "depthToBlurRatioBias", "fragmentShader", "vertexShader", "blending", "depthWrite", "depthTest", "toneMapped", "setTexelSize", "x", "y", "Float32Array", "value", "set", "multiplyScalar", "setResolution", "copy"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/materials/ConvolutionMaterial.js"], "sourcesContent": ["import { ShaderMaterial, Uniform, Vector2, NoBlending } from 'three';\n\nclass ConvolutionMaterial extends ShaderMaterial {\n  constructor(texelSize = new Vector2()) {\n    super({\n      uniforms: {\n        inputBuffer: new Uniform(null),\n        depthBuffer: new Uniform(null),\n        resolution: new Uniform(new Vector2()),\n        texelSize: new Uniform(new Vector2()),\n        halfTexelSize: new Uniform(new Vector2()),\n        kernel: new Uniform(0.0),\n        scale: new Uniform(1.0),\n        cameraNear: new Uniform(0.0),\n        cameraFar: new Uniform(1.0),\n        minDepthThreshold: new Uniform(0.0),\n        maxDepthThreshold: new Uniform(1.0),\n        depthScale: new Uniform(0.0),\n        depthToBlurRatioBias: new Uniform(0.25)\n      },\n      fragmentShader: `#include <common>\n        #include <dithering_pars_fragment>      \n        uniform sampler2D inputBuffer;\n        uniform sampler2D depthBuffer;\n        uniform float cameraNear;\n        uniform float cameraFar;\n        uniform float minDepthThreshold;\n        uniform float maxDepthThreshold;\n        uniform float depthScale;\n        uniform float depthToBlurRatioBias;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          float depthFactor = 0.0;\n          \n          #ifdef USE_DEPTH\n            vec4 depth = texture2D(depthBuffer, vUv);\n            depthFactor = smoothstep(minDepthThreshold, maxDepthThreshold, 1.0-(depth.r * depth.a));\n            depthFactor *= depthScale;\n            depthFactor = max(0.0, min(1.0, depthFactor + 0.25));\n          #endif\n          \n          vec4 sum = texture2D(inputBuffer, mix(vUv0, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv1, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv2, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv3, vUv, depthFactor));\n          gl_FragColor = sum * 0.25 ;\n\n          #include <dithering_fragment>\n          #include <tonemapping_fragment>\n          #include <encodings_fragment>\n        }`,\n      vertexShader: `uniform vec2 texelSize;\n        uniform vec2 halfTexelSize;\n        uniform float kernel;\n        uniform float scale;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          vec2 uv = position.xy * 0.5 + 0.5;\n          vUv = uv;\n\n          vec2 dUv = (texelSize * vec2(kernel) + halfTexelSize) * scale;\n          vUv0 = vec2(uv.x - dUv.x, uv.y + dUv.y);\n          vUv1 = vec2(uv.x + dUv.x, uv.y + dUv.y);\n          vUv2 = vec2(uv.x + dUv.x, uv.y - dUv.y);\n          vUv3 = vec2(uv.x - dUv.x, uv.y - dUv.y);\n\n          gl_Position = vec4(position.xy, 1.0, 1.0);\n        }`,\n      blending: NoBlending,\n      depthWrite: false,\n      depthTest: false\n    });\n    this.toneMapped = false;\n    this.setTexelSize(texelSize.x, texelSize.y);\n    this.kernel = new Float32Array([0.0, 1.0, 2.0, 2.0, 3.0]);\n  }\n\n  setTexelSize(x, y) {\n    this.uniforms.texelSize.value.set(x, y);\n    this.uniforms.halfTexelSize.value.set(x, y).multiplyScalar(0.5);\n  }\n\n  setResolution(resolution) {\n    this.uniforms.resolution.value.copy(resolution);\n  }\n\n}\n\nexport { ConvolutionMaterial };\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,QAAQ,OAAO;AAEpE,MAAMC,mBAAmB,SAASJ,cAAc,CAAC;EAC/CK,WAAWA,CAACC,SAAS,GAAG,IAAIJ,OAAO,CAAC,CAAC,EAAE;IACrC,KAAK,CAAC;MACJK,QAAQ,EAAE;QACRC,WAAW,EAAE,IAAIP,OAAO,CAAC,IAAI,CAAC;QAC9BQ,WAAW,EAAE,IAAIR,OAAO,CAAC,IAAI,CAAC;QAC9BS,UAAU,EAAE,IAAIT,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,CAAC;QACtCI,SAAS,EAAE,IAAIL,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,CAAC;QACrCS,aAAa,EAAE,IAAIV,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,CAAC;QACzCU,MAAM,EAAE,IAAIX,OAAO,CAAC,GAAG,CAAC;QACxBY,KAAK,EAAE,IAAIZ,OAAO,CAAC,GAAG,CAAC;QACvBa,UAAU,EAAE,IAAIb,OAAO,CAAC,GAAG,CAAC;QAC5Bc,SAAS,EAAE,IAAId,OAAO,CAAC,GAAG,CAAC;QAC3Be,iBAAiB,EAAE,IAAIf,OAAO,CAAC,GAAG,CAAC;QACnCgB,iBAAiB,EAAE,IAAIhB,OAAO,CAAC,GAAG,CAAC;QACnCiB,UAAU,EAAE,IAAIjB,OAAO,CAAC,GAAG,CAAC;QAC5BkB,oBAAoB,EAAE,IAAIlB,OAAO,CAAC,IAAI;MACxC,CAAC;MACDmB,cAAc,EAAE;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;MACJC,YAAY,EAAE;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;MACJC,QAAQ,EAAEnB,UAAU;MACpBoB,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE;IACb,CAAC,CAAC;IACF,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,YAAY,CAACpB,SAAS,CAACqB,CAAC,EAAErB,SAAS,CAACsB,CAAC,CAAC;IAC3C,IAAI,CAAChB,MAAM,GAAG,IAAIiB,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;EAC3D;EAEAH,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACjB,IAAI,CAACrB,QAAQ,CAACD,SAAS,CAACwB,KAAK,CAACC,GAAG,CAACJ,CAAC,EAAEC,CAAC,CAAC;IACvC,IAAI,CAACrB,QAAQ,CAACI,aAAa,CAACmB,KAAK,CAACC,GAAG,CAACJ,CAAC,EAAEC,CAAC,CAAC,CAACI,cAAc,CAAC,GAAG,CAAC;EACjE;EAEAC,aAAaA,CAACvB,UAAU,EAAE;IACxB,IAAI,CAACH,QAAQ,CAACG,UAAU,CAACoB,KAAK,CAACI,IAAI,CAACxB,UAAU,CAAC;EACjD;AAEF;AAEA,SAASN,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}