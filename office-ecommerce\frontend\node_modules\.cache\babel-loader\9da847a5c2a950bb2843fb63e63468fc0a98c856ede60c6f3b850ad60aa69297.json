{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { extend, useThree } from '@react-three/fiber';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { useTexture } from './Texture.js';\nimport { version } from '../helpers/constants.js';\n\n// {texture: THREE.Texture} XOR {url: string}\n\nconst ImageMaterialImpl = /* @__PURE__ */shaderMaterial({\n  color: /* @__PURE__ */new THREE.Color('white'),\n  scale: /* @__PURE__ */new THREE.Vector2(1, 1),\n  imageBounds: /* @__PURE__ */new THREE.Vector2(1, 1),\n  resolution: 1024,\n  map: null,\n  zoom: 1,\n  radius: 0,\n  grayscale: 0,\n  opacity: 1\n}, /* glsl */`\n  varying vec2 vUv;\n  varying vec2 vPos;\n  void main() {\n    gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n    vUv = uv;\n    vPos = position.xy;\n  }\n`, /* glsl */`\n  // mostly from https://gist.github.com/statico/df64c5d167362ecf7b34fca0b1459a44\n  varying vec2 vUv;\n  varying vec2 vPos;\n  uniform vec2 scale;\n  uniform vec2 imageBounds;\n  uniform float resolution;\n  uniform vec3 color;\n  uniform sampler2D map;\n  uniform float radius;\n  uniform float zoom;\n  uniform float grayscale;\n  uniform float opacity;\n  const vec3 luma = vec3(.299, 0.587, 0.114);\n  vec4 toGrayscale(vec4 color, float intensity) {\n    return vec4(mix(color.rgb, vec3(dot(color.rgb, luma)), intensity), color.a);\n  }\n  vec2 aspect(vec2 size) {\n    return size / min(size.x, size.y);\n  }\n  \n  const float PI = 3.14159265;\n    \n  // from https://iquilezles.org/articles/distfunctions\n  float udRoundBox( vec2 p, vec2 b, float r ) {\n    return length(max(abs(p)-b+r,0.0))-r;\n  }\n\n  void main() {\n    vec2 s = aspect(scale);\n    vec2 i = aspect(imageBounds);\n    float rs = s.x / s.y;\n    float ri = i.x / i.y;\n    vec2 new = rs < ri ? vec2(i.x * s.y / i.y, s.y) : vec2(s.x, i.y * s.x / i.x);\n    vec2 offset = (rs < ri ? vec2((new.x - s.x) / 2.0, 0.0) : vec2(0.0, (new.y - s.y) / 2.0)) / new;\n    vec2 uv = vUv * s / new + offset;\n    vec2 zUv = (uv - vec2(0.5, 0.5)) / zoom + vec2(0.5, 0.5);\n\n    vec2 res = vec2(scale * resolution);\n    vec2 halfRes = 0.5 * res;\n    float b = udRoundBox(vUv.xy * res - halfRes, halfRes, resolution * radius);    \n\t  vec3 a = mix(vec3(1.0,0.0,0.0), vec3(0.0,0.0,0.0), smoothstep(0.0, 1.0, b));\n    gl_FragColor = toGrayscale(texture2D(map, zUv) * vec4(color, opacity * a), grayscale);\n    \n    #include <tonemapping_fragment>\n    #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n  }\n`);\nconst ImageBase = /* @__PURE__ */React.forwardRef(({\n  children,\n  color,\n  segments = 1,\n  scale = 1,\n  zoom = 1,\n  grayscale = 0,\n  opacity = 1,\n  radius = 0,\n  texture,\n  toneMapped,\n  transparent,\n  side,\n  ...props\n}, fref) => {\n  extend({\n    ImageMaterial: ImageMaterialImpl\n  });\n  const ref = React.useRef(null);\n  const size = useThree(state => state.size);\n  const planeBounds = Array.isArray(scale) ? [scale[0], scale[1]] : [scale, scale];\n  const imageBounds = [texture.image.width, texture.image.height];\n  const resolution = Math.max(size.width, size.height);\n  React.useImperativeHandle(fref, () => ref.current, []);\n  React.useLayoutEffect(() => {\n    // Support arbitrary plane geometries (for instance with rounded corners)\n    // @ts-ignore\n    if (ref.current.geometry.parameters) {\n      // @ts-ignore\n      ref.current.material.scale.set(\n      // @ts-ignore\n      planeBounds[0] * ref.current.geometry.parameters.width,\n      // @ts-ignore\n      planeBounds[1] * ref.current.geometry.parameters.height);\n    }\n  }, [planeBounds[0], planeBounds[1]]);\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref,\n    scale: Array.isArray(scale) ? [...scale, 1] : scale\n  }, props), /*#__PURE__*/React.createElement(\"planeGeometry\", {\n    args: [1, 1, segments, segments]\n  }), /*#__PURE__*/React.createElement(\"imageMaterial\", {\n    color: color,\n    map: texture,\n    zoom: zoom,\n    grayscale: grayscale,\n    opacity: opacity,\n    scale: planeBounds,\n    imageBounds: imageBounds,\n    resolution: resolution,\n    radius: radius,\n    toneMapped: toneMapped,\n    transparent: transparent,\n    side: side,\n    key: ImageMaterialImpl.key\n  }), children);\n});\nconst ImageWithUrl = /* @__PURE__ */React.forwardRef(({\n  url,\n  ...props\n}, ref) => {\n  const texture = useTexture(url);\n  return /*#__PURE__*/React.createElement(ImageBase, _extends({}, props, {\n    texture: texture,\n    ref: ref\n  }));\n});\nconst ImageWithTexture = /* @__PURE__ */React.forwardRef(({\n  url: _url,\n  ...props\n}, ref) => {\n  return /*#__PURE__*/React.createElement(ImageBase, _extends({}, props, {\n    ref: ref\n  }));\n});\nconst Image = /* @__PURE__ */React.forwardRef((props, ref) => {\n  if (props.url) return /*#__PURE__*/React.createElement(ImageWithUrl, _extends({}, props, {\n    ref: ref\n  }));else if (props.texture) return /*#__PURE__*/React.createElement(ImageWithTexture, _extends({}, props, {\n    ref: ref\n  }));else throw new Error('<Image /> requires a url or texture');\n});\nexport { Image };", "map": {"version": 3, "names": ["_extends", "React", "THREE", "extend", "useThree", "shaderMaterial", "useTexture", "version", "ImageMaterialImpl", "color", "Color", "scale", "Vector2", "imageBounds", "resolution", "map", "zoom", "radius", "grayscale", "opacity", "ImageBase", "forwardRef", "children", "segments", "texture", "toneMapped", "transparent", "side", "props", "fref", "ImageMaterial", "ref", "useRef", "size", "state", "planeBounds", "Array", "isArray", "image", "width", "height", "Math", "max", "useImperativeHandle", "current", "useLayoutEffect", "geometry", "parameters", "material", "set", "createElement", "args", "key", "ImageWithUrl", "url", "ImageWithTexture", "_url", "Image", "Error"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Image.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { extend, useThree } from '@react-three/fiber';\nimport { shaderMaterial } from './shaderMaterial.js';\nimport { useTexture } from './Texture.js';\nimport { version } from '../helpers/constants.js';\n\n// {texture: THREE.Texture} XOR {url: string}\n\nconst ImageMaterialImpl = /* @__PURE__ */shaderMaterial({\n  color: /* @__PURE__ */new THREE.Color('white'),\n  scale: /* @__PURE__ */new THREE.Vector2(1, 1),\n  imageBounds: /* @__PURE__ */new THREE.Vector2(1, 1),\n  resolution: 1024,\n  map: null,\n  zoom: 1,\n  radius: 0,\n  grayscale: 0,\n  opacity: 1\n}, /* glsl */`\n  varying vec2 vUv;\n  varying vec2 vPos;\n  void main() {\n    gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n    vUv = uv;\n    vPos = position.xy;\n  }\n`, /* glsl */`\n  // mostly from https://gist.github.com/statico/df64c5d167362ecf7b34fca0b1459a44\n  varying vec2 vUv;\n  varying vec2 vPos;\n  uniform vec2 scale;\n  uniform vec2 imageBounds;\n  uniform float resolution;\n  uniform vec3 color;\n  uniform sampler2D map;\n  uniform float radius;\n  uniform float zoom;\n  uniform float grayscale;\n  uniform float opacity;\n  const vec3 luma = vec3(.299, 0.587, 0.114);\n  vec4 toGrayscale(vec4 color, float intensity) {\n    return vec4(mix(color.rgb, vec3(dot(color.rgb, luma)), intensity), color.a);\n  }\n  vec2 aspect(vec2 size) {\n    return size / min(size.x, size.y);\n  }\n  \n  const float PI = 3.14159265;\n    \n  // from https://iquilezles.org/articles/distfunctions\n  float udRoundBox( vec2 p, vec2 b, float r ) {\n    return length(max(abs(p)-b+r,0.0))-r;\n  }\n\n  void main() {\n    vec2 s = aspect(scale);\n    vec2 i = aspect(imageBounds);\n    float rs = s.x / s.y;\n    float ri = i.x / i.y;\n    vec2 new = rs < ri ? vec2(i.x * s.y / i.y, s.y) : vec2(s.x, i.y * s.x / i.x);\n    vec2 offset = (rs < ri ? vec2((new.x - s.x) / 2.0, 0.0) : vec2(0.0, (new.y - s.y) / 2.0)) / new;\n    vec2 uv = vUv * s / new + offset;\n    vec2 zUv = (uv - vec2(0.5, 0.5)) / zoom + vec2(0.5, 0.5);\n\n    vec2 res = vec2(scale * resolution);\n    vec2 halfRes = 0.5 * res;\n    float b = udRoundBox(vUv.xy * res - halfRes, halfRes, resolution * radius);    \n\t  vec3 a = mix(vec3(1.0,0.0,0.0), vec3(0.0,0.0,0.0), smoothstep(0.0, 1.0, b));\n    gl_FragColor = toGrayscale(texture2D(map, zUv) * vec4(color, opacity * a), grayscale);\n    \n    #include <tonemapping_fragment>\n    #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n  }\n`);\nconst ImageBase = /* @__PURE__ */React.forwardRef(({\n  children,\n  color,\n  segments = 1,\n  scale = 1,\n  zoom = 1,\n  grayscale = 0,\n  opacity = 1,\n  radius = 0,\n  texture,\n  toneMapped,\n  transparent,\n  side,\n  ...props\n}, fref) => {\n  extend({\n    ImageMaterial: ImageMaterialImpl\n  });\n  const ref = React.useRef(null);\n  const size = useThree(state => state.size);\n  const planeBounds = Array.isArray(scale) ? [scale[0], scale[1]] : [scale, scale];\n  const imageBounds = [texture.image.width, texture.image.height];\n  const resolution = Math.max(size.width, size.height);\n  React.useImperativeHandle(fref, () => ref.current, []);\n  React.useLayoutEffect(() => {\n    // Support arbitrary plane geometries (for instance with rounded corners)\n    // @ts-ignore\n    if (ref.current.geometry.parameters) {\n      // @ts-ignore\n      ref.current.material.scale.set(\n      // @ts-ignore\n      planeBounds[0] * ref.current.geometry.parameters.width,\n      // @ts-ignore\n      planeBounds[1] * ref.current.geometry.parameters.height);\n    }\n  }, [planeBounds[0], planeBounds[1]]);\n  return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n    ref: ref,\n    scale: Array.isArray(scale) ? [...scale, 1] : scale\n  }, props), /*#__PURE__*/React.createElement(\"planeGeometry\", {\n    args: [1, 1, segments, segments]\n  }), /*#__PURE__*/React.createElement(\"imageMaterial\", {\n    color: color,\n    map: texture,\n    zoom: zoom,\n    grayscale: grayscale,\n    opacity: opacity,\n    scale: planeBounds,\n    imageBounds: imageBounds,\n    resolution: resolution,\n    radius: radius,\n    toneMapped: toneMapped,\n    transparent: transparent,\n    side: side,\n    key: ImageMaterialImpl.key\n  }), children);\n});\nconst ImageWithUrl = /* @__PURE__ */React.forwardRef(({\n  url,\n  ...props\n}, ref) => {\n  const texture = useTexture(url);\n  return /*#__PURE__*/React.createElement(ImageBase, _extends({}, props, {\n    texture: texture,\n    ref: ref\n  }));\n});\nconst ImageWithTexture = /* @__PURE__ */React.forwardRef(({\n  url: _url,\n  ...props\n}, ref) => {\n  return /*#__PURE__*/React.createElement(ImageBase, _extends({}, props, {\n    ref: ref\n  }));\n});\nconst Image = /* @__PURE__ */React.forwardRef((props, ref) => {\n  if (props.url) return /*#__PURE__*/React.createElement(ImageWithUrl, _extends({}, props, {\n    ref: ref\n  }));else if (props.texture) return /*#__PURE__*/React.createElement(ImageWithTexture, _extends({}, props, {\n    ref: ref\n  }));else throw new Error('<Image /> requires a url or texture');\n});\n\nexport { Image };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,OAAO,QAAQ,yBAAyB;;AAEjD;;AAEA,MAAMC,iBAAiB,GAAG,eAAeH,cAAc,CAAC;EACtDI,KAAK,EAAE,eAAe,IAAIP,KAAK,CAACQ,KAAK,CAAC,OAAO,CAAC;EAC9CC,KAAK,EAAE,eAAe,IAAIT,KAAK,CAACU,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7CC,WAAW,EAAE,eAAe,IAAIX,KAAK,CAACU,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACnDE,UAAU,EAAE,IAAI;EAChBC,GAAG,EAAE,IAAI;EACTC,IAAI,EAAE,CAAC;EACPC,MAAM,EAAE,CAAC;EACTC,SAAS,EAAE,CAAC;EACZC,OAAO,EAAE;AACX,CAAC,EAAE,UAAU;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAE,UAAU;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBZ,OAAO,IAAI,GAAG,GAAG,qBAAqB,GAAG,oBAAoB;AAC7E;AACA,CAAC,CAAC;AACF,MAAMa,SAAS,GAAG,eAAenB,KAAK,CAACoB,UAAU,CAAC,CAAC;EACjDC,QAAQ;EACRb,KAAK;EACLc,QAAQ,GAAG,CAAC;EACZZ,KAAK,GAAG,CAAC;EACTK,IAAI,GAAG,CAAC;EACRE,SAAS,GAAG,CAAC;EACbC,OAAO,GAAG,CAAC;EACXF,MAAM,GAAG,CAAC;EACVO,OAAO;EACPC,UAAU;EACVC,WAAW;EACXC,IAAI;EACJ,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV1B,MAAM,CAAC;IACL2B,aAAa,EAAEtB;EACjB,CAAC,CAAC;EACF,MAAMuB,GAAG,GAAG9B,KAAK,CAAC+B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,IAAI,GAAG7B,QAAQ,CAAC8B,KAAK,IAAIA,KAAK,CAACD,IAAI,CAAC;EAC1C,MAAME,WAAW,GAAGC,KAAK,CAACC,OAAO,CAAC1B,KAAK,CAAC,GAAG,CAACA,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAACA,KAAK,EAAEA,KAAK,CAAC;EAChF,MAAME,WAAW,GAAG,CAACW,OAAO,CAACc,KAAK,CAACC,KAAK,EAAEf,OAAO,CAACc,KAAK,CAACE,MAAM,CAAC;EAC/D,MAAM1B,UAAU,GAAG2B,IAAI,CAACC,GAAG,CAACT,IAAI,CAACM,KAAK,EAAEN,IAAI,CAACO,MAAM,CAAC;EACpDvC,KAAK,CAAC0C,mBAAmB,CAACd,IAAI,EAAE,MAAME,GAAG,CAACa,OAAO,EAAE,EAAE,CAAC;EACtD3C,KAAK,CAAC4C,eAAe,CAAC,MAAM;IAC1B;IACA;IACA,IAAId,GAAG,CAACa,OAAO,CAACE,QAAQ,CAACC,UAAU,EAAE;MACnC;MACAhB,GAAG,CAACa,OAAO,CAACI,QAAQ,CAACrC,KAAK,CAACsC,GAAG;MAC9B;MACAd,WAAW,CAAC,CAAC,CAAC,GAAGJ,GAAG,CAACa,OAAO,CAACE,QAAQ,CAACC,UAAU,CAACR,KAAK;MACtD;MACAJ,WAAW,CAAC,CAAC,CAAC,GAAGJ,GAAG,CAACa,OAAO,CAACE,QAAQ,CAACC,UAAU,CAACP,MAAM,CAAC;IAC1D;EACF,CAAC,EAAE,CAACL,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;EACpC,OAAO,aAAalC,KAAK,CAACiD,aAAa,CAAC,MAAM,EAAElD,QAAQ,CAAC;IACvD+B,GAAG,EAAEA,GAAG;IACRpB,KAAK,EAAEyB,KAAK,CAACC,OAAO,CAAC1B,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK,EAAE,CAAC,CAAC,GAAGA;EAChD,CAAC,EAAEiB,KAAK,CAAC,EAAE,aAAa3B,KAAK,CAACiD,aAAa,CAAC,eAAe,EAAE;IAC3DC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE5B,QAAQ,EAAEA,QAAQ;EACjC,CAAC,CAAC,EAAE,aAAatB,KAAK,CAACiD,aAAa,CAAC,eAAe,EAAE;IACpDzC,KAAK,EAAEA,KAAK;IACZM,GAAG,EAAES,OAAO;IACZR,IAAI,EAAEA,IAAI;IACVE,SAAS,EAAEA,SAAS;IACpBC,OAAO,EAAEA,OAAO;IAChBR,KAAK,EAAEwB,WAAW;IAClBtB,WAAW,EAAEA,WAAW;IACxBC,UAAU,EAAEA,UAAU;IACtBG,MAAM,EAAEA,MAAM;IACdQ,UAAU,EAAEA,UAAU;IACtBC,WAAW,EAAEA,WAAW;IACxBC,IAAI,EAAEA,IAAI;IACVyB,GAAG,EAAE5C,iBAAiB,CAAC4C;EACzB,CAAC,CAAC,EAAE9B,QAAQ,CAAC;AACf,CAAC,CAAC;AACF,MAAM+B,YAAY,GAAG,eAAepD,KAAK,CAACoB,UAAU,CAAC,CAAC;EACpDiC,GAAG;EACH,GAAG1B;AACL,CAAC,EAAEG,GAAG,KAAK;EACT,MAAMP,OAAO,GAAGlB,UAAU,CAACgD,GAAG,CAAC;EAC/B,OAAO,aAAarD,KAAK,CAACiD,aAAa,CAAC9B,SAAS,EAAEpB,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;IACrEJ,OAAO,EAAEA,OAAO;IAChBO,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,MAAMwB,gBAAgB,GAAG,eAAetD,KAAK,CAACoB,UAAU,CAAC,CAAC;EACxDiC,GAAG,EAAEE,IAAI;EACT,GAAG5B;AACL,CAAC,EAAEG,GAAG,KAAK;EACT,OAAO,aAAa9B,KAAK,CAACiD,aAAa,CAAC9B,SAAS,EAAEpB,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;IACrEG,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,MAAM0B,KAAK,GAAG,eAAexD,KAAK,CAACoB,UAAU,CAAC,CAACO,KAAK,EAAEG,GAAG,KAAK;EAC5D,IAAIH,KAAK,CAAC0B,GAAG,EAAE,OAAO,aAAarD,KAAK,CAACiD,aAAa,CAACG,YAAY,EAAErD,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;IACvFG,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC,CAAC,KAAK,IAAIH,KAAK,CAACJ,OAAO,EAAE,OAAO,aAAavB,KAAK,CAACiD,aAAa,CAACK,gBAAgB,EAAEvD,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;IACxGG,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,IAAI2B,KAAK,CAAC,qCAAqC,CAAC;AACjE,CAAC,CAAC;AAEF,SAASD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}