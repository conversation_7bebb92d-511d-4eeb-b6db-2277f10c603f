{"ast": null, "code": "import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { AxisArrow } from './AxisArrow.js';\nimport { PlaneSlider } from './PlaneSlider.js';\nimport { AxisRotator } from './AxisRotator.js';\nimport { context } from './context.js';\nconst tV0 = new THREE.Vector3();\nconst tV1 = new THREE.Vector3();\nconst tV2 = new THREE.Vector3();\nconst getPoint2 = (point3, camera, size) => {\n  const widthHalf = size.width / 2;\n  const heightHalf = size.height / 2;\n  camera.updateMatrixWorld(false);\n  const vector = point3.project(camera);\n  vector.x = vector.x * widthHalf + widthHalf;\n  vector.y = -(vector.y * heightHalf) + heightHalf;\n  return vector;\n};\nconst getPoint3 = (point2, camera, size, zValue = 1) => {\n  const vector = tV0.set(point2.x / size.width * 2 - 1, -(point2.y / size.height) * 2 + 1, zValue);\n  vector.unproject(camera);\n  return vector;\n};\nconst calculateScaleFactor = (point3, radiusPx, camera, size) => {\n  const point2 = getPoint2(tV2.copy(point3), camera, size);\n  let scale = 0;\n  for (let i = 0; i < 2; ++i) {\n    const point2off = tV1.copy(point2).setComponent(i, point2.getComponent(i) + radiusPx);\n    const point3off = getPoint3(point2off, camera, size, point2off.z);\n    scale = Math.max(scale, point3.distanceTo(point3off));\n  }\n  return scale;\n};\nconst mL0 = new THREE.Matrix4();\nconst mW0 = new THREE.Matrix4();\nconst mP = new THREE.Matrix4();\nconst mPInv = new THREE.Matrix4();\nconst mW = new THREE.Matrix4();\nconst mL = new THREE.Matrix4();\nconst mL0Inv = new THREE.Matrix4();\nconst mdL = new THREE.Matrix4();\nconst bb = new THREE.Box3();\nconst bbObj = new THREE.Box3();\nconst vCenter = new THREE.Vector3();\nconst vSize = new THREE.Vector3();\nconst vAnchorOffset = new THREE.Vector3();\nconst vPosition = new THREE.Vector3();\nconst xDir = new THREE.Vector3(1, 0, 0);\nconst yDir = new THREE.Vector3(0, 1, 0);\nconst zDir = new THREE.Vector3(0, 0, 1);\nconst PivotControls = /*#__PURE__*/React.forwardRef(({\n  matrix,\n  onDragStart,\n  onDrag,\n  onDragEnd,\n  autoTransform = true,\n  anchor,\n  disableAxes = false,\n  disableSliders = false,\n  disableRotations = false,\n  activeAxes = [true, true, true],\n  offset = [0, 0, 0],\n  rotation = [0, 0, 0],\n  scale = 1,\n  lineWidth = 4,\n  fixed = false,\n  translationLimits,\n  rotationLimits,\n  depthTest = true,\n  axisColors = ['#ff2060', '#20df80', '#2080ff'],\n  hoveredColor = '#ffff40',\n  annotations = false,\n  annotationsClass,\n  opacity = 1,\n  visible = true,\n  userData,\n  children,\n  ...props\n}, fRef) => {\n  const invalidate = useThree(state => state.invalidate);\n  const parentRef = React.useRef(null);\n  const ref = React.useRef(null);\n  const gizmoRef = React.useRef(null);\n  const childrenRef = React.useRef(null);\n  const translation = React.useRef([0, 0, 0]);\n  React.useLayoutEffect(() => {\n    if (!anchor) return;\n    childrenRef.current.updateWorldMatrix(true, true);\n    mPInv.copy(childrenRef.current.matrixWorld).invert();\n    bb.makeEmpty();\n    childrenRef.current.traverse(obj => {\n      if (!obj.geometry) return;\n      if (!obj.geometry.boundingBox) obj.geometry.computeBoundingBox();\n      mL.copy(obj.matrixWorld).premultiply(mPInv);\n      bbObj.copy(obj.geometry.boundingBox);\n      bbObj.applyMatrix4(mL);\n      bb.union(bbObj);\n    });\n    vCenter.copy(bb.max).add(bb.min).multiplyScalar(0.5);\n    vSize.copy(bb.max).sub(bb.min).multiplyScalar(0.5);\n    vAnchorOffset.copy(vSize).multiply(new THREE.Vector3(...anchor)).add(vCenter);\n    vPosition.set(...offset).add(vAnchorOffset);\n    gizmoRef.current.position.copy(vPosition);\n    invalidate();\n  });\n  const config = React.useMemo(() => ({\n    onDragStart: props => {\n      mL0.copy(ref.current.matrix);\n      mW0.copy(ref.current.matrixWorld);\n      onDragStart && onDragStart(props);\n      invalidate();\n    },\n    onDrag: mdW => {\n      mP.copy(parentRef.current.matrixWorld);\n      mPInv.copy(mP).invert(); // After applying the delta\n\n      mW.copy(mW0).premultiply(mdW);\n      mL.copy(mW).premultiply(mPInv);\n      mL0Inv.copy(mL0).invert();\n      mdL.copy(mL).multiply(mL0Inv);\n      if (autoTransform) ref.current.matrix.copy(mL);\n      onDrag && onDrag(mL, mdL, mW, mdW);\n      invalidate();\n    },\n    onDragEnd: () => {\n      if (onDragEnd) onDragEnd();\n      invalidate();\n    },\n    translation,\n    translationLimits,\n    rotationLimits,\n    axisColors,\n    hoveredColor,\n    opacity,\n    scale,\n    lineWidth,\n    fixed,\n    depthTest,\n    userData,\n    annotations,\n    annotationsClass\n  }), [onDragStart, onDrag, onDragEnd, translation, translationLimits, rotationLimits, depthTest, scale, lineWidth, fixed, ...axisColors, hoveredColor, opacity, userData, autoTransform, annotations, annotationsClass]);\n  const vec = new THREE.Vector3();\n  useFrame(state => {\n    if (fixed) {\n      const sf = calculateScaleFactor(gizmoRef.current.getWorldPosition(vec), scale, state.camera, state.size);\n      if (gizmoRef.current) {\n        var _gizmoRef$current, _gizmoRef$current2, _gizmoRef$current3;\n        if (((_gizmoRef$current = gizmoRef.current) == null ? void 0 : _gizmoRef$current.scale.x) !== sf || ((_gizmoRef$current2 = gizmoRef.current) == null ? void 0 : _gizmoRef$current2.scale.y) !== sf || ((_gizmoRef$current3 = gizmoRef.current) == null ? void 0 : _gizmoRef$current3.scale.z) !== sf) {\n          gizmoRef.current.scale.setScalar(sf);\n          state.invalidate();\n        }\n      }\n    }\n  });\n  React.useImperativeHandle(fRef, () => ref.current, []);\n  React.useLayoutEffect(() => {\n    // If the matrix is a real matrix4 it means that the user wants to control the gizmo\n    // In that case it should just be set, as a bare prop update would merely copy it\n    if (matrix && matrix instanceof THREE.Matrix4) ref.current.matrix = matrix;\n  }, [matrix]);\n  return /*#__PURE__*/React.createElement(context.Provider, {\n    value: config\n  }, /*#__PURE__*/React.createElement(\"group\", {\n    ref: parentRef\n  }, /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref,\n    matrix: matrix,\n    matrixAutoUpdate: false\n  }, props), /*#__PURE__*/React.createElement(\"group\", {\n    visible: visible,\n    ref: gizmoRef,\n    position: offset,\n    rotation: rotation\n  }, !disableAxes && activeAxes[0] && /*#__PURE__*/React.createElement(AxisArrow, {\n    axis: 0,\n    direction: xDir\n  }), !disableAxes && activeAxes[1] && /*#__PURE__*/React.createElement(AxisArrow, {\n    axis: 1,\n    direction: yDir\n  }), !disableAxes && activeAxes[2] && /*#__PURE__*/React.createElement(AxisArrow, {\n    axis: 2,\n    direction: zDir\n  }), !disableSliders && activeAxes[0] && activeAxes[1] && /*#__PURE__*/React.createElement(PlaneSlider, {\n    axis: 2,\n    dir1: xDir,\n    dir2: yDir\n  }), !disableSliders && activeAxes[0] && activeAxes[2] && /*#__PURE__*/React.createElement(PlaneSlider, {\n    axis: 1,\n    dir1: zDir,\n    dir2: xDir\n  }), !disableSliders && activeAxes[2] && activeAxes[1] && /*#__PURE__*/React.createElement(PlaneSlider, {\n    axis: 0,\n    dir1: yDir,\n    dir2: zDir\n  }), !disableRotations && activeAxes[0] && activeAxes[1] && /*#__PURE__*/React.createElement(AxisRotator, {\n    axis: 2,\n    dir1: xDir,\n    dir2: yDir\n  }), !disableRotations && activeAxes[0] && activeAxes[2] && /*#__PURE__*/React.createElement(AxisRotator, {\n    axis: 1,\n    dir1: zDir,\n    dir2: xDir\n  }), !disableRotations && activeAxes[2] && activeAxes[1] && /*#__PURE__*/React.createElement(AxisRotator, {\n    axis: 0,\n    dir1: yDir,\n    dir2: zDir\n  })), /*#__PURE__*/React.createElement(\"group\", {\n    ref: childrenRef\n  }, children))));\n});\nexport { PivotControls, calculateScaleFactor };", "map": {"version": 3, "names": ["_extends", "THREE", "React", "useThree", "useFrame", "AxisArrow", "PlaneSlider", "AxisRotator", "context", "tV0", "Vector3", "tV1", "tV2", "getPoint2", "point3", "camera", "size", "widthHalf", "width", "heightHalf", "height", "updateMatrixWorld", "vector", "project", "x", "y", "getPoint3", "point2", "zValue", "set", "unproject", "calculateScaleFactor", "radiusPx", "copy", "scale", "i", "point2off", "setComponent", "getComponent", "point3off", "z", "Math", "max", "distanceTo", "mL0", "Matrix4", "mW0", "mP", "mPInv", "mW", "mL", "mL0Inv", "mdL", "bb", "Box3", "bbObj", "vCenter", "vSize", "vAnchorOffset", "vPosition", "xDir", "yDir", "zDir", "PivotControls", "forwardRef", "matrix", "onDragStart", "onDrag", "onDragEnd", "autoTransform", "anchor", "disableAxes", "disableSliders", "disableRotations", "activeAxes", "offset", "rotation", "lineWidth", "fixed", "translationLimits", "rotationLimits", "depthTest", "axisColors", "hoveredColor", "annotations", "annotationsClass", "opacity", "visible", "userData", "children", "props", "fRef", "invalidate", "state", "parentRef", "useRef", "ref", "gizmoRef", "childrenRef", "translation", "useLayoutEffect", "current", "updateWorldMatrix", "matrixWorld", "invert", "makeEmpty", "traverse", "obj", "geometry", "boundingBox", "computeBoundingBox", "premultiply", "applyMatrix4", "union", "add", "min", "multiplyScalar", "sub", "multiply", "position", "config", "useMemo", "mdW", "vec", "sf", "getWorldPosition", "_gizmoRef$current", "_gizmoRef$current2", "_gizmoRef$current3", "setScalar", "useImperativeHandle", "createElement", "Provider", "value", "matrixAutoUpdate", "axis", "direction", "dir1", "dir2"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/web/pivotControls/index.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { AxisArrow } from './AxisArrow.js';\nimport { PlaneSlider } from './PlaneSlider.js';\nimport { AxisRotator } from './AxisRotator.js';\nimport { context } from './context.js';\n\nconst tV0 = new THREE.Vector3();\nconst tV1 = new THREE.Vector3();\nconst tV2 = new THREE.Vector3();\n\nconst getPoint2 = (point3, camera, size) => {\n  const widthHalf = size.width / 2;\n  const heightHalf = size.height / 2;\n  camera.updateMatrixWorld(false);\n  const vector = point3.project(camera);\n  vector.x = vector.x * widthHalf + widthHalf;\n  vector.y = -(vector.y * heightHalf) + heightHalf;\n  return vector;\n};\n\nconst getPoint3 = (point2, camera, size, zValue = 1) => {\n  const vector = tV0.set(point2.x / size.width * 2 - 1, -(point2.y / size.height) * 2 + 1, zValue);\n  vector.unproject(camera);\n  return vector;\n};\n\nconst calculateScaleFactor = (point3, radiusPx, camera, size) => {\n  const point2 = getPoint2(tV2.copy(point3), camera, size);\n  let scale = 0;\n\n  for (let i = 0; i < 2; ++i) {\n    const point2off = tV1.copy(point2).setComponent(i, point2.getComponent(i) + radiusPx);\n    const point3off = getPoint3(point2off, camera, size, point2off.z);\n    scale = Math.max(scale, point3.distanceTo(point3off));\n  }\n\n  return scale;\n};\nconst mL0 = new THREE.Matrix4();\nconst mW0 = new THREE.Matrix4();\nconst mP = new THREE.Matrix4();\nconst mPInv = new THREE.Matrix4();\nconst mW = new THREE.Matrix4();\nconst mL = new THREE.Matrix4();\nconst mL0Inv = new THREE.Matrix4();\nconst mdL = new THREE.Matrix4();\nconst bb = new THREE.Box3();\nconst bbObj = new THREE.Box3();\nconst vCenter = new THREE.Vector3();\nconst vSize = new THREE.Vector3();\nconst vAnchorOffset = new THREE.Vector3();\nconst vPosition = new THREE.Vector3();\nconst xDir = new THREE.Vector3(1, 0, 0);\nconst yDir = new THREE.Vector3(0, 1, 0);\nconst zDir = new THREE.Vector3(0, 0, 1);\nconst PivotControls = /*#__PURE__*/React.forwardRef(({\n  matrix,\n  onDragStart,\n  onDrag,\n  onDragEnd,\n  autoTransform = true,\n  anchor,\n  disableAxes = false,\n  disableSliders = false,\n  disableRotations = false,\n  activeAxes = [true, true, true],\n  offset = [0, 0, 0],\n  rotation = [0, 0, 0],\n  scale = 1,\n  lineWidth = 4,\n  fixed = false,\n  translationLimits,\n  rotationLimits,\n  depthTest = true,\n  axisColors = ['#ff2060', '#20df80', '#2080ff'],\n  hoveredColor = '#ffff40',\n  annotations = false,\n  annotationsClass,\n  opacity = 1,\n  visible = true,\n  userData,\n  children,\n  ...props\n}, fRef) => {\n  const invalidate = useThree(state => state.invalidate);\n  const parentRef = React.useRef(null);\n  const ref = React.useRef(null);\n  const gizmoRef = React.useRef(null);\n  const childrenRef = React.useRef(null);\n  const translation = React.useRef([0, 0, 0]);\n  React.useLayoutEffect(() => {\n    if (!anchor) return;\n    childrenRef.current.updateWorldMatrix(true, true);\n    mPInv.copy(childrenRef.current.matrixWorld).invert();\n    bb.makeEmpty();\n    childrenRef.current.traverse(obj => {\n      if (!obj.geometry) return;\n      if (!obj.geometry.boundingBox) obj.geometry.computeBoundingBox();\n      mL.copy(obj.matrixWorld).premultiply(mPInv);\n      bbObj.copy(obj.geometry.boundingBox);\n      bbObj.applyMatrix4(mL);\n      bb.union(bbObj);\n    });\n    vCenter.copy(bb.max).add(bb.min).multiplyScalar(0.5);\n    vSize.copy(bb.max).sub(bb.min).multiplyScalar(0.5);\n    vAnchorOffset.copy(vSize).multiply(new THREE.Vector3(...anchor)).add(vCenter);\n    vPosition.set(...offset).add(vAnchorOffset);\n    gizmoRef.current.position.copy(vPosition);\n    invalidate();\n  });\n  const config = React.useMemo(() => ({\n    onDragStart: props => {\n      mL0.copy(ref.current.matrix);\n      mW0.copy(ref.current.matrixWorld);\n      onDragStart && onDragStart(props);\n      invalidate();\n    },\n    onDrag: mdW => {\n      mP.copy(parentRef.current.matrixWorld);\n      mPInv.copy(mP).invert(); // After applying the delta\n\n      mW.copy(mW0).premultiply(mdW);\n      mL.copy(mW).premultiply(mPInv);\n      mL0Inv.copy(mL0).invert();\n      mdL.copy(mL).multiply(mL0Inv);\n      if (autoTransform) ref.current.matrix.copy(mL);\n      onDrag && onDrag(mL, mdL, mW, mdW);\n      invalidate();\n    },\n    onDragEnd: () => {\n      if (onDragEnd) onDragEnd();\n      invalidate();\n    },\n    translation,\n    translationLimits,\n    rotationLimits,\n    axisColors,\n    hoveredColor,\n    opacity,\n    scale,\n    lineWidth,\n    fixed,\n    depthTest,\n    userData,\n    annotations,\n    annotationsClass\n  }), [onDragStart, onDrag, onDragEnd, translation, translationLimits, rotationLimits, depthTest, scale, lineWidth, fixed, ...axisColors, hoveredColor, opacity, userData, autoTransform, annotations, annotationsClass]);\n  const vec = new THREE.Vector3();\n  useFrame(state => {\n    if (fixed) {\n      const sf = calculateScaleFactor(gizmoRef.current.getWorldPosition(vec), scale, state.camera, state.size);\n\n      if (gizmoRef.current) {\n        var _gizmoRef$current, _gizmoRef$current2, _gizmoRef$current3;\n\n        if (((_gizmoRef$current = gizmoRef.current) == null ? void 0 : _gizmoRef$current.scale.x) !== sf || ((_gizmoRef$current2 = gizmoRef.current) == null ? void 0 : _gizmoRef$current2.scale.y) !== sf || ((_gizmoRef$current3 = gizmoRef.current) == null ? void 0 : _gizmoRef$current3.scale.z) !== sf) {\n          gizmoRef.current.scale.setScalar(sf);\n          state.invalidate();\n        }\n      }\n    }\n  });\n  React.useImperativeHandle(fRef, () => ref.current, []);\n  React.useLayoutEffect(() => {\n    // If the matrix is a real matrix4 it means that the user wants to control the gizmo\n    // In that case it should just be set, as a bare prop update would merely copy it\n    if (matrix && matrix instanceof THREE.Matrix4) ref.current.matrix = matrix;\n  }, [matrix]);\n  return /*#__PURE__*/React.createElement(context.Provider, {\n    value: config\n  }, /*#__PURE__*/React.createElement(\"group\", {\n    ref: parentRef\n  }, /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref,\n    matrix: matrix,\n    matrixAutoUpdate: false\n  }, props), /*#__PURE__*/React.createElement(\"group\", {\n    visible: visible,\n    ref: gizmoRef,\n    position: offset,\n    rotation: rotation\n  }, !disableAxes && activeAxes[0] && /*#__PURE__*/React.createElement(AxisArrow, {\n    axis: 0,\n    direction: xDir\n  }), !disableAxes && activeAxes[1] && /*#__PURE__*/React.createElement(AxisArrow, {\n    axis: 1,\n    direction: yDir\n  }), !disableAxes && activeAxes[2] && /*#__PURE__*/React.createElement(AxisArrow, {\n    axis: 2,\n    direction: zDir\n  }), !disableSliders && activeAxes[0] && activeAxes[1] && /*#__PURE__*/React.createElement(PlaneSlider, {\n    axis: 2,\n    dir1: xDir,\n    dir2: yDir\n  }), !disableSliders && activeAxes[0] && activeAxes[2] && /*#__PURE__*/React.createElement(PlaneSlider, {\n    axis: 1,\n    dir1: zDir,\n    dir2: xDir\n  }), !disableSliders && activeAxes[2] && activeAxes[1] && /*#__PURE__*/React.createElement(PlaneSlider, {\n    axis: 0,\n    dir1: yDir,\n    dir2: zDir\n  }), !disableRotations && activeAxes[0] && activeAxes[1] && /*#__PURE__*/React.createElement(AxisRotator, {\n    axis: 2,\n    dir1: xDir,\n    dir2: yDir\n  }), !disableRotations && activeAxes[0] && activeAxes[2] && /*#__PURE__*/React.createElement(AxisRotator, {\n    axis: 1,\n    dir1: zDir,\n    dir2: xDir\n  }), !disableRotations && activeAxes[2] && activeAxes[1] && /*#__PURE__*/React.createElement(AxisRotator, {\n    axis: 0,\n    dir1: yDir,\n    dir2: zDir\n  })), /*#__PURE__*/React.createElement(\"group\", {\n    ref: childrenRef\n  }, children))));\n});\n\nexport { PivotControls, calculateScaleFactor };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,oBAAoB;AACvD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,cAAc;AAEtC,MAAMC,GAAG,GAAG,IAAIR,KAAK,CAACS,OAAO,CAAC,CAAC;AAC/B,MAAMC,GAAG,GAAG,IAAIV,KAAK,CAACS,OAAO,CAAC,CAAC;AAC/B,MAAME,GAAG,GAAG,IAAIX,KAAK,CAACS,OAAO,CAAC,CAAC;AAE/B,MAAMG,SAAS,GAAGA,CAACC,MAAM,EAAEC,MAAM,EAAEC,IAAI,KAAK;EAC1C,MAAMC,SAAS,GAAGD,IAAI,CAACE,KAAK,GAAG,CAAC;EAChC,MAAMC,UAAU,GAAGH,IAAI,CAACI,MAAM,GAAG,CAAC;EAClCL,MAAM,CAACM,iBAAiB,CAAC,KAAK,CAAC;EAC/B,MAAMC,MAAM,GAAGR,MAAM,CAACS,OAAO,CAACR,MAAM,CAAC;EACrCO,MAAM,CAACE,CAAC,GAAGF,MAAM,CAACE,CAAC,GAAGP,SAAS,GAAGA,SAAS;EAC3CK,MAAM,CAACG,CAAC,GAAG,EAAEH,MAAM,CAACG,CAAC,GAAGN,UAAU,CAAC,GAAGA,UAAU;EAChD,OAAOG,MAAM;AACf,CAAC;AAED,MAAMI,SAAS,GAAGA,CAACC,MAAM,EAAEZ,MAAM,EAAEC,IAAI,EAAEY,MAAM,GAAG,CAAC,KAAK;EACtD,MAAMN,MAAM,GAAGb,GAAG,CAACoB,GAAG,CAACF,MAAM,CAACH,CAAC,GAAGR,IAAI,CAACE,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,EAAES,MAAM,CAACF,CAAC,GAAGT,IAAI,CAACI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEQ,MAAM,CAAC;EAChGN,MAAM,CAACQ,SAAS,CAACf,MAAM,CAAC;EACxB,OAAOO,MAAM;AACf,CAAC;AAED,MAAMS,oBAAoB,GAAGA,CAACjB,MAAM,EAAEkB,QAAQ,EAAEjB,MAAM,EAAEC,IAAI,KAAK;EAC/D,MAAMW,MAAM,GAAGd,SAAS,CAACD,GAAG,CAACqB,IAAI,CAACnB,MAAM,CAAC,EAAEC,MAAM,EAAEC,IAAI,CAAC;EACxD,IAAIkB,KAAK,GAAG,CAAC;EAEb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;IAC1B,MAAMC,SAAS,GAAGzB,GAAG,CAACsB,IAAI,CAACN,MAAM,CAAC,CAACU,YAAY,CAACF,CAAC,EAAER,MAAM,CAACW,YAAY,CAACH,CAAC,CAAC,GAAGH,QAAQ,CAAC;IACrF,MAAMO,SAAS,GAAGb,SAAS,CAACU,SAAS,EAAErB,MAAM,EAAEC,IAAI,EAAEoB,SAAS,CAACI,CAAC,CAAC;IACjEN,KAAK,GAAGO,IAAI,CAACC,GAAG,CAACR,KAAK,EAAEpB,MAAM,CAAC6B,UAAU,CAACJ,SAAS,CAAC,CAAC;EACvD;EAEA,OAAOL,KAAK;AACd,CAAC;AACD,MAAMU,GAAG,GAAG,IAAI3C,KAAK,CAAC4C,OAAO,CAAC,CAAC;AAC/B,MAAMC,GAAG,GAAG,IAAI7C,KAAK,CAAC4C,OAAO,CAAC,CAAC;AAC/B,MAAME,EAAE,GAAG,IAAI9C,KAAK,CAAC4C,OAAO,CAAC,CAAC;AAC9B,MAAMG,KAAK,GAAG,IAAI/C,KAAK,CAAC4C,OAAO,CAAC,CAAC;AACjC,MAAMI,EAAE,GAAG,IAAIhD,KAAK,CAAC4C,OAAO,CAAC,CAAC;AAC9B,MAAMK,EAAE,GAAG,IAAIjD,KAAK,CAAC4C,OAAO,CAAC,CAAC;AAC9B,MAAMM,MAAM,GAAG,IAAIlD,KAAK,CAAC4C,OAAO,CAAC,CAAC;AAClC,MAAMO,GAAG,GAAG,IAAInD,KAAK,CAAC4C,OAAO,CAAC,CAAC;AAC/B,MAAMQ,EAAE,GAAG,IAAIpD,KAAK,CAACqD,IAAI,CAAC,CAAC;AAC3B,MAAMC,KAAK,GAAG,IAAItD,KAAK,CAACqD,IAAI,CAAC,CAAC;AAC9B,MAAME,OAAO,GAAG,IAAIvD,KAAK,CAACS,OAAO,CAAC,CAAC;AACnC,MAAM+C,KAAK,GAAG,IAAIxD,KAAK,CAACS,OAAO,CAAC,CAAC;AACjC,MAAMgD,aAAa,GAAG,IAAIzD,KAAK,CAACS,OAAO,CAAC,CAAC;AACzC,MAAMiD,SAAS,GAAG,IAAI1D,KAAK,CAACS,OAAO,CAAC,CAAC;AACrC,MAAMkD,IAAI,GAAG,IAAI3D,KAAK,CAACS,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACvC,MAAMmD,IAAI,GAAG,IAAI5D,KAAK,CAACS,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACvC,MAAMoD,IAAI,GAAG,IAAI7D,KAAK,CAACS,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACvC,MAAMqD,aAAa,GAAG,aAAa7D,KAAK,CAAC8D,UAAU,CAAC,CAAC;EACnDC,MAAM;EACNC,WAAW;EACXC,MAAM;EACNC,SAAS;EACTC,aAAa,GAAG,IAAI;EACpBC,MAAM;EACNC,WAAW,GAAG,KAAK;EACnBC,cAAc,GAAG,KAAK;EACtBC,gBAAgB,GAAG,KAAK;EACxBC,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC/BC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAClBC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACpB1C,KAAK,GAAG,CAAC;EACT2C,SAAS,GAAG,CAAC;EACbC,KAAK,GAAG,KAAK;EACbC,iBAAiB;EACjBC,cAAc;EACdC,SAAS,GAAG,IAAI;EAChBC,UAAU,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAC9CC,YAAY,GAAG,SAAS;EACxBC,WAAW,GAAG,KAAK;EACnBC,gBAAgB;EAChBC,OAAO,GAAG,CAAC;EACXC,OAAO,GAAG,IAAI;EACdC,QAAQ;EACRC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,IAAI,KAAK;EACV,MAAMC,UAAU,GAAGzF,QAAQ,CAAC0F,KAAK,IAAIA,KAAK,CAACD,UAAU,CAAC;EACtD,MAAME,SAAS,GAAG5F,KAAK,CAAC6F,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMC,GAAG,GAAG9F,KAAK,CAAC6F,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAME,QAAQ,GAAG/F,KAAK,CAAC6F,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMG,WAAW,GAAGhG,KAAK,CAAC6F,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMI,WAAW,GAAGjG,KAAK,CAAC6F,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3C7F,KAAK,CAACkG,eAAe,CAAC,MAAM;IAC1B,IAAI,CAAC9B,MAAM,EAAE;IACb4B,WAAW,CAACG,OAAO,CAACC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC;IACjDtD,KAAK,CAACf,IAAI,CAACiE,WAAW,CAACG,OAAO,CAACE,WAAW,CAAC,CAACC,MAAM,CAAC,CAAC;IACpDnD,EAAE,CAACoD,SAAS,CAAC,CAAC;IACdP,WAAW,CAACG,OAAO,CAACK,QAAQ,CAACC,GAAG,IAAI;MAClC,IAAI,CAACA,GAAG,CAACC,QAAQ,EAAE;MACnB,IAAI,CAACD,GAAG,CAACC,QAAQ,CAACC,WAAW,EAAEF,GAAG,CAACC,QAAQ,CAACE,kBAAkB,CAAC,CAAC;MAChE5D,EAAE,CAACjB,IAAI,CAAC0E,GAAG,CAACJ,WAAW,CAAC,CAACQ,WAAW,CAAC/D,KAAK,CAAC;MAC3CO,KAAK,CAACtB,IAAI,CAAC0E,GAAG,CAACC,QAAQ,CAACC,WAAW,CAAC;MACpCtD,KAAK,CAACyD,YAAY,CAAC9D,EAAE,CAAC;MACtBG,EAAE,CAAC4D,KAAK,CAAC1D,KAAK,CAAC;IACjB,CAAC,CAAC;IACFC,OAAO,CAACvB,IAAI,CAACoB,EAAE,CAACX,GAAG,CAAC,CAACwE,GAAG,CAAC7D,EAAE,CAAC8D,GAAG,CAAC,CAACC,cAAc,CAAC,GAAG,CAAC;IACpD3D,KAAK,CAACxB,IAAI,CAACoB,EAAE,CAACX,GAAG,CAAC,CAAC2E,GAAG,CAAChE,EAAE,CAAC8D,GAAG,CAAC,CAACC,cAAc,CAAC,GAAG,CAAC;IAClD1D,aAAa,CAACzB,IAAI,CAACwB,KAAK,CAAC,CAAC6D,QAAQ,CAAC,IAAIrH,KAAK,CAACS,OAAO,CAAC,GAAG4D,MAAM,CAAC,CAAC,CAAC4C,GAAG,CAAC1D,OAAO,CAAC;IAC7EG,SAAS,CAAC9B,GAAG,CAAC,GAAG8C,MAAM,CAAC,CAACuC,GAAG,CAACxD,aAAa,CAAC;IAC3CuC,QAAQ,CAACI,OAAO,CAACkB,QAAQ,CAACtF,IAAI,CAAC0B,SAAS,CAAC;IACzCiC,UAAU,CAAC,CAAC;EACd,CAAC,CAAC;EACF,MAAM4B,MAAM,GAAGtH,KAAK,CAACuH,OAAO,CAAC,OAAO;IAClCvD,WAAW,EAAEwB,KAAK,IAAI;MACpB9C,GAAG,CAACX,IAAI,CAAC+D,GAAG,CAACK,OAAO,CAACpC,MAAM,CAAC;MAC5BnB,GAAG,CAACb,IAAI,CAAC+D,GAAG,CAACK,OAAO,CAACE,WAAW,CAAC;MACjCrC,WAAW,IAAIA,WAAW,CAACwB,KAAK,CAAC;MACjCE,UAAU,CAAC,CAAC;IACd,CAAC;IACDzB,MAAM,EAAEuD,GAAG,IAAI;MACb3E,EAAE,CAACd,IAAI,CAAC6D,SAAS,CAACO,OAAO,CAACE,WAAW,CAAC;MACtCvD,KAAK,CAACf,IAAI,CAACc,EAAE,CAAC,CAACyD,MAAM,CAAC,CAAC,CAAC,CAAC;;MAEzBvD,EAAE,CAAChB,IAAI,CAACa,GAAG,CAAC,CAACiE,WAAW,CAACW,GAAG,CAAC;MAC7BxE,EAAE,CAACjB,IAAI,CAACgB,EAAE,CAAC,CAAC8D,WAAW,CAAC/D,KAAK,CAAC;MAC9BG,MAAM,CAAClB,IAAI,CAACW,GAAG,CAAC,CAAC4D,MAAM,CAAC,CAAC;MACzBpD,GAAG,CAACnB,IAAI,CAACiB,EAAE,CAAC,CAACoE,QAAQ,CAACnE,MAAM,CAAC;MAC7B,IAAIkB,aAAa,EAAE2B,GAAG,CAACK,OAAO,CAACpC,MAAM,CAAChC,IAAI,CAACiB,EAAE,CAAC;MAC9CiB,MAAM,IAAIA,MAAM,CAACjB,EAAE,EAAEE,GAAG,EAAEH,EAAE,EAAEyE,GAAG,CAAC;MAClC9B,UAAU,CAAC,CAAC;IACd,CAAC;IACDxB,SAAS,EAAEA,CAAA,KAAM;MACf,IAAIA,SAAS,EAAEA,SAAS,CAAC,CAAC;MAC1BwB,UAAU,CAAC,CAAC;IACd,CAAC;IACDO,WAAW;IACXpB,iBAAiB;IACjBC,cAAc;IACdE,UAAU;IACVC,YAAY;IACZG,OAAO;IACPpD,KAAK;IACL2C,SAAS;IACTC,KAAK;IACLG,SAAS;IACTO,QAAQ;IACRJ,WAAW;IACXC;EACF,CAAC,CAAC,EAAE,CAACnB,WAAW,EAAEC,MAAM,EAAEC,SAAS,EAAE+B,WAAW,EAAEpB,iBAAiB,EAAEC,cAAc,EAAEC,SAAS,EAAE/C,KAAK,EAAE2C,SAAS,EAAEC,KAAK,EAAE,GAAGI,UAAU,EAAEC,YAAY,EAAEG,OAAO,EAAEE,QAAQ,EAAEnB,aAAa,EAAEe,WAAW,EAAEC,gBAAgB,CAAC,CAAC;EACvN,MAAMsC,GAAG,GAAG,IAAI1H,KAAK,CAACS,OAAO,CAAC,CAAC;EAC/BN,QAAQ,CAACyF,KAAK,IAAI;IAChB,IAAIf,KAAK,EAAE;MACT,MAAM8C,EAAE,GAAG7F,oBAAoB,CAACkE,QAAQ,CAACI,OAAO,CAACwB,gBAAgB,CAACF,GAAG,CAAC,EAAEzF,KAAK,EAAE2D,KAAK,CAAC9E,MAAM,EAAE8E,KAAK,CAAC7E,IAAI,CAAC;MAExG,IAAIiF,QAAQ,CAACI,OAAO,EAAE;QACpB,IAAIyB,iBAAiB,EAAEC,kBAAkB,EAAEC,kBAAkB;QAE7D,IAAI,CAAC,CAACF,iBAAiB,GAAG7B,QAAQ,CAACI,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyB,iBAAiB,CAAC5F,KAAK,CAACV,CAAC,MAAMoG,EAAE,IAAI,CAAC,CAACG,kBAAkB,GAAG9B,QAAQ,CAACI,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG0B,kBAAkB,CAAC7F,KAAK,CAACT,CAAC,MAAMmG,EAAE,IAAI,CAAC,CAACI,kBAAkB,GAAG/B,QAAQ,CAACI,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2B,kBAAkB,CAAC9F,KAAK,CAACM,CAAC,MAAMoF,EAAE,EAAE;UACpS3B,QAAQ,CAACI,OAAO,CAACnE,KAAK,CAAC+F,SAAS,CAACL,EAAE,CAAC;UACpC/B,KAAK,CAACD,UAAU,CAAC,CAAC;QACpB;MACF;IACF;EACF,CAAC,CAAC;EACF1F,KAAK,CAACgI,mBAAmB,CAACvC,IAAI,EAAE,MAAMK,GAAG,CAACK,OAAO,EAAE,EAAE,CAAC;EACtDnG,KAAK,CAACkG,eAAe,CAAC,MAAM;IAC1B;IACA;IACA,IAAInC,MAAM,IAAIA,MAAM,YAAYhE,KAAK,CAAC4C,OAAO,EAAEmD,GAAG,CAACK,OAAO,CAACpC,MAAM,GAAGA,MAAM;EAC5E,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,OAAO,aAAa/D,KAAK,CAACiI,aAAa,CAAC3H,OAAO,CAAC4H,QAAQ,EAAE;IACxDC,KAAK,EAAEb;EACT,CAAC,EAAE,aAAatH,KAAK,CAACiI,aAAa,CAAC,OAAO,EAAE;IAC3CnC,GAAG,EAAEF;EACP,CAAC,EAAE,aAAa5F,KAAK,CAACiI,aAAa,CAAC,OAAO,EAAEnI,QAAQ,CAAC;IACpDgG,GAAG,EAAEA,GAAG;IACR/B,MAAM,EAAEA,MAAM;IACdqE,gBAAgB,EAAE;EACpB,CAAC,EAAE5C,KAAK,CAAC,EAAE,aAAaxF,KAAK,CAACiI,aAAa,CAAC,OAAO,EAAE;IACnD5C,OAAO,EAAEA,OAAO;IAChBS,GAAG,EAAEC,QAAQ;IACbsB,QAAQ,EAAE5C,MAAM;IAChBC,QAAQ,EAAEA;EACZ,CAAC,EAAE,CAACL,WAAW,IAAIG,UAAU,CAAC,CAAC,CAAC,IAAI,aAAaxE,KAAK,CAACiI,aAAa,CAAC9H,SAAS,EAAE;IAC9EkI,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE5E;EACb,CAAC,CAAC,EAAE,CAACW,WAAW,IAAIG,UAAU,CAAC,CAAC,CAAC,IAAI,aAAaxE,KAAK,CAACiI,aAAa,CAAC9H,SAAS,EAAE;IAC/EkI,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE3E;EACb,CAAC,CAAC,EAAE,CAACU,WAAW,IAAIG,UAAU,CAAC,CAAC,CAAC,IAAI,aAAaxE,KAAK,CAACiI,aAAa,CAAC9H,SAAS,EAAE;IAC/EkI,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE1E;EACb,CAAC,CAAC,EAAE,CAACU,cAAc,IAAIE,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,aAAaxE,KAAK,CAACiI,aAAa,CAAC7H,WAAW,EAAE;IACrGiI,IAAI,EAAE,CAAC;IACPE,IAAI,EAAE7E,IAAI;IACV8E,IAAI,EAAE7E;EACR,CAAC,CAAC,EAAE,CAACW,cAAc,IAAIE,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,aAAaxE,KAAK,CAACiI,aAAa,CAAC7H,WAAW,EAAE;IACrGiI,IAAI,EAAE,CAAC;IACPE,IAAI,EAAE3E,IAAI;IACV4E,IAAI,EAAE9E;EACR,CAAC,CAAC,EAAE,CAACY,cAAc,IAAIE,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,aAAaxE,KAAK,CAACiI,aAAa,CAAC7H,WAAW,EAAE;IACrGiI,IAAI,EAAE,CAAC;IACPE,IAAI,EAAE5E,IAAI;IACV6E,IAAI,EAAE5E;EACR,CAAC,CAAC,EAAE,CAACW,gBAAgB,IAAIC,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,aAAaxE,KAAK,CAACiI,aAAa,CAAC5H,WAAW,EAAE;IACvGgI,IAAI,EAAE,CAAC;IACPE,IAAI,EAAE7E,IAAI;IACV8E,IAAI,EAAE7E;EACR,CAAC,CAAC,EAAE,CAACY,gBAAgB,IAAIC,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,aAAaxE,KAAK,CAACiI,aAAa,CAAC5H,WAAW,EAAE;IACvGgI,IAAI,EAAE,CAAC;IACPE,IAAI,EAAE3E,IAAI;IACV4E,IAAI,EAAE9E;EACR,CAAC,CAAC,EAAE,CAACa,gBAAgB,IAAIC,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,aAAaxE,KAAK,CAACiI,aAAa,CAAC5H,WAAW,EAAE;IACvGgI,IAAI,EAAE,CAAC;IACPE,IAAI,EAAE5E,IAAI;IACV6E,IAAI,EAAE5E;EACR,CAAC,CAAC,CAAC,EAAE,aAAa5D,KAAK,CAACiI,aAAa,CAAC,OAAO,EAAE;IAC7CnC,GAAG,EAAEE;EACP,CAAC,EAAET,QAAQ,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC,CAAC;AAEF,SAAS1B,aAAa,EAAEhC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}