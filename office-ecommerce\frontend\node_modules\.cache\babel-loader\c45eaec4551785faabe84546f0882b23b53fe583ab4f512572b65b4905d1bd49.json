{"ast": null, "code": "import { defineHidden, is, createInterpolator, eachProp, hasFluidValue, getFluidValue, each, isAnimatedString, useForceUpdate, useIsomorphicLayoutEffect, addFluidObserver, removeFluidObserver, raf, useOnce } from '@react-spring/shared';\nimport * as React from 'react';\nimport { forwardRef, useRef, useCallback, useEffect } from 'react';\nconst $node = Symbol.for('Animated:node');\nconst isAnimated = value => !!value && value[$node] === value;\nconst getAnimated = owner => owner && owner[$node];\nconst setAnimated = (owner, node) => defineHidden(owner, $node, node);\nconst getPayload = owner => owner && owner[$node] && owner[$node].getPayload();\nclass Animated {\n  constructor() {\n    this.payload = void 0;\n    setAnimated(this, this);\n  }\n  getPayload() {\n    return this.payload || [];\n  }\n}\nclass AnimatedValue extends Animated {\n  constructor(_value) {\n    super();\n    this.done = true;\n    this.elapsedTime = void 0;\n    this.lastPosition = void 0;\n    this.lastVelocity = void 0;\n    this.v0 = void 0;\n    this.durationProgress = 0;\n    this._value = _value;\n    if (is.num(this._value)) {\n      this.lastPosition = this._value;\n    }\n  }\n  static create(value) {\n    return new AnimatedValue(value);\n  }\n  getPayload() {\n    return [this];\n  }\n  getValue() {\n    return this._value;\n  }\n  setValue(value, step) {\n    if (is.num(value)) {\n      this.lastPosition = value;\n      if (step) {\n        value = Math.round(value / step) * step;\n        if (this.done) {\n          this.lastPosition = value;\n        }\n      }\n    }\n    if (this._value === value) {\n      return false;\n    }\n    this._value = value;\n    return true;\n  }\n  reset() {\n    const {\n      done\n    } = this;\n    this.done = false;\n    if (is.num(this._value)) {\n      this.elapsedTime = 0;\n      this.durationProgress = 0;\n      this.lastPosition = this._value;\n      if (done) this.lastVelocity = null;\n      this.v0 = null;\n    }\n  }\n}\nclass AnimatedString extends AnimatedValue {\n  constructor(value) {\n    super(0);\n    this._string = null;\n    this._toString = void 0;\n    this._toString = createInterpolator({\n      output: [value, value]\n    });\n  }\n  static create(value) {\n    return new AnimatedString(value);\n  }\n  getValue() {\n    let value = this._string;\n    return value == null ? this._string = this._toString(this._value) : value;\n  }\n  setValue(value) {\n    if (is.str(value)) {\n      if (value == this._string) {\n        return false;\n      }\n      this._string = value;\n      this._value = 1;\n    } else if (super.setValue(value)) {\n      this._string = null;\n    } else {\n      return false;\n    }\n    return true;\n  }\n  reset(goal) {\n    if (goal) {\n      this._toString = createInterpolator({\n        output: [this.getValue(), goal]\n      });\n    }\n    this._value = 0;\n    super.reset();\n  }\n}\nconst TreeContext = {\n  dependencies: null\n};\nclass AnimatedObject extends Animated {\n  constructor(source) {\n    super();\n    this.source = source;\n    this.setValue(source);\n  }\n  getValue(animated) {\n    const values = {};\n    eachProp(this.source, (source, key) => {\n      if (isAnimated(source)) {\n        values[key] = source.getValue(animated);\n      } else if (hasFluidValue(source)) {\n        values[key] = getFluidValue(source);\n      } else if (!animated) {\n        values[key] = source;\n      }\n    });\n    return values;\n  }\n  setValue(source) {\n    this.source = source;\n    this.payload = this._makePayload(source);\n  }\n  reset() {\n    if (this.payload) {\n      each(this.payload, node => node.reset());\n    }\n  }\n  _makePayload(source) {\n    if (source) {\n      const payload = new Set();\n      eachProp(source, this._addToPayload, payload);\n      return Array.from(payload);\n    }\n  }\n  _addToPayload(source) {\n    if (TreeContext.dependencies && hasFluidValue(source)) {\n      TreeContext.dependencies.add(source);\n    }\n    const payload = getPayload(source);\n    if (payload) {\n      each(payload, node => this.add(node));\n    }\n  }\n}\nclass AnimatedArray extends AnimatedObject {\n  constructor(source) {\n    super(source);\n  }\n  static create(source) {\n    return new AnimatedArray(source);\n  }\n  getValue() {\n    return this.source.map(node => node.getValue());\n  }\n  setValue(source) {\n    const payload = this.getPayload();\n    if (source.length == payload.length) {\n      return payload.map((node, i) => node.setValue(source[i])).some(Boolean);\n    }\n    super.setValue(source.map(makeAnimated));\n    return true;\n  }\n}\nfunction makeAnimated(value) {\n  const nodeType = isAnimatedString(value) ? AnimatedString : AnimatedValue;\n  return nodeType.create(value);\n}\nfunction getAnimatedType(value) {\n  const parentNode = getAnimated(value);\n  return parentNode ? parentNode.constructor : is.arr(value) ? AnimatedArray : isAnimatedString(value) ? AnimatedString : AnimatedValue;\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nconst withAnimated = (Component, host) => {\n  const hasInstance = !is.fun(Component) || Component.prototype && Component.prototype.isReactComponent;\n  return forwardRef((givenProps, givenRef) => {\n    const instanceRef = useRef(null);\n    const ref = hasInstance && useCallback(value => {\n      instanceRef.current = updateRef(givenRef, value);\n    }, [givenRef]);\n    const [props, deps] = getAnimatedState(givenProps, host);\n    const forceUpdate = useForceUpdate();\n    const callback = () => {\n      const instance = instanceRef.current;\n      if (hasInstance && !instance) {\n        return;\n      }\n      const didUpdate = instance ? host.applyAnimatedValues(instance, props.getValue(true)) : false;\n      if (didUpdate === false) {\n        forceUpdate();\n      }\n    };\n    const observer = new PropsObserver(callback, deps);\n    const observerRef = useRef();\n    useIsomorphicLayoutEffect(() => {\n      observerRef.current = observer;\n      each(deps, dep => addFluidObserver(dep, observer));\n      return () => {\n        if (observerRef.current) {\n          each(observerRef.current.deps, dep => removeFluidObserver(dep, observerRef.current));\n          raf.cancel(observerRef.current.update);\n        }\n      };\n    });\n    useEffect(callback, []);\n    useOnce(() => () => {\n      const observer = observerRef.current;\n      each(observer.deps, dep => removeFluidObserver(dep, observer));\n    });\n    const usedProps = host.getComponentProps(props.getValue());\n    return React.createElement(Component, _extends({}, usedProps, {\n      ref: ref\n    }));\n  });\n};\nclass PropsObserver {\n  constructor(update, deps) {\n    this.update = update;\n    this.deps = deps;\n  }\n  eventObserved(event) {\n    if (event.type == 'change') {\n      raf.write(this.update);\n    }\n  }\n}\nfunction getAnimatedState(props, host) {\n  const dependencies = new Set();\n  TreeContext.dependencies = dependencies;\n  if (props.style) props = _extends({}, props, {\n    style: host.createAnimatedStyle(props.style)\n  });\n  props = new AnimatedObject(props);\n  TreeContext.dependencies = null;\n  return [props, dependencies];\n}\nfunction updateRef(ref, value) {\n  if (ref) {\n    if (is.fun(ref)) ref(value);else ref.current = value;\n  }\n  return value;\n}\nconst cacheKey = Symbol.for('AnimatedComponent');\nconst createHost = (components, {\n  applyAnimatedValues: _applyAnimatedValues = () => false,\n  createAnimatedStyle: _createAnimatedStyle = style => new AnimatedObject(style),\n  getComponentProps: _getComponentProps = props => props\n} = {}) => {\n  const hostConfig = {\n    applyAnimatedValues: _applyAnimatedValues,\n    createAnimatedStyle: _createAnimatedStyle,\n    getComponentProps: _getComponentProps\n  };\n  const animated = Component => {\n    const displayName = getDisplayName(Component) || 'Anonymous';\n    if (is.str(Component)) {\n      Component = animated[Component] || (animated[Component] = withAnimated(Component, hostConfig));\n    } else {\n      Component = Component[cacheKey] || (Component[cacheKey] = withAnimated(Component, hostConfig));\n    }\n    Component.displayName = `Animated(${displayName})`;\n    return Component;\n  };\n  eachProp(components, (Component, key) => {\n    if (is.arr(components)) {\n      key = getDisplayName(Component);\n    }\n    animated[key] = animated(Component);\n  });\n  return {\n    animated\n  };\n};\nconst getDisplayName = arg => is.str(arg) ? arg : arg && is.str(arg.displayName) ? arg.displayName : is.fun(arg) && arg.name || null;\nexport { Animated, AnimatedArray, AnimatedObject, AnimatedString, AnimatedValue, createHost, getAnimated, getAnimatedType, getPayload, isAnimated, setAnimated };", "map": {"version": 3, "names": ["defineHidden", "is", "createInterpolator", "eachProp", "hasFluidValue", "getFluidValue", "each", "isAnimatedString", "useForceUpdate", "useIsomorphicLayoutEffect", "addFluidObserver", "removeFluidObserver", "raf", "useOnce", "React", "forwardRef", "useRef", "useCallback", "useEffect", "$node", "Symbol", "for", "isAnimated", "value", "getAnimated", "owner", "setAnimated", "node", "getPayload", "Animated", "constructor", "payload", "AnimatedValue", "_value", "done", "elapsedTime", "lastPosition", "lastVelocity", "v0", "durationProgress", "num", "create", "getValue", "setValue", "step", "Math", "round", "reset", "AnimatedString", "_string", "_toString", "output", "str", "goal", "TreeContext", "dependencies", "AnimatedObject", "source", "animated", "values", "key", "_makePayload", "Set", "_addToPayload", "Array", "from", "add", "AnimatedArray", "map", "length", "i", "some", "Boolean", "makeAnimated", "nodeType", "getAnimatedType", "parentNode", "arr", "_extends", "Object", "assign", "bind", "target", "arguments", "prototype", "hasOwnProperty", "call", "apply", "withAnimated", "Component", "host", "hasInstance", "fun", "isReactComponent", "givenProps", "givenRef", "instanceRef", "ref", "current", "updateRef", "props", "deps", "getAnimatedState", "forceUpdate", "callback", "instance", "didUpdate", "applyAnimatedValues", "observer", "PropsObserver", "observerRef", "dep", "cancel", "update", "usedProps", "getComponentProps", "createElement", "eventObserved", "event", "type", "write", "style", "createAnimatedStyle", "cache<PERSON>ey", "createHost", "components", "_applyAnimatedValues", "_createAnimatedStyle", "_getComponentProps", "hostConfig", "displayName", "getDisplayName", "arg", "name"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-spring/animated/dist/react-spring-animated.esm.js"], "sourcesContent": ["import { defineHidden, is, createInterpolator, eachProp, hasFluidValue, getFluidValue, each, isAnimatedString, useForceUpdate, useIsomorphicLayoutEffect, addFluidObserver, removeFluidObserver, raf, useOnce } from '@react-spring/shared';\nimport * as React from 'react';\nimport { forwardRef, useRef, useCallback, useEffect } from 'react';\n\nconst $node = Symbol.for('Animated:node');\nconst isAnimated = value => !!value && value[$node] === value;\nconst getAnimated = owner => owner && owner[$node];\nconst setAnimated = (owner, node) => defineHidden(owner, $node, node);\nconst getPayload = owner => owner && owner[$node] && owner[$node].getPayload();\nclass Animated {\n  constructor() {\n    this.payload = void 0;\n    setAnimated(this, this);\n  }\n\n  getPayload() {\n    return this.payload || [];\n  }\n\n}\n\nclass AnimatedValue extends Animated {\n  constructor(_value) {\n    super();\n    this.done = true;\n    this.elapsedTime = void 0;\n    this.lastPosition = void 0;\n    this.lastVelocity = void 0;\n    this.v0 = void 0;\n    this.durationProgress = 0;\n    this._value = _value;\n\n    if (is.num(this._value)) {\n      this.lastPosition = this._value;\n    }\n  }\n\n  static create(value) {\n    return new AnimatedValue(value);\n  }\n\n  getPayload() {\n    return [this];\n  }\n\n  getValue() {\n    return this._value;\n  }\n\n  setValue(value, step) {\n    if (is.num(value)) {\n      this.lastPosition = value;\n\n      if (step) {\n        value = Math.round(value / step) * step;\n\n        if (this.done) {\n          this.lastPosition = value;\n        }\n      }\n    }\n\n    if (this._value === value) {\n      return false;\n    }\n\n    this._value = value;\n    return true;\n  }\n\n  reset() {\n    const {\n      done\n    } = this;\n    this.done = false;\n\n    if (is.num(this._value)) {\n      this.elapsedTime = 0;\n      this.durationProgress = 0;\n      this.lastPosition = this._value;\n      if (done) this.lastVelocity = null;\n      this.v0 = null;\n    }\n  }\n\n}\n\nclass AnimatedString extends AnimatedValue {\n  constructor(value) {\n    super(0);\n    this._string = null;\n    this._toString = void 0;\n    this._toString = createInterpolator({\n      output: [value, value]\n    });\n  }\n\n  static create(value) {\n    return new AnimatedString(value);\n  }\n\n  getValue() {\n    let value = this._string;\n    return value == null ? this._string = this._toString(this._value) : value;\n  }\n\n  setValue(value) {\n    if (is.str(value)) {\n      if (value == this._string) {\n        return false;\n      }\n\n      this._string = value;\n      this._value = 1;\n    } else if (super.setValue(value)) {\n      this._string = null;\n    } else {\n      return false;\n    }\n\n    return true;\n  }\n\n  reset(goal) {\n    if (goal) {\n      this._toString = createInterpolator({\n        output: [this.getValue(), goal]\n      });\n    }\n\n    this._value = 0;\n    super.reset();\n  }\n\n}\n\nconst TreeContext = {\n  dependencies: null\n};\n\nclass AnimatedObject extends Animated {\n  constructor(source) {\n    super();\n    this.source = source;\n    this.setValue(source);\n  }\n\n  getValue(animated) {\n    const values = {};\n    eachProp(this.source, (source, key) => {\n      if (isAnimated(source)) {\n        values[key] = source.getValue(animated);\n      } else if (hasFluidValue(source)) {\n        values[key] = getFluidValue(source);\n      } else if (!animated) {\n        values[key] = source;\n      }\n    });\n    return values;\n  }\n\n  setValue(source) {\n    this.source = source;\n    this.payload = this._makePayload(source);\n  }\n\n  reset() {\n    if (this.payload) {\n      each(this.payload, node => node.reset());\n    }\n  }\n\n  _makePayload(source) {\n    if (source) {\n      const payload = new Set();\n      eachProp(source, this._addToPayload, payload);\n      return Array.from(payload);\n    }\n  }\n\n  _addToPayload(source) {\n    if (TreeContext.dependencies && hasFluidValue(source)) {\n      TreeContext.dependencies.add(source);\n    }\n\n    const payload = getPayload(source);\n\n    if (payload) {\n      each(payload, node => this.add(node));\n    }\n  }\n\n}\n\nclass AnimatedArray extends AnimatedObject {\n  constructor(source) {\n    super(source);\n  }\n\n  static create(source) {\n    return new AnimatedArray(source);\n  }\n\n  getValue() {\n    return this.source.map(node => node.getValue());\n  }\n\n  setValue(source) {\n    const payload = this.getPayload();\n\n    if (source.length == payload.length) {\n      return payload.map((node, i) => node.setValue(source[i])).some(Boolean);\n    }\n\n    super.setValue(source.map(makeAnimated));\n    return true;\n  }\n\n}\n\nfunction makeAnimated(value) {\n  const nodeType = isAnimatedString(value) ? AnimatedString : AnimatedValue;\n  return nodeType.create(value);\n}\n\nfunction getAnimatedType(value) {\n  const parentNode = getAnimated(value);\n  return parentNode ? parentNode.constructor : is.arr(value) ? AnimatedArray : isAnimatedString(value) ? AnimatedString : AnimatedValue;\n}\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nconst withAnimated = (Component, host) => {\n  const hasInstance = !is.fun(Component) || Component.prototype && Component.prototype.isReactComponent;\n  return forwardRef((givenProps, givenRef) => {\n    const instanceRef = useRef(null);\n    const ref = hasInstance && useCallback(value => {\n      instanceRef.current = updateRef(givenRef, value);\n    }, [givenRef]);\n    const [props, deps] = getAnimatedState(givenProps, host);\n    const forceUpdate = useForceUpdate();\n\n    const callback = () => {\n      const instance = instanceRef.current;\n\n      if (hasInstance && !instance) {\n        return;\n      }\n\n      const didUpdate = instance ? host.applyAnimatedValues(instance, props.getValue(true)) : false;\n\n      if (didUpdate === false) {\n        forceUpdate();\n      }\n    };\n\n    const observer = new PropsObserver(callback, deps);\n    const observerRef = useRef();\n    useIsomorphicLayoutEffect(() => {\n      observerRef.current = observer;\n      each(deps, dep => addFluidObserver(dep, observer));\n      return () => {\n        if (observerRef.current) {\n          each(observerRef.current.deps, dep => removeFluidObserver(dep, observerRef.current));\n          raf.cancel(observerRef.current.update);\n        }\n      };\n    });\n    useEffect(callback, []);\n    useOnce(() => () => {\n      const observer = observerRef.current;\n      each(observer.deps, dep => removeFluidObserver(dep, observer));\n    });\n    const usedProps = host.getComponentProps(props.getValue());\n    return React.createElement(Component, _extends({}, usedProps, {\n      ref: ref\n    }));\n  });\n};\n\nclass PropsObserver {\n  constructor(update, deps) {\n    this.update = update;\n    this.deps = deps;\n  }\n\n  eventObserved(event) {\n    if (event.type == 'change') {\n      raf.write(this.update);\n    }\n  }\n\n}\n\nfunction getAnimatedState(props, host) {\n  const dependencies = new Set();\n  TreeContext.dependencies = dependencies;\n  if (props.style) props = _extends({}, props, {\n    style: host.createAnimatedStyle(props.style)\n  });\n  props = new AnimatedObject(props);\n  TreeContext.dependencies = null;\n  return [props, dependencies];\n}\n\nfunction updateRef(ref, value) {\n  if (ref) {\n    if (is.fun(ref)) ref(value);else ref.current = value;\n  }\n\n  return value;\n}\n\nconst cacheKey = Symbol.for('AnimatedComponent');\nconst createHost = (components, {\n  applyAnimatedValues: _applyAnimatedValues = () => false,\n  createAnimatedStyle: _createAnimatedStyle = style => new AnimatedObject(style),\n  getComponentProps: _getComponentProps = props => props\n} = {}) => {\n  const hostConfig = {\n    applyAnimatedValues: _applyAnimatedValues,\n    createAnimatedStyle: _createAnimatedStyle,\n    getComponentProps: _getComponentProps\n  };\n\n  const animated = Component => {\n    const displayName = getDisplayName(Component) || 'Anonymous';\n\n    if (is.str(Component)) {\n      Component = animated[Component] || (animated[Component] = withAnimated(Component, hostConfig));\n    } else {\n      Component = Component[cacheKey] || (Component[cacheKey] = withAnimated(Component, hostConfig));\n    }\n\n    Component.displayName = `Animated(${displayName})`;\n    return Component;\n  };\n\n  eachProp(components, (Component, key) => {\n    if (is.arr(components)) {\n      key = getDisplayName(Component);\n    }\n\n    animated[key] = animated(Component);\n  });\n  return {\n    animated\n  };\n};\n\nconst getDisplayName = arg => is.str(arg) ? arg : arg && is.str(arg.displayName) ? arg.displayName : is.fun(arg) && arg.name || null;\n\nexport { Animated, AnimatedArray, AnimatedObject, AnimatedString, AnimatedValue, createHost, getAnimated, getAnimatedType, getPayload, isAnimated, setAnimated };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,EAAE,EAAEC,kBAAkB,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,aAAa,EAAEC,IAAI,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,yBAAyB,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,GAAG,EAAEC,OAAO,QAAQ,sBAAsB;AAC3O,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAElE,MAAMC,KAAK,GAAGC,MAAM,CAACC,GAAG,CAAC,eAAe,CAAC;AACzC,MAAMC,UAAU,GAAGC,KAAK,IAAI,CAAC,CAACA,KAAK,IAAIA,KAAK,CAACJ,KAAK,CAAC,KAAKI,KAAK;AAC7D,MAAMC,WAAW,GAAGC,KAAK,IAAIA,KAAK,IAAIA,KAAK,CAACN,KAAK,CAAC;AAClD,MAAMO,WAAW,GAAGA,CAACD,KAAK,EAAEE,IAAI,KAAK3B,YAAY,CAACyB,KAAK,EAAEN,KAAK,EAAEQ,IAAI,CAAC;AACrE,MAAMC,UAAU,GAAGH,KAAK,IAAIA,KAAK,IAAIA,KAAK,CAACN,KAAK,CAAC,IAAIM,KAAK,CAACN,KAAK,CAAC,CAACS,UAAU,CAAC,CAAC;AAC9E,MAAMC,QAAQ,CAAC;EACbC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAG,KAAK,CAAC;IACrBL,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC;EACzB;EAEAE,UAAUA,CAAA,EAAG;IACX,OAAO,IAAI,CAACG,OAAO,IAAI,EAAE;EAC3B;AAEF;AAEA,MAAMC,aAAa,SAASH,QAAQ,CAAC;EACnCC,WAAWA,CAACG,MAAM,EAAE;IAClB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,WAAW,GAAG,KAAK,CAAC;IACzB,IAAI,CAACC,YAAY,GAAG,KAAK,CAAC;IAC1B,IAAI,CAACC,YAAY,GAAG,KAAK,CAAC;IAC1B,IAAI,CAACC,EAAE,GAAG,KAAK,CAAC;IAChB,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACN,MAAM,GAAGA,MAAM;IAEpB,IAAIhC,EAAE,CAACuC,GAAG,CAAC,IAAI,CAACP,MAAM,CAAC,EAAE;MACvB,IAAI,CAACG,YAAY,GAAG,IAAI,CAACH,MAAM;IACjC;EACF;EAEA,OAAOQ,MAAMA,CAAClB,KAAK,EAAE;IACnB,OAAO,IAAIS,aAAa,CAACT,KAAK,CAAC;EACjC;EAEAK,UAAUA,CAAA,EAAG;IACX,OAAO,CAAC,IAAI,CAAC;EACf;EAEAc,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACT,MAAM;EACpB;EAEAU,QAAQA,CAACpB,KAAK,EAAEqB,IAAI,EAAE;IACpB,IAAI3C,EAAE,CAACuC,GAAG,CAACjB,KAAK,CAAC,EAAE;MACjB,IAAI,CAACa,YAAY,GAAGb,KAAK;MAEzB,IAAIqB,IAAI,EAAE;QACRrB,KAAK,GAAGsB,IAAI,CAACC,KAAK,CAACvB,KAAK,GAAGqB,IAAI,CAAC,GAAGA,IAAI;QAEvC,IAAI,IAAI,CAACV,IAAI,EAAE;UACb,IAAI,CAACE,YAAY,GAAGb,KAAK;QAC3B;MACF;IACF;IAEA,IAAI,IAAI,CAACU,MAAM,KAAKV,KAAK,EAAE;MACzB,OAAO,KAAK;IACd;IAEA,IAAI,CAACU,MAAM,GAAGV,KAAK;IACnB,OAAO,IAAI;EACb;EAEAwB,KAAKA,CAAA,EAAG;IACN,MAAM;MACJb;IACF,CAAC,GAAG,IAAI;IACR,IAAI,CAACA,IAAI,GAAG,KAAK;IAEjB,IAAIjC,EAAE,CAACuC,GAAG,CAAC,IAAI,CAACP,MAAM,CAAC,EAAE;MACvB,IAAI,CAACE,WAAW,GAAG,CAAC;MACpB,IAAI,CAACI,gBAAgB,GAAG,CAAC;MACzB,IAAI,CAACH,YAAY,GAAG,IAAI,CAACH,MAAM;MAC/B,IAAIC,IAAI,EAAE,IAAI,CAACG,YAAY,GAAG,IAAI;MAClC,IAAI,CAACC,EAAE,GAAG,IAAI;IAChB;EACF;AAEF;AAEA,MAAMU,cAAc,SAAShB,aAAa,CAAC;EACzCF,WAAWA,CAACP,KAAK,EAAE;IACjB,KAAK,CAAC,CAAC,CAAC;IACR,IAAI,CAAC0B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,KAAK,CAAC;IACvB,IAAI,CAACA,SAAS,GAAGhD,kBAAkB,CAAC;MAClCiD,MAAM,EAAE,CAAC5B,KAAK,EAAEA,KAAK;IACvB,CAAC,CAAC;EACJ;EAEA,OAAOkB,MAAMA,CAAClB,KAAK,EAAE;IACnB,OAAO,IAAIyB,cAAc,CAACzB,KAAK,CAAC;EAClC;EAEAmB,QAAQA,CAAA,EAAG;IACT,IAAInB,KAAK,GAAG,IAAI,CAAC0B,OAAO;IACxB,OAAO1B,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC0B,OAAO,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACjB,MAAM,CAAC,GAAGV,KAAK;EAC3E;EAEAoB,QAAQA,CAACpB,KAAK,EAAE;IACd,IAAItB,EAAE,CAACmD,GAAG,CAAC7B,KAAK,CAAC,EAAE;MACjB,IAAIA,KAAK,IAAI,IAAI,CAAC0B,OAAO,EAAE;QACzB,OAAO,KAAK;MACd;MAEA,IAAI,CAACA,OAAO,GAAG1B,KAAK;MACpB,IAAI,CAACU,MAAM,GAAG,CAAC;IACjB,CAAC,MAAM,IAAI,KAAK,CAACU,QAAQ,CAACpB,KAAK,CAAC,EAAE;MAChC,IAAI,CAAC0B,OAAO,GAAG,IAAI;IACrB,CAAC,MAAM;MACL,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb;EAEAF,KAAKA,CAACM,IAAI,EAAE;IACV,IAAIA,IAAI,EAAE;MACR,IAAI,CAACH,SAAS,GAAGhD,kBAAkB,CAAC;QAClCiD,MAAM,EAAE,CAAC,IAAI,CAACT,QAAQ,CAAC,CAAC,EAAEW,IAAI;MAChC,CAAC,CAAC;IACJ;IAEA,IAAI,CAACpB,MAAM,GAAG,CAAC;IACf,KAAK,CAACc,KAAK,CAAC,CAAC;EACf;AAEF;AAEA,MAAMO,WAAW,GAAG;EAClBC,YAAY,EAAE;AAChB,CAAC;AAED,MAAMC,cAAc,SAAS3B,QAAQ,CAAC;EACpCC,WAAWA,CAAC2B,MAAM,EAAE;IAClB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACd,QAAQ,CAACc,MAAM,CAAC;EACvB;EAEAf,QAAQA,CAACgB,QAAQ,EAAE;IACjB,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjBxD,QAAQ,CAAC,IAAI,CAACsD,MAAM,EAAE,CAACA,MAAM,EAAEG,GAAG,KAAK;MACrC,IAAItC,UAAU,CAACmC,MAAM,CAAC,EAAE;QACtBE,MAAM,CAACC,GAAG,CAAC,GAAGH,MAAM,CAACf,QAAQ,CAACgB,QAAQ,CAAC;MACzC,CAAC,MAAM,IAAItD,aAAa,CAACqD,MAAM,CAAC,EAAE;QAChCE,MAAM,CAACC,GAAG,CAAC,GAAGvD,aAAa,CAACoD,MAAM,CAAC;MACrC,CAAC,MAAM,IAAI,CAACC,QAAQ,EAAE;QACpBC,MAAM,CAACC,GAAG,CAAC,GAAGH,MAAM;MACtB;IACF,CAAC,CAAC;IACF,OAAOE,MAAM;EACf;EAEAhB,QAAQA,CAACc,MAAM,EAAE;IACf,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC1B,OAAO,GAAG,IAAI,CAAC8B,YAAY,CAACJ,MAAM,CAAC;EAC1C;EAEAV,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAAChB,OAAO,EAAE;MAChBzB,IAAI,CAAC,IAAI,CAACyB,OAAO,EAAEJ,IAAI,IAAIA,IAAI,CAACoB,KAAK,CAAC,CAAC,CAAC;IAC1C;EACF;EAEAc,YAAYA,CAACJ,MAAM,EAAE;IACnB,IAAIA,MAAM,EAAE;MACV,MAAM1B,OAAO,GAAG,IAAI+B,GAAG,CAAC,CAAC;MACzB3D,QAAQ,CAACsD,MAAM,EAAE,IAAI,CAACM,aAAa,EAAEhC,OAAO,CAAC;MAC7C,OAAOiC,KAAK,CAACC,IAAI,CAAClC,OAAO,CAAC;IAC5B;EACF;EAEAgC,aAAaA,CAACN,MAAM,EAAE;IACpB,IAAIH,WAAW,CAACC,YAAY,IAAInD,aAAa,CAACqD,MAAM,CAAC,EAAE;MACrDH,WAAW,CAACC,YAAY,CAACW,GAAG,CAACT,MAAM,CAAC;IACtC;IAEA,MAAM1B,OAAO,GAAGH,UAAU,CAAC6B,MAAM,CAAC;IAElC,IAAI1B,OAAO,EAAE;MACXzB,IAAI,CAACyB,OAAO,EAAEJ,IAAI,IAAI,IAAI,CAACuC,GAAG,CAACvC,IAAI,CAAC,CAAC;IACvC;EACF;AAEF;AAEA,MAAMwC,aAAa,SAASX,cAAc,CAAC;EACzC1B,WAAWA,CAAC2B,MAAM,EAAE;IAClB,KAAK,CAACA,MAAM,CAAC;EACf;EAEA,OAAOhB,MAAMA,CAACgB,MAAM,EAAE;IACpB,OAAO,IAAIU,aAAa,CAACV,MAAM,CAAC;EAClC;EAEAf,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACe,MAAM,CAACW,GAAG,CAACzC,IAAI,IAAIA,IAAI,CAACe,QAAQ,CAAC,CAAC,CAAC;EACjD;EAEAC,QAAQA,CAACc,MAAM,EAAE;IACf,MAAM1B,OAAO,GAAG,IAAI,CAACH,UAAU,CAAC,CAAC;IAEjC,IAAI6B,MAAM,CAACY,MAAM,IAAItC,OAAO,CAACsC,MAAM,EAAE;MACnC,OAAOtC,OAAO,CAACqC,GAAG,CAAC,CAACzC,IAAI,EAAE2C,CAAC,KAAK3C,IAAI,CAACgB,QAAQ,CAACc,MAAM,CAACa,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAACC,OAAO,CAAC;IACzE;IAEA,KAAK,CAAC7B,QAAQ,CAACc,MAAM,CAACW,GAAG,CAACK,YAAY,CAAC,CAAC;IACxC,OAAO,IAAI;EACb;AAEF;AAEA,SAASA,YAAYA,CAAClD,KAAK,EAAE;EAC3B,MAAMmD,QAAQ,GAAGnE,gBAAgB,CAACgB,KAAK,CAAC,GAAGyB,cAAc,GAAGhB,aAAa;EACzE,OAAO0C,QAAQ,CAACjC,MAAM,CAAClB,KAAK,CAAC;AAC/B;AAEA,SAASoD,eAAeA,CAACpD,KAAK,EAAE;EAC9B,MAAMqD,UAAU,GAAGpD,WAAW,CAACD,KAAK,CAAC;EACrC,OAAOqD,UAAU,GAAGA,UAAU,CAAC9C,WAAW,GAAG7B,EAAE,CAAC4E,GAAG,CAACtD,KAAK,CAAC,GAAG4C,aAAa,GAAG5D,gBAAgB,CAACgB,KAAK,CAAC,GAAGyB,cAAc,GAAGhB,aAAa;AACvI;AAEA,SAAS8C,QAAQA,CAAA,EAAG;EAClBA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAClE,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,SAAS,CAACd,MAAM,EAAEC,CAAC,EAAE,EAAE;MACzC,IAAIb,MAAM,GAAG0B,SAAS,CAACb,CAAC,CAAC;MAEzB,KAAK,IAAIV,GAAG,IAAIH,MAAM,EAAE;QACtB,IAAIsB,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC7B,MAAM,EAAEG,GAAG,CAAC,EAAE;UACrDsB,MAAM,CAACtB,GAAG,CAAC,GAAGH,MAAM,CAACG,GAAG,CAAC;QAC3B;MACF;IACF;IAEA,OAAOsB,MAAM;EACf,CAAC;EACD,OAAOJ,QAAQ,CAACS,KAAK,CAAC,IAAI,EAAEJ,SAAS,CAAC;AACxC;AAEA,MAAMK,YAAY,GAAGA,CAACC,SAAS,EAAEC,IAAI,KAAK;EACxC,MAAMC,WAAW,GAAG,CAAC1F,EAAE,CAAC2F,GAAG,CAACH,SAAS,CAAC,IAAIA,SAAS,CAACL,SAAS,IAAIK,SAAS,CAACL,SAAS,CAACS,gBAAgB;EACrG,OAAO9E,UAAU,CAAC,CAAC+E,UAAU,EAAEC,QAAQ,KAAK;IAC1C,MAAMC,WAAW,GAAGhF,MAAM,CAAC,IAAI,CAAC;IAChC,MAAMiF,GAAG,GAAGN,WAAW,IAAI1E,WAAW,CAACM,KAAK,IAAI;MAC9CyE,WAAW,CAACE,OAAO,GAAGC,SAAS,CAACJ,QAAQ,EAAExE,KAAK,CAAC;IAClD,CAAC,EAAE,CAACwE,QAAQ,CAAC,CAAC;IACd,MAAM,CAACK,KAAK,EAAEC,IAAI,CAAC,GAAGC,gBAAgB,CAACR,UAAU,EAAEJ,IAAI,CAAC;IACxD,MAAMa,WAAW,GAAG/F,cAAc,CAAC,CAAC;IAEpC,MAAMgG,QAAQ,GAAGA,CAAA,KAAM;MACrB,MAAMC,QAAQ,GAAGT,WAAW,CAACE,OAAO;MAEpC,IAAIP,WAAW,IAAI,CAACc,QAAQ,EAAE;QAC5B;MACF;MAEA,MAAMC,SAAS,GAAGD,QAAQ,GAAGf,IAAI,CAACiB,mBAAmB,CAACF,QAAQ,EAAEL,KAAK,CAAC1D,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK;MAE7F,IAAIgE,SAAS,KAAK,KAAK,EAAE;QACvBH,WAAW,CAAC,CAAC;MACf;IACF,CAAC;IAED,MAAMK,QAAQ,GAAG,IAAIC,aAAa,CAACL,QAAQ,EAAEH,IAAI,CAAC;IAClD,MAAMS,WAAW,GAAG9F,MAAM,CAAC,CAAC;IAC5BP,yBAAyB,CAAC,MAAM;MAC9BqG,WAAW,CAACZ,OAAO,GAAGU,QAAQ;MAC9BtG,IAAI,CAAC+F,IAAI,EAAEU,GAAG,IAAIrG,gBAAgB,CAACqG,GAAG,EAAEH,QAAQ,CAAC,CAAC;MAClD,OAAO,MAAM;QACX,IAAIE,WAAW,CAACZ,OAAO,EAAE;UACvB5F,IAAI,CAACwG,WAAW,CAACZ,OAAO,CAACG,IAAI,EAAEU,GAAG,IAAIpG,mBAAmB,CAACoG,GAAG,EAAED,WAAW,CAACZ,OAAO,CAAC,CAAC;UACpFtF,GAAG,CAACoG,MAAM,CAACF,WAAW,CAACZ,OAAO,CAACe,MAAM,CAAC;QACxC;MACF,CAAC;IACH,CAAC,CAAC;IACF/F,SAAS,CAACsF,QAAQ,EAAE,EAAE,CAAC;IACvB3F,OAAO,CAAC,MAAM,MAAM;MAClB,MAAM+F,QAAQ,GAAGE,WAAW,CAACZ,OAAO;MACpC5F,IAAI,CAACsG,QAAQ,CAACP,IAAI,EAAEU,GAAG,IAAIpG,mBAAmB,CAACoG,GAAG,EAAEH,QAAQ,CAAC,CAAC;IAChE,CAAC,CAAC;IACF,MAAMM,SAAS,GAAGxB,IAAI,CAACyB,iBAAiB,CAACf,KAAK,CAAC1D,QAAQ,CAAC,CAAC,CAAC;IAC1D,OAAO5B,KAAK,CAACsG,aAAa,CAAC3B,SAAS,EAAEX,QAAQ,CAAC,CAAC,CAAC,EAAEoC,SAAS,EAAE;MAC5DjB,GAAG,EAAEA;IACP,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC;AAED,MAAMY,aAAa,CAAC;EAClB/E,WAAWA,CAACmF,MAAM,EAAEZ,IAAI,EAAE;IACxB,IAAI,CAACY,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACZ,IAAI,GAAGA,IAAI;EAClB;EAEAgB,aAAaA,CAACC,KAAK,EAAE;IACnB,IAAIA,KAAK,CAACC,IAAI,IAAI,QAAQ,EAAE;MAC1B3G,GAAG,CAAC4G,KAAK,CAAC,IAAI,CAACP,MAAM,CAAC;IACxB;EACF;AAEF;AAEA,SAASX,gBAAgBA,CAACF,KAAK,EAAEV,IAAI,EAAE;EACrC,MAAMnC,YAAY,GAAG,IAAIO,GAAG,CAAC,CAAC;EAC9BR,WAAW,CAACC,YAAY,GAAGA,YAAY;EACvC,IAAI6C,KAAK,CAACqB,KAAK,EAAErB,KAAK,GAAGtB,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;IAC3CqB,KAAK,EAAE/B,IAAI,CAACgC,mBAAmB,CAACtB,KAAK,CAACqB,KAAK;EAC7C,CAAC,CAAC;EACFrB,KAAK,GAAG,IAAI5C,cAAc,CAAC4C,KAAK,CAAC;EACjC9C,WAAW,CAACC,YAAY,GAAG,IAAI;EAC/B,OAAO,CAAC6C,KAAK,EAAE7C,YAAY,CAAC;AAC9B;AAEA,SAAS4C,SAASA,CAACF,GAAG,EAAE1E,KAAK,EAAE;EAC7B,IAAI0E,GAAG,EAAE;IACP,IAAIhG,EAAE,CAAC2F,GAAG,CAACK,GAAG,CAAC,EAAEA,GAAG,CAAC1E,KAAK,CAAC,CAAC,KAAK0E,GAAG,CAACC,OAAO,GAAG3E,KAAK;EACtD;EAEA,OAAOA,KAAK;AACd;AAEA,MAAMoG,QAAQ,GAAGvG,MAAM,CAACC,GAAG,CAAC,mBAAmB,CAAC;AAChD,MAAMuG,UAAU,GAAGA,CAACC,UAAU,EAAE;EAC9BlB,mBAAmB,EAAEmB,oBAAoB,GAAGA,CAAA,KAAM,KAAK;EACvDJ,mBAAmB,EAAEK,oBAAoB,GAAGN,KAAK,IAAI,IAAIjE,cAAc,CAACiE,KAAK,CAAC;EAC9EN,iBAAiB,EAAEa,kBAAkB,GAAG5B,KAAK,IAAIA;AACnD,CAAC,GAAG,CAAC,CAAC,KAAK;EACT,MAAM6B,UAAU,GAAG;IACjBtB,mBAAmB,EAAEmB,oBAAoB;IACzCJ,mBAAmB,EAAEK,oBAAoB;IACzCZ,iBAAiB,EAAEa;EACrB,CAAC;EAED,MAAMtE,QAAQ,GAAG+B,SAAS,IAAI;IAC5B,MAAMyC,WAAW,GAAGC,cAAc,CAAC1C,SAAS,CAAC,IAAI,WAAW;IAE5D,IAAIxF,EAAE,CAACmD,GAAG,CAACqC,SAAS,CAAC,EAAE;MACrBA,SAAS,GAAG/B,QAAQ,CAAC+B,SAAS,CAAC,KAAK/B,QAAQ,CAAC+B,SAAS,CAAC,GAAGD,YAAY,CAACC,SAAS,EAAEwC,UAAU,CAAC,CAAC;IAChG,CAAC,MAAM;MACLxC,SAAS,GAAGA,SAAS,CAACkC,QAAQ,CAAC,KAAKlC,SAAS,CAACkC,QAAQ,CAAC,GAAGnC,YAAY,CAACC,SAAS,EAAEwC,UAAU,CAAC,CAAC;IAChG;IAEAxC,SAAS,CAACyC,WAAW,GAAG,YAAYA,WAAW,GAAG;IAClD,OAAOzC,SAAS;EAClB,CAAC;EAEDtF,QAAQ,CAAC0H,UAAU,EAAE,CAACpC,SAAS,EAAE7B,GAAG,KAAK;IACvC,IAAI3D,EAAE,CAAC4E,GAAG,CAACgD,UAAU,CAAC,EAAE;MACtBjE,GAAG,GAAGuE,cAAc,CAAC1C,SAAS,CAAC;IACjC;IAEA/B,QAAQ,CAACE,GAAG,CAAC,GAAGF,QAAQ,CAAC+B,SAAS,CAAC;EACrC,CAAC,CAAC;EACF,OAAO;IACL/B;EACF,CAAC;AACH,CAAC;AAED,MAAMyE,cAAc,GAAGC,GAAG,IAAInI,EAAE,CAACmD,GAAG,CAACgF,GAAG,CAAC,GAAGA,GAAG,GAAGA,GAAG,IAAInI,EAAE,CAACmD,GAAG,CAACgF,GAAG,CAACF,WAAW,CAAC,GAAGE,GAAG,CAACF,WAAW,GAAGjI,EAAE,CAAC2F,GAAG,CAACwC,GAAG,CAAC,IAAIA,GAAG,CAACC,IAAI,IAAI,IAAI;AAEpI,SAASxG,QAAQ,EAAEsC,aAAa,EAAEX,cAAc,EAAER,cAAc,EAAEhB,aAAa,EAAE4F,UAAU,EAAEpG,WAAW,EAAEmD,eAAe,EAAE/C,UAAU,EAAEN,UAAU,EAAEI,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}