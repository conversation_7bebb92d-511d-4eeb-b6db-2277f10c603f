{"ast": null, "code": "import * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\nimport * as THREE from 'three';\nconst Float = /*#__PURE__*/React.forwardRef(({\n  children,\n  enabled = true,\n  speed = 1,\n  rotationIntensity = 1,\n  floatIntensity = 1,\n  floatingRange = [-0.1, 0.1],\n  ...props\n}, forwardRef) => {\n  const ref = React.useRef(null);\n  const offset = React.useRef(Math.random() * 10000);\n  useFrame(state => {\n    var _floatingRange$, _floatingRange$2;\n    if (!enabled || speed === 0) return;\n    const t = offset.current + state.clock.getElapsedTime();\n    ref.current.rotation.x = Math.cos(t / 4 * speed) / 8 * rotationIntensity;\n    ref.current.rotation.y = Math.sin(t / 4 * speed) / 8 * rotationIntensity;\n    ref.current.rotation.z = Math.sin(t / 4 * speed) / 20 * rotationIntensity;\n    let yPosition = Math.sin(t / 4 * speed) / 10;\n    yPosition = THREE.MathUtils.mapLinear(yPosition, -0.1, 0.1, (_floatingRange$ = floatingRange == null ? void 0 : floatingRange[0]) !== null && _floatingRange$ !== void 0 ? _floatingRange$ : -0.1, (_floatingRange$2 = floatingRange == null ? void 0 : floatingRange[1]) !== null && _floatingRange$2 !== void 0 ? _floatingRange$2 : 0.1);\n    ref.current.position.y = yPosition * floatIntensity;\n    ref.current.updateMatrix();\n  });\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"group\", {\n    ref: mergeRefs([ref, forwardRef]),\n    matrixAutoUpdate: false\n  }, children));\n});\nexport { Float };", "map": {"version": 3, "names": ["React", "useFrame", "mergeRefs", "THREE", "Float", "forwardRef", "children", "enabled", "speed", "rotationIntensity", "floatIntensity", "floatingRange", "props", "ref", "useRef", "offset", "Math", "random", "state", "_floatingRange$", "_floatingRange$2", "t", "current", "clock", "getElapsedTime", "rotation", "x", "cos", "y", "sin", "z", "yPosition", "MathUtils", "mapLinear", "position", "updateMatrix", "createElement", "matrixAutoUpdate"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/node_modules/@react-three/drei/core/Float.js"], "sourcesContent": ["import * as React from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport mergeRefs from 'react-merge-refs';\nimport * as THREE from 'three';\n\nconst Float = /*#__PURE__*/React.forwardRef(({\n  children,\n  enabled = true,\n  speed = 1,\n  rotationIntensity = 1,\n  floatIntensity = 1,\n  floatingRange = [-0.1, 0.1],\n  ...props\n}, forwardRef) => {\n  const ref = React.useRef(null);\n  const offset = React.useRef(Math.random() * 10000);\n  useFrame(state => {\n    var _floatingRange$, _floatingRange$2;\n\n    if (!enabled || speed === 0) return;\n    const t = offset.current + state.clock.getElapsedTime();\n    ref.current.rotation.x = Math.cos(t / 4 * speed) / 8 * rotationIntensity;\n    ref.current.rotation.y = Math.sin(t / 4 * speed) / 8 * rotationIntensity;\n    ref.current.rotation.z = Math.sin(t / 4 * speed) / 20 * rotationIntensity;\n    let yPosition = Math.sin(t / 4 * speed) / 10;\n    yPosition = THREE.MathUtils.mapLinear(yPosition, -0.1, 0.1, (_floatingRange$ = floatingRange == null ? void 0 : floatingRange[0]) !== null && _floatingRange$ !== void 0 ? _floatingRange$ : -0.1, (_floatingRange$2 = floatingRange == null ? void 0 : floatingRange[1]) !== null && _floatingRange$2 !== void 0 ? _floatingRange$2 : 0.1);\n    ref.current.position.y = yPosition * floatIntensity;\n    ref.current.updateMatrix();\n  });\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"group\", {\n    ref: mergeRefs([ref, forwardRef]),\n    matrixAutoUpdate: false\n  }, children));\n});\n\nexport { Float };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,kBAAkB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,MAAMC,KAAK,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAC;EAC3CC,QAAQ;EACRC,OAAO,GAAG,IAAI;EACdC,KAAK,GAAG,CAAC;EACTC,iBAAiB,GAAG,CAAC;EACrBC,cAAc,GAAG,CAAC;EAClBC,aAAa,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;EAC3B,GAAGC;AACL,CAAC,EAAEP,UAAU,KAAK;EAChB,MAAMQ,GAAG,GAAGb,KAAK,CAACc,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,MAAM,GAAGf,KAAK,CAACc,MAAM,CAACE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC;EAClDhB,QAAQ,CAACiB,KAAK,IAAI;IAChB,IAAIC,eAAe,EAAEC,gBAAgB;IAErC,IAAI,CAACb,OAAO,IAAIC,KAAK,KAAK,CAAC,EAAE;IAC7B,MAAMa,CAAC,GAAGN,MAAM,CAACO,OAAO,GAAGJ,KAAK,CAACK,KAAK,CAACC,cAAc,CAAC,CAAC;IACvDX,GAAG,CAACS,OAAO,CAACG,QAAQ,CAACC,CAAC,GAAGV,IAAI,CAACW,GAAG,CAACN,CAAC,GAAG,CAAC,GAAGb,KAAK,CAAC,GAAG,CAAC,GAAGC,iBAAiB;IACxEI,GAAG,CAACS,OAAO,CAACG,QAAQ,CAACG,CAAC,GAAGZ,IAAI,CAACa,GAAG,CAACR,CAAC,GAAG,CAAC,GAAGb,KAAK,CAAC,GAAG,CAAC,GAAGC,iBAAiB;IACxEI,GAAG,CAACS,OAAO,CAACG,QAAQ,CAACK,CAAC,GAAGd,IAAI,CAACa,GAAG,CAACR,CAAC,GAAG,CAAC,GAAGb,KAAK,CAAC,GAAG,EAAE,GAAGC,iBAAiB;IACzE,IAAIsB,SAAS,GAAGf,IAAI,CAACa,GAAG,CAACR,CAAC,GAAG,CAAC,GAAGb,KAAK,CAAC,GAAG,EAAE;IAC5CuB,SAAS,GAAG5B,KAAK,CAAC6B,SAAS,CAACC,SAAS,CAACF,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAACZ,eAAe,GAAGR,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIQ,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAG,CAAC,GAAG,EAAE,CAACC,gBAAgB,GAAGT,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIS,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAG,GAAG,CAAC;IAC3UP,GAAG,CAACS,OAAO,CAACY,QAAQ,CAACN,CAAC,GAAGG,SAAS,GAAGrB,cAAc;IACnDG,GAAG,CAACS,OAAO,CAACa,YAAY,CAAC,CAAC;EAC5B,CAAC,CAAC;EACF,OAAO,aAAanC,KAAK,CAACoC,aAAa,CAAC,OAAO,EAAExB,KAAK,EAAE,aAAaZ,KAAK,CAACoC,aAAa,CAAC,OAAO,EAAE;IAChGvB,GAAG,EAAEX,SAAS,CAAC,CAACW,GAAG,EAAER,UAAU,CAAC,CAAC;IACjCgC,gBAAgB,EAAE;EACpB,CAAC,EAAE/B,QAAQ,CAAC,CAAC;AACf,CAAC,CAAC;AAEF,SAASF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}